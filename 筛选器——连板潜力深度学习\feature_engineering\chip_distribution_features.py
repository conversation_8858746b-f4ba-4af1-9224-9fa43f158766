#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
筹码分布特征工程
开发筹码分布分析特征，包括筹码集中度、主力成本区间、获利盘和套牢盘比例
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ChipDistributionFeatureEngine:
    """筹码分布特征工程器"""
    
    def __init__(self):
        # 筹码分布计算参数
        self.chip_params = {
            'analysis_period': 60,      # 分析周期（天）
            'decay_factor': 0.95,       # 筹码衰减因子
            'price_bins': 100,          # 价格区间数量
            'volume_threshold': 0.01,   # 成交量阈值（相对于总成交量）
            'concentration_threshold': 0.7,  # 集中度阈值
            'profit_loss_threshold': 0.05    # 盈亏阈值（5%）
        }
        
        # 筹码分布特征权重
        self.chip_weights = {
            'concentration': 0.3,       # 筹码集中度
            'cost_distribution': 0.25,  # 成本分布
            'profit_loss_ratio': 0.2,   # 盈亏比例
            'peak_analysis': 0.15,      # 峰值分析
            'migration_trend': 0.1      # 迁移趋势
        }
        
        # 主力筹码识别参数
        self.main_force_params = {
            'large_volume_threshold': 2.0,    # 大成交量阈值（倍数）
            'continuous_days': 3,             # 连续天数
            'price_stability': 0.03,          # 价格稳定性阈值
            'cost_range_ratio': 0.15          # 成本区间比例
        }
        
        logger.info("🧮 筹码分布特征工程器初始化完成")
    
    def extract_chip_distribution_features(self, kline_data: pd.DataFrame, current_price: float = None) -> Dict[str, Any]:
        """提取筹码分布特征"""
        try:
            if kline_data.empty:
                return self._get_empty_chip_features()
            
            logger.info(f"🧮 开始提取筹码分布特征...")
            
            features = {}
            
            # 使用最新价格作为当前价格
            if current_price is None:
                current_price = kline_data['close'].iloc[-1]
            
            # 1. 计算筹码分布
            chip_distribution = self._calculate_chip_distribution(kline_data)
            
            if chip_distribution is not None:
                # 2. 筹码集中度特征
                concentration_features = self._extract_concentration_features(chip_distribution, current_price)
                features.update(concentration_features)
                
                # 3. 成本分布特征
                cost_features = self._extract_cost_distribution_features(chip_distribution, current_price)
                features.update(cost_features)
                
                # 4. 盈亏比例特征
                profit_loss_features = self._extract_profit_loss_features(chip_distribution, current_price)
                features.update(profit_loss_features)
                
                # 5. 筹码峰值分析特征
                peak_features = self._extract_peak_analysis_features(chip_distribution, current_price)
                features.update(peak_features)
                
                # 6. 主力成本区间特征
                main_force_features = self._extract_main_force_cost_features(kline_data, chip_distribution, current_price)
                features.update(main_force_features)
                
                # 7. 筹码迁移特征
                migration_features = self._extract_migration_features(kline_data, chip_distribution)
                features.update(migration_features)
                
                # 8. 综合筹码评分
                comprehensive_score = self._calculate_comprehensive_chip_score(features)
                features.update(comprehensive_score)
                
                features['chip_analysis_success'] = True
            else:
                features = self._get_empty_chip_features()
            
            features['total_chip_features'] = len([k for k in features.keys() if not k.startswith('chip_analysis_')])
            
            logger.info(f"✅ 筹码分布特征提取完成，共{features['total_chip_features']}个特征")
            return features
            
        except Exception as e:
            logger.error(f"筹码分布特征提取失败: {e}")
            return self._get_empty_chip_features()
    
    def _calculate_chip_distribution(self, kline_data: pd.DataFrame) -> Optional[pd.DataFrame]:
        """计算筹码分布"""
        try:
            if len(kline_data) < 10:
                return None
            
            # 取最近分析周期的数据
            analysis_period = min(self.chip_params['analysis_period'], len(kline_data))
            recent_data = kline_data.tail(analysis_period).copy()
            
            # 计算价格区间
            min_price = recent_data['low'].min()
            max_price = recent_data['high'].max()
            price_range = max_price - min_price
            
            if price_range <= 0:
                return None
            
            # 创建价格区间
            price_bins = np.linspace(min_price, max_price, self.chip_params['price_bins'])
            price_centers = (price_bins[:-1] + price_bins[1:]) / 2
            
            # 初始化筹码分布
            chip_distribution = np.zeros(len(price_centers))
            
            # 计算每日筹码分布
            for i, (_, row) in enumerate(recent_data.iterrows()):
                # 计算衰减权重（越近的数据权重越大）
                days_ago = analysis_period - i - 1
                weight = self.chip_params['decay_factor'] ** days_ago
                
                # 计算当日价格区间的筹码分布
                daily_volume = row['volume']
                daily_high = row['high']
                daily_low = row['low']
                
                # 简化：假设成交量在当日价格区间内均匀分布
                for j, price_center in enumerate(price_centers):
                    if daily_low <= price_center <= daily_high:
                        # 根据价格在当日区间的位置分配成交量
                        if daily_high > daily_low:
                            distribution_factor = 1.0 - abs(price_center - (daily_high + daily_low) / 2) / (daily_high - daily_low)
                        else:
                            distribution_factor = 1.0
                        
                        chip_distribution[j] += daily_volume * weight * distribution_factor
            
            # 标准化筹码分布
            total_chips = chip_distribution.sum()
            if total_chips > 0:
                chip_distribution = chip_distribution / total_chips
            
            # 创建筹码分布DataFrame
            chip_df = pd.DataFrame({
                'price': price_centers,
                'chips': chip_distribution,
                'cumulative_chips': np.cumsum(chip_distribution)
            })
            
            return chip_df
            
        except Exception as e:
            logger.error(f"计算筹码分布失败: {e}")
            return None
    
    def _extract_concentration_features(self, chip_distribution: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """提取筹码集中度特征"""
        try:
            features = {}
            
            if chip_distribution.empty:
                return features
            
            # 计算筹码集中度
            # 方法1：基尼系数
            chips = chip_distribution['chips'].values
            sorted_chips = np.sort(chips)
            n = len(sorted_chips)
            
            if n > 1:
                cumsum = np.cumsum(sorted_chips)
                gini = (2 * np.sum((np.arange(1, n + 1) * sorted_chips))) / (n * cumsum[-1]) - (n + 1) / n
                features['chip_gini_coefficient'] = gini
                features['high_concentration'] = 1.0 if gini > 0.7 else 0.0
            
            # 方法2：前20%价格区间的筹码占比
            top_20_percent = int(len(chip_distribution) * 0.2)
            sorted_by_chips = chip_distribution.sort_values('chips', ascending=False)
            top_20_chips_ratio = sorted_by_chips.head(top_20_percent)['chips'].sum()
            
            features.update({
                'top_20_percent_chips_ratio': top_20_chips_ratio,
                'chip_concentration_high': 1.0 if top_20_chips_ratio > 0.6 else 0.0
            })
            
            # 方法3：当前价格附近的筹码集中度
            current_price_range = current_price * 0.05  # 5%价格范围
            nearby_chips = chip_distribution[
                (chip_distribution['price'] >= current_price - current_price_range) &
                (chip_distribution['price'] <= current_price + current_price_range)
            ]
            
            if not nearby_chips.empty:
                nearby_chips_ratio = nearby_chips['chips'].sum()
                features['current_price_chips_concentration'] = nearby_chips_ratio
                features['current_price_high_concentration'] = 1.0 if nearby_chips_ratio > 0.3 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"筹码集中度特征提取失败: {e}")
            return {}
    
    def _extract_cost_distribution_features(self, chip_distribution: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """提取成本分布特征"""
        try:
            features = {}
            
            if chip_distribution.empty:
                return features
            
            # 计算加权平均成本
            total_chips = chip_distribution['chips'].sum()
            if total_chips > 0:
                weighted_avg_cost = (chip_distribution['price'] * chip_distribution['chips']).sum() / total_chips
                features['weighted_average_cost'] = weighted_avg_cost
                features['current_price_vs_avg_cost'] = (current_price - weighted_avg_cost) / weighted_avg_cost
            
            # 计算成本分布的标准差
            if total_chips > 0:
                variance = ((chip_distribution['price'] - weighted_avg_cost) ** 2 * chip_distribution['chips']).sum() / total_chips
                cost_std = np.sqrt(variance)
                features['cost_distribution_std'] = cost_std
                features['cost_distribution_cv'] = cost_std / weighted_avg_cost if weighted_avg_cost > 0 else 0
            
            # 计算成本分布的分位数
            cumulative_chips = chip_distribution['cumulative_chips'].values
            prices = chip_distribution['price'].values
            
            # 找到25%, 50%, 75%分位数对应的价格
            percentiles = [0.25, 0.5, 0.75]
            for p in percentiles:
                idx = np.searchsorted(cumulative_chips, p)
                if idx < len(prices):
                    features[f'cost_percentile_{int(p*100)}'] = prices[idx]
            
            # 计算成本分布的偏度和峰度
            if total_chips > 0 and 'cost_distribution_std' in features and features['cost_distribution_std'] > 0:
                # 偏度
                skewness = ((chip_distribution['price'] - weighted_avg_cost) ** 3 * chip_distribution['chips']).sum() / (total_chips * features['cost_distribution_std'] ** 3)
                features['cost_distribution_skewness'] = skewness
                
                # 峰度
                kurtosis = ((chip_distribution['price'] - weighted_avg_cost) ** 4 * chip_distribution['chips']).sum() / (total_chips * features['cost_distribution_std'] ** 4) - 3
                features['cost_distribution_kurtosis'] = kurtosis
            
            return features
            
        except Exception as e:
            logger.error(f"成本分布特征提取失败: {e}")
            return {}
    
    def _extract_profit_loss_features(self, chip_distribution: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """提取盈亏比例特征"""
        try:
            features = {}
            
            if chip_distribution.empty:
                return features
            
            # 计算获利盘和套牢盘
            profit_chips = chip_distribution[chip_distribution['price'] < current_price]['chips'].sum()
            loss_chips = chip_distribution[chip_distribution['price'] > current_price]['chips'].sum()
            equal_chips = chip_distribution[chip_distribution['price'] == current_price]['chips'].sum()
            
            total_chips = profit_chips + loss_chips + equal_chips
            
            if total_chips > 0:
                features.update({
                    'profit_chips_ratio': profit_chips / total_chips,
                    'loss_chips_ratio': loss_chips / total_chips,
                    'equal_chips_ratio': equal_chips / total_chips
                })
                
                # 盈亏比
                if loss_chips > 0:
                    features['profit_loss_ratio'] = profit_chips / loss_chips
                else:
                    features['profit_loss_ratio'] = 10.0  # 设置一个较大值表示几乎全是获利盘
            
            # 计算不同盈亏幅度的筹码分布
            profit_thresholds = [0.05, 0.1, 0.2]  # 5%, 10%, 20%盈利
            loss_thresholds = [0.05, 0.1, 0.2]    # 5%, 10%, 20%亏损
            
            for threshold in profit_thresholds:
                threshold_price = current_price * (1 - threshold)
                profit_chips_threshold = chip_distribution[chip_distribution['price'] <= threshold_price]['chips'].sum()
                if total_chips > 0:
                    features[f'profit_chips_ratio_{int(threshold*100)}pct'] = profit_chips_threshold / total_chips
            
            for threshold in loss_thresholds:
                threshold_price = current_price * (1 + threshold)
                loss_chips_threshold = chip_distribution[chip_distribution['price'] >= threshold_price]['chips'].sum()
                if total_chips > 0:
                    features[f'loss_chips_ratio_{int(threshold*100)}pct'] = loss_chips_threshold / total_chips
            
            # 获利盘压力和套牢盘支撑
            features.update({
                'profit_pressure': 1.0 if profit_chips / total_chips > 0.7 else 0.0,
                'loss_support': 1.0 if loss_chips / total_chips > 0.6 else 0.0,
                'balanced_profit_loss': 1.0 if 0.4 <= profit_chips / total_chips <= 0.6 else 0.0
            })
            
            return features
            
        except Exception as e:
            logger.error(f"盈亏比例特征提取失败: {e}")
            return {}

    def _extract_peak_analysis_features(self, chip_distribution: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """提取筹码峰值分析特征"""
        try:
            features = {}

            if chip_distribution.empty or len(chip_distribution) < 5:
                return features

            chips = chip_distribution['chips'].values
            prices = chip_distribution['price'].values

            # 寻找筹码峰值
            peaks = []
            for i in range(1, len(chips) - 1):
                if chips[i] > chips[i-1] and chips[i] > chips[i+1]:
                    peaks.append({'price': prices[i], 'chips': chips[i], 'index': i})

            # 按筹码数量排序，找到主要峰值
            peaks.sort(key=lambda x: x['chips'], reverse=True)

            features['chip_peaks_count'] = len(peaks)

            if peaks:
                # 主峰特征
                main_peak = peaks[0]
                features.update({
                    'main_peak_price': main_peak['price'],
                    'main_peak_chips': main_peak['chips'],
                    'main_peak_distance_to_current': abs(main_peak['price'] - current_price) / current_price,
                    'current_price_near_main_peak': 1.0 if abs(main_peak['price'] - current_price) / current_price < 0.05 else 0.0
                })

                # 双峰或多峰特征
                if len(peaks) >= 2:
                    second_peak = peaks[1]
                    features.update({
                        'has_double_peaks': 1.0,
                        'second_peak_price': second_peak['price'],
                        'second_peak_chips': second_peak['chips'],
                        'peaks_price_distance': abs(main_peak['price'] - second_peak['price']) / current_price,
                        'peaks_chips_ratio': second_peak['chips'] / main_peak['chips']
                    })

                    # 判断峰值形态
                    if main_peak['price'] < current_price < second_peak['price'] or second_peak['price'] < current_price < main_peak['price']:
                        features['current_price_between_peaks'] = 1.0
                    else:
                        features['current_price_between_peaks'] = 0.0
                else:
                    features['has_double_peaks'] = 0.0

                # 单峰集中度
                if len(peaks) == 1:
                    features['single_peak_concentration'] = 1.0
                else:
                    features['single_peak_concentration'] = 0.0

            return features

        except Exception as e:
            logger.error(f"筹码峰值分析特征提取失败: {e}")
            return {}

    def _extract_main_force_cost_features(self, kline_data: pd.DataFrame, chip_distribution: pd.DataFrame, current_price: float) -> Dict[str, float]:
        """提取主力成本区间特征"""
        try:
            features = {}

            if kline_data.empty or chip_distribution.empty:
                return features

            # 识别主力建仓区间（大成交量且价格相对稳定的区间）
            recent_data = kline_data.tail(30).copy()  # 最近30天

            # 计算成交量异常天数
            avg_volume = recent_data['volume'].mean()
            volume_threshold = avg_volume * self.main_force_params['large_volume_threshold']

            large_volume_days = recent_data[recent_data['volume'] > volume_threshold]

            if not large_volume_days.empty:
                # 主力成本区间
                main_force_cost_low = large_volume_days['low'].min()
                main_force_cost_high = large_volume_days['high'].max()
                main_force_cost_center = (main_force_cost_low + main_force_cost_high) / 2

                features.update({
                    'main_force_cost_low': main_force_cost_low,
                    'main_force_cost_high': main_force_cost_high,
                    'main_force_cost_center': main_force_cost_center,
                    'main_force_cost_range': (main_force_cost_high - main_force_cost_low) / main_force_cost_center,
                    'current_price_in_main_force_range': 1.0 if main_force_cost_low <= current_price <= main_force_cost_high else 0.0,
                    'current_price_above_main_force_cost': 1.0 if current_price > main_force_cost_high else 0.0
                })

                # 主力成本区间的筹码集中度
                main_force_chips = chip_distribution[
                    (chip_distribution['price'] >= main_force_cost_low) &
                    (chip_distribution['price'] <= main_force_cost_high)
                ]['chips'].sum()

                total_chips = chip_distribution['chips'].sum()
                if total_chips > 0:
                    features['main_force_chips_concentration'] = main_force_chips / total_chips
                    features['main_force_high_concentration'] = 1.0 if main_force_chips / total_chips > 0.4 else 0.0

            # 识别主力拉升阶段
            if len(recent_data) >= 10:
                recent_10d = recent_data.tail(10)
                price_increase = (recent_10d['close'].iloc[-1] - recent_10d['close'].iloc[0]) / recent_10d['close'].iloc[0]
                volume_increase = recent_10d['volume'].tail(5).mean() / recent_10d['volume'].head(5).mean()

                features.update({
                    'recent_price_increase': price_increase,
                    'recent_volume_increase': volume_increase,
                    'main_force_pulling_up': 1.0 if price_increase > 0.1 and volume_increase > 1.5 else 0.0
                })

            return features

        except Exception as e:
            logger.error(f"主力成本区间特征提取失败: {e}")
            return {}

    def _extract_migration_features(self, kline_data: pd.DataFrame, chip_distribution: pd.DataFrame) -> Dict[str, float]:
        """提取筹码迁移特征"""
        try:
            features = {}

            if len(kline_data) < 20:
                return features

            # 计算不同时期的筹码分布重心
            periods = [5, 10, 20]
            centers = []

            for period in periods:
                if len(kline_data) >= period:
                    period_data = kline_data.tail(period)
                    # 简化：使用成交量加权平均价格作为筹码重心
                    total_volume = period_data['volume'].sum()
                    if total_volume > 0:
                        weighted_price = (period_data['close'] * period_data['volume']).sum() / total_volume
                        centers.append(weighted_price)
                    else:
                        centers.append(period_data['close'].mean())

            if len(centers) >= 2:
                # 筹码迁移方向
                short_term_center = centers[0]  # 5日重心
                long_term_center = centers[-1]  # 20日重心

                migration_direction = (short_term_center - long_term_center) / long_term_center
                features.update({
                    'chip_migration_direction': migration_direction,
                    'chips_migrating_up': 1.0 if migration_direction > 0.02 else 0.0,
                    'chips_migrating_down': 1.0 if migration_direction < -0.02 else 0.0,
                    'chips_stable': 1.0 if abs(migration_direction) <= 0.02 else 0.0
                })

                # 筹码迁移速度
                if len(centers) >= 3:
                    migration_speed = abs(centers[0] - centers[1]) / centers[1]
                    features['chip_migration_speed'] = migration_speed
                    features['fast_chip_migration'] = 1.0 if migration_speed > 0.05 else 0.0

            # 筹码换手特征
            recent_data = kline_data.tail(10)
            avg_turnover = recent_data.get('turnover_rate', pd.Series([0] * len(recent_data))).mean()

            features.update({
                'recent_avg_turnover': avg_turnover,
                'high_turnover': 1.0 if avg_turnover > 5.0 else 0.0,
                'chip_exchange_active': 1.0 if avg_turnover > 3.0 else 0.0
            })

            return features

        except Exception as e:
            logger.error(f"筹码迁移特征提取失败: {e}")
            return {}

    def _calculate_comprehensive_chip_score(self, features: Dict[str, float]) -> Dict[str, float]:
        """计算综合筹码评分"""
        try:
            score_components = {}

            # 1. 筹码集中度评分
            concentration_score = 0.0
            if 'chip_gini_coefficient' in features:
                concentration_score += features['chip_gini_coefficient'] * 0.5
            if 'top_20_percent_chips_ratio' in features:
                concentration_score += features['top_20_percent_chips_ratio'] * 0.3
            if 'current_price_chips_concentration' in features:
                concentration_score += features['current_price_chips_concentration'] * 0.2

            score_components['concentration_score'] = min(concentration_score, 1.0)

            # 2. 成本分布评分
            cost_score = 0.0
            if 'current_price_vs_avg_cost' in features:
                # 当前价格高于平均成本是好事
                cost_ratio = features['current_price_vs_avg_cost']
                cost_score = min(max(cost_ratio + 0.5, 0), 1.0)

            score_components['cost_distribution_score'] = cost_score

            # 3. 盈亏比例评分
            profit_loss_score = 0.0
            if 'profit_chips_ratio' in features:
                profit_ratio = features['profit_chips_ratio']
                # 适度的获利盘比例是好的（不要太高也不要太低）
                if 0.3 <= profit_ratio <= 0.7:
                    profit_loss_score = 1.0
                else:
                    profit_loss_score = 1.0 - abs(profit_ratio - 0.5) * 2

            score_components['profit_loss_score'] = max(profit_loss_score, 0)

            # 4. 峰值分析评分
            peak_score = 0.0
            if 'single_peak_concentration' in features and features['single_peak_concentration'] > 0:
                peak_score = 0.8
            elif 'current_price_near_main_peak' in features and features['current_price_near_main_peak'] > 0:
                peak_score = 0.6

            score_components['peak_analysis_score'] = peak_score

            # 5. 迁移趋势评分
            migration_score = 0.0
            if 'chips_migrating_up' in features and features['chips_migrating_up'] > 0:
                migration_score = 0.8
            elif 'chips_stable' in features and features['chips_stable'] > 0:
                migration_score = 0.6

            score_components['migration_trend_score'] = migration_score

            # 计算加权综合评分
            comprehensive_score = 0.0
            for component, weight in self.chip_weights.items():
                score_key = f"{component}_score"
                if score_key in score_components:
                    comprehensive_score += score_components[score_key] * weight

            return {
                'chip_comprehensive_score': comprehensive_score,
                'chip_bullish': 1.0 if comprehensive_score > 0.6 else 0.0,
                'chip_bearish': 1.0 if comprehensive_score < 0.3 else 0.0,
                **score_components
            }

        except Exception as e:
            logger.error(f"综合筹码评分计算失败: {e}")
            return {'chip_comprehensive_score': 0.0}

    def _get_empty_chip_features(self) -> Dict[str, Any]:
        """获取空的筹码分布特征"""
        return {
            'chip_analysis_success': False,
            'total_chip_features': 0,
            'chip_comprehensive_score': 0.0,
            'error': '筹码分布特征提取失败'
        }

def main():
    """测试筹码分布特征工程器"""
    print("🧮 测试筹码分布特征工程器...")

    engine = ChipDistributionFeatureEngine()

    # 创建测试K线数据
    dates = pd.date_range('2024-05-01', periods=60, freq='D')
    np.random.seed(42)

    # 模拟更真实的股票数据
    base_price = 10.0
    prices = []
    volumes = []

    for i in range(60):
        # 模拟价格走势
        if i < 20:  # 前20天横盘
            price_change = np.random.randn() * 0.02
        elif i < 40:  # 中间20天上涨
            price_change = np.random.randn() * 0.03 + 0.02
        else:  # 后20天调整
            price_change = np.random.randn() * 0.025 - 0.01

        base_price *= (1 + price_change)
        prices.append(base_price)

        # 模拟成交量
        if 20 <= i < 40:  # 上涨期间放量
            volume = 1000000 + np.random.randn() * 300000 + 500000
        else:
            volume = 1000000 + np.random.randn() * 200000

        volumes.append(max(volume, 100000))

    test_kline = pd.DataFrame({
        'date': dates,
        'open': [p * (1 + np.random.randn() * 0.01) for p in prices],
        'close': prices,
        'volume': volumes,
        'turnover_rate': [v / 50000000 * 100 for v in volumes]  # 模拟换手率
    })

    # 计算high和low
    test_kline['high'] = test_kline[['open', 'close']].max(axis=1) * (1 + np.random.rand(60) * 0.02)
    test_kline['low'] = test_kline[['open', 'close']].min(axis=1) * (1 - np.random.rand(60) * 0.02)

    # 提取筹码分布特征
    print(f"\n🔍 测试筹码分布特征提取...")
    current_price = test_kline['close'].iloc[-1]
    features = engine.extract_chip_distribution_features(test_kline, current_price)

    print(f"✅ 特征提取完成")
    print(f"分析成功: {features.get('chip_analysis_success', False)}")
    print(f"总特征数: {features.get('total_chip_features', 0)}")
    print(f"当前价格: {current_price:.2f}")

    # 显示关键特征
    key_features = ['chip_gini_coefficient', 'weighted_average_cost', 'profit_chips_ratio',
                   'main_peak_price', 'chip_comprehensive_score']

    print(f"\n🧮 关键筹码分布特征:")
    for feature in key_features:
        if feature in features:
            print(f"  {feature}: {features[feature]:.3f}")

if __name__ == "__main__":
    main()
