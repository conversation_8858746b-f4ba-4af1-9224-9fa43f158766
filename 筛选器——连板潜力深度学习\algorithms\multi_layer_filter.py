#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多层筛选算法
设计四层筛选算法：基础筛选(5500→500)、技术信号筛选(500→50)、资金信号筛选(50→10)、时机信号筛选(10→2-3)
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from data_layer.historical_data_fetcher import HistoricalDataFetcher
from data_layer.historical_data_storage import HistoricalDataStorage
from feature_engineering.technical_indicators import TechnicalIndicatorsFeatureEngine
from feature_engineering.capital_flow_features import CapitalFlowFeatureEngine
from feature_engineering.youzi_behavior_features import YouzieBehaviorFeatureEngine
from feature_engineering.chip_distribution_features import ChipDistributionFeatureEngine
from feature_engineering.intraday_features_optimizer import IntradayFeaturesOptimizer
from feature_engineering.sector_linkage_features import SectorLinkageFeatureEngine
from ml_models.limit_up_predictor import LimitUpPredictor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MultiLayerFilter:
    """多层筛选算法"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        
        # 初始化组件
        self.data_fetcher = HistoricalDataFetcher()
        self.data_storage = HistoricalDataStorage()
        
        # 特征工程器
        self.technical_engine = TechnicalIndicatorsFeatureEngine()
        self.capital_engine = CapitalFlowFeatureEngine()
        self.youzi_engine = YouzieBehaviorFeatureEngine()
        self.chip_engine = ChipDistributionFeatureEngine()
        self.intraday_engine = IntradayFeaturesOptimizer()
        self.sector_engine = SectorLinkageFeatureEngine()
        
        # 机器学习模型
        self.predictor = LimitUpPredictor()
        
        # 筛选统计
        self.filter_stats = {}
        
        logger.info("🔍 多层筛选算法初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            # 基础筛选配置
            'basic_filter': {
                'min_market_cap': 30e8,        # 最小市值30亿
                'max_market_cap': 1300e8,      # 最大市值1300亿
                'min_price': 3.0,              # 最小价格3元
                'max_price': 100.0,            # 最大价格100元
                'min_turnover_rate': 1.0,      # 最小换手率1%
                'max_turnover_rate': 20.0,     # 最大换手率20%
                'exclude_st': True,            # 排除ST股票
                'exclude_new_stock_days': 60,  # 排除上市60天内新股
                'target_count': 500            # 目标筛选数量
            },
            
            # 技术信号筛选配置
            'technical_filter': {
                'min_technical_score': 0.6,   # 最小技术评分
                'required_signals': [         # 必需的技术信号
                    'ma5_above_ma10',
                    'volume_significant_increase'
                ],
                'preferred_signals': [        # 优选的技术信号
                    'macd_golden_cross',
                    'rsi_bullish_zone',
                    'kdj_golden_cross',
                    'bb_near_upper'
                ],
                'target_count': 50            # 目标筛选数量
            },
            
            # 资金信号筛选配置
            'capital_filter': {
                'min_capital_score': 0.5,     # 最小资金评分
                'required_capital_signals': [ # 必需的资金信号
                    'main_inflow_positive',
                    'large_inflow_positive'
                ],
                'min_main_inflow_ratio': 0.05, # 最小主力净流入比例5%
                'min_volume_ratio': 1.5,       # 最小成交量比率1.5倍
                'target_count': 10             # 目标筛选数量
            },
            
            # 时机信号筛选配置
            'timing_filter': {
                'min_timing_score': 0.7,      # 最小时机评分
                'core_timing_signals': [      # 核心时机信号
                    'morning_volume_active',
                    'active_volume_pattern',
                    'near_recent_high'
                ],
                'min_ml_probability': 0.6,    # 最小机器学习预测概率
                'target_count': 3             # 目标筛选数量
            }
        }
    
    def run_multi_layer_filtering(self, stock_universe: List[str] = None) -> Dict[str, Any]:
        """运行多层筛选算法"""
        try:
            logger.info("🔍 开始多层筛选算法...")
            
            filtering_results = {
                'start_time': datetime.now().isoformat(),
                'layers': {}
            }
            
            # 获取股票池
            if stock_universe is None:
                stock_universe = self._get_stock_universe()
            
            logger.info(f"📊 初始股票池: {len(stock_universe)}只股票")
            filtering_results['initial_count'] = len(stock_universe)
            
            # 第一层：基础筛选 (5500 → 500)
            logger.info("🔍 第一层：基础筛选")
            layer1_results = self._basic_filter(stock_universe)
            filtering_results['layers']['layer1_basic'] = layer1_results
            
            if not layer1_results.get('success', False):
                logger.error("❌ 基础筛选失败")
                return filtering_results
            
            # 第二层：技术信号筛选 (500 → 50)
            logger.info("🔍 第二层：技术信号筛选")
            layer2_results = self._technical_signal_filter(layer1_results['filtered_stocks'])
            filtering_results['layers']['layer2_technical'] = layer2_results
            
            if not layer2_results.get('success', False):
                logger.error("❌ 技术信号筛选失败")
                return filtering_results
            
            # 第三层：资金信号筛选 (50 → 10)
            logger.info("🔍 第三层：资金信号筛选")
            layer3_results = self._capital_signal_filter(layer2_results['filtered_stocks'])
            filtering_results['layers']['layer3_capital'] = layer3_results
            
            if not layer3_results.get('success', False):
                logger.error("❌ 资金信号筛选失败")
                return filtering_results
            
            # 第四层：时机信号筛选 (10 → 2-3)
            logger.info("🔍 第四层：时机信号筛选")
            layer4_results = self._timing_signal_filter(layer3_results['filtered_stocks'])
            filtering_results['layers']['layer4_timing'] = layer4_results
            
            # 汇总结果
            filtering_results.update({
                'success': True,
                'final_stocks': layer4_results.get('filtered_stocks', []),
                'final_count': len(layer4_results.get('filtered_stocks', [])),
                'end_time': datetime.now().isoformat(),
                'filtering_summary': self._generate_filtering_summary(filtering_results)
            })
            
            logger.info(f"🎉 多层筛选完成，最终筛选出{filtering_results['final_count']}只股票")
            return filtering_results
            
        except Exception as e:
            logger.error(f"多层筛选算法失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_stock_universe(self) -> List[str]:
        """获取股票池（模拟A股全市场）"""
        try:
            # 这里应该从实际数据源获取全市场股票代码
            # 现在使用模拟数据
            stock_universe = []
            
            # 模拟A股股票代码
            # 深市主板 000001-000999
            for i in range(1, 100):
                stock_universe.append(f"00{i:04d}")
            
            # 深市中小板 002001-002999
            for i in range(1, 50):
                stock_universe.append(f"00{2000+i:04d}")
            
            # 沪市主板 600000-603999
            for i in range(1, 100):
                stock_universe.append(f"60{i:04d}")
            
            logger.info(f"📊 生成模拟股票池: {len(stock_universe)}只股票")
            return stock_universe
            
        except Exception as e:
            logger.error(f"获取股票池失败: {e}")
            return []
    
    def _basic_filter(self, stock_list: List[str]) -> Dict[str, Any]:
        """第一层：基础筛选"""
        try:
            logger.info(f"🔍 基础筛选开始，输入{len(stock_list)}只股票")
            
            config = self.config['basic_filter']
            filtered_stocks = []
            filter_reasons = {}
            
            for stock_code in stock_list:
                try:
                    # 模拟基础数据检查
                    basic_data = self._get_basic_stock_data(stock_code)
                    
                    if not basic_data:
                        filter_reasons[stock_code] = "无基础数据"
                        continue
                    
                    # 基础筛选条件
                    reasons = []
                    
                    # 市值筛选
                    market_cap = basic_data.get('market_cap', 0)
                    if market_cap < config['min_market_cap'] or market_cap > config['max_market_cap']:
                        reasons.append(f"市值不符合({market_cap/1e8:.1f}亿)")
                    
                    # 价格筛选
                    price = basic_data.get('price', 0)
                    if price < config['min_price'] or price > config['max_price']:
                        reasons.append(f"价格不符合({price:.2f}元)")
                    
                    # 换手率筛选
                    turnover_rate = basic_data.get('turnover_rate', 0)
                    if turnover_rate < config['min_turnover_rate'] or turnover_rate > config['max_turnover_rate']:
                        reasons.append(f"换手率不符合({turnover_rate:.2f}%)")
                    
                    # ST股票筛选
                    if config['exclude_st'] and basic_data.get('is_st', False):
                        reasons.append("ST股票")
                    
                    # 新股筛选
                    if basic_data.get('days_since_listing', 0) < config['exclude_new_stock_days']:
                        reasons.append("新股")
                    
                    # 如果通过所有筛选条件
                    if not reasons:
                        filtered_stocks.append({
                            'stock_code': stock_code,
                            'stock_name': basic_data.get('stock_name', ''),
                            'market_cap': market_cap,
                            'price': price,
                            'turnover_rate': turnover_rate,
                            'basic_score': self._calculate_basic_score(basic_data)
                        })
                    else:
                        filter_reasons[stock_code] = "; ".join(reasons)
                
                except Exception as e:
                    filter_reasons[stock_code] = f"处理错误: {e}"
                    continue
            
            # 按基础评分排序，取前N只
            filtered_stocks.sort(key=lambda x: x['basic_score'], reverse=True)
            target_count = min(config['target_count'], len(filtered_stocks))
            final_filtered = filtered_stocks[:target_count]
            
            logger.info(f"✅ 基础筛选完成: {len(stock_list)} → {len(final_filtered)}")
            
            return {
                'success': True,
                'input_count': len(stock_list),
                'output_count': len(final_filtered),
                'filtered_stocks': final_filtered,
                'filter_reasons': filter_reasons,
                'filter_rate': len(final_filtered) / len(stock_list) if stock_list else 0
            }
            
        except Exception as e:
            logger.error(f"基础筛选失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_basic_stock_data(self, stock_code: str) -> Dict[str, Any]:
        """获取股票基础数据（模拟）"""
        try:
            # 模拟股票基础数据
            np.random.seed(int(stock_code[-4:]))  # 使用股票代码作为随机种子
            
            # 模拟不同类型的股票
            is_st = np.random.random() < 0.05  # 5%的ST股票
            is_new = np.random.random() < 0.1   # 10%的新股
            
            # 模拟市值分布（对数正态分布）
            log_market_cap = np.random.normal(np.log(100e8), 1.5)  # 均值100亿
            market_cap = np.exp(log_market_cap)
            
            # 模拟价格
            if market_cap > 500e8:  # 大盘股
                price = np.random.uniform(10, 50)
            elif market_cap > 100e8:  # 中盘股
                price = np.random.uniform(5, 30)
            else:  # 小盘股
                price = np.random.uniform(3, 20)
            
            # 模拟换手率
            turnover_rate = np.random.exponential(3)  # 指数分布
            
            return {
                'stock_code': stock_code,
                'stock_name': f"股票{stock_code}",
                'market_cap': market_cap,
                'price': price,
                'turnover_rate': turnover_rate,
                'is_st': is_st,
                'days_since_listing': 30 if is_new else np.random.randint(100, 3000)
            }
            
        except Exception as e:
            logger.error(f"获取{stock_code}基础数据失败: {e}")
            return {}
    
    def _calculate_basic_score(self, basic_data: Dict[str, Any]) -> float:
        """计算基础评分"""
        try:
            score = 0.0
            
            # 市值评分（中等市值得分较高）
            market_cap = basic_data.get('market_cap', 0) / 1e8
            if 50 <= market_cap <= 300:
                score += 0.4
            elif 30 <= market_cap <= 500:
                score += 0.3
            else:
                score += 0.1
            
            # 价格评分（中等价格得分较高）
            price = basic_data.get('price', 0)
            if 8 <= price <= 30:
                score += 0.3
            elif 5 <= price <= 50:
                score += 0.2
            else:
                score += 0.1
            
            # 换手率评分（适度活跃得分较高）
            turnover_rate = basic_data.get('turnover_rate', 0)
            if 2 <= turnover_rate <= 8:
                score += 0.3
            elif 1 <= turnover_rate <= 15:
                score += 0.2
            else:
                score += 0.1
            
            return score
            
        except Exception as e:
            logger.error(f"计算基础评分失败: {e}")
            return 0.0

    def _technical_signal_filter(self, stock_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """第二层：技术信号筛选"""
        try:
            logger.info(f"🔍 技术信号筛选开始，输入{len(stock_list)}只股票")

            config = self.config['technical_filter']
            filtered_stocks = []

            for stock_info in stock_list:
                try:
                    stock_code = stock_info['stock_code']

                    # 获取K线数据
                    kline_data = self._get_mock_kline_data(stock_code)

                    if kline_data.empty:
                        continue

                    # 提取技术指标特征
                    technical_features = self.technical_engine.extract_all_technical_features(kline_data)

                    # 计算技术信号评分
                    technical_score = self._calculate_technical_score(technical_features, config)

                    # 检查必需信号
                    required_signals_met = all(
                        technical_features.get(signal, 0) > 0
                        for signal in config['required_signals']
                    )

                    # 通过技术筛选条件
                    if technical_score >= config['min_technical_score'] and required_signals_met:
                        stock_info.update({
                            'technical_score': technical_score,
                            'technical_features': technical_features
                        })
                        filtered_stocks.append(stock_info)

                except Exception as e:
                    logger.warning(f"处理股票{stock_info.get('stock_code', 'unknown')}技术信号失败: {e}")
                    continue

            # 按技术评分排序
            filtered_stocks.sort(key=lambda x: x['technical_score'], reverse=True)
            target_count = min(config['target_count'], len(filtered_stocks))
            final_filtered = filtered_stocks[:target_count]

            logger.info(f"✅ 技术信号筛选完成: {len(stock_list)} → {len(final_filtered)}")

            return {
                'success': True,
                'input_count': len(stock_list),
                'output_count': len(final_filtered),
                'filtered_stocks': final_filtered,
                'filter_rate': len(final_filtered) / len(stock_list) if stock_list else 0
            }

        except Exception as e:
            logger.error(f"技术信号筛选失败: {e}")
            return {'success': False, 'error': str(e)}

    def _capital_signal_filter(self, stock_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """第三层：资金信号筛选"""
        try:
            logger.info(f"🔍 资金信号筛选开始，输入{len(stock_list)}只股票")

            config = self.config['capital_filter']
            filtered_stocks = []

            for stock_info in stock_list:
                try:
                    stock_code = stock_info['stock_code']

                    # 获取K线数据
                    kline_data = self._get_mock_kline_data(stock_code)

                    if kline_data.empty:
                        continue

                    # 提取资金流向特征
                    capital_features = self.capital_engine.extract_capital_flow_features(stock_code, kline_data)

                    # 计算资金信号评分
                    capital_score = self._calculate_capital_score(capital_features, config)

                    # 检查必需的资金信号
                    required_capital_met = all(
                        capital_features.get(signal, 0) > 0
                        for signal in config['required_capital_signals']
                    )

                    # 检查主力净流入比例
                    main_inflow_ratio = capital_features.get('main_net_inflow_ratio', 0)
                    main_inflow_ok = main_inflow_ratio >= config['min_main_inflow_ratio']

                    # 通过资金筛选条件
                    if (capital_score >= config['min_capital_score'] and
                        required_capital_met and main_inflow_ok):

                        stock_info.update({
                            'capital_score': capital_score,
                            'capital_features': capital_features
                        })
                        filtered_stocks.append(stock_info)

                except Exception as e:
                    logger.warning(f"处理股票{stock_info.get('stock_code', 'unknown')}资金信号失败: {e}")
                    continue

            # 按资金评分排序
            filtered_stocks.sort(key=lambda x: x['capital_score'], reverse=True)
            target_count = min(config['target_count'], len(filtered_stocks))
            final_filtered = filtered_stocks[:target_count]

            logger.info(f"✅ 资金信号筛选完成: {len(stock_list)} → {len(final_filtered)}")

            return {
                'success': True,
                'input_count': len(stock_list),
                'output_count': len(final_filtered),
                'filtered_stocks': final_filtered,
                'filter_rate': len(final_filtered) / len(stock_list) if stock_list else 0
            }

        except Exception as e:
            logger.error(f"资金信号筛选失败: {e}")
            return {'success': False, 'error': str(e)}

    def _timing_signal_filter(self, stock_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """第四层：时机信号筛选"""
        try:
            logger.info(f"🔍 时机信号筛选开始，输入{len(stock_list)}只股票")

            config = self.config['timing_filter']
            filtered_stocks = []

            for stock_info in stock_list:
                try:
                    stock_code = stock_info['stock_code']
                    stock_name = stock_info['stock_name']

                    # 获取K线数据
                    kline_data = self._get_mock_kline_data(stock_code)

                    if kline_data.empty:
                        continue

                    # 提取所有特征用于机器学习预测
                    all_features = self._extract_comprehensive_features(stock_code, stock_name, kline_data)

                    # 机器学习预测
                    ml_prediction = self._get_ml_prediction(all_features)
                    ml_probability = ml_prediction.get('ensemble_probability', 0)

                    # 计算时机信号评分
                    timing_score = self._calculate_timing_score(all_features, config)

                    # 检查核心时机信号
                    core_timing_met = all(
                        all_features.get(signal, 0) > 0
                        for signal in config['core_timing_signals']
                    )

                    # 通过时机筛选条件
                    if (timing_score >= config['min_timing_score'] and
                        ml_probability >= config['min_ml_probability'] and
                        core_timing_met):

                        stock_info.update({
                            'timing_score': timing_score,
                            'ml_probability': ml_probability,
                            'ml_prediction': ml_prediction,
                            'comprehensive_features': all_features,
                            'final_score': (timing_score * 0.4 + ml_probability * 0.6)
                        })
                        filtered_stocks.append(stock_info)

                except Exception as e:
                    logger.warning(f"处理股票{stock_info.get('stock_code', 'unknown')}时机信号失败: {e}")
                    continue

            # 按最终评分排序
            filtered_stocks.sort(key=lambda x: x['final_score'], reverse=True)
            target_count = min(config['target_count'], len(filtered_stocks))
            final_filtered = filtered_stocks[:target_count]

            logger.info(f"✅ 时机信号筛选完成: {len(stock_list)} → {len(final_filtered)}")

            return {
                'success': True,
                'input_count': len(stock_list),
                'output_count': len(final_filtered),
                'filtered_stocks': final_filtered,
                'filter_rate': len(final_filtered) / len(stock_list) if stock_list else 0
            }

        except Exception as e:
            logger.error(f"时机信号筛选失败: {e}")
            return {'success': False, 'error': str(e)}

    def _get_mock_kline_data(self, stock_code: str) -> pd.DataFrame:
        """获取模拟K线数据"""
        try:
            # 生成30天的模拟K线数据
            dates = pd.date_range(end=datetime.now(), periods=30, freq='D')

            np.random.seed(int(stock_code[-4:]))  # 使用股票代码作为随机种子

            # 模拟价格走势
            base_price = np.random.uniform(8, 25)
            prices = []
            volumes = []

            for i in range(30):
                # 模拟价格变化
                change = np.random.randn() * 0.03
                base_price *= (1 + change)
                prices.append(base_price)

                # 模拟成交量
                volume = np.random.uniform(1000000, 10000000)
                volumes.append(volume)

            # 创建K线数据
            kline_data = pd.DataFrame({
                'date': dates,
                'open': [p * (1 + np.random.randn() * 0.01) for p in prices],
                'close': prices,
                'high': [p * (1 + np.random.rand() * 0.02) for p in prices],
                'low': [p * (1 - np.random.rand() * 0.02) for p in prices],
                'volume': volumes,
                'amount': [p * v for p, v in zip(prices, volumes)],
                'turnover_rate': [v / 50000000 * 100 for v in volumes]
            })

            return kline_data

        except Exception as e:
            logger.error(f"生成{stock_code}模拟K线数据失败: {e}")
            return pd.DataFrame()

    def _calculate_technical_score(self, technical_features: Dict[str, Any], config: Dict[str, Any]) -> float:
        """计算技术信号评分"""
        try:
            score = 0.0

            # 必需信号评分
            required_score = 0.0
            for signal in config['required_signals']:
                if technical_features.get(signal, 0) > 0:
                    required_score += 0.3

            # 优选信号评分
            preferred_score = 0.0
            for signal in config['preferred_signals']:
                if technical_features.get(signal, 0) > 0:
                    preferred_score += 0.1

            # 综合技术评分
            comprehensive_score = technical_features.get('technical_comprehensive_score', 0) * 0.4

            score = required_score + preferred_score + comprehensive_score
            return min(score, 1.0)

        except Exception as e:
            logger.error(f"计算技术评分失败: {e}")
            return 0.0

    def _calculate_capital_score(self, capital_features: Dict[str, Any], config: Dict[str, Any]) -> float:
        """计算资金信号评分"""
        try:
            score = 0.0

            # 主力资金流入评分
            main_inflow_ratio = capital_features.get('main_net_inflow_ratio', 0)
            if main_inflow_ratio > 0.1:
                score += 0.4
            elif main_inflow_ratio > 0.05:
                score += 0.3
            elif main_inflow_ratio > 0:
                score += 0.2

            # 大单流入评分
            large_inflow = capital_features.get('large_inflow_positive', 0)
            if large_inflow > 0:
                score += 0.3

            # 综合资金评分
            comprehensive_score = capital_features.get('capital_flow_comprehensive_score', 0) * 0.3

            score += comprehensive_score
            return min(score, 1.0)

        except Exception as e:
            logger.error(f"计算资金评分失败: {e}")
            return 0.0

    def _calculate_timing_score(self, all_features: Dict[str, Any], config: Dict[str, Any]) -> float:
        """计算时机信号评分"""
        try:
            score = 0.0

            # 核心时机信号评分
            core_signals_score = 0.0
            for signal in config['core_timing_signals']:
                if all_features.get(signal, 0) > 0:
                    core_signals_score += 0.2

            # 分时特征评分
            intraday_score = all_features.get('intraday_comprehensive_score', 0) * 0.3

            # 筹码分布评分
            chip_score = all_features.get('chip_comprehensive_score', 0) * 0.2

            # 游资行为评分
            youzi_score = all_features.get('youzi_behavior_comprehensive_score', 0) * 0.2

            score = core_signals_score + intraday_score + chip_score + youzi_score
            return min(score, 1.0)

        except Exception as e:
            logger.error(f"计算时机评分失败: {e}")
            return 0.0

    def _extract_comprehensive_features(self, stock_code: str, stock_name: str, kline_data: pd.DataFrame) -> Dict[str, Any]:
        """提取综合特征"""
        try:
            all_features = {}

            # 技术指标特征
            technical_features = self.technical_engine.extract_all_technical_features(kline_data)
            all_features.update(technical_features)

            # 资金流向特征
            capital_features = self.capital_engine.extract_capital_flow_features(stock_code, kline_data)
            all_features.update(capital_features)

            # 筹码分布特征
            chip_features = self.chip_engine.extract_chip_distribution_features(kline_data)
            all_features.update(chip_features)

            # 清理特征
            cleaned_features = {}
            for key, value in all_features.items():
                if isinstance(value, (int, float, bool)) and not pd.isna(value):
                    cleaned_features[key] = float(value)

            return cleaned_features

        except Exception as e:
            logger.error(f"提取{stock_code}综合特征失败: {e}")
            return {}

    def _get_ml_prediction(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """获取机器学习预测"""
        try:
            # 尝试加载已训练的模型
            try:
                self.predictor.load_models()
            except:
                # 如果没有训练好的模型，使用模拟预测
                pass

            if self.predictor.trained_models:
                return self.predictor.predict_limit_up_probability(features)
            else:
                # 模拟预测结果
                # 基于特征计算简单的预测概率
                score = 0.0

                # 核心特征权重
                core_features = ['morning_volume_active', 'active_volume_pattern', 'near_recent_high']
                for feature in core_features:
                    if features.get(feature, 0) > 0:
                        score += 0.2

                # 技术特征权重
                if features.get('ma5_above_ma10', 0) > 0:
                    score += 0.1
                if features.get('volume_significant_increase', 0) > 0:
                    score += 0.1

                # 添加随机性
                score += np.random.uniform(-0.1, 0.1)
                probability = max(0, min(1, score))

                return {
                    'ensemble_prediction': 1 if probability > 0.5 else 0,
                    'ensemble_probability': probability,
                    'prediction_confidence': 'medium'
                }

        except Exception as e:
            logger.error(f"机器学习预测失败: {e}")
            return {'ensemble_prediction': 0, 'ensemble_probability': 0.0, 'prediction_confidence': 'low'}

    def _generate_filtering_summary(self, filtering_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成筛选总结"""
        try:
            summary = {
                'total_reduction_rate': 0.0,
                'layer_reduction_rates': {},
                'final_stocks_info': []
            }

            initial_count = filtering_results.get('initial_count', 0)
            final_count = filtering_results.get('final_count', 0)

            if initial_count > 0:
                summary['total_reduction_rate'] = (initial_count - final_count) / initial_count

            # 各层筛选率
            for layer_name, layer_data in filtering_results.get('layers', {}).items():
                if layer_data.get('success', False):
                    input_count = layer_data.get('input_count', 0)
                    output_count = layer_data.get('output_count', 0)
                    if input_count > 0:
                        reduction_rate = (input_count - output_count) / input_count
                        summary['layer_reduction_rates'][layer_name] = reduction_rate

            # 最终股票信息
            final_stocks = filtering_results.get('final_stocks', [])
            for stock in final_stocks:
                stock_summary = {
                    'stock_code': stock.get('stock_code', ''),
                    'stock_name': stock.get('stock_name', ''),
                    'final_score': stock.get('final_score', 0),
                    'ml_probability': stock.get('ml_probability', 0),
                    'timing_score': stock.get('timing_score', 0)
                }
                summary['final_stocks_info'].append(stock_summary)

            return summary

        except Exception as e:
            logger.error(f"生成筛选总结失败: {e}")
            return {}

def main():
    """测试多层筛选算法"""
    print("🔍 测试多层筛选算法...")

    filter_engine = MultiLayerFilter()

    # 运行多层筛选
    print("\n🚀 开始多层筛选...")
    results = filter_engine.run_multi_layer_filtering()

    if results.get('success', False):
        print("✅ 多层筛选成功！")
        print(f"  初始股票数: {results.get('initial_count', 0)}")
        print(f"  最终股票数: {results.get('final_count', 0)}")

        # 显示各层筛选结果
        for layer_name, layer_data in results.get('layers', {}).items():
            if layer_data.get('success', False):
                input_count = layer_data.get('input_count', 0)
                output_count = layer_data.get('output_count', 0)
                filter_rate = layer_data.get('filter_rate', 0)
                print(f"  {layer_name}: {input_count} → {output_count} (筛选率: {filter_rate:.1%})")

        # 显示最终筛选股票
        final_stocks = results.get('final_stocks', [])
        if final_stocks:
            print(f"\n🎯 最终筛选股票:")
            for i, stock in enumerate(final_stocks, 1):
                print(f"  {i}. {stock.get('stock_code', '')} {stock.get('stock_name', '')}")
                print(f"     最终评分: {stock.get('final_score', 0):.3f}")
                print(f"     ML概率: {stock.get('ml_probability', 0):.3f}")
    else:
        print("❌ 多层筛选失败")
        print(f"  错误: {results.get('error', 'Unknown error')}")

if __name__ == "__main__":
    main()
