#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统配置文件
连板潜力股深度学习器的全局配置管理
"""

import os
from typing import Dict, Any, List
from datetime import datetime

class SystemConfig:
    """系统配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        
        # 基础配置
        self.VERSION = "1.0.0"
        self.PROJECT_NAME = "连板潜力股深度学习器"
        self.PROJECT_ROOT = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        
        # 数据配置
        self.DATA_CONFIG = {
            'cache_timeout': 3600,  # 缓存超时时间（秒）
            'max_retry_times': 3,   # 最大重试次数
            'retry_interval': 30,   # 重试间隔（秒）
            'data_sources': {
                'akshare': {'enabled': True, 'priority': 1},
                'adata': {'enabled': True, 'priority': 2},
                'tushare': {'enabled': False, 'priority': 3}
            }
        }
        
        # 特征工程配置
        self.FEATURE_CONFIG = {
            'technical_indicators': {
                'ma_periods': [5, 10, 20, 60],
                'rsi_period': 14,
                'macd_params': {'fast': 12, 'slow': 26, 'signal': 9},
                'bollinger_period': 20,
                'kdj_params': {'k_period': 9, 'd_period': 3, 'j_period': 3}
            },
            'volume_analysis': {
                'volume_ma_periods': [5, 10, 20],
                'turnover_thresholds': {'low': 2.0, 'high': 8.0},
                'volume_ratio_threshold': 2.0
            },
            'minute_analysis': {
                'morning_period': 60,  # 早盘分析时长（分钟）
                'tail_period': 30,     # 尾盘分析时长（分钟）
                'volume_concentration_threshold': 0.35
            }
        }
        
        # 筛选算法配置
        self.SCREENING_CONFIG = {
            'four_layer_filter': {
                'layer1_basic': {
                    'market_cap_range': [30, 130],  # 市值范围（亿元）
                    'exclude_st': True,
                    'exclude_chinext': True,
                    'exclude_star': True,
                    'exclude_bse': True,
                    'target_count': 500
                },
                'layer2_technical': {
                    'ma_alignment': True,
                    'volume_breakout': True,
                    'price_pattern': True,
                    'target_count': 50
                },
                'layer3_capital': {
                    'capital_inflow': True,
                    'youzi_activity': True,
                    'lhb_records': True,
                    'target_count': 10
                },
                'layer4_timing': {
                    'morning_volume_active': True,
                    'minute_pattern': True,
                    'sector_resonance': True,
                    'target_count': 3
                }
            }
        }
        
        # 模型配置
        self.MODEL_CONFIG = {
            'xgboost': {
                'n_estimators': 100,
                'max_depth': 6,
                'learning_rate': 0.1,
                'subsample': 0.8,
                'colsample_bytree': 0.8,
                'random_state': 42
            },
            'lstm': {
                'sequence_length': 20,
                'hidden_units': 64,
                'dropout_rate': 0.2,
                'epochs': 50,
                'batch_size': 32
            },
            'random_forest': {
                'n_estimators': 100,
                'max_depth': 10,
                'min_samples_split': 5,
                'min_samples_leaf': 2,
                'random_state': 42
            },
            'ensemble': {
                'weights': {
                    'xgboost': 0.4,
                    'lstm': 0.35,
                    'random_forest': 0.25
                },
                'voting_method': 'weighted'
            }
        }
        
        # 实时监控配置
        self.REALTIME_CONFIG = {
            'scan_interval': 60,    # 扫描间隔（秒）
            'market_hours': {
                'morning_start': '09:30',
                'morning_end': '11:30',
                'afternoon_start': '13:00',
                'afternoon_end': '15:00'
            },
            'alert_thresholds': {
                'prediction_confidence': 0.7,
                'volume_surge': 2.0,
                'price_momentum': 0.03
            }
        }
        
        # 回测配置
        self.BACKTEST_CONFIG = {
            'start_date': '2023-01-01',
            'end_date': '2024-12-31',
            'initial_capital': 1000000,  # 初始资金（元）
            'position_size': 0.1,        # 单只股票仓位
            'commission_rate': 0.0003,   # 手续费率
            'slippage': 0.001           # 滑点
        }
        
        # 日志配置
        self.LOGGING_CONFIG = {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            'file_path': 'logs/system.log',
            'max_file_size': 10 * 1024 * 1024,  # 10MB
            'backup_count': 5
        }
        
        # 核心特征配置（基于分析结果）
        self.CORE_FEATURES = {
            'mandatory_features': [
                'morning_volume_active',      # 早盘成交量活跃（100%特征）
                'active_volume_pattern'       # 活跃成交量模式（100%特征）
            ],
            'high_value_features': [
                'near_recent_high',           # 接近近期高点（64%）
                'stable_price_pattern',       # 稳定价格模式（56%）
                'volume_price_positive_correlation',  # 量价正相关（48%）
                'price_breakthrough'          # 价格突破（44%）
            ],
            'feature_weights': {
                'morning_volume_active': 0.25,
                'active_volume_pattern': 0.25,
                'near_recent_high': 0.16,
                'stable_price_pattern': 0.14,
                'volume_price_positive_correlation': 0.12,
                'price_breakthrough': 0.08
            }
        }
        
        # 性能指标配置
        self.PERFORMANCE_CONFIG = {
            'target_metrics': {
                'prediction_accuracy': 0.60,    # 目标预测准确率
                'screening_efficiency': 0.9995, # 筛选效率（99.95%过滤率）
                'false_positive_rate': 0.30,    # 误报率上限
                'response_time': 30              # 预警响应时间（分钟）
            },
            'evaluation_metrics': [
                'accuracy', 'precision', 'recall', 'f1_score',
                'sharpe_ratio', 'max_drawdown', 'annual_return'
            ]
        }
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'version': self.VERSION,
            'project_name': self.PROJECT_NAME,
            'data_sources_count': len(self.DATA_CONFIG['data_sources']),
            'mandatory_features_count': len(self.CORE_FEATURES['mandatory_features']),
            'high_value_features_count': len(self.CORE_FEATURES['high_value_features']),
            'model_types': list(self.MODEL_CONFIG.keys()),
            'target_accuracy': self.PERFORMANCE_CONFIG['target_metrics']['prediction_accuracy'],
            'screening_layers': len(self.SCREENING_CONFIG['four_layer_filter']),
            'last_updated': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
    
    def get_feature_config(self) -> Dict[str, Any]:
        """获取特征配置"""
        return self.FEATURE_CONFIG
    
    def get_model_config(self, model_name: str = None) -> Dict[str, Any]:
        """获取模型配置"""
        if model_name:
            return self.MODEL_CONFIG.get(model_name, {})
        return self.MODEL_CONFIG
    
    def get_screening_config(self) -> Dict[str, Any]:
        """获取筛选配置"""
        return self.SCREENING_CONFIG
    
    def get_core_features(self) -> Dict[str, Any]:
        """获取核心特征配置"""
        return self.CORE_FEATURES
    
    def update_config(self, section: str, key: str, value: Any):
        """更新配置"""
        if hasattr(self, section):
            config_section = getattr(self, section)
            if isinstance(config_section, dict) and key in config_section:
                config_section[key] = value
                return True
        return False
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置有效性"""
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 验证必要路径
        if not os.path.exists(self.PROJECT_ROOT):
            validation_results['errors'].append(f"项目根目录不存在: {self.PROJECT_ROOT}")
            validation_results['valid'] = False
        
        # 验证特征权重总和
        total_weight = sum(self.CORE_FEATURES['feature_weights'].values())
        if abs(total_weight - 1.0) > 0.01:
            validation_results['warnings'].append(f"特征权重总和不为1.0: {total_weight}")
        
        # 验证模型配置
        for model_name, config in self.MODEL_CONFIG.items():
            if not isinstance(config, dict):
                validation_results['errors'].append(f"模型配置格式错误: {model_name}")
                validation_results['valid'] = False
        
        return validation_results

# 全局配置实例
config = SystemConfig()
