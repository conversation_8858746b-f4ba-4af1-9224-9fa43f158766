#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型预测器
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_model_loading():
    """测试模型加载"""
    print("🔍 测试模型加载...")
    
    try:
        from training.model_predictor import ModelPredictor
        
        predictor = ModelPredictor()
        
        print(f"📊 模型加载状态:")
        print(f"  加载的模型数量: {len(predictor.models)}")
        print(f"  模型列表: {list(predictor.models.keys())}")
        print(f"  特征列数量: {len(predictor.feature_columns)}")
        print(f"  标准化器: {'已加载' if predictor.scaler else '未加载'}")
        
        if predictor.models:
            print("✅ 模型加载成功")
            return True, predictor
        else:
            print("❌ 没有加载到任何模型")
            return False, None
            
    except Exception as e:
        print(f"❌ 模型加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False, None

def test_single_prediction(predictor):
    """测试单只股票预测"""
    print("\n🔍 测试单只股票预测...")
    
    try:
        # 创建测试特征数据
        test_features = {
            'close_price': 15.5,
            'price_change_5d': 0.05,
            'price_change_10d': 0.08,
            'volume_avg_5d': 1000000,
            'volume_ratio': 1.2,
            'turnover_rate': 3.5,
            'turnover_avg_5d': 3.0,
            'amplitude': 5.2,
            'price_vs_ma5': 1.02,
            'price_vs_ma10': 1.05,
            'ma5_slope': 0.02,
            'ma10_slope': 0.01,
            'rsi': 65.0,
            'macd_signal': 1,
            'bollinger_position': 0.7,
            'volume_ma_ratio': 1.1,
            'price_volatility': 0.03,
            'main_net_inflow': 1000000,
            'main_net_inflow_ratio': 0.05,
            'retail_net_inflow': -500000,
            'large_order_ratio': 0.3,
            'is_shenzhen': 1,
            'is_shanghai': 0,
            'is_sme_board': 1,
            'youzi_activity_score': 0.6,
            'gaming_capital_score': 0.5,
            'high_quality': 1,
            'medium_quality': 0,
            'low_quality': 0,
            'technical_signal_count': 3
        }
        
        print(f"📊 测试特征数量: {len(test_features)}")
        
        # 进行预测
        result = predictor.predict_single_stock(test_features)
        
        print(f"📈 预测结果:")
        print(f"  成功: {result.get('success', False)}")
        
        if result.get('success', False):
            print(f"  预测标签: {result.get('prediction_label', 'N/A')}")
            print(f"  预测分数: {result.get('prediction_score', 'N/A'):.3f}")
            print(f"  个体预测: {result.get('individual_predictions', {})}")
            print(f"  个体分数: {result.get('individual_scores', {})}")
            print("✅ 单只股票预测成功")
            return True
        else:
            print(f"  错误: {result.get('error', 'Unknown error')}")
            print("❌ 单只股票预测失败")
            return False
            
    except Exception as e:
        print(f"❌ 单只股票预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_prediction(predictor):
    """测试批量预测"""
    print("\n🔍 测试批量预测...")
    
    try:
        # 创建测试数据
        test_stocks = []
        for i in range(3):
            stock_features = {
                'stock_code': f'00000{i+1}',
                'stock_name': f'测试股票{i+1}',
                'close_price': 15.5 + i,
                'price_change_5d': 0.05 + i * 0.01,
                'volume_ratio': 1.2 + i * 0.1,
                'main_net_inflow': 1000000 * (i + 1),
                'is_shenzhen': 1,
                'is_shanghai': 0,
                'technical_signal_count': 3 + i
            }
            test_stocks.append(stock_features)
        
        print(f"📊 测试股票数量: {len(test_stocks)}")
        
        # 进行批量预测
        results = predictor.predict_multiple_stocks(test_stocks)
        
        print(f"📈 批量预测结果:")
        print(f"  结果数量: {len(results)}")
        
        success_count = 0
        for i, result in enumerate(results):
            stock_code = result.get('stock_code', f'stock_{i}')
            success = result.get('success', False)
            if success:
                score = result.get('prediction_score', 0)
                print(f"  {stock_code}: ✅ 成功, 分数: {score:.3f}")
                success_count += 1
            else:
                error = result.get('error', 'Unknown error')
                print(f"  {stock_code}: ❌ 失败, 错误: {error}")
        
        print(f"  成功率: {success_count}/{len(results)} ({success_count/len(results)*100:.1f}%)")
        
        if success_count > 0:
            print("✅ 批量预测部分成功")
            return True
        else:
            print("❌ 批量预测全部失败")
            return False
            
    except Exception as e:
        print(f"❌ 批量预测测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 模型预测器测试...")
    print("="*60)
    
    # 测试模型加载
    loading_success, predictor = test_model_loading()
    
    if not loading_success:
        print("\n❌ 模型加载失败，无法继续测试")
        return
    
    # 测试单只股票预测
    single_success = test_single_prediction(predictor)
    
    # 测试批量预测
    batch_success = test_batch_prediction(predictor)
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if loading_success and single_success and batch_success:
        print("✅ 模型预测器工作正常")
        print("💡 潜在股调查的模型预测问题已修复")
    elif loading_success and single_success:
        print("✅ 模型加载和单只预测正常")
        print("⚠️ 批量预测可能有问题")
    elif loading_success:
        print("✅ 模型加载正常")
        print("❌ 预测功能有问题")
    else:
        print("❌ 模型预测器有严重问题")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
