#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时数据获取器
获取同板块龙头股表现、北向资金流向等实时数据
"""

import akshare as ak
import pandas as pd
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Any
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RealtimeDataFetcher:
    """实时数据获取器"""
    
    def __init__(self):
        """初始化实时数据获取器"""
        self.cache = {}  # 数据缓存
        self.cache_timeout = 300  # 缓存5分钟
        logger.info("✅ 实时数据获取器初始化完成")
    
    def get_sector_leader_performance(self, stock_code: str) -> Dict[str, Any]:
        """
        获取同板块龙头股表现
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 板块龙头表现数据
        """
        try:
            cache_key = f"sector_leader_{stock_code}"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 获取股票所属板块
            sector_info = self._get_stock_sector(stock_code)
            if not sector_info:
                return {'error': '无法获取板块信息'}
            
            # 获取板块内股票表现
            sector_stocks = self._get_sector_stocks(sector_info['sector_name'])
            if not sector_stocks:
                return {'error': '无法获取板块股票'}
            
            # 分析龙头表现
            leader_analysis = self._analyze_sector_leaders(sector_stocks)
            
            # 缓存结果
            self.cache[cache_key] = {
                'data': leader_analysis,
                'timestamp': datetime.now()
            }
            
            return leader_analysis
            
        except Exception as e:
            logger.error(f"获取板块龙头表现失败: {e}")
            return {'error': str(e)}
    
    def get_northbound_capital_flow(self) -> Dict[str, Any]:
        """
        获取北向资金流向
        
        Returns:
            Dict: 北向资金流向数据
        """
        try:
            cache_key = "northbound_capital"
            
            # 检查缓存
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # 获取北向资金流向数据
            try:
                # 获取沪深港通资金流向
                hsgt_df = ak.tool_trade_date_hist_sina()
                if hsgt_df.empty:
                    return {'error': '无法获取北向资金数据'}
                
                # 获取最新数据
                latest_date = hsgt_df['trade_date'].max()
                latest_data = hsgt_df[hsgt_df['trade_date'] == latest_date].iloc[0]
                
                # 获取实时北向资金流向
                try:
                    realtime_hsgt = ak.stock_hsgt_fund_flow_summary_em()
                    if not realtime_hsgt.empty:
                        today_flow = realtime_hsgt.iloc[0]
                        
                        northbound_data = {
                            'date': latest_date,
                            'net_inflow': float(today_flow.get('北向资金', 0)),
                            'shanghai_inflow': float(today_flow.get('沪股通', 0)),
                            'shenzhen_inflow': float(today_flow.get('深股通', 0)),
                            'trend': self._analyze_capital_trend(float(today_flow.get('北向资金', 0))),
                            'strength': self._get_capital_strength(float(today_flow.get('北向资金', 0)))
                        }
                    else:
                        northbound_data = {
                            'date': latest_date,
                            'net_inflow': 0,
                            'shanghai_inflow': 0,
                            'shenzhen_inflow': 0,
                            'trend': 'unknown',
                            'strength': 'weak'
                        }
                        
                except Exception as sub_e:
                    logger.warning(f"获取实时北向资金失败，使用历史数据: {sub_e}")
                    northbound_data = {
                        'date': latest_date,
                        'net_inflow': 0,
                        'trend': 'unknown',
                        'strength': 'weak'
                    }
                
                # 缓存结果
                self.cache[cache_key] = {
                    'data': northbound_data,
                    'timestamp': datetime.now()
                }
                
                return northbound_data
                
            except Exception as ak_e:
                logger.error(f"AKShare获取北向资金失败: {ak_e}")
                return {'error': f'数据源异常: {ak_e}'}
            
        except Exception as e:
            logger.error(f"获取北向资金流向失败: {e}")
            return {'error': str(e)}
    
    def get_stock_realtime_data(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票实时数据（增强版）
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 实时股票数据
        """
        try:
            cache_key = f"realtime_{stock_code}"
            
            # 检查缓存（实时数据缓存时间短）
            if self._is_cache_valid(cache_key, timeout=60):  # 1分钟缓存
                return self.cache[cache_key]['data']
            
            # 获取实时行情
            try:
                # 尝试获取实时数据
                realtime_df = ak.stock_zh_a_spot_em()
                stock_data = realtime_df[realtime_df['代码'] == stock_code]
                
                if stock_data.empty:
                    return {'error': f'未找到股票 {stock_code}'}
                
                stock_info = stock_data.iloc[0]
                
                # 构建增强数据
                enhanced_data = {
                    'stock_code': stock_code,
                    'stock_name': stock_info['名称'],
                    'current_price': float(stock_info['最新价']),
                    'change_pct': float(stock_info['涨跌幅']),
                    'change': float(stock_info['涨跌额']),
                    'volume': int(stock_info['成交量']),
                    'amount': float(stock_info['成交额']),
                    'turnover_rate': float(stock_info['换手率']),
                    'volume_ratio': float(stock_info.get('量比', 1.0)),
                    'open': float(stock_info['今开']),
                    'high': float(stock_info['最高']),
                    'low': float(stock_info['最低']),
                    'prev_close': float(stock_info['昨收']),
                    'timestamp': datetime.now().isoformat()
                }
                
                # 缓存结果
                self.cache[cache_key] = {
                    'data': enhanced_data,
                    'timestamp': datetime.now()
                }
                
                return enhanced_data
                
            except Exception as ak_e:
                logger.error(f"AKShare获取实时数据失败: {ak_e}")
                return {'error': f'数据源异常: {ak_e}'}
            
        except Exception as e:
            logger.error(f"获取实时数据失败: {e}")
            return {'error': str(e)}
    
    def _get_stock_sector(self, stock_code: str) -> Optional[Dict[str, str]]:
        """获取股票所属板块"""
        try:
            # 获取股票基本信息
            stock_info_df = ak.stock_individual_info_em(symbol=stock_code)
            if stock_info_df.empty:
                return None
            
            # 提取板块信息
            sector_info = {}
            for _, row in stock_info_df.iterrows():
                if row['item'] == '所属行业':
                    sector_info['sector_name'] = row['value']
                elif row['item'] == '所属概念':
                    sector_info['concept'] = row['value']
            
            return sector_info if sector_info else None
            
        except Exception as e:
            logger.error(f"获取股票板块信息失败: {e}")
            return None
    
    def _get_sector_stocks(self, sector_name: str) -> Optional[pd.DataFrame]:
        """获取板块内股票"""
        try:
            # 获取行业板块数据
            sector_df = ak.stock_board_industry_name_em()
            if sector_df.empty:
                return None
            
            # 查找对应板块
            target_sector = sector_df[sector_df['板块名称'].str.contains(sector_name, na=False)]
            if target_sector.empty:
                return None
            
            # 获取板块内股票
            sector_code = target_sector.iloc[0]['板块代码']
            stocks_df = ak.stock_board_industry_cons_em(symbol=sector_name)
            
            return stocks_df if not stocks_df.empty else None
            
        except Exception as e:
            logger.error(f"获取板块股票失败: {e}")
            return None
    
    def _analyze_sector_leaders(self, sector_stocks: pd.DataFrame) -> Dict[str, Any]:
        """分析板块龙头表现"""
        try:
            if sector_stocks.empty:
                return {'error': '板块数据为空'}
            
            # 按市值排序找龙头
            sector_stocks_sorted = sector_stocks.sort_values('总市值', ascending=False)
            top_3_leaders = sector_stocks_sorted.head(3)
            
            # 分析龙头表现
            leader_performance = []
            avg_change = 0
            
            for _, stock in top_3_leaders.iterrows():
                change_pct = float(stock.get('涨跌幅', 0))
                avg_change += change_pct
                
                leader_performance.append({
                    'code': stock['代码'],
                    'name': stock['名称'],
                    'change_pct': change_pct,
                    'market_cap': float(stock.get('总市值', 0)),
                    'performance': 'strong' if change_pct > 3 else 'weak' if change_pct < -3 else 'normal'
                })
            
            avg_change /= len(top_3_leaders)
            
            # 板块整体表现
            sector_trend = 'bullish' if avg_change > 2 else 'bearish' if avg_change < -2 else 'neutral'
            
            return {
                'leaders': leader_performance,
                'avg_change': avg_change,
                'sector_trend': sector_trend,
                'leader_count': len(leader_performance),
                'strong_leaders': len([l for l in leader_performance if l['performance'] == 'strong'])
            }
            
        except Exception as e:
            logger.error(f"分析板块龙头失败: {e}")
            return {'error': str(e)}
    
    def _analyze_capital_trend(self, net_inflow: float) -> str:
        """分析资金流向趋势"""
        if net_inflow > 50:  # 5000万以上
            return 'strong_inflow'
        elif net_inflow > 10:  # 1000万以上
            return 'inflow'
        elif net_inflow > -10:  # -1000万到1000万
            return 'neutral'
        elif net_inflow > -50:  # -5000万到-1000万
            return 'outflow'
        else:
            return 'strong_outflow'
    
    def _get_capital_strength(self, net_inflow: float) -> str:
        """获取资金流向强度"""
        abs_flow = abs(net_inflow)
        if abs_flow > 100:
            return 'very_strong'
        elif abs_flow > 50:
            return 'strong'
        elif abs_flow > 20:
            return 'moderate'
        else:
            return 'weak'
    
    def _is_cache_valid(self, cache_key: str, timeout: int = None) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        timeout = timeout or self.cache_timeout
        
        return (datetime.now() - cache_time).seconds < timeout

# 创建全局实例
realtime_data_fetcher = RealtimeDataFetcher()
