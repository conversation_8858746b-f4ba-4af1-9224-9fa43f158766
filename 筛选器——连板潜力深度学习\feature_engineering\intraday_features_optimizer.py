#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分时特征优化器
优化分时特征分析，增加早盘、尾盘、中午等不同时段的精细化特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import logging
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class IntradayFeaturesOptimizer:
    """分时特征优化器"""
    
    def __init__(self):
        # 交易时段配置
        self.trading_sessions = {
            'morning_open': {'start': '09:30', 'end': '10:00', 'name': '早盘开盘'},
            'morning_active': {'start': '10:00', 'end': '10:30', 'name': '早盘活跃'},
            'morning_stable': {'start': '10:30', 'end': '11:30', 'name': '早盘稳定'},
            'lunch_break': {'start': '11:30', 'end': '13:00', 'name': '午休时间'},
            'afternoon_open': {'start': '13:00', 'end': '13:30', 'name': '午后开盘'},
            'afternoon_active': {'start': '13:30', 'end': '14:30', 'name': '午后活跃'},
            'tail_trading': {'start': '14:30', 'end': '15:00', 'name': '尾盘交易'}
        }
        
        # 关键时点配置
        self.key_timepoints = {
            'opening_5min': {'start': '09:30', 'end': '09:35', 'weight': 0.2},
            'opening_15min': {'start': '09:30', 'end': '09:45', 'weight': 0.15},
            'morning_close': {'start': '11:25', 'end': '11:30', 'weight': 0.1},
            'afternoon_open_5min': {'start': '13:00', 'end': '13:05', 'weight': 0.15},
            'tail_15min': {'start': '14:45', 'end': '15:00', 'weight': 0.25},
            'tail_5min': {'start': '14:55', 'end': '15:00', 'weight': 0.15}
        }
        
        # 分时特征权重配置
        self.intraday_weights = {
            'opening_strength': 0.25,      # 开盘强度
            'volume_distribution': 0.2,    # 成交量分布
            'price_stability': 0.15,       # 价格稳定性
            'tail_performance': 0.2,       # 尾盘表现
            'session_momentum': 0.2        # 时段动量
        }
        
        # 异动识别参数
        self.anomaly_params = {
            'volume_spike_threshold': 3.0,     # 成交量异动阈值
            'price_spike_threshold': 0.03,     # 价格异动阈值
            'continuous_minutes': 3,           # 连续异动分钟数
            'relative_strength_threshold': 1.5  # 相对强度阈值
        }
        
        logger.info("⏰ 分时特征优化器初始化完成")
    
    def extract_intraday_features(self, minute_data: pd.DataFrame, daily_data: pd.DataFrame = None) -> Dict[str, Any]:
        """提取分时特征"""
        try:
            if minute_data.empty:
                return self._get_empty_intraday_features()
            
            logger.info(f"⏰ 开始提取分时特征...")
            
            features = {}
            
            # 预处理分时数据
            processed_data = self._preprocess_minute_data(minute_data)
            
            if processed_data.empty:
                return self._get_empty_intraday_features()
            
            # 1. 时段分析特征
            session_features = self._extract_session_features(processed_data)
            features.update(session_features)
            
            # 2. 开盘特征分析
            opening_features = self._extract_opening_features(processed_data)
            features.update(opening_features)
            
            # 3. 尾盘特征分析
            tail_features = self._extract_tail_features(processed_data)
            features.update(tail_features)
            
            # 4. 成交量分布特征
            volume_features = self._extract_volume_distribution_features(processed_data)
            features.update(volume_features)
            
            # 5. 价格波动特征
            volatility_features = self._extract_intraday_volatility_features(processed_data)
            features.update(volatility_features)
            
            # 6. 异动识别特征
            anomaly_features = self._extract_anomaly_features(processed_data)
            features.update(anomaly_features)
            
            # 7. 时段动量特征
            momentum_features = self._extract_session_momentum_features(processed_data)
            features.update(momentum_features)
            
            # 8. 综合分时评分
            comprehensive_score = self._calculate_comprehensive_intraday_score(features)
            features.update(comprehensive_score)
            
            features['intraday_analysis_success'] = True
            features['total_intraday_features'] = len([k for k in features.keys() if not k.startswith('intraday_analysis_')])
            
            logger.info(f"✅ 分时特征提取完成，共{features['total_intraday_features']}个特征")
            return features
            
        except Exception as e:
            logger.error(f"分时特征提取失败: {e}")
            return self._get_empty_intraday_features()
    
    def _preprocess_minute_data(self, minute_data: pd.DataFrame) -> pd.DataFrame:
        """预处理分时数据"""
        try:
            if minute_data.empty:
                return pd.DataFrame()
            
            # 复制数据避免修改原数据
            data = minute_data.copy()
            
            # 确保有时间列
            if 'time' not in data.columns and 'datetime' not in data.columns:
                # 如果没有时间列，创建标准交易时间
                trading_minutes = []
                current_time = datetime.strptime('09:30', '%H:%M')
                
                for i in range(len(data)):
                    if current_time.time() == time(11, 30):
                        current_time = datetime.strptime('13:00', '%H:%M')
                    
                    trading_minutes.append(current_time.strftime('%H:%M'))
                    current_time += timedelta(minutes=1)
                    
                    if current_time.time() > time(15, 0):
                        break
                
                data['time'] = trading_minutes[:len(data)]
            
            # 标准化列名
            if 'datetime' in data.columns:
                data['time'] = pd.to_datetime(data['datetime']).dt.strftime('%H:%M')
            
            # 确保必要的列存在
            required_columns = ['close', 'volume']
            for col in required_columns:
                if col not in data.columns:
                    if col == 'close' and 'price' in data.columns:
                        data['close'] = data['price']
                    else:
                        logger.warning(f"缺少必要列: {col}")
                        return pd.DataFrame()
            
            # 计算价格变化
            if len(data) > 1:
                data['price_change'] = data['close'].pct_change()
                data['price_change_abs'] = data['price_change'].abs()
            
            # 计算累计成交量
            data['cumulative_volume'] = data['volume'].cumsum()
            
            return data
            
        except Exception as e:
            logger.error(f"分时数据预处理失败: {e}")
            return pd.DataFrame()
    
    def _extract_session_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取时段分析特征"""
        try:
            features = {}
            
            if data.empty or 'time' not in data.columns:
                return features
            
            # 分析各个交易时段
            for session_name, session_config in self.trading_sessions.items():
                if session_name == 'lunch_break':  # 跳过午休时间
                    continue
                
                start_time = session_config['start']
                end_time = session_config['end']
                
                # 筛选时段数据
                session_data = data[
                    (data['time'] >= start_time) & 
                    (data['time'] <= end_time)
                ]
                
                if not session_data.empty:
                    # 时段成交量特征
                    session_volume = session_data['volume'].sum()
                    total_volume = data['volume'].sum()
                    
                    if total_volume > 0:
                        volume_ratio = session_volume / total_volume
                        features[f'{session_name}_volume_ratio'] = volume_ratio
                    
                    # 时段价格变化特征
                    if len(session_data) > 1:
                        session_price_change = (session_data['close'].iloc[-1] - session_data['close'].iloc[0]) / session_data['close'].iloc[0]
                        features[f'{session_name}_price_change'] = session_price_change
                        features[f'{session_name}_price_up'] = 1.0 if session_price_change > 0 else 0.0
                    
                    # 时段波动率
                    if len(session_data) > 2:
                        session_volatility = session_data['price_change'].std() if 'price_change' in session_data.columns else 0
                        features[f'{session_name}_volatility'] = session_volatility
            
            return features
            
        except Exception as e:
            logger.error(f"时段分析特征提取失败: {e}")
            return {}
    
    def _extract_opening_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取开盘特征"""
        try:
            features = {}
            
            if data.empty:
                return features
            
            # 开盘5分钟特征
            opening_5min = data[data['time'] <= '09:35']
            if not opening_5min.empty:
                opening_volume_5min = opening_5min['volume'].sum()
                total_volume = data['volume'].sum()
                
                if total_volume > 0:
                    features['opening_5min_volume_ratio'] = opening_volume_5min / total_volume
                    features['opening_volume_strong'] = 1.0 if opening_volume_5min / total_volume > 0.1 else 0.0
                
                if len(opening_5min) > 1:
                    opening_price_change = (opening_5min['close'].iloc[-1] - opening_5min['close'].iloc[0]) / opening_5min['close'].iloc[0]
                    features['opening_5min_price_change'] = opening_price_change
                    features['opening_gap_up'] = 1.0 if opening_price_change > 0.02 else 0.0
            
            # 开盘15分钟特征
            opening_15min = data[data['time'] <= '09:45']
            if not opening_15min.empty:
                opening_volume_15min = opening_15min['volume'].sum()
                
                if total_volume > 0:
                    features['opening_15min_volume_ratio'] = opening_volume_15min / total_volume
                
                if len(opening_15min) > 1:
                    opening_15min_change = (opening_15min['close'].iloc[-1] - opening_15min['close'].iloc[0]) / opening_15min['close'].iloc[0]
                    features['opening_15min_price_change'] = opening_15min_change
                    features['opening_momentum_strong'] = 1.0 if opening_15min_change > 0.03 else 0.0
            
            # 开盘强度综合评分
            opening_strength = 0.0
            if 'opening_5min_volume_ratio' in features:
                opening_strength += features['opening_5min_volume_ratio'] * 0.4
            if 'opening_5min_price_change' in features:
                opening_strength += max(features['opening_5min_price_change'] * 10, 0) * 0.6
            
            features['opening_strength_score'] = min(opening_strength, 1.0)
            features['strong_opening'] = 1.0 if opening_strength > 0.3 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"开盘特征提取失败: {e}")
            return {}
    
    def _extract_tail_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取尾盘特征"""
        try:
            features = {}
            
            if data.empty:
                return features
            
            # 尾盘15分钟特征
            tail_15min = data[data['time'] >= '14:45']
            if not tail_15min.empty:
                tail_volume_15min = tail_15min['volume'].sum()
                total_volume = data['volume'].sum()
                
                if total_volume > 0:
                    features['tail_15min_volume_ratio'] = tail_volume_15min / total_volume
                    features['tail_volume_concentration'] = 1.0 if tail_volume_15min / total_volume > 0.2 else 0.0
                
                if len(tail_15min) > 1:
                    tail_price_change = (tail_15min['close'].iloc[-1] - tail_15min['close'].iloc[0]) / tail_15min['close'].iloc[0]
                    features['tail_15min_price_change'] = tail_price_change
                    features['tail_rally'] = 1.0 if tail_price_change > 0.02 else 0.0
            
            # 尾盘5分钟特征
            tail_5min = data[data['time'] >= '14:55']
            if not tail_5min.empty:
                tail_volume_5min = tail_5min['volume'].sum()
                
                if total_volume > 0:
                    features['tail_5min_volume_ratio'] = tail_volume_5min / total_volume
                
                if len(tail_5min) > 1:
                    tail_5min_change = (tail_5min['close'].iloc[-1] - tail_5min['close'].iloc[0]) / tail_5min['close'].iloc[0]
                    features['tail_5min_price_change'] = tail_5min_change
                    features['tail_surge'] = 1.0 if tail_5min_change > 0.015 else 0.0
            
            # 尾盘强度综合评分
            tail_strength = 0.0
            if 'tail_15min_volume_ratio' in features:
                tail_strength += features['tail_15min_volume_ratio'] * 0.4
            if 'tail_15min_price_change' in features:
                tail_strength += max(features['tail_15min_price_change'] * 10, 0) * 0.6
            
            features['tail_strength_score'] = min(tail_strength, 1.0)
            features['strong_tail'] = 1.0 if tail_strength > 0.25 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"尾盘特征提取失败: {e}")
            return {}

    def _extract_volume_distribution_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取成交量分布特征"""
        try:
            features = {}

            if data.empty:
                return features

            total_volume = data['volume'].sum()
            if total_volume == 0:
                return features

            # 按时段分析成交量分布
            morning_volume = data[data['time'] <= '11:30']['volume'].sum()
            afternoon_volume = data[data['time'] >= '13:00']['volume'].sum()

            features.update({
                'morning_volume_ratio': morning_volume / total_volume,
                'afternoon_volume_ratio': afternoon_volume / total_volume,
                'morning_afternoon_volume_balance': abs(morning_volume - afternoon_volume) / total_volume
            })

            # 成交量集中度分析
            # 计算每10分钟的成交量
            volume_10min_segments = []
            for i in range(0, len(data), 10):
                segment_volume = data.iloc[i:i+10]['volume'].sum()
                volume_10min_segments.append(segment_volume)

            if volume_10min_segments:
                max_10min_volume = max(volume_10min_segments)
                features['max_10min_volume_ratio'] = max_10min_volume / total_volume
                features['volume_concentration_high'] = 1.0 if max_10min_volume / total_volume > 0.15 else 0.0

                # 成交量分布的标准差
                volume_std = np.std(volume_10min_segments)
                volume_mean = np.mean(volume_10min_segments)
                if volume_mean > 0:
                    features['volume_distribution_cv'] = volume_std / volume_mean
                    features['volume_distribution_uneven'] = 1.0 if volume_std / volume_mean > 1.0 else 0.0

            # 成交量趋势分析
            if len(data) >= 60:  # 至少1小时数据
                first_hour_volume = data.head(60)['volume'].sum()
                last_hour_volume = data.tail(60)['volume'].sum()

                if first_hour_volume > 0:
                    volume_trend = (last_hour_volume - first_hour_volume) / first_hour_volume
                    features['volume_trend'] = volume_trend
                    features['volume_increasing'] = 1.0 if volume_trend > 0.2 else 0.0

            return features

        except Exception as e:
            logger.error(f"成交量分布特征提取失败: {e}")
            return {}

    def _extract_intraday_volatility_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取分时波动率特征"""
        try:
            features = {}

            if data.empty or 'price_change' not in data.columns:
                return features

            # 整体分时波动率
            intraday_volatility = data['price_change'].std()
            features['intraday_volatility'] = intraday_volatility
            features['high_intraday_volatility'] = 1.0 if intraday_volatility > 0.01 else 0.0

            # 分时段波动率
            morning_data = data[data['time'] <= '11:30']
            afternoon_data = data[data['time'] >= '13:00']

            if not morning_data.empty and 'price_change' in morning_data.columns:
                morning_volatility = morning_data['price_change'].std()
                features['morning_volatility'] = morning_volatility

            if not afternoon_data.empty and 'price_change' in afternoon_data.columns:
                afternoon_volatility = afternoon_data['price_change'].std()
                features['afternoon_volatility'] = afternoon_volatility

                # 上下午波动率对比
                if 'morning_volatility' in features and features['morning_volatility'] > 0:
                    volatility_ratio = afternoon_volatility / features['morning_volatility']
                    features['afternoon_morning_volatility_ratio'] = volatility_ratio
                    features['afternoon_more_volatile'] = 1.0 if volatility_ratio > 1.2 else 0.0

            # 价格稳定性分析
            if len(data) > 10:
                # 计算价格在均值附近的比例
                price_mean = data['close'].mean()
                price_std = data['close'].std()

                if price_std > 0:
                    stable_range = price_std * 0.5
                    stable_points = data[
                        (data['close'] >= price_mean - stable_range) &
                        (data['close'] <= price_mean + stable_range)
                    ]

                    features['price_stability_ratio'] = len(stable_points) / len(data)
                    features['price_stable'] = 1.0 if len(stable_points) / len(data) > 0.6 else 0.0

            return features

        except Exception as e:
            logger.error(f"分时波动率特征提取失败: {e}")
            return {}

    def _extract_anomaly_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取异动识别特征"""
        try:
            features = {}

            if data.empty:
                return features

            # 成交量异动识别
            if 'volume' in data.columns:
                volume_mean = data['volume'].mean()
                volume_std = data['volume'].std()

                if volume_std > 0:
                    volume_spike_threshold = volume_mean + volume_std * self.anomaly_params['volume_spike_threshold']
                    volume_spikes = data[data['volume'] > volume_spike_threshold]

                    features['volume_spike_count'] = len(volume_spikes)
                    features['has_volume_spike'] = 1.0 if len(volume_spikes) > 0 else 0.0

                    if not volume_spikes.empty:
                        features['max_volume_spike_ratio'] = volume_spikes['volume'].max() / volume_mean

            # 价格异动识别
            if 'price_change_abs' in data.columns:
                price_spike_threshold = self.anomaly_params['price_spike_threshold']
                price_spikes = data[data['price_change_abs'] > price_spike_threshold]

                features['price_spike_count'] = len(price_spikes)
                features['has_price_spike'] = 1.0 if len(price_spikes) > 0 else 0.0

                if not price_spikes.empty:
                    features['max_price_spike'] = price_spikes['price_change_abs'].max()

            # 连续异动识别
            continuous_anomaly_count = 0
            current_streak = 0

            for _, row in data.iterrows():
                volume_anomaly = row.get('volume', 0) > volume_mean + volume_std * 2 if volume_std > 0 else False
                price_anomaly = row.get('price_change_abs', 0) > price_spike_threshold

                if volume_anomaly or price_anomaly:
                    current_streak += 1
                else:
                    if current_streak >= self.anomaly_params['continuous_minutes']:
                        continuous_anomaly_count += 1
                    current_streak = 0

            # 检查最后一段
            if current_streak >= self.anomaly_params['continuous_minutes']:
                continuous_anomaly_count += 1

            features['continuous_anomaly_count'] = continuous_anomaly_count
            features['has_continuous_anomaly'] = 1.0 if continuous_anomaly_count > 0 else 0.0

            return features

        except Exception as e:
            logger.error(f"异动识别特征提取失败: {e}")
            return {}

    def _extract_session_momentum_features(self, data: pd.DataFrame) -> Dict[str, float]:
        """提取时段动量特征"""
        try:
            features = {}

            if data.empty:
                return features

            # 计算各时段的动量
            sessions = [
                ('morning', '09:30', '11:30'),
                ('afternoon', '13:00', '15:00'),
                ('opening_hour', '09:30', '10:30'),
                ('closing_hour', '14:00', '15:00')
            ]

            for session_name, start_time, end_time in sessions:
                session_data = data[
                    (data['time'] >= start_time) &
                    (data['time'] <= end_time)
                ]

                if len(session_data) > 1:
                    # 价格动量
                    price_momentum = (session_data['close'].iloc[-1] - session_data['close'].iloc[0]) / session_data['close'].iloc[0]
                    features[f'{session_name}_price_momentum'] = price_momentum
                    features[f'{session_name}_positive_momentum'] = 1.0 if price_momentum > 0.01 else 0.0

                    # 成交量动量
                    if len(session_data) >= 10:
                        first_half_volume = session_data.head(len(session_data)//2)['volume'].mean()
                        second_half_volume = session_data.tail(len(session_data)//2)['volume'].mean()

                        if first_half_volume > 0:
                            volume_momentum = (second_half_volume - first_half_volume) / first_half_volume
                            features[f'{session_name}_volume_momentum'] = volume_momentum
                            features[f'{session_name}_volume_accelerating'] = 1.0 if volume_momentum > 0.2 else 0.0

            # 整体动量趋势
            if len(data) > 60:  # 至少1小时数据
                # 分为三段分析动量变化
                segment_length = len(data) // 3

                segments = [
                    data.iloc[:segment_length],
                    data.iloc[segment_length:2*segment_length],
                    data.iloc[2*segment_length:]
                ]

                segment_returns = []
                for segment in segments:
                    if len(segment) > 1:
                        segment_return = (segment['close'].iloc[-1] - segment['close'].iloc[0]) / segment['close'].iloc[0]
                        segment_returns.append(segment_return)

                if len(segment_returns) == 3:
                    # 动量加速度
                    momentum_acceleration = segment_returns[2] - segment_returns[0]
                    features['momentum_acceleration'] = momentum_acceleration
                    features['momentum_accelerating'] = 1.0 if momentum_acceleration > 0.01 else 0.0

                    # 动量一致性
                    positive_segments = sum(1 for r in segment_returns if r > 0)
                    features['momentum_consistency'] = positive_segments / 3
                    features['consistent_momentum'] = 1.0 if positive_segments >= 2 else 0.0

            return features

        except Exception as e:
            logger.error(f"时段动量特征提取失败: {e}")
            return {}

    def _calculate_comprehensive_intraday_score(self, features: Dict[str, float]) -> Dict[str, float]:
        """计算综合分时评分"""
        try:
            score_components = {}

            # 1. 开盘强度评分
            opening_score = features.get('opening_strength_score', 0)
            score_components['opening_strength_score'] = opening_score

            # 2. 成交量分布评分
            volume_score = 0.0
            if 'morning_volume_ratio' in features:
                # 早盘成交量占比适中为好
                morning_ratio = features['morning_volume_ratio']
                if 0.3 <= morning_ratio <= 0.6:
                    volume_score += 0.4
                else:
                    volume_score += 0.4 * (1 - abs(morning_ratio - 0.45) * 2)

            if 'volume_concentration_high' in features and features['volume_concentration_high'] > 0:
                volume_score += 0.3

            if 'volume_increasing' in features and features['volume_increasing'] > 0:
                volume_score += 0.3

            score_components['volume_distribution_score'] = max(volume_score, 0)

            # 3. 价格稳定性评分
            stability_score = 0.0
            if 'price_stable' in features and features['price_stable'] > 0:
                stability_score += 0.5

            if 'intraday_volatility' in features:
                volatility = features['intraday_volatility']
                # 适度波动为好，太高或太低都不好
                if 0.005 <= volatility <= 0.015:
                    stability_score += 0.5
                else:
                    stability_score += 0.5 * (1 - abs(volatility - 0.01) * 50)

            score_components['price_stability_score'] = max(stability_score, 0)

            # 4. 尾盘表现评分
            tail_score = features.get('tail_strength_score', 0)
            score_components['tail_performance_score'] = tail_score

            # 5. 时段动量评分
            momentum_score = 0.0
            if 'consistent_momentum' in features and features['consistent_momentum'] > 0:
                momentum_score += 0.4

            if 'momentum_accelerating' in features and features['momentum_accelerating'] > 0:
                momentum_score += 0.3

            if 'morning_positive_momentum' in features and features['morning_positive_momentum'] > 0:
                momentum_score += 0.3

            score_components['session_momentum_score'] = momentum_score

            # 计算加权综合评分
            comprehensive_score = 0.0
            for component, weight in self.intraday_weights.items():
                score_key = f"{component}_score"
                if score_key in score_components:
                    comprehensive_score += score_components[score_key] * weight

            return {
                'intraday_comprehensive_score': comprehensive_score,
                'intraday_bullish': 1.0 if comprehensive_score > 0.6 else 0.0,
                'intraday_bearish': 1.0 if comprehensive_score < 0.3 else 0.0,
                **score_components
            }

        except Exception as e:
            logger.error(f"综合分时评分计算失败: {e}")
            return {'intraday_comprehensive_score': 0.0}

    def _get_empty_intraday_features(self) -> Dict[str, Any]:
        """获取空的分时特征"""
        return {
            'intraday_analysis_success': False,
            'total_intraday_features': 0,
            'intraday_comprehensive_score': 0.0,
            'error': '分时特征提取失败'
        }

def main():
    """测试分时特征优化器"""
    print("⏰ 测试分时特征优化器...")

    optimizer = IntradayFeaturesOptimizer()

    # 创建测试分时数据
    np.random.seed(42)

    # 生成标准交易时间
    trading_times = []

    # 上午时段 09:30-11:30
    current_time = datetime.strptime('09:30', '%H:%M')
    end_morning = datetime.strptime('11:30', '%H:%M')

    while current_time <= end_morning:
        trading_times.append(current_time.strftime('%H:%M'))
        current_time += timedelta(minutes=1)

    # 下午时段 13:00-15:00
    current_time = datetime.strptime('13:00', '%H:%M')
    end_afternoon = datetime.strptime('15:00', '%H:%M')

    while current_time <= end_afternoon:
        trading_times.append(current_time.strftime('%H:%M'))
        current_time += timedelta(minutes=1)

    # 生成模拟分时数据
    base_price = 10.0
    prices = []
    volumes = []

    for i, time_str in enumerate(trading_times):
        # 模拟不同时段的特征
        hour = int(time_str.split(':')[0])
        minute = int(time_str.split(':')[1])

        # 开盘阶段波动较大
        if hour == 9 and minute <= 45:
            price_change = np.random.randn() * 0.008 + 0.002
            volume_multiplier = 2.0
        # 尾盘阶段
        elif hour == 14 and minute >= 45:
            price_change = np.random.randn() * 0.006 + 0.001
            volume_multiplier = 1.5
        # 其他时段
        else:
            price_change = np.random.randn() * 0.004
            volume_multiplier = 1.0

        base_price *= (1 + price_change)
        prices.append(base_price)

        # 生成成交量
        base_volume = 100000
        volume = base_volume * volume_multiplier * (1 + np.random.randn() * 0.3)
        volumes.append(max(volume, 10000))

    test_minute_data = pd.DataFrame({
        'time': trading_times,
        'close': prices,
        'volume': volumes
    })

    # 提取分时特征
    print(f"\n🔍 测试分时特征提取...")
    print(f"分时数据长度: {len(test_minute_data)}分钟")

    features = optimizer.extract_intraday_features(test_minute_data)

    print(f"✅ 特征提取完成")
    print(f"分析成功: {features.get('intraday_analysis_success', False)}")
    print(f"总特征数: {features.get('total_intraday_features', 0)}")

    # 显示关键特征
    key_features = ['opening_strength_score', 'morning_volume_ratio', 'tail_strength_score',
                   'intraday_volatility', 'intraday_comprehensive_score']

    print(f"\n⏰ 关键分时特征:")
    for feature in key_features:
        if feature in features:
            print(f"  {feature}: {features[feature]:.3f}")

if __name__ == "__main__":
    main()
