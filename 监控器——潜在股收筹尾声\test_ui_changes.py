#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试UI修改：验证按钮变更
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import threading
import time

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_ui_buttons():
    """测试UI按钮修改"""
    print("🧪 开始测试UI按钮修改...")
    
    try:
        # 导入修改后的模块
        from 潜在股进一步筛选 import StockFurtherScreening
        
        # 创建筛选器实例
        screener = StockFurtherScreening()
        
        print("✅ 应用启动成功")
        
        # 检查按钮是否正确创建
        buttons_to_check = {
            'settings_btn': '⚙️ 设置',
            'analysis_btn': '🔍 四维度分析', 
            'reinit_btn': '🔄 重新初始化',
            'clear_all_btn': '🗑️ 全部清空'
        }
        
        # 检查应该存在的按钮
        print("\n📋 检查应该存在的按钮:")
        for btn_attr, btn_text in buttons_to_check.items():
            if hasattr(screener, btn_attr):
                button = getattr(screener, btn_attr)
                actual_text = button.cget('text')
                if actual_text == btn_text:
                    print(f"   ✅ {btn_attr}: {actual_text}")
                else:
                    print(f"   ⚠️ {btn_attr}: 期望 '{btn_text}', 实际 '{actual_text}'")
            else:
                print(f"   ❌ {btn_attr}: 按钮不存在")
        
        # 检查应该被删除的按钮
        removed_buttons = {
            'test_notification_btn': '🔔 测试通知',
            'test_data_btn': '📊 测试数据',
            'view_history_btn': '📋 历史数据'
        }
        
        print("\n📋 检查应该被删除的按钮:")
        for btn_attr, btn_text in removed_buttons.items():
            if hasattr(screener, btn_attr):
                print(f"   ❌ {btn_attr}: 按钮仍然存在（应该被删除）")
            else:
                print(f"   ✅ {btn_attr}: 按钮已正确删除")
        
        # 检查方法是否正确
        print("\n📋 检查方法:")
        
        # 应该存在的方法
        if hasattr(screener, 'clear_all_stocks'):
            print("   ✅ clear_all_stocks: 方法存在")
        else:
            print("   ❌ clear_all_stocks: 方法不存在")
        
        # 应该被删除的方法
        removed_methods = ['test_notification', 'test_data_fetch', 'view_historical_data']
        for method_name in removed_methods:
            if hasattr(screener, method_name):
                print(f"   ❌ {method_name}: 方法仍然存在（应该被删除）")
            else:
                print(f"   ✅ {method_name}: 方法已正确删除")
        
        # 测试清空功能（不实际执行，只检查方法可调用性）
        print("\n📋 测试清空功能:")
        try:
            # 检查方法是否可调用（但不实际调用，避免清空数据）
            clear_method = getattr(screener, 'clear_all_stocks', None)
            if clear_method and callable(clear_method):
                print("   ✅ clear_all_stocks 方法可调用")
            else:
                print("   ❌ clear_all_stocks 方法不可调用")
        except Exception as e:
            print(f"   ❌ 测试清空功能失败: {e}")
        
        print("\n✅ UI按钮修改测试完成")
        
        # 显示窗口几秒钟让用户看到界面
        print("\n👀 显示界面5秒钟供检查...")
        screener.root.after(5000, screener.root.destroy)  # 5秒后自动关闭
        screener.root.mainloop()
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试UI按钮修改")
    print("=" * 50)
    
    test_ui_buttons()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
