#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
二次启动确认层算法
实现量价共振模型和资金流验证
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from enhanced_data_fetcher import enhanced_stock_fetcher

class SecondLaunchConfirmator:
    """二次启动确认器"""
    
    def __init__(self):
        self.volume_ma_multiplier = 1.5  # 前日量能>MA5_VOL*1.5
        self.rsi_volume_threshold = 70  # 60分钟RSI_Volume>70
        self.big_order_threshold = 100  # 大单标准（100手）
        self.big_order_ratio_threshold = 0.15  # 大单净流入占比>15%
        self.tail_rise_threshold = 0.005  # 尾盘涨幅>0.5%
    
    def confirm_second_launch(self, stock_code, progress_callback=None):
        """确认二次启动信号"""
        try:
            if progress_callback:
                progress_callback(f"确认 {stock_code} 二次启动信号...")
            
            # 1. 量价共振模型验证
            volume_price_result = self._check_volume_price_resonance(stock_code)
            
            # 2. 资金流验证
            money_flow_result = self._verify_money_flow(stock_code)
            
            # 3. 综合判断
            confirmation_result = self._make_confirmation_decision(
                volume_price_result, money_flow_result
            )
            
            return {
                'confirmed': confirmation_result['confirmed'],
                'reason': confirmation_result['reason'],
                'confidence_score': confirmation_result['confidence_score'],
                'analysis_details': {
                    'volume_price_resonance': volume_price_result,
                    'money_flow_verification': money_flow_result,
                    'confirmation_analysis': confirmation_result
                }
            }
            
        except Exception as e:
            print(f"❌ 确认 {stock_code} 二次启动信号失败: {e}")
            return {
                'confirmed': False,
                'reason': f'确认失败: {e}',
                'confidence_score': 0,
                'analysis_details': {}
            }
    
    def _check_volume_price_resonance(self, stock_code):
        """检查量价共振模型"""
        try:
            # 获取日K线数据用于量价分析
            daily_data = self._get_daily_kline_data(stock_code, days=30)
            
            if daily_data.empty:
                return {
                    'resonance': False,
                    'reason': '无法获取日K线数据',
                    'volume_condition': False,
                    'price_breakout': False,
                    'rsi_volume': 0,
                    'no_limit_up': True
                }
            
            # 条件1：前日量能 > MA5_VOL * 1.5
            volume_condition = self._check_volume_condition(daily_data)
            
            # 条件2：最高价突破20日高点
            price_breakout = self._check_price_breakout(daily_data)
            
            # 条件3：60分钟RSI_Volume > 70（简化为成交量RSI）
            rsi_volume = self._calculate_volume_rsi(daily_data)
            rsi_condition = rsi_volume > self.rsi_volume_threshold
            
            # 条件4：未涨停留有余地
            no_limit_up = self._check_no_limit_up(daily_data)
            
            # 综合判断
            resonance = volume_condition and price_breakout and rsi_condition and no_limit_up
            
            if resonance:
                reason = '量价共振信号确认'
            else:
                failed_conditions = []
                if not volume_condition:
                    failed_conditions.append('量能不足')
                if not price_breakout:
                    failed_conditions.append('未突破高点')
                if not rsi_condition:
                    failed_conditions.append(f'RSI_Volume={rsi_volume:.1f}<70')
                if not no_limit_up:
                    failed_conditions.append('已涨停')
                reason = f'量价共振未确认: {", ".join(failed_conditions)}'
            
            return {
                'resonance': resonance,
                'reason': reason,
                'volume_condition': volume_condition,
                'price_breakout': price_breakout,
                'rsi_volume': rsi_volume,
                'no_limit_up': no_limit_up
            }
            
        except Exception as e:
            return {
                'resonance': False,
                'reason': f'量价共振检查失败: {e}',
                'volume_condition': False,
                'price_breakout': False,
                'rsi_volume': 0,
                'no_limit_up': True
            }
    
    def _verify_money_flow(self, stock_code):
        """验证资金流"""
        try:
            # 获取分时数据用于资金流分析
            minute_data = self._get_recent_minute_data(stock_code)
            
            if minute_data.empty:
                return {
                    'verified': False,
                    'reason': '无法获取分时数据',
                    'big_order_ratio': 0,
                    'last15_rise': 0
                }
            
            # 模拟大单流向分析
            big_order_analysis = self._simulate_big_order_flow(minute_data)
            
            # 尾盘抢筹特征检测
            tail_rise = self._detect_tail_buying(minute_data)
            
            # 验证条件
            big_order_qualified = big_order_analysis['big_order_ratio'] > self.big_order_ratio_threshold
            tail_rise_qualified = tail_rise > self.tail_rise_threshold
            
            verified = big_order_qualified and tail_rise_qualified
            
            if verified:
                reason = '资金流验证通过'
            else:
                failed_conditions = []
                if not big_order_qualified:
                    failed_conditions.append(f'大单比例{big_order_analysis["big_order_ratio"]:.1%}<15%')
                if not tail_rise_qualified:
                    failed_conditions.append(f'尾盘涨幅{tail_rise:.1%}<0.5%')
                reason = f'资金流验证未通过: {", ".join(failed_conditions)}'
            
            return {
                'verified': verified,
                'reason': reason,
                'big_order_ratio': big_order_analysis['big_order_ratio'],
                'last15_rise': tail_rise,
                'big_order_details': big_order_analysis
            }
            
        except Exception as e:
            return {
                'verified': False,
                'reason': f'资金流验证失败: {e}',
                'big_order_ratio': 0,
                'last15_rise': 0
            }
    
    def _get_daily_kline_data(self, stock_code, days=30):
        """获取日K线数据"""
        try:
            # 使用现有的K线数据获取方法
            kline_data = enhanced_stock_fetcher.get_daily_kline(stock_code, days)
            return kline_data if not kline_data.empty else pd.DataFrame()
        except Exception as e:
            print(f"⚠️ 获取 {stock_code} 日K线数据失败: {e}")
            return pd.DataFrame()
    
    def _get_recent_minute_data(self, stock_code):
        """获取最近的分时数据"""
        try:
            minute_result = enhanced_stock_fetcher.get_historical_minute_data(stock_code)
            
            if minute_result and minute_result.get('data_available', False):
                return minute_result['minute_data']
            else:
                return pd.DataFrame()
        except Exception as e:
            print(f"⚠️ 获取 {stock_code} 分时数据失败: {e}")
            return pd.DataFrame()
    
    def _check_volume_condition(self, daily_data):
        """检查前日量能条件"""
        try:
            if '成交量' not in daily_data.columns or len(daily_data) < 6:
                return False
            
            volumes = daily_data['成交量']
            
            # 计算5日平均成交量
            ma5_volume = volumes.rolling(window=5).mean()
            
            # 检查前日量能是否大于MA5*1.5
            if len(volumes) >= 2:
                yesterday_volume = volumes.iloc[-2]  # 前日成交量
                ma5_yesterday = ma5_volume.iloc[-2]  # 前日的MA5
                
                return yesterday_volume > ma5_yesterday * self.volume_ma_multiplier
            
            return False
            
        except Exception as e:
            return False
    
    def _check_price_breakout(self, daily_data):
        """检查价格突破20日高点"""
        try:
            if '最高' not in daily_data.columns or len(daily_data) < 21:
                return False
            
            highs = daily_data['最高']
            
            # 计算20日最高价（不包括当日）
            if len(highs) >= 21:
                high_20d = highs.iloc[-21:-1].max()  # 前20日最高价
                current_high = highs.iloc[-1]  # 当日最高价
                
                return current_high > high_20d
            
            return False
            
        except Exception as e:
            return False
    
    def _calculate_volume_rsi(self, daily_data):
        """计算成交量RSI"""
        try:
            if '成交量' not in daily_data.columns or len(daily_data) < 14:
                return 0
            
            volumes = daily_data['成交量']
            
            # 计算成交量变化
            volume_changes = volumes.diff()
            
            # 计算RSI
            gains = volume_changes.where(volume_changes > 0, 0)
            losses = -volume_changes.where(volume_changes < 0, 0)
            
            avg_gains = gains.rolling(window=14).mean()
            avg_losses = losses.rolling(window=14).mean()
            
            rs = avg_gains / avg_losses
            rsi = 100 - (100 / (1 + rs))
            
            return float(rsi.iloc[-1]) if not pd.isna(rsi.iloc[-1]) else 0
            
        except Exception as e:
            return 0
    
    def _check_no_limit_up(self, daily_data):
        """检查未涨停留有余地"""
        try:
            if '收盘' not in daily_data.columns or '开盘' not in daily_data.columns or len(daily_data) < 1:
                return True
            
            # 简化判断：当日涨幅是否接近10%
            close_price = daily_data['收盘'].iloc[-1]
            open_price = daily_data['开盘'].iloc[-1]
            
            if open_price > 0:
                daily_change = (close_price - open_price) / open_price
                return daily_change < 0.095  # 涨幅小于9.5%认为未涨停
            
            return True
            
        except Exception as e:
            return True
    
    def _simulate_big_order_flow(self, minute_data):
        """模拟大单流向分析"""
        try:
            if minute_data.empty or '成交量' not in minute_data.columns:
                return {'big_order_ratio': 0, 'big_order_net': 0, 'total_turnover': 0}
            
            volumes = minute_data['成交量']
            
            # 简化大单识别：成交量大于平均值2倍的认为是大单
            avg_volume = volumes.mean()
            big_order_threshold_volume = avg_volume * 2
            
            # 模拟大单净流入计算
            big_order_volumes = volumes[volumes >= big_order_threshold_volume]
            big_order_net = big_order_volumes.sum()
            total_turnover = volumes.sum()
            
            big_order_ratio = big_order_net / total_turnover if total_turnover > 0 else 0
            
            return {
                'big_order_ratio': big_order_ratio,
                'big_order_net': big_order_net,
                'total_turnover': total_turnover
            }
            
        except Exception as e:
            return {'big_order_ratio': 0, 'big_order_net': 0, 'total_turnover': 0}
    
    def _detect_tail_buying(self, minute_data):
        """检测尾盘抢筹特征"""
        try:
            if minute_data.empty or '收盘' not in minute_data.columns or len(minute_data) < 15:
                return 0
            
            prices = minute_data['收盘']
            
            # 计算最后15分钟的涨幅
            if len(prices) >= 15:
                last_15min_start = prices.iloc[-15]
                last_price = prices.iloc[-1]
                
                if last_15min_start > 0:
                    tail_rise = (last_price - last_15min_start) / last_15min_start
                    return tail_rise
            
            return 0
            
        except Exception as e:
            return 0
    
    def _make_confirmation_decision(self, volume_price_result, money_flow_result):
        """做出确认决策"""
        try:
            # 计算置信度评分
            confidence_score = 0
            
            # 量价共振评分（60分）
            if volume_price_result['resonance']:
                confidence_score += 60
            else:
                # 部分条件满足也给分
                if volume_price_result['volume_condition']:
                    confidence_score += 15
                if volume_price_result['price_breakout']:
                    confidence_score += 15
                if volume_price_result['rsi_volume'] > 50:
                    confidence_score += 15
                if volume_price_result['no_limit_up']:
                    confidence_score += 15
            
            # 资金流验证评分（40分）
            if money_flow_result['verified']:
                confidence_score += 40
            else:
                # 部分条件满足也给分
                if money_flow_result['big_order_ratio'] > 0.1:
                    confidence_score += 20
                if money_flow_result['last15_rise'] > 0.002:
                    confidence_score += 20
            
            # 确认条件：置信度≥70分
            confirmed = confidence_score >= 70
            
            if confirmed:
                reason = f'二次启动确认 (置信度{confidence_score:.0f}分)'
            else:
                reason = f'二次启动未确认 (置信度{confidence_score:.0f}分<70)'
            
            return {
                'confirmed': confirmed,
                'reason': reason,
                'confidence_score': confidence_score
            }
            
        except Exception as e:
            return {
                'confirmed': False,
                'reason': f'确认决策失败: {e}',
                'confidence_score': 0
            }
    
    def batch_confirm_stocks(self, stock_codes, progress_callback=None):
        """批量确认股票的二次启动信号"""
        try:
            results = []
            total_stocks = len(stock_codes)
            
            for i, stock_code in enumerate(stock_codes):
                try:
                    if progress_callback:
                        progress = (i + 1) / total_stocks * 100
                        progress_callback(progress, f"二次启动确认: {stock_code}")
                    
                    confirmation_result = self.confirm_second_launch(stock_code)
                    
                    result = {
                        'code': stock_code,
                        'second_launch_confirmation': confirmation_result
                    }
                    
                    results.append(result)
                    
                    status = "确认" if confirmation_result['confirmed'] else "未确认"
                    score = confirmation_result['confidence_score']
                    print(f"✅ {stock_code}: 二次启动 {status} (置信度{score:.0f}分)")
                    
                except Exception as e:
                    print(f"❌ 确认 {stock_code} 二次启动失败: {e}")
                    continue
            
            # 筛选确认的股票
            confirmed_stocks = [r for r in results if r['second_launch_confirmation']['confirmed']]
            
            print(f"📊 二次启动确认完成:")
            print(f"   分析股票: {len(results)} 只")
            print(f"   确认股票: {len(confirmed_stocks)} 只")
            print(f"   确认率: {len(confirmed_stocks)/len(results)*100:.1f}%" if results else "   确认率: 0.0%")
            
            return confirmed_stocks
            
        except Exception as e:
            print(f"❌ 批量二次启动确认失败: {e}")
            return []

def test_second_launch_confirmator():
    """测试二次启动确认器"""
    print("🧪 测试二次启动确认器")
    
    try:
        confirmator = SecondLaunchConfirmator()
        
        # 测试股票
        test_stocks = ['000001', '000002', '600036']
        
        print(f"📊 批量确认 {len(test_stocks)} 只股票的二次启动信号:")
        
        confirmed_stocks = confirmator.batch_confirm_stocks(test_stocks)
        
        if confirmed_stocks:
            print(f"\n🏆 确认二次启动的股票:")
            for stock in confirmed_stocks:
                confirmation = stock['second_launch_confirmation']
                details = confirmation['analysis_details']
                print(f"   {stock['code']}: {confirmation['reason']}")
                print(f"     量价共振: {'✅' if details['volume_price_resonance']['resonance'] else '❌'}")
                print(f"     资金流验证: {'✅' if details['money_flow_verification']['verified'] else '❌'}")
        else:
            print("⚠️ 无股票确认二次启动信号")
        
        return len(confirmed_stocks) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_second_launch_confirmator()
