#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
同花顺板块数据解析器
专门解析同花顺的行业板块数据
"""

import requests
import re
import json
from datetime import datetime
from bs4 import BeautifulSoup

def get_ths_sector_data():
    """获取同花顺板块数据"""
    try:
        print("🔍 获取同花顺板块数据...")
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'http://q.10jqka.com.cn/',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        }
        
        # 同花顺行业板块数据接口
        url = "http://q.10jqka.com.cn/thshy/index/field/199112/order/desc/page/1/ajax/1/"
        
        response = requests.get(url, headers=headers, timeout=15)
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
        content = response.text
        print(f"✅ 获取成功，数据长度: {len(content)}")
        
        return content
        
    except Exception as e:
        print(f"❌ 获取同花顺数据失败: {e}")
        return None

def parse_ths_sector_data(content):
    """解析同花顺板块数据"""
    try:
        print("\n🔍 解析同花顺板块数据...")

        # 使用BeautifulSoup解析HTML
        soup = BeautifulSoup(content, 'html.parser')

        # 查找表格数据
        table = soup.find('table')
        if not table:
            print("❌ 未找到数据表格")
            return {}

        # 解析表头
        headers = []
        header_row = table.find('thead')
        if header_row:
            for th in header_row.find_all('th'):
                headers.append(th.get_text(strip=True))

        print(f"✅ 表头: {headers}")

        # 解析数据行
        sectors = {}
        tbody = table.find('tbody')
        if tbody:
            rows = tbody.find_all('tr')
            print(f"✅ 找到 {len(rows)} 行数据")

            for i, row in enumerate(rows):
                cells = row.find_all('td')
                if len(cells) >= 3:  # 至少需要序号、名称、涨跌幅
                    try:
                        # 根据表头结构解析
                        # 序号、板块、涨跌幅(%)、...

                        # 第二列是板块名称
                        name_cell = cells[1].get_text(strip=True) if len(cells) > 1 else None

                        # 第三列是涨跌幅
                        change_text = cells[2].get_text(strip=True) if len(cells) > 2 else None

                        if name_cell and change_text:
                            # 清理板块名称（移除链接等）
                            name_link = cells[1].find('a')
                            if name_link:
                                name_cell = name_link.get_text(strip=True)

                            # 解析涨跌幅
                            try:
                                change_text = change_text.replace('%', '').replace('+', '').replace('--', '0')
                                change_value = float(change_text)
                            except ValueError:
                                print(f"⚠️ 无法解析涨跌幅: {change_text}")
                                continue

                            sectors[name_cell] = {
                                'name': name_cell,
                                'change': change_value,
                                'raw_data': [cell.get_text(strip=True) for cell in cells]
                            }

                            # 显示前几个解析结果用于调试
                            if i < 5:
                                print(f"  解析: {name_cell} -> {change_value:+.2f}%")

                    except Exception as e:
                        print(f"⚠️ 解析第{i+1}行失败: {e}")
                        # 显示原始数据用于调试
                        if i < 3:
                            raw_texts = [cell.get_text(strip=True) for cell in cells]
                            print(f"    原始数据: {raw_texts}")
                        continue

        print(f"✅ 成功解析 {len(sectors)} 个板块")
        return sectors

    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return {}

def find_sector_by_name_ths(sectors, target_name):
    """根据名称查找同花顺板块"""
    # 精确匹配
    if target_name in sectors:
        return sectors[target_name]
    
    # 板块名称映射（同花顺特有）
    ths_mapping = {
        '汽车零部件': ['汽车整车', '汽车服务', '汽车行业', '汽车'],
        '文化传媒': ['传媒', '影视音像', '广告包装', '文化传播'],
        '通用设备': ['机械', '通用机械', '专用机械', '机械设备'],
        '造纸印刷': ['造纸', '印刷包装', '包装材料'],
        '电子元器件': ['电子', '电子器件', '半导体', '集成电路'],
        '医药生物': ['医药', '生物制药', '中药', '医疗器械'],
        '银行': ['银行业', '商业银行'],
        '保险': ['保险业'],
        '证券': ['证券业', '券商'],
        '房地产': ['房地产业', '地产'],
        '化工': ['化工业', '石油化工', '精细化工'],
        '建筑': ['建筑业', '建筑装饰', '基建'],
        '食品饮料': ['食品', '饮料', '酿酒'],
        '纺织服装': ['纺织', '服装'],
        '交通运输': ['运输', '物流', '航空', '铁路'],
        '电力': ['电力业', '公用事业'],
        '商贸': ['商业', '零售', '贸易']
    }
    
    # 尝试映射匹配
    for mapped_names in ths_mapping.get(target_name, []):
        for name, data in sectors.items():
            if mapped_names in name or name in mapped_names:
                return data
    
    # 模糊匹配
    for name, data in sectors.items():
        if target_name in name or name in target_name:
            return data
    
    return None

def test_ths_sector_parsing():
    """测试同花顺板块数据解析"""
    print("🚀 同花顺板块数据解析测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("="*80)
    
    # 获取数据
    content = get_ths_sector_data()
    if not content:
        return
    
    # 解析数据
    sectors = parse_ths_sector_data(content)
    
    if not sectors:
        print("❌ 解析失败")
        return
    
    print(f"\n📊 解析结果统计:")
    print(f"总板块数: {len(sectors)}")
    
    # 显示所有板块
    print(f"\n📋 所有板块列表:")
    sorted_sectors = sorted(sectors.items(), key=lambda x: x[1]['change'], reverse=True)
    
    for i, (name, data) in enumerate(sorted_sectors, 1):
        change = data['change']
        print(f"{i:2d}. {name:15s} | 涨跌: {change:+6.2f}%")
    
    # 查找特定板块
    test_names = ["汽车零部件", "文化传媒", "通用设备", "造纸印刷"]
    print(f"\n🔍 测试特定板块查找:")
    
    for test_name in test_names:
        result = find_sector_by_name_ths(sectors, test_name)
        if result:
            print(f"  ✅ {test_name}: {result['change']:+.2f}%")
        else:
            print(f"  ❌ {test_name}: 未找到")
    
    # 显示涨幅前10的板块
    print(f"\n📈 涨幅前10的板块:")
    for i, (name, data) in enumerate(sorted_sectors[:10], 1):
        change = data['change']
        print(f"{i:2d}. {name}: {change:+.2f}%")
    
    print("\n" + "="*80)
    print("✅ 测试完成")
    
    return sectors

def create_ths_integration():
    """创建同花顺集成方案"""
    print("\n🔧 同花顺集成方案:")
    print("1. 同花顺API相对稳定，数据更新及时")
    print("2. 需要HTML解析，比JSON接口复杂一些")
    print("3. 可以作为新浪财经的备用数据源")
    print("4. 建议实现缓存机制，避免频繁请求")
    
    integration_code = '''
# 集成到real_data_interface.py的示例代码
def _get_sector_performance_from_ths(self, sector_name: str) -> Dict:
    """从同花顺获取板块表现数据"""
    try:
        # 获取同花顺板块数据
        content = self._get_ths_sector_data()
        if not content:
            return {'data_available': False, 'morning_gain': 0.0}
        
        # 解析板块数据
        sectors = self._parse_ths_sector_data(content)
        if not sectors:
            return {'data_available': False, 'morning_gain': 0.0}
        
        # 查找目标板块
        target_sector = self._find_ths_sector_by_name(sectors, sector_name)
        
        if target_sector:
            change_pct = target_sector['change']
            
            result = {
                'morning_gain': change_pct,
                'limit_up_count': 0,
                'total_stocks': 0,
                'limit_up_ratio': 0.0,
                'sector_strength': 'moderate' if abs(change_pct) < 2 else 'strong',
                'data_available': True,
                'data_source': 'tonghuashun',
                'sector_matched': target_sector['name']
            }
            
            logger.info(f"✅ 同花顺获取板块{sector_name}表现: {change_pct:.2f}%")
            return result
        else:
            return {'data_available': False, 'morning_gain': 0.0}
            
    except Exception as e:
        logger.error(f"同花顺获取板块数据失败: {e}")
        return {'data_available': False, 'morning_gain': 0.0}
    '''
    
    print(f"\n💻 集成代码示例:")
    print(integration_code)

def main():
    """主测试函数"""
    print("🚀 同花顺板块数据解析器")
    print("="*80)
    
    # 测试同花顺板块数据解析
    sectors = test_ths_sector_parsing()
    
    if sectors:
        create_ths_integration()

if __name__ == "__main__":
    main()
