#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游资席位数据管理器
类似limit_up_data的功能，管理过去一年的游资席位数据
"""

import os
import json
import threading
import time
from datetime import datetime, timedelta
import pandas as pd

class YouziSeatsManager:
    """游资席位数据管理器"""
    
    def __init__(self):
        self.data_dir = "youzi_seats_data"
        self.status_file = os.path.join(self.data_dir, "download_status.json")
        self.index_file = os.path.join(self.data_dir, "data_index.json")
        
        # 创建数据目录
        os.makedirs(self.data_dir, exist_ok=True)
        
        # 下载控制
        self.is_downloading = False
        self.download_thread = None
        
        # 知名游资席位数据库
        self.famous_seats_db = self._init_famous_seats_database()
        
        print(f"📁 游资席位数据目录: {self.data_dir}")
    
    def _init_famous_seats_database(self):
        """初始化知名游资席位数据库"""
        return {
            # 华鑫系游资（顶级）
            "华鑫系": [
                "华鑫证券有限责任公司上海分公司",
                "华鑫证券有限责任公司宁波分公司",
                "华鑫证券上海分公司",
                "华鑫证券宁波分公司"
            ],
            
            # 中信系游资
            "中信系": [
                "中信证券股份有限公司上海溧阳路证券营业部",
                "中信证券股份有限公司北京远大路证券营业部",
                "中信证券上海溧阳路",
                "中信证券北京远大路"
            ],
            
            # 华泰系游资
            "华泰系": [
                "华泰证券股份有限公司上海武定路证券营业部",
                "华泰证券股份有限公司深圳益田路证券营业部",
                "华泰证券上海武定路",
                "华泰证券深圳益田路"
            ],
            
            # 银河系游资
            "银河系": [
                "中国银河证券股份有限公司绍兴证券营业部",
                "银河证券绍兴",
                "中国银河证券绍兴"
            ],
            
            # 其他知名游资
            "光大系": ["光大证券股份有限公司佛山绿景路证券营业部"],
            "国泰系": ["国泰君安证券股份有限公司深圳益田路证券营业部"],
            "申万系": ["申万宏源证券有限公司上海东川路证券营业部"],
            "海通系": ["海通证券股份有限公司大连黄河路证券营业部"],
            
            # 散户营业部（用于风险识别）
            "散户营业部": [
                "东方财富证券股份有限公司拉萨团结路第一证券营业部",
                "东方财富证券拉萨团结路",
                "东方财富拉萨"
            ]
        }
    
    def check_data_completeness(self):
        """检查数据完整性"""
        try:
            # 计算需要的日期范围（过去一年的交易日）
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            
            required_dates = self._get_trading_dates(start_date, end_date)
            existing_dates = self._get_existing_dates()
            missing_dates = [date for date in required_dates if date not in existing_dates]
            
            # 清理过期数据
            self._cleanup_old_data(required_dates)
            
            return {
                'is_complete': len(missing_dates) == 0,
                'required_days': len(required_dates),
                'existing_days': len(existing_dates),
                'missing_days': missing_dates,
                'total_missing': len(missing_dates)
            }
        except Exception as e:
            print(f"❌ 检查游资席位数据完整性失败: {e}")
            return {
                'is_complete': False,
                'required_days': 0,
                'existing_days': 0,
                'missing_days': [],
                'total_missing': 0
            }
    
    def _get_trading_dates(self, start_date, end_date):
        """获取交易日期列表（排除周末）"""
        dates = []
        current = start_date
        while current <= end_date:
            # 排除周末
            if current.weekday() < 5:  # 0-4是周一到周五
                dates.append(current.strftime('%Y%m%d'))
            current += timedelta(days=1)
        return dates
    
    def _get_existing_dates(self):
        """获取已存在的数据日期"""
        existing_dates = []
        if os.path.exists(self.data_dir):
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.json') and len(filename) == 13:  # YYYYMMDD.json
                    date_str = filename[:8]
                    if date_str.isdigit():
                        existing_dates.append(date_str)
        return existing_dates
    
    def _cleanup_old_data(self, required_dates):
        """清理过期数据"""
        try:
            existing_dates = self._get_existing_dates()
            for date in existing_dates:
                if date not in required_dates:
                    file_path = os.path.join(self.data_dir, f"{date}.json")
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        print(f"🗑️ 删除过期游资席位数据: {date}")
        except Exception as e:
            print(f"⚠️ 清理过期游资席位数据失败: {e}")
    
    def start_background_download(self, progress_callback=None, complete_callback=None):
        """启动后台下载任务"""
        if self.is_downloading:
            print("⚠️ 游资席位数据下载任务已在进行中")
            return
        
        self.is_downloading = True
        
        def download_worker():
            try:
                # 检查需要下载的日期
                status = self.check_data_completeness()
                missing_dates = status['missing_days']
                
                if not missing_dates:
                    if complete_callback:
                        complete_callback(True, "游资席位数据已完整")
                    return
                
                print(f"📊 开始下载 {len(missing_dates)} 个交易日的游资席位数据")
                
                # 按日期倒序下载（从最新开始）
                missing_dates.sort(reverse=True)
                
                success_count = 0
                total_count = len(missing_dates)
                
                for i, date in enumerate(missing_dates):
                    try:
                        # 更新进度
                        if progress_callback:
                            progress_callback(i + 1, total_count, date)
                        
                        # 下载单日数据
                        success = self._download_single_date(date)
                        if success:
                            success_count += 1
                            print(f"✅ {date}: 游资席位数据下载成功 ({i+1}/{total_count})")
                        else:
                            print(f"❌ {date}: 游资席位数据下载失败 ({i+1}/{total_count})")
                        
                        # 添加延迟避免API限制
                        time.sleep(3)  # 3秒延迟，比涨停数据稍长
                        
                    except Exception as e:
                        print(f"❌ {date}: 游资席位数据下载异常 - {e}")
                        continue
                
                # 下载完成
                message = f"游资席位数据下载完成: {success_count}/{total_count} 成功"
                if complete_callback:
                    complete_callback(success_count > 0, message)
                
            except Exception as e:
                print(f"❌ 游资席位数据下载任务异常: {e}")
                if complete_callback:
                    complete_callback(False, f"下载异常: {e}")
            finally:
                self.is_downloading = False
        
        # 启动下载线程
        self.download_thread = threading.Thread(target=download_worker, daemon=True)
        self.download_thread.start()
    
    def _download_single_date(self, date):
        """下载单日游资席位数据"""
        try:
            # 使用AKShare获取龙虎榜营业部数据
            success = self._download_with_akshare(date)
            if success:
                return True
            
            # 如果AKShare失败，保存空数据文件
            print(f"⚠️ {date} 无游资席位数据，保存空记录")
            self._save_empty_data(date)
            return True
            
        except Exception as e:
            print(f"❌ 下载 {date} 游资席位数据失败: {e}")
            return False
    
    def _download_with_akshare(self, date):
        """使用AKShare获取游资席位数据"""
        try:
            import akshare as ak
            
            print(f"🔍 获取 {date} 的龙虎榜营业部数据...")
            
            # 获取当日龙虎榜营业部数据
            lhb_seats = ak.stock_lhb_hyyyb_em(start_date=date, end_date=date)
            
            if not lhb_seats.empty:
                print(f"✅ 获取到 {len(lhb_seats)} 条龙虎榜营业部记录")
                
                # 处理和分析数据
                processed_data = self._process_lhb_seats_data(lhb_seats, date)
                
                # 保存数据
                self._save_seats_data(date, processed_data, lhb_seats)
                
                return True
            else:
                print(f"⚠️ {date} 无龙虎榜营业部数据")
                self._save_empty_data(date)
                return True
                
        except Exception as e:
            print(f"❌ AKShare获取{date}游资席位数据失败: {e}")
            return False
    
    def _process_lhb_seats_data(self, lhb_seats, date):
        """处理龙虎榜营业部数据"""
        try:
            processed_seats = []
            famous_seats_found = []
            
            for _, row in lhb_seats.iterrows():
                seat_name = str(row.get('营业部名称', ''))
                if not seat_name or seat_name == 'nan':
                    continue
                
                # 识别游资类型
                seat_type = self._identify_seat_type(seat_name)
                
                seat_info = {
                    'seat_name': seat_name,
                    'seat_type': seat_type,
                    'buy_count': int(row.get('买入个股数', 0)),
                    'sell_count': int(row.get('卖出个股数', 0)),
                    'buy_amount': float(row.get('买入总金额', 0)),
                    'sell_amount': float(row.get('卖出总金额', 0)),
                    'net_amount': float(row.get('总买卖净额', 0)),
                    'buy_stocks': str(row.get('买入股票', '')),
                    'is_famous': seat_type != '普通营业部'
                }
                
                processed_seats.append(seat_info)
                
                # 记录知名游资
                if seat_info['is_famous']:
                    famous_seats_found.append({
                        'seat_name': seat_name,
                        'seat_type': seat_type,
                        'net_amount': seat_info['net_amount']
                    })
            
            return {
                'total_seats': len(processed_seats),
                'famous_seats_count': len(famous_seats_found),
                'seats_detail': processed_seats,
                'famous_seats': famous_seats_found,
                'analysis_summary': self._analyze_seats_activity(processed_seats)
            }
            
        except Exception as e:
            print(f"❌ 处理游资席位数据失败: {e}")
            return {
                'total_seats': 0,
                'famous_seats_count': 0,
                'seats_detail': [],
                'famous_seats': [],
                'analysis_summary': {}
            }
    
    def _identify_seat_type(self, seat_name):
        """识别营业部类型"""
        for seat_type, seat_list in self.famous_seats_db.items():
            for known_seat in seat_list:
                if known_seat in seat_name or any(part in seat_name for part in known_seat.split()):
                    return seat_type
        return '普通营业部'
    
    def _analyze_seats_activity(self, seats_data):
        """分析席位活跃度"""
        try:
            if not seats_data:
                return {}
            
            # 按类型统计
            type_stats = {}
            total_net_amount = 0
            
            for seat in seats_data:
                seat_type = seat['seat_type']
                net_amount = seat['net_amount']
                
                if seat_type not in type_stats:
                    type_stats[seat_type] = {
                        'count': 0,
                        'total_net_amount': 0,
                        'avg_net_amount': 0
                    }
                
                type_stats[seat_type]['count'] += 1
                type_stats[seat_type]['total_net_amount'] += net_amount
                total_net_amount += net_amount
            
            # 计算平均值
            for seat_type in type_stats:
                count = type_stats[seat_type]['count']
                if count > 0:
                    type_stats[seat_type]['avg_net_amount'] = type_stats[seat_type]['total_net_amount'] / count
            
            return {
                'type_distribution': type_stats,
                'total_net_amount': total_net_amount,
                'market_sentiment': self._assess_market_sentiment(type_stats)
            }
            
        except Exception as e:
            print(f"❌ 分析席位活跃度失败: {e}")
            return {}
    
    def _assess_market_sentiment(self, type_stats):
        """评估市场情绪"""
        try:
            # 计算知名游资vs散户营业部的资金对比
            famous_amount = 0
            scatter_amount = 0
            
            for seat_type, stats in type_stats.items():
                if seat_type == '散户营业部':
                    scatter_amount += stats['total_net_amount']
                elif seat_type != '普通营业部':
                    famous_amount += stats['total_net_amount']
            
            total_amount = famous_amount + scatter_amount
            
            if total_amount > 0:
                famous_ratio = famous_amount / total_amount
                if famous_ratio > 0.7:
                    return '游资主导'
                elif famous_ratio > 0.3:
                    return '游资散户混合'
                else:
                    return '散户主导'
            else:
                return '无明显趋势'
                
        except Exception as e:
            return '分析失败'
    
    def _save_seats_data(self, date, processed_data, raw_data):
        """保存游资席位数据"""
        try:
            data = {
                'date': date,
                'download_time': datetime.now().isoformat(),
                'data_source': 'akshare_lhb_seats',
                'summary': {
                    'total_seats': processed_data['total_seats'],
                    'famous_seats_count': processed_data['famous_seats_count'],
                    'market_sentiment': processed_data['analysis_summary'].get('market_sentiment', '未知')
                },
                'processed_data': processed_data,
                'raw_data_count': len(raw_data)
            }
            
            file_path = os.path.join(self.data_dir, f"{date}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"❌ 保存游资席位数据失败: {e}")
            return False
    
    def _save_empty_data(self, date):
        """保存空数据记录"""
        try:
            data = {
                'date': date,
                'download_time': datetime.now().isoformat(),
                'data_source': 'empty',
                'summary': {
                    'total_seats': 0,
                    'famous_seats_count': 0,
                    'market_sentiment': '无数据'
                },
                'processed_data': {
                    'total_seats': 0,
                    'famous_seats_count': 0,
                    'seats_detail': [],
                    'famous_seats': [],
                    'analysis_summary': {}
                },
                'raw_data_count': 0
            }
            
            file_path = os.path.join(self.data_dir, f"{date}.json")
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            return True
            
        except Exception as e:
            print(f"❌ 保存空游资席位数据失败: {e}")
            return False

    def get_youzi_activity_statistics(self):
        """获取游资活跃度统计"""
        try:
            print("📊 统计本地游资席位数据...")

            existing_dates = self._get_existing_dates()
            if not existing_dates:
                print("⚠️ 无本地游资席位数据")
                return {}

            # 统计数据
            total_days = len(existing_dates)
            famous_seats_stats = {}
            daily_activity = []

            for date in existing_dates:
                try:
                    file_path = os.path.join(self.data_dir, f"{date}.json")
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    processed_data = data.get('processed_data', {})
                    famous_seats = processed_data.get('famous_seats', [])

                    # 统计每日活跃度
                    daily_activity.append({
                        'date': date,
                        'total_seats': processed_data.get('total_seats', 0),
                        'famous_seats_count': processed_data.get('famous_seats_count', 0),
                        'market_sentiment': processed_data.get('analysis_summary', {}).get('market_sentiment', '未知')
                    })

                    # 统计知名游资出现频次
                    for seat in famous_seats:
                        seat_name = seat['seat_name']
                        seat_type = seat['seat_type']

                        if seat_name not in famous_seats_stats:
                            famous_seats_stats[seat_name] = {
                                'seat_type': seat_type,
                                'appearance_count': 0,
                                'total_net_amount': 0,
                                'dates': []
                            }

                        famous_seats_stats[seat_name]['appearance_count'] += 1
                        famous_seats_stats[seat_name]['total_net_amount'] += seat.get('net_amount', 0)
                        famous_seats_stats[seat_name]['dates'].append(date)

                except Exception as e:
                    print(f"⚠️ 读取{date}数据失败: {e}")
                    continue

            # 计算活跃度排名
            active_seats = []
            for seat_name, stats in famous_seats_stats.items():
                activity_score = stats['appearance_count'] / total_days * 100
                active_seats.append({
                    'seat_name': seat_name,
                    'seat_type': stats['seat_type'],
                    'appearance_count': stats['appearance_count'],
                    'activity_rate': activity_score,
                    'avg_net_amount': stats['total_net_amount'] / stats['appearance_count'] if stats['appearance_count'] > 0 else 0
                })

            # 按活跃度排序
            active_seats.sort(key=lambda x: x['activity_rate'], reverse=True)

            print(f"✅ 游资席位统计完成: {total_days}个交易日, {len(famous_seats_stats)}个知名席位")

            return {
                'total_days': total_days,
                'total_famous_seats': len(famous_seats_stats),
                'active_seats_ranking': active_seats[:20],  # 前20名最活跃
                'daily_activity': daily_activity,
                'summary': {
                    'most_active_seat': active_seats[0] if active_seats else None,
                    'avg_daily_famous_seats': sum(day['famous_seats_count'] for day in daily_activity) / len(daily_activity) if daily_activity else 0
                }
            }

        except Exception as e:
            print(f"❌ 统计游资席位数据失败: {e}")
            return {}

    def get_stock_youzi_history(self, stock_code):
        """获取特定股票的游资操作历史"""
        try:
            print(f"🔍 查询股票 {stock_code} 的游资操作历史...")

            existing_dates = self._get_existing_dates()
            if not existing_dates:
                return {}

            stock_youzi_history = []

            for date in existing_dates:
                try:
                    file_path = os.path.join(self.data_dir, f"{date}.json")
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    processed_data = data.get('processed_data', {})
                    seats_detail = processed_data.get('seats_detail', [])

                    # 查找涉及该股票的席位
                    for seat in seats_detail:
                        buy_stocks = seat.get('buy_stocks', '')
                        if stock_code in buy_stocks:
                            stock_youzi_history.append({
                                'date': date,
                                'seat_name': seat['seat_name'],
                                'seat_type': seat['seat_type'],
                                'is_famous': seat['is_famous'],
                                'net_amount': seat['net_amount'],
                                'buy_amount': seat['buy_amount'],
                                'sell_amount': seat['sell_amount']
                            })

                except Exception as e:
                    continue

            # 按日期排序
            stock_youzi_history.sort(key=lambda x: x['date'], reverse=True)

            # 统计分析
            famous_operations = [op for op in stock_youzi_history if op['is_famous']]
            total_net_amount = sum(op['net_amount'] for op in famous_operations)

            analysis = {
                'total_operations': len(stock_youzi_history),
                'famous_operations': len(famous_operations),
                'total_net_amount': total_net_amount,
                'recent_activity': stock_youzi_history[:10],  # 最近10次操作
                'seat_types': {}
            }

            # 按席位类型统计
            for op in famous_operations:
                seat_type = op['seat_type']
                if seat_type not in analysis['seat_types']:
                    analysis['seat_types'][seat_type] = {
                        'count': 0,
                        'net_amount': 0
                    }
                analysis['seat_types'][seat_type]['count'] += 1
                analysis['seat_types'][seat_type]['net_amount'] += op['net_amount']

            print(f"✅ 查询完成: 发现 {len(stock_youzi_history)} 次游资操作记录")

            return {
                'stock_code': stock_code,
                'history': stock_youzi_history,
                'analysis': analysis
            }

        except Exception as e:
            print(f"❌ 查询股票游资历史失败: {e}")
            return {}

    def get_recent_youzi_activity(self, days=30):
        """获取最近N天的游资活跃情况"""
        try:
            print(f"📊 获取最近{days}天的游资活跃情况...")

            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)
            target_dates = self._get_trading_dates(start_date, end_date)

            recent_activity = []

            for date in target_dates:
                file_path = os.path.join(self.data_dir, f"{date}.json")
                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        summary = data.get('summary', {})
                        recent_activity.append({
                            'date': date,
                            'total_seats': summary.get('total_seats', 0),
                            'famous_seats_count': summary.get('famous_seats_count', 0),
                            'market_sentiment': summary.get('market_sentiment', '未知')
                        })

                    except Exception as e:
                        print(f"⚠️ 读取{date}数据失败: {e}")
                        continue

            if not recent_activity:
                print("⚠️ 无最近游资活跃数据")
                return {}

            # 计算趋势
            avg_famous_seats = sum(day['famous_seats_count'] for day in recent_activity) / len(recent_activity)

            # 情绪分布
            sentiment_count = {}
            for day in recent_activity:
                sentiment = day['market_sentiment']
                sentiment_count[sentiment] = sentiment_count.get(sentiment, 0) + 1

            print(f"✅ 最近{days}天游资活跃分析完成")

            return {
                'period': f'最近{days}天',
                'total_days': len(recent_activity),
                'avg_famous_seats_per_day': avg_famous_seats,
                'sentiment_distribution': sentiment_count,
                'daily_activity': recent_activity,
                'trend_analysis': self._analyze_activity_trend(recent_activity)
            }

        except Exception as e:
            print(f"❌ 获取最近游资活跃情况失败: {e}")
            return {}

    def _analyze_activity_trend(self, daily_activity):
        """分析活跃度趋势"""
        try:
            if len(daily_activity) < 5:
                return '数据不足'

            # 计算前半段和后半段的平均活跃度
            mid_point = len(daily_activity) // 2
            first_half = daily_activity[:mid_point]
            second_half = daily_activity[mid_point:]

            first_avg = sum(day['famous_seats_count'] for day in first_half) / len(first_half)
            second_avg = sum(day['famous_seats_count'] for day in second_half) / len(second_half)

            change_rate = (second_avg - first_avg) / first_avg * 100 if first_avg > 0 else 0

            if change_rate > 20:
                return '活跃度上升'
            elif change_rate < -20:
                return '活跃度下降'
            else:
                return '活跃度稳定'

        except Exception as e:
            return '分析失败'
