#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
龙虎榜量化分析算法
实现报告中要求的游资活跃度评分
"""

import pandas as pd
import numpy as np
import json
import os
from datetime import datetime, timedelta
from collections import defaultdict

class LHBQuantitativeAnalyzer:
    """龙虎榜量化分析器"""
    
    def __init__(self, youzi_seats_manager):
        self.youzi_manager = youzi_seats_manager
        self.data_dir = youzi_seats_manager.data_dir
        
        # 知名游资席位库（扩展版）
        self.hot_broker_keywords = [
            # 顶级游资
            '华鑫证券有限责任公司上海分公司',
            '华鑫证券有限责任公司宁波分公司',
            '中信证券股份有限公司上海溧阳路证券营业部',
            '中信证券股份有限公司北京远大路证券营业部',
            '华泰证券股份有限公司上海武定路证券营业部',
            '华泰证券股份有限公司深圳益田路证券营业部',
            '中国银河证券股份有限公司绍兴证券营业部',
            
            # 新生代游资
            '华泰证券台州中心大道',
            '光大证券股份有限公司佛山绿景路证券营业部',
            '国泰君安证券股份有限公司深圳益田路证券营业部',
            '申万宏源证券有限公司上海东川路证券营业部',
            '海通证券股份有限公司大连黄河路证券营业部',
            
            # 关键词匹配
            '华鑫', '溧阳路', '武定路', '绍兴', '佛山绿景路', '台州中心大道'
        ]
        
        # 机构席位关键词
        self.institution_keywords = [
            '基金', '保险', '社保', '养老金', 'QFII', '券商自营', 
            '信托', '银行理财', '专户', '资管计划'
        ]
    
    def analyze_hot_money_score(self, stock_code, analysis_days=250, progress_callback=None):
        """计算游资活跃度评分"""
        try:
            if progress_callback:
                progress_callback(f"分析 {stock_code} 龙虎榜游资活跃度...")
            
            # 1. 获取龙虎榜记录
            dragon_list = self._get_dragon_list_records(stock_code, analysis_days)
            
            if not dragon_list:
                return {
                    'hot_money_score': 0,
                    'qualified': False,
                    'reason': '无龙虎榜记录',
                    'analysis_details': {}
                }
            
            # 2. 计算关键指标
            repeat_seat_count = self._calculate_repeat_seat_count(dragon_list)
            buy_concentration = self._calculate_buy_concentration(dragon_list)
            institution_avoidance = self._calculate_institution_avoidance(dragon_list)
            
            # 3. 计算综合评分
            score = (repeat_seat_count * 0.4 + 
                    buy_concentration * 0.3 + 
                    institution_avoidance * 0.3)
            
            # 4. 判断是否符合要求（评分≥0.7）
            qualified = score >= 0.7
            
            analysis_details = {
                'total_records': len(dragon_list),
                'repeat_seat_score': repeat_seat_count,
                'buy_concentration_score': buy_concentration,
                'institution_avoidance_score': institution_avoidance,
                'hot_broker_appearances': self._get_hot_broker_details(dragon_list),
                'institution_sell_ratio': self._get_institution_sell_ratio(dragon_list)
            }
            
            return {
                'hot_money_score': score,
                'qualified': qualified,
                'reason': f'龙虎榜评分: {score:.3f} ({"符合" if qualified else "不符合"}要求≥0.7)',
                'analysis_details': analysis_details
            }
            
        except Exception as e:
            print(f"❌ 分析 {stock_code} 龙虎榜游资活跃度失败: {e}")
            return {
                'hot_money_score': 0,
                'qualified': False,
                'reason': f'分析失败: {e}',
                'analysis_details': {}
            }
    
    def _get_dragon_list_records(self, stock_code, analysis_days):
        """获取龙虎榜记录"""
        try:
            dragon_records = []
            
            # 获取可用的数据日期
            available_dates = self._get_available_dates()
            
            # 限制分析天数
            analysis_dates = available_dates[:analysis_days]
            
            for date in analysis_dates:
                try:
                    file_path = os.path.join(self.data_dir, f"{date}.json")
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    processed_data = data.get('processed_data', {})
                    seats_detail = processed_data.get('seats_detail', [])
                    
                    # 查找涉及该股票的席位记录
                    for seat in seats_detail:
                        buy_stocks = seat.get('buy_stocks', '')
                        sell_stocks = seat.get('sell_stocks', '')
                        
                        if stock_code in buy_stocks or stock_code in sell_stocks:
                            # 构造龙虎榜记录
                            record = {
                                'date': date,
                                'seat_name': seat['seat_name'],
                                'seat_type': seat['seat_type'],
                                'buy_amount': seat.get('buy_amount', 0),
                                'sell_amount': seat.get('sell_amount', 0),
                                'net_amount': seat.get('net_amount', 0),
                                'buy_count': seat.get('buy_count', 0),
                                'sell_count': seat.get('sell_count', 0),
                                'is_buy_side': stock_code in buy_stocks,
                                'is_sell_side': stock_code in sell_stocks
                            }
                            dragon_records.append(record)
                
                except Exception as e:
                    continue
            
            return dragon_records
            
        except Exception as e:
            print(f"❌ 获取 {stock_code} 龙虎榜记录失败: {e}")
            return []
    
    def _calculate_repeat_seat_count(self, dragon_list):
        """计算重复席位次数评分"""
        try:
            if not dragon_list:
                return 0
            
            # 统计知名游资席位出现次数
            hot_broker_count = 0
            seat_appearances = defaultdict(int)
            
            for record in dragon_list:
                seat_name = record['seat_name']
                
                # 检查是否为知名游资席位
                is_hot_broker = any(keyword in seat_name for keyword in self.hot_broker_keywords)
                
                if is_hot_broker:
                    hot_broker_count += 1
                    seat_appearances[seat_name] += 1
            
            # 计算重复出现的席位数量
            repeat_seats = sum(1 for count in seat_appearances.values() if count > 1)
            
            # 标准化评分（0-1）
            total_records = len(dragon_list)
            if total_records == 0:
                return 0
            
            # 重复席位比例 + 知名游资比例
            repeat_ratio = repeat_seats / len(seat_appearances) if seat_appearances else 0
            hot_broker_ratio = hot_broker_count / total_records
            
            # 综合评分
            score = (repeat_ratio * 0.6 + hot_broker_ratio * 0.4)
            
            return min(1.0, score)
            
        except Exception as e:
            return 0
    
    def _calculate_buy_concentration(self, dragon_list):
        """计算买入金额集中度评分"""
        try:
            if not dragon_list:
                return 0
            
            # 只分析买入记录
            buy_records = [r for r in dragon_list if r['is_buy_side'] and r['buy_amount'] > 0]
            
            if len(buy_records) < 2:  # 至少需要2条买入记录
                return 0
            
            # 按买入金额排序
            buy_records.sort(key=lambda x: x['buy_amount'], reverse=True)
            
            # 计算前3名买入金额占比（更宽松的条件）
            total_buy_amount = sum(r['buy_amount'] for r in buy_records)
            top3_buy_amount = sum(r['buy_amount'] for r in buy_records[:min(3, len(buy_records))])
            
            if total_buy_amount == 0:
                return 0
            
            buy_top3_ratio = top3_buy_amount / total_buy_amount

            # 集中度评分：比例越高分数越高
            # 0.3以上为高集中度，给满分（更宽松的条件）
            score = min(1.0, buy_top3_ratio / 0.3)
            
            return score
            
        except Exception as e:
            return 0
    
    def _calculate_institution_avoidance(self, dragon_list):
        """计算机构回避评分"""
        try:
            if not dragon_list:
                return 0
            
            # 分析卖出方是否有机构
            sell_records = [r for r in dragon_list if r['is_sell_side']]
            
            if not sell_records:
                return 1.0  # 无卖出记录，认为机构完全回避
            
            # 检查卖出席位中机构比例
            institution_sell_count = 0
            
            for record in sell_records:
                seat_name = record['seat_name']
                is_institution = any(keyword in seat_name for keyword in self.institution_keywords)
                
                if is_institution:
                    institution_sell_count += 1
            
            # 机构回避评分：机构卖出比例越低分数越高
            institution_ratio = institution_sell_count / len(sell_records) if sell_records else 0
            avoidance_score = 1.0 - institution_ratio
            
            return avoidance_score
            
        except Exception as e:
            return 0
    
    def _get_hot_broker_details(self, dragon_list):
        """获取知名游资详情"""
        try:
            hot_broker_details = []
            
            for record in dragon_list:
                seat_name = record['seat_name']
                is_hot_broker = any(keyword in seat_name for keyword in self.hot_broker_keywords)
                
                if is_hot_broker:
                    hot_broker_details.append({
                        'date': record['date'],
                        'seat_name': seat_name,
                        'seat_type': record['seat_type'],
                        'buy_amount': record['buy_amount'],
                        'sell_amount': record['sell_amount'],
                        'net_amount': record['net_amount'],
                        'side': 'buy' if record['is_buy_side'] else 'sell'
                    })
            
            return hot_broker_details
            
        except Exception as e:
            return []
    
    def _get_institution_sell_ratio(self, dragon_list):
        """获取机构卖出比例"""
        try:
            sell_records = [r for r in dragon_list if r['is_sell_side']]
            
            if not sell_records:
                return 0
            
            institution_count = 0
            for record in sell_records:
                seat_name = record['seat_name']
                is_institution = any(keyword in seat_name for keyword in self.institution_keywords)
                if is_institution:
                    institution_count += 1
            
            return institution_count / len(sell_records) if sell_records else 0
            
        except Exception as e:
            return 0
    
    def _get_available_dates(self):
        """获取可用的数据日期"""
        dates = []
        if os.path.exists(self.data_dir):
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.json') and len(filename) == 13:
                    date_str = filename[:8]
                    if date_str.isdigit():
                        dates.append(date_str)
        return sorted(dates, reverse=True)  # 按日期倒序
    
    def batch_analyze_stocks(self, stock_codes, progress_callback=None):
        """批量分析股票的龙虎榜评分"""
        try:
            results = []
            total_stocks = len(stock_codes)
            
            for i, stock_code in enumerate(stock_codes):
                try:
                    if progress_callback:
                        progress = (i + 1) / total_stocks * 100
                        progress_callback(progress, f"龙虎榜分析: {stock_code}")
                    
                    analysis = self.analyze_hot_money_score(stock_code)
                    
                    result = {
                        'code': stock_code,
                        'lhb_analysis': analysis
                    }
                    
                    results.append(result)
                    
                    print(f"✅ {stock_code}: 龙虎榜评分 {analysis['hot_money_score']:.3f} ({'符合' if analysis['qualified'] else '不符合'})")
                    
                except Exception as e:
                    print(f"❌ 分析 {stock_code} 失败: {e}")
                    continue
            
            # 筛选符合要求的股票
            qualified_stocks = [r for r in results if r['lhb_analysis']['qualified']]
            
            print(f"📊 龙虎榜量化分析完成:")
            print(f"   分析股票: {len(results)} 只")
            print(f"   符合要求: {len(qualified_stocks)} 只 (评分≥0.7)")
            print(f"   通过率: {len(qualified_stocks)/len(results)*100:.1f}%" if results else "   通过率: 0.0%")
            
            return qualified_stocks
            
        except Exception as e:
            print(f"❌ 批量龙虎榜分析失败: {e}")
            return []

def test_lhb_quantitative_analyzer():
    """测试龙虎榜量化分析器"""
    print("🧪 测试龙虎榜量化分析器")
    
    try:
        from youzi_seats_manager import YouziSeatsManager
        
        # 初始化管理器
        youzi_manager = YouziSeatsManager()
        analyzer = LHBQuantitativeAnalyzer(youzi_manager)
        
        # 测试股票
        test_stocks = ['000001', '000002', '600036', '000858', '002415']
        
        print(f"📊 批量分析 {len(test_stocks)} 只股票的龙虎榜评分:")
        
        qualified_stocks = analyzer.batch_analyze_stocks(test_stocks)
        
        if qualified_stocks:
            print(f"\n🏆 符合龙虎榜要求的股票:")
            for stock in qualified_stocks:
                analysis = stock['lhb_analysis']
                details = analysis['analysis_details']
                print(f"   {stock['code']}: 评分 {analysis['hot_money_score']:.3f}")
                print(f"     龙虎榜记录: {details['total_records']} 条")
                print(f"     知名游资出现: {len(details['hot_broker_appearances'])} 次")
                print(f"     机构卖出比例: {details['institution_sell_ratio']:.2f}")
        else:
            print("⚠️ 无股票符合龙虎榜要求")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_lhb_quantitative_analyzer()
