#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试特征不匹配问题
"""

import joblib
import numpy as np

def check_feature_files():
    """检查特征文件"""
    print("🔍 检查特征文件...")
    
    try:
        # 加载特征名称
        feature_names = joblib.load('trained_models/feature_names.joblib')
        print(f"📊 特征名称文件:")
        print(f"  特征数量: {len(feature_names)}")
        print(f"  前10个特征: {feature_names[:10]}")
        print(f"  后10个特征: {feature_names[-10:]}")
        
        # 加载标准化器
        scaler = joblib.load('trained_models/scaler.joblib')
        print(f"\n📊 标准化器信息:")
        print(f"  期望特征数量: {scaler.n_features_in_}")
        print(f"  特征均值形状: {scaler.mean_.shape}")
        print(f"  特征方差形状: {scaler.scale_.shape}")
        
        # 检查不匹配
        if len(feature_names) != scaler.n_features_in_:
            print(f"\n❌ 特征不匹配!")
            print(f"  特征名称数量: {len(feature_names)}")
            print(f"  标准化器期望: {scaler.n_features_in_}")
            
            # 分析可能的原因
            print(f"\n🔍 可能的原因:")
            print(f"  1. 训练时使用了不同的特征集")
            print(f"  2. 特征名称文件和标准化器来自不同的训练")
            print(f"  3. 特征工程过程中特征数量发生了变化")
            
            return False
        else:
            print(f"\n✅ 特征数量匹配")
            return True
            
    except Exception as e:
        print(f"❌ 检查特征文件失败: {e}")
        return False

def check_training_results():
    """检查训练结果中的特征信息"""
    print("\n🔍 检查训练结果中的特征信息...")
    
    try:
        import json
        
        # 查找最新的训练结果文件
        import os
        results_dir = "ui_integration/training_results"
        if os.path.exists(results_dir):
            result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
            if result_files:
                latest_file = sorted(result_files)[-1]
                result_path = os.path.join(results_dir, latest_file)
                
                with open(result_path, 'r', encoding='utf-8') as f:
                    results = json.load(f)
                
                print(f"📊 训练结果文件: {latest_file}")
                
                # 检查数据准备阶段的特征信息
                data_prep = results.get('data_preparation', {})
                if 'feature_count' in data_prep:
                    print(f"  训练时特征数量: {data_prep['feature_count']}")
                
                if 'feature_names' in data_prep:
                    feature_names = data_prep['feature_names']
                    print(f"  训练时特征名称数量: {len(feature_names)}")
                    print(f"  训练时前10个特征: {feature_names[:10]}")
                
                return True
            else:
                print("❌ 没有找到训练结果文件")
                return False
        else:
            print("❌ 训练结果目录不存在")
            return False
            
    except Exception as e:
        print(f"❌ 检查训练结果失败: {e}")
        return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 解决方案建议:")
    print("1. 重新训练模型，确保特征一致性")
    print("2. 修复特征工程，使预测时特征与训练时一致")
    print("3. 检查特征名称文件是否正确")
    print("4. 验证标准化器是否与当前特征匹配")

def main():
    """主函数"""
    print("🔧 特征不匹配问题调试...")
    print("="*60)
    
    # 检查特征文件
    feature_ok = check_feature_files()
    
    # 检查训练结果
    training_ok = check_training_results()
    
    print("\n" + "="*60)
    print("📋 调试结论:")
    
    if not feature_ok:
        print("❌ 特征文件不匹配，这是模型预测失败的主要原因")
        suggest_solutions()
    elif feature_ok and training_ok:
        print("✅ 特征文件正常，问题可能在其他地方")
    else:
        print("⚠️ 需要进一步调查")
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
