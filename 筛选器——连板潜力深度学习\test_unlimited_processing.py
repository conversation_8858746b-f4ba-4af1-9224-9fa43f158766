#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试无限制处理配置
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_unlimited_config():
    """测试无限制处理配置"""
    print("🔍 测试无限制处理配置...")
    
    try:
        from investigation.potential_stock_investigator import PotentialStockInvestigator
        
        # 创建调查器
        investigator = PotentialStockInvestigator()
        
        print(f"📊 调查器配置:")
        print(f"  最小涨停次数: {investigator.config['min_limit_up_count']}")
        print(f"  市值范围: {investigator.config['market_cap_min']}-{investigator.config['market_cap_max']}亿")
        print(f"  最大处理股票数: {investigator.config['max_stocks_to_process']}")
        
        # 验证配置
        if investigator.config['max_stocks_to_process'] is None:
            print("✅ 已配置为无限制处理所有候选股票")
            return True
        else:
            print(f"❌ 仍有处理限制: {investigator.config['max_stocks_to_process']}只")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_feature_extraction_logic():
    """测试特征提取逻辑"""
    print("\n🔍 测试特征提取逻辑...")
    
    try:
        from investigation.potential_stock_investigator import PotentialStockInvestigator
        
        # 创建调查器
        investigator = PotentialStockInvestigator()
        
        # 创建模拟股票数据
        mock_stocks = []
        for i in range(10):  # 创建10只模拟股票
            stock = {
                '标准化代码': f'00000{i}',
                '名称': f'测试股票{i}',
                '市值': 50 + i * 10,
                '最新价': 10 + i,
                '涨跌幅': i - 5,
                '涨停次数': 6 + i
            }
            mock_stocks.append(stock)
        
        print(f"📊 模拟股票数据: {len(mock_stocks)}只")
        
        # 测试特征提取逻辑（不实际调用API）
        print("🧪 测试特征提取逻辑...")
        
        # 检查处理数量逻辑
        if investigator.config['max_stocks_to_process'] is not None:
            stocks_to_process = mock_stocks[:investigator.config['max_stocks_to_process']]
            print(f"  限制处理: {len(stocks_to_process)}/{len(mock_stocks)}只")
        else:
            stocks_to_process = mock_stocks
            print(f"  处理所有: {len(stocks_to_process)}只")
        
        if len(stocks_to_process) == len(mock_stocks):
            print("✅ 特征提取逻辑支持处理所有股票")
            return True
        else:
            print("❌ 特征提取逻辑仍有限制")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def estimate_processing_time():
    """估算处理时间"""
    print("\n📊 估算处理时间...")
    
    try:
        from investigation.limit_up_analyzer import LimitUpAnalyzer
        
        analyzer = LimitUpAnalyzer()
        
        # 获取统计数据
        stats = analyzer.load_annual_limit_up_stats(days_back=365)
        
        # 估算候选股票数量
        limit_up_6_stocks = analyzer.get_stocks_with_min_limit_ups(min_count=6)
        limit_up_6_count = len(limit_up_6_stocks)
        
        # 估算基础筛选后的数量
        basic_filtered = 1658  # 从之前的日志获取
        estimated_candidates = int(basic_filtered * limit_up_6_count / 4000)  # 估算
        
        print(f"📈 处理时间估算:")
        print(f"  预计候选股票: {estimated_candidates}只")
        print(f"  每只股票处理时间: 约1-2秒")
        print(f"  总处理时间: 约{estimated_candidates * 1.5 / 60:.1f}分钟")
        print(f"  API调用次数: 约{estimated_candidates * 2}次")
        
        # 风险提示
        if estimated_candidates > 300:
            print(f"\n⚠️ 风险提示:")
            print(f"  处理{estimated_candidates}只股票可能需要较长时间")
            print(f"  建议在网络稳定的环境下运行")
            print(f"  如遇到API限流，程序会自动重试")
        else:
            print(f"\n✅ 处理数量合理，预计能在合理时间内完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 估算失败: {e}")
        return False

def show_comparison():
    """显示配置对比"""
    print("\n📊 配置对比:")
    print("="*50)
    
    print("修改前:")
    print("  max_stocks_to_process: 500")
    print("  处理范围: 前500只候选股票")
    print("  优点: 处理时间可控")
    print("  缺点: 可能遗漏优质股票")
    
    print("\n修改后:")
    print("  max_stocks_to_process: None")
    print("  处理范围: 所有候选股票")
    print("  优点: 全面分析，不遗漏")
    print("  缺点: 处理时间较长")
    
    print("\n🎯 预期效果:")
    print("  更全面的股票分析")
    print("  更准确的潜在股发现")
    print("  更高的推荐质量")

def main():
    """主函数"""
    print("🔧 测试无限制处理配置...")
    print("="*60)
    
    # 测试配置
    config_ok = test_unlimited_config()
    
    # 测试逻辑
    logic_ok = test_feature_extraction_logic()
    
    # 估算处理时间
    estimate_ok = estimate_processing_time()
    
    # 显示对比
    show_comparison()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if config_ok and logic_ok and estimate_ok:
        print("✅ 无限制处理配置成功")
        print("✅ 特征提取逻辑支持处理所有候选股票")
        print("✅ 处理时间估算合理")
        print("\n💡 建议：现在可以运行完整的潜在股调查")
        print("⚠️ 注意：处理时间可能较长，请耐心等待")
    else:
        print("❌ 配置或逻辑存在问题")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
