#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游资行为特征工程
扩展游资行为分析，包括游资席位连续性、操作风格、历史成功率等
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import os
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class YouzieBehaviorFeatureEngine:
    """游资行为特征工程器"""
    
    def __init__(self, youzi_data_path: str = "../youzi_seats_data"):
        self.youzi_data_path = youzi_data_path
        self.data_cache = {}  # 数据缓存
        self.cache_timeout = 3600  # 缓存1小时
        
        # 知名游资席位配置
        self.famous_youzi_seats = {
            'top_tier': {
                '中信证券股份有限公司上海溧阳路证券营业部': {'tier': 'S', 'style': 'aggressive', 'success_rate': 0.75},
                '华泰证券股份有限公司南京广州路证券营业部': {'tier': 'S', 'style': 'momentum', 'success_rate': 0.72},
                '中信证券股份有限公司杭州延安路证券营业部': {'tier': 'S', 'style': 'technical', 'success_rate': 0.70},
                '国泰君安证券股份有限公司上海江苏路证券营业部': {'tier': 'A', 'style': 'value', 'success_rate': 0.68},
                '招商证券股份有限公司深圳蛇口工业七路证券营业部': {'tier': 'A', 'style': 'growth', 'success_rate': 0.65}
            },
            'second_tier': {
                '东方财富证券股份有限公司拉萨团结路第二证券营业部': {'tier': 'B', 'style': 'speculative', 'success_rate': 0.60},
                '方正证券股份有限公司杭州延安路证券营业部': {'tier': 'B', 'style': 'momentum', 'success_rate': 0.58},
                '光大证券股份有限公司宁波解放南路证券营业部': {'tier': 'B', 'style': 'technical', 'success_rate': 0.55}
            }
        }
        
        # 游资操作风格特征
        self.operation_styles = {
            'aggressive': {'hold_days': 1.5, 'profit_target': 0.15, 'risk_tolerance': 0.08},
            'momentum': {'hold_days': 3.0, 'profit_target': 0.25, 'risk_tolerance': 0.12},
            'technical': {'hold_days': 5.0, 'profit_target': 0.30, 'risk_tolerance': 0.10},
            'value': {'hold_days': 10.0, 'profit_target': 0.40, 'risk_tolerance': 0.15},
            'growth': {'hold_days': 7.0, 'profit_target': 0.35, 'risk_tolerance': 0.12},
            'speculative': {'hold_days': 2.0, 'profit_target': 0.20, 'risk_tolerance': 0.15}
        }
        
        # 游资行为评分权重
        self.behavior_weights = {
            'seat_quality': 0.3,           # 席位质量
            'operation_consistency': 0.25,  # 操作一致性
            'success_rate': 0.2,           # 历史成功率
            'capital_scale': 0.15,         # 资金规模
            'coordination_effect': 0.1     # 协同效应
        }
        
        logger.info("🎯 游资行为特征工程器初始化完成")
    
    def extract_youzi_behavior_features(self, stock_code: str, stock_name: str = None, analysis_date: datetime = None) -> Dict[str, Any]:
        """提取游资行为特征"""
        try:
            if analysis_date is None:
                analysis_date = datetime.now()
            
            logger.info(f"🎯 开始提取{stock_code}的游资行为特征...")
            
            features = {}
            
            # 1. 获取龙虎榜数据
            lhb_data = self._get_lhb_data(stock_code, stock_name, analysis_date)
            
            if lhb_data.get('data_available', False):
                # 2. 席位质量分析特征
                seat_features = self._extract_seat_quality_features(lhb_data['lhb_records'])
                features.update(seat_features)
                
                # 3. 游资操作行为特征
                behavior_features = self._extract_operation_behavior_features(lhb_data['lhb_records'])
                features.update(behavior_features)
                
                # 4. 游资连续性特征
                continuity_features = self._extract_continuity_features(lhb_data['lhb_records'])
                features.update(continuity_features)
                
                # 5. 游资协同效应特征
                coordination_features = self._extract_coordination_features(lhb_data['lhb_records'])
                features.update(coordination_features)
                
                # 6. 历史成功率特征
                success_features = self._extract_success_rate_features(lhb_data['lhb_records'], stock_code)
                features.update(success_features)
                
                # 7. 综合游资行为评分
                comprehensive_score = self._calculate_comprehensive_youzi_score(features)
                features.update(comprehensive_score)
                
                features['youzi_behavior_analysis_success'] = True
            else:
                features = self._get_empty_youzi_features()
            
            features['total_youzi_features'] = len([k for k in features.keys() if not k.startswith('youzi_behavior_')])
            
            logger.info(f"✅ {stock_code}游资行为特征提取完成，共{features['total_youzi_features']}个特征")
            return features
            
        except Exception as e:
            logger.error(f"游资行为特征提取失败: {e}")
            return self._get_empty_youzi_features()
    
    def _get_lhb_data(self, stock_code: str, stock_name: str, analysis_date: datetime) -> Dict[str, Any]:
        """获取龙虎榜数据"""
        try:
            # 查找最近3个月的龙虎榜数据
            start_date = analysis_date - timedelta(days=90)
            
            lhb_records = []
            
            # 遍历日期范围查找龙虎榜文件
            current_date = start_date
            while current_date <= analysis_date:
                date_str = current_date.strftime('%Y%m%d')

                # 尝试JSON格式文件
                json_file = os.path.join(self.youzi_data_path, f"{date_str}.json")
                csv_file = os.path.join(self.youzi_data_path, f"lhb_{date_str}.csv")

                if os.path.exists(json_file):
                    try:
                        import json
                        with open(json_file, 'r', encoding='utf-8') as f:
                            daily_data = json.load(f)

                        # 处理JSON数据结构
                        if isinstance(daily_data, dict) and 'processed_data' in daily_data:
                            seats_detail = daily_data['processed_data'].get('seats_detail', [])

                            for seat_record in seats_detail:
                                seat_name = seat_record.get('seat_name', '')
                                buy_stocks = seat_record.get('buy_stocks', '')
                                sell_stocks = seat_record.get('sell_stocks', '')

                                # 检查股票代码或股票名称是否在买入或卖出股票列表中
                                stock_match = (stock_code in buy_stocks or stock_code in sell_stocks or
                                             (stock_name and (stock_name in buy_stocks or stock_name in sell_stocks)))

                                if stock_match:
                                    # 估算该股票的买卖金额（简化处理）
                                    total_buy_amount = float(seat_record.get('buy_amount', 0))
                                    total_sell_amount = float(seat_record.get('sell_amount', 0))
                                    buy_count = int(seat_record.get('buy_count', 1))
                                    sell_count = int(seat_record.get('sell_count', 1))

                                    # 简化：平均分配到每只股票
                                    buy_match = stock_code in buy_stocks or (stock_name and stock_name in buy_stocks)
                                    sell_match = stock_code in sell_stocks or (stock_name and stock_name in sell_stocks)

                                    estimated_buy = total_buy_amount / max(buy_count, 1) if buy_match else 0
                                    estimated_sell = total_sell_amount / max(sell_count, 1) if sell_match else 0

                                    lhb_records.append({
                                        'date': date_str,
                                        'stock_code': stock_code,
                                        'seat_name': seat_name,
                                        'buy_amount': estimated_buy,
                                        'sell_amount': estimated_sell,
                                        'net_amount': estimated_buy - estimated_sell,
                                        'reason': '龙虎榜',
                                        'rank': 0,
                                        'is_famous': seat_record.get('is_famous', False),
                                        'seat_type': seat_record.get('seat_type', '普通营业部')
                                    })
                        elif isinstance(daily_data, list):
                            for record in daily_data:
                                if record.get('股票代码') == stock_code or record.get('代码') == stock_code:
                                    lhb_records.append({
                                        'date': date_str,
                                        'stock_code': stock_code,
                                        'seat_name': record.get('营业部名称', record.get('席位名称', '')),
                                        'buy_amount': float(record.get('买入金额', record.get('买入额', 0))),
                                        'sell_amount': float(record.get('卖出金额', record.get('卖出额', 0))),
                                        'net_amount': float(record.get('买入金额', record.get('买入额', 0))) - float(record.get('卖出金额', record.get('卖出额', 0))),
                                        'reason': record.get('上榜原因', record.get('原因', '')),
                                        'rank': int(record.get('排名', record.get('序号', 0)))
                                    })
                    except Exception as e:
                        logger.debug(f"读取{json_file}失败: {e}")

                elif os.path.exists(csv_file):
                    try:
                        daily_lhb = pd.read_csv(csv_file, encoding='utf-8')

                        # 筛选目标股票
                        if '代码' in daily_lhb.columns:
                            stock_lhb = daily_lhb[daily_lhb['代码'] == stock_code]

                            for _, row in stock_lhb.iterrows():
                                lhb_records.append({
                                    'date': date_str,
                                    'stock_code': stock_code,
                                    'seat_name': row.get('营业部名称', ''),
                                    'buy_amount': float(row.get('买入金额', 0)),
                                    'sell_amount': float(row.get('卖出金额', 0)),
                                    'net_amount': float(row.get('买入金额', 0)) - float(row.get('卖出金额', 0)),
                                    'reason': row.get('上榜原因', ''),
                                    'rank': int(row.get('排名', 0))
                                })
                    except Exception as e:
                        logger.debug(f"读取{csv_file}失败: {e}")

                current_date += timedelta(days=1)
            
            if lhb_records:
                return {
                    'data_available': True,
                    'lhb_records': lhb_records,
                    'record_count': len(lhb_records),
                    'date_range': f"{start_date.strftime('%Y%m%d')} - {analysis_date.strftime('%Y%m%d')}"
                }
            else:
                return {'data_available': False}
                
        except Exception as e:
            logger.error(f"获取龙虎榜数据失败: {e}")
            return {'data_available': False}
    
    def _extract_seat_quality_features(self, lhb_records: List[Dict]) -> Dict[str, float]:
        """提取席位质量特征"""
        try:
            features = {}
            
            if not lhb_records:
                return features
            
            # 统计席位信息
            seat_stats = {}
            total_buy_amount = 0
            total_sell_amount = 0
            
            for record in lhb_records:
                seat_name = record['seat_name']
                buy_amount = record['buy_amount']
                sell_amount = record['sell_amount']
                
                if seat_name not in seat_stats:
                    seat_stats[seat_name] = {
                        'appearances': 0,
                        'total_buy': 0,
                        'total_sell': 0,
                        'net_amount': 0,
                        'tier': 'C',  # 默认等级
                        'style': 'unknown'
                    }
                
                seat_stats[seat_name]['appearances'] += 1
                seat_stats[seat_name]['total_buy'] += buy_amount
                seat_stats[seat_name]['total_sell'] += sell_amount
                seat_stats[seat_name]['net_amount'] += (buy_amount - sell_amount)
                
                total_buy_amount += buy_amount
                total_sell_amount += sell_amount
                
                # 识别知名席位
                for tier_name, seats in self.famous_youzi_seats.items():
                    if seat_name in seats:
                        seat_stats[seat_name]['tier'] = seats[seat_name]['tier']
                        seat_stats[seat_name]['style'] = seats[seat_name]['style']
                        break
            
            # 计算席位质量特征
            famous_seats = [name for name, stats in seat_stats.items() if stats['tier'] in ['S', 'A', 'B']]
            top_tier_seats = [name for name, stats in seat_stats.items() if stats['tier'] == 'S']
            
            features.update({
                'total_unique_seats': len(seat_stats),
                'famous_seats_count': len(famous_seats),
                'top_tier_seats_count': len(top_tier_seats),
                'famous_seats_ratio': len(famous_seats) / len(seat_stats) if seat_stats else 0,
                'has_top_tier_youzi': 1.0 if top_tier_seats else 0.0
            })
            
            # 计算知名席位资金占比
            if total_buy_amount > 0:
                famous_buy_amount = sum(seat_stats[name]['total_buy'] for name in famous_seats)
                features['famous_seats_buy_ratio'] = famous_buy_amount / total_buy_amount
            else:
                features['famous_seats_buy_ratio'] = 0.0
            
            # 席位集中度
            if seat_stats:
                seat_amounts = [stats['total_buy'] + stats['total_sell'] for stats in seat_stats.values()]
                max_amount = max(seat_amounts)
                total_amount = sum(seat_amounts)
                
                features['seat_concentration'] = max_amount / total_amount if total_amount > 0 else 0
                features['high_seat_concentration'] = 1.0 if features['seat_concentration'] > 0.3 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"席位质量特征提取失败: {e}")
            return {}
    
    def _extract_operation_behavior_features(self, lhb_records: List[Dict]) -> Dict[str, float]:
        """提取操作行为特征"""
        try:
            features = {}
            
            if not lhb_records:
                return features
            
            # 按日期分组分析
            date_groups = {}
            for record in lhb_records:
                date = record['date']
                if date not in date_groups:
                    date_groups[date] = []
                date_groups[date].append(record)
            
            # 分析买卖行为
            buy_dominant_days = 0
            sell_dominant_days = 0
            balanced_days = 0
            
            for date, records in date_groups.items():
                total_buy = sum(r['buy_amount'] for r in records)
                total_sell = sum(r['sell_amount'] for r in records)
                
                if total_buy > total_sell * 1.5:
                    buy_dominant_days += 1
                elif total_sell > total_buy * 1.5:
                    sell_dominant_days += 1
                else:
                    balanced_days += 1
            
            total_days = len(date_groups)
            
            if total_days > 0:
                features.update({
                    'buy_dominant_ratio': buy_dominant_days / total_days,
                    'sell_dominant_ratio': sell_dominant_days / total_days,
                    'balanced_operation_ratio': balanced_days / total_days,
                    'operation_consistency': max(buy_dominant_days, sell_dominant_days) / total_days
                })
            
            # 操作风格识别
            avg_net_amount = np.mean([r['net_amount'] for r in lhb_records])
            operation_frequency = len(lhb_records) / max(total_days, 1)
            
            # 简化的风格识别
            if operation_frequency > 2 and avg_net_amount > 0:
                style = 'aggressive'
            elif operation_frequency > 1 and avg_net_amount > 0:
                style = 'momentum'
            elif avg_net_amount > 0:
                style = 'technical'
            else:
                style = 'conservative'
            
            features['operation_style'] = style
            features['operation_frequency'] = operation_frequency
            features['avg_net_position'] = avg_net_amount / 1e6  # 转换为百万
            
            return features
            
        except Exception as e:
            logger.error(f"操作行为特征提取失败: {e}")
            return {}

    def _extract_continuity_features(self, lhb_records: List[Dict]) -> Dict[str, float]:
        """提取连续性特征"""
        try:
            features = {}

            if not lhb_records:
                return features

            # 按席位分组分析连续性
            seat_dates = {}
            for record in lhb_records:
                seat_name = record['seat_name']
                date = record['date']

                if seat_name not in seat_dates:
                    seat_dates[seat_name] = []
                seat_dates[seat_name].append(date)

            # 分析连续出现的席位
            continuous_seats = 0
            max_continuity = 0

            for seat_name, dates in seat_dates.items():
                if len(dates) >= 2:
                    continuous_seats += 1

                    # 计算最大连续天数
                    sorted_dates = sorted(dates)
                    current_continuity = 1
                    max_seat_continuity = 1

                    for i in range(1, len(sorted_dates)):
                        prev_date = datetime.strptime(sorted_dates[i-1], '%Y%m%d')
                        curr_date = datetime.strptime(sorted_dates[i], '%Y%m%d')

                        if (curr_date - prev_date).days <= 3:  # 3天内算连续
                            current_continuity += 1
                            max_seat_continuity = max(max_seat_continuity, current_continuity)
                        else:
                            current_continuity = 1

                    max_continuity = max(max_continuity, max_seat_continuity)

            total_seats = len(seat_dates)

            features.update({
                'continuous_seats_count': continuous_seats,
                'continuous_seats_ratio': continuous_seats / total_seats if total_seats > 0 else 0,
                'max_seat_continuity': max_continuity,
                'has_continuous_operation': 1.0 if continuous_seats > 0 else 0.0,
                'strong_continuity': 1.0 if max_continuity >= 3 else 0.0
            })

            return features

        except Exception as e:
            logger.error(f"连续性特征提取失败: {e}")
            return {}

    def _extract_coordination_features(self, lhb_records: List[Dict]) -> Dict[str, float]:
        """提取协同效应特征"""
        try:
            features = {}

            if not lhb_records:
                return features

            # 按日期分组分析协同效应
            date_groups = {}
            for record in lhb_records:
                date = record['date']
                if date not in date_groups:
                    date_groups[date] = []
                date_groups[date].append(record)

            # 分析多席位协同
            multi_seat_days = 0
            coordination_strength = 0

            for date, records in date_groups.items():
                if len(records) >= 2:
                    multi_seat_days += 1

                    # 计算协同强度（同向操作的比例）
                    buy_seats = sum(1 for r in records if r['net_amount'] > 0)
                    sell_seats = sum(1 for r in records if r['net_amount'] < 0)

                    same_direction_ratio = max(buy_seats, sell_seats) / len(records)
                    coordination_strength += same_direction_ratio

            total_days = len(date_groups)

            features.update({
                'multi_seat_days': multi_seat_days,
                'multi_seat_ratio': multi_seat_days / total_days if total_days > 0 else 0,
                'avg_coordination_strength': coordination_strength / max(multi_seat_days, 1),
                'strong_coordination': 1.0 if coordination_strength / max(multi_seat_days, 1) > 0.8 else 0.0
            })

            # 知名席位协同分析
            famous_coordination_days = 0
            for date, records in date_groups.items():
                famous_seats_in_day = 0
                for record in records:
                    seat_name = record['seat_name']
                    for tier_seats in self.famous_youzi_seats.values():
                        if seat_name in tier_seats:
                            famous_seats_in_day += 1
                            break

                if famous_seats_in_day >= 2:
                    famous_coordination_days += 1

            features.update({
                'famous_coordination_days': famous_coordination_days,
                'famous_coordination_ratio': famous_coordination_days / total_days if total_days > 0 else 0,
                'has_famous_coordination': 1.0 if famous_coordination_days > 0 else 0.0
            })

            return features

        except Exception as e:
            logger.error(f"协同效应特征提取失败: {e}")
            return {}

    def _extract_success_rate_features(self, lhb_records: List[Dict], stock_code: str) -> Dict[str, float]:
        """提取历史成功率特征"""
        try:
            features = {}

            if not lhb_records:
                return features

            # 统计席位历史成功率（简化实现）
            seat_success_rates = {}
            total_weighted_success_rate = 0
            total_weight = 0

            for record in lhb_records:
                seat_name = record['seat_name']

                # 查找知名席位的历史成功率
                success_rate = 0.5  # 默认成功率
                for tier_seats in self.famous_youzi_seats.values():
                    if seat_name in tier_seats:
                        success_rate = tier_seats[seat_name]['success_rate']
                        break

                # 根据操作金额加权
                weight = abs(record['net_amount'])
                total_weighted_success_rate += success_rate * weight
                total_weight += weight

                seat_success_rates[seat_name] = success_rate

            # 计算加权平均成功率
            if total_weight > 0:
                avg_success_rate = total_weighted_success_rate / total_weight
            else:
                avg_success_rate = 0.5

            features.update({
                'avg_seat_success_rate': avg_success_rate,
                'high_success_rate_seats': sum(1 for rate in seat_success_rates.values() if rate > 0.6),
                'has_high_success_seats': 1.0 if any(rate > 0.7 for rate in seat_success_rates.values()) else 0.0,
                'success_rate_confidence': avg_success_rate if avg_success_rate > 0.6 else 0.0
            })

            return features

        except Exception as e:
            logger.error(f"历史成功率特征提取失败: {e}")
            return {}

    def _calculate_comprehensive_youzi_score(self, features: Dict[str, float]) -> Dict[str, float]:
        """计算综合游资行为评分"""
        try:
            score_components = {}

            # 1. 席位质量评分
            famous_ratio = features.get('famous_seats_ratio', 0)
            top_tier_count = features.get('top_tier_seats_count', 0)
            seat_quality_score = famous_ratio * 0.7 + min(top_tier_count / 3, 1) * 0.3
            score_components['seat_quality_score'] = seat_quality_score

            # 2. 操作一致性评分
            consistency = features.get('operation_consistency', 0)
            score_components['operation_consistency_score'] = consistency

            # 3. 历史成功率评分
            success_rate = features.get('avg_seat_success_rate', 0.5)
            score_components['success_rate_score'] = (success_rate - 0.5) * 2  # 标准化到0-1

            # 4. 资金规模评分
            avg_net_position = abs(features.get('avg_net_position', 0))
            capital_scale_score = min(avg_net_position / 50, 1)  # 5000万为满分
            score_components['capital_scale_score'] = capital_scale_score

            # 5. 协同效应评分
            coordination = features.get('avg_coordination_strength', 0)
            score_components['coordination_effect_score'] = coordination

            # 计算加权综合评分
            comprehensive_score = 0.0
            for component, weight in self.behavior_weights.items():
                score_key = f"{component}_score"
                if score_key in score_components:
                    comprehensive_score += score_components[score_key] * weight

            return {
                'youzi_behavior_comprehensive_score': comprehensive_score,
                'youzi_behavior_bullish': 1.0 if comprehensive_score > 0.6 else 0.0,
                'youzi_behavior_strong': 1.0 if comprehensive_score > 0.8 else 0.0,
                **score_components
            }

        except Exception as e:
            logger.error(f"综合游资行为评分计算失败: {e}")
            return {'youzi_behavior_comprehensive_score': 0.0}

    def _get_empty_youzi_features(self) -> Dict[str, Any]:
        """获取空的游资行为特征"""
        return {
            'youzi_behavior_analysis_success': False,
            'total_youzi_features': 0,
            'youzi_behavior_comprehensive_score': 0.0,
            'error': '游资行为特征提取失败'
        }

def main():
    """测试游资行为特征工程器"""
    print("🎯 测试游资行为特征工程器...")

    # 检查游资数据目录
    youzi_data_path = "youzi_seats_data"
    if not os.path.exists(youzi_data_path):
        print(f"⚠️ 游资数据目录不存在: {youzi_data_path}")
        print("使用模拟数据进行测试...")

        # 创建测试目录和模拟数据
        os.makedirs(youzi_data_path, exist_ok=True)

        # 创建模拟龙虎榜数据
        test_date = datetime.now().strftime('%Y%m%d')
        test_lhb_file = os.path.join(youzi_data_path, f"lhb_{test_date}.csv")

        test_data = pd.DataFrame({
            '代码': ['000001', '000001', '000002'],
            '营业部名称': [
                '中信证券股份有限公司上海溧阳路证券营业部',
                '华泰证券股份有限公司南京广州路证券营业部',
                '东方财富证券股份有限公司拉萨团结路第二证券营业部'
            ],
            '买入金额': [50000000, 30000000, 20000000],
            '卖出金额': [0, 10000000, 25000000],
            '上榜原因': ['日涨幅偏离值达7%', '日涨幅偏离值达7%', '日跌幅偏离值达7%'],
            '排名': [1, 2, 1]
        })

        test_data.to_csv(test_lhb_file, index=False, encoding='utf-8')
        print(f"✅ 创建测试数据: {test_lhb_file}")

    engine = YouzieBehaviorFeatureEngine(youzi_data_path)

    # 测试特征提取
    print(f"\n🔍 测试游资行为特征提取...")
    features = engine.extract_youzi_behavior_features('000601', '韶能股份')  # 韶能股份

    print(f"✅ 特征提取完成")
    print(f"分析成功: {features.get('youzi_behavior_analysis_success', False)}")
    print(f"总特征数: {features.get('total_youzi_features', 0)}")

    # 显示关键特征
    key_features = ['famous_seats_count', 'top_tier_seats_count', 'operation_consistency',
                   'avg_seat_success_rate', 'youzi_behavior_comprehensive_score']

    print(f"\n🎯 关键游资行为特征:")
    for feature in key_features:
        if feature in features:
            print(f"  {feature}: {features[feature]:.3f}")

if __name__ == "__main__":
    main()
