#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试负样本创建修复
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_layer.efinance_data_fetcher import EfinanceDataFetcher
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_kline_query_with_date_range():
    """测试带日期范围的K线数据查询"""
    print("🧪 测试带日期范围的K线数据查询...")
    
    storage = HistoricalDataStorage()
    
    # 测试股票（我们刚刚存储了这些股票的数据）
    test_stock = "600191"
    
    # 模拟训练流程中的查询逻辑
    limit_up_date = "2025-07-04"  # 假设的涨停日期
    limit_up_datetime = datetime.strptime(limit_up_date, '%Y-%m-%d')
    start_date_for_query = (limit_up_datetime - timedelta(days=30)).strftime('%Y-%m-%d')
    end_date_for_query = limit_up_date
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 查询时间范围: {start_date_for_query} 到 {end_date_for_query}")
    
    try:
        # 1. 不带日期范围的查询（原来的方式）
        print("\n1️⃣ 不带日期范围的查询...")
        kline_data_no_date = storage.get_kline_data(test_stock)
        print(f"✅ 查询结果: {len(kline_data_no_date)}条记录")
        
        # 2. 带日期范围的查询（修复后的方式）
        print("\n2️⃣ 带日期范围的查询...")
        kline_data_with_date = storage.get_kline_data(
            test_stock,
            start_date=start_date_for_query,
            end_date=end_date_for_query
        )
        print(f"✅ 查询结果: {len(kline_data_with_date)}条记录")
        
        if not kline_data_with_date.empty:
            print(f"   日期范围: {kline_data_with_date['trade_date'].min()} 到 {kline_data_with_date['trade_date'].max()}")
            print(f"   样本数据:\n{kline_data_with_date.head(3)}")
            return True
        else:
            print("❌ 带日期范围的查询仍然为空")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_multiple_stocks_date_query():
    """测试多只股票的日期范围查询"""
    print("\n🧪 测试多只股票的日期范围查询...")
    
    storage = HistoricalDataStorage()
    
    # 测试多只股票
    test_stocks = ["600191", "002261", "002207", "002940"]
    
    results = {}
    
    for stock_code in test_stocks:
        print(f"\n📊 测试股票: {stock_code}")
        
        # 模拟不同的涨停日期
        limit_up_date = "2025-07-01"
        limit_up_datetime = datetime.strptime(limit_up_date, '%Y-%m-%d')
        start_date = (limit_up_datetime - timedelta(days=30)).strftime('%Y-%m-%d')
        
        try:
            kline_data = storage.get_kline_data(
                stock_code,
                start_date=start_date,
                end_date=limit_up_date
            )
            
            results[stock_code] = len(kline_data)
            print(f"✅ 查询成功: {len(kline_data)}条记录")
            
        except Exception as e:
            results[stock_code] = 0
            print(f"❌ 查询失败: {e}")
    
    print("\n📋 查询结果汇总:")
    for stock_code, count in results.items():
        print(f"   {stock_code}: {count}条记录")
    
    success_count = sum(1 for count in results.values() if count > 0)
    return success_count, len(results)

def check_current_database_status():
    """检查当前数据库状态"""
    print("\n🔍 检查当前数据库状态...")
    
    import sqlite3
    conn = sqlite3.connect('historical_data.db')
    
    try:
        # 检查K线数据
        count = conn.execute('SELECT COUNT(*) FROM stock_kline_data').fetchone()[0]
        print(f"📊 K线数据总数: {count}")
        
        # 检查特征数据
        feature_count = conn.execute('SELECT COUNT(*) FROM stock_features').fetchone()[0]
        print(f"📊 特征数据总数: {feature_count}")
        
        if feature_count > 0:
            # 检查标签分布
            label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features GROUP BY label', conn)
            print(f"📊 标签分布:\n{label_dist}")
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
    
    finally:
        conn.close()

def main():
    """主函数"""
    print("🔧 负样本创建修复测试开始...")
    print("="*60)
    
    # 检查当前数据库状态
    check_current_database_status()
    
    # 测试带日期范围的查询
    single_test_result = test_kline_query_with_date_range()
    
    # 测试多只股票的日期范围查询
    success_count, total_count = test_multiple_stocks_date_query()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if single_test_result:
        print("✅ 带日期范围的K线查询功能正常")
    else:
        print("❌ 带日期范围的K线查询仍有问题")
    
    print(f"📊 多股票查询测试: {success_count}/{total_count} 成功")
    
    if single_test_result and success_count > 0:
        print("✅ 修复成功！现在可以重新运行完整训练")
        print("💡 建议: 重新运行完整训练，应该能够创建负样本了")
    else:
        print("❌ 修复可能不完整，需要进一步调试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
