#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全面功能测试脚本
模拟交易时间，完整测试监控流程
"""

import sys
import os
import json
from pathlib import Path
from datetime import datetime, time
import pandas as pd

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def full_monitoring_test():
    """全面监控功能测试"""
    print("🚀 开始全面监控功能测试")
    print("=" * 80)
    
    try:
        # 导入应用类
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        
        # 创建测试监控器
        class TestMonitor(LimitUpPotentialMonitor):
            def __init__(self):
                # 跳过GUI初始化，只初始化核心功能
                self.data_cache_dir = Path("historical_data_cache")
                self.data_cache_dir.mkdir(exist_ok=True)
                
                # 配置参数
                self.config = {
                    'scan_interval': 30,
                    'key_time_scan_interval': 15,
                    'flat_open_threshold': 1.0,
                    'high_open_min': 2.0,
                    'high_open_max': 6.0,
                    'volume_spike_threshold': 3.0,
                    'depth_change_threshold': 0.5,
                    'volume_ratio_threshold': 3.0,
                    'cancel_rate_threshold': 0.4,
                    'notification_duration': 30
                }
                
                # 数据缓存
                self.monitored_stocks = {}
                self.historical_base_data = {}
                self.realtime_data_cache = {}
                self.continuity_cache = {}
                
                # 模拟交易时间
                self.is_trading_time_override = True
                
            def is_trading_time(self):
                """重写交易时间检查，模拟交易时间"""
                return self.is_trading_time_override
        
        monitor = TestMonitor()
        
        # 测试股票列表
        test_stocks = {
            "000001": {"name": "平安银行", "initialized": False},
            "600519": {"name": "贵州茅台", "initialized": False},
            "000002": {"name": "万科A", "initialized": False}
        }
        
        monitor.monitored_stocks = test_stocks
        
        print(f"📊 测试股票: {len(test_stocks)} 只")
        for code, info in test_stocks.items():
            print(f"   - {code} {info['name']}")
        
        return test_complete_workflow(monitor)
        
    except Exception as e:
        print(f"❌ 测试初始化失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_complete_workflow(monitor):
    """测试完整工作流程"""
    print("\n🔄 开始完整工作流程测试...")
    print("-" * 60)
    
    test_results = []
    
    # 1. 测试历史数据获取和初始化
    print("\n1️⃣ 测试历史数据获取和初始化")
    result1 = test_historical_data_workflow(monitor)
    test_results.append(result1)
    
    # 2. 测试实时数据获取
    print("\n2️⃣ 测试实时数据获取")
    result2 = test_realtime_data_workflow(monitor)
    test_results.append(result2)
    
    # 3. 测试核心算法分析
    print("\n3️⃣ 测试核心算法分析")
    result3 = test_algorithm_workflow(monitor)
    test_results.append(result3)
    
    # 4. 测试信号检测和通知
    print("\n4️⃣ 测试信号检测和通知")
    result4 = test_signal_detection_workflow(monitor)
    test_results.append(result4)
    
    # 5. 测试时段策略
    print("\n5️⃣ 测试时段策略")
    result5 = test_time_strategy_workflow(monitor)
    test_results.append(result5)
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 完整工作流程测试结果:")
    
    test_names = [
        "历史数据获取和初始化",
        "实时数据获取",
        "核心算法分析", 
        "信号检测和通知",
        "时段策略"
    ]
    
    for i, (name, result) in enumerate(zip(test_names, test_results)):
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {i+1}. {name}: {status}")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"\n🎯 总体结果: {passed}/{total} 通过 ({passed/total:.1%})")
    
    if passed == total:
        print("🎉 所有测试通过！监控系统运行正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")
    
    return passed == total

def test_historical_data_workflow(monitor):
    """测试历史数据工作流程"""
    try:
        print("   🔍 检查历史数据完整性...")
        
        success_count = 0
        for code in monitor.monitored_stocks.keys():
            # 检查数据完整性
            is_complete = monitor.check_historical_data_completeness(code)
            print(f"      {code}: {'完整' if is_complete else '需要下载'}")
            
            if not is_complete:
                print(f"   📥 下载 {code} 历史数据...")
                if monitor.download_single_stock_historical_data(code):
                    success_count += 1
                    print(f"      ✅ {code} 下载成功")
                else:
                    print(f"      ❌ {code} 下载失败")
            else:
                # 加载缓存数据
                if monitor.load_historical_data_from_cache(code):
                    success_count += 1
                    print(f"      ✅ {code} 缓存加载成功")
        
        print(f"   📊 历史数据准备完成: {success_count}/{len(monitor.monitored_stocks)} 只股票")
        return success_count > 0
        
    except Exception as e:
        print(f"   ❌ 历史数据测试失败: {e}")
        return False

def test_realtime_data_workflow(monitor):
    """测试实时数据工作流程"""
    try:
        print("   🔍 获取实时数据...")
        
        success_count = 0
        for code, info in monitor.monitored_stocks.items():
            print(f"   📡 获取 {code} {info['name']} 实时数据...")
            
            # 获取实时数据
            stock_data = monitor.fetch_realtime_stock_data(code)
            
            if stock_data:
                print(f"      ✅ 数据获取成功")
                print(f"         当前价格: {stock_data.get('current_price', 'N/A')}")
                print(f"         涨跌幅: {stock_data.get('change_pct', 'N/A')}")
                print(f"         买一量: {stock_data.get('bid_volumes', [0])[0] if stock_data.get('bid_volumes') else 'N/A'}")
                print(f"         卖一量: {stock_data.get('ask_volumes', [0])[0] if stock_data.get('ask_volumes') else 'N/A'}")
                
                # 更新缓存
                monitor.update_realtime_data_cache(code, stock_data)
                success_count += 1
            else:
                print(f"      ❌ 数据获取失败")
        
        print(f"   📊 实时数据获取完成: {success_count}/{len(monitor.monitored_stocks)} 只股票")
        return success_count > 0
        
    except Exception as e:
        print(f"   ❌ 实时数据测试失败: {e}")
        return False

def test_algorithm_workflow(monitor):
    """测试核心算法工作流程"""
    try:
        print("   🔍 测试核心算法...")
        
        success_count = 0
        for code in monitor.monitored_stocks.keys():
            if code not in monitor.realtime_data_cache:
                print(f"      ⚠️ {code} 无实时数据，跳过算法测试")
                continue
                
            stock_data = monitor.realtime_data_cache[code]['stock_data']
            print(f"   🧮 分析 {code} 算法...")
            
            try:
                # 1. 开盘类型分类
                open_type_result = monitor.classify_open_type(stock_data)
                print(f"      开盘类型: {open_type_result.get('type_name', '未知')}")
                
                # 2. 买盘厚度分析
                bid_depth_result = monitor.analyze_bid_depth(code, stock_data)
                print(f"      买盘厚度: {bid_depth_result.get('reason', '未知')}")
                
                # 3. 挂单模式分析
                order_pattern_result = monitor.analyze_order_pattern(code, stock_data)
                print(f"      挂单模式: {order_pattern_result.get('pattern_description', '未知')}")
                
                # 4. 撤单行为分析
                cancel_behavior_result = monitor.analyze_cancel_behavior(code, stock_data)
                print(f"      撤单行为: {cancel_behavior_result.get('behavior_description', '未知')}")
                
                success_count += 1
                
            except Exception as e:
                print(f"      ❌ {code} 算法分析失败: {e}")
        
        print(f"   📊 算法分析完成: {success_count}/{len(monitor.monitored_stocks)} 只股票")
        return success_count > 0
        
    except Exception as e:
        print(f"   ❌ 算法测试失败: {e}")
        return False

def test_signal_detection_workflow(monitor):
    """测试信号检测工作流程"""
    try:
        print("   🔍 测试信号检测...")
        
        success_count = 0
        for code in monitor.monitored_stocks.keys():
            if code not in monitor.realtime_data_cache:
                continue
                
            stock_data = monitor.realtime_data_cache[code]['stock_data']
            print(f"   🎯 检测 {code} 买入信号...")
            
            try:
                # 执行完整的信号检测
                signal_result = monitor.detect_limit_up_signals(code, stock_data)
                
                print(f"      算法类型: {signal_result.get('algorithm_type', '未知')}")
                print(f"      信号强度: {signal_result.get('signal_strength', 0):.1%}")
                print(f"      买入信号: {'是' if signal_result.get('buy_signal', False) else '否'}")
                print(f"      操作建议: {signal_result.get('recommendation', '未知')}")
                print(f"      建议仓位: {signal_result.get('position_size', 0):.1%}")
                
                success_count += 1
                
            except Exception as e:
                print(f"      ❌ {code} 信号检测失败: {e}")
        
        print(f"   📊 信号检测完成: {success_count}/{len(monitor.monitored_stocks)} 只股票")
        return success_count > 0
        
    except Exception as e:
        print(f"   ❌ 信号检测测试失败: {e}")
        return False

def test_time_strategy_workflow(monitor):
    """测试时段策略工作流程"""
    try:
        print("   🔍 测试时段策略...")
        
        # 测试不同时段的策略
        test_times = [
            time(9, 35),   # 开盘量能监控
            time(10, 15),  # 均价征服监控
            time(11, 0),   # 盘中突击监控
            time(13, 30),  # 午盘冲锋监控
        ]
        
        for test_time in test_times:
            strategy = monitor.get_time_based_strategy(test_time)
            print(f"      {test_time.strftime('%H:%M')} - {strategy['name']}")
            print(f"         扫描间隔: {strategy.get('scan_interval', 30)}秒")
            print(f"         关键指标: {', '.join(strategy.get('key_indicators', []))}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 时段策略测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🎯 连板潜力股实时监控 - 全面功能测试")
    print("模拟交易时间，测试完整监控流程")
    print()
    
    success = full_monitoring_test()
    
    if success:
        print("\n🎉 全面测试完成！系统运行正常")
    else:
        print("\n⚠️ 测试发现问题，需要进一步优化")
