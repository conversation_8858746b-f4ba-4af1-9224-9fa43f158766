#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
涨停数据分析器
用于分析历史涨停数据，统计股票涨停次数
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Set, Any
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class LimitUpAnalyzer:
    """涨停数据分析器"""
    
    def __init__(self, data_path: str = "limit_up_data"):
        """
        初始化涨停数据分析器

        Args:
            data_path: 涨停数据文件夹路径
        """
        # 尝试多个可能的路径
        possible_paths = [
            data_path,  # 相对路径
            os.path.join(os.path.dirname(__file__), "..", data_path),  # 项目根目录
            os.path.join(os.path.dirname(os.path.dirname(__file__)), data_path),  # 上级目录
            os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), data_path)  # 更上级目录
        ]

        self.data_path = None
        for path in possible_paths:
            abs_path = os.path.abspath(path)
            if os.path.exists(abs_path):
                self.data_path = abs_path
                break

        if not self.data_path:
            logger.warning(f"⚠️ 未找到涨停数据目录，尝试的路径: {possible_paths}")
            self.data_path = data_path  # 使用默认路径
        self.limit_up_stats = {}  # 缓存统计结果
        self.name_to_code_map = {}  # 股票名称到代码的映射
        self.code_to_name_map = {}  # 股票代码到名称的映射
        
        logger.info("🔍 涨停数据分析器初始化完成")
    
    def load_annual_limit_up_stats(self, days_back: int = 365) -> Dict[str, int]:
        """
        加载年度涨停统计数据
        
        Args:
            days_back: 回溯天数，默认365天
            
        Returns:
            Dict[str, int]: 股票代码到涨停次数的映射
        """
        try:
            logger.info(f"📊 开始统计过去{days_back}天的涨停数据...")
            
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days_back)
            
            # 统计涨停次数
            limit_up_count = defaultdict(int)
            processed_files = 0
            total_limit_ups = 0
            
            # 遍历数据文件
            for filename in os.listdir(self.data_path):
                if not filename.endswith('.json') or filename == 'annual_stats_2025.json':
                    continue
                
                try:
                    # 解析日期
                    date_str = filename.replace('.json', '')
                    file_date = datetime.strptime(date_str, '%Y%m%d')
                    
                    # 检查日期范围
                    if file_date < start_date or file_date > end_date:
                        continue
                    
                    # 读取文件
                    file_path = os.path.join(self.data_path, filename)
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    # 统计涨停股票
                    stocks = data.get('stocks', [])
                    for stock in stocks:
                        code = stock.get('code', '')
                        name = stock.get('name', '')
                        
                        if code and name:
                            # 标准化股票代码
                            code = self._normalize_stock_code(code)
                            
                            # 更新统计
                            limit_up_count[code] += 1
                            total_limit_ups += 1
                            
                            # 更新映射关系
                            self.name_to_code_map[name] = code
                            self.code_to_name_map[code] = name
                    
                    processed_files += 1
                    
                except Exception as e:
                    logger.warning(f"处理文件{filename}失败: {e}")
                    continue
            
            logger.info(f"✅ 统计完成: 处理{processed_files}个文件，{total_limit_ups}次涨停")
            logger.info(f"📈 涨停股票数量: {len(limit_up_count)}只")
            
            # 缓存结果
            self.limit_up_stats = dict(limit_up_count)
            
            return self.limit_up_stats
            
        except Exception as e:
            logger.error(f"加载涨停统计数据失败: {e}")
            return {}
    
    def get_stocks_with_min_limit_ups(self, min_count: int = 3, days_back: int = 365) -> Set[str]:
        """
        获取涨停次数达到最小要求的股票代码集合
        
        Args:
            min_count: 最小涨停次数
            days_back: 回溯天数
            
        Returns:
            Set[str]: 符合条件的股票代码集合
        """
        try:
            # 如果没有缓存数据，先加载
            if not self.limit_up_stats:
                self.load_annual_limit_up_stats(days_back)
            
            # 筛选符合条件的股票
            qualified_stocks = {
                code for code, count in self.limit_up_stats.items() 
                if count >= min_count
            }
            
            logger.info(f"🎯 涨停次数≥{min_count}次的股票: {len(qualified_stocks)}只")
            
            # 显示前10只股票的统计
            if qualified_stocks:
                sorted_stocks = sorted(
                    [(code, self.limit_up_stats[code]) for code in qualified_stocks],
                    key=lambda x: x[1], reverse=True
                )
                
                logger.info("📋 涨停次数最多的前10只股票:")
                for i, (code, count) in enumerate(sorted_stocks[:10], 1):
                    name = self.code_to_name_map.get(code, '未知')
                    logger.info(f"   {i}. {code} {name}: {count}次")
            
            return qualified_stocks
            
        except Exception as e:
            logger.error(f"获取涨停股票失败: {e}")
            return set()
    
    def get_stock_limit_up_count(self, stock_code: str) -> int:
        """
        获取指定股票的涨停次数
        
        Args:
            stock_code: 股票代码
            
        Returns:
            int: 涨停次数
        """
        if not self.limit_up_stats:
            self.load_annual_limit_up_stats()
        
        code = self._normalize_stock_code(stock_code)
        return self.limit_up_stats.get(code, 0)
    
    def get_stock_name_by_code(self, stock_code: str) -> str:
        """
        根据股票代码获取股票名称
        
        Args:
            stock_code: 股票代码
            
        Returns:
            str: 股票名称
        """
        code = self._normalize_stock_code(stock_code)
        return self.code_to_name_map.get(code, '')
    
    def get_stock_code_by_name(self, stock_name: str) -> str:
        """
        根据股票名称获取股票代码
        
        Args:
            stock_name: 股票名称
            
        Returns:
            str: 股票代码
        """
        return self.name_to_code_map.get(stock_name, '')
    
    def _normalize_stock_code(self, code: str) -> str:
        """
        标准化股票代码
        
        Args:
            code: 原始股票代码
            
        Returns:
            str: 标准化后的股票代码
        """
        if not code:
            return ''
        
        # 移除可能的前缀和后缀
        code = str(code).strip()
        
        # 移除市场标识
        if '.' in code:
            code = code.split('.')[0]
        
        # 确保是6位数字
        if code.isdigit() and len(code) == 6:
            return code
        
        return code
    
    def get_statistics_summary(self) -> Dict[str, Any]:
        """
        获取统计摘要
        
        Returns:
            Dict[str, Any]: 统计摘要信息
        """
        if not self.limit_up_stats:
            self.load_annual_limit_up_stats()
        
        if not self.limit_up_stats:
            return {}
        
        counts = list(self.limit_up_stats.values())
        
        summary = {
            'total_stocks': len(self.limit_up_stats),
            'total_limit_ups': sum(counts),
            'max_limit_ups': max(counts) if counts else 0,
            'min_limit_ups': min(counts) if counts else 0,
            'avg_limit_ups': sum(counts) / len(counts) if counts else 0,
            'stocks_with_3_plus': len([c for c in counts if c >= 3]),
            'stocks_with_5_plus': len([c for c in counts if c >= 5]),
            'stocks_with_10_plus': len([c for c in counts if c >= 10])
        }
        
        return summary
