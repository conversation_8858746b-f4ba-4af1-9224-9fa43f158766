#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试全部清空功能
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
import threading
import time
import tempfile
import shutil
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_clear_all_function():
    """测试全部清空功能"""
    print("🧪 开始测试全部清空功能...")
    
    try:
        # 导入修改后的模块
        from 潜在股进一步筛选 import StockFurtherScreening
        
        # 创建一个临时目录用于测试
        temp_dir = Path(tempfile.mkdtemp())
        print(f"📁 创建临时测试目录: {temp_dir}")
        
        # 创建筛选器实例
        screener = StockFurtherScreening()
        screener.root.withdraw()  # 隐藏窗口
        
        # 备份原始配置路径
        original_config_file = screener.config_file
        original_cache_dir = screener.data_cache_dir
        
        # 设置临时路径
        screener.config_file = temp_dir / "test_config.json"
        screener.data_cache_dir = temp_dir / "test_cache"
        screener.data_cache_dir.mkdir(exist_ok=True)
        
        print("✅ 测试环境设置完成")
        
        # 添加一些测试股票
        test_stocks = {
            "000001": {"name": "平安银行", "data": {}, "signals": {}, "decision": {}, 
                      "last_update": None, "initialized": True, "added_time": "2025-01-01", "alert_history": []},
            "000002": {"name": "万科A", "data": {}, "signals": {}, "decision": {}, 
                      "last_update": None, "initialized": True, "added_time": "2025-01-01", "alert_history": []}
        }
        
        # 手动添加测试股票到监控列表
        screener.monitored_stocks.update(test_stocks)
        
        # 添加一些历史数据
        screener.historical_base_data["000001"] = {"stock_name": "平安银行", "prev_close": 10.0}
        screener.historical_base_data["000002"] = {"stock_name": "万科A", "prev_close": 6.5}
        
        # 创建一些测试缓存文件
        (screener.data_cache_dir / "000001_historical_data.json").write_text('{"test": "data"}')
        (screener.data_cache_dir / "000002_historical_data.json").write_text('{"test": "data"}')
        
        # 保存配置文件
        import json
        with open(screener.config_file, 'w', encoding='utf-8') as f:
            json.dump({"stocks": test_stocks}, f, ensure_ascii=False, indent=2)
        
        print(f"📊 添加测试数据: {len(screener.monitored_stocks)} 只股票")
        print(f"📊 历史数据: {len(screener.historical_base_data)} 条记录")
        print(f"📁 配置文件: {screener.config_file.exists()}")
        print(f"📁 缓存目录: {screener.data_cache_dir.exists()}")
        print(f"📁 缓存文件数量: {len(list(screener.data_cache_dir.glob('*.json')))}")
        
        # 模拟用户确认清空操作（重写messagebox.askyesno）
        original_askyesno = messagebox.askyesno
        messagebox.askyesno = lambda *args, **kwargs: True  # 总是返回True（确认）
        
        try:
            # 执行清空操作
            print("\n🗑️ 执行清空操作...")
            screener.clear_all_stocks()
            
            # 检查清空结果
            print("\n📋 检查清空结果:")
            
            # 检查监控股票列表
            if len(screener.monitored_stocks) == 0:
                print("   ✅ 监控股票列表已清空")
            else:
                print(f"   ❌ 监控股票列表未清空，还有 {len(screener.monitored_stocks)} 只股票")
            
            # 检查历史数据
            if len(screener.historical_base_data) == 0:
                print("   ✅ 历史数据已清空")
            else:
                print(f"   ❌ 历史数据未清空，还有 {len(screener.historical_base_data)} 条记录")
            
            # 检查配置文件
            if not screener.config_file.exists():
                print("   ✅ 配置文件已删除")
            else:
                print("   ❌ 配置文件仍然存在")
            
            # 检查缓存目录
            cache_files = list(screener.data_cache_dir.glob('*.json'))
            if len(cache_files) == 0:
                print("   ✅ 缓存文件已清空")
            else:
                print(f"   ❌ 缓存文件未清空，还有 {len(cache_files)} 个文件")
            
            print("\n✅ 全部清空功能测试完成")
            
        finally:
            # 恢复原始messagebox函数
            messagebox.askyesno = original_askyesno
        
        # 清理测试环境
        screener.root.destroy()
        shutil.rmtree(temp_dir)
        print(f"🧹 清理临时测试目录: {temp_dir}")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试全部清空功能")
    print("=" * 50)
    
    test_clear_all_function()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
