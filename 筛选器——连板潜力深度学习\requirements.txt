# 连板潜力股深度学习器依赖包列表

# 核心数据处理
pandas>=1.5.0
numpy>=1.21.0
scipy>=1.9.0

# 数据获取
akshare>=1.11.0
requests>=2.28.0
beautifulsoup4>=4.11.0
lxml>=4.9.0

# 机器学习
scikit-learn>=1.1.0
xgboost>=1.6.0
lightgbm>=3.3.0
tensorflow>=2.10.0
keras>=2.10.0
torch>=1.12.0
torchvision>=0.13.0

# 数据可视化
matplotlib>=3.5.0
plotly>=5.10.0
seaborn>=0.11.0
bokeh>=2.4.0

# 技术指标计算
talib>=0.4.0
ta>=0.10.0
pandas-ta>=0.3.0

# 数据库
sqlite3
sqlalchemy>=1.4.0
pymongo>=4.2.0

# 时间处理
python-dateutil>=2.8.0
pytz>=2022.1

# 并发处理
asyncio
aiohttp>=3.8.0
concurrent-futures>=3.1.0

# 配置管理
pyyaml>=6.0
configparser>=5.3.0
python-dotenv>=0.20.0

# 日志处理
loguru>=0.6.0

# 数学计算
sympy>=1.11.0
statsmodels>=0.13.0

# 网络请求
urllib3>=1.26.0
certifi>=2022.6.0

# 文件处理
openpyxl>=3.0.0
xlrd>=2.0.0
xlwt>=1.3.0

# 进度条
tqdm>=4.64.0

# 内存优化
psutil>=5.9.0
memory-profiler>=0.60.0

# 测试框架
pytest>=7.1.0
pytest-cov>=3.0.0
unittest2>=1.1.0

# 代码质量
flake8>=5.0.0
black>=22.6.0
isort>=5.10.0

# 文档生成
sphinx>=5.1.0
sphinx-rtd-theme>=1.0.0

# 其他工具
joblib>=1.1.0
pickle5>=0.0.12
dill>=0.3.5
cloudpickle>=2.1.0

# 特定版本要求（避免兼容性问题）
# 注意：某些包可能需要根据实际环境调整版本
