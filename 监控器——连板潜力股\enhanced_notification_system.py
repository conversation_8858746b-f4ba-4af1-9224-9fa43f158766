#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版通知系统
使用Windows Toast通知样式，更专业的股票通知体验
"""

import tkinter as tk
from tkinter import messagebox
import winsound
import threading
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
import logging
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EnhancedNotificationSystem:
    """增强版通知系统"""
    
    def __init__(self):
        """初始化增强版通知系统"""
        self.notification_history = []
        self.sound_enabled = True
        self.popup_enabled = True
        self.toast_enabled = True
        
        # 尝试导入Windows Toast通知
        self.toast_notifier = None
        try:
            from win10toast import ToastNotifier
            self.toast_notifier = ToastNotifier()
            self.toast_available = True
            logger.info("✅ Windows Toast通知可用")
        except ImportError:
            self.toast_available = False
            logger.warning("⚠️ Windows Toast通知不可用，使用备用方案")
        
        logger.info("✅ 增强版通知系统初始化完成")
    
    def send_critical_alert(self, stock_code: str, stock_name: str, 
                          alert_type: str, message: str, 
                          current_price: float, profit_pct: float,
                          confidence: str = "高") -> bool:
        """
        发送关键警报（增强版）
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            alert_type: 警报类型
            message: 警报消息
            current_price: 当前价格
            profit_pct: 盈亏百分比
            confidence: 置信度
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建通知内容
            notification = {
                'timestamp': datetime.now(),
                'stock_code': stock_code,
                'stock_name': stock_name,
                'alert_type': alert_type,
                'message': message,
                'current_price': current_price,
                'profit_pct': profit_pct,
                'confidence': confidence
            }
            
            # 记录通知历史
            self.notification_history.append(notification)
            
            # 发送通知（优先使用Toast）
            if self.toast_available and self.toast_enabled:
                self._send_toast_notification(notification)
            else:
                self._send_popup_notification(notification)
            
            # 播放声音
            self._play_alert_sound(alert_type)
            
            # 记录日志
            self._log_notification(notification)
            
            return True
            
        except Exception as e:
            logger.error(f"发送关键警报失败: {e}")
            return False
    
    def send_stop_loss_alert(self, stock_code: str, stock_name: str, 
                           current_price: float, loss_pct: float,
                           reason: str) -> bool:
        """发送止损警报（Windows Toast样式）"""
        # 构建Toast通知内容
        title = f"🚨 止损警报 - {stock_name}"
        message = f"代码: {stock_code}\n" \
                 f"价格: {current_price:.2f}\n" \
                 f"亏损: {loss_pct:.2f}%\n" \
                 f"原因: {reason}\n\n" \
                 f"⚠️ 建议立即止损！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'stop_loss', message, 
            current_price, loss_pct, '高'
        )
    
    def send_take_profit_alert(self, stock_code: str, stock_name: str,
                             current_price: float, profit_pct: float,
                             strategy: str) -> bool:
        """发送止盈提醒（Windows Toast样式）"""
        title = f"🎉 止盈提醒 - {stock_name}"
        message = f"代码: {stock_code}\n" \
                 f"价格: {current_price:.2f}\n" \
                 f"盈利: {profit_pct:.2f}%\n" \
                 f"策略: {strategy}\n\n" \
                 f"💰 考虑获利了结！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'take_profit', message,
            current_price, profit_pct, '高'
        )
    
    def send_critical_decision_alert(self, stock_code: str, stock_name: str,
                                   current_price: float, profit_pct: float,
                                   decision: str, reason: str, hold_days: int) -> bool:
        """发送关键决策警报（Windows Toast样式）"""
        # 根据持有天数确定标题
        if hold_days == 3:
            title = f"🎯 第3天关键决策 - {stock_name}"
        elif hold_days == 4:
            title = f"🚀 第4天拉升确认 - {stock_name}"
        elif hold_days >= 5:
            title = f"⚠️ 第{hold_days}天超期持有 - {stock_name}"
        else:
            title = f"📊 第{hold_days}天决策 - {stock_name}"
        
        message = f"代码: {stock_code}\n" \
                 f"价格: {current_price:.2f}\n" \
                 f"盈亏: {profit_pct:+.2f}%\n" \
                 f"决策: {decision}\n" \
                 f"原因: {reason}\n\n" \
                 f"🔔 请及时关注！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'critical_decision', message,
            current_price, profit_pct, '高'
        )
    
    def send_main_force_escape_alert(self, stock_code: str, stock_name: str,
                                   current_price: float, loss_pct: float,
                                   escape_signals: List[str]) -> bool:
        """发送主力出逃警报（Windows Toast样式）"""
        title = f"🚨 主力出逃警报 - {stock_name}"
        signals_text = "、".join(escape_signals[:3])  # 只显示前3个信号
        
        message = f"代码: {stock_code}\n" \
                 f"价格: {current_price:.2f}\n" \
                 f"亏损: {loss_pct:.2f}%\n" \
                 f"信号: {signals_text}\n\n" \
                 f"⚠️ 主力已出逃，立即止损！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'main_force_escape', message,
            current_price, loss_pct, '极高'
        )
    
    def send_wash_trading_alert(self, stock_code: str, stock_name: str,
                              current_price: float, profit_pct: float,
                              wash_probability: float, wash_signals: List[str]) -> bool:
        """发送洗盘确认警报（Windows Toast样式）"""
        title = f"🌊 洗盘确认 - {stock_name}"
        signals_text = "、".join(wash_signals[:3])  # 只显示前3个信号
        
        message = f"代码: {stock_code}\n" \
                 f"价格: {current_price:.2f}\n" \
                 f"盈亏: {profit_pct:+.2f}%\n" \
                 f"概率: {wash_probability:.1%}\n" \
                 f"信号: {signals_text}\n\n" \
                 f"💪 坚定持有，不要被洗出去！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'wash_trading', message,
            current_price, profit_pct, '高'
        )
    
    def _send_toast_notification(self, notification: Dict) -> bool:
        """发送Windows Toast通知"""
        try:
            if not self.toast_available or not self.toast_notifier:
                return False
            
            # 构建Toast标题和内容
            alert_type = notification['alert_type']
            stock_name = notification['stock_name']
            stock_code = notification['stock_code']
            
            # 根据类型设置标题图标
            if alert_type == 'stop_loss':
                title = f"🚨 止损警报 - {stock_name}"
            elif alert_type == 'take_profit':
                title = f"🎉 止盈提醒 - {stock_name}"
            elif alert_type == 'main_force_escape':
                title = f"🚨 主力出逃 - {stock_name}"
            elif alert_type == 'wash_trading':
                title = f"🌊 洗盘确认 - {stock_name}"
            else:
                title = f"📊 股票提醒 - {stock_name}"
            
            # Toast消息内容
            msg = notification['message']
            
            # 发送Toast通知
            def send_toast():
                try:
                    self.toast_notifier.show_toast(
                        title=title,
                        msg=msg,
                        duration=15,  # 显示15秒
                        icon_path=None,  # 可以添加自定义图标
                        threaded=True
                    )
                except Exception as e:
                    logger.error(f"Toast通知发送失败: {e}")
            
            # 在新线程中发送
            toast_thread = threading.Thread(target=send_toast, daemon=True)
            toast_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"发送Toast通知失败: {e}")
            return False
    
    def _send_popup_notification(self, notification: Dict) -> bool:
        """发送弹窗通知（备用方案）"""
        try:
            if not self.popup_enabled:
                return True
            
            def show_popup():
                try:
                    root = tk.Tk()
                    root.withdraw()
                    root.attributes('-topmost', True)
                    root.lift()
                    root.focus_force()
                    
                    alert_type = notification['alert_type']
                    if alert_type == 'stop_loss':
                        title = "🚨 止损警报"
                        icon = messagebox.ERROR
                    elif alert_type == 'take_profit':
                        title = "🎉 止盈提醒"
                        icon = messagebox.INFO
                    elif alert_type == 'main_force_escape':
                        title = "🚨 主力出逃"
                        icon = messagebox.ERROR
                    elif alert_type == 'wash_trading':
                        title = "🌊 洗盘确认"
                        icon = messagebox.WARNING
                    else:
                        title = "📊 关键决策"
                        icon = messagebox.WARNING
                    
                    messagebox.showinfo(title, notification['message'], icon=icon)
                    root.destroy()
                    
                except Exception as e:
                    logger.error(f"显示弹窗失败: {e}")
            
            popup_thread = threading.Thread(target=show_popup, daemon=True)
            popup_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"发送弹窗通知失败: {e}")
            return False
    
    def _play_alert_sound(self, alert_type: str) -> bool:
        """播放警报声音"""
        try:
            if not self.sound_enabled:
                return True
            
            def play_sound():
                try:
                    if alert_type in ['stop_loss', 'main_force_escape']:
                        # 紧急警报声 - 连续3声
                        for _ in range(3):
                            winsound.Beep(1000, 500)
                            time.sleep(0.2)
                    elif alert_type == 'take_profit':
                        # 成功提示声 - 上升音调
                        winsound.Beep(800, 300)
                        winsound.Beep(1000, 300)
                        winsound.Beep(1200, 300)
                    else:
                        # 普通提示声
                        winsound.Beep(800, 400)
                        
                except Exception as e:
                    logger.error(f"播放声音失败: {e}")
            
            sound_thread = threading.Thread(target=play_sound, daemon=True)
            sound_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"播放警报声音失败: {e}")
            return False
    
    def _log_notification(self, notification: Dict) -> None:
        """记录通知日志"""
        try:
            timestamp = notification['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            stock_info = f"{notification['stock_name']}({notification['stock_code']})"
            alert_type = notification['alert_type']
            profit_pct = notification['profit_pct']
            
            log_message = f"📢 通知发送: {timestamp} | {stock_info} | {alert_type} | {profit_pct:+.2f}%"
            logger.info(log_message)
            
            # 控制台输出
            print(f"\n{'='*60}")
            print(f"📢 {alert_type.upper()} 通知已发送")
            print(f"📊 股票: {stock_info}")
            print(f"💰 盈亏: {profit_pct:+.2f}%")
            print(f"⏰ 时间: {timestamp}")
            if self.toast_available:
                print(f"📱 样式: Windows Toast通知")
            else:
                print(f"📱 样式: 弹窗通知")
            print(f"{'='*60}\n")
            
        except Exception as e:
            logger.error(f"记录通知日志失败: {e}")
    
    def get_notification_history(self, hours: int = 24) -> List[Dict]:
        """获取通知历史"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_notifications = [
                notif for notif in self.notification_history
                if notif['timestamp'] > cutoff_time
            ]
            return recent_notifications
        except Exception as e:
            logger.error(f"获取通知历史失败: {e}")
            return []
    
    def clear_notification_history(self) -> bool:
        """清空通知历史"""
        try:
            self.notification_history.clear()
            logger.info("✅ 通知历史已清空")
            return True
        except Exception as e:
            logger.error(f"清空通知历史失败: {e}")
            return False
    
    def set_sound_enabled(self, enabled: bool) -> None:
        """设置声音开关"""
        self.sound_enabled = enabled
        logger.info(f"🔊 声音通知: {'开启' if enabled else '关闭'}")
    
    def set_popup_enabled(self, enabled: bool) -> None:
        """设置弹窗开关"""
        self.popup_enabled = enabled
        logger.info(f"💬 弹窗通知: {'开启' if enabled else '关闭'}")
    
    def set_toast_enabled(self, enabled: bool) -> None:
        """设置Toast通知开关"""
        self.toast_enabled = enabled
        logger.info(f"📱 Toast通知: {'开启' if enabled else '关闭'}")
    
    def get_notification_status(self) -> Dict[str, Any]:
        """获取通知系统状态"""
        return {
            'toast_available': self.toast_available,
            'toast_enabled': self.toast_enabled,
            'popup_enabled': self.popup_enabled,
            'sound_enabled': self.sound_enabled,
            'notification_count': len(self.notification_history)
        }

# 创建全局实例
enhanced_notification_system = EnhancedNotificationSystem()
