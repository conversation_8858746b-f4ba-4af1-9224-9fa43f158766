#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试量比修复效果
"""

import sys
import os
import time
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_volume_ratio_fix():
    """测试量比修复效果"""
    print("🔍 测试量比修复效果")
    print("=" * 50)
    
    try:
        # 导入系统
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        from enhanced_data_fetcher import EnhancedStockDataFetcher
        
        print("✅ 系统导入成功")
        
        # 创建监控器实例
        monitor = LimitUpPotentialMonitor()
        
        # 创建数据获取器
        fetcher = EnhancedStockDataFetcher()
        
        # 测试股票代码（使用监控列表中的股票）
        test_codes = ['603115', '002645', '000751']
        
        print(f"\n🔍 测试 {len(test_codes)} 只股票的量比计算")
        print("-" * 30)
        
        for code in test_codes:
            try:
                print(f"\n📊 测试股票: {code}")
                
                # 获取实时数据
                real_data = fetcher.get_realtime_quote([code])
                if real_data.empty:
                    print(f"   ❌ 获取实时数据失败")
                    continue
                
                # 转换为字典格式
                stock_data = real_data.iloc[0].to_dict()
                
                print(f"   📊 原始数据:")
                print(f"     当前价格: {stock_data.get('current_price', 0)}")
                print(f"     成交量: {stock_data.get('volume', 0)}")
                print(f"     量比(修复前): {stock_data.get('volume_ratio', '未设置')}")
                
                # 测试量比计算方法
                calculated_ratio = monitor.calculate_current_volume_ratio(code, stock_data)
                print(f"   🔧 计算的量比: {calculated_ratio:.2f}")
                
                # 模拟修复后的处理流程
                volume_ratio = monitor.calculate_current_volume_ratio(code, stock_data)
                stock_data['volume_ratio'] = volume_ratio
                
                print(f"   ✅ 修复后量比: {stock_data.get('volume_ratio', 0):.2f}")
                
                # 验证数据一致性
                if abs(calculated_ratio - stock_data['volume_ratio']) < 0.01:
                    print(f"   ✅ 数据一致性验证通过")
                else:
                    print(f"   ❌ 数据一致性验证失败")
                
            except Exception as e:
                print(f"   ❌ 测试股票 {code} 失败: {e}")
                continue
        
        print(f"\n🔍 测试历史数据获取")
        print("-" * 30)
        
        # 测试历史平均成交量获取
        for code in test_codes[:2]:  # 只测试前2只
            try:
                avg_volume = monitor.get_historical_volume_average(code)
                print(f"   📊 {code} 历史平均成交量: {avg_volume:,.0f}")
                
                if avg_volume > 0:
                    print(f"   ✅ 历史数据可用")
                else:
                    print(f"   ⚠️ 历史数据不可用")
                    
            except Exception as e:
                print(f"   ❌ 获取 {code} 历史数据失败: {e}")
        
        print(f"\n🔍 测试完整处理流程")
        print("-" * 30)
        
        # 测试完整的股票处理流程
        test_code = test_codes[0]
        try:
            # 获取实时数据
            real_data = fetcher.get_realtime_quote([test_code])
            if not real_data.empty:
                stock_data = real_data.iloc[0].to_dict()
                
                print(f"   📊 测试股票: {test_code}")
                print(f"   📊 处理前量比: {stock_data.get('volume_ratio', '未设置')}")
                
                # 模拟execute_stock_selection_algorithms中的修复代码
                volume_ratio = monitor.calculate_current_volume_ratio(test_code, stock_data)
                stock_data['volume_ratio'] = volume_ratio
                
                print(f"   📊 处理后量比: {stock_data.get('volume_ratio', 0):.2f}")
                
                # 验证后续算法能正确获取量比
                retrieved_ratio = stock_data.get('volume_ratio', 0)
                print(f"   📊 算法获取量比: {retrieved_ratio:.2f}")
                
                if retrieved_ratio > 0:
                    print(f"   ✅ 修复成功！量比数据流正常")
                else:
                    print(f"   ❌ 修复失败，量比仍为0")
                    
        except Exception as e:
            print(f"   ❌ 完整流程测试失败: {e}")
        
        print(f"\n🔍 测试边界情况")
        print("-" * 30)
        
        # 测试边界情况
        try:
            # 测试空数据
            empty_data = {}
            ratio = monitor.calculate_current_volume_ratio('000001', empty_data)
            print(f"   📊 空数据量比: {ratio:.2f} (应该为0)")
            
            # 测试无历史数据的股票
            fake_data = {'volume': 1000000}
            ratio = monitor.calculate_current_volume_ratio('999999', fake_data)
            print(f"   📊 无历史数据量比: {ratio:.2f} (应该为0)")
            
            # 测试零成交量
            zero_volume_data = {'volume': 0}
            ratio = monitor.calculate_current_volume_ratio('603115', zero_volume_data)
            print(f"   📊 零成交量量比: {ratio:.2f} (应该为0)")
            
            print(f"   ✅ 边界情况测试完成")
            
        except Exception as e:
            print(f"   ❌ 边界情况测试失败: {e}")
        
        print(f"\n✅ 量比修复测试完成")
        print("=" * 50)
        print("📋 修复总结:")
        print("   1. ✅ 在execute_stock_selection_algorithms中添加量比计算")
        print("   2. ✅ 在process_bought_stock中添加量比计算") 
        print("   3. ✅ 确保stock_data['volume_ratio']正确设置")
        print("   4. ✅ 所有算法现在都能获取到正确的量比数据")
        print("   5. ✅ 显示的量比不再是0.00")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_volume_ratio_in_display():
    """测试量比在显示中的效果"""
    print("\n🔍 测试量比显示效果")
    print("-" * 30)
    
    try:
        # 模拟修复后的stock_data
        mock_stock_data = {
            'stock_code': '000001',
            'stock_name': '平安银行',
            'current_price': 10.50,
            'change_pct': 2.15,
            'volume': 5000000,
            'volume_ratio': 1.85,  # 修复后应该有这个值
            'prev_close': 10.28
        }
        
        print(f"📊 模拟股票数据:")
        print(f"   股票代码: {mock_stock_data['stock_code']}")
        print(f"   股票名称: {mock_stock_data['stock_name']}")
        print(f"   当前价格: {mock_stock_data['current_price']}")
        print(f"   涨跌幅: {mock_stock_data['change_pct']:.2f}%")
        print(f"   量比: {mock_stock_data['volume_ratio']:.2f}")
        
        # 验证显示格式
        volume_ratio = mock_stock_data.get('volume_ratio', 0)
        if volume_ratio > 0:
            print(f"   ✅ 量比显示正常: {volume_ratio:.2f}")
        else:
            print(f"   ❌ 量比显示异常: {volume_ratio}")
            
    except Exception as e:
        print(f"❌ 显示测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始量比修复测试")
    print("=" * 50)
    
    # 运行测试
    test_volume_ratio_fix()
    test_volume_ratio_in_display()
    
    print("\n🏁 测试完成")
    print("=" * 50)
