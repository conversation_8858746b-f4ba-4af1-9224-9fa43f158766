#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试历史数据加载
"""

import sys
import os
import json
from pathlib import Path

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_historical_data_loading():
    """测试历史数据加载"""
    print("🔍 测试历史数据加载")
    print("=" * 50)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        
        print("✅ 系统导入成功")
        
        # 创建监控器实例
        monitor = LimitUpPotentialMonitor()
        
        # 测试股票代码
        test_code = '603115'
        
        print(f"\n🔍 测试股票: {test_code}")
        print("-" * 30)
        
        # 1. 检查历史数据文件是否存在
        cache_file = Path("historical_data_cache") / f"{test_code}_historical_data.json"
        if cache_file.exists():
            print(f"✅ 历史数据文件存在: {cache_file}")
            
            # 读取文件内容
            with open(cache_file, 'r', encoding='utf-8') as f:
                file_data = json.load(f)
            
            print(f"📊 文件中的数据:")
            print(f"   avg_volume_5d: {file_data.get('avg_volume_5d', '未找到')}")
            print(f"   avg_volume_10d: {file_data.get('avg_volume_10d', '未找到')}")
            print(f"   prev_close: {file_data.get('prev_close', '未找到')}")
            
        else:
            print(f"❌ 历史数据文件不存在: {cache_file}")
        
        # 2. 检查内存中的历史数据
        print(f"\n📊 内存中的历史数据:")
        if test_code in monitor.historical_base_data:
            base_data = monitor.historical_base_data[test_code]
            print(f"   ✅ {test_code} 在historical_base_data中")
            print(f"   avg_volume_5d: {base_data.get('avg_volume_5d', '未找到')}")
            print(f"   avg_volume_10d: {base_data.get('avg_volume_10d', '未找到')}")
            print(f"   prev_close: {base_data.get('prev_close', '未找到')}")
        else:
            print(f"   ❌ {test_code} 不在historical_base_data中")
            print(f"   📊 historical_base_data包含的股票: {list(monitor.historical_base_data.keys())}")
        
        # 3. 测试get_historical_volume_average方法
        print(f"\n🔧 测试量比计算方法:")
        avg_volume = monitor.get_historical_volume_average(test_code)
        print(f"   get_historical_volume_average({test_code}): {avg_volume}")
        
        # 4. 测试量比计算
        mock_stock_data = {
            'volume': 500000,  # 50万成交量
            'current_price': 19.0
        }
        
        volume_ratio = monitor.calculate_current_volume_ratio(test_code, mock_stock_data)
        print(f"   calculate_current_volume_ratio({test_code}): {volume_ratio:.2f}")
        
        if avg_volume > 0:
            expected_ratio = 500000 / avg_volume
            print(f"   预期量比: {expected_ratio:.2f}")
            
            if abs(volume_ratio - expected_ratio) < 0.01:
                print(f"   ✅ 量比计算正确")
            else:
                print(f"   ❌ 量比计算错误")
        else:
            print(f"   ❌ 历史平均成交量为0，无法计算量比")
        
        # 5. 测试其他监控股票
        print(f"\n🔍 测试其他监控股票:")
        test_codes = ['002645', '000751', '002550']
        
        for code in test_codes:
            avg_vol = monitor.get_historical_volume_average(code)
            in_memory = code in monitor.historical_base_data
            print(f"   {code}: avg_volume={avg_vol}, in_memory={in_memory}")
        
        # 6. 检查监控股票列表
        print(f"\n📊 监控股票列表:")
        print(f"   监控股票数量: {len(monitor.monitored_stocks)}")
        print(f"   历史数据数量: {len(monitor.historical_base_data)}")
        
        if len(monitor.monitored_stocks) != len(monitor.historical_base_data):
            print(f"   ⚠️ 监控股票数量与历史数据数量不匹配")
            
            missing_in_historical = set(monitor.monitored_stocks.keys()) - set(monitor.historical_base_data.keys())
            missing_in_monitored = set(monitor.historical_base_data.keys()) - set(monitor.monitored_stocks.keys())
            
            if missing_in_historical:
                print(f"   缺少历史数据的股票: {missing_in_historical}")
            if missing_in_monitored:
                print(f"   多余历史数据的股票: {missing_in_monitored}")
        else:
            print(f"   ✅ 监控股票与历史数据数量匹配")
        
        print(f"\n✅ 历史数据加载测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_direct_file_reading():
    """直接测试文件读取"""
    print("\n🔍 直接测试文件读取")
    print("-" * 30)
    
    try:
        cache_dir = Path("historical_data_cache")
        if not cache_dir.exists():
            print(f"❌ 缓存目录不存在: {cache_dir}")
            return
        
        # 列出所有历史数据文件
        json_files = list(cache_dir.glob("*_historical_data.json"))
        print(f"📊 找到 {len(json_files)} 个历史数据文件")
        
        # 检查前3个文件
        for i, file_path in enumerate(json_files[:3]):
            print(f"\n📄 文件 {i+1}: {file_path.name}")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                print(f"   avg_volume_5d: {data.get('avg_volume_5d', '未找到')}")
                print(f"   stock_code: {data.get('stock_code', '未找到')}")
                print(f"   cache_time: {data.get('cache_time', '未找到')}")
                
            except Exception as e:
                print(f"   ❌ 读取失败: {e}")
        
    except Exception as e:
        print(f"❌ 直接文件读取测试失败: {e}")

if __name__ == "__main__":
    print("🚀 开始历史数据加载测试")
    print("=" * 50)
    
    # 运行测试
    test_historical_data_loading()
    test_direct_file_reading()
    
    print("\n🏁 测试完成")
    print("=" * 50)
