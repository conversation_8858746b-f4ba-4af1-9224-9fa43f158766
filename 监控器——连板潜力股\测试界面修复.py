#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试界面修复效果
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_open_type_classification():
    """测试开盘类型分类修复"""
    print("🔍 测试开盘类型分类修复")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        
        monitor = LimitUpPotentialMonitor()
        print("✅ 系统导入成功")
        
        # 测试不同开盘类型的股票数据
        test_cases = [
            {
                'name': '高开股',
                'data': {
                    'current_price': 10.5,
                    'open': 10.3,
                    'prev_close': 10.0,
                    'change_pct': 5.0
                },
                'expected_type': '高开'
            },
            {
                'name': '平开股',
                'data': {
                    'current_price': 10.1,
                    'open': 10.05,
                    'prev_close': 10.0,
                    'change_pct': 1.0
                },
                'expected_type': '平开'
            },
            {
                'name': '低开股',
                'data': {
                    'current_price': 9.8,
                    'open': 9.7,
                    'prev_close': 10.0,
                    'change_pct': -2.0
                },
                'expected_type': '低开'
            }
        ]
        
        print("📊 开盘类型分类测试:")
        for case in test_cases:
            result = monitor.classify_open_type(case['data'])
            type_name = result.get('type_name', '未知')
            open_change = result.get('open_change_pct', 0)
            current_change = result.get('current_change_pct', 0)
            
            status = "✅" if type_name == case['expected_type'] else "❌"
            print(f"   {status} {case['name']}: {type_name} (开盘{open_change:+.2f}%, 当前{current_change:+.2f}%)")
        
        print("✅ 开盘类型分类测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_low_open_algorithm():
    """测试低开算法"""
    print("\n🔍 测试低开反转算法")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        
        monitor = LimitUpPotentialMonitor()
        
        # 模拟低开反转的股票数据
        low_open_data = {
            'current_price': 9.9,
            'open': 9.7,
            'prev_close': 10.0,
            'change_pct': -1.0,
            'volume_ratio': 2.0,  # 量比2.0，满足反转条件
        }
        
        print("📊 低开股数据:")
        print(f"   开盘价: {low_open_data['open']}")
        print(f"   当前价: {low_open_data['current_price']}")
        print(f"   前收盘: {low_open_data['prev_close']}")
        print(f"   量比: {low_open_data['volume_ratio']}")
        
        # 测试低开算法
        result = monitor.analyze_low_open_stock('000001', low_open_data)
        
        print(f"\n🔧 低开算法分析结果:")
        print(f"   量能反转: {'✅' if result.get('volume_reversal') else '❌'}")
        print(f"   价格回升: {'✅' if result.get('price_recovery') else '❌'}")
        print(f"   形态反转: {'✅' if result.get('pattern_reversal') else '❌'}")
        print(f"   买入信号: {'✅' if result.get('buy_signal') else '❌'}")
        print(f"   信号强度: {result.get('signal_strength', 0):.2f}")
        print(f"   操作建议: {result.get('recommendation', '未知')}")
        
        print("✅ 低开算法测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_signal_display():
    """测试信号显示逻辑"""
    print("\n🔍 测试信号显示逻辑")
    print("-" * 40)
    
    try:
        # 模拟不同类型的核心信号
        signal_tests = [
            {
                'type': 'flat_open',
                'name': '平开股',
                'signals': {
                    'volume_spike': True,
                    'depth_anomaly': False,
                    'pattern_break': True
                },
                'expected': ['✅', '❌', '✅']
            },
            {
                'type': 'high_open',
                'name': '高开股',
                'signals': {
                    'volume_consistent': True,
                    'pressure_test': True,
                    'gap_protection': False
                },
                'expected': ['✅', '✅', '❌']
            },
            {
                'type': 'low_open',
                'name': '低开股',
                'signals': {
                    'volume_reversal': True,
                    'price_recovery': True,
                    'pattern_reversal': False
                },
                'expected': ['✅', '✅', '❌']
            }
        ]
        
        print("📊 信号显示测试:")
        for test in signal_tests:
            print(f"\n   📈 {test['name']}:")
            
            if test['type'] == 'flat_open':
                volume_signal = '✅' if test['signals'].get('volume_spike', False) else '❌'
                depth_signal = '✅' if test['signals'].get('depth_anomaly', False) else '❌'
                pattern_signal = '✅' if test['signals'].get('pattern_break', False) else '❌'
                signal_names = ['量能突击', '盘口异动', '形态突破']
            elif test['type'] == 'high_open':
                volume_signal = '✅' if test['signals'].get('volume_consistent', False) else '❌'
                depth_signal = '✅' if test['signals'].get('pressure_test', False) else '❌'
                pattern_signal = '✅' if test['signals'].get('gap_protection', False) else '❌'
                signal_names = ['量能持续', '抛压测试', '跳空守护']
            elif test['type'] == 'low_open':
                volume_signal = '✅' if test['signals'].get('volume_reversal', False) else '❌'
                depth_signal = '✅' if test['signals'].get('price_recovery', False) else '❌'
                pattern_signal = '✅' if test['signals'].get('pattern_reversal', False) else '❌'
                signal_names = ['量能反转', '价格回升', '形态反转']
            
            actual = [volume_signal, depth_signal, pattern_signal]
            
            for i, (name, actual_signal, expected_signal) in enumerate(zip(signal_names, actual, test['expected'])):
                status = "✅" if actual_signal == expected_signal else "❌"
                print(f"     {status} {name}: {actual_signal}")
        
        print("✅ 信号显示测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_interface_columns():
    """测试界面列结构"""
    print("\n🔍 测试界面列结构")
    print("-" * 40)
    
    expected_columns = [
        '股票代码', '股票名称', '实时涨跌', '开盘类型', 
        '量能突击', '盘口异动', '形态突破', '综合信号', 
        '买入建议', '当前价格', '更新时间'
    ]
    
    print("📊 期望的界面列结构:")
    for i, col in enumerate(expected_columns, 1):
        print(f"   {i:2d}. {col}")
    
    print("✅ 界面列结构测试完成")

def main():
    """主测试函数"""
    print("🚀 开始界面修复测试")
    print("=" * 50)
    
    # 运行所有测试
    test_open_type_classification()
    test_low_open_algorithm()
    test_signal_display()
    test_interface_columns()
    
    print("\n📋 修复总结:")
    print("=" * 50)
    print("1. ✅ 界面列结构修复:")
    print("   - 添加'实时涨跌'列")
    print("   - 调整列宽度和显示顺序")
    
    print("\n2. ✅ 开盘类型分类修复:")
    print("   - 简化分类逻辑，基于开盘涨跌幅")
    print("   - 高开: ≥+2%, 低开: ≤-2%, 平开: -2%~+2%")
    print("   - 固定显示，不再动态变化")
    
    print("\n3. ✅ 低开反转算法新增:")
    print("   - 量能反转: 量比>1.5")
    print("   - 价格回升: 从开盘回升>1%")
    print("   - 形态反转: 突破开盘价或接近前收盘")
    print("   - 买入条件: 满足2/3信号即可")
    
    print("\n4. ✅ 信号显示修复:")
    print("   - 平开股: 量能突击、盘口异动、形态突破")
    print("   - 高开股: 量能持续、抛压测试、跳空守护")
    print("   - 低开股: 量能反转、价格回升、形态反转")
    
    print("\n5. ✅ 算法切换优化:")
    print("   - 低开股自动使用低开反转算法")
    print("   - 持续上涨时动态切换到对应算法")
    print("   - 确保上涨力度大的股票被捕捉")
    
    print("\n🎯 预期效果:")
    print("- 开盘类型不再显示'未知'，固定显示高开/平开/低开")
    print("- 量能突击、盘口异动、形态突破不再一直显示❌")
    print("- 低开反转的股票能被正确识别和分析")
    print("- 买入建议不再总是'观察中'")
    
    print("\n🏁 测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
