#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试负样本创建列名修复
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from training.training_pipeline import TrainingPipeline
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_negative_sample_creation():
    """测试负样本创建"""
    print("🧪 测试负样本创建...")
    
    # 创建训练流程和存储器
    pipeline = TrainingPipeline()
    storage = HistoricalDataStorage()
    
    # 测试股票
    test_stock = "000001"  # 平安银行
    
    print(f"📊 测试股票: {test_stock}")
    
    try:
        # 1. 检查数据库当前状态
        print("\n1️⃣ 检查数据库当前状态...")
        import sqlite3
        conn = sqlite3.connect('historical_data.db')
        
        # 检查K线数据
        kline_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
        print(f"   {test_stock}的K线数据: {kline_count}条")
        
        # 检查特征数据
        feature_count = conn.execute('SELECT COUNT(*) FROM stock_features WHERE stock_code = ?', (test_stock,)).fetchone()[0]
        print(f"   {test_stock}的特征数据: {feature_count}条")
        
        # 检查标签分布
        if feature_count > 0:
            label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features WHERE stock_code = ? GROUP BY label', conn, params=[test_stock])
            print(f"   标签分布:\n{label_dist}")
        
        conn.close()
        
        # 2. 获取K线数据
        print("\n2️⃣ 获取K线数据...")
        test_date = datetime(2025, 1, 29)
        kline_data = pipeline._get_historical_kline_before_date(test_stock, test_date, 30)
        
        if not kline_data.empty:
            print(f"✅ 获取K线数据成功: {len(kline_data)}条")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   样本数据:\n{kline_data.head(3)}")
            
            # 3. 查询存储的K线数据
            print("\n3️⃣ 查询存储的K线数据...")
            start_date = (test_date - timedelta(days=30)).strftime('%Y-%m-%d')
            end_date = test_date.strftime('%Y-%m-%d')
            
            stored_kline = storage.get_kline_data(test_stock, start_date=start_date, end_date=end_date)
            
            if not stored_kline.empty:
                print(f"✅ 查询K线数据成功: {len(stored_kline)}条")
                print(f"   数据列: {list(stored_kline.columns)}")
                print(f"   样本数据:\n{stored_kline.head(3)}")
                
                # 4. 测试负样本创建
                print("\n4️⃣ 测试负样本创建...")
                
                # 模拟基础特征
                base_features = {
                    'ma5': 10.5,
                    'ma10': 10.3,
                    'ma20': 10.1,
                    'rsi': 65.2,
                    'volume_ratio': 1.5
                }
                
                # 模拟涨停日期
                limit_dates = ['2025-01-15', '2025-01-20']
                
                # 创建负样本
                pipeline._create_negative_samples(test_stock, stored_kline, limit_dates, base_features)
                
                # 5. 检查负样本创建结果
                print("\n5️⃣ 检查负样本创建结果...")
                conn = sqlite3.connect('historical_data.db')
                
                # 检查新的特征数据
                new_feature_count = conn.execute('SELECT COUNT(*) FROM stock_features WHERE stock_code = ?', (test_stock,)).fetchone()[0]
                print(f"   {test_stock}现在的特征数据: {new_feature_count}条")
                print(f"   新增特征数据: {new_feature_count - feature_count}条")
                
                # 检查新的标签分布
                new_label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features WHERE stock_code = ? GROUP BY label', conn, params=[test_stock])
                print(f"   新的标签分布:\n{new_label_dist}")
                
                conn.close()
                
                if new_feature_count > feature_count:
                    print("✅ 负样本创建成功！")
                    return True
                else:
                    print("❌ 负样本创建失败，没有新增特征数据")
                    return False
            else:
                print("❌ 查询K线数据失败")
                return False
        else:
            print("❌ 获取K线数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_column_compatibility():
    """测试列名兼容性"""
    print("\n🧪 测试列名兼容性...")
    
    pipeline = TrainingPipeline()
    
    # 创建测试数据，模拟不同的列名情况
    test_cases = [
        {
            "name": "trade_date列（数据库格式）",
            "data": pd.DataFrame({
                'trade_date': ['2025-01-15', '2025-01-16', '2025-01-17', '2025-01-18', '2025-01-19'],
                'close_price': [10.1, 10.2, 10.3, 10.4, 10.5]
            })
        },
        {
            "name": "date列（efinance格式）",
            "data": pd.DataFrame({
                'date': pd.to_datetime(['2025-01-15', '2025-01-16', '2025-01-17', '2025-01-18', '2025-01-19']),
                'close_price': [10.1, 10.2, 10.3, 10.4, 10.5]
            })
        },
        {
            "name": "无日期列",
            "data": pd.DataFrame({
                'close_price': [10.1, 10.2, 10.3, 10.4, 10.5]
            })
        }
    ]
    
    base_features = {'test_feature': 1.0}
    limit_dates = ['2025-01-15']
    
    results = {}
    
    for test_case in test_cases:
        print(f"\n📊 测试: {test_case['name']}")
        
        try:
            # 创建负样本
            pipeline._create_negative_samples("TEST", test_case['data'], limit_dates, base_features)
            results[test_case['name']] = "成功"
            print(f"✅ 测试成功")
        except Exception as e:
            results[test_case['name']] = f"失败: {str(e)}"
            print(f"❌ 测试失败: {e}")
    
    # 打印结果汇总
    print("\n📋 列名兼容性测试结果:")
    for test_name, result in results.items():
        print(f"   {test_name}: {result}")
    
    success_count = sum(1 for result in results.values() if result == "成功")
    return success_count, len(results)

def main():
    """主函数"""
    print("🔧 负样本创建列名修复测试开始...")
    print("="*60)
    
    # 测试负样本创建
    creation_result = test_negative_sample_creation()
    
    # 测试列名兼容性
    success_count, total_count = test_column_compatibility()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if creation_result:
        print("✅ 负样本创建功能正常")
    else:
        print("❌ 负样本创建功能异常")
    
    print(f"📊 列名兼容性测试: {success_count}/{total_count} 成功")
    
    if creation_result and success_count >= 2:  # 至少支持两种主要格式
        print("🎉 修复成功！现在可以重新运行完整训练")
        print("💡 预期结果:")
        print("   - 负样本创建不再报错")
        print("   - 能够正确处理trade_date和date列名")
        print("   - 负样本会被成功创建")
        print("   - 模型训练会正常进行")
    else:
        print("❌ 修复可能不完整，需要进一步调试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
