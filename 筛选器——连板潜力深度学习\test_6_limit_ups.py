#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试6次涨停筛选标准
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_new_limit_up_threshold():
    """测试新的涨停次数阈值"""
    print("🔍 测试新的涨停次数阈值（6次）...")
    
    try:
        from investigation.limit_up_analyzer import LimitUpAnalyzer
        
        # 创建分析器
        analyzer = LimitUpAnalyzer()
        
        # 加载统计数据
        print("📊 加载涨停统计数据...")
        stats = analyzer.load_annual_limit_up_stats(days_back=365)
        
        if not stats:
            print("❌ 无法加载涨停数据")
            return False
        
        # 获取统计摘要
        summary = analyzer.get_statistics_summary()
        
        print(f"\n📈 涨停统计摘要:")
        print(f"  总股票数: {summary.get('total_stocks', 0)}")
        print(f"  总涨停次数: {summary.get('total_limit_ups', 0)}")
        print(f"  涨停≥3次股票: {summary.get('stocks_with_3_plus', 0)}")
        print(f"  涨停≥5次股票: {summary.get('stocks_with_5_plus', 0)}")
        print(f"  涨停≥10次股票: {summary.get('stocks_with_10_plus', 0)}")
        
        # 测试不同阈值的效果
        thresholds = [3, 5, 6, 8, 10]
        
        print(f"\n🎯 不同涨停次数阈值的筛选效果:")
        for threshold in thresholds:
            qualified_stocks = analyzer.get_stocks_with_min_limit_ups(min_count=threshold)
            print(f"  涨停≥{threshold}次: {len(qualified_stocks)}只股票")
        
        # 重点测试6次阈值
        print(f"\n🔍 详细分析涨停≥6次的股票:")
        qualified_6_stocks = analyzer.get_stocks_with_min_limit_ups(min_count=6)
        
        print(f"✅ 符合6次涨停要求的股票: {len(qualified_6_stocks)}只")
        
        # 显示前20只股票
        if qualified_6_stocks:
            sorted_stocks = sorted(
                [(code, stats[code]) for code in qualified_6_stocks],
                key=lambda x: x[1], reverse=True
            )
            
            print(f"\n📋 涨停次数最多的前20只股票:")
            for i, (code, count) in enumerate(sorted_stocks[:20], 1):
                name = analyzer.get_stock_name_by_code(code)
                print(f"   {i:2d}. {code} {name}: {count}次")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_investigator_config():
    """测试调查器的新配置"""
    print("\n🔍 测试潜在股调查器的新配置...")
    
    try:
        from investigation.potential_stock_investigator import PotentialStockInvestigator
        
        # 创建调查器
        investigator = PotentialStockInvestigator()
        
        print(f"📊 调查器配置:")
        print(f"  最小涨停次数: {investigator.config['min_limit_up_count']}")
        print(f"  回溯天数: {investigator.config['limit_up_days_back']}")
        print(f"  市值范围: {investigator.config['market_cap_min']}-{investigator.config['market_cap_max']}亿")
        print(f"  最大处理股票数: {investigator.config['max_stocks_to_process']}")
        
        # 验证配置是否正确更新
        if investigator.config['min_limit_up_count'] == 6:
            print("✅ 涨停次数阈值已更新为6次")
            return True
        else:
            print(f"❌ 涨停次数阈值未正确更新: {investigator.config['min_limit_up_count']}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def estimate_filtering_effect():
    """估算筛选效果"""
    print("\n📊 估算新筛选标准的效果...")
    
    try:
        from investigation.limit_up_analyzer import LimitUpAnalyzer
        
        analyzer = LimitUpAnalyzer()
        
        # 获取统计数据
        stats = analyzer.load_annual_limit_up_stats(days_back=365)
        summary = analyzer.get_statistics_summary()
        
        # 估算筛选效果
        total_market = 5417  # 全市场股票数
        basic_filtered = 1658  # 基础筛选通过数（从日志中获取）
        
        # 不同阈值下的涨停筛选结果
        limit_up_3 = summary.get('stocks_with_3_plus', 0)
        limit_up_5 = summary.get('stocks_with_5_plus', 0)
        limit_up_10 = summary.get('stocks_with_10_plus', 0)
        
        # 估算6次涨停的股票数量（在5次和10次之间）
        limit_up_6 = analyzer.get_stocks_with_min_limit_ups(min_count=6)
        limit_up_6_count = len(limit_up_6)
        
        print(f"📈 筛选效果估算:")
        print(f"  全市场股票: {total_market}只")
        print(f"  基础筛选后: {basic_filtered}只 ({basic_filtered/total_market*100:.1f}%)")
        print(f"  涨停≥3次筛选后: 约{int(basic_filtered * limit_up_3 / 4000)}只")
        print(f"  涨停≥6次筛选后: 约{int(basic_filtered * limit_up_6_count / 4000)}只")
        print(f"  涨停≥10次筛选后: 约{int(basic_filtered * limit_up_10 / 4000)}只")
        
        # 估算最终处理的股票数量
        estimated_final = int(basic_filtered * limit_up_6_count / 4000)
        
        print(f"\n🎯 预期效果:")
        print(f"  6次涨停阈值将筛选出约 {estimated_final} 只高质量候选股票")
        print(f"  相比3次阈值，筛选更加严格，质量更高")
        print(f"  仍有足够的股票数量进行深度分析")
        
        return True
        
    except Exception as e:
        print(f"❌ 估算失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 测试6次涨停筛选标准...")
    print("="*60)
    
    # 测试涨停数据分析
    analyzer_ok = test_new_limit_up_threshold()
    
    # 测试调查器配置
    config_ok = test_investigator_config()
    
    # 估算筛选效果
    estimate_ok = estimate_filtering_effect()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if analyzer_ok and config_ok and estimate_ok:
        print("✅ 6次涨停筛选标准配置成功")
        print("✅ 预期能显著提高候选股票质量")
        print("✅ 筛选后的股票数量仍然充足")
        print("\n💡 建议：现在可以重新运行潜在股调查")
    else:
        print("❌ 配置或测试存在问题")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
