#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场情绪数据收集模块
专门收集市场情绪相关的详细数据，导出到Excel供AI分析
不进行内置分析，只负责数据收集和整理
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketSentimentDataCollector:
    """市场情绪数据收集器"""
    
    def __init__(self):
        self.data_days = 30  # 收集30天的数据
        self.index_codes = ['000001', '399001', '399006']  # 上证、深证、创业板
        
    def get_market_sentiment_data(self, stock_code: str) -> Dict:
        """
        收集完整的市场情绪数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 包含各类市场情绪数据
        """
        try:
            logger.info(f"开始收集{stock_code}的市场情绪数据")
            
            result = {
                'data_available': True,
                'collection_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'target_stock': stock_code,
                'market_indices_data': pd.DataFrame(),
                'market_breadth_data': pd.DataFrame(),
                'volatility_data': pd.DataFrame(),
                'volume_sentiment_data': pd.DataFrame(),
                'sector_sentiment_data': pd.DataFrame(),
                'fear_greed_indicators': pd.DataFrame(),
                'market_momentum_data': pd.DataFrame(),
                'sentiment_summary': {}
            }
            
            # 1. 收集市场指数数据
            result['market_indices_data'] = self._collect_market_indices_data()
            
            # 2. 收集市场广度数据
            result['market_breadth_data'] = self._collect_market_breadth_data()
            
            # 3. 收集波动率数据
            result['volatility_data'] = self._collect_volatility_data()
            
            # 4. 收集成交量情绪数据
            result['volume_sentiment_data'] = self._collect_volume_sentiment_data()
            
            # 5. 收集板块情绪数据
            result['sector_sentiment_data'] = self._collect_sector_sentiment_data()
            
            # 6. 收集恐慌贪婪指标
            result['fear_greed_indicators'] = self._collect_fear_greed_indicators()
            
            # 7. 收集市场动量数据
            result['market_momentum_data'] = self._collect_market_momentum_data()
            
            # 8. 生成数据摘要
            result['sentiment_summary'] = self._generate_data_summary(result)
            
            logger.info(f"市场情绪数据收集完成")
            return result
            
        except Exception as e:
            logger.error(f"市场情绪数据收集失败: {e}")
            return {
                'data_available': False,
                'error': str(e),
                'collection_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
    
    def _collect_market_indices_data(self) -> pd.DataFrame:
        """收集主要市场指数数据"""
        try:
            logger.info("收集市场指数数据")
            
            indices_data = []
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=self.data_days)).strftime('%Y%m%d')
            
            for index_code in self.index_codes:
                try:
                    # 获取指数历史数据
                    index_data = ak.stock_zh_index_daily(symbol=f"sh{index_code}")
                    
                    if not index_data.empty:
                        # 检查并处理日期列
                        date_column = None
                        for col in ['date', '日期', 'Date', 'DATE']:
                            if col in index_data.columns:
                                date_column = col
                                break

                        if date_column is None:
                            # 如果没有日期列，使用索引
                            index_data = index_data.reset_index()
                            if 'date' in index_data.columns:
                                date_column = 'date'
                            else:
                                logger.warning(f"指数{index_code}数据缺少日期列")
                                continue

                        # 筛选时间范围
                        index_data['date'] = pd.to_datetime(index_data[date_column], errors='coerce')
                        mask = (index_data['date'] >= pd.to_datetime(start_date)) & \
                               (index_data['date'] <= pd.to_datetime(end_date))
                        recent_data = index_data[mask].copy()
                        
                        if not recent_data.empty:
                            recent_data['index_code'] = index_code
                            recent_data['index_name'] = self._get_index_name(index_code)
                            indices_data.append(recent_data)
                            
                except Exception as e:
                    logger.warning(f"获取指数{index_code}数据失败: {e}")
                    continue
            
            if indices_data:
                combined_data = pd.concat(indices_data, ignore_index=True)
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"收集市场指数数据失败: {e}")
            return pd.DataFrame()
    
    def _collect_market_breadth_data(self) -> pd.DataFrame:
        """收集市场广度数据（涨跌家数等）"""
        try:
            logger.info("收集市场广度数据")
            
            # 获取A股涨跌统计
            try:
                market_breadth = ak.stock_zh_a_gdhs()
                if not market_breadth.empty:
                    # 添加时间戳
                    market_breadth['collection_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    return market_breadth
            except Exception as e:
                logger.warning(f"获取市场广度数据失败: {e}")
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"收集市场广度数据失败: {e}")
            return pd.DataFrame()
    
    def _collect_volatility_data(self) -> pd.DataFrame:
        """收集波动率数据"""
        try:
            logger.info("收集波动率数据")
            
            volatility_data = []
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=self.data_days)).strftime('%Y%m%d')
            
            for index_code in self.index_codes:
                try:
                    # 获取指数数据计算波动率
                    index_data = ak.stock_zh_index_daily(symbol=f"sh{index_code}")
                    
                    if not index_data.empty:
                        # 检查并处理日期列
                        date_column = None
                        for col in ['date', '日期', 'Date', 'DATE']:
                            if col in index_data.columns:
                                date_column = col
                                break

                        if date_column is None:
                            index_data = index_data.reset_index()
                            if 'date' in index_data.columns:
                                date_column = 'date'
                            else:
                                logger.warning(f"指数{index_code}波动率数据缺少日期列")
                                continue

                        index_data['date'] = pd.to_datetime(index_data[date_column], errors='coerce')
                        mask = (index_data['date'] >= pd.to_datetime(start_date)) & \
                               (index_data['date'] <= pd.to_datetime(end_date))
                        recent_data = index_data[mask].copy()
                        
                        if len(recent_data) > 1:
                            # 计算日收益率
                            recent_data['daily_return'] = recent_data['close'].pct_change()
                            
                            # 计算滚动波动率
                            recent_data['volatility_5d'] = recent_data['daily_return'].rolling(5).std() * np.sqrt(252)
                            recent_data['volatility_10d'] = recent_data['daily_return'].rolling(10).std() * np.sqrt(252)
                            recent_data['volatility_20d'] = recent_data['daily_return'].rolling(20).std() * np.sqrt(252)
                            
                            recent_data['index_code'] = index_code
                            recent_data['index_name'] = self._get_index_name(index_code)
                            volatility_data.append(recent_data)
                            
                except Exception as e:
                    logger.warning(f"计算指数{index_code}波动率失败: {e}")
                    continue
            
            if volatility_data:
                combined_data = pd.concat(volatility_data, ignore_index=True)
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"收集波动率数据失败: {e}")
            return pd.DataFrame()
    
    def _collect_volume_sentiment_data(self) -> pd.DataFrame:
        """收集成交量情绪数据"""
        try:
            logger.info("收集成交量情绪数据")
            
            # 获取两市成交额数据
            try:
                volume_data = ak.stock_zh_a_hist_min_em(symbol="000001", period="daily",
                                                       start_date=(datetime.now() - timedelta(days=self.data_days)).strftime('%Y%m%d'),
                                                       end_date=datetime.now().strftime('%Y%m%d'))

                if volume_data is not None and not volume_data.empty:
                    # 检查成交量列名
                    volume_column = None
                    for col in ['成交量', 'volume', 'Volume', 'VOLUME', '成交量(股)']:
                        if col in volume_data.columns:
                            volume_column = col
                            break

                    if volume_column is not None:
                        # 计算成交量相关指标
                        volume_data['volume_ma5'] = volume_data[volume_column].rolling(5).mean()
                        volume_data['volume_ma10'] = volume_data[volume_column].rolling(10).mean()
                        volume_data['volume_ma20'] = volume_data[volume_column].rolling(20).mean()

                        # 计算成交量比率
                        volume_data['volume_ratio_5d'] = volume_data[volume_column] / volume_data['volume_ma5']
                        volume_data['volume_ratio_10d'] = volume_data[volume_column] / volume_data['volume_ma10']

                        return volume_data
                    else:
                        logger.warning("成交量数据缺少成交量列")
                        return pd.DataFrame()
                    
            except Exception as e:
                logger.warning(f"获取成交量数据失败: {e}")
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"收集成交量情绪数据失败: {e}")
            return pd.DataFrame()
    
    def _collect_sector_sentiment_data(self) -> pd.DataFrame:
        """收集板块情绪数据"""
        try:
            logger.info("收集板块情绪数据")
            
            # 获取行业板块数据
            try:
                sector_data = ak.stock_board_industry_name_em()
                if not sector_data.empty:
                    # 添加时间戳
                    sector_data['collection_time'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    # 计算板块情绪指标
                    if '涨跌幅' in sector_data.columns:
                        sector_data['positive_sectors'] = (sector_data['涨跌幅'] > 0).astype(int)
                        sector_data['negative_sectors'] = (sector_data['涨跌幅'] < 0).astype(int)
                    
                    return sector_data
                    
            except Exception as e:
                logger.warning(f"获取板块数据失败: {e}")
            
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"收集板块情绪数据失败: {e}")
            return pd.DataFrame()
    
    def _collect_fear_greed_indicators(self) -> pd.DataFrame:
        """收集恐慌贪婪指标数据"""
        try:
            logger.info("收集恐慌贪婪指标")
            
            indicators_data = []
            
            # 收集各种情绪指标的原始数据
            try:
                # VIX类似指标 - 使用波动率数据
                vix_data = self._calculate_vix_like_indicator()
                if not vix_data.empty:
                    indicators_data.append(vix_data)
                
                # 资金流向指标
                fund_flow_data = self._collect_fund_flow_sentiment()
                if not fund_flow_data.empty:
                    indicators_data.append(fund_flow_data)
                
                # 新高新低指标
                new_high_low_data = self._collect_new_high_low_data()
                if not new_high_low_data.empty:
                    indicators_data.append(new_high_low_data)
                    
            except Exception as e:
                logger.warning(f"收集恐慌贪婪指标失败: {e}")
            
            if indicators_data:
                # 合并所有指标数据
                combined_data = pd.concat(indicators_data, ignore_index=True)
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"收集恐慌贪婪指标失败: {e}")
            return pd.DataFrame()
    
    def _collect_market_momentum_data(self) -> pd.DataFrame:
        """收集市场动量数据"""
        try:
            logger.info("收集市场动量数据")
            
            momentum_data = []
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=self.data_days)).strftime('%Y%m%d')
            
            for index_code in self.index_codes:
                try:
                    # 获取指数数据
                    index_data = ak.stock_zh_index_daily(symbol=f"sh{index_code}")
                    
                    if not index_data.empty:
                        # 检查并处理日期列
                        date_column = None
                        for col in ['date', '日期', 'Date', 'DATE']:
                            if col in index_data.columns:
                                date_column = col
                                break

                        if date_column is None:
                            index_data = index_data.reset_index()
                            if 'date' in index_data.columns:
                                date_column = 'date'
                            else:
                                logger.warning(f"指数{index_code}动量数据缺少日期列")
                                continue

                        index_data['date'] = pd.to_datetime(index_data[date_column], errors='coerce')
                        mask = (index_data['date'] >= pd.to_datetime(start_date)) & \
                               (index_data['date'] <= pd.to_datetime(end_date))
                        recent_data = index_data[mask].copy()
                        
                        if len(recent_data) > 20:
                            # 计算动量指标
                            recent_data['rsi_14'] = self._calculate_rsi(recent_data['close'], 14)
                            recent_data['momentum_10'] = recent_data['close'] / recent_data['close'].shift(10) - 1
                            recent_data['momentum_20'] = recent_data['close'] / recent_data['close'].shift(20) - 1
                            
                            # 计算移动平均线
                            recent_data['ma5'] = recent_data['close'].rolling(5).mean()
                            recent_data['ma10'] = recent_data['close'].rolling(10).mean()
                            recent_data['ma20'] = recent_data['close'].rolling(20).mean()
                            
                            recent_data['index_code'] = index_code
                            recent_data['index_name'] = self._get_index_name(index_code)
                            momentum_data.append(recent_data)
                            
                except Exception as e:
                    logger.warning(f"计算指数{index_code}动量失败: {e}")
                    continue
            
            if momentum_data:
                combined_data = pd.concat(momentum_data, ignore_index=True)
                return combined_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"收集市场动量数据失败: {e}")
            return pd.DataFrame()

    def _get_index_name(self, index_code: str) -> str:
        """获取指数名称"""
        index_names = {
            '000001': '上证指数',
            '399001': '深证成指',
            '399006': '创业板指'
        }
        return index_names.get(index_code, f'指数{index_code}')

    def _calculate_vix_like_indicator(self) -> pd.DataFrame:
        """计算类VIX波动率指标"""
        try:
            # 基于上证指数计算波动率指标
            index_data = ak.stock_zh_index_daily(symbol="sh000001")

            if not index_data.empty and len(index_data) > 20:
                # 检查并处理日期列
                date_column = None
                for col in ['date', '日期', 'Date', 'DATE']:
                    if col in index_data.columns:
                        date_column = col
                        break

                if date_column is None:
                    index_data = index_data.reset_index()
                    if 'date' in index_data.columns:
                        date_column = 'date'
                    else:
                        logger.warning("VIX指标数据缺少日期列")
                        return pd.DataFrame()

                index_data['date'] = pd.to_datetime(index_data[date_column], errors='coerce')
                index_data = index_data.tail(30)  # 最近30天

                # 计算日收益率
                index_data['daily_return'] = index_data['close'].pct_change()

                # 计算滚动波动率作为VIX类似指标
                index_data['vix_like'] = index_data['daily_return'].rolling(20).std() * np.sqrt(252) * 100

                # 创建VIX数据格式
                vix_data = pd.DataFrame({
                    'date': index_data['date'],
                    'indicator_type': 'VIX_LIKE',
                    'indicator_name': '类VIX波动率指标',
                    'value': index_data['vix_like'],
                    'description': '基于上证指数20日滚动波动率计算'
                })

                return vix_data.dropna()

        except Exception as e:
            logger.warning(f"计算VIX类似指标失败: {e}")

        return pd.DataFrame()

    def _collect_fund_flow_sentiment(self) -> pd.DataFrame:
        """收集资金流向情绪数据"""
        try:
            # 获取沪深港通资金流向
            fund_flow_data = ak.stock_hsgt_fund_flow_summary_em()

            if not fund_flow_data.empty:
                # 转换为情绪指标格式
                sentiment_data = pd.DataFrame({
                    'date': [datetime.now().strftime('%Y-%m-%d')],
                    'indicator_type': 'FUND_FLOW',
                    'indicator_name': '资金流向情绪',
                    'value': fund_flow_data.iloc[0].get('沪股通净流入', 0) if not fund_flow_data.empty else 0,
                    'description': '沪深港通资金流向数据'
                })

                return sentiment_data

        except Exception as e:
            logger.warning(f"收集资金流向情绪失败: {e}")

        return pd.DataFrame()

    def _collect_new_high_low_data(self) -> pd.DataFrame:
        """收集新高新低数据"""
        try:
            # 获取A股新高新低统计
            new_high_low = ak.stock_zh_a_new()

            if not new_high_low.empty:
                # 转换为情绪指标格式
                sentiment_data = pd.DataFrame({
                    'date': [datetime.now().strftime('%Y-%m-%d')],
                    'indicator_type': 'NEW_HIGH_LOW',
                    'indicator_name': '新高新低比率',
                    'value': len(new_high_low),  # 新高新低股票总数
                    'description': 'A股新高新低统计数据'
                })

                return sentiment_data

        except Exception as e:
            logger.warning(f"收集新高新低数据失败: {e}")

        return pd.DataFrame()

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi
        except:
            return pd.Series([np.nan] * len(prices))

    def _generate_data_summary(self, result: Dict) -> Dict:
        """生成数据收集摘要"""
        try:
            summary = {
                'collection_time': result['collection_time'],
                'target_stock': result['target_stock'],
                'data_completeness': {},
                'data_counts': {},
                'data_date_ranges': {},
                'collection_status': 'success'
            }

            # 统计各类数据的完整性
            data_types = [
                'market_indices_data',
                'market_breadth_data',
                'volatility_data',
                'volume_sentiment_data',
                'sector_sentiment_data',
                'fear_greed_indicators',
                'market_momentum_data'
            ]

            for data_type in data_types:
                data = result.get(data_type, pd.DataFrame())
                if isinstance(data, pd.DataFrame) and not data.empty:
                    summary['data_completeness'][data_type] = True
                    summary['data_counts'][data_type] = len(data)

                    # 尝试获取日期范围
                    date_columns = [col for col in data.columns if 'date' in col.lower() or 'time' in col.lower()]
                    if date_columns:
                        try:
                            date_col = date_columns[0]
                            if data[date_col].dtype == 'object':
                                dates = pd.to_datetime(data[date_col], errors='coerce')
                            else:
                                dates = data[date_col]

                            valid_dates = dates.dropna()
                            if not valid_dates.empty:
                                summary['data_date_ranges'][data_type] = {
                                    'start_date': valid_dates.min().strftime('%Y-%m-%d'),
                                    'end_date': valid_dates.max().strftime('%Y-%m-%d')
                                }
                        except:
                            pass
                else:
                    summary['data_completeness'][data_type] = False
                    summary['data_counts'][data_type] = 0

            # 计算总体完整性
            total_types = len(data_types)
            completed_types = sum(summary['data_completeness'].values())
            summary['overall_completeness'] = f"{completed_types}/{total_types}"
            summary['completeness_percentage'] = round(completed_types / total_types * 100, 1)

            return summary

        except Exception as e:
            logger.error(f"生成数据摘要失败: {e}")
            return {
                'collection_time': result.get('collection_time', ''),
                'target_stock': result.get('target_stock', ''),
                'collection_status': 'error',
                'error': str(e)
            }


def test_market_sentiment_collector():
    """测试市场情绪数据收集器"""
    print("🚀 测试市场情绪数据收集器")
    print("=" * 60)

    collector = MarketSentimentDataCollector()

    # 测试股票列表
    test_stocks = ["000001", "601519", "300229"]

    for stock_code in test_stocks:
        print(f"\n📊 测试股票: {stock_code}")
        print("-" * 40)

        try:
            # 收集市场情绪数据
            sentiment_data = collector.get_market_sentiment_data(stock_code)

            if sentiment_data['data_available']:
                print("✅ 市场情绪数据收集成功")

                # 显示数据摘要
                summary = sentiment_data['sentiment_summary']
                print(f"📋 数据完整性: {summary.get('overall_completeness', 'N/A')}")
                print(f"📊 完整性百分比: {summary.get('completeness_percentage', 0)}%")

                # 显示各类数据统计
                data_counts = summary.get('data_counts', {})
                for data_type, count in data_counts.items():
                    if count > 0:
                        print(f"  • {data_type}: {count}条记录")

                # 显示日期范围
                date_ranges = summary.get('data_date_ranges', {})
                if date_ranges:
                    print("📅 数据日期范围:")
                    for data_type, date_range in date_ranges.items():
                        print(f"  • {data_type}: {date_range['start_date']} 到 {date_range['end_date']}")

            else:
                print(f"❌ 数据收集失败: {sentiment_data.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ 测试失败: {e}")

    print("\n" + "=" * 60)
    print("🎯 市场情绪数据收集器测试完成")


if __name__ == "__main__":
    test_market_sentiment_collector()
