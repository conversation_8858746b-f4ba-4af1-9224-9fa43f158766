#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场情绪分析器
开发市场情绪指标，包括VIX指数、涨跌停比例、成交量变化等
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import akshare as ak
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MarketSentimentAnalyzer:
    """市场情绪分析器"""
    
    def __init__(self):
        self.akshare_available = self._check_akshare_availability()
        self.data_cache = {}  # 数据缓存
        self.cache_timeout = 900  # 缓存15分钟
        
        # 情绪指标配置
        self.sentiment_indicators = {
            'limit_up_down_ratio': {
                'name': '涨跌停比例',
                'weight': 0.25,
                'description': '涨停与跌停股票数量比例，反映市场强弱'
            },
            'volume_sentiment': {
                'name': '成交量情绪',
                'weight': 0.2,
                'description': '成交量变化趋势，反映市场活跃度'
            },
            'market_breadth': {
                'name': '市场广度',
                'weight': 0.2,
                'description': '上涨股票占比，反映市场参与度'
            },
            'volatility_index': {
                'name': '波动率指数',
                'weight': 0.15,
                'description': '市场波动程度，反映恐慌或贪婪情绪'
            },
            'money_flow_sentiment': {
                'name': '资金流向情绪',
                'weight': 0.2,
                'description': '主力资金净流入流出，反映机构态度'
            }
        }
        
        # 情绪阈值配置
        self.sentiment_thresholds = {
            'extreme_fear': 0.2,      # 极度恐慌
            'fear': 0.4,              # 恐慌
            'neutral': 0.6,           # 中性
            'greed': 0.8,             # 贪婪
            'extreme_greed': 1.0      # 极度贪婪
        }
        
        logger.info("📊 市场情绪分析器初始化完成")
    
    def _check_akshare_availability(self) -> bool:
        """检查AKShare可用性"""
        try:
            import akshare as ak
            # 简单测试
            ak.stock_zh_a_spot_em()
            return True
        except Exception as e:
            logger.warning(f"AKShare不可用: {e}")
            return False
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (datetime.now().timestamp() - cache_time) < self.cache_timeout
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now().timestamp()
        }
    
    def _get_cache(self, cache_key: str) -> Any:
        """获取缓存数据"""
        return self.data_cache[cache_key]['data']
    
    def get_limit_up_down_sentiment(self) -> Dict[str, Any]:
        """获取涨跌停情绪指标"""
        cache_key = 'limit_up_down_sentiment'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_sentiment_data('limit_up_down')
            
            # 获取涨停股票数据
            limit_up_stocks = ak.stock_zh_a_st_em(symbol="涨停")
            limit_down_stocks = ak.stock_zh_a_st_em(symbol="跌停")
            
            limit_up_count = len(limit_up_stocks) if not limit_up_stocks.empty else 0
            limit_down_count = len(limit_down_stocks) if not limit_down_stocks.empty else 0
            
            # 计算涨跌停比例
            total_limit = limit_up_count + limit_down_count
            if total_limit > 0:
                limit_ratio = limit_up_count / total_limit
            else:
                limit_ratio = 0.5  # 中性
            
            # 计算情绪评分
            sentiment_score = self._calculate_limit_sentiment_score(limit_up_count, limit_down_count)
            
            result = {
                'indicator': 'limit_up_down_ratio',
                'limit_up_count': limit_up_count,
                'limit_down_count': limit_down_count,
                'limit_ratio': limit_ratio,
                'sentiment_score': sentiment_score,
                'sentiment_level': self._get_sentiment_level(sentiment_score),
                'description': self._generate_limit_description(limit_up_count, limit_down_count),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 涨跌停情绪获取成功: 涨停{limit_up_count}只, 跌停{limit_down_count}只")
            return result
            
        except Exception as e:
            logger.error(f"获取涨跌停情绪失败: {e}")
            return self._get_empty_sentiment_data('limit_up_down')
    
    def get_volume_sentiment(self) -> Dict[str, Any]:
        """获取成交量情绪指标"""
        cache_key = 'volume_sentiment'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_sentiment_data('volume')
            
            # 获取市场成交量数据
            market_data = ak.stock_zh_a_spot_em()
            
            if market_data.empty:
                return self._get_empty_sentiment_data('volume')
            
            # 计算成交量指标
            total_volume = market_data['成交额'].sum() if '成交额' in market_data.columns else 0
            avg_turnover = market_data['换手率'].mean() if '换手率' in market_data.columns else 0
            
            # 获取历史成交量进行对比（简化实现）
            volume_ratio = self._calculate_volume_ratio(total_volume)
            
            # 计算成交量情绪评分
            sentiment_score = self._calculate_volume_sentiment_score(volume_ratio, avg_turnover)
            
            result = {
                'indicator': 'volume_sentiment',
                'total_volume': float(total_volume),
                'avg_turnover': float(avg_turnover),
                'volume_ratio': volume_ratio,
                'sentiment_score': sentiment_score,
                'sentiment_level': self._get_sentiment_level(sentiment_score),
                'description': self._generate_volume_description(volume_ratio, avg_turnover),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 成交量情绪获取成功: 成交额{total_volume/1e8:.1f}亿, 平均换手{avg_turnover:.2f}%")
            return result
            
        except Exception as e:
            logger.error(f"获取成交量情绪失败: {e}")
            return self._get_empty_sentiment_data('volume')
    
    def get_market_breadth_sentiment(self) -> Dict[str, Any]:
        """获取市场广度情绪指标"""
        cache_key = 'market_breadth_sentiment'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_sentiment_data('market_breadth')
            
            # 获取全市场股票数据
            market_data = ak.stock_zh_a_spot_em()
            
            if market_data.empty or '涨跌幅' not in market_data.columns:
                return self._get_empty_sentiment_data('market_breadth')
            
            # 计算市场广度指标
            total_stocks = len(market_data)
            rising_stocks = len(market_data[market_data['涨跌幅'] > 0])
            falling_stocks = len(market_data[market_data['涨跌幅'] < 0])
            unchanged_stocks = total_stocks - rising_stocks - falling_stocks
            
            # 计算上涨股票比例
            rising_ratio = rising_stocks / total_stocks if total_stocks > 0 else 0
            
            # 计算市场广度情绪评分
            sentiment_score = self._calculate_breadth_sentiment_score(rising_ratio)
            
            result = {
                'indicator': 'market_breadth',
                'total_stocks': total_stocks,
                'rising_stocks': rising_stocks,
                'falling_stocks': falling_stocks,
                'unchanged_stocks': unchanged_stocks,
                'rising_ratio': rising_ratio,
                'sentiment_score': sentiment_score,
                'sentiment_level': self._get_sentiment_level(sentiment_score),
                'description': self._generate_breadth_description(rising_ratio, rising_stocks, total_stocks),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 市场广度获取成功: {rising_stocks}/{total_stocks}只股票上涨({rising_ratio:.1%})")
            return result
            
        except Exception as e:
            logger.error(f"获取市场广度失败: {e}")
            return self._get_empty_sentiment_data('market_breadth')
    
    def get_volatility_sentiment(self) -> Dict[str, Any]:
        """获取波动率情绪指标"""
        cache_key = 'volatility_sentiment'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_sentiment_data('volatility')
            
            # 获取主要指数数据计算波动率
            index_data = self._get_index_volatility()
            
            if not index_data:
                return self._get_empty_sentiment_data('volatility')
            
            # 计算波动率情绪评分
            sentiment_score = self._calculate_volatility_sentiment_score(index_data['volatility'])
            
            result = {
                'indicator': 'volatility_index',
                'volatility': index_data['volatility'],
                'index_change': index_data['change_pct'],
                'sentiment_score': sentiment_score,
                'sentiment_level': self._get_sentiment_level(sentiment_score),
                'description': self._generate_volatility_description(index_data['volatility']),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 波动率情绪获取成功: 波动率{index_data['volatility']:.2f}%")
            return result
            
        except Exception as e:
            logger.error(f"获取波动率情绪失败: {e}")
            return self._get_empty_sentiment_data('volatility')

    def _calculate_limit_sentiment_score(self, limit_up_count: int, limit_down_count: int) -> float:
        """计算涨跌停情绪评分"""
        try:
            total_limit = limit_up_count + limit_down_count

            if total_limit == 0:
                return 0.5  # 中性

            # 基础比例评分
            ratio_score = limit_up_count / total_limit

            # 考虑绝对数量的影响
            if limit_up_count > 50:  # 大量涨停，极度乐观
                ratio_score = min(ratio_score + 0.2, 1.0)
            elif limit_down_count > 50:  # 大量跌停，极度悲观
                ratio_score = max(ratio_score - 0.2, 0.0)

            return ratio_score

        except Exception as e:
            logger.error(f"计算涨跌停情绪评分失败: {e}")
            return 0.5

    def _calculate_volume_ratio(self, current_volume: float) -> float:
        """计算成交量比率（简化实现）"""
        try:
            # 简化实现：假设平均成交量
            # 实际应用中应该获取历史数据进行对比
            avg_volume = 1e12  # 假设平均成交额1万亿

            if avg_volume > 0:
                return current_volume / avg_volume
            else:
                return 1.0

        except Exception as e:
            logger.error(f"计算成交量比率失败: {e}")
            return 1.0

    def _calculate_volume_sentiment_score(self, volume_ratio: float, avg_turnover: float) -> float:
        """计算成交量情绪评分"""
        try:
            # 成交量放大评分
            volume_score = min(volume_ratio / 2.0, 1.0)  # 成交量翻倍为满分

            # 换手率评分
            turnover_score = min(avg_turnover / 5.0, 1.0)  # 5%换手率为满分

            # 综合评分
            sentiment_score = volume_score * 0.6 + turnover_score * 0.4

            return max(0, min(sentiment_score, 1.0))

        except Exception as e:
            logger.error(f"计算成交量情绪评分失败: {e}")
            return 0.5

    def _calculate_breadth_sentiment_score(self, rising_ratio: float) -> float:
        """计算市场广度情绪评分"""
        try:
            # 直接使用上涨股票比例作为基础评分
            # 50%为中性，80%以上为极度乐观，20%以下为极度悲观
            if rising_ratio > 0.8:
                return 0.9 + (rising_ratio - 0.8) * 0.5  # 0.9-1.0
            elif rising_ratio > 0.6:
                return 0.6 + (rising_ratio - 0.6) * 1.5  # 0.6-0.9
            elif rising_ratio > 0.4:
                return 0.4 + (rising_ratio - 0.4) * 1.0  # 0.4-0.6
            elif rising_ratio > 0.2:
                return 0.1 + (rising_ratio - 0.2) * 1.5  # 0.1-0.4
            else:
                return rising_ratio * 0.5  # 0-0.1

        except Exception as e:
            logger.error(f"计算市场广度情绪评分失败: {e}")
            return 0.5

    def _get_index_volatility(self) -> Dict[str, float]:
        """获取指数波动率数据"""
        try:
            # 获取上证指数数据
            sh_index = ak.stock_zh_index_spot_em(symbol="sh000001")

            if sh_index.empty:
                return {}

            # 简化波动率计算
            change_pct = float(sh_index.iloc[0].get('涨跌幅', 0))
            volatility = abs(change_pct)  # 简化：使用当日涨跌幅绝对值

            return {
                'volatility': volatility,
                'change_pct': change_pct
            }

        except Exception as e:
            logger.error(f"获取指数波动率失败: {e}")
            return {}

    def _calculate_volatility_sentiment_score(self, volatility: float) -> float:
        """计算波动率情绪评分"""
        try:
            # 波动率越高，情绪越极端（恐慌或贪婪）
            # 这里简化处理：低波动率为中性，高波动率为极端
            if volatility < 1.0:  # 低波动
                return 0.5 + volatility * 0.1  # 0.5-0.6
            elif volatility < 3.0:  # 中等波动
                return 0.6 + (volatility - 1.0) * 0.15  # 0.6-0.9
            else:  # 高波动
                return min(0.9 + (volatility - 3.0) * 0.05, 1.0)  # 0.9-1.0

        except Exception as e:
            logger.error(f"计算波动率情绪评分失败: {e}")
            return 0.5

    def _get_sentiment_level(self, score: float) -> str:
        """根据评分获取情绪等级"""
        if score >= self.sentiment_thresholds['extreme_greed']:
            return 'extreme_greed'
        elif score >= self.sentiment_thresholds['greed']:
            return 'greed'
        elif score >= self.sentiment_thresholds['neutral']:
            return 'neutral'
        elif score >= self.sentiment_thresholds['fear']:
            return 'fear'
        else:
            return 'extreme_fear'

    def _generate_limit_description(self, limit_up: int, limit_down: int) -> str:
        """生成涨跌停描述"""
        if limit_up > limit_down * 2:
            return f"市场情绪乐观，涨停股{limit_up}只远超跌停股{limit_down}只"
        elif limit_down > limit_up * 2:
            return f"市场情绪悲观，跌停股{limit_down}只远超涨停股{limit_up}只"
        else:
            return f"市场情绪相对平衡，涨停{limit_up}只，跌停{limit_down}只"

    def _generate_volume_description(self, volume_ratio: float, avg_turnover: float) -> str:
        """生成成交量描述"""
        if volume_ratio > 1.5:
            return f"成交量大幅放大{volume_ratio:.1f}倍，市场活跃度高"
        elif volume_ratio > 1.2:
            return f"成交量温和放大{volume_ratio:.1f}倍，市场参与度提升"
        elif volume_ratio < 0.8:
            return f"成交量萎缩至{volume_ratio:.1f}倍，市场观望情绪浓厚"
        else:
            return f"成交量相对平稳，换手率{avg_turnover:.2f}%"

    def _generate_breadth_description(self, rising_ratio: float, rising_count: int, total_count: int) -> str:
        """生成市场广度描述"""
        if rising_ratio > 0.7:
            return f"市场普涨格局，{rising_count}/{total_count}只股票上涨({rising_ratio:.1%})"
        elif rising_ratio > 0.5:
            return f"市场偏强运行，多数股票上涨({rising_ratio:.1%})"
        elif rising_ratio > 0.3:
            return f"市场分化明显，{rising_count}只股票上涨({rising_ratio:.1%})"
        else:
            return f"市场普跌格局，仅{rising_count}只股票上涨({rising_ratio:.1%})"

    def _generate_volatility_description(self, volatility: float) -> str:
        """生成波动率描述"""
        if volatility > 3.0:
            return f"市场波动剧烈({volatility:.2f}%)，情绪极度不稳定"
        elif volatility > 1.5:
            return f"市场波动较大({volatility:.2f}%)，投资者情绪波动"
        elif volatility > 0.5:
            return f"市场波动温和({volatility:.2f}%)，情绪相对稳定"
        else:
            return f"市场波动微弱({volatility:.2f}%)，交投清淡"

    def _get_empty_sentiment_data(self, indicator_type: str) -> Dict[str, Any]:
        """获取空的情绪数据结构"""
        return {
            'indicator': indicator_type,
            'sentiment_score': 0.5,
            'sentiment_level': 'neutral',
            'description': '数据不可用',
            'data_available': False,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def get_comprehensive_sentiment_analysis(self) -> Dict[str, Any]:
        """获取综合市场情绪分析"""
        try:
            logger.info("🔍 开始综合市场情绪分析...")

            # 获取各项情绪指标
            indicators_data = {
                'limit_up_down': self.get_limit_up_down_sentiment(),
                'volume': self.get_volume_sentiment(),
                'market_breadth': self.get_market_breadth_sentiment(),
                'volatility': self.get_volatility_sentiment()
            }

            # 计算综合情绪评分
            total_weighted_score = 0.0
            total_weight = 0.0
            available_indicators = []

            for indicator_name, data in indicators_data.items():
                if data.get('data_available', False):
                    weight = self.sentiment_indicators.get(indicator_name, {}).get('weight', 0.2)
                    score = data.get('sentiment_score', 0.5)

                    total_weighted_score += score * weight
                    total_weight += weight
                    available_indicators.append(indicator_name)

            # 计算综合评分
            if total_weight > 0:
                comprehensive_score = total_weighted_score / total_weight
            else:
                comprehensive_score = 0.5

            # 确定综合情绪等级
            comprehensive_level = self._get_sentiment_level(comprehensive_score)

            # 生成情绪描述
            sentiment_description = self._generate_comprehensive_description(
                comprehensive_level, comprehensive_score, available_indicators
            )

            # 生成交易建议
            trading_suggestions = self._generate_trading_suggestions(comprehensive_level, comprehensive_score)

            result = {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'comprehensive_score': comprehensive_score,
                'comprehensive_level': comprehensive_level,
                'sentiment_description': sentiment_description,
                'available_indicators': available_indicators,
                'indicators_count': len(available_indicators),
                'total_indicators': len(indicators_data),
                'indicators_data': indicators_data,
                'trading_suggestions': trading_suggestions,
                'market_outlook': self._generate_market_outlook(comprehensive_level),
                'data_quality': 'good' if len(available_indicators) >= 3 else 'limited'
            }

            logger.info(f"✅ 综合情绪分析完成，评分: {comprehensive_score:.3f}, 等级: {comprehensive_level}")
            return result

        except Exception as e:
            logger.error(f"综合情绪分析失败: {e}")
            return {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'comprehensive_score': 0.5,
                'comprehensive_level': 'neutral',
                'sentiment_description': '情绪分析失败',
                'available_indicators': [],
                'indicators_count': 0,
                'total_indicators': 0,
                'indicators_data': {},
                'trading_suggestions': [],
                'market_outlook': 'unknown',
                'data_quality': 'error'
            }

    def _generate_comprehensive_description(self, level: str, score: float, indicators: List[str]) -> str:
        """生成综合情绪描述"""
        level_descriptions = {
            'extreme_greed': '市场情绪极度贪婪，投资者过度乐观，需警惕回调风险',
            'greed': '市场情绪偏向贪婪，投资者较为乐观，可适度参与',
            'neutral': '市场情绪相对中性，投资者观望情绪较重，等待方向明确',
            'fear': '市场情绪偏向恐慌，投资者较为悲观，可关注超跌机会',
            'extreme_fear': '市场情绪极度恐慌，投资者过度悲观，或现抄底良机'
        }

        base_description = level_descriptions.get(level, '市场情绪未知')
        indicator_info = f"基于{len(indicators)}项指标分析"

        return f"{base_description}（评分: {score:.2f}，{indicator_info}）"

    def _generate_trading_suggestions(self, level: str, score: float) -> List[Dict[str, Any]]:
        """生成交易建议"""
        suggestions = []

        if level == 'extreme_greed':
            suggestions.extend([
                {'type': 'risk_control', 'description': '高度警惕市场回调，适当减仓控制风险'},
                {'type': 'profit_taking', 'description': '考虑获利了结，锁定前期收益'},
                {'type': 'avoid_chase', 'description': '避免追高，等待更好的入场时机'}
            ])
        elif level == 'greed':
            suggestions.extend([
                {'type': 'moderate_participation', 'description': '可适度参与，但控制仓位'},
                {'type': 'selective_buying', 'description': '精选个股，避免盲目跟风'},
                {'type': 'set_stop_loss', 'description': '设置止损位，控制下行风险'}
            ])
        elif level == 'neutral':
            suggestions.extend([
                {'type': 'wait_and_see', 'description': '保持观望，等待市场方向明确'},
                {'type': 'prepare_positions', 'description': '做好准备，关注突破信号'},
                {'type': 'balanced_allocation', 'description': '均衡配置，分散投资风险'}
            ])
        elif level == 'fear':
            suggestions.extend([
                {'type': 'look_for_opportunities', 'description': '关注超跌反弹机会'},
                {'type': 'gradual_entry', 'description': '可考虑分批建仓'},
                {'type': 'focus_quality', 'description': '重点关注优质标的'}
            ])
        elif level == 'extreme_fear':
            suggestions.extend([
                {'type': 'contrarian_thinking', 'description': '逆向思维，关注抄底机会'},
                {'type': 'long_term_value', 'description': '从长期价值角度选择标的'},
                {'type': 'patience_required', 'description': '保持耐心，等待市场企稳'}
            ])

        return suggestions

    def _generate_market_outlook(self, level: str) -> str:
        """生成市场展望"""
        outlooks = {
            'extreme_greed': '短期调整风险较大，中期需观察基本面支撑',
            'greed': '短期可能震荡，中期趋势取决于基本面变化',
            'neutral': '方向不明，需等待催化剂出现',
            'fear': '短期可能企稳，中期有反弹潜力',
            'extreme_fear': '底部区域，中长期配置价值显现'
        }

        return outlooks.get(level, '市场前景不明')

def main():
    """测试市场情绪分析器"""
    print("📊 测试市场情绪分析器...")

    analyzer = MarketSentimentAnalyzer()

    # 测试各项指标
    print("\n🔍 测试涨跌停情绪...")
    limit_sentiment = analyzer.get_limit_up_down_sentiment()
    print(f"涨停: {limit_sentiment.get('limit_up_count', 0)}只, 跌停: {limit_sentiment.get('limit_down_count', 0)}只")
    print(f"情绪等级: {limit_sentiment.get('sentiment_level', 'unknown')}")

    print("\n🔍 测试成交量情绪...")
    volume_sentiment = analyzer.get_volume_sentiment()
    print(f"成交额: {volume_sentiment.get('total_volume', 0)/1e8:.1f}亿")
    print(f"情绪等级: {volume_sentiment.get('sentiment_level', 'unknown')}")

    print("\n🔍 测试市场广度...")
    breadth_sentiment = analyzer.get_market_breadth_sentiment()
    print(f"上涨股票比例: {breadth_sentiment.get('rising_ratio', 0):.1%}")
    print(f"情绪等级: {breadth_sentiment.get('sentiment_level', 'unknown')}")

    print("\n🔍 综合情绪分析...")
    comprehensive = analyzer.get_comprehensive_sentiment_analysis()
    print(f"综合评分: {comprehensive['comprehensive_score']:.3f}")
    print(f"情绪等级: {comprehensive['comprehensive_level']}")
    print(f"描述: {comprehensive['sentiment_description']}")
    print(f"交易建议数: {len(comprehensive['trading_suggestions'])}")

if __name__ == "__main__":
    main()
