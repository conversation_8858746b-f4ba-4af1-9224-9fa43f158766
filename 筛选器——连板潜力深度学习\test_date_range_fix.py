#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期范围修复
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from training.training_pipeline import TrainingPipeline

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_date_range_kline_data():
    """测试日期范围K线数据获取"""
    print("🧪 测试日期范围K线数据获取...")
    
    # 创建训练流程
    pipeline = TrainingPipeline()
    
    # 测试股票和日期（选择一个较早的日期）
    test_stock = "000001"  # 平安银行，数据比较稳定
    test_date = datetime(2025, 1, 29)  # 涨停前一天
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 测试日期: {test_date.strftime('%Y-%m-%d')}")
    
    try:
        # 1. 检查数据库当前状态
        print("\n1️⃣ 检查数据库当前状态...")
        import sqlite3
        conn = sqlite3.connect('historical_data.db')
        
        before_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
        print(f"   {test_stock}当前K线数据: {before_count}条")
        
        conn.close()
        
        # 2. 测试新的日期范围获取方法
        print("\n2️⃣ 测试新的日期范围获取方法...")
        start_date = test_date - timedelta(days=60)  # 涨停前60天
        end_date = test_date - timedelta(days=1)     # 涨停前一天
        
        print(f"   查询时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        kline_data = pipeline._get_kline_data_by_date_range(test_stock, start_date, end_date)
        
        if not kline_data.empty:
            print(f"✅ 获取K线数据成功: {len(kline_data)}条")
            print(f"   数据时间范围: {kline_data['date'].min()} 到 {kline_data['date'].max()}")
            print(f"   样本数据:")
            print(kline_data.head(3))
            
            # 3. 测试完整的历史数据获取方法
            print("\n3️⃣ 测试完整的历史数据获取方法...")
            historical_data = pipeline._get_historical_kline_before_date(test_stock, test_date, 30)
            
            if not historical_data.empty:
                print(f"✅ 历史数据获取成功: {len(historical_data)}条")
                print(f"   数据时间范围: {historical_data['date'].min()} 到 {historical_data['date'].max()}")
                
                # 4. 检查数据库状态变化
                print("\n4️⃣ 检查数据库状态变化...")
                conn = sqlite3.connect('historical_data.db')
                
                after_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
                print(f"   {test_stock}现在K线数据: {after_count}条")
                print(f"   新增数据: {after_count - before_count}条")
                
                if after_count > before_count:
                    print("✅ K线数据存储成功！")
                    
                    # 5. 测试查询功能
                    print("\n5️⃣ 测试查询功能...")
                    query_start = (test_date - timedelta(days=30)).strftime('%Y-%m-%d')
                    query_end = test_date.strftime('%Y-%m-%d')
                    
                    stored_data = pipeline.data_storage.get_kline_data(
                        test_stock,
                        start_date=query_start,
                        end_date=query_end
                    )
                    
                    print(f"✅ 查询K线数据成功: {len(stored_data)}条")
                    
                    if len(stored_data) > 0:
                        print("🎉 日期范围K线数据获取和存储都正常工作！")
                        return True
                    else:
                        print("❌ 查询结果为空")
                        return False
                else:
                    print("❌ K线数据没有存储到数据库")
                    return False
                
                conn.close()
            else:
                print("❌ 历史数据获取失败")
                return False
        else:
            print("❌ 日期范围数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_date_ranges():
    """测试多个日期范围"""
    print("\n🧪 测试多个日期范围...")
    
    pipeline = TrainingPipeline()
    
    # 测试不同的日期范围
    test_cases = [
        ("000001", datetime(2025, 1, 29)),  # 2025年1月
        ("000001", datetime(2024, 12, 15)), # 2024年12月
        ("000001", datetime(2024, 11, 20)), # 2024年11月
    ]
    
    results = {}
    
    for stock_code, test_date in test_cases:
        print(f"\n📊 测试: {stock_code}, 日期: {test_date.strftime('%Y-%m-%d')}")
        
        try:
            # 获取历史K线数据
            kline_data = pipeline._get_historical_kline_before_date(stock_code, test_date, 30)
            
            if not kline_data.empty:
                # 测试查询
                start_date = (test_date - timedelta(days=30)).strftime('%Y-%m-%d')
                end_date = test_date.strftime('%Y-%m-%d')
                
                stored_data = pipeline.data_storage.get_kline_data(
                    stock_code,
                    start_date=start_date,
                    end_date=end_date
                )
                
                results[test_date.strftime('%Y-%m-%d')] = {
                    "获取": len(kline_data),
                    "查询": len(stored_data),
                    "状态": "成功" if len(stored_data) > 0 else "查询失败"
                }
                print(f"✅ 获取: {len(kline_data)}条, 查询: {len(stored_data)}条")
            else:
                results[test_date.strftime('%Y-%m-%d')] = {
                    "获取": 0,
                    "查询": 0,
                    "状态": "获取失败"
                }
                print(f"❌ 获取失败")
                
        except Exception as e:
            results[test_date.strftime('%Y-%m-%d')] = {
                "获取": 0,
                "查询": 0,
                "状态": f"异常: {str(e)}"
            }
            print(f"❌ 异常: {e}")
    
    # 打印结果汇总
    print("\n📋 测试结果汇总:")
    print("日期\t\t获取\t查询\t状态")
    print("-" * 50)
    for date_str, result in results.items():
        print(f"{date_str}\t{result['获取']}\t{result['查询']}\t{result['状态']}")
    
    success_count = sum(1 for result in results.values() if result['状态'] == '成功')
    return success_count, len(results)

def main():
    """主函数"""
    print("🔧 日期范围K线数据获取修复测试开始...")
    print("="*60)
    
    # 测试单个日期范围
    single_result = test_date_range_kline_data()
    
    # 测试多个日期范围
    success_count, total_count = test_multiple_date_ranges()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if single_result:
        print("✅ 日期范围K线数据获取和存储正常")
    else:
        print("❌ 日期范围K线数据获取或存储异常")
    
    print(f"📊 多日期范围测试: {success_count}/{total_count} 成功")
    
    if single_result and success_count > 0:
        print("🎉 修复成功！现在可以重新运行完整训练")
        print("💡 预期结果:")
        print("   - 能够获取指定日期之前的历史K线数据")
        print("   - K线数据会正常存储到数据库")
        print("   - 负样本创建会成功")
        print("   - 模型训练会正常进行")
    else:
        print("❌ 修复可能不完整，需要进一步调试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
