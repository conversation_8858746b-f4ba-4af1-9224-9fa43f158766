#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新浪财经板块数据解析器
专门解析新浪财经的行业板块数据
"""

import requests
import re
import json
from datetime import datetime

def get_sina_sector_data():
    """获取新浪财经板块数据"""
    try:
        print("🔍 获取新浪财经板块数据...")
        url = "http://vip.stock.finance.sina.com.cn/q/view/newSinaHy.php"
        
        response = requests.get(url, timeout=10)
        if response.status_code != 200:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
        content = response.text
        print(f"✅ 获取成功，数据长度: {len(content)}")
        
        return content
        
    except Exception as e:
        print(f"❌ 获取新浪数据失败: {e}")
        return None

def parse_sina_sector_data_v2(content):
    """解析新浪财经板块数据 - 改进版"""
    try:
        print("\n🔍 解析新浪财经板块数据...")
        
        # 查找行业板块数据变量
        if 'S_Finance_bankuai_sinaindustry' not in content:
            print("❌ 未找到行业板块数据变量")
            return {}
        
        # 提取完整的JavaScript对象
        start_marker = 'S_Finance_bankuai_sinaindustry = {'
        start_pos = content.find(start_marker)
        
        if start_pos == -1:
            print("❌ 未找到数据开始位置")
            return {}
        
        # 从开始位置查找完整的对象
        brace_count = 0
        data_start = content.find('{', start_pos)
        current_pos = data_start
        
        while current_pos < len(content):
            char = content[current_pos]
            if char == '{':
                brace_count += 1
            elif char == '}':
                brace_count -= 1
                if brace_count == 0:
                    break
            current_pos += 1
        
        if brace_count != 0:
            print("❌ 未找到完整的数据对象")
            return {}
        
        # 提取数据字符串
        data_str = content[data_start:current_pos+1]
        print(f"✅ 提取数据字符串，长度: {len(data_str)}")
        
        # 手动解析数据
        sectors = {}
        
        # 使用正则表达式提取每个板块的数据
        # 格式: "code":"code,name,count,price,change,..."
        pattern = r'"([^"]+)":"[^,]+,([^,]+),([^,]+),([^,]+),([^,]+),'
        matches = re.findall(pattern, data_str)
        
        print(f"✅ 找到 {len(matches)} 个板块数据")
        
        for match in matches:
            code, name, count, price, change = match
            try:
                change_float = float(change)
                price_float = float(price)
                count_int = int(count)
                
                sectors[name] = {
                    'code': code,
                    'name': name,
                    'count': count_int,
                    'price': price_float,
                    'change': change_float
                }
            except ValueError as e:
                print(f"⚠️ 解析板块 {name} 数据失败: {e}")
                continue
        
        return sectors
        
    except Exception as e:
        print(f"❌ 解析失败: {e}")
        return {}

def find_sector_by_name(sectors, target_name):
    """根据名称查找板块"""
    # 精确匹配
    if target_name in sectors:
        return sectors[target_name]
    
    # 模糊匹配
    for name, data in sectors.items():
        if target_name in name or name in target_name:
            return data
    
    return None

def test_sector_parsing():
    """测试板块数据解析"""
    print("🚀 新浪财经板块数据解析测试")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 获取数据
    content = get_sina_sector_data()
    if not content:
        return
    
    # 解析数据
    sectors = parse_sina_sector_data_v2(content)
    
    if not sectors:
        print("❌ 解析失败")
        return
    
    print(f"\n📊 解析结果统计:")
    print(f"总板块数: {len(sectors)}")
    
    # 显示所有板块
    print(f"\n📋 所有板块列表:")
    for i, (name, data) in enumerate(sectors.items(), 1):
        change = data['change']
        count = data['count']
        price = data['price']
        print(f"{i:2d}. {name:12s} | 涨跌: {change:+6.2f}% | 股票数: {count:2d} | 均价: {price:6.2f}")
    
    # 查找汽车相关板块
    print(f"\n🚗 汽车相关板块:")
    auto_sectors = {k: v for k, v in sectors.items() if '汽车' in k}
    
    if auto_sectors:
        for name, data in auto_sectors.items():
            change = data['change']
            count = data['count']
            print(f"  - {name}: {change:+.2f}% ({count}只股票)")
    else:
        print("  未找到汽车相关板块")
    
    # 测试特定板块查找
    test_names = ["汽车零部件", "汽车整车", "汽车", "汽车行业"]
    print(f"\n🔍 测试特定板块查找:")
    
    for test_name in test_names:
        result = find_sector_by_name(sectors, test_name)
        if result:
            print(f"  ✅ {test_name}: {result['change']:+.2f}%")
        else:
            print(f"  ❌ {test_name}: 未找到")
    
    # 显示涨幅前10的板块
    print(f"\n📈 涨幅前10的板块:")
    sorted_sectors = sorted(sectors.items(), key=lambda x: x[1]['change'], reverse=True)
    for i, (name, data) in enumerate(sorted_sectors[:10], 1):
        change = data['change']
        print(f"{i:2d}. {name}: {change:+.2f}%")
    
    print("\n" + "=" * 80)
    print("✅ 测试完成")

if __name__ == "__main__":
    test_sector_parsing()
