#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据测试器 - 模拟真实的盘口数据和量比变化
"""

import sys
import os
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 导入虚拟数据生成器
from virtual_data_generator import VirtualDataGenerator

class RealisticDataTester:
    """真实数据测试器 - 展示详细的盘口和量比数据"""
    
    def __init__(self):
        self.data_generator = VirtualDataGenerator()
        self.notifications_captured = []
        
        # 模拟主应用的数据结构
        self.realtime_data_series = {}
        self.historical_base_data = {}
        self.monitored_stocks = {}
        
        # 初始化测试股票
        self.setup_test_stock()
    
    def setup_test_stock(self):
        """设置测试股票"""
        test_stock = self.data_generator.get_test_stock_info()
        code = test_stock['code']
        
        self.monitored_stocks[code] = {
            'name': test_stock['name'],
            'initialized': True
        }
        
        self.historical_base_data[code] = {
            'k_line_data': [],
            'volume_data': {'avg_volume_5d': 1000000, 'avg_volume_10d': 1200000},
            'price_stats': {'avg_price_5d': 10.0},
            'circulating_shares': 50000
        }
        
        self.realtime_data_series[code] = []
    
    def generate_realistic_scenario_data(self, scenario_name: str) -> List[Dict[str, Any]]:
        """生成更真实的场景数据"""
        if scenario_name == "强势涨停":
            return self._generate_strong_limit_up_scenario()
        elif scenario_name == "假突破回落":
            return self._generate_false_breakout_scenario()
        elif scenario_name == "量价背离":
            return self._generate_volume_price_divergence()
        elif scenario_name == "主力试盘":
            return self._generate_main_force_testing()
        elif scenario_name == "散户追高":
            return self._generate_retail_chasing()
        else:
            return self.data_generator.generate_scenario_data(scenario_name, 60)
    
    def _generate_strong_limit_up_scenario(self) -> List[Dict[str, Any]]:
        """生成强势涨停场景 - 真实的量价配合"""
        data_points = []
        base_price = 10.0
        current_price = base_price
        
        # 模拟强势涨停的真实数据特征
        scenarios = [
            # 时间, 涨幅目标, 量比, 买一量, 买五总量, 卖一量, 卖五总量, 描述
            ("09:30", 2.5, 1.8, 15000, 45000, 3000, 12000, "开盘温和高开"),
            ("09:32", 4.2, 2.3, 25000, 68000, 2000, 8000, "量能放大，买盘增厚"),
            ("09:35", 6.8, 3.1, 45000, 120000, 1500, 5000, "主力开始发力"),
            ("09:38", 8.5, 4.2, 80000, 200000, 800, 2500, "强势拉升，卖盘稀少"),
            ("09:40", 9.8, 5.5, 150000, 350000, 0, 0, "接近涨停，卖盘消失"),
            ("09:42", 10.0, 6.2, 200000, 500000, 0, 0, "封涨停，巨量封单"),
        ]
        
        for i, (time_str, target_gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc) in enumerate(scenarios):
            current_price = base_price * (1 + target_gain / 100)
            
            # 生成买五档数据
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            
            # 生成卖五档数据
            ask_volumes = self._distribute_volume(sell5_total, sell1) if sell5_total > 0 else [0, 0, 0, 0, 0]
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(target_gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),  # 基础成交量80万
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc
            }
            data_points.append(data_point)
        
        return data_points
    
    def _generate_false_breakout_scenario(self) -> List[Dict[str, Any]]:
        """生成假突破回落场景"""
        data_points = []
        base_price = 10.0
        
        scenarios = [
            ("09:30", 1.2, 1.1, 8000, 25000, 6000, 20000, "平开小涨"),
            ("09:33", 3.8, 1.8, 18000, 50000, 4000, 15000, "温和上涨"),
            ("09:36", 6.2, 2.5, 35000, 85000, 2000, 8000, "加速拉升"),
            ("09:39", 7.8, 3.2, 50000, 120000, 1000, 4000, "接近突破"),
            ("09:42", 8.9, 2.1, 25000, 60000, 8000, 35000, "假突破，卖盘涌出"),
            ("09:45", 6.5, 1.8, 12000, 35000, 15000, 60000, "快速回落"),
            ("09:48", 4.2, 1.5, 8000, 25000, 20000, 80000, "继续下跌"),
        ]
        
        for time_str, target_gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in scenarios:
            current_price = base_price * (1 + target_gain / 100)
            
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1)
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(target_gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc
            }
            data_points.append(data_point)
        
        return data_points
    
    def _generate_volume_price_divergence(self) -> List[Dict[str, Any]]:
        """生成量价背离场景"""
        data_points = []
        base_price = 10.0
        
        scenarios = [
            ("09:30", 2.1, 2.8, 20000, 55000, 5000, 18000, "高开放量"),
            ("09:33", 3.5, 2.2, 15000, 42000, 8000, 25000, "涨幅扩大，量能减少"),
            ("09:36", 4.8, 1.8, 12000, 35000, 12000, 40000, "继续上涨，量能萎缩"),
            ("09:39", 5.9, 1.4, 8000, 25000, 18000, 65000, "价涨量缩，卖压增加"),
            ("09:42", 6.2, 1.1, 5000, 18000, 25000, 85000, "量价背离明显"),
            ("09:45", 4.8, 2.5, 15000, 45000, 30000, 120000, "开始回落，量能放大"),
        ]
        
        for time_str, target_gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in scenarios:
            current_price = base_price * (1 + target_gain / 100)
            
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1)
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(target_gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc
            }
            data_points.append(data_point)
        
        return data_points
    
    def _generate_main_force_testing(self) -> List[Dict[str, Any]]:
        """生成主力试盘场景"""
        data_points = []
        base_price = 10.0
        
        scenarios = [
            ("09:30", 1.5, 1.2, 10000, 30000, 8000, 25000, "平开试探"),
            ("09:33", 4.2, 2.8, 35000, 90000, 3000, 12000, "主力试盘拉升"),
            ("09:36", 2.8, 1.8, 15000, 45000, 12000, 45000, "回调测试抛压"),
            ("09:39", 5.8, 3.5, 50000, 130000, 2000, 8000, "再次拉升确认"),
            ("09:42", 3.5, 2.1, 20000, 60000, 15000, 55000, "再次回调洗盘"),
            ("09:45", 7.2, 4.2, 80000, 200000, 1000, 4000, "确认突破，强势拉升"),
        ]
        
        for time_str, target_gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in scenarios:
            current_price = base_price * (1 + target_gain / 100)
            
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1)
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(target_gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc
            }
            data_points.append(data_point)
        
        return data_points
    
    def _generate_retail_chasing(self) -> List[Dict[str, Any]]:
        """生成散户追高场景"""
        data_points = []
        base_price = 10.0
        
        scenarios = [
            ("09:30", 0.8, 0.9, 5000, 18000, 6000, 20000, "低开弱势"),
            ("09:33", 2.2, 1.5, 12000, 35000, 8000, 28000, "缓慢上涨"),
            ("09:36", 4.5, 2.8, 25000, 70000, 5000, 18000, "散户开始追入"),
            ("09:39", 6.8, 4.2, 45000, 120000, 3000, 12000, "追高情绪高涨"),
            ("09:42", 8.2, 5.8, 65000, 180000, 2000, 8000, "疯狂追高"),
            ("09:45", 5.5, 3.2, 20000, 60000, 25000, 95000, "主力出货，散户接盘"),
            ("09:48", 2.8, 2.1, 8000, 25000, 35000, 140000, "快速下跌，散户被套"),
        ]
        
        for time_str, target_gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in scenarios:
            current_price = base_price * (1 + target_gain / 100)
            
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1)
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(target_gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc
            }
            data_points.append(data_point)
        
        return data_points
    
    def _distribute_volume(self, total_volume: int, first_level_volume: int) -> List[int]:
        """将总量分配到五个档位"""
        if total_volume <= 0:
            return [0, 0, 0, 0, 0]
        
        volumes = [first_level_volume]
        remaining = max(0, total_volume - first_level_volume)
        
        # 按递减比例分配剩余量到其他四档
        ratios = [0.4, 0.25, 0.2, 0.15]  # 买二到买五的比例
        
        for ratio in ratios:
            volume = int(remaining * ratio)
            volumes.append(volume)
            remaining -= volume
        
        return volumes[:5]

    # ==================== 简化的算法逻辑（从主应用复制） ====================

    def select_intelligent_algorithm(self, code, stock_data):
        """智能算法选择"""
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            if prev_close <= 0:
                return {'algorithm_type': 'unknown', 'algorithm_name': '数据异常', 'confidence': 0.0}

            current_gain = (current_price - prev_close) / prev_close * 100
            duration = len(self.realtime_data_series.get(code, []))

            # 算法选择逻辑
            if current_gain < -1.0 and duration >= 3:
                return {
                    'algorithm_type': 'drop_reversal',
                    'algorithm_name': '开盘下跌反转算法',
                    'confidence': 0.8
                }
            elif current_gain >= 3.0 and duration >= 3:
                return {
                    'algorithm_type': 'high_open_correction',
                    'algorithm_name': '高开修正算法',
                    'confidence': 0.85
                }
            elif 1.0 <= current_gain <= 5.0 and duration >= 3:
                return {
                    'algorithm_type': 'mid_level_accumulation',
                    'algorithm_name': '中位蓄势算法',
                    'confidence': 0.85
                }
            elif current_gain >= 6.0:
                return {
                    'algorithm_type': 'high_platform_breakout',
                    'algorithm_name': '高位横盘突破算法',
                    'confidence': 0.9
                }
            elif -1.0 <= current_gain <= 1.0:
                return {
                    'algorithm_type': 'standard_flat_open',
                    'algorithm_name': '标准平开算法',
                    'confidence': 0.7
                }
            else:
                return {
                    'algorithm_type': 'standard_high_open',
                    'algorithm_name': '标准高开算法',
                    'confidence': 0.75
                }

        except Exception as e:
            return {'algorithm_type': 'error', 'algorithm_name': '算法错误', 'confidence': 0.0}

    def analyze_buy_sell_strength(self, stock_data):
        """分析买卖盘强度"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])

            if not bid_volumes or not ask_volumes:
                return {'buy_strength': 0, 'sell_pressure': 0, 'ratio': 0}

            total_bid = sum(bid_volumes)
            total_ask = sum(ask_volumes)

            # 买卖比
            ratio = total_bid / total_ask if total_ask > 0 else 999

            # 买盘强度评分
            if ratio > 3.0:
                buy_strength = 5  # 极强
            elif ratio > 2.0:
                buy_strength = 4  # 很强
            elif ratio > 1.5:
                buy_strength = 3  # 较强
            elif ratio > 1.0:
                buy_strength = 2  # 一般
            else:
                buy_strength = 1  # 较弱

            # 卖压评分
            if total_ask < 5000:
                sell_pressure = 1  # 很轻
            elif total_ask < 15000:
                sell_pressure = 2  # 较轻
            elif total_ask < 30000:
                sell_pressure = 3  # 一般
            elif total_ask < 60000:
                sell_pressure = 4  # 较重
            else:
                sell_pressure = 5  # 很重

            return {
                'buy_strength': buy_strength,
                'sell_pressure': sell_pressure,
                'ratio': ratio,
                'total_bid': total_bid,
                'total_ask': total_ask
            }

        except Exception:
            return {'buy_strength': 0, 'sell_pressure': 0, 'ratio': 0}

    def calculate_signal_strength(self, stock_data, algorithm_info, buy_sell_analysis):
        """计算信号强度"""
        try:
            volume_ratio = stock_data.get('volume_ratio', 0)
            change_pct = stock_data.get('change_pct', 0)
            buy_strength = buy_sell_analysis.get('buy_strength', 0)
            sell_pressure = buy_sell_analysis.get('sell_pressure', 0)

            # 基础分数
            base_score = 0

            # 量比评分 (0-0.3)
            if volume_ratio > 3.0:
                volume_score = 0.3
            elif volume_ratio > 2.0:
                volume_score = 0.25
            elif volume_ratio > 1.5:
                volume_score = 0.2
            elif volume_ratio > 1.0:
                volume_score = 0.15
            else:
                volume_score = 0.1

            # 涨幅评分 (0-0.3)
            if 2.0 <= change_pct <= 8.0:
                price_score = 0.3
            elif 0 <= change_pct <= 2.0:
                price_score = 0.2
            elif change_pct > 8.0:
                price_score = 0.15  # 涨幅过大风险增加
            else:
                price_score = 0.1

            # 买卖盘评分 (0-0.4)
            buysell_score = (buy_strength * 0.08) - (sell_pressure * 0.04)
            buysell_score = max(0, min(0.4, buysell_score))

            total_score = volume_score + price_score + buysell_score

            return min(1.0, total_score)

        except Exception:
            return 0.0

    def should_send_buy_signal(self, signal_strength, algorithm_info, buy_sell_analysis):
        """判断是否应该发送买入信号"""
        try:
            # 基础条件：信号强度 >= 0.6
            if signal_strength < 0.6:
                return False, "信号强度不足"

            # 买卖盘条件：买盘强度 >= 3 或 买卖比 >= 1.5
            buy_strength = buy_sell_analysis.get('buy_strength', 0)
            ratio = buy_sell_analysis.get('ratio', 0)

            if buy_strength < 3 and ratio < 1.5:
                return False, "买盘力量不足"

            # 算法置信度条件
            confidence = algorithm_info.get('confidence', 0)
            if confidence < 0.7:
                return False, "算法置信度不足"

            return True, "符合买入条件"

        except Exception:
            return False, "判断异常"

    def display_detailed_analysis(self, data_point, algorithm_info, buy_sell_analysis, signal_strength, buy_signal, reason):
        """显示详细的分析结果"""
        print(f"\n⏰ {data_point['timestamp']} - {data_point.get('scenario_desc', '')}")
        print("=" * 80)

        # 基础数据
        print(f"📊 基础数据:")
        print(f"   价格: {data_point['current_price']:.2f}元 | 涨幅: {data_point['change_pct']:.2f}% | 量比: {data_point['volume_ratio']:.2f}")
        print(f"   成交量: {data_point['volume']:,}股")

        # 盘口数据
        bid_volumes = data_point.get('bid_volumes', [])
        ask_volumes = data_point.get('ask_volumes', [])

        print(f"\n💰 盘口数据:")
        print(f"   买盘: 买一 {bid_volumes[0]:,} | 买二 {bid_volumes[1]:,} | 买三 {bid_volumes[2]:,} | 买四 {bid_volumes[3]:,} | 买五 {bid_volumes[4]:,}")
        print(f"   卖盘: 卖一 {ask_volumes[0]:,} | 卖二 {ask_volumes[1]:,} | 卖三 {ask_volumes[2]:,} | 卖四 {ask_volumes[3]:,} | 卖五 {ask_volumes[4]:,}")

        # 买卖盘分析
        total_bid = buy_sell_analysis.get('total_bid', 0)
        total_ask = buy_sell_analysis.get('total_ask', 0)
        ratio = buy_sell_analysis.get('ratio', 0)
        buy_strength = buy_sell_analysis.get('buy_strength', 0)
        sell_pressure = buy_sell_analysis.get('sell_pressure', 0)

        print(f"\n🎯 盘口分析:")
        print(f"   买五总量: {total_bid:,}手 | 卖五总量: {total_ask:,}手")
        print(f"   买卖比: {ratio:.2f} | 买盘强度: {buy_strength}/5 | 卖压: {sell_pressure}/5")

        # 算法信息
        print(f"\n🧮 算法分析:")
        print(f"   选择算法: {algorithm_info.get('algorithm_name', '未知')}")
        print(f"   算法置信度: {algorithm_info.get('confidence', 0):.2f}")
        print(f"   信号强度: {signal_strength:.2f}")

        # 买入判断
        signal_icon = "🚀" if buy_signal else "❌"
        print(f"\n{signal_icon} 买入判断: {'是' if buy_signal else '否'}")
        print(f"   判断理由: {reason}")

        if buy_signal:
            if signal_strength >= 0.8:
                recommendation = "🚀 强烈买入"
            elif signal_strength >= 0.7:
                recommendation = "📈 建议买入"
            else:
                recommendation = "👀 谨慎买入"

            print(f"   操作建议: {recommendation}")

            # 模拟通知
            self.notifications_captured.append({
                'time': data_point['timestamp'],
                'stock': '测试股票',
                'price': data_point['current_price'],
                'recommendation': recommendation,
                'signal_strength': signal_strength,
                'algorithm': algorithm_info.get('algorithm_name', ''),
                'reason': reason
            })
            print(f"   🔔 已发送买入通知")

        print("-" * 80)

    def run_realistic_scenario_test(self, scenario_name: str):
        """运行真实场景测试"""
        print(f"\n🎬 开始测试场景: {scenario_name}")
        print("=" * 100)

        # 清空数据
        test_code = 'TEST001'
        self.realtime_data_series[test_code] = []
        self.notifications_captured.clear()

        # 生成真实场景数据
        scenario_data = self.generate_realistic_scenario_data(scenario_name)

        buy_signals_count = 0
        max_signal_strength = 0

        for data_point in scenario_data:
            # 添加到实时数据序列
            self.realtime_data_series[test_code].append(data_point)

            # 算法选择
            algorithm_info = self.select_intelligent_algorithm(test_code, data_point)

            # 买卖盘分析
            buy_sell_analysis = self.analyze_buy_sell_strength(data_point)

            # 信号强度计算
            signal_strength = self.calculate_signal_strength(data_point, algorithm_info, buy_sell_analysis)
            max_signal_strength = max(max_signal_strength, signal_strength)

            # 买入信号判断
            buy_signal, reason = self.should_send_buy_signal(signal_strength, algorithm_info, buy_sell_analysis)
            if buy_signal:
                buy_signals_count += 1

            # 显示详细分析
            self.display_detailed_analysis(
                data_point, algorithm_info, buy_sell_analysis,
                signal_strength, buy_signal, reason
            )

            time.sleep(1)  # 暂停1秒，便于观察

        # 场景总结
        print(f"\n📊 场景 '{scenario_name}' 测试总结:")
        print(f"   • 数据点数: {len(scenario_data)}")
        print(f"   • 买入信号: {buy_signals_count} 次")
        print(f"   • 最大信号强度: {max_signal_strength:.2f}")
        print(f"   • 通知发送: {len(self.notifications_captured)} 次")

        if self.notifications_captured:
            print(f"\n🔔 通知详情:")
            for notification in self.notifications_captured:
                print(f"   • {notification['time']}: {notification['recommendation']} "
                      f"(强度: {notification['signal_strength']:.2f}, 算法: {notification['algorithm']})")

        return {
            'scenario': scenario_name,
            'data_points': len(scenario_data),
            'buy_signals': buy_signals_count,
            'max_signal_strength': max_signal_strength,
            'notifications': len(self.notifications_captured)
        }

    def run_all_realistic_tests(self):
        """运行所有真实场景测试"""
        print("🚀 开始真实数据测试系统")
        print("🎯 目标: 验证算法在真实盘口数据下的表现")
        print("=" * 100)

        scenarios = [
            "强势涨停",
            "假突破回落",
            "量价背离",
            "主力试盘",
            "散户追高"
        ]

        results = []
        for scenario in scenarios:
            result = self.run_realistic_scenario_test(scenario)
            results.append(result)

            print(f"\n{'='*50} 场景间隔 {'='*50}")
            time.sleep(2)

        # 生成总体报告
        self.generate_realistic_test_report(results)

    def generate_realistic_test_report(self, results):
        """生成真实测试报告"""
        print(f"\n🎉 真实数据测试报告")
        print("=" * 100)

        total_data_points = sum(r['data_points'] for r in results)
        total_buy_signals = sum(r['buy_signals'] for r in results)
        total_notifications = sum(r['notifications'] for r in results)

        print(f"📈 测试概览:")
        print(f"   • 测试场景: {len(results)} 个")
        print(f"   • 总数据点: {total_data_points} 个")
        print(f"   • 总买入信号: {total_buy_signals} 次")
        print(f"   • 总通知数: {total_notifications} 次")
        print(f"   • 信号准确率: {(total_buy_signals/total_data_points*100):.1f}%")

        print(f"\n📋 各场景表现:")
        for result in results:
            scenario = result['scenario']
            data_points = result['data_points']
            buy_signals = result['buy_signals']
            max_strength = result['max_signal_strength']

            signal_rate = (buy_signals / data_points * 100) if data_points > 0 else 0

            print(f"   📊 {scenario}:")
            print(f"      数据点: {data_points} | 买入信号: {buy_signals} | 信号率: {signal_rate:.1f}% | 最大强度: {max_strength:.2f}")

        print(f"\n🎯 算法表现评估:")

        # 根据场景特点评估算法合理性
        scenario_expectations = {
            "强势涨停": "应该多次触发买入信号",
            "假突破回落": "应该在回落时停止信号",
            "量价背离": "应该识别风险，减少信号",
            "主力试盘": "应该在确认突破时发信号",
            "散户追高": "应该在高位减少信号"
        }

        for result in results:
            scenario = result['scenario']
            buy_signals = result['buy_signals']
            expectation = scenario_expectations.get(scenario, "")

            if scenario == "强势涨停" and buy_signals >= 3:
                assessment = "✅ 表现优秀"
            elif scenario == "假突破回落" and buy_signals <= 2:
                assessment = "✅ 风控良好"
            elif scenario == "量价背离" and buy_signals <= 1:
                assessment = "✅ 识别准确"
            elif scenario == "主力试盘" and 1 <= buy_signals <= 3:
                assessment = "✅ 判断合理"
            elif scenario == "散户追高" and buy_signals <= 2:
                assessment = "✅ 避险及时"
            else:
                assessment = "⚠️ 需要优化"

            print(f"   {assessment} {scenario}: {expectation}")

        print(f"\n🎉 测试完成! 算法在真实数据下表现{'良好' if total_buy_signals > 0 else '需要调优'}。")

if __name__ == "__main__":
    tester = RealisticDataTester()
    tester.run_all_realistic_tests()
