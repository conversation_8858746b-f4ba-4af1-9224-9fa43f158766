#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试数据存储修复
验证Timestamp类型转换是否正常工作
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_kline_data_storage():
    """测试K线数据存储"""
    print("🧪 测试K线数据存储...")
    
    # 创建存储器
    storage = HistoricalDataStorage()
    
    # 创建测试数据，包含pandas Timestamp
    test_dates = pd.date_range(start='2024-01-01', periods=5, freq='D')
    
    test_kline_data = pd.DataFrame({
        'date': test_dates,  # 这里是pandas Timestamp类型
        'open': [10.5, 10.8, 11.2, 11.0, 10.9],
        'high': [10.9, 11.3, 11.5, 11.2, 11.1],
        'low': [10.3, 10.7, 11.0, 10.8, 10.7],
        'close': [10.8, 11.2, 11.0, 10.9, 11.0],
        'volume': [1000000, 1200000, 1500000, 1100000, 1300000],
        'amount': [10800000, 13440000, 16500000, 11990000, 14300000],
        'change_pct': [2.86, 3.70, -1.79, -0.91, 0.92],
        'turnover_rate': [2.5, 3.0, 3.8, 2.8, 3.2]
    })
    
    print(f"📊 测试数据类型:")
    print(f"   date列类型: {type(test_kline_data['date'].iloc[0])}")
    print(f"   数据样本:\n{test_kline_data.head(2)}")
    
    # 测试存储
    try:
        stored_count = storage.store_kline_data('TEST001', test_kline_data)
        print(f"✅ K线数据存储成功: {stored_count}条")
        
        if stored_count > 0:
            print("✅ Timestamp类型转换修复成功！")
        else:
            print("❌ 数据存储失败，可能还有其他问题")
            
    except Exception as e:
        print(f"❌ K线数据存储失败: {e}")
    
    # 清理测试数据
    try:
        storage.conn.execute("DELETE FROM stock_kline_data WHERE stock_code = 'TEST001'")
        storage.conn.commit()
        print("🧹 清理测试数据完成")
    except Exception as e:
        print(f"⚠️ 清理测试数据失败: {e}")

def test_capital_flow_data_storage():
    """测试资金流向数据存储"""
    print("\n🧪 测试资金流向数据存储...")
    
    # 创建存储器
    storage = HistoricalDataStorage()
    
    # 创建测试数据，包含pandas Timestamp
    test_dates = pd.date_range(start='2024-01-01', periods=3, freq='D')
    
    test_capital_data = pd.DataFrame({
        '日期': test_dates,  # 这里是pandas Timestamp类型
        '主力净流入-净额': [1000000, -500000, 2000000],
        '主力净流入-净占比': [5.2, -2.1, 8.5],
        '超大单净流入-净额': [800000, -300000, 1500000],
        '大单净流入-净额': [200000, -200000, 500000],
        '中单净流入-净额': [-300000, 100000, -800000],
        '小单净流入-净额': [-700000, 400000, -1200000]
    })
    
    print(f"📊 测试数据类型:")
    print(f"   日期列类型: {type(test_capital_data['日期'].iloc[0])}")
    print(f"   数据样本:\n{test_capital_data.head(2)}")
    
    # 测试存储
    try:
        stored_count = storage.store_capital_flow_data('TEST001', test_capital_data)
        print(f"✅ 资金流向数据存储成功: {stored_count}条")
        
        if stored_count > 0:
            print("✅ 资金流向数据Timestamp类型转换修复成功！")
        else:
            print("❌ 资金流向数据存储失败")
            
    except Exception as e:
        print(f"❌ 资金流向数据存储失败: {e}")
    
    # 清理测试数据
    try:
        storage.conn.execute("DELETE FROM capital_flow_data WHERE stock_code = 'TEST001'")
        storage.conn.commit()
        print("🧹 清理测试数据完成")
    except Exception as e:
        print(f"⚠️ 清理测试数据失败: {e}")

def test_minute_data_storage():
    """测试分时数据存储"""
    print("\n🧪 测试分时数据存储...")
    
    # 创建存储器
    storage = HistoricalDataStorage()
    
    # 创建测试数据，包含datetime时间
    base_time = datetime(2024, 1, 1, 9, 30)
    test_times = [base_time + timedelta(minutes=i) for i in range(5)]
    
    test_minute_data = pd.DataFrame({
        'time': test_times,  # 这里是datetime类型
        'open': [10.5, 10.6, 10.7, 10.8, 10.9],
        'high': [10.6, 10.7, 10.8, 10.9, 11.0],
        'low': [10.4, 10.5, 10.6, 10.7, 10.8],
        'close': [10.6, 10.7, 10.8, 10.9, 11.0],
        'volume': [100000, 120000, 150000, 110000, 130000],
        'amount': [1060000, 1284000, 1620000, 1199000, 1430000]
    })
    
    print(f"📊 测试数据类型:")
    print(f"   time列类型: {type(test_minute_data['time'].iloc[0])}")
    print(f"   数据样本:\n{test_minute_data.head(2)}")
    
    # 测试存储
    try:
        stored_count = storage.store_minute_data('TEST001', '2024-01-01', test_minute_data)
        print(f"✅ 分时数据存储成功: {stored_count}条")
        
        if stored_count > 0:
            print("✅ 分时数据datetime类型转换修复成功！")
        else:
            print("❌ 分时数据存储失败")
            
    except Exception as e:
        print(f"❌ 分时数据存储失败: {e}")
    
    # 清理测试数据
    try:
        storage.conn.execute("DELETE FROM stock_minute_data WHERE stock_code = 'TEST001'")
        storage.conn.commit()
        print("🧹 清理测试数据完成")
    except Exception as e:
        print(f"⚠️ 清理测试数据失败: {e}")

def main():
    """主函数"""
    print("🔧 数据存储修复测试开始...")
    print("="*60)
    
    # 测试K线数据存储
    test_kline_data_storage()
    
    # 测试资金流向数据存储
    test_capital_flow_data_storage()
    
    # 测试分时数据存储
    test_minute_data_storage()
    
    print("\n" + "="*60)
    print("🎉 数据存储修复测试完成！")
    print("💡 如果所有测试都通过，说明Timestamp类型转换问题已修复")

if __name__ == "__main__":
    main()
