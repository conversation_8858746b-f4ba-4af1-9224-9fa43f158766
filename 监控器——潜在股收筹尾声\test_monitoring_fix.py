#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试监控修复效果
"""

import sys
import os
from pathlib import Path
import time

# 添加当前目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_chip_analysis_fix():
    """测试筹码分析修复"""
    print("🧪 测试筹码分析修复")
    print("=" * 60)
    
    try:
        # 导入应用类
        from 潜在股进一步筛选 import StockFurtherScreening
        
        # 创建测试实例
        class TestScreening(StockFurtherScreening):
            def __init__(self):
                self.data_cache_dir = Path("screening_historical_data_cache")
                self.data_cache_dir.mkdir(exist_ok=True)
                
                self.monitored_stocks = {}
                self.historical_base_data = {}
                self.realtime_data_cache = {}
                self.historical_cache = {}
                
                self.config = {
                    'chip_stability_threshold': 65,
                    'scan_interval': 30,
                    'key_time_scan_interval': 15,
                }
                
        screening = TestScreening()
        
        # 模拟筹码分布数据（字符串键）
        chip_distribution = {
            "10.5": 1000,
            "11.0": 1500,
            "11.5": 2000,
            "12.0": 1200,
            "12.5": 800
        }
        
        # 模拟股票数据
        stock_data = {
            'latest_price': 11.8,
            'change_pct': 2.5,
            'volume': 50000000,
            'chip_distribution_30d': chip_distribution
        }
        
        # 模拟历史数据
        historical_data = {
            'chip_distribution_30d': chip_distribution
        }
        
        print("1️⃣ 测试实时筹码分析...")
        
        # 执行实时筹码分析
        result = screening.analyze_chip_lock_realtime("TEST", stock_data, historical_data)
        
        print(f"   ✅ 实时筹码分析成功")
        print(f"   📊 筹码稳定性: {result.get('chip_stability', False)}")
        print(f"   📊 低位筹码锁定: {result.get('low_chip_lock', False)}")
        print(f"   📊 评分: {result.get('score', 0)}")
        
        print("\n2️⃣ 测试收盘后筹码分析...")
        
        # 模拟今日数据
        today_data = {"收盘": 11.8}
        
        # 执行收盘后筹码分析
        result2 = screening.analyze_chip_lock_eod("TEST", today_data, historical_data)
        
        print(f"   ✅ 收盘后筹码分析成功")
        print(f"   📊 筹码稳定性: {result2.get('chip_stability', False)}")
        print(f"   📊 低位筹码锁定: {result2.get('low_chip_lock', False)}")
        print(f"   📊 评分: {result2.get('score', 0)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delay_mechanism():
    """测试延迟机制"""
    print("\n🧪 测试延迟机制")
    print("=" * 60)
    
    print("1️⃣ 测试单股票延迟...")
    start_time = time.time()
    
    # 模拟处理3只股票
    for i in range(3):
        print(f"   处理股票 {i+1}...")
        if i < 2:  # 前两只股票需要延迟
            time.sleep(0.3)
    
    end_time = time.time()
    elapsed = end_time - start_time
    
    print(f"   ✅ 处理3只股票耗时: {elapsed:.1f}秒")
    print(f"   📊 预期时间: ~0.6秒 (2次延迟)")
    
    if 0.5 <= elapsed <= 0.8:
        print("   ✅ 延迟机制正常")
        return True
    else:
        print("   ❌ 延迟机制异常")
        return False

def main():
    """主测试函数"""
    print("🚀 潜在股进一步筛选器修复测试")
    print("=" * 80)
    
    test_results = []
    
    # 测试筹码分析修复
    test_results.append(test_chip_analysis_fix())
    
    # 测试延迟机制
    test_results.append(test_delay_mechanism())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"   ✅ 通过测试: {passed}/{total}")
    print(f"   📈 成功率: {passed/total:.1%}")
    
    if passed == total:
        print("🎉 所有修复测试通过！")
        print("💡 筹码分析错误已修复")
        print("💡 数据获取延迟机制已添加")
        print("💡 错误处理和状态提示已完善")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
