#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游资席位数据预处理模块
根据报告要求实现游资席位统计分析
"""

import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta

class YouziDataProcessor:
    """游资席位数据预处理器"""
    
    def __init__(self, youzi_seats_manager):
        self.youzi_seats_manager = youzi_seats_manager
        self.processed_data = None
        self.top_traders = None
        self.stock_trader_matrix = None
    
    def process_all_data(self):
        """处理所有游资席位数据"""
        try:
            print("🔄 开始游资席位数据预处理...")
            
            # 1. 数据预处理
            all_data = self._load_and_preprocess_data()
            
            if not all_data:
                print("⚠️ 无可用的游资席位数据")
                return False
            
            # 2. 构建核心指标体系
            seat_stats = self._build_seat_statistics(all_data)
            
            # 3. 计算游资活跃度评分
            seat_stats = self._calculate_hot_money_scores(seat_stats)
            
            # 4. 构建股票-游资关联矩阵
            self.stock_trader_matrix = self._build_stock_trader_matrix(all_data)
            
            # 5. 筛选顶级游资
            self.top_traders = self._identify_top_traders(seat_stats)
            
            # 6. 保存处理结果
            self.processed_data = {
                'seat_stats': seat_stats,
                'top_traders': self.top_traders,
                'stock_trader_matrix': self.stock_trader_matrix,
                'process_time': datetime.now().isoformat()
            }
            
            print(f"✅ 游资席位数据预处理完成")
            print(f"   识别顶级游资: {len(self.top_traders)} 家")
            print(f"   股票关联记录: {len(self.stock_trader_matrix)} 条")
            
            return True
            
        except Exception as e:
            print(f"❌ 游资席位数据预处理失败: {e}")
            return False
    
    def _load_and_preprocess_data(self):
        """加载并预处理原始数据"""
        try:
            all_data = []
            
            # 获取游资席位数据目录
            data_dir = self.youzi_seats_manager.data_dir
            
            if not os.path.exists(data_dir):
                print(f"⚠️ 游资席位数据目录不存在: {data_dir}")
                return []
            
            # 遍历所有日期文件
            for filename in os.listdir(data_dir):
                if filename.endswith('.json'):
                    file_path = os.path.join(data_dir, filename)
                    
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            daily_data = json.load(f)
                        
                        # 提取日期
                        date_str = filename.replace('.json', '')
                        
                        # 处理席位详情
                        if 'processed_data' in daily_data and 'seats_detail' in daily_data['processed_data']:
                            for seat in daily_data['processed_data']['seats_detail']:
                                # 处理NaN值
                                for col in ['buy_amount', 'sell_amount', 'net_amount']:
                                    if pd.isna(seat.get(col)) or seat.get(col) is None:
                                        seat[col] = 0.0
                                
                                # 解析股票操作
                                buy_stocks = seat.get('buy_stocks', '')
                                stocks = buy_stocks.split() if isinstance(buy_stocks, str) and buy_stocks else []
                                
                                all_data.append({
                                    'date': date_str,
                                    'seat_name': seat.get('seat_name', ''),
                                    'seat_type': seat.get('seat_type', ''),
                                    'is_famous': seat.get('is_famous', False),
                                    'buy_amount': float(seat.get('buy_amount', 0)),
                                    'sell_amount': float(seat.get('sell_amount', 0)),
                                    'net_amount': float(seat.get('net_amount', 0)),
                                    'stocks': stocks,
                                    'operation_count': len(stocks)
                                })
                    
                    except Exception as e:
                        print(f"⚠️ 处理文件 {filename} 失败: {e}")
                        continue
            
            print(f"📊 加载游资席位数据: {len(all_data)} 条记录")
            return all_data
            
        except Exception as e:
            print(f"❌ 加载游资席位数据失败: {e}")
            return []
    
    def _build_seat_statistics(self, all_data):
        """构建席位基础统计"""
        try:
            df = pd.DataFrame(all_data)
            
            if df.empty:
                return pd.DataFrame()
            
            # 席位基础画像
            seat_stats = df.groupby('seat_name').agg(
                total_days=('date', 'nunique'),
                total_operations=('operation_count', 'sum'),
                total_buy=('buy_amount', 'sum'),
                total_sell=('sell_amount', 'sum'),
                avg_net=('net_amount', 'mean'),
                last_active=('date', 'max'),
                is_famous=('is_famous', 'first')
            ).reset_index()
            
            # 计算衍生指标
            seat_stats['buy_ratio'] = seat_stats['total_buy'] / (seat_stats['total_buy'] + seat_stats['total_sell'] + 1e-8)
            seat_stats['operation_intensity'] = seat_stats['total_operations'] / (seat_stats['total_days'] + 1e-8)
            
            # 连板操作特征
            continuation_stats = df.groupby(['seat_name', 'date']).agg(
                daily_operations=('operation_count', 'sum')
            ).reset_index()
            
            continuation_rates = continuation_stats.groupby('seat_name').agg(
                continuation_rate=('daily_operations', lambda x: (x > 1).mean())
            ).reset_index()
            
            # 合并数据
            seat_stats = pd.merge(seat_stats, continuation_rates, on='seat_name', how='left')
            seat_stats['continuation_rate'] = seat_stats['continuation_rate'].fillna(0)
            
            return seat_stats
            
        except Exception as e:
            print(f"❌ 构建席位统计失败: {e}")
            return pd.DataFrame()
    
    def _calculate_hot_money_scores(self, seat_stats):
        """计算游资活跃度评分"""
        try:
            if seat_stats.empty:
                return seat_stats
            
            def calculate_score(row):
                # 基础活跃度 (30%)
                activity_score = np.log1p(row['total_operations']) * 0.03
                
                # 资金规模 (25%)
                size_score = np.log1p(row['total_buy']) * 0.000000025
                
                # 操作集中度 (20%)
                concentration_score = (1 - 1/(1+row['operation_intensity'])) * 0.2
                
                # 连板能力 (15%)
                continuation_score = row.get('continuation_rate', 0) * 0.15
                
                # 近期活跃 (10%)
                try:
                    last_active = pd.to_datetime(row['last_active'])
                    days_since = (pd.Timestamp.today() - last_active).days
                    recency_score = 0.1 if days_since < 30 else 0.01
                except:
                    recency_score = 0.01
                
                return min(activity_score + size_score + concentration_score + continuation_score + recency_score, 1.0)
            
            seat_stats['hot_score'] = seat_stats.apply(calculate_score, axis=1)
            
            return seat_stats
            
        except Exception as e:
            print(f"❌ 计算游资活跃度评分失败: {e}")
            return seat_stats
    
    def _build_stock_trader_matrix(self, all_data):
        """构建股票-游资关联矩阵"""
        try:
            # 展开股票列表
            stock_records = []
            
            for record in all_data:
                for stock_code in record['stocks']:
                    if stock_code:  # 确保股票代码不为空
                        stock_records.append({
                            'stock_code': stock_code,
                            'seat_name': record['seat_name'],
                            'date': record['date'],
                            'buy_amount': record['buy_amount'],
                            'operation_count': 1
                        })
            
            if not stock_records:
                return pd.DataFrame()
            
            stock_df = pd.DataFrame(stock_records)
            
            # 构建股票-席位关联矩阵
            stock_trader_matrix = stock_df.groupby(['stock_code', 'seat_name']).agg(
                operation_count=('operation_count', 'sum'),
                total_buy=('buy_amount', 'sum'),
                last_operation=('date', 'max')
            ).reset_index()
            
            return stock_trader_matrix
            
        except Exception as e:
            print(f"❌ 构建股票-游资关联矩阵失败: {e}")
            return pd.DataFrame()
    
    def _identify_top_traders(self, seat_stats):
        """识别顶级游资"""
        try:
            if seat_stats.empty:
                return pd.DataFrame()
            
            # 筛选顶级游资
            top_traders = seat_stats[
                (seat_stats['hot_score'] > 0.3) & 
                (seat_stats['total_buy'] > 1e7)  # 总买入金额超过1000万
            ].sort_values('hot_score', ascending=False)
            
            # 选择关键字段
            top_traders = top_traders[[
                'seat_name', 'hot_score', 'total_buy', 
                'operation_intensity', 'last_active', 'is_famous'
            ]].copy()
            
            return top_traders
            
        except Exception as e:
            print(f"❌ 识别顶级游资失败: {e}")
            return pd.DataFrame()
    
    def get_stock_youzi_score(self, stock_code):
        """获取股票的游资活跃度评分"""
        try:
            if self.stock_trader_matrix is None or self.top_traders is None:
                return 0.0
            
            # 查找操作该股票的游资
            stock_traders = self.stock_trader_matrix[
                self.stock_trader_matrix['stock_code'] == stock_code
            ]
            
            if stock_traders.empty:
                return 0.0
            
            # 计算游资质量评分
            total_score = 0.0
            
            for _, trader_record in stock_traders.iterrows():
                seat_name = trader_record['seat_name']
                
                # 查找该席位在顶级游资中的评分
                top_trader_record = self.top_traders[
                    self.top_traders['seat_name'] == seat_name
                ]
                
                if not top_trader_record.empty:
                    hot_score = top_trader_record['hot_score'].iloc[0]
                    operation_weight = min(trader_record['operation_count'] / 10.0, 1.0)
                    total_score += hot_score * operation_weight
            
            return min(total_score, 1.0)
            
        except Exception as e:
            print(f"⚠️ 获取股票游资评分失败 {stock_code}: {e}")
            return 0.0
    
    def get_processing_summary(self):
        """获取处理摘要"""
        try:
            if not self.processed_data:
                return "未进行数据处理"
            
            summary = f"""
📊 游资席位数据处理摘要:
   处理时间: {self.processed_data['process_time']}
   顶级游资: {len(self.top_traders)} 家
   股票关联: {len(self.stock_trader_matrix)} 条记录
   
🏆 顶级游资前5名:
"""
            
            if not self.top_traders.empty:
                for i, (_, trader) in enumerate(self.top_traders.head(5).iterrows()):
                    summary += f"   {i+1}. {trader['seat_name'][:20]}... (评分: {trader['hot_score']:.3f})\n"
            
            return summary
            
        except Exception as e:
            return f"获取处理摘要失败: {e}"

def test_youzi_data_processor():
    """测试游资数据处理器"""
    print("🧪 测试游资数据处理器")
    
    try:
        # 这里需要传入真实的youzi_seats_manager
        # processor = YouziDataProcessor(youzi_seats_manager)
        # success = processor.process_all_data()
        # print(processor.get_processing_summary())
        
        print("✅ 游资数据处理器测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_youzi_data_processor()
