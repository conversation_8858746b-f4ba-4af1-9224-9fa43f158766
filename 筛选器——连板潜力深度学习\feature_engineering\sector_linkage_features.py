#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块联动特征工程
开发板块联动特征，分析股票与所属板块的联动强度和领先滞后关系
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SectorLinkageFeatureEngine:
    """板块联动特征工程器"""
    
    def __init__(self):
        # 板块分类配置
        self.sector_mapping = {
            # 科技板块
            'technology': {
                'keywords': ['科技', '软件', '计算机', '电子', '通信', '互联网', '人工智能', '芯片', '半导体'],
                'weight': 1.0,
                'volatility_factor': 1.2
            },
            # 新能源板块
            'new_energy': {
                'keywords': ['新能源', '电池', '锂电', '光伏', '风电', '储能', '充电桩', '电动车'],
                'weight': 1.0,
                'volatility_factor': 1.3
            },
            # 医药板块
            'healthcare': {
                'keywords': ['医药', '生物', '医疗', '疫苗', '器械', 'CRO', '医院'],
                'weight': 0.9,
                'volatility_factor': 1.1
            },
            # 金融板块
            'finance': {
                'keywords': ['银行', '保险', '券商', '信托', '金融', '支付'],
                'weight': 0.8,
                'volatility_factor': 0.8
            },
            # 消费板块
            'consumption': {
                'keywords': ['消费', '白酒', '食品', '零售', '服装', '家电', '汽车'],
                'weight': 0.9,
                'volatility_factor': 0.9
            },
            # 基建板块
            'infrastructure': {
                'keywords': ['基建', '建筑', '水泥', '钢铁', '房地产', '工程'],
                'weight': 0.7,
                'volatility_factor': 0.7
            }
        }
        
        # 联动分析参数
        self.linkage_params = {
            'correlation_window': 20,       # 相关性计算窗口
            'lead_lag_window': 5,          # 领先滞后分析窗口
            'strength_threshold': 0.6,      # 联动强度阈值
            'relative_strength_period': 10, # 相对强度计算周期
            'momentum_period': 5           # 动量分析周期
        }
        
        # 联动特征权重
        self.linkage_weights = {
            'correlation_strength': 0.3,    # 相关性强度
            'relative_performance': 0.25,   # 相对表现
            'lead_lag_relationship': 0.2,   # 领先滞后关系
            'sector_momentum': 0.15,        # 板块动量
            'cross_sector_influence': 0.1   # 跨板块影响
        }
        
        logger.info("🔗 板块联动特征工程器初始化完成")
    
    def extract_sector_linkage_features(self, stock_code: str, stock_name: str, 
                                      stock_data: pd.DataFrame, 
                                      sector_data: Dict[str, pd.DataFrame] = None) -> Dict[str, Any]:
        """提取板块联动特征"""
        try:
            if stock_data.empty:
                return self._get_empty_linkage_features()
            
            logger.info(f"🔗 开始提取{stock_code}({stock_name})的板块联动特征...")
            
            features = {}
            
            # 1. 识别股票所属板块
            stock_sectors = self._identify_stock_sectors(stock_name)
            features['identified_sectors'] = stock_sectors
            
            # 2. 获取板块数据
            if sector_data is None:
                sector_data = self._get_sector_data(stock_sectors)
            
            # 3. 计算与主要板块的联动特征
            if sector_data:
                correlation_features = self._extract_correlation_features(stock_data, sector_data, stock_sectors)
                features.update(correlation_features)
                
                # 4. 相对强度特征
                relative_features = self._extract_relative_strength_features(stock_data, sector_data, stock_sectors)
                features.update(relative_features)
                
                # 5. 领先滞后关系特征
                lead_lag_features = self._extract_lead_lag_features(stock_data, sector_data, stock_sectors)
                features.update(lead_lag_features)
                
                # 6. 板块动量特征
                momentum_features = self._extract_sector_momentum_features(stock_data, sector_data, stock_sectors)
                features.update(momentum_features)
                
                # 7. 跨板块影响特征
                cross_sector_features = self._extract_cross_sector_features(stock_data, sector_data)
                features.update(cross_sector_features)
                
                # 8. 综合联动评分
                comprehensive_score = self._calculate_comprehensive_linkage_score(features)
                features.update(comprehensive_score)
                
                features['sector_linkage_analysis_success'] = True
            else:
                features = self._get_empty_linkage_features()
                features['error'] = '无法获取板块数据'
            
            features['total_linkage_features'] = len([k for k in features.keys() if not k.startswith('sector_linkage_')])
            
            logger.info(f"✅ {stock_code}板块联动特征提取完成，共{features['total_linkage_features']}个特征")
            return features
            
        except Exception as e:
            logger.error(f"板块联动特征提取失败: {e}")
            return self._get_empty_linkage_features()
    
    def _identify_stock_sectors(self, stock_name: str) -> List[str]:
        """识别股票所属板块"""
        try:
            identified_sectors = []
            
            for sector_name, sector_config in self.sector_mapping.items():
                keywords = sector_config['keywords']
                
                # 检查股票名称是否包含板块关键词
                for keyword in keywords:
                    if keyword in stock_name:
                        identified_sectors.append(sector_name)
                        break
            
            # 如果没有识别到特定板块，归类为综合板块
            if not identified_sectors:
                identified_sectors.append('general')
            
            return identified_sectors
            
        except Exception as e:
            logger.error(f"识别股票板块失败: {e}")
            return ['general']
    
    def _get_sector_data(self, stock_sectors: List[str]) -> Dict[str, pd.DataFrame]:
        """获取板块数据（模拟实现）"""
        try:
            sector_data = {}
            
            # 这里应该从实际数据源获取板块指数数据
            # 现在使用模拟数据进行演示
            for sector in stock_sectors:
                if sector != 'general':
                    # 生成模拟板块数据
                    dates = pd.date_range(end=datetime.now(), periods=30, freq='D')
                    
                    # 模拟板块价格走势
                    np.random.seed(hash(sector) % 1000)
                    base_price = 1000
                    volatility = self.sector_mapping.get(sector, {}).get('volatility_factor', 1.0) * 0.02
                    
                    prices = []
                    for i in range(30):
                        change = np.random.randn() * volatility
                        base_price *= (1 + change)
                        prices.append(base_price)
                    
                    sector_data[sector] = pd.DataFrame({
                        'date': dates,
                        'close': prices,
                        'change_pct': pd.Series(prices).pct_change()
                    })
            
            return sector_data
            
        except Exception as e:
            logger.error(f"获取板块数据失败: {e}")
            return {}
    
    def _extract_correlation_features(self, stock_data: pd.DataFrame, 
                                    sector_data: Dict[str, pd.DataFrame], 
                                    stock_sectors: List[str]) -> Dict[str, float]:
        """提取相关性特征"""
        try:
            features = {}
            
            if len(stock_data) < self.linkage_params['correlation_window']:
                return features
            
            # 计算股票收益率
            stock_returns = stock_data['close'].pct_change().dropna()
            
            correlations = []
            
            for sector in stock_sectors:
                if sector in sector_data and sector != 'general':
                    sector_df = sector_data[sector]
                    
                    if len(sector_df) >= len(stock_returns):
                        # 对齐数据长度
                        min_length = min(len(stock_returns), len(sector_df))
                        stock_ret = stock_returns.tail(min_length)
                        sector_ret = sector_df['change_pct'].tail(min_length).dropna()
                        
                        if len(stock_ret) >= 10 and len(sector_ret) >= 10:
                            # 计算相关系数
                            correlation = stock_ret.corr(sector_ret)
                            if not pd.isna(correlation):
                                correlations.append(correlation)
                                features[f'{sector}_correlation'] = correlation
                                features[f'{sector}_high_correlation'] = 1.0 if abs(correlation) > self.linkage_params['strength_threshold'] else 0.0
            
            if correlations:
                features.update({
                    'max_sector_correlation': max(correlations),
                    'avg_sector_correlation': np.mean(correlations),
                    'correlation_stability': 1.0 - np.std(correlations),
                    'strong_sector_linkage': 1.0 if max(correlations) > self.linkage_params['strength_threshold'] else 0.0
                })
            
            return features
            
        except Exception as e:
            logger.error(f"相关性特征提取失败: {e}")
            return {}
    
    def _extract_relative_strength_features(self, stock_data: pd.DataFrame, 
                                          sector_data: Dict[str, pd.DataFrame], 
                                          stock_sectors: List[str]) -> Dict[str, float]:
        """提取相对强度特征"""
        try:
            features = {}
            
            period = self.linkage_params['relative_strength_period']
            if len(stock_data) < period:
                return features
            
            # 计算股票相对强度
            stock_return = (stock_data['close'].iloc[-1] - stock_data['close'].iloc[-period]) / stock_data['close'].iloc[-period]
            
            relative_strengths = []
            
            for sector in stock_sectors:
                if sector in sector_data and sector != 'general':
                    sector_df = sector_data[sector]
                    
                    if len(sector_df) >= period:
                        sector_return = (sector_df['close'].iloc[-1] - sector_df['close'].iloc[-period]) / sector_df['close'].iloc[-period]
                        
                        # 相对强度 = 股票收益率 - 板块收益率
                        relative_strength = stock_return - sector_return
                        relative_strengths.append(relative_strength)
                        
                        features[f'{sector}_relative_strength'] = relative_strength
                        features[f'{sector}_outperform'] = 1.0 if relative_strength > 0.02 else 0.0
            
            if relative_strengths:
                features.update({
                    'max_relative_strength': max(relative_strengths),
                    'avg_relative_strength': np.mean(relative_strengths),
                    'consistent_outperformance': 1.0 if all(rs > 0 for rs in relative_strengths) else 0.0,
                    'strong_relative_performance': 1.0 if max(relative_strengths) > 0.05 else 0.0
                })
            
            return features
            
        except Exception as e:
            logger.error(f"相对强度特征提取失败: {e}")
            return {}
    
    def _extract_lead_lag_features(self, stock_data: pd.DataFrame, 
                                 sector_data: Dict[str, pd.DataFrame], 
                                 stock_sectors: List[str]) -> Dict[str, float]:
        """提取领先滞后关系特征"""
        try:
            features = {}
            
            window = self.linkage_params['lead_lag_window']
            if len(stock_data) < window * 2:
                return features
            
            stock_returns = stock_data['close'].pct_change().dropna()
            
            lead_lag_results = []
            
            for sector in stock_sectors:
                if sector in sector_data and sector != 'general':
                    sector_df = sector_data[sector]
                    sector_returns = sector_df['change_pct'].dropna()
                    
                    if len(sector_returns) >= len(stock_returns):
                        # 对齐数据
                        min_length = min(len(stock_returns), len(sector_returns))
                        stock_ret = stock_returns.tail(min_length)
                        sector_ret = sector_returns.tail(min_length)
                        
                        # 计算不同滞后期的相关性
                        correlations = []
                        for lag in range(-window, window + 1):
                            if lag == 0:
                                corr = stock_ret.corr(sector_ret)
                            elif lag > 0:
                                # 股票滞后于板块
                                if len(stock_ret) > lag:
                                    corr = stock_ret.iloc[lag:].corr(sector_ret.iloc[:-lag])
                            else:
                                # 股票领先于板块
                                lag_abs = abs(lag)
                                if len(sector_ret) > lag_abs:
                                    corr = stock_ret.iloc[:-lag_abs].corr(sector_ret.iloc[lag_abs:])
                            
                            if not pd.isna(corr):
                                correlations.append((lag, corr))
                        
                        if correlations:
                            # 找到最大相关性对应的滞后期
                            best_lag, best_corr = max(correlations, key=lambda x: abs(x[1]))
                            
                            features[f'{sector}_best_lag'] = best_lag
                            features[f'{sector}_best_correlation'] = best_corr
                            features[f'{sector}_leads_sector'] = 1.0 if best_lag < 0 else 0.0
                            features[f'{sector}_lags_sector'] = 1.0 if best_lag > 0 else 0.0
                            
                            lead_lag_results.append((best_lag, best_corr))
            
            if lead_lag_results:
                avg_lag = np.mean([result[0] for result in lead_lag_results])
                features.update({
                    'avg_lead_lag': avg_lag,
                    'generally_leads_sectors': 1.0 if avg_lag < -0.5 else 0.0,
                    'generally_lags_sectors': 1.0 if avg_lag > 0.5 else 0.0,
                    'synchronous_with_sectors': 1.0 if abs(avg_lag) <= 0.5 else 0.0
                })
            
            return features
            
        except Exception as e:
            logger.error(f"领先滞后关系特征提取失败: {e}")
            return {}

    def _extract_sector_momentum_features(self, stock_data: pd.DataFrame,
                                        sector_data: Dict[str, pd.DataFrame],
                                        stock_sectors: List[str]) -> Dict[str, float]:
        """提取板块动量特征"""
        try:
            features = {}

            momentum_period = self.linkage_params['momentum_period']
            if len(stock_data) < momentum_period * 2:
                return features

            # 计算股票动量
            stock_momentum = (stock_data['close'].iloc[-1] - stock_data['close'].iloc[-momentum_period]) / stock_data['close'].iloc[-momentum_period]

            sector_momentums = []

            for sector in stock_sectors:
                if sector in sector_data and sector != 'general':
                    sector_df = sector_data[sector]

                    if len(sector_df) >= momentum_period:
                        # 计算板块动量
                        sector_momentum = (sector_df['close'].iloc[-1] - sector_df['close'].iloc[-momentum_period]) / sector_df['close'].iloc[-momentum_period]
                        sector_momentums.append(sector_momentum)

                        features[f'{sector}_momentum'] = sector_momentum
                        features[f'{sector}_positive_momentum'] = 1.0 if sector_momentum > 0.02 else 0.0

                        # 动量一致性
                        momentum_consistency = 1.0 if (stock_momentum > 0 and sector_momentum > 0) or (stock_momentum < 0 and sector_momentum < 0) else 0.0
                        features[f'{sector}_momentum_consistency'] = momentum_consistency

            if sector_momentums:
                avg_sector_momentum = np.mean(sector_momentums)
                features.update({
                    'avg_sector_momentum': avg_sector_momentum,
                    'sectors_positive_momentum': 1.0 if avg_sector_momentum > 0.01 else 0.0,
                    'stock_sector_momentum_alignment': 1.0 if (stock_momentum > 0 and avg_sector_momentum > 0) else 0.0,
                    'momentum_amplification': stock_momentum / avg_sector_momentum if avg_sector_momentum != 0 else 0
                })

            return features

        except Exception as e:
            logger.error(f"板块动量特征提取失败: {e}")
            return {}

    def _extract_cross_sector_features(self, stock_data: pd.DataFrame,
                                     sector_data: Dict[str, pd.DataFrame]) -> Dict[str, float]:
        """提取跨板块影响特征"""
        try:
            features = {}

            if len(sector_data) < 2:
                return features

            # 计算不同板块之间的相关性
            sector_correlations = []
            sector_names = list(sector_data.keys())

            for i in range(len(sector_names)):
                for j in range(i + 1, len(sector_names)):
                    sector1 = sector_names[i]
                    sector2 = sector_names[j]

                    if sector1 != 'general' and sector2 != 'general':
                        df1 = sector_data[sector1]
                        df2 = sector_data[sector2]

                        if len(df1) >= 10 and len(df2) >= 10:
                            min_length = min(len(df1), len(df2))
                            ret1 = df1['change_pct'].tail(min_length).dropna()
                            ret2 = df2['change_pct'].tail(min_length).dropna()

                            if len(ret1) >= 5 and len(ret2) >= 5:
                                corr = ret1.corr(ret2)
                                if not pd.isna(corr):
                                    sector_correlations.append(abs(corr))

            if sector_correlations:
                features.update({
                    'cross_sector_avg_correlation': np.mean(sector_correlations),
                    'cross_sector_max_correlation': max(sector_correlations),
                    'high_cross_sector_linkage': 1.0 if max(sector_correlations) > 0.7 else 0.0,
                    'market_wide_correlation': 1.0 if np.mean(sector_correlations) > 0.5 else 0.0
                })

            # 分析板块轮动效应
            if len(sector_data) >= 3:
                sector_returns = {}
                for sector, df in sector_data.items():
                    if sector != 'general' and len(df) >= 5:
                        recent_return = (df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5]
                        sector_returns[sector] = recent_return

                if len(sector_returns) >= 2:
                    returns_list = list(sector_returns.values())
                    returns_std = np.std(returns_list)

                    features.update({
                        'sector_rotation_intensity': returns_std,
                        'active_sector_rotation': 1.0 if returns_std > 0.05 else 0.0,
                        'best_performing_sector_return': max(returns_list),
                        'worst_performing_sector_return': min(returns_list)
                    })

            return features

        except Exception as e:
            logger.error(f"跨板块影响特征提取失败: {e}")
            return {}

    def _calculate_comprehensive_linkage_score(self, features: Dict[str, float]) -> Dict[str, float]:
        """计算综合联动评分"""
        try:
            score_components = {}

            # 1. 相关性强度评分
            correlation_score = 0.0
            if 'max_sector_correlation' in features:
                correlation_score = min(abs(features['max_sector_correlation']), 1.0)
            score_components['correlation_strength_score'] = correlation_score

            # 2. 相对表现评分
            relative_score = 0.0
            if 'max_relative_strength' in features:
                # 相对强度转换为0-1评分
                relative_strength = features['max_relative_strength']
                relative_score = min(max(relative_strength * 5 + 0.5, 0), 1.0)
            score_components['relative_performance_score'] = relative_score

            # 3. 领先滞后关系评分
            lead_lag_score = 0.0
            if 'generally_leads_sectors' in features and features['generally_leads_sectors'] > 0:
                lead_lag_score = 0.8  # 领先板块是好事
            elif 'synchronous_with_sectors' in features and features['synchronous_with_sectors'] > 0:
                lead_lag_score = 0.6  # 同步也不错
            elif 'generally_lags_sectors' in features and features['generally_lags_sectors'] > 0:
                lead_lag_score = 0.3  # 滞后相对较差
            score_components['lead_lag_relationship_score'] = lead_lag_score

            # 4. 板块动量评分
            momentum_score = 0.0
            if 'stock_sector_momentum_alignment' in features and features['stock_sector_momentum_alignment'] > 0:
                momentum_score += 0.5
            if 'sectors_positive_momentum' in features and features['sectors_positive_momentum'] > 0:
                momentum_score += 0.5
            score_components['sector_momentum_score'] = momentum_score

            # 5. 跨板块影响评分
            cross_sector_score = 0.0
            if 'market_wide_correlation' in features and features['market_wide_correlation'] > 0:
                cross_sector_score += 0.3
            if 'active_sector_rotation' in features and features['active_sector_rotation'] > 0:
                cross_sector_score += 0.4
            if 'best_performing_sector_return' in features and features['best_performing_sector_return'] > 0.03:
                cross_sector_score += 0.3
            score_components['cross_sector_influence_score'] = cross_sector_score

            # 计算加权综合评分
            comprehensive_score = 0.0
            for component, weight in self.linkage_weights.items():
                score_key = f"{component}_score"
                if score_key in score_components:
                    comprehensive_score += score_components[score_key] * weight

            return {
                'sector_linkage_comprehensive_score': comprehensive_score,
                'sector_linkage_bullish': 1.0 if comprehensive_score > 0.6 else 0.0,
                'sector_linkage_bearish': 1.0 if comprehensive_score < 0.3 else 0.0,
                **score_components
            }

        except Exception as e:
            logger.error(f"综合联动评分计算失败: {e}")
            return {'sector_linkage_comprehensive_score': 0.0}

    def _get_empty_linkage_features(self) -> Dict[str, Any]:
        """获取空的板块联动特征"""
        return {
            'sector_linkage_analysis_success': False,
            'total_linkage_features': 0,
            'sector_linkage_comprehensive_score': 0.0,
            'identified_sectors': [],
            'error': '板块联动特征提取失败'
        }

def main():
    """测试板块联动特征工程器"""
    print("🔗 测试板块联动特征工程器...")

    engine = SectorLinkageFeatureEngine()

    # 创建测试股票数据
    dates = pd.date_range('2024-05-01', periods=30, freq='D')
    np.random.seed(42)

    # 模拟科技股数据
    base_price = 50.0
    prices = []

    for i in range(30):
        # 模拟科技股的高波动特征
        price_change = np.random.randn() * 0.03 + 0.001
        base_price *= (1 + price_change)
        prices.append(base_price)

    test_stock_data = pd.DataFrame({
        'date': dates,
        'close': prices
    })

    # 测试特征提取
    print(f"\n🔍 测试板块联动特征提取...")
    stock_code = '000001'
    stock_name = '科技软件'  # 包含科技关键词

    features = engine.extract_sector_linkage_features(stock_code, stock_name, test_stock_data)

    print(f"✅ 特征提取完成")
    print(f"分析成功: {features.get('sector_linkage_analysis_success', False)}")
    print(f"识别板块: {features.get('identified_sectors', [])}")
    print(f"总特征数: {features.get('total_linkage_features', 0)}")

    # 显示关键特征
    key_features = ['max_sector_correlation', 'max_relative_strength', 'avg_lead_lag',
                   'avg_sector_momentum', 'sector_linkage_comprehensive_score']

    print(f"\n🔗 关键板块联动特征:")
    for feature in key_features:
        if feature in features:
            print(f"  {feature}: {features[feature]:.3f}")

if __name__ == "__main__":
    main()
