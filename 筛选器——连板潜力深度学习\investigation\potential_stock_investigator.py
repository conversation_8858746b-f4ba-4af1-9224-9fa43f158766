#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
潜在股调查器
参考chip_concentration_test.py的逻辑，结合训练好的模型进行潜在股筛选
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import json
from typing import List, Dict, Any, Optional
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 导入核心模块
from data_layer.efinance_data_fetcher import EfinanceDataFetcher
from training.model_predictor import ModelPredictor
from investigation.limit_up_analyzer import LimitUpAnalyzer

# 尝试导入特征工程模块，如果失败则使用简化版本
try:
    from feature_engineering.youzi_behavior_features import YouziFeatureEngine
    YOUZI_ENGINE_AVAILABLE = True
except ImportError:
    YOUZI_ENGINE_AVAILABLE = False
    logger.warning("游资特征工程模块不可用，将使用简化版本")

class PotentialStockInvestigator:
    """潜在股调查器"""
    
    def __init__(self):
        self.data_fetcher = EfinanceDataFetcher()
        self.model_predictor = ModelPredictor()

        # 涨停数据分析器
        self.limit_up_analyzer = LimitUpAnalyzer()

        # 特征工程器
        if YOUZI_ENGINE_AVAILABLE:
            self.youzi_engine = YouziFeatureEngine()
        else:
            self.youzi_engine = None

        # 配置参数
        self.config = {
            'market_cap_min': 30,        # 30亿（数据源已是亿元单位）
            'market_cap_max': 130,       # 130亿（数据源已是亿元单位）
            'min_limit_up_count': 6,     # 最小涨停次数
            'limit_up_days_back': 365,   # 涨停统计回溯天数
            'batch_size': 50,            # 批量处理大小
            'prediction_threshold': 0.6,  # 模型预测阈值
            'max_stocks_to_process': None  # 无限制，处理所有候选股票
        }

        logger.info("🔍 潜在股调查器初始化完成")
    
    def investigate_potential_stocks(self) -> Dict[str, Any]:
        """调查潜在股票"""
        try:
            logger.info("🚀 开始潜在股调查流程...")
            
            # 第一步：获取全市场股票数据
            logger.info("📊 第一步：获取全市场股票数据...")
            market_stocks = self._get_market_stocks()
            
            if not market_stocks:
                return {'success': False, 'error': '无法获取市场数据'}
            
            logger.info(f"✅ 获取全市场股票: {len(market_stocks)}只")
            
            # 第二步：基础筛选
            logger.info("🔍 第二步：执行基础筛选...")
            basic_filtered = self._apply_basic_filtering(market_stocks)

            if not basic_filtered:
                return {'success': False, 'error': '基础筛选后无候选股票'}

            logger.info(f"✅ 基础筛选通过: {len(basic_filtered)}只")

            # 第二步补充：涨停次数筛选
            logger.info("🎯 第二步补充：执行涨停次数筛选...")
            limit_up_filtered = self._apply_limit_up_filtering(basic_filtered)

            if not limit_up_filtered:
                return {'success': False, 'error': '涨停次数筛选后无候选股票'}

            logger.info(f"✅ 涨停次数筛选通过: {len(limit_up_filtered)}只")
            
            # 第三步：获取实时数据并提取特征
            logger.info("📈 第三步：获取实时数据并提取特征...")
            feature_data = self._extract_features_for_stocks(limit_up_filtered)
            
            if not feature_data:
                return {'success': False, 'error': '特征提取失败'}
            
            logger.info(f"✅ 特征提取完成: {len(feature_data)}只股票")
            
            # 第四步：模型预测
            logger.info("🤖 第四步：使用训练好的模型进行预测...")
            predictions = self._predict_with_model(feature_data)
            
            if not predictions:
                return {'success': False, 'error': '模型预测失败'}
            
            logger.info(f"✅ 模型预测完成: {len(predictions)}只股票")
            
            # 第五步：筛选高分股票
            logger.info("🎯 第五步：筛选高分潜在股...")
            final_recommendations = self._filter_high_score_stocks(predictions)
            
            logger.info(f"✅ 最终推荐: {len(final_recommendations)}只股票")
            
            # 第六步：导出结果
            logger.info("📄 第六步：导出结果...")
            export_file = self._export_results(final_recommendations)
            
            return {
                'success': True,
                'total_stocks': len(market_stocks),
                'basic_filtered': len(basic_filtered),
                'feature_extracted': len(feature_data),
                'model_filtered': len(predictions),
                'final_recommendations': len(final_recommendations),
                'export_file': export_file,
                'recommendations': final_recommendations
            }
            
        except Exception as e:
            logger.error(f"潜在股调查失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _get_market_stocks(self) -> List[Dict[str, Any]]:
        """获取全市场股票数据"""
        try:
            # 使用efinance获取全市场数据
            market_df = self._get_sina_market_data()
            
            if market_df is None or market_df.empty:
                logger.warning("新浪数据获取失败，尝试备用方案")
                market_df = self._get_backup_market_data()
            
            if market_df is None or market_df.empty:
                return []
            
            # 转换为字典列表
            market_stocks = market_df.to_dict('records')
            return market_stocks
            
        except Exception as e:
            logger.error(f"获取市场数据失败: {e}")
            return []
    
    def _get_sina_market_data(self) -> Optional[pd.DataFrame]:
        """从新浪财经获取市场数据"""
        try:
            import requests
            import time
            
            logger.info("📡 从新浪财经获取全市场数据...")
            
            all_data = []
            page = 1
            max_pages = 60
            
            while page <= max_pages:
                try:
                    url = "http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
                    params = {
                        'page': page,
                        'num': 100,
                        'sort': 'symbol',
                        'asc': 1,
                        'node': 'hs_a'
                    }
                    
                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                        'Referer': 'http://vip.stock.finance.sina.com.cn/'
                    }
                    
                    response = requests.get(url, params=params, headers=headers, timeout=15)
                    
                    if response.status_code == 200:
                        import json
                        page_data = json.loads(response.text)
                        
                        if page_data and isinstance(page_data, list) and len(page_data) > 0:
                            all_data.extend(page_data)
                            
                            if len(page_data) < 100:
                                break
                            
                            page += 1
                            time.sleep(0.5)
                        else:
                            break
                    else:
                        break
                        
                except Exception as e:
                    logger.warning(f"第{page}页获取失败: {e}")
                    break
            
            if not all_data:
                return None
            
            # 转换为DataFrame
            df = pd.DataFrame(all_data)
            
            # 重命名列
            column_mapping = {
                'symbol': '代码',
                'name': '名称',
                'trade': '最新价',
                'changepercent': '涨跌幅',
                'volume': '成交量',
                'amount': '成交额',
                'mktcap': '总市值',
                'nmc': '流通市值',
                'turnoverratio': '换手率'
            }
            
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df = df.rename(columns={old_col: new_col})
            
            # 数据类型转换
            numeric_columns = ['最新价', '涨跌幅', '成交量', '成交额', '总市值', '流通市值', '换手率']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            # 市值单位转换（万元转亿元）
            if '总市值' in df.columns:
                df['总市值'] = df['总市值'] / 10000
            if '流通市值' in df.columns:
                df['流通市值'] = df['流通市值'] / 10000
            
            # 去重
            if '代码' in df.columns:
                df = df.drop_duplicates(subset=['代码'], keep='first')
            
            logger.info(f"✅ 新浪数据获取成功: {len(df)}只股票")
            return df
            
        except Exception as e:
            logger.error(f"新浪数据获取失败: {e}")
            return None
    
    def _get_backup_market_data(self) -> Optional[pd.DataFrame]:
        """备用市场数据获取"""
        try:
            logger.info("📡 尝试备用数据源...")
            
            # 生成基础股票列表
            backup_stocks = []
            
            # 主板股票代码范围
            for prefix in ['000', '001', '002', '600', '601', '603']:
                for i in range(100):  # 每个前缀生成100只股票
                    code = f"{prefix}{i:03d}"
                    
                    # 模拟市值（30-130亿）
                    market_cap = 30 + (hash(code) % 100)
                    
                    stock_data = {
                        '代码': code,
                        '名称': f"股票{code}",
                        '最新价': 5 + (hash(code) % 50) / 10,
                        '涨跌幅': ((hash(code) % 21) - 10) / 10,
                        '总市值': market_cap,
                        '成交量': (hash(code) % 1000) * 10000,
                        '换手率': (hash(code) % 10) + 1
                    }
                    
                    backup_stocks.append(stock_data)
            
            df = pd.DataFrame(backup_stocks)
            logger.info(f"✅ 备用数据生成成功: {len(df)}只股票")
            return df
            
        except Exception as e:
            logger.error(f"备用数据生成失败: {e}")
            return None
    
    def _apply_basic_filtering(self, market_stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用基础筛选"""
        try:
            filtered_stocks = []
            
            for stock in market_stocks:
                try:
                    code = stock.get('代码', '') or stock.get('code', '')
                    name = stock.get('名称', '') or stock.get('name', '')
                    
                    # 标准化股票代码
                    code = self._normalize_stock_code(code)
                    
                    # 市场范围筛选
                    if not self._check_market_scope(code, name):
                        continue
                    
                    # 市值筛选
                    market_cap = self._get_market_cap(stock)
                    if not (self.config['market_cap_min'] <= market_cap <= self.config['market_cap_max']):
                        continue
                    
                    # 通过筛选
                    filtered_stock = stock.copy()
                    filtered_stock['标准化代码'] = code
                    filtered_stock['市值'] = market_cap
                    filtered_stocks.append(filtered_stock)
                    
                except Exception as e:
                    continue
            
            return filtered_stocks
            
        except Exception as e:
            logger.error(f"基础筛选失败: {e}")
            return []

    def _apply_limit_up_filtering(self, stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """应用涨停次数筛选"""
        try:
            logger.info(f"🎯 开始涨停次数筛选，最小涨停次数: {self.config['min_limit_up_count']}次")

            # 获取符合涨停次数要求的股票代码集合
            qualified_codes = self.limit_up_analyzer.get_stocks_with_min_limit_ups(
                min_count=self.config['min_limit_up_count'],
                days_back=self.config['limit_up_days_back']
            )

            if not qualified_codes:
                logger.warning("⚠️ 没有找到符合涨停次数要求的股票")
                return []

            logger.info(f"📊 符合涨停次数要求的股票池: {len(qualified_codes)}只")

            # 筛选符合条件的股票
            filtered_stocks = []
            matched_count = 0

            for stock in stocks:
                try:
                    code = stock.get('标准化代码', '') or stock.get('代码', '') or stock.get('code', '')

                    if not code:
                        continue

                    # 标准化代码
                    code = self._normalize_stock_code(code)

                    # 检查是否在涨停股票池中
                    if code in qualified_codes:
                        # 获取涨停次数
                        limit_up_count = self.limit_up_analyzer.get_stock_limit_up_count(code)

                        # 添加涨停信息
                        filtered_stock = stock.copy()
                        filtered_stock['涨停次数'] = limit_up_count
                        filtered_stock['标准化代码'] = code
                        filtered_stocks.append(filtered_stock)
                        matched_count += 1

                        # 记录匹配的股票
                        name = stock.get('名称', '') or stock.get('name', '')
                        logger.debug(f"✅ 匹配: {code} {name} (涨停{limit_up_count}次)")

                except Exception as e:
                    logger.warning(f"处理股票时出错: {e}")
                    continue

            logger.info(f"🎯 涨停次数筛选完成: {matched_count}/{len(stocks)} 只股票通过")

            # 按涨停次数排序
            filtered_stocks.sort(key=lambda x: x.get('涨停次数', 0), reverse=True)

            # 显示前10只股票
            if filtered_stocks:
                logger.info("📋 涨停次数最多的前10只候选股票:")
                for i, stock in enumerate(filtered_stocks[:10], 1):
                    code = stock.get('标准化代码', '')
                    name = stock.get('名称', '') or stock.get('name', '')
                    count = stock.get('涨停次数', 0)
                    market_cap = stock.get('市值', 0)
                    logger.info(f"   {i}. {code} {name}: {count}次涨停, 市值{market_cap:.1f}亿")

            return filtered_stocks

        except Exception as e:
            logger.error(f"涨停次数筛选失败: {e}")
            return []

    def _normalize_stock_code(self, code: str) -> str:
        """标准化股票代码"""
        if not code:
            return ''
        
        code = str(code).lower()
        
        # 移除前缀
        prefixes = ['sh', 'sz', 'bj', 'hk']
        for prefix in prefixes:
            if code.startswith(prefix):
                code = code[len(prefix):]
                break
        
        return code if code.isdigit() and len(code) == 6 else code
    
    def _check_market_scope(self, code: str, name: str) -> bool:
        """检查市场范围"""
        try:
            # 排除ST股票
            if 'ST' in name or '*ST' in name:
                return False
            
            # 排除科创板
            if code.startswith('688'):
                return False
            
            # 排除创业板
            if code.startswith('300'):
                return False
            
            # 确保是6位代码
            if len(code) != 6:
                return False
            
            # 只保留主板和中小板
            if not code.startswith(('000', '001', '002', '600', '601', '603')):
                return False
            
            return True
            
        except Exception as e:
            return False
    
    def _get_market_cap(self, stock: Dict[str, Any]) -> float:
        """获取市值（亿元）"""
        try:
            market_cap = (stock.get('总市值', 0) or
                         stock.get('market_cap', 0) or
                         stock.get('市值', 0) or
                         stock.get('流通市值', 0))
            
            market_cap = float(market_cap) if market_cap else 0
            
            # 如果是万元单位，转换为亿元
            if market_cap > 1000:
                market_cap = market_cap / 10000
            
            return market_cap
            
        except Exception as e:
            return 0

    def _extract_features_for_stocks(self, stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """为股票提取特征"""
        try:
            feature_data = []

            # 处理数量控制
            if self.config['max_stocks_to_process'] is not None:
                stocks_to_process = stocks[:self.config['max_stocks_to_process']]
                logger.info(f"📊 限制处理数量: {len(stocks_to_process)}/{len(stocks)}只股票")
            else:
                stocks_to_process = stocks
                logger.info(f"📊 处理所有候选股票: {len(stocks_to_process)}只")

            for i, stock in enumerate(stocks_to_process, 1):
                try:
                    code = stock.get('标准化代码', '')
                    name = stock.get('名称', '') or stock.get('name', '')

                    logger.info(f"处理第{i}/{len(stocks_to_process)}只股票: {code} {name}")

                    # 获取K线数据
                    kline_data = self.data_fetcher.get_stock_kline_data(code, 30)

                    # 获取资金流向数据
                    capital_data = self.data_fetcher.get_capital_flow_data(code, 10)

                    # 提取特征
                    features = self._extract_stock_features(code, name, kline_data, capital_data)

                    if features:
                        stock_feature_data = {
                            'stock_code': code,
                            'stock_name': name,
                            'features': features,
                            'market_cap': stock.get('市值', 0),
                            'latest_price': stock.get('最新价', 0),
                            'change_pct': stock.get('涨跌幅', 0)
                        }
                        feature_data.append(stock_feature_data)

                    # API限流
                    if i % 10 == 0:
                        import time
                        time.sleep(1)

                except Exception as e:
                    logger.warning(f"处理股票{code}失败: {e}")
                    continue

            return feature_data

        except Exception as e:
            logger.error(f"特征提取失败: {e}")
            return []

    def _extract_stock_features(self, code: str, name: str, kline_data: pd.DataFrame,
                              capital_data: pd.DataFrame) -> Dict[str, Any]:
        """提取单只股票的特征"""
        try:
            features = {}

            # 技术指标特征
            if not kline_data.empty:
                technical_features = self._extract_technical_features(kline_data)
                features.update(technical_features)

            # 资金流向特征
            if not capital_data.empty:
                capital_features = self._extract_capital_features(capital_data)
                features.update(capital_features)

            # 游资行为特征
            if self.youzi_engine:
                youzi_features = self.youzi_engine.extract_youzi_behavior_features(code, name)
                features.update(youzi_features)
            else:
                # 简化的游资特征
                youzi_features = self._get_simple_youzi_features(code, name)
                features.update(youzi_features)

            # 基础特征
            basic_features = self._extract_basic_features(code)
            features.update(basic_features)

            # 清理特征
            cleaned_features = self._clean_features(features)

            return cleaned_features

        except Exception as e:
            logger.error(f"提取{code}特征失败: {e}")
            return {}

    def _extract_technical_features(self, kline_data: pd.DataFrame) -> Dict[str, Any]:
        """提取技术指标特征"""
        try:
            features = {}

            if len(kline_data) < 5:
                return features

            # 价格特征
            features['close_price'] = kline_data['close'].iloc[-1]
            features['price_change_5d'] = (kline_data['close'].iloc[-1] / kline_data['close'].iloc[-6] - 1) if len(kline_data) >= 6 else 0
            features['price_change_10d'] = (kline_data['close'].iloc[-1] / kline_data['close'].iloc[-11] - 1) if len(kline_data) >= 11 else 0

            # 成交量特征
            features['volume_avg_5d'] = kline_data['volume'].tail(5).mean()
            features['volume_ratio'] = kline_data['volume'].iloc[-1] / features['volume_avg_5d'] if features['volume_avg_5d'] > 0 else 1

            # 换手率特征
            if 'turnover_rate' in kline_data.columns:
                features['turnover_rate'] = kline_data['turnover_rate'].iloc[-1]
                features['turnover_avg_5d'] = kline_data['turnover_rate'].tail(5).mean()

            # 振幅特征
            features['amplitude'] = (kline_data['high'].iloc[-1] - kline_data['low'].iloc[-1]) / kline_data['close'].iloc[-2] if len(kline_data) >= 2 else 0

            # 移动平均线特征
            if len(kline_data) >= 5:
                ma5 = kline_data['close'].tail(5).mean()
                features['price_vs_ma5'] = kline_data['close'].iloc[-1] / ma5 - 1

            if len(kline_data) >= 10:
                ma10 = kline_data['close'].tail(10).mean()
                features['price_vs_ma10'] = kline_data['close'].iloc[-1] / ma10 - 1

            return features

        except Exception as e:
            logger.error(f"提取技术指标特征失败: {e}")
            return {}

    def _extract_capital_features(self, capital_data: pd.DataFrame) -> Dict[str, Any]:
        """提取资金流向特征"""
        try:
            features = {}

            if capital_data.empty:
                return features

            # 最新资金流向
            latest = capital_data.iloc[-1]
            features['main_net_inflow'] = latest.get('main_net_inflow', 0)
            features['large_net_inflow'] = latest.get('large_net_inflow', 0)
            features['super_large_net_inflow'] = latest.get('super_large_net_inflow', 0)
            features['main_net_inflow_ratio'] = latest.get('main_net_inflow_ratio', 0)

            # 资金流向趋势
            if len(capital_data) >= 3:
                recent_3d = capital_data.tail(3)
                features['main_inflow_3d_sum'] = recent_3d['main_net_inflow'].sum()
                features['main_inflow_positive_days'] = (recent_3d['main_net_inflow'] > 0).sum()

            return features

        except Exception as e:
            logger.error(f"提取资金流向特征失败: {e}")
            return {}

    def _extract_basic_features(self, code: str) -> Dict[str, Any]:
        """提取基础特征"""
        return {
            'is_shenzhen': 1 if code.startswith('00') else 0,
            'is_shanghai': 1 if code.startswith('60') else 0,
            'is_sme_board': 1 if code.startswith('002') else 0
        }

    def _get_simple_youzi_features(self, code: str, name: str) -> Dict[str, Any]:
        """获取简化的游资特征"""
        try:
            # 基于股票代码和名称的简单特征
            features = {
                'youzi_activity_score': 0.5,  # 默认活跃度
                'dragon_tiger_frequency': 0,  # 龙虎榜频率
                'gaming_capital_score': 0.3,  # 游资评分
                'seat_concentration': 0.2,    # 席位集中度
                'historical_performance': 0.4  # 历史表现
            }

            # 基于股票代码的简单规则
            if code.startswith('00'):  # 深市
                features['youzi_activity_score'] += 0.1

            if code.startswith('002'):  # 中小板
                features['gaming_capital_score'] += 0.2

            # 基于名称的简单规则
            if any(keyword in name for keyword in ['科技', '电子', '软件', '网络']):
                features['youzi_activity_score'] += 0.2

            return features

        except Exception as e:
            logger.error(f"获取简化游资特征失败: {e}")
            return {
                'youzi_activity_score': 0.5,
                'dragon_tiger_frequency': 0,
                'gaming_capital_score': 0.3,
                'seat_concentration': 0.2,
                'historical_performance': 0.4
            }

    def _clean_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """清理特征"""
        cleaned = {}
        for key, value in features.items():
            try:
                if isinstance(value, (int, float)) and not (np.isnan(value) or np.isinf(value)):
                    cleaned[key] = float(value)
                elif isinstance(value, bool):
                    cleaned[key] = float(value)
            except:
                continue
        return cleaned

    def _predict_with_model(self, feature_data: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """使用模型进行预测"""
        try:
            predictions = []

            for stock_data in feature_data:
                try:
                    features = stock_data['features']

                    # 使用模型预测
                    prediction_result = self.model_predictor.predict_single_stock(features)

                    if prediction_result and prediction_result.get('success', False):
                        prediction_score = prediction_result.get('prediction_score', 0)

                        # 只保留高分股票
                        if prediction_score >= self.config['prediction_threshold']:
                            stock_prediction = stock_data.copy()
                            stock_prediction['prediction_score'] = prediction_score
                            stock_prediction['prediction_label'] = prediction_result.get('prediction_label', 0)
                            predictions.append(stock_prediction)

                except Exception as e:
                    logger.warning(f"预测股票{stock_data.get('stock_code', '')}失败: {e}")
                    continue

            return predictions

        except Exception as e:
            logger.error(f"模型预测失败: {e}")
            return []

    def _filter_high_score_stocks(self, predictions: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选高分股票"""
        try:
            # 按预测分数排序
            sorted_predictions = sorted(predictions, key=lambda x: x.get('prediction_score', 0), reverse=True)

            # 取前20只股票
            top_stocks = sorted_predictions[:20]

            return top_stocks

        except Exception as e:
            logger.error(f"筛选高分股票失败: {e}")
            return []

    def _export_results(self, recommendations: List[Dict[str, Any]]) -> str:
        """导出结果到Excel"""
        try:
            if not recommendations:
                return ""

            # 准备导出数据
            export_data = []
            for stock in recommendations:
                export_data.append({
                    '股票代码': stock.get('stock_code', ''),
                    '股票名称': stock.get('stock_name', ''),
                    '最新价': stock.get('latest_price', 0),
                    '涨跌幅': stock.get('change_pct', 0),
                    '市值(亿)': stock.get('market_cap', 0),
                    '预测分数': stock.get('prediction_score', 0),
                    '预测标签': stock.get('prediction_label', 0)
                })

            # 创建DataFrame
            df = pd.DataFrame(export_data)

            # 导出文件
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"潜在股调查结果_{timestamp}.xlsx"
            filepath = os.path.join(os.path.dirname(os.path.dirname(__file__)), filename)

            df.to_excel(filepath, index=False, engine='openpyxl')

            logger.info(f"✅ 结果已导出: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"导出结果失败: {e}")
            return ""

def main():
    """测试潜在股调查器"""
    investigator = PotentialStockInvestigator()
    result = investigator.investigate_potential_stocks()
    print(f"调查结果: {result}")

if __name__ == "__main__":
    main()
