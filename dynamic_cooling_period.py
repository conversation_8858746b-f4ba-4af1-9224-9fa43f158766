#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动态冷却期算法
实现报告中要求的冷却期优化策略
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import json
import os

class DynamicCoolingPeriodAnalyzer:
    """动态冷却期分析器"""
    
    def __init__(self, limit_up_manager):
        self.limit_up_manager = limit_up_manager
        self.min_cooling_days = 10  # 最小冷却期
        self.max_cooling_days = 30  # 最大冷却期
    
    def analyze_cooling_period(self, stock_code, progress_callback=None):
        """分析股票的动态冷却期状态"""
        try:
            if progress_callback:
                progress_callback(f"分析 {stock_code} 冷却期状态...")
            
            # 1. 获取最近一次涨停日期
            last_limit_up_date = self._get_last_limit_up_date(stock_code)
            
            if not last_limit_up_date:
                return {
                    'in_cooling_period': False,
                    'reason': '无涨停历史记录',
                    'last_limit_up_date': None,
                    'cooling_days': 0,
                    'cooling_quality_score': 0
                }
            
            # 2. 计算冷却天数
            cooling_days = self._calculate_cooling_days(last_limit_up_date)
            
            # 3. 判断是否在有效冷却期内
            in_cooling_period = self.min_cooling_days <= cooling_days <= self.max_cooling_days
            
            if not in_cooling_period:
                return {
                    'in_cooling_period': False,
                    'reason': f'冷却期不符合要求({cooling_days}天)',
                    'last_limit_up_date': last_limit_up_date,
                    'cooling_days': cooling_days,
                    'cooling_quality_score': 0
                }
            
            # 4. 分析冷却期质量（暂时不传递K线数据，保持原有逻辑）
            cooling_quality = self._analyze_cooling_quality(stock_code, last_limit_up_date, cooling_days)
            
            return {
                'in_cooling_period': True,
                'reason': f'符合动态冷却期要求({cooling_days}天)',
                'last_limit_up_date': last_limit_up_date,
                'cooling_days': cooling_days,
                'cooling_quality_score': cooling_quality['total_score'],
                'cooling_analysis': cooling_quality
            }
            
        except Exception as e:
            print(f"❌ 分析 {stock_code} 冷却期失败: {e}")
            return {
                'in_cooling_period': False,
                'reason': f'分析失败: {e}',
                'last_limit_up_date': None,
                'cooling_days': 0,
                'cooling_quality_score': 0
            }
    
    def _get_last_limit_up_date(self, stock_code):
        """获取最近一次涨停日期"""
        try:
            # 从本地涨停数据中查找
            data_dir = self.limit_up_manager.data_dir
            existing_dates = self.limit_up_manager._get_existing_dates()
            
            # 按日期倒序查找
            for date in sorted(existing_dates, reverse=True):
                file_path = os.path.join(data_dir, f"{date}.json")
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    stocks = data.get('stocks', [])
                    
                    # 查找目标股票
                    for stock in stocks:
                        if stock.get('code') == stock_code:
                            return date
                            
                except Exception as e:
                    continue
            
            return None
            
        except Exception as e:
            print(f"❌ 获取 {stock_code} 最近涨停日期失败: {e}")
            return None
    
    def _calculate_cooling_days(self, last_limit_up_date):
        """计算冷却天数（交易日）"""
        try:
            last_date = datetime.strptime(last_limit_up_date, '%Y%m%d')
            current_date = datetime.now()
            
            # 计算交易日天数（排除周末）
            cooling_days = 0
            check_date = last_date + timedelta(days=1)
            
            while check_date <= current_date:
                # 排除周末
                if check_date.weekday() < 5:  # 0-4是周一到周五
                    cooling_days += 1
                check_date += timedelta(days=1)
            
            return cooling_days
            
        except Exception as e:
            return 0
    
    def _analyze_cooling_quality(self, stock_code, last_limit_up_date, cooling_days, existing_kline_data=None):
        """分析冷却期质量（优化版：可使用已有K线数据）"""
        try:
            # 获取冷却期内的K线数据（优先使用已有数据）
            if existing_kline_data is not None and not existing_kline_data.empty:
                print(f"♻️ 复用已有K线数据进行冷却期分析: {stock_code}")
                cooling_kline = existing_kline_data.tail(cooling_days)
            else:
                cooling_kline = self._get_cooling_period_kline(stock_code, cooling_days)

            if cooling_kline.empty:
                return {
                    'total_score': 0,
                    'turnover_convergence': False,
                    'amplitude_shrinking': False,
                    'volume_convergence': False,
                    'reason': '无法获取冷却期K线数据'
                }
            
            # 1. 量价收敛分析
            turnover_analysis = self._analyze_turnover_convergence(cooling_kline)
            
            # 2. 振幅收敛分析
            amplitude_analysis = self._analyze_amplitude_shrinking(cooling_kline)
            
            # 3. 成交量收敛分析
            volume_analysis = self._analyze_volume_convergence(cooling_kline)
            
            # 4. 计算综合评分
            total_score = self._calculate_cooling_quality_score(
                turnover_analysis, amplitude_analysis, volume_analysis
            )
            
            return {
                'total_score': total_score,
                'turnover_convergence': turnover_analysis['convergence'],
                'amplitude_shrinking': amplitude_analysis['shrinking'],
                'volume_convergence': volume_analysis['convergence'],
                'turnover_ratio': turnover_analysis['ratio'],
                'amplitude_ratio': amplitude_analysis['ratio'],
                'volume_ratio': volume_analysis['ratio'],
                'analysis_details': {
                    'turnover': turnover_analysis,
                    'amplitude': amplitude_analysis,
                    'volume': volume_analysis
                }
            }
            
        except Exception as e:
            print(f"❌ 分析 {stock_code} 冷却期质量失败: {e}")
            return {
                'total_score': 0,
                'turnover_convergence': False,
                'amplitude_shrinking': False,
                'volume_convergence': False,
                'reason': f'分析失败: {e}'
            }
    
    def _get_cooling_period_kline(self, stock_code, cooling_days):
        """获取冷却期内的K线数据"""
        try:
            # 这里需要调用现有的K线数据获取方法
            # 获取最近cooling_days+10天的数据（多获取一些用于对比）
            from enhanced_data_fetcher import enhanced_stock_fetcher
            
            kline_data = enhanced_stock_fetcher.get_daily_kline(stock_code, cooling_days + 10)
            
            if not kline_data.empty:
                # 只取最近cooling_days天的数据
                return kline_data.tail(cooling_days)
            else:
                return pd.DataFrame()
                
        except Exception as e:
            print(f"❌ 获取 {stock_code} 冷却期K线数据失败: {e}")
            return pd.DataFrame()
    
    def _analyze_turnover_convergence(self, kline_data):
        """分析换手率收敛情况"""
        try:
            if len(kline_data) < 10:
                return {'convergence': False, 'ratio': 0, 'reason': '数据不足'}
            
            # 计算换手率（如果没有直接的换手率字段，用成交量/流通股本估算）
            if '换手率' in kline_data.columns:
                turnover_rates = kline_data['换手率']
            elif '成交量' in kline_data.columns:
                # 简化计算：用成交量变化代替换手率
                volumes = kline_data['成交量']
                turnover_rates = volumes / volumes.mean()
            else:
                return {'convergence': False, 'ratio': 0, 'reason': '无换手率数据'}
            
            # 计算前半段和后半段的平均换手率
            mid_point = len(turnover_rates) // 2
            first_half_avg = turnover_rates[:mid_point].mean()
            second_half_avg = turnover_rates[mid_point:].mean()
            
            # 计算收敛比例
            if first_half_avg > 0:
                convergence_ratio = second_half_avg / first_half_avg
                # 要求后半段换手率 < 前半段的50%
                convergence = convergence_ratio < 0.5
            else:
                convergence_ratio = 0
                convergence = False
            
            return {
                'convergence': convergence,
                'ratio': convergence_ratio,
                'first_half_avg': first_half_avg,
                'second_half_avg': second_half_avg,
                'reason': f'换手率收敛比例: {convergence_ratio:.2f}'
            }
            
        except Exception as e:
            return {'convergence': False, 'ratio': 0, 'reason': f'分析失败: {e}'}
    
    def _analyze_amplitude_shrinking(self, kline_data):
        """分析振幅收敛情况"""
        try:
            if len(kline_data) < 10:
                return {'shrinking': False, 'ratio': 0, 'reason': '数据不足'}
            
            # 计算每日振幅
            if '最高' in kline_data.columns and '最低' in kline_data.columns and '收盘' in kline_data.columns:
                amplitudes = (kline_data['最高'] - kline_data['最低']) / kline_data['收盘'] * 100
            else:
                return {'shrinking': False, 'ratio': 0, 'reason': '无振幅数据'}
            
            # 比较最后5日和前5日的振幅
            if len(amplitudes) >= 10:
                last_5_avg = amplitudes[-5:].mean()
                prev_5_avg = amplitudes[-10:-5].mean()
                
                if prev_5_avg > 0:
                    shrinking_ratio = last_5_avg / prev_5_avg
                    # 要求最后5日振幅 < 前5日振幅的70%
                    shrinking = shrinking_ratio < 0.7
                else:
                    shrinking_ratio = 0
                    shrinking = False
            else:
                # 数据不足，用整体趋势判断
                shrinking_ratio = amplitudes[-3:].mean() / amplitudes[:3].mean() if len(amplitudes) >= 6 else 0
                shrinking = shrinking_ratio < 0.7
            
            return {
                'shrinking': shrinking,
                'ratio': shrinking_ratio,
                'last_5_avg': amplitudes[-5:].mean() if len(amplitudes) >= 5 else 0,
                'prev_5_avg': amplitudes[-10:-5].mean() if len(amplitudes) >= 10 else 0,
                'reason': f'振幅收敛比例: {shrinking_ratio:.2f}'
            }
            
        except Exception as e:
            return {'shrinking': False, 'ratio': 0, 'reason': f'分析失败: {e}'}
    
    def _analyze_volume_convergence(self, kline_data):
        """分析成交量收敛情况"""
        try:
            if len(kline_data) < 5:
                return {'convergence': False, 'ratio': 0, 'reason': '数据不足'}
            
            if '成交量' not in kline_data.columns:
                return {'convergence': False, 'ratio': 0, 'reason': '无成交量数据'}
            
            volumes = kline_data['成交量']
            
            # 计算成交量的变异系数（标准差/均值）
            volume_cv = volumes.std() / volumes.mean() if volumes.mean() > 0 else 0
            
            # 计算最近几日成交量相对于前期的比例
            if len(volumes) >= 10:
                recent_avg = volumes[-5:].mean()
                earlier_avg = volumes[:-5].mean()
                volume_ratio = recent_avg / earlier_avg if earlier_avg > 0 else 0
            else:
                volume_ratio = 1.0
            
            # 成交量收敛：变异系数较小且最近成交量相对稳定
            convergence = volume_cv < 0.5 and 0.5 <= volume_ratio <= 1.5
            
            return {
                'convergence': convergence,
                'ratio': volume_ratio,
                'cv': volume_cv,
                'recent_avg': volumes[-5:].mean() if len(volumes) >= 5 else 0,
                'earlier_avg': volumes[:-5].mean() if len(volumes) > 5 else volumes.mean(),
                'reason': f'成交量变异系数: {volume_cv:.2f}, 比例: {volume_ratio:.2f}'
            }
            
        except Exception as e:
            return {'convergence': False, 'ratio': 0, 'reason': f'分析失败: {e}'}
    
    def _calculate_cooling_quality_score(self, turnover_analysis, amplitude_analysis, volume_analysis):
        """计算冷却期质量综合评分"""
        try:
            score = 0
            
            # 换手率收敛评分（40分）
            if turnover_analysis['convergence']:
                score += 40
            elif turnover_analysis['ratio'] < 0.8:  # 部分收敛
                score += 20
            
            # 振幅收敛评分（40分）
            if amplitude_analysis['shrinking']:
                score += 40
            elif amplitude_analysis['ratio'] < 0.9:  # 部分收敛
                score += 20
            
            # 成交量收敛评分（20分）
            if volume_analysis['convergence']:
                score += 20
            elif volume_analysis['cv'] < 0.8:  # 部分收敛
                score += 10
            
            return min(100, score)
            
        except Exception as e:
            return 0

def test_cooling_period_analyzer():
    """测试动态冷却期分析器"""
    print("🧪 测试动态冷却期分析器")
    
    try:
        from youzi_seats_manager import YouziSeatsManager
        from enhanced_stock_monitor import HistoricalLimitUpManager
        
        # 初始化管理器
        limit_up_manager = HistoricalLimitUpManager()
        analyzer = DynamicCoolingPeriodAnalyzer(limit_up_manager)
        
        # 测试股票
        test_stocks = ['000001', '000002', '600036']
        
        for stock_code in test_stocks:
            print(f"\n📊 分析 {stock_code} 的冷却期状态:")
            
            result = analyzer.analyze_cooling_period(stock_code)
            
            print(f"   冷却期状态: {'✅ 符合' if result['in_cooling_period'] else '❌ 不符合'}")
            print(f"   原因: {result['reason']}")
            print(f"   冷却天数: {result['cooling_days']}天")
            print(f"   质量评分: {result['cooling_quality_score']:.1f}分")
            
            if result['in_cooling_period']:
                analysis = result.get('cooling_analysis', {})
                print(f"   换手率收敛: {'✅' if analysis.get('turnover_convergence') else '❌'}")
                print(f"   振幅收敛: {'✅' if analysis.get('amplitude_shrinking') else '❌'}")
                print(f"   成交量收敛: {'✅' if analysis.get('volume_convergence') else '❌'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_cooling_period_analyzer()
