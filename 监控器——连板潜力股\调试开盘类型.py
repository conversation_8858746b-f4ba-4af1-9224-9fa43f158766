#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试开盘类型显示问题
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def debug_stock_data():
    """调试股票数据获取"""
    print("🔍 调试股票数据获取")
    print("-" * 40)
    
    try:
        from enhanced_data_fetcher import EnhancedStockDataFetcher
        
        fetcher = EnhancedStockDataFetcher()
        print("✅ 数据获取器导入成功")
        
        # 测试获取一只股票的数据
        test_codes = ['000001', '603115']
        
        for code in test_codes:
            print(f"\n📊 测试股票: {code}")
            
            # 获取实时数据
            df = fetcher.get_realtime_quote([code])
            
            if df is not None and not df.empty:
                print(f"✅ 获取到数据，行数: {len(df)}")
                
                # 显示列名
                print(f"📋 数据列: {list(df.columns)}")
                
                # 获取第一行数据
                if len(df) > 0:
                    row = df.iloc[0]
                    stock_data = row.to_dict()
                    
                    print(f"📊 股票数据:")
                    print(f"   current_price: {stock_data.get('current_price', '未找到')}")
                    print(f"   open: {stock_data.get('open', '未找到')}")
                    print(f"   prev_close: {stock_data.get('prev_close', '未找到')}")
                    print(f"   change_pct: {stock_data.get('change_pct', '未找到')}")
                    
                    # 测试开盘类型分类
                    from 连板潜力股实时监控 import LimitUpPotentialMonitor
                    monitor = LimitUpPotentialMonitor()
                    
                    result = monitor.classify_open_type(stock_data)
                    print(f"🔧 开盘类型分类结果:")
                    print(f"   type: {result.get('type', '未找到')}")
                    print(f"   type_name: {result.get('type_name', '未找到')}")
                    print(f"   open_change_pct: {result.get('open_change_pct', '未找到')}")
                    print(f"   current_change_pct: {result.get('current_change_pct', '未找到')}")
                    
            else:
                print(f"❌ 未获取到数据")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def debug_historical_data():
    """调试历史数据"""
    print("\n🔍 调试历史数据")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        
        monitor = LimitUpPotentialMonitor()
        print("✅ 监控器导入成功")
        
        # 检查历史数据
        test_code = '603115'
        
        if test_code in monitor.historical_base_data:
            historical_data = monitor.historical_base_data[test_code]
            print(f"📊 {test_code} 历史数据:")
            print(f"   prev_close: {historical_data.get('prev_close', '未找到')}")
            print(f"   prev_high: {historical_data.get('prev_high', '未找到')}")
            print(f"   prev_low: {historical_data.get('prev_low', '未找到')}")
        else:
            print(f"❌ {test_code} 历史数据不存在")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")

def debug_real_monitoring():
    """调试实际监控过程"""
    print("\n🔍 调试实际监控过程")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        from enhanced_data_fetcher import EnhancedStockDataFetcher
        
        monitor = LimitUpPotentialMonitor()
        fetcher = EnhancedStockDataFetcher()
        
        # 选择一只监控股票
        if monitor.monitored_stocks:
            test_code = list(monitor.monitored_stocks.keys())[0]
            stock_name = monitor.monitored_stocks[test_code]['name']
            
            print(f"📊 测试监控股票: {test_code} {stock_name}")
            
            # 获取实时数据
            df = fetcher.get_realtime_quote([test_code])
            
            if df is not None and not df.empty:
                row = df.iloc[0]
                stock_data = row.to_dict()
                
                print(f"📊 原始股票数据:")
                for key, value in stock_data.items():
                    print(f"   {key}: {value}")
                
                # 补充历史数据
                if test_code in monitor.historical_base_data:
                    historical_data = monitor.historical_base_data[test_code]
                    stock_data['prev_close'] = historical_data.get('prev_close', 0)
                    stock_data['prev_high'] = historical_data.get('prev_high', 0)
                    stock_data['prev_low'] = historical_data.get('prev_low', 0)
                    
                    print(f"\n📊 补充历史数据后:")
                    print(f"   prev_close: {stock_data.get('prev_close')}")
                    print(f"   prev_high: {stock_data.get('prev_high')}")
                    print(f"   prev_low: {stock_data.get('prev_low')}")
                
                # 测试开盘类型分类
                result = monitor.classify_open_type(stock_data)
                print(f"\n🔧 开盘类型分类结果:")
                print(f"   type: {result.get('type')}")
                print(f"   type_name: {result.get('type_name')}")
                print(f"   open_change_pct: {result.get('open_change_pct')}")
                print(f"   current_change_pct: {result.get('current_change_pct')}")
                print(f"   description: {result.get('description')}")
                
                # 检查是否有问题
                current_price = stock_data.get('current_price', 0)
                open_price = stock_data.get('open', 0)
                prev_close = stock_data.get('prev_close', 0)
                
                print(f"\n🔍 数据检查:")
                print(f"   current_price > 0: {current_price > 0}")
                print(f"   open_price > 0: {open_price > 0}")
                print(f"   prev_close > 0: {prev_close > 0}")
                
                if prev_close <= 0:
                    print(f"❌ 问题发现: prev_close <= 0，这会导致显示'未知'")
                elif open_price <= 0:
                    print(f"⚠️ 注意: open_price <= 0，将使用current_price计算")
                else:
                    print(f"✅ 数据正常")
                
            else:
                print(f"❌ 未获取到实时数据")
        else:
            print(f"❌ 没有监控股票")
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()

def main():
    """主调试函数"""
    print("🚀 开始调试开盘类型显示问题")
    print("=" * 50)
    
    # 运行调试
    debug_stock_data()
    debug_historical_data()
    debug_real_monitoring()
    
    print("\n📋 可能的问题原因:")
    print("=" * 50)
    print("1. prev_close 数据缺失或为0")
    print("2. 历史数据没有正确补充到实时数据中")
    print("3. 数据获取器返回的字段名不匹配")
    print("4. 开盘价 open 字段缺失")
    print("5. 数据类型转换问题")
    
    print("\n🏁 调试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
