#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
潜在股进一步筛选器 - 主力吸筹尾声精准买入系统
基于《主力吸筹尾声精准买入指南》开发

核心功能：
1. 量能异动监测（地量-倍量转折、窒息量识别）
2. 价格结构分析（三线收拢、假破位过滤）
3. 筹码锁定监测（低位筹码稳定性检测）
4. 盘口确认系统（托单防御、抛压枯竭、试盘痕迹）
5. 投资决策树逻辑（四维度共振判断）
6. 风控管理系统（分阶段风控监控）

作者：AI Assistant
版本：1.0.0
创建时间：2025-07-19
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
import time
import json
import os
from pathlib import Path

# 导入现有应用的数据获取模块
try:
    from enhanced_data_fetcher import EnhancedStockDataFetcher
    enhanced_stock_fetcher = EnhancedStockDataFetcher()
    print("✅ 成功导入增强数据获取器")
except ImportError as e:
    print(f"⚠️ 无法导入增强数据获取器: {e}")
    print("⚠️ 尝试使用备用数据获取方法...")
    
    # 备用数据获取器
    try:
        import akshare as ak
        import requests
        AKSHARE_AVAILABLE = True
        print("✅ AKShare数据源可用")
        
        class BackupDataFetcher:
            """备用数据获取器"""
            
            def __init__(self):
                self.session = requests.Session()
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
            
            def get_stock_basic_info(self, code):
                """获取股票基本信息"""
                try:
                    # 方法1：使用代码对照表（最快最稳定）
                    try:
                        df = ak.stock_info_a_code_name()
                        if not df.empty:
                            df['code'] = df['code'].astype(str).str.zfill(6)
                            stock_row = df[df['code'] == code]
                            if not stock_row.empty:
                                stock_name = stock_row['name'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法1代码对照表获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法1代码对照表失败 {code}: {e}")

                    # 方法2：使用个股信息接口
                    try:
                        stock_info = ak.stock_individual_info_em(symbol=code)
                        if not stock_info.empty:
                            name_row = stock_info[stock_info['item'] == '股票简称']
                            if not name_row.empty:
                                stock_name = name_row['value'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法2个股信息获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法2个股信息失败 {code}: {e}")

                    # 方法3：从实时行情中获取股票名称（备用）
                    try:
                        df = ak.stock_zh_a_spot_em()
                        if not df.empty:
                            df['代码'] = df['代码'].astype(str).str.zfill(6)
                            stock_row = df[df['代码'] == code]
                            if not stock_row.empty:
                                stock_name = stock_row['名称'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法3实时行情获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法3实时行情失败 {code}: {e}")

                    # 方法4：从本地映射表获取
                    try:
                        from stock_name_mapping import get_stock_name_from_mapping
                        mapped_name = get_stock_name_from_mapping(code)
                        if mapped_name:
                            print(f"   ✅ 方法4本地映射获取: {code} -> {mapped_name}")
                            return {'name': mapped_name}
                    except Exception as e:
                        print(f"   ⚠️ 方法4本地映射失败 {code}: {e}")

                    # 如果所有方法都失败，返回默认名称
                    print(f"   ⚠️ 所有方法都无法获取股票名称 {code}，使用默认名称")
                    return {'name': f'股票{code}'}

                except Exception as e:
                    print(f"   ❌ 获取股票基本信息异常 {code}: {e}")
                    return {'name': f'股票{code}'}
            
            def get_realtime_quote(self, codes):
                """获取实时行情"""
                try:
                    # 使用AKShare获取实时行情
                    df = ak.stock_zh_a_spot_em()
                    
                    if df.empty:
                        return pd.DataFrame()
                    
                    # 筛选指定股票
                    df['代码'] = df['代码'].astype(str).str.zfill(6)
                    filtered_df = df[df['代码'].isin(codes)]
                    
                    # 标准化列名以匹配主应用格式
                    if not filtered_df.empty:
                        filtered_df = filtered_df.rename(columns={
                            '代码': 'stock_code',
                            '名称': 'stock_name', 
                            '最新价': 'current_price',
                            '涨跌额': 'change',
                            '涨跌幅': 'change_pct',
                            '今开': 'open',
                            '最高': 'high',
                            '最低': 'low',
                            '昨收': 'prev_close',
                            '成交量': 'volume',
                            '成交额': 'amount',
                            '总市值': 'market_cap'
                        })
                    
                    return filtered_df
                    
                except Exception as e:
                    print(f"⚠️ 获取实时行情失败: {e}")
                    return pd.DataFrame()
            
            def get_market_depth(self, code):
                """获取五档行情"""
                try:
                    # 使用新浪财经API获取五档数据
                    url = f"http://hq.sinajs.cn/list={self._get_market_prefix(code)}{code}"
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code != 200:
                        return pd.DataFrame()
                    
                    data = response.text
                    if 'var hq_str_' not in data:
                        return pd.DataFrame()
                    
                    # 解析数据
                    content = data.split('"')[1]
                    items = content.split(',')
                    
                    if len(items) < 33:
                        return pd.DataFrame()
                    
                    # 构建五档数据
                    depth_data = {
                        '买1': float(items[6]) if items[6] else 0,
                        '买量1': int(items[10]) if items[10] else 0,
                        '买2': float(items[7]) if items[7] else 0,
                        '买量2': int(items[11]) if items[11] else 0,
                        '买3': float(items[8]) if items[8] else 0,
                        '买量3': int(items[12]) if items[12] else 0,
                        '买4': float(items[9]) if items[9] else 0,
                        '买量4': int(items[13]) if items[13] else 0,
                        '买5': float(items[14]) if len(items) > 14 and items[14] else 0,
                        '买量5': int(items[15]) if len(items) > 15 and items[15] else 0,
                        '卖1': float(items[20]) if items[20] else 0,
                        '卖量1': int(items[21]) if items[21] else 0,
                        '卖2': float(items[22]) if items[22] else 0,
                        '卖量2': int(items[23]) if items[23] else 0,
                        '卖3': float(items[24]) if items[24] else 0,
                        '卖量3': int(items[25]) if items[25] else 0,
                        '卖4': float(items[26]) if items[26] else 0,
                        '卖量4': int(items[27]) if items[27] else 0,
                        '卖5': float(items[28]) if items[28] else 0,
                        '卖量5': int(items[29]) if items[29] else 0,
                    }
                    
                    return pd.DataFrame([depth_data])
                    
                except Exception as e:
                    print(f"⚠️ 获取五档数据失败 {code}: {e}")
                    return pd.DataFrame()
            
            def get_daily_kline(self, code, days=2):
                """获取日K线数据"""
                try:
                    df = ak.stock_zh_a_hist(symbol=code, adjust="qfq")
                    if not df.empty:
                        return df.tail(days)
                    return pd.DataFrame()
                except Exception as e:
                    print(f"⚠️ 获取日K线数据失败 {code}: {e}")
                    return pd.DataFrame()
            
            def _get_market_prefix(self, code):
                """获取市场前缀"""
                if code.startswith(('600', '601', '603', '688')):
                    return 'sh'
                elif code.startswith(('000', '001', '002', '300')):
                    return 'sz'
                else:
                    return 'sh'
        
        enhanced_stock_fetcher = BackupDataFetcher()
        print("✅ 备用数据获取器初始化成功")
        
    except ImportError:
        enhanced_stock_fetcher = None
        print("❌ 所有数据获取器都不可用")

# Windows通知功能
try:
    import win10toast
    toaster = win10toast.ToastNotifier()
    NOTIFICATION_AVAILABLE = True
    print("✅ Windows通知功能可用")
except ImportError:
    NOTIFICATION_AVAILABLE = False
    print("⚠️ Windows通知不可用，请安装: pip install win10toast")


class StockFurtherScreening:
    """潜在股进一步筛选器 - 主力吸筹尾声精准买入系统"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎯 潜在股进一步筛选器 - 主力吸筹尾声精准买入系统")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # 设置全局字体
        self.setup_fonts()
        
        # 监控配置
        self.config = {
            'scan_interval': 30,                    # 扫描间隔（秒）
            'key_time_scan_interval': 15,           # 关键时间窗口扫描间隔（秒）
            'volume_min_threshold': 0.45,           # 窒息量阈值（5日均量的45%）
            'ma_converge_threshold': 0.015,         # 均线收拢阈值（1.5%）
            'chip_stability_threshold': 65,         # 筹码稳定性阈值（65%）
            'bid_defense_ratio': 3.0,               # 托单防御比例（买一量>卖一量3倍）
            'ask_exhaustion_threshold': 0.0003,     # 抛压枯竭阈值（流通盘的0.03%）
            'notification_duration': 30,            # 通知持续时间（秒）
        }
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.monitored_stocks = {}  # {code: {name, data, signals, decision}}
        
        # 数据分层管理
        self.historical_base_data = {}  # 历史基础数据（一次性加载）
        self.realtime_data_cache = {}   # 实时数据缓存（每30秒更新）
        self.historical_cache = {}      # 临时历史数据缓存（兼容性保留）

        # 历史数据缓存目录
        self.data_cache_dir = Path("screening_historical_data_cache")
        self.data_cache_dir.mkdir(exist_ok=True)

        # 创建GUI界面
        self.create_gui()
        
        # 加载配置
        self.load_config()

        # 检查历史数据完整性
        if self.monitored_stocks:
            self.check_all_historical_data()

        print("✅ 潜在股进一步筛选器初始化完成")

    def setup_fonts(self):
        """设置全局字体"""
        try:
            # 设置默认字体为微软雅黑
            default_font = ('Microsoft YaHei', 9)
            self.root.option_add('*Font', default_font)

            # 设置ttk样式
            style = ttk.Style()

            # 配置各种控件的字体
            style.configure('TLabel', font=('Microsoft YaHei', 9))
            style.configure('TButton', font=('Microsoft YaHei', 9))
            style.configure('TEntry', font=('Microsoft YaHei', 9))
            style.configure('TCombobox', font=('Microsoft YaHei', 9))
            style.configure('Treeview', font=('Microsoft YaHei', 9))
            style.configure('Treeview.Heading', font=('Microsoft YaHei', 9, 'bold'))

            # 设置表格行高，适应中文字体
            style.configure('Treeview', rowheight=30)

            # 优化表格外观
            style.configure('Treeview',
                          background='white',
                          foreground='black',
                          fieldbackground='white',
                          borderwidth=1,
                          relief='solid')

            # 优化表格标题外观
            style.configure('Treeview.Heading',
                          background='#e1e1e1',
                          foreground='black',
                          borderwidth=1,
                          relief='raised')

            # 优化按钮外观
            style.configure('TButton',
                          padding=(10, 5),
                          relief='raised',
                          borderwidth=1)

            print("✅ 字体设置完成")

        except Exception as e:
            print(f"⚠️ 字体设置失败: {e}")

    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="🎯 潜在股进一步筛选器",
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 添加股票按钮
        self.add_stock_btn = ttk.Button(control_frame, text="➕ 添加股票",
                                       command=self.add_stocks_dialog)
        self.add_stock_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 删除股票按钮
        self.remove_stock_btn = ttk.Button(control_frame, text="➖ 删除股票",
                                          command=self.remove_selected_stock)
        self.remove_stock_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 开始/停止监控按钮
        self.monitor_btn = ttk.Button(control_frame, text="🚀 开始监控",
                                     command=self.toggle_monitoring)
        self.monitor_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 设置按钮
        self.settings_btn = ttk.Button(control_frame, text="⚙️ 设置",
                                      command=self.show_settings)
        self.settings_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 四维度分析按钮
        self.analysis_btn = ttk.Button(control_frame, text="🔍 四维度分析",
                                      command=self.show_four_dimension_analysis)
        self.analysis_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 重新初始化按钮
        self.reinit_btn = ttk.Button(control_frame, text="🔄 重新初始化",
                                    command=self.reinitialize_all_stocks)
        self.reinit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 全部清空按钮
        self.clear_all_btn = ttk.Button(control_frame, text="🗑️ 全部清空",
                                       command=self.clear_all_stocks)
        self.clear_all_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 监控状态标签
        self.status_label = ttk.Label(control_frame, text="⏸️ 筛选已停止",
                                     font=('Microsoft YaHei', 10, 'bold'))
        self.status_label.pack(side=tk.RIGHT)

        # 创建股票列表表格（专门的四维度显示）
        self.create_stock_table(main_frame)

        # 底部状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.info_label = ttk.Label(status_frame, text="💡 请添加股票代码开始四维度筛选")
        self.info_label.pack(side=tk.LEFT)

        self.time_label = ttk.Label(status_frame, text="")
        self.time_label.pack(side=tk.RIGHT)

        # 更新时间显示
        self.update_time_display()

    def create_stock_table(self, parent):
        """创建股票列表表格（四维度专门显示）"""
        # 表格框架
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # 定义列（专门的四维度列）
        columns = ('股票代码', '股票名称', '量能异动', '价格结构', '筹码锁定', '盘口确认',
                  '综合评分', '决策状态', '最新价格', '更新时间')

        # 创建表格
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 设置列标题和宽度
        column_widths = {
            '股票代码': 80, '股票名称': 100, '量能异动': 80, '价格结构': 80,
            '筹码锁定': 80, '盘口确认': 80, '综合评分': 80, '决策状态': 100,
            '最新价格': 80, '更新时间': 120
        }

        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100))

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)

    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"当前时间: {current_time}")
        self.root.after(1000, self.update_time_display)

    def check_all_historical_data(self):
        """检查所有股票的历史数据完整性"""
        print(f"🔍 开始检查 {len(self.monitored_stocks)} 只股票的历史数据完整性...")

        stocks_need_download = []

        for code in self.monitored_stocks.keys():
            if not self.check_historical_data_completeness(code):
                stocks_need_download.append(code)
            else:
                # 加载缓存数据
                self.load_historical_data_from_cache(code)

        if stocks_need_download:
            print(f"📥 需要下载历史数据的股票: {len(stocks_need_download)} 只")
            self.download_batch_historical_data(stocks_need_download)
        else:
            print("✅ 所有股票历史数据完整，无需下载")
            print("📊 筹码分布数据已缓存，避免重复计算")

    def check_historical_data_completeness(self, code):
        """检查单只股票的历史数据完整性"""
        try:
            cache_file = self.data_cache_dir / f"{code}_historical_data.json"

            if not cache_file.exists():
                print(f"   ⚠️ {code} 历史数据缓存不存在")
                return False

            # 检查文件修改时间（超过1天认为过期）
            file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
            if datetime.now() - file_time > timedelta(days=1):
                print(f"   ⚠️ {code} 历史数据缓存已过期")
                return False

            # 检查缓存内容完整性
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                required_fields = [
                    'prev_close', 'avg_volume_5d', 'ma5', 'ma10', 'ma20',
                    'chip_distribution_30d', 'cache_time'
                ]
                missing_fields = [field for field in required_fields if field not in cache_data]
                if missing_fields:
                    print(f"   ⚠️ {code} 历史数据缓存不完整，缺少字段: {missing_fields}")
                    return False

                # 特别检查筹码分布数据的有效性
                chip_data = cache_data.get('chip_distribution_30d')
                if not chip_data or not isinstance(chip_data, dict) or len(chip_data) == 0:
                    print(f"   ⚠️ {code} 筹码分布数据无效或为空")
                    return False

                print(f"   ✅ {code} 历史数据完整（包含 {len(chip_data)} 个筹码分布数据点）")
                return True

            except json.JSONDecodeError as e:
                print(f"   ⚠️ {code} 历史数据缓存文件损坏: {e}")
                # 删除损坏的缓存文件
                try:
                    cache_file.unlink()
                    print(f"   🗑️ 已删除损坏的缓存文件: {code}")
                except:
                    pass
                return False

        except Exception as e:
            print(f"   ❌ {code} 历史数据检查失败: {e}")
            return False

    def load_historical_data_from_cache(self, code):
        """从缓存文件加载历史数据到内存"""
        try:
            cache_file = self.data_cache_dir / f"{code}_historical_data.json"
            if cache_file.exists():
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        historical_data = json.load(f)
                    self.historical_base_data[code] = historical_data
                    print(f"   ✅ {code} 历史数据缓存加载成功")
                    return True
                except json.JSONDecodeError as e:
                    print(f"   ⚠️ {code} 缓存文件损坏，删除重建: {e}")
                    cache_file.unlink()
                    return False
            return False
        except Exception as e:
            print(f"❌ {code} 历史数据缓存加载失败: {e}")
            return False

    def download_batch_historical_data(self, stock_codes):
        """批量下载历史数据"""
        try:
            success_count = 0
            for i, code in enumerate(stock_codes, 1):
                stock_name = self.monitored_stocks[code]['name']
                print(f"📥 [{i}/{len(stock_codes)}] 下载 {code} {stock_name} 历史数据...")

                if self.download_and_cache_historical_data(code):
                    success_count += 1
                    print(f"   ✅ {code} 历史数据下载成功")
                else:
                    print(f"   ❌ {code} 历史数据下载失败")

            print(f"📥 历史数据下载完成: 成功 {success_count}/{len(stock_codes)} 只股票")

        except Exception as e:
            print(f"❌ 批量下载历史数据失败: {e}")

    def initialize_stock_monitoring(self, code, stock_name=None):
        """初始化股票监控（加载历史基础数据）"""
        print(f"📊 初始化 {code} 的历史基础数据...")

        # 首先尝试从缓存加载
        if self.load_historical_data_from_cache(code):
            return True

        # 缓存不可用，重新下载
        print(f"   🔄 缓存不可用，重新下载 {code} 历史数据...")
        return self.download_and_cache_historical_data(code, stock_name)

    def download_and_cache_historical_data(self, code, stock_name=None):
        """下载并缓存历史数据"""
        try:
            base_data = {}

            # 1. 加载历史K线数据（用于计算基准）
            print(f"   🔄 加载 {code} 历史K线数据...")
            kline_history = enhanced_stock_fetcher.get_daily_kline(code, 30)

            if not kline_history.empty and len(kline_history) >= 2:
                base_data['kline_history_30d'] = kline_history
                base_data['prev_close'] = float(kline_history.iloc[-2].get('收盘', kline_history.iloc[-2].get('close', 0)))
                base_data['prev_high'] = float(kline_history.iloc[-2].get('最高', kline_history.iloc[-2].get('high', 0)))
                base_data['prev_low'] = float(kline_history.iloc[-2].get('最低', kline_history.iloc[-2].get('low', 0)))
                base_data['prev_open'] = float(kline_history.iloc[-2].get('开盘', kline_history.iloc[-2].get('open', 0)))
                print(f"   ✅ K线数据加载完成，前日收盘价: {base_data['prev_close']}")
            else:
                print(f"   ⚠️ K线数据不足，使用默认值")
                base_data['prev_close'] = 0
                base_data['prev_high'] = 0
                base_data['prev_low'] = 0
                base_data['prev_open'] = 0

            # 2. 计算历史成交量基准（用于量能异动检测）
            print(f"   🔄 计算 {code} 成交量基准...")
            if not kline_history.empty and len(kline_history) >= 10:
                recent_volumes = []
                for _, row in kline_history.tail(10).iterrows():
                    volume = int(row.get('成交量', row.get('volume', 0)))
                    if volume > 0:
                        recent_volumes.append(volume)

                if recent_volumes:
                    base_data['avg_volume_5d'] = sum(recent_volumes[-5:]) / len(recent_volumes[-5:])
                    base_data['avg_volume_7d'] = sum(recent_volumes[-7:]) / len(recent_volumes[-7:])
                    base_data['min_volume_7d'] = min(recent_volumes[-7:])
                    base_data['volume_series_7d'] = recent_volumes[-7:]
                    print(f"   ✅ 成交量基准计算完成，5日均量: {base_data['avg_volume_5d']:.0f}")
                else:
                    base_data['avg_volume_5d'] = 0
                    base_data['avg_volume_7d'] = 0
                    base_data['min_volume_7d'] = 0
                    base_data['volume_series_7d'] = []
            else:
                base_data['avg_volume_5d'] = 0
                base_data['avg_volume_7d'] = 0
                base_data['min_volume_7d'] = 0
                base_data['volume_series_7d'] = []

            # 3. 计算均线数据（用于价格结构分析）
            print(f"   🔄 计算 {code} 均线数据...")
            if not kline_history.empty and len(kline_history) >= 20:
                closes = [float(row.get('收盘', row.get('close', 0))) for _, row in kline_history.iterrows()]

                if len(closes) >= 20:
                    base_data['ma5'] = sum(closes[-5:]) / 5
                    base_data['ma10'] = sum(closes[-10:]) / 10
                    base_data['ma20'] = sum(closes[-20:]) / 20
                    print(f"   ✅ 均线数据计算完成，MA5: {base_data['ma5']:.2f}")
                else:
                    base_data['ma5'] = closes[-1] if closes else 0
                    base_data['ma10'] = closes[-1] if closes else 0
                    base_data['ma20'] = closes[-1] if closes else 0
            else:
                base_data['ma5'] = 0
                base_data['ma10'] = 0
                base_data['ma20'] = 0

            # 4. 加载筹码分布历史数据（如果主应用支持）
            print(f"   🔄 尝试加载 {code} 筹码分布数据...")
            try:
                if hasattr(enhanced_stock_fetcher, 'get_chip_distribution'):
                    chip_data = enhanced_stock_fetcher.get_chip_distribution(code)
                    base_data['chip_distribution_30d'] = chip_data
                    print(f"   ✅ 筹码分布数据加载完成")
                else:
                    # 基于历史K线数据构建简化筹码分布
                    base_data['chip_distribution_30d'] = self.build_simple_chip_distribution(kline_history)
                    print(f"   ✅ 简化筹码分布构建完成")
            except Exception as e:
                print(f"   ⚠️ 筹码分布数据加载失败: {e}")
                base_data['chip_distribution_30d'] = None

            # 5. 计算价格区间统计
            if not kline_history.empty:
                highs = [float(row.get('最高', row.get('high', 0))) for _, row in kline_history.iterrows()]
                lows = [float(row.get('最低', row.get('low', 0))) for _, row in kline_history.iterrows()]
                closes = [float(row.get('收盘', row.get('close', 0))) for _, row in kline_history.iterrows()]

                base_data['price_range'] = {
                    'max_high_30d': max(highs) if highs else 0,
                    'min_low_30d': min(lows) if lows else 0,
                    'avg_close_30d': sum(closes) / len(closes) if closes else 0,
                    'key_support': min(lows[-10:]) if len(lows) >= 10 else (min(lows) if lows else 0)
                }
            else:
                base_data['price_range'] = {
                    'max_high_30d': 0,
                    'min_low_30d': 0,
                    'avg_close_30d': 0,
                    'key_support': 0
                }

            # 6. 保存加载时间戳
            base_data['cache_time'] = datetime.now().isoformat()
            base_data['stock_code'] = code
            # 使用传入的股票名称，如果没有则尝试从监控列表获取，最后使用默认名称
            if stock_name:
                base_data['stock_name'] = stock_name
            elif code in self.monitored_stocks:
                base_data['stock_name'] = self.monitored_stocks[code]['name']
            else:
                base_data['stock_name'] = f'股票{code}'

            # 处理数据序列化
            base_data = self.prepare_data_for_json(base_data)

            # 保存到缓存文件
            cache_file = self.data_cache_dir / f"{code}_historical_data.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(base_data, f, ensure_ascii=False, indent=2, default=str)

            # 保存到历史基础数据缓存
            self.historical_base_data[code] = base_data

            print(f"✅ {code} 历史基础数据初始化完成")
            return True

        except Exception as e:
            print(f"❌ {code} 历史数据初始化失败: {e}")
            return False

    def prepare_data_for_json(self, data):
        """准备数据用于JSON序列化，处理DataFrame和Timestamp对象"""
        try:
            if isinstance(data, dict):
                result = {}
                for key, value in data.items():
                    result[key] = self.prepare_data_for_json(value)
                return result
            elif isinstance(data, list):
                return [self.prepare_data_for_json(item) for item in data]
            elif isinstance(data, pd.DataFrame):
                # 将DataFrame转换为字典列表
                return data.to_dict('records')
            elif isinstance(data, (pd.Timestamp, datetime)):
                # 将时间戳转换为ISO格式字符串
                return data.isoformat() if hasattr(data, 'isoformat') else str(data)
            elif isinstance(data, (pd.Series, pd.Index)):
                # 将Series转换为列表
                return data.tolist()
            elif pd.isna(data):
                # 处理NaN值
                return None
            else:
                return data
        except Exception as e:
            print(f"⚠️ 数据序列化处理失败: {e}")
            return str(data)

    def build_simple_chip_distribution(self, kline_history):
        """基于历史K线数据构建简化筹码分布"""
        try:
            if kline_history.empty:
                return None

            chip_distribution = {}
            current_time = datetime.now()

            # 基于成交量和价格构建筹码分布
            for i, (_, row) in enumerate(kline_history.iterrows()):
                close_price = float(row.get('收盘', row.get('close', 0)))
                volume = int(row.get('成交量', row.get('volume', 0)))
                trade_date = row.get('日期', row.get('date', current_time))

                if close_price > 0 and volume > 0:
                    # 计算时间衰减因子（越近的数据权重越大）
                    if isinstance(trade_date, str):
                        trade_date = datetime.strptime(trade_date, '%Y-%m-%d')
                    elif not isinstance(trade_date, datetime):
                        trade_date = current_time

                    days_ago = (current_time - trade_date).days
                    decay_factor = 0.98 ** days_ago  # 每天衰减2%

                    # 按价格区间统计筹码
                    price_level = round(close_price, 2)
                    weighted_volume = volume * decay_factor

                    if price_level in chip_distribution:
                        chip_distribution[price_level] += weighted_volume
                    else:
                        chip_distribution[price_level] = weighted_volume

            return chip_distribution

        except Exception as e:
            print(f"⚠️ 构建简化筹码分布失败: {e}")
            return None

    # ==================== 四大核心维度算法 ====================

    def detect_volume_anomaly(self, code, stock_data, historical_data):
        """检测量能异动（维度1：吸筹尾声核心标志）"""
        try:
            current_volume = stock_data.get('volume', 0)
            current_price = stock_data.get('latest_price', 0)

            # 获取历史量能数据
            avg_volume_5d = historical_data.get('avg_volume_5d', 1)
            min_volume_7d = historical_data.get('min_volume_7d', current_volume)
            volume_series_7d = historical_data.get('volume_series_7d', [])

            signals = {
                'ground_volume': False,      # 地量信号
                'suffocate_volume': False,   # 窒息量信号
                'volume_breakout': False,    # 倍量突破信号
                'score': 0,                  # 量能异动评分
                'details': {}
            }

            # 算法1：「地量-倍量」转折模型
            if current_volume > 0 and min_volume_7d > 0:
                # 条件1：出现地量（过去7日最低量）
                is_min_vol = current_volume <= min_volume_7d * 1.1  # 允许10%误差
                signals['ground_volume'] = is_min_vol

                if is_min_vol:
                    signals['score'] += 30
                    signals['details']['ground_volume_ratio'] = current_volume / min_volume_7d
                    print(f"   📊 {code} 检测到地量信号，当前量/7日最低量: {current_volume/min_volume_7d:.2f}")

            # 算法2：「窒息量」识别
            if avg_volume_5d > 0:
                volume_ratio = current_volume / avg_volume_5d
                # 量能＜5日均量45%
                if volume_ratio < self.config['volume_min_threshold']:
                    # 检查是否为连续第三日缩量
                    if len(volume_series_7d) >= 3:
                        recent_3_volumes = volume_series_7d[-3:]
                        is_continuous_shrink = all(
                            recent_3_volumes[i] > recent_3_volumes[i+1]
                            for i in range(len(recent_3_volumes)-1)
                        )

                        if is_continuous_shrink:
                            signals['suffocate_volume'] = True
                            signals['score'] += 25
                            signals['details']['suffocate_ratio'] = volume_ratio
                            print(f"   📊 {code} 检测到窒息量信号，量比: {volume_ratio:.2f}")

            # 算法3：次日早盘倍量预警（需要在早盘时段检测）
            current_time = datetime.now().time()
            if (datetime.strptime("09:30", "%H:%M").time() <= current_time <=
                datetime.strptime("09:45", "%H:%M").time()):

                # 如果前日是地量，检测早盘倍量
                if signals['ground_volume'] and min_volume_7d > 0:
                    # 估算早盘15分钟成交量占全天比例
                    early_volume_ratio = current_volume / min_volume_7d
                    if early_volume_ratio > 0.4:  # 早盘15分钟量达前日全天40%
                        signals['volume_breakout'] = True
                        signals['score'] += 35
                        signals['details']['early_volume_ratio'] = early_volume_ratio
                        print(f"   📊 {code} 检测到早盘倍量突破，早盘量比: {early_volume_ratio:.2f}")

            # 综合评分
            if signals['score'] >= 60:
                signals['level'] = '✅'  # 强烈信号
            elif signals['score'] >= 30:
                signals['level'] = '🟡'  # 预警信号
            else:
                signals['level'] = '❌'  # 无信号

            return signals

        except Exception as e:
            print(f"⚠️ {code} 量能异动检测失败: {e}")
            return {'ground_volume': False, 'suffocate_volume': False, 'volume_breakout': False,
                   'score': 0, 'level': '❌', 'details': {}}

    def analyze_price_structure(self, code, stock_data, historical_data):
        """分析价格结构（维度2：突破前兆）"""
        try:
            current_price = stock_data.get('latest_price', 0)
            current_low = stock_data.get('low', current_price)

            # 获取均线数据
            ma5 = historical_data.get('ma5', 0)
            ma10 = historical_data.get('ma10', 0)
            ma20 = historical_data.get('ma20', 0)
            key_support = historical_data.get('price_range', {}).get('key_support', 0)

            signals = {
                'lines_converge': False,     # 三线收拢
                'fake_breakout': False,      # 假破位过滤
                'structure_ready': False,    # 结构就绪
                'score': 0,                  # 价格结构评分
                'details': {}
            }

            # 算法1：「三线收拢」形态识别
            if ma5 > 0 and ma10 > 0 and ma20 > 0:
                gap1 = abs(ma5 - ma10) / ma10
                gap2 = abs(ma10 - ma20) / ma20

                # 均线间距＜1.5%
                if gap1 < self.config['ma_converge_threshold'] and gap2 < 0.02:
                    signals['lines_converge'] = True
                    signals['score'] += 40
                    signals['details']['ma_gap1'] = gap1
                    signals['details']['ma_gap2'] = gap2
                    print(f"   📊 {code} 检测到三线收拢，间距: {gap1:.3f}, {gap2:.3f}")

            # 算法2：「假破位」陷阱过滤
            if key_support > 0 and current_low > 0:
                # 盘中破位但收盘收回关键支撑
                if current_low < key_support and current_price > key_support:
                    signals['fake_breakout'] = True
                    signals['score'] += 20
                    signals['details']['support_test'] = current_low / key_support
                    print(f"   📊 {code} 检测到假破位，支撑测试: {current_low/key_support:.3f}")

            # 算法3：价格位置分析
            if ma20 > 0:
                price_position = current_price / ma20
                if 0.95 <= price_position <= 1.05:  # 价格在20日均线附近5%范围内
                    signals['structure_ready'] = True
                    signals['score'] += 15
                    signals['details']['price_position'] = price_position

            # 综合评分
            if signals['score'] >= 50:
                signals['level'] = '✅'  # 结构完美
            elif signals['score'] >= 25:
                signals['level'] = '🟡'  # 结构良好
            else:
                signals['level'] = '❌'  # 结构不佳

            return signals

        except Exception as e:
            print(f"⚠️ {code} 价格结构分析失败: {e}")
            return {'lines_converge': False, 'fake_breakout': False, 'structure_ready': False,
                   'score': 0, 'level': '❌', 'details': {}}

    def detect_chip_lock(self, code, stock_data, historical_data):
        """检测筹码锁定（维度3：核心）"""
        try:
            current_price = stock_data.get('latest_price', 0)
            chip_distribution = historical_data.get('chip_distribution_30d')

            signals = {
                'chip_stability': False,     # 筹码稳定性
                'low_chip_lock': False,      # 低位筹码锁定
                'high_flow_control': False,  # 上方流动控制
                'score': 0,                  # 筹码锁定评分
                'details': {}
            }

            if not chip_distribution or current_price <= 0:
                signals['level'] = '❌'
                return signals

            # 算法：「低位筹码稳定性」检测
            try:
                # 获取主力成本区（最大筹码峰）
                main_cost = self.get_main_cost_area(chip_distribution)

                if main_cost > 0:
                    # 计算低位筹码（主力成本下方10%）留存率
                    low_threshold = main_cost * 0.9
                    total_chips = sum(chip_distribution.values())
                    low_chips = sum(volume for price, volume in chip_distribution.items()
                                  if float(price) <= low_threshold)

                    if total_chips > 0:
                        low_chip_ratio = low_chips / total_chips
                        signals['details']['low_chip_ratio'] = low_chip_ratio

                        # 低位筹码留存率>65%
                        if low_chip_ratio > self.config['chip_stability_threshold'] / 100:
                            signals['low_chip_lock'] = True
                            signals['score'] += 35
                            print(f"   📊 {code} 低位筹码锁定，留存率: {low_chip_ratio:.1%}")

                    # 计算当日上方筹码流动率
                    high_chips = sum(volume for price, volume in chip_distribution.items()
                                   if float(price) > current_price)

                    if total_chips > 0:
                        high_flow_ratio = high_chips / total_chips
                        signals['details']['high_flow_ratio'] = high_flow_ratio

                        # 上方筹码流动率<15%
                        if high_flow_ratio < 0.15:
                            signals['high_flow_control'] = True
                            signals['score'] += 25
                            print(f"   📊 {code} 上方流动控制，流动率: {high_flow_ratio:.1%}")

                    # 综合筹码稳定性判断
                    if signals['low_chip_lock'] and signals['high_flow_control']:
                        signals['chip_stability'] = True
                        signals['score'] += 20

            except Exception as e:
                print(f"   ⚠️ {code} 筹码分布分析失败: {e}")

            # 综合评分
            if signals['score'] >= 60:
                signals['level'] = '✅'  # 筹码高度锁定
            elif signals['score'] >= 30:
                signals['level'] = '🟡'  # 筹码部分锁定
            else:
                signals['level'] = '❌'  # 筹码分散

            return signals

        except Exception as e:
            print(f"⚠️ {code} 筹码锁定检测失败: {e}")
            return {'chip_stability': False, 'low_chip_lock': False, 'high_flow_control': False,
                   'score': 0, 'level': '❌', 'details': {}}

    def get_main_cost_area(self, chip_distribution):
        """获取主力成本区（最大筹码峰）"""
        try:
            if not chip_distribution:
                return 0

            # 找到筹码量最大的价格区间
            max_volume = 0
            main_cost = 0

            for price, volume in chip_distribution.items():
                if volume > max_volume:
                    max_volume = volume
                    main_cost = float(price)

            return main_cost

        except Exception as e:
            return 0

    def confirm_market_signals(self, code, stock_data, historical_data):
        """盘口确认（维度4：实时决策）"""
        try:
            bid_prices = stock_data.get('bid_prices', [0] * 5)
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_prices = stock_data.get('ask_prices', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            current_price = stock_data.get('latest_price', 0)

            signals = {
                'bid_defense': False,        # 托单防御
                'ask_exhaustion': False,     # 抛压枯竭
                'test_traces': False,        # 试盘痕迹
                'score': 0,                  # 盘口确认评分
                'details': {}
            }

            # 买点确认三条件

            # 条件1：托单防御 - 买一量持续＞卖一量3倍（持续5分钟）
            if bid_volumes[0] > 0 and ask_volumes[0] > 0:
                bid_ask_ratio = bid_volumes[0] / ask_volumes[0]
                if bid_ask_ratio >= self.config['bid_defense_ratio']:
                    signals['bid_defense'] = True
                    signals['score'] += 30
                    signals['details']['bid_ask_ratio'] = bid_ask_ratio
                    print(f"   📊 {code} 托单防御，买一/卖一: {bid_ask_ratio:.1f}")

            # 条件2：抛压枯竭 - 卖五总量＜流通盘的0.03%（小盘股＜1000手）
            ask_total = sum(ask_volumes)
            market_cap = stock_data.get('market_cap', 0)

            # 估算流通盘（基于市值）
            if market_cap > 0 and current_price > 0:
                circulation_shares = market_cap * 100000000 / current_price  # 估算流通股本
                ask_threshold = circulation_shares * self.config['ask_exhaustion_threshold']

                if ask_total < ask_threshold:
                    signals['ask_exhaustion'] = True
                    signals['score'] += 25
                    signals['details']['ask_exhaustion_ratio'] = ask_total / ask_threshold
                    print(f"   📊 {code} 抛压枯竭，卖五总量: {ask_total}")
            else:
                # 小盘股简化判断：卖五总量<1000手
                if ask_total < 1000:
                    signals['ask_exhaustion'] = True
                    signals['score'] += 25
                    signals['details']['ask_total'] = ask_total
                    print(f"   📊 {code} 抛压枯竭（小盘股），卖五总量: {ask_total}")

            # 条件3：试盘痕迹 - 出现≥2次瞬间拉升测试
            # 这里需要基于历史价格变化来检测，简化实现
            high_price = stock_data.get('high', current_price)
            if high_price > current_price:
                pullback_ratio = (high_price - current_price) / current_price
                if 0.005 <= pullback_ratio <= 0.015:  # 0.5%-1.5%的回落
                    signals['test_traces'] = True
                    signals['score'] += 20
                    signals['details']['pullback_ratio'] = pullback_ratio
                    print(f"   📊 {code} 试盘痕迹，回落幅度: {pullback_ratio:.1%}")

            # 综合评分
            if signals['score'] >= 60:
                signals['level'] = '✅'  # 盘口强势
            elif signals['score'] >= 30:
                signals['level'] = '🟡'  # 盘口一般
            else:
                signals['level'] = '❌'  # 盘口疲弱

            return signals

        except Exception as e:
            print(f"⚠️ {code} 盘口确认失败: {e}")
            return {'bid_defense': False, 'ask_exhaustion': False, 'test_traces': False,
                   'score': 0, 'level': '❌', 'details': {}}

    def make_investment_decision(self, code, volume_signals, price_signals, chip_signals, market_signals):
        """投资决策树逻辑（四维度共振判断）"""
        try:
            # 按照报告的决策流程
            decision = {
                'action': '继续观察',        # 决策动作
                'confidence': 0,           # 信心度
                'reason': '',              # 决策原因
                'risk_level': 'medium',    # 风险等级
                'comprehensive_score': 0,   # 综合评分
                'details': {}
            }

            # 计算综合评分
            total_score = (
                volume_signals.get('score', 0) * 0.3 +    # 量能异动权重30%
                price_signals.get('score', 0) * 0.25 +    # 价格结构权重25%
                chip_signals.get('score', 0) * 0.25 +     # 筹码锁定权重25%
                market_signals.get('score', 0) * 0.2      # 盘口确认权重20%
            )

            decision['comprehensive_score'] = total_score

            # 决策树逻辑

            # 第一步：量能异动？
            if not (volume_signals.get('ground_volume') or volume_signals.get('suffocate_volume')):
                decision['action'] = '继续观察'
                decision['reason'] = '量能异动不明显'
                decision['confidence'] = 20
                return decision

            # 第二步：价格收拢？
            if not price_signals.get('lines_converge'):
                decision['action'] = '继续观察'
                decision['reason'] = '价格结构未收拢'
                decision['confidence'] = 30
                return decision

            # 第三步：筹码锁定？
            if not chip_signals.get('chip_stability'):
                decision['action'] = '继续观察'
                decision['reason'] = '筹码锁定不足'
                decision['confidence'] = 40
                return decision

            # 第四步：早盘监测盘口
            current_time = datetime.now().time()

            # 关键时间窗口判断
            is_key_time = (
                (datetime.strptime("09:45", "%H:%M").time() <= current_time <=
                 datetime.strptime("10:00", "%H:%M").time()) or
                (datetime.strptime("13:15", "%H:%M").time() <= current_time <=
                 datetime.strptime("13:30", "%H:%M").time()) or
                (datetime.strptime("14:45", "%H:%M").time() <= current_time <=
                 datetime.strptime("15:00", "%H:%M").time())
            )

            # 第五步：盘口确认
            if market_signals.get('bid_defense') and market_signals.get('ask_exhaustion'):
                # 出现托单防御+抛压枯竭
                decision['action'] = '立即买入'
                decision['confidence'] = 85
                decision['reason'] = '四维度共振，托单防御+抛压枯竭'
                decision['risk_level'] = 'low'

                if is_key_time:
                    decision['confidence'] = 90
                    decision['reason'] += '（关键时间窗口）'

            elif market_signals.get('test_traces'):
                # 有试盘痕迹
                decision['action'] = '回落买入'
                decision['confidence'] = 75
                decision['reason'] = '四维度共振，等待回落至试盘起点'
                decision['risk_level'] = 'low'

            elif total_score >= 70:
                # 综合评分较高但盘口信号不足
                decision['action'] = '密切关注'
                decision['confidence'] = 65
                decision['reason'] = '三维度达标，等待盘口确认'
                decision['risk_level'] = 'medium'

            else:
                decision['action'] = '继续观察'
                decision['confidence'] = 50
                decision['reason'] = '部分条件满足，继续观察'
                decision['risk_level'] = 'medium'

            # 特殊情况处理

            # 如果是地量后的倍量突破
            if volume_signals.get('volume_breakout'):
                if decision['action'] in ['继续观察', '密切关注']:
                    decision['action'] = '密切关注'
                    decision['confidence'] += 10
                    decision['reason'] += '+倍量突破'

            # 如果检测到假破位
            if price_signals.get('fake_breakout'):
                if decision['action'] in ['继续观察', '密切关注']:
                    decision['action'] = '密切关注'
                    decision['confidence'] += 5
                    decision['reason'] += '+假破位'

            # 设置详细信息
            decision['details'] = {
                'volume_level': volume_signals.get('level', '❌'),
                'price_level': price_signals.get('level', '❌'),
                'chip_level': chip_signals.get('level', '❌'),
                'market_level': market_signals.get('level', '❌'),
                'is_key_time': is_key_time,
                'total_score': total_score
            }

            return decision

        except Exception as e:
            print(f"⚠️ {code} 投资决策失败: {e}")
            return {
                'action': '继续观察',
                'confidence': 0,
                'reason': '决策分析失败',
                'risk_level': 'high',
                'comprehensive_score': 0,
                'details': {}
            }

    # ==================== GUI功能方法 ====================

    def add_stocks_dialog(self):
        """添加股票对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加股票代码")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # 说明标签
        info_label = ttk.Label(dialog, text="请输入股票代码（一行一个）：",
                              font=('Microsoft YaHei', 10))
        info_label.pack(pady=10)

        # 文本输入框
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, height=10, width=40,
                              font=('Microsoft YaHei', 10))
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)

        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.configure(yscrollcommand=scrollbar.set)

        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        def add_stocks():
            """添加股票"""
            try:
                content = text_widget.get(1.0, tk.END).strip()
                if not content:
                    messagebox.showwarning("警告", "请输入股票代码")
                    return

                codes = [line.strip() for line in content.split('\n') if line.strip()]
                added_count = 0

                for code in codes:
                    if self.add_stock_to_monitor(code):
                        added_count += 1

                if added_count > 0:
                    messagebox.showinfo("成功", f"成功添加 {added_count} 只股票到筛选列表")
                    dialog.destroy()
                else:
                    messagebox.showerror("错误", "未能添加任何股票，请检查代码格式")

            except Exception as e:
                messagebox.showerror("错误", f"添加股票失败: {e}")

        ttk.Button(button_frame, text="确定", command=add_stocks).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def add_stock_to_monitor(self, code):
        """添加股票到监控列表"""
        try:
            # 标准化股票代码
            code = str(code).strip().upper()
            if len(code) != 6 or not code.isdigit():
                print(f"❌ 无效的股票代码: {code}")
                return False

            # 检查是否已存在
            if code in self.monitored_stocks:
                print(f"⚠️ 股票 {code} 已在监控列表中")
                return False

            # 获取股票名称
            print(f"   🔄 正在获取股票名称: {code}")
            stock_name = f'股票{code}'  # 默认名称

            if enhanced_stock_fetcher:
                try:
                    basic_info = enhanced_stock_fetcher.get_stock_basic_info(code)
                    fetched_name = basic_info.get('name', f'股票{code}')

                    # 检查是否获取到真实名称
                    if fetched_name and not fetched_name.startswith('股票') and fetched_name != '未知':
                        stock_name = fetched_name
                        print(f"   ✅ 成功获取股票名称: {code} -> {stock_name}")
                    else:
                        print(f"   ⚠️ 未获取到真实股票名称，使用默认: {code} -> {stock_name}")

                except Exception as e:
                    print(f"   ❌ 获取股票名称失败 {code}: {e}")
                    stock_name = f'股票{code}'
            else:
                print(f"   ⚠️ 数据获取器不可用，使用默认名称: {code} -> {stock_name}")

            # 初始化历史基础数据
            print(f"🔄 正在初始化 {code} {stock_name}...")
            if self.initialize_stock_monitoring(code, stock_name):

                # 添加到监控列表
                self.monitored_stocks[code] = {
                    'name': stock_name,
                    'data': {},
                    'signals': {},
                    'decision': {},
                    'last_update': None,
                    'initialized': True,
                    'added_time': datetime.now().isoformat(),
                    'alert_history': []
                }

                # 添加到表格
                self.tree.insert('', 'end', iid=code, values=(
                    code, stock_name, '❌', '❌', '❌', '❌', '0', '等待分析', '-', '已初始化'
                ))

                print(f"✅ 添加股票到筛选: {code} {stock_name}")

                # 保存配置
                self.save_config()

                return True

            else:
                print(f"❌ {code} {stock_name} 初始化失败，跳过添加")
                return False

        except Exception as e:
            print(f"❌ 添加股票失败 {code}: {e}")
            return False

    def remove_selected_stock(self):
        """删除选中的股票"""
        try:
            selected_items = self.tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择要删除的股票")
                return

            for item in selected_items:
                code = item
                if code in self.monitored_stocks:
                    stock_name = self.monitored_stocks[code]['name']
                    del self.monitored_stocks[code]

                    # 清理缓存数据
                    if code in self.historical_base_data:
                        del self.historical_base_data[code]
                    if code in self.realtime_data_cache:
                        del self.realtime_data_cache[code]

                    print(f"✅ 删除股票: {code} {stock_name}")

                self.tree.delete(item)

            # 保存配置
            self.save_config()

            messagebox.showinfo("成功", f"成功删除 {len(selected_items)} 只股票")

        except Exception as e:
            messagebox.showerror("错误", f"删除股票失败: {e}")

    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """开始监控"""
        if not self.monitored_stocks:
            messagebox.showwarning("警告", "请先添加股票到筛选列表")
            return

        self.monitoring = True
        self.monitor_btn.config(text="⏸️ 停止监控")
        self.status_label.config(text="🚀 监控运行中")
        self.info_label.config(text=f"💡 正在监控 {len(self.monitored_stocks)} 只股票")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()

        print("🚀 开始四维度筛选")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.monitor_btn.config(text="🚀 开始监控")
        self.status_label.config(text="⏸️ 监控已停止")
        self.info_label.config(text="💡 监控已停止")
        print("⏸️ 停止四维度筛选")

    def monitor_loop(self):
        """监控主循环"""
        print("🔍 四维度筛选循环启动")

        while self.monitoring:
            try:
                if not self.is_trading_time():
                    print("⏰ 非交易时间，暂停筛选")
                    time.sleep(60)  # 非交易时间每分钟检查一次
                    continue

                # 检查是否需要每日数据刷新
                self.daily_data_refresh()

                print(f"🔍 开始四维度筛选 {len(self.monitored_stocks)} 只股票...")

                # 数据获取失败计数器
                failed_count = 0
                total_stocks = len(self.monitored_stocks)

                # 分析每只股票（使用实时数据获取）
                for i, code in enumerate(self.monitored_stocks.keys(), 1):
                    if not self.monitoring:  # 检查是否需要停止
                        break

                    try:
                        # 使用实时数据获取方法（结合历史基础数据）
                        stock_data = self.get_realtime_data_with_history_context(code)

                        if stock_data:
                            # 执行四维度分析
                            analysis_result = self.perform_four_dimension_analysis(code, stock_data)

                            # 更新监控数据
                            self.monitored_stocks[code]['data'] = stock_data
                            self.monitored_stocks[code]['signals'] = analysis_result
                            self.monitored_stocks[code]['last_update'] = datetime.now()

                            # 更新GUI显示
                            self.root.after(0, self.update_stock_display, code, analysis_result)

                            # 检查是否触发买入信号
                            if self.check_buy_signal(analysis_result):
                                self.root.after(0, self.send_buy_alert, code, analysis_result)

                            # 重置状态信息
                            self.root.after(0, lambda: self.info_label.config(text="🔍 正在监控..."))
                        else:
                            print(f"⚠️ {code} 实时数据获取失败")
                            failed_count += 1

                    except Exception as e:
                        print(f"⚠️ 分析股票 {code} 失败: {e}")
                        failed_count += 1
                        continue

                    # 每只股票获取后等待0.3秒，避免请求过于频繁
                    if i < total_stocks:  # 最后一只股票不需要等待
                        time.sleep(0.3)

                # 检查数据获取失败率
                if failed_count > total_stocks * 0.3:  # 超过30%失败
                    error_msg = "数据获取出错，请检查"
                    print(f"❌ 数据获取失败率过高: {failed_count}/{total_stocks}")
                    print("⏸️ 暂停2分钟后重试...")

                    # 更新GUI状态
                    self.root.after(0, lambda: self.info_label.config(text=error_msg, fg="red"))

                    # 暂停2分钟
                    time.sleep(120)
                    continue

                # 获取扫描间隔（关键时间窗口使用更短间隔）
                scan_interval = self.get_current_scan_interval()
                print(f"✅ 四维度筛选完成，等待 {scan_interval} 秒...")
                time.sleep(scan_interval)

            except Exception as e:
                print(f"❌ 监控循环异常: {e}")
                time.sleep(10)  # 出错后等待10秒再继续

    def is_trading_time(self):
        """判断是否为交易时间"""
        current_time = datetime.now().time()

        # 交易时间段
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()

        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)

    def get_current_scan_interval(self):
        """获取当前扫描间隔（关键时间窗口使用更短间隔）"""
        current_time = datetime.now().time()

        # 关键时间窗口：使用更短的扫描间隔
        key_windows = [
            (datetime.strptime("09:45", "%H:%M").time(), datetime.strptime("10:00", "%H:%M").time()),
            (datetime.strptime("13:15", "%H:%M").time(), datetime.strptime("13:30", "%H:%M").time()),
            (datetime.strptime("14:45", "%H:%M").time(), datetime.strptime("15:00", "%H:%M").time())
        ]

        for start_time, end_time in key_windows:
            if start_time <= current_time <= end_time:
                return self.config['key_time_scan_interval']  # 15秒

        return self.config['scan_interval']  # 30秒

    def daily_data_refresh(self):
        """每日数据刷新（在开盘前执行）"""
        try:
            current_time = datetime.now().time()

            # 在9:00-9:25之间执行每日数据刷新
            if (datetime.strptime("09:00", "%H:%M").time() <= current_time <=
                datetime.strptime("09:25", "%H:%M").time()):

                print("🔄 执行每日历史数据刷新...")

                for code in self.monitored_stocks.keys():
                    if self.should_refresh_historical_data(code):
                        print(f"🔄 刷新 {code} 的历史基础数据...")
                        stock_name = self.monitored_stocks[code].get('name', f'股票{code}')
                        self.initialize_stock_monitoring(code, stock_name)

                print("✅ 每日历史数据刷新完成")

        except Exception as e:
            print(f"⚠️ 每日数据刷新失败: {e}")

    def should_refresh_historical_data(self, code):
        """判断是否需要刷新历史数据"""
        try:
            historical = self.historical_base_data.get(code, {})
            loaded_date = historical.get('loaded_date')
            current_date = datetime.now().date()

            # 如果是新的交易日，刷新历史数据
            if not loaded_date or loaded_date < current_date:
                print(f"   📅 {code} 需要刷新：数据日期 {loaded_date} < 当前日期 {current_date}")
                return True

            # 检查筹码分布数据是否完整
            chip_data = historical.get('chip_distribution_30d')
            if not chip_data or not isinstance(chip_data, dict) or len(chip_data) == 0:
                print(f"   📊 {code} 需要刷新：筹码分布数据缺失或无效")
                return True

            # 检查数据时效性（超过1天强制刷新）
            cache_time = historical.get('cache_time')
            if cache_time:
                try:
                    cache_datetime = datetime.fromisoformat(cache_time)
                    if datetime.now() - cache_datetime > timedelta(days=1):
                        print(f"   ⏰ {code} 需要刷新：数据已过期 {cache_datetime}")
                        return True
                except:
                    print(f"   ❓ {code} 需要刷新：缓存时间格式异常")
                    return True

            print(f"   ✅ {code} 数据完整且新鲜，无需刷新")
            return False

        except Exception as e:
            print(f"   ⚠️ {code} 检查失败，需要刷新: {e}")
            return True

    def get_realtime_data_with_history_context(self, code):
        """获取实时数据（结合历史基础数据）"""
        try:
            # 1. 获取历史基础数据
            historical = self.historical_base_data.get(code, {})

            if not historical:
                print(f"⚠️ {code} 缺少历史基础数据，尝试重新初始化...")
                stock_name = self.monitored_stocks.get(code, {}).get('name', f'股票{code}')
                if not self.initialize_stock_monitoring(code, stock_name):
                    return None
                historical = self.historical_base_data.get(code, {})

            # 2. 获取实时数据（每30秒必须更新）
            realtime_quote = enhanced_stock_fetcher.get_realtime_quote([code])
            if realtime_quote.empty:
                print(f"⚠️ {code} 实时行情数据为空")
                return None

            quote_data = realtime_quote.iloc[0]

            # 3. 获取实时五档数据
            depth_data = enhanced_stock_fetcher.get_market_depth(code)
            parsed_depth = self.parse_market_depth(depth_data) if not depth_data.empty else {
                'bid_prices': [0] * 5, 'bid_volumes': [0] * 5,
                'ask_prices': [0] * 5, 'ask_volumes': [0] * 5
            }

            # 4. 构建完整的实时数据
            realtime_data = {
                # 基础实时数据
                'latest_price': float(quote_data.get('current_price', quote_data.get('最新价', 0))),
                'change_pct': float(quote_data.get('change_pct', quote_data.get('涨跌幅', 0))),
                'volume': int(quote_data.get('volume', quote_data.get('成交量', 0))),
                'turnover': float(quote_data.get('amount', quote_data.get('成交额', 0))),
                'high': float(quote_data.get('high', quote_data.get('最高', 0))),
                'low': float(quote_data.get('low', quote_data.get('最低', 0))),
                'open': float(quote_data.get('open', quote_data.get('今开', 0))),
                'market_cap': float(quote_data.get('market_cap', quote_data.get('总市值', 0))),

                # 五档数据
                'bid_prices': parsed_depth['bid_prices'],
                'bid_volumes': parsed_depth['bid_volumes'],
                'ask_prices': parsed_depth['ask_prices'],
                'ask_volumes': parsed_depth['ask_volumes'],

                # 补充历史基准数据（用于算法分析）
                'prev_close': historical.get('prev_close', 0),
                'prev_high': historical.get('prev_high', 0),
                'prev_low': historical.get('prev_low', 0),
                'prev_open': historical.get('prev_open', 0),

                # 成交量基准
                'avg_volume_5d': historical.get('avg_volume_5d', 0),
                'avg_volume_7d': historical.get('avg_volume_7d', 0),
                'min_volume_7d': historical.get('min_volume_7d', 0),
                'volume_series_7d': historical.get('volume_series_7d', []),

                # 均线基准
                'ma5': historical.get('ma5', 0),
                'ma10': historical.get('ma10', 0),
                'ma20': historical.get('ma20', 0),

                # 价格区间统计
                'price_range': historical.get('price_range', {}),

                # 筹码分布数据
                'chip_distribution_30d': historical.get('chip_distribution_30d'),

                # 时间戳
                'timestamp': datetime.now()
            }

            # 5. 缓存实时数据用于持续性监控
            self.cache_realtime_data_for_continuity(code, realtime_data)

            # 6. 缓存实时数据
            self.realtime_data_cache[code] = realtime_data

            return realtime_data

        except Exception as e:
            print(f"❌ 获取 {code} 实时数据失败: {e}")
            return None

    def parse_market_depth(self, depth_df):
        """解析五档行情数据"""
        try:
            depth_data = {
                'bid_prices': [0] * 5,  # 买一到买五价格
                'bid_volumes': [0] * 5, # 买一到买五数量
                'ask_prices': [0] * 5,  # 卖一到卖五价格
                'ask_volumes': [0] * 5, # 卖一到卖五数量
            }

            if depth_df.empty:
                return depth_data

            row = depth_df.iloc[0]

            # 主应用的五档数据格式：b1,bv1,b2,bv2... (买盘) s1,sv1,s2,sv2... (卖盘)
            for i in range(5):
                # 买盘数据 (b1, bv1, b2, bv2, ...)
                bid_price_key = f'b{i+1}'
                bid_volume_key = f'bv{i+1}'

                if bid_price_key in row and row[bid_price_key] is not None:
                    depth_data['bid_prices'][i] = float(row[bid_price_key])
                if bid_volume_key in row and row[bid_volume_key] is not None:
                    depth_data['bid_volumes'][i] = int(row[bid_volume_key])

                # 卖盘数据 (s1, sv1, s2, sv2, ...)
                ask_price_key = f's{i+1}'
                ask_volume_key = f'sv{i+1}'

                if ask_price_key in row and row[ask_price_key] is not None:
                    depth_data['ask_prices'][i] = float(row[ask_price_key])
                if ask_volume_key in row and row[ask_volume_key] is not None:
                    depth_data['ask_volumes'][i] = int(row[ask_volume_key])

            # 备用解析：尝试其他格式
            if all(p == 0 for p in depth_data['bid_prices']):
                # 尝试中文格式
                for i in range(5):
                    bid_price_key = f'买{i+1}'
                    bid_volume_key = f'买量{i+1}'
                    ask_price_key = f'卖{i+1}'
                    ask_volume_key = f'卖量{i+1}'

                    if bid_price_key in row and row[bid_price_key] is not None:
                        depth_data['bid_prices'][i] = float(row[bid_price_key])
                    if bid_volume_key in row and row[bid_volume_key] is not None:
                        depth_data['bid_volumes'][i] = int(row[bid_volume_key])
                    if ask_price_key in row and row[ask_price_key] is not None:
                        depth_data['ask_prices'][i] = float(row[ask_price_key])
                    if ask_volume_key in row and row[ask_volume_key] is not None:
                        depth_data['ask_volumes'][i] = int(row[ask_volume_key])

            return depth_data

        except Exception as e:
            print(f"⚠️ 解析五档数据失败: {e}")
            return {
                'bid_prices': [0] * 5,
                'bid_volumes': [0] * 5,
                'ask_prices': [0] * 5,
                'ask_volumes': [0] * 5,
            }

    def cache_realtime_data_for_continuity(self, code, realtime_data):
        """缓存实时数据用于持续性监控"""
        try:
            # 初始化持续性监控缓存
            if not hasattr(self, 'continuity_cache'):
                self.continuity_cache = {}

            if code not in self.continuity_cache:
                self.continuity_cache[code] = {
                    'bid_defense_history': [],  # 托单防御历史
                    'price_history': [],        # 价格历史（用于试盘痕迹）
                    'volume_history': []        # 成交量历史
                }

            current_time = datetime.now()

            # 缓存托单防御数据（保留5分钟）
            bid_ask_ratio = 0
            if realtime_data['ask_volumes'][0] > 0:
                bid_ask_ratio = realtime_data['bid_volumes'][0] / realtime_data['ask_volumes'][0]

            self.continuity_cache[code]['bid_defense_history'].append({
                'timestamp': current_time,
                'bid_ask_ratio': bid_ask_ratio,
                'bid1_volume': realtime_data['bid_volumes'][0],
                'ask1_volume': realtime_data['ask_volumes'][0]
            })

            # 缓存价格数据（保留15分钟用于试盘痕迹检测）
            self.continuity_cache[code]['price_history'].append({
                'timestamp': current_time,
                'price': realtime_data['latest_price'],
                'high': realtime_data['high'],
                'low': realtime_data['low']
            })

            # 缓存成交量数据
            self.continuity_cache[code]['volume_history'].append({
                'timestamp': current_time,
                'volume': realtime_data['volume']
            })

            # 清理过期数据
            cutoff_time_5min = current_time - timedelta(minutes=5)
            cutoff_time_15min = current_time - timedelta(minutes=15)

            # 保留5分钟内的托单防御数据
            self.continuity_cache[code]['bid_defense_history'] = [
                item for item in self.continuity_cache[code]['bid_defense_history']
                if item['timestamp'] > cutoff_time_5min
            ]

            # 保留15分钟内的价格数据
            self.continuity_cache[code]['price_history'] = [
                item for item in self.continuity_cache[code]['price_history']
                if item['timestamp'] > cutoff_time_15min
            ]

            # 保留15分钟内的成交量数据
            self.continuity_cache[code]['volume_history'] = [
                item for item in self.continuity_cache[code]['volume_history']
                if item['timestamp'] > cutoff_time_15min
            ]

        except Exception as e:
            print(f"⚠️ {code} 持续性数据缓存失败: {e}")

    def perform_four_dimension_analysis(self, code, stock_data):
        """执行四维度分析"""
        try:
            # 获取历史基础数据
            historical_data = self.historical_base_data.get(code, {})

            # 执行四大维度分析
            volume_signals = self.detect_volume_anomaly(code, stock_data, historical_data)
            price_signals = self.analyze_price_structure(code, stock_data, historical_data)
            chip_signals = self.detect_chip_lock(code, stock_data, historical_data)
            market_signals = self.confirm_market_signals_enhanced(code, stock_data, historical_data)

            # 投资决策树分析
            decision = self.make_investment_decision(code, volume_signals, price_signals, chip_signals, market_signals)

            # 构建完整的分析结果
            analysis_result = {
                'volume_signals': volume_signals,
                'price_signals': price_signals,
                'chip_signals': chip_signals,
                'market_signals': market_signals,
                'decision': decision,
                'timestamp': datetime.now(),
                'comprehensive_score': decision.get('comprehensive_score', 0)
            }

            return analysis_result

        except Exception as e:
            print(f"❌ {code} 四维度分析失败: {e}")
            return {
                'volume_signals': {'level': '❌', 'score': 0},
                'price_signals': {'level': '❌', 'score': 0},
                'chip_signals': {'level': '❌', 'score': 0},
                'market_signals': {'level': '❌', 'score': 0},
                'decision': {'action': '分析失败', 'confidence': 0},
                'timestamp': datetime.now(),
                'comprehensive_score': 0
            }

    def confirm_market_signals_enhanced(self, code, stock_data, historical_data):
        """增强版盘口确认（包含持续性监控）"""
        try:
            bid_prices = stock_data.get('bid_prices', [0] * 5)
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_prices = stock_data.get('ask_prices', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            current_price = stock_data.get('latest_price', 0)

            signals = {
                'bid_defense': False,        # 托单防御
                'bid_defense_continuous': False,  # 持续托单防御
                'ask_exhaustion': False,     # 抛压枯竭
                'test_traces': False,        # 试盘痕迹
                'test_traces_count': 0,      # 试盘次数
                'score': 0,                  # 盘口确认评分
                'details': {}
            }

            # 条件1：托单防御 - 买一量持续＞卖一量3倍（持续5分钟）
            if bid_volumes[0] > 0 and ask_volumes[0] > 0:
                bid_ask_ratio = bid_volumes[0] / ask_volumes[0]
                if bid_ask_ratio >= self.config['bid_defense_ratio']:
                    signals['bid_defense'] = True
                    signals['score'] += 20
                    signals['details']['bid_ask_ratio'] = bid_ask_ratio

                    # 检查持续性（5分钟）
                    continuous_defense = self.check_bid_defense_continuity(code)
                    if continuous_defense:
                        signals['bid_defense_continuous'] = True
                        signals['score'] += 15
                        signals['details']['continuous_minutes'] = continuous_defense
                        print(f"   📊 {code} 持续托单防御 {continuous_defense} 分钟，买一/卖一: {bid_ask_ratio:.1f}")
                    else:
                        print(f"   📊 {code} 托单防御，买一/卖一: {bid_ask_ratio:.1f}")

            # 条件2：抛压枯竭 - 卖五总量＜流通盘的0.03%（小盘股＜1000手）
            ask_total = sum(ask_volumes)
            market_cap = stock_data.get('market_cap', 0)

            # 估算流通盘（基于市值）
            if market_cap > 0 and current_price > 0:
                circulation_shares = market_cap * 100000000 / current_price  # 估算流通股本
                ask_threshold = circulation_shares * self.config['ask_exhaustion_threshold']

                if ask_total < ask_threshold:
                    signals['ask_exhaustion'] = True
                    signals['score'] += 25
                    signals['details']['ask_exhaustion_ratio'] = ask_total / ask_threshold
                    print(f"   📊 {code} 抛压枯竭，卖五总量: {ask_total}")
            else:
                # 小盘股简化判断：卖五总量<1000手
                if ask_total < 1000:
                    signals['ask_exhaustion'] = True
                    signals['score'] += 25
                    signals['details']['ask_total'] = ask_total
                    print(f"   📊 {code} 抛压枯竭（小盘股），卖五总量: {ask_total}")

            # 条件3：试盘痕迹 - 出现≥2次瞬间拉升测试
            test_traces_result = self.detect_test_traces(code, current_price)
            if test_traces_result['count'] >= 2:
                signals['test_traces'] = True
                signals['test_traces_count'] = test_traces_result['count']
                signals['score'] += 20
                signals['details']['test_traces'] = test_traces_result
                print(f"   📊 {code} 试盘痕迹，检测到 {test_traces_result['count']} 次拉升测试")
            elif test_traces_result['count'] == 1:
                signals['score'] += 10
                signals['details']['test_traces'] = test_traces_result
                print(f"   📊 {code} 疑似试盘痕迹，检测到 {test_traces_result['count']} 次拉升测试")

            # 综合评分
            if signals['score'] >= 60:
                signals['level'] = '✅'  # 盘口强势
            elif signals['score'] >= 30:
                signals['level'] = '🟡'  # 盘口一般
            else:
                signals['level'] = '❌'  # 盘口疲弱

            return signals

        except Exception as e:
            print(f"⚠️ {code} 增强版盘口确认失败: {e}")
            return {'bid_defense': False, 'bid_defense_continuous': False, 'ask_exhaustion': False,
                   'test_traces': False, 'test_traces_count': 0, 'score': 0, 'level': '❌', 'details': {}}

    def check_bid_defense_continuity(self, code):
        """检查托单防御的持续性（5分钟）"""
        try:
            if not hasattr(self, 'continuity_cache') or code not in self.continuity_cache:
                return 0

            bid_history = self.continuity_cache[code]['bid_defense_history']
            if len(bid_history) < 2:
                return 0

            # 检查最近5分钟内持续满足托单防御条件的时间
            continuous_minutes = 0
            defense_threshold = self.config['bid_defense_ratio']

            for record in bid_history:
                if record['bid_ask_ratio'] >= defense_threshold:
                    continuous_minutes += 0.5  # 每30秒记录一次，相当于0.5分钟

            return continuous_minutes if continuous_minutes >= 2.5 else 0  # 至少持续2.5分钟

        except Exception as e:
            return 0

    def detect_test_traces(self, code, current_price):
        """检测试盘痕迹（≥2次瞬间拉升测试）"""
        try:
            if not hasattr(self, 'continuity_cache') or code not in self.continuity_cache:
                return {'count': 0, 'details': []}

            price_history = self.continuity_cache[code]['price_history']
            if len(price_history) < 3:
                return {'count': 0, 'details': []}

            test_traces = []

            # 检测拉升-回落模式
            for i in range(1, len(price_history) - 1):
                prev_price = price_history[i-1]['price']
                curr_high = price_history[i]['high']
                next_price = price_history[i+1]['price']

                # 检测拉升幅度（1分钟内涨1%）
                if curr_high > prev_price:
                    pullup_ratio = (curr_high - prev_price) / prev_price

                    # 检测回落幅度（回落0.4%）
                    if next_price < curr_high:
                        pullback_ratio = (curr_high - next_price) / curr_high

                        # 符合试盘条件：拉升0.8%-2%，回落0.3%-0.8%
                        if (0.008 <= pullup_ratio <= 0.02 and
                            0.003 <= pullback_ratio <= 0.008):

                            test_traces.append({
                                'timestamp': price_history[i]['timestamp'],
                                'pullup_ratio': pullup_ratio,
                                'pullback_ratio': pullback_ratio,
                                'high_price': curr_high
                            })

            return {
                'count': len(test_traces),
                'details': test_traces
            }

        except Exception as e:
            return {'count': 0, 'details': []}

    def check_buy_signal(self, analysis_result):
        """检查是否触发买入信号"""
        try:
            decision = analysis_result.get('decision', {})
            action = decision.get('action', '继续观察')
            confidence = decision.get('confidence', 0)

            # 触发买入信号的条件
            buy_actions = ['立即买入', '回落买入']
            high_confidence_threshold = 70

            return action in buy_actions and confidence >= high_confidence_threshold

        except Exception as e:
            return False

    def send_buy_alert(self, code, analysis_result):
        """发送买入提醒"""
        try:
            stock_name = self.monitored_stocks[code]['name']
            decision = analysis_result.get('decision', {})
            action = decision.get('action', '未知')
            confidence = decision.get('confidence', 0)
            reason = decision.get('reason', '')
            comprehensive_score = analysis_result.get('comprehensive_score', 0)

            # 构建通知消息
            title = f"🎯 四维度共振买入信号"

            message_lines = [
                f"股票: {code} {stock_name}",
                f"决策: {action}",
                f"信心度: {confidence}%",
                f"综合评分: {comprehensive_score:.1f}",
                f"原因: {reason}",
                "",
                "四维度状态:",
                f"量能异动: {analysis_result['volume_signals']['level']}",
                f"价格结构: {analysis_result['price_signals']['level']}",
                f"筹码锁定: {analysis_result['chip_signals']['level']}",
                f"盘口确认: {analysis_result['market_signals']['level']}"
            ]

            message = "\n".join(message_lines)

            # 发送Windows通知
            if NOTIFICATION_AVAILABLE:
                try:
                    toaster.show_toast(
                        title,
                        message,
                        duration=self.config['notification_duration'],
                        icon_path=None,
                        threaded=True
                    )
                    print(f"🔔 发送买入提醒: {code} {stock_name} - {action}")
                except Exception as e:
                    print(f"⚠️ 发送通知失败: {e}")
            else:
                print(f"🔔 买入信号（通知功能不可用）: {code} {stock_name} - {action}")

            # 记录到监控历史
            if 'alert_history' not in self.monitored_stocks[code]:
                self.monitored_stocks[code]['alert_history'] = []

            self.monitored_stocks[code]['alert_history'].append({
                'timestamp': datetime.now(),
                'action': action,
                'confidence': confidence,
                'comprehensive_score': comprehensive_score,
                'reason': reason
            })

        except Exception as e:
            print(f"❌ 发送买入提醒失败 {code}: {e}")

    def update_stock_display(self, code, analysis_result):
        """更新股票显示"""
        try:
            if code not in self.monitored_stocks:
                return

            stock_name = self.monitored_stocks[code]['name']
            stock_data = self.monitored_stocks[code]['data']

            # 获取四维度状态
            volume_level = analysis_result['volume_signals']['level']
            price_level = analysis_result['price_signals']['level']
            chip_level = analysis_result['chip_signals']['level']
            market_level = analysis_result['market_signals']['level']

            # 获取决策信息
            decision = analysis_result['decision']
            action = decision.get('action', '未知')
            comprehensive_score = analysis_result.get('comprehensive_score', 0)

            # 获取最新价格
            latest_price = stock_data.get('latest_price', 0)
            update_time = datetime.now().strftime("%H:%M:%S")

            # 更新表格显示
            if self.tree.exists(code):
                self.tree.item(code, values=(
                    code,                           # 股票代码
                    stock_name,                     # 股票名称
                    volume_level,                   # 量能异动
                    price_level,                    # 价格结构
                    chip_level,                     # 筹码锁定
                    market_level,                   # 盘口确认
                    f"{comprehensive_score:.1f}",   # 综合评分
                    action,                         # 决策状态
                    f"{latest_price:.2f}" if latest_price > 0 else "-",  # 最新价格
                    update_time                     # 更新时间
                ))

                # 根据决策状态设置行颜色
                if action == '立即买入':
                    self.tree.set(code, '决策状态', f"🚀 {action}")
                elif action == '回落买入':
                    self.tree.set(code, '决策状态', f"⏳ {action}")
                elif action == '密切关注':
                    self.tree.set(code, '决策状态', f"👀 {action}")
                else:
                    self.tree.set(code, '决策状态', f"⏸️ {action}")

        except Exception as e:
            print(f"⚠️ 更新股票显示失败 {code}: {e}")

    def clear_all_stocks(self):
        """清空所有股票"""
        try:
            if not self.monitored_stocks:
                messagebox.showinfo("提示", "当前没有监控的股票")
                return

            # 确认对话框
            stock_count = len(self.monitored_stocks)
            result = messagebox.askyesno(
                "确认清空",
                f"确定要清空所有 {stock_count} 只股票吗？\n\n此操作将：\n"
                "• 清空监控列表\n"
                "• 清空历史数据缓存\n"
                "• 删除配置文件\n"
                "• 清空表格显示\n\n"
                "此操作不可撤销！",
                icon='warning'
            )

            if not result:
                return

            print("🗑️ 开始清空所有股票...")

            # 1. 停止筛选循环
            if hasattr(self, 'screening_active') and self.screening_active:
                self.stop_screening()
                print("   ✅ 已停止筛选循环")

            # 2. 清空监控股票字典
            cleared_count = len(self.monitored_stocks)
            self.monitored_stocks.clear()
            print(f"   ✅ 已清空监控列表: {cleared_count} 只股票")

            # 3. 清空历史基础数据
            self.historical_base_data.clear()
            print("   ✅ 已清空历史基础数据缓存")

            # 4. 清空表格显示
            for item in self.tree.get_children():
                self.tree.delete(item)
            print("   ✅ 已清空表格显示")

            # 5. 删除配置文件
            try:
                config_file = Path("config_screening.json")
                if config_file.exists():
                    config_file.unlink()
                    print("   ✅ 已删除配置文件")
            except Exception as e:
                print(f"   ⚠️ 删除配置文件失败: {e}")

            # 6. 清空历史数据缓存文件
            try:
                import shutil
                if self.data_cache_dir.exists():
                    shutil.rmtree(self.data_cache_dir)
                    self.data_cache_dir.mkdir(exist_ok=True)
                    print("   ✅ 已清空历史数据缓存文件")
            except Exception as e:
                print(f"   ⚠️ 清空缓存文件失败: {e}")

            # 7. 更新状态显示
            self.status_label.config(text="⏸️ 筛选已停止")

            print("✅ 所有股票清空完成")
            messagebox.showinfo("完成", f"已成功清空所有 {cleared_count} 只股票！")

        except Exception as e:
            error_msg = f"清空股票失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def show_four_dimension_analysis(self):
        """显示四维度分析（收盘后历史数据分析）"""
        try:
            if not self.monitored_stocks:
                messagebox.showwarning("警告", "请先添加股票到监控列表")
                return

            # 首先检查历史数据完整性
            print("🔍 检查历史数据完整性...")
            self.check_all_historical_data()

            print("\n" + "="*60)
            print("🔍 开始收盘后四维度分析（基于历史数据）")
            print("="*60)

            # 创建分析结果窗口
            analysis_window = tk.Toplevel(self.root)
            analysis_window.title("📊 收盘后四维度分析结果")
            analysis_window.geometry("1000x700")
            analysis_window.transient(self.root)

            # 创建结果显示区域
            result_frame = ttk.Frame(analysis_window)
            result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 创建表格显示分析结果
            columns = ('股票代码', '股票名称', '量能异动', '价格结构', '筹码锁定', '综合评分', '建议操作', '次日预警')
            result_tree = ttk.Treeview(result_frame, columns=columns, show='headings', height=15)
            result_tree.pack(fill=tk.BOTH, expand=True)

            # 设置列标题和宽度
            column_widths = {
                '股票代码': 80, '股票名称': 100, '量能异动': 80, '价格结构': 80,
                '筹码锁定': 80, '综合评分': 80, '建议操作': 120, '次日预警': 150
            }

            for col in columns:
                result_tree.heading(col, text=col)
                result_tree.column(col, width=column_widths.get(col, 100))

            # 添加滚动条
            scrollbar = ttk.Scrollbar(result_frame, orient=tk.VERTICAL, command=result_tree.yview)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            result_tree.configure(yscrollcommand=scrollbar.set)

            # 执行收盘后分析
            analysis_results = []
            for code in self.monitored_stocks.keys():
                try:
                    print(f"🔍 分析股票: {code} {self.monitored_stocks[code]['name']}")
                    result = self.perform_end_of_day_analysis(code)
                    analysis_results.append(result)

                    # 添加到表格
                    result_tree.insert('', 'end', values=(
                        result['code'],
                        result['name'],
                        result['volume_signal'],
                        result['price_signal'],
                        result['chip_signal'],
                        f"{result['score']:.1f}",
                        result['recommendation'],
                        result['next_day_alert']
                    ))

                except Exception as e:
                    print(f"❌ 分析股票 {code} 失败: {e}")
                    result_tree.insert('', 'end', values=(
                        code, self.monitored_stocks[code]['name'],
                        '❌', '❌', '❌', '0', '分析失败', '无'
                    ))

            # 添加统计信息
            stats_frame = ttk.Frame(analysis_window)
            stats_frame.pack(fill=tk.X, padx=10, pady=5)

            qualified_count = len([r for r in analysis_results if r['score'] >= 60])
            stats_text = f"📊 分析完成：共 {len(self.monitored_stocks)} 只股票，{qualified_count} 只达标（评分≥60分）"
            ttk.Label(stats_frame, text=stats_text, font=('Microsoft YaHei', 10, 'bold')).pack()

            # 添加操作按钮
            button_frame = ttk.Frame(analysis_window)
            button_frame.pack(pady=10)

            def export_results():
                """导出分析结果"""
                try:
                    import csv
                    from tkinter import filedialog

                    filename = filedialog.asksaveasfilename(
                        defaultextension=".csv",
                        filetypes=[("CSV files", "*.csv"), ("All files", "*.*")],
                        title="保存分析结果"
                    )

                    if filename:
                        with open(filename, 'w', newline='', encoding='utf-8-sig') as csvfile:
                            writer = csv.writer(csvfile)
                            writer.writerow(['股票代码', '股票名称', '量能异动', '价格结构', '筹码锁定',
                                           '综合评分', '建议操作', '次日预警', '分析时间'])

                            for result in analysis_results:
                                writer.writerow([
                                    result['code'], result['name'], result['volume_signal'],
                                    result['price_signal'], result['chip_signal'], f"{result['score']:.1f}",
                                    result['recommendation'], result['next_day_alert'],
                                    datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                                ])

                        messagebox.showinfo("成功", f"分析结果已保存到: {filename}")
                except Exception as e:
                    messagebox.showerror("错误", f"导出失败: {e}")

            ttk.Button(button_frame, text="📁 导出结果", command=export_results).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="❌ 关闭", command=analysis_window.destroy).pack(side=tk.LEFT, padx=5)

            print("✅ 收盘后四维度分析完成")
            print("="*60)

        except Exception as e:
            error_msg = f"四维度分析失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def perform_end_of_day_analysis(self, code):
        """执行收盘后分析（基于历史数据）"""
        try:
            stock_name = self.monitored_stocks[code]['name']

            # 获取历史基础数据
            historical_data = self.historical_base_data.get(code, {})
            if not historical_data:
                print(f"⚠️ {code} 缺少历史基础数据，尝试重新初始化...")
                stock_name = self.monitored_stocks.get(code, {}).get('name', f'股票{code}')
                if not self.initialize_stock_monitoring(code, stock_name):
                    raise Exception("历史数据初始化失败")
                historical_data = self.historical_base_data.get(code, {})

            # 获取K线历史数据
            kline_history = historical_data.get('kline_history_30d')

            # 处理从缓存加载的数据（可能是list格式）
            if isinstance(kline_history, list):
                # 从list重建DataFrame
                if len(kline_history) == 0:
                    raise Exception("K线历史数据不足")
                kline_history = pd.DataFrame(kline_history)
            elif kline_history is None or (hasattr(kline_history, 'empty') and kline_history.empty):
                raise Exception("K线历史数据不足")
            elif not isinstance(kline_history, pd.DataFrame):
                raise Exception("K线历史数据格式错误")

            # 获取最新一天的数据作为"今日"数据
            today_data = kline_history.iloc[-1]

            # 执行三大维度分析（基于历史数据）
            volume_result = self.analyze_volume_eod(code, today_data, kline_history, historical_data)
            price_result = self.analyze_price_structure_eod(code, today_data, kline_history, historical_data)
            chip_result = self.analyze_chip_lock_eod(code, today_data, historical_data)

            # 计算综合评分
            total_score = (
                volume_result['score'] * 0.35 +    # 量能异动权重35%
                price_result['score'] * 0.35 +     # 价格结构权重35%
                chip_result['score'] * 0.30        # 筹码锁定权重30%
            )

            # 生成建议操作
            recommendation = self.generate_eod_recommendation(total_score, volume_result, price_result, chip_result)

            # 生成次日预警条件
            next_day_alert = self.generate_next_day_alert(code, today_data, total_score)

            result = {
                'code': code,
                'name': stock_name,
                'volume_signal': volume_result['level'],
                'price_signal': price_result['level'],
                'chip_signal': chip_result['level'],
                'score': total_score,
                'recommendation': recommendation,
                'next_day_alert': next_day_alert,
                'details': {
                    'volume_details': volume_result,
                    'price_details': price_result,
                    'chip_details': chip_result
                }
            }

            print(f"   📊 {code} 分析完成：量能{volume_result['level']} 价格{price_result['level']} 筹码{chip_result['level']} 评分{total_score:.1f}")

            return result

        except Exception as e:
            print(f"❌ {code} 收盘后分析失败: {e}")
            return {
                'code': code,
                'name': self.monitored_stocks[code]['name'],
                'volume_signal': '❌',
                'price_signal': '❌',
                'chip_signal': '❌',
                'score': 0,
                'recommendation': '分析失败',
                'next_day_alert': '无',
                'details': {}
            }

    def analyze_volume_eod(self, code, today_data, kline_history, historical_data):
        """收盘后量能异动分析"""
        try:
            # 获取成交量数据
            today_volume = int(today_data.get('成交量', today_data.get('volume', 0)))
            volume_series = [int(row.get('成交量', row.get('volume', 0)))
                           for _, row in kline_history.tail(7).iterrows()]

            avg_volume_5d = historical_data.get('avg_volume_5d', 1)

            signals = {
                'ground_volume': False,      # 地量信号
                'suffocate_volume': False,   # 窒息量信号
                'score': 0,
                'details': {}
            }

            # 算法1：地量检测（7日最低量）
            if len(volume_series) >= 7:
                min_volume_7d = min(volume_series)
                if today_volume <= min_volume_7d * 1.1:  # 允许10%误差
                    signals['ground_volume'] = True
                    signals['score'] += 40
                    signals['details']['ground_volume_ratio'] = today_volume / min_volume_7d
                    print(f"      ✅ {code} 地量信号，当前量/7日最低量: {today_volume/min_volume_7d:.2f}")

            # 算法2：窒息量检测（<5日均量45%且三连缩）
            if avg_volume_5d > 0:
                volume_ratio = today_volume / avg_volume_5d
                if volume_ratio < 0.45:  # 量能<5日均量45%
                    # 检查是否三连缩
                    if len(volume_series) >= 3:
                        recent_3_volumes = volume_series[-3:]
                        is_continuous_shrink = all(
                            recent_3_volumes[i] >= recent_3_volumes[i+1]
                            for i in range(len(recent_3_volumes)-1)
                        )

                        if is_continuous_shrink:
                            signals['suffocate_volume'] = True
                            signals['score'] += 35
                            signals['details']['suffocate_ratio'] = volume_ratio
                            print(f"      ✅ {code} 窒息量信号，量比: {volume_ratio:.2f}")

            # 综合评分
            if signals['score'] >= 60:
                signals['level'] = '✅'
            elif signals['score'] >= 30:
                signals['level'] = '🟡'
            else:
                signals['level'] = '❌'

            return signals

        except Exception as e:
            print(f"      ⚠️ {code} 量能分析失败: {e}")
            return {'ground_volume': False, 'suffocate_volume': False, 'score': 0, 'level': '❌', 'details': {}}

    def analyze_price_structure_eod(self, code, today_data, kline_history, historical_data):
        """收盘后价格结构分析"""
        try:
            # 获取价格数据
            today_close = float(today_data.get('收盘', today_data.get('close', 0)))
            today_high = float(today_data.get('最高', today_data.get('high', 0)))
            today_low = float(today_data.get('最低', today_data.get('low', 0)))

            # 获取均线数据
            ma5 = historical_data.get('ma5', 0)
            ma10 = historical_data.get('ma10', 0)
            ma20 = historical_data.get('ma20', 0)

            signals = {
                'lines_converge': False,     # 三线收拢
                'fake_breakout': False,      # 假破位
                'score': 0,
                'details': {}
            }

            # 算法1：三线收拢检测
            if ma5 > 0 and ma10 > 0 and ma20 > 0:
                gap1 = abs(ma5 - ma10) / ma10
                gap2 = abs(ma10 - ma20) / ma20

                # 均线间距<1.5%和2%
                if gap1 < 0.015 and gap2 < 0.02:
                    signals['lines_converge'] = True
                    signals['score'] += 45
                    signals['details']['ma_gap1'] = gap1
                    signals['details']['ma_gap2'] = gap2
                    print(f"      ✅ {code} 三线收拢，间距: {gap1:.3f}, {gap2:.3f}")

            # 算法2：假破位检测
            # 获取30日最低点作为关键支撑
            if len(kline_history) >= 30:
                lows = [float(row.get('最低', row.get('low', 0))) for _, row in kline_history.tail(30).iterrows()]
                key_support = min([low for low in lows if low > 0])

                # 盘中破位但收盘收回关键支撑
                if today_low < key_support and today_close > key_support:
                    signals['fake_breakout'] = True
                    signals['score'] += 25
                    signals['details']['support_test'] = today_low / key_support
                    signals['details']['key_support'] = key_support
                    print(f"      ✅ {code} 假破位，支撑{key_support:.2f}，最低{today_low:.2f}，收盘{today_close:.2f}")

            # 算法3：价格位置分析
            if ma20 > 0:
                price_position = today_close / ma20
                if 0.95 <= price_position <= 1.05:  # 价格在20日均线附近5%范围内
                    signals['score'] += 15
                    signals['details']['price_position'] = price_position

            # 综合评分
            if signals['score'] >= 50:
                signals['level'] = '✅'
            elif signals['score'] >= 25:
                signals['level'] = '🟡'
            else:
                signals['level'] = '❌'

            return signals

        except Exception as e:
            print(f"      ⚠️ {code} 价格结构分析失败: {e}")
            return {'lines_converge': False, 'fake_breakout': False, 'score': 0, 'level': '❌', 'details': {}}

    def analyze_chip_lock_eod(self, code, today_data, historical_data):
        """收盘后筹码锁定分析"""
        try:
            today_close = float(today_data.get('收盘', today_data.get('close', 0)))
            chip_distribution = historical_data.get('chip_distribution_30d')

            signals = {
                'chip_stability': False,     # 筹码稳定性
                'low_chip_lock': False,      # 低位筹码锁定
                'score': 0,
                'details': {}
            }

            if not chip_distribution or today_close <= 0:
                signals['level'] = '❌'
                return signals

            try:
                # 获取主力成本区（最大筹码峰）
                main_cost = self.get_main_cost_area(chip_distribution)

                if main_cost > 0:
                    # 计算低位筹码（主力成本下方10%）留存率
                    low_threshold = main_cost * 0.9
                    total_chips = sum(chip_distribution.values())
                    low_chips = sum(volume for price, volume in chip_distribution.items()
                                  if float(price) <= low_threshold)

                    if total_chips > 0:
                        low_chip_ratio = low_chips / total_chips
                        signals['details']['low_chip_ratio'] = low_chip_ratio

                        # 低位筹码留存率>65%
                        if low_chip_ratio > 0.65:
                            signals['low_chip_lock'] = True
                            signals['score'] += 40
                            print(f"      ✅ {code} 低位筹码锁定，留存率: {low_chip_ratio:.1%}")

                    # 计算当日收盘价上方筹码流动率
                    high_chips = sum(volume for price, volume in chip_distribution.items()
                                   if float(price) > today_close)

                    if total_chips > 0:
                        high_flow_ratio = high_chips / total_chips
                        signals['details']['high_flow_ratio'] = high_flow_ratio

                        # 上方筹码流动率<15%
                        if high_flow_ratio < 0.15:
                            signals['score'] += 30
                            print(f"      ✅ {code} 上方流动控制，流动率: {high_flow_ratio:.1%}")

                    # 综合筹码稳定性判断
                    if signals['low_chip_lock'] and high_flow_ratio < 0.15:
                        signals['chip_stability'] = True
                        signals['score'] += 10

            except Exception as e:
                print(f"      ⚠️ {code} 筹码分布分析失败: {e}")

            # 综合评分
            if signals['score'] >= 60:
                signals['level'] = '✅'
            elif signals['score'] >= 30:
                signals['level'] = '🟡'
            else:
                signals['level'] = '❌'

            return signals

        except Exception as e:
            print(f"      ⚠️ {code} 筹码锁定分析失败: {e}")
            return {'chip_stability': False, 'low_chip_lock': False, 'score': 0, 'level': '❌', 'details': {}}

    def generate_eod_recommendation(self, total_score, volume_result, price_result, chip_result):
        """生成收盘后建议操作"""
        try:
            if total_score >= 70:
                if (volume_result['level'] == '✅' and
                    price_result['level'] == '✅' and
                    chip_result['level'] == '✅'):
                    return "🚀 重点关注"
                else:
                    return "👀 密切关注"
            elif total_score >= 50:
                return "📝 加入观察"
            elif total_score >= 30:
                return "⏳ 继续跟踪"
            else:
                return "❌ 暂不关注"

        except Exception as e:
            return "❓ 评估失败"

    def generate_next_day_alert(self, code, today_data, total_score):
        """生成次日预警条件"""
        try:
            if total_score < 50:
                return "无预警"

            today_high = float(today_data.get('最高', today_data.get('high', 0)))
            today_volume = int(today_data.get('成交量', today_data.get('volume', 0)))

            alert_conditions = []

            # 条件1：量比>2.0
            alert_conditions.append("量比>2.0")

            # 条件2：突破今日高点+1%
            if today_high > 0:
                breakthrough_price = today_high * 1.01
                alert_conditions.append(f"价格>{breakthrough_price:.2f}")

            # 条件3：如果评分很高，添加更严格的条件
            if total_score >= 70:
                alert_conditions.append("买一量>卖一量3倍")

            return " 且 ".join(alert_conditions)

        except Exception as e:
            return "预警设置失败"

    def show_settings(self):
        """显示设置"""
        print("⚙️ 设置功能待实现")
        messagebox.showinfo("提示", "设置功能待实现")

    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'config': self.config,
                'monitored_stocks': {},
                'save_time': datetime.now().isoformat()
            }

            # 保存监控股票信息（不包含实时数据）
            for code, stock_info in self.monitored_stocks.items():
                config_data['monitored_stocks'][code] = {
                    'name': stock_info['name'],
                    'initialized': stock_info.get('initialized', False),
                    'added_time': stock_info.get('added_time', datetime.now().isoformat())
                }

            # 保存到配置文件
            config_file = Path("config_screening.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 配置已保存，监控股票: {len(self.monitored_stocks)} 只")

        except Exception as e:
            print(f"⚠️ 配置保存失败: {e}")

    def load_config(self):
        """加载配置"""
        try:
            config_file = Path("config_screening.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)

                    # 加载配置参数
                    self.config.update(saved_config.get('config', {}))

                    # 加载监控股票
                    saved_stocks = saved_config.get('monitored_stocks', {})

                    print(f"📁 发现配置文件，包含 {len(saved_stocks)} 只股票")

                    for code, stock_info in saved_stocks.items():
                        try:
                            # 重新获取股票名称（如果配置中的名称是"未知"或以"股票"开头）
                            saved_name = stock_info.get('name', '未知')
                            if saved_name == '未知' or saved_name.startswith('股票'):
                                print(f"   🔄 重新获取股票名称: {code}")
                                try:
                                    new_stock_info = enhanced_stock_fetcher.get_stock_basic_info(code)
                                    stock_name = new_stock_info.get('name', saved_name)
                                except:
                                    stock_name = saved_name
                            else:
                                stock_name = saved_name

                            # 恢复股票到监控列表
                            self.monitored_stocks[code] = {
                                'name': stock_name,
                                'data': {},
                                'signals': {},
                                'decision': {},
                                'last_update': None,
                                'initialized': False,  # 重新启动后需要重新初始化
                                'added_time': stock_info.get('added_time', datetime.now().isoformat()),
                                'alert_history': []
                            }

                            # 添加到表格显示
                            self.tree.insert('', 'end', iid=code, values=(
                                code, stock_name, '❌', '❌', '❌', '❌',
                                '0', '需重新初始化', '-', '已加载'
                            ))

                            print(f"   📊 加载股票: {code} {stock_name}")

                        except Exception as e:
                            print(f"   ⚠️ 加载股票 {code} 失败: {e}")
                            continue

                print(f"✅ 配置加载成功，监控股票: {len(self.monitored_stocks)} 只")

                # 显示加载提示
                if self.monitored_stocks:
                    self.info_label.config(text=f"💡 已加载 {len(self.monitored_stocks)} 只股票，请重新初始化历史数据")

            else:
                print("📝 配置文件不存在，使用默认配置")

        except Exception as e:
            print(f"⚠️ 配置加载失败: {e}")

    def reinitialize_all_stocks(self):
        """重新初始化所有股票的历史数据"""
        try:
            if not self.monitored_stocks:
                messagebox.showinfo("提示", "没有需要初始化的股票")
                return

            print(f"🔄 开始重新初始化 {len(self.monitored_stocks)} 只股票的历史数据...")

            success_count = 0
            for code in list(self.monitored_stocks.keys()):
                try:
                    stock_name = self.monitored_stocks[code]['name']
                    print(f"🔄 重新初始化 {code} {stock_name}...")

                    if self.initialize_stock_monitoring(code, stock_name):
                        self.monitored_stocks[code]['initialized'] = True
                        success_count += 1

                        # 更新表格显示
                        if self.tree.exists(code):
                            current_values = list(self.tree.item(code, 'values'))
                            current_values[8] = '已初始化'  # 更新最新价格列显示状态
                            self.tree.item(code, values=current_values)
                    else:
                        print(f"❌ {code} 初始化失败")

                except Exception as e:
                    print(f"❌ {code} 初始化异常: {e}")
                    continue

            # 保存配置
            self.save_config()

            print(f"✅ 重新初始化完成：成功 {success_count}/{len(self.monitored_stocks)} 只股票")
            messagebox.showinfo("完成", f"重新初始化完成：\n成功 {success_count} 只股票\n失败 {len(self.monitored_stocks) - success_count} 只股票")

            # 更新状态提示
            self.info_label.config(text=f"💡 {success_count} 只股票已就绪，可以开始监控或分析")

        except Exception as e:
            error_msg = f"重新初始化失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)


def main():
    """主程序入口"""
    try:
        print("🚀 启动潜在股进一步筛选器...")
        app = StockFurtherScreening()

        print("🚀 潜在股进一步筛选器启动成功")
        print("🎯 主力吸筹尾声精准买入系统")
        print("="*50)

        app.root.mainloop()

    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
