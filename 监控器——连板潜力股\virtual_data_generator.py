#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟数据生成器
用于生成各种股票走势场景的测试数据
"""

import random
import math
from datetime import datetime, timedelta
from typing import List, Dict, Any

class VirtualDataGenerator:
    """虚拟股票数据生成器"""
    
    def __init__(self):
        self.base_price = 10.0  # 基础价格
        self.prev_close = 10.0  # 昨日收盘价
        self.circulating_shares = 50000  # 流通股本（万股）
        
    def generate_scenario_data(self, scenario_name: str, duration_minutes: int = 120) -> List[Dict[str, Any]]:
        """
        生成特定场景的股票数据
        
        Args:
            scenario_name: 场景名称
            duration_minutes: 持续时间（分钟）
            
        Returns:
            List[Dict]: 股票数据序列
        """
        if scenario_name == "平开上涨":
            return self._generate_flat_open_rise(duration_minutes)
        elif scenario_name == "高开回落反转":
            return self._generate_high_open_correction(duration_minutes)
        elif scenario_name == "低开反转":
            return self._generate_low_open_reversal(duration_minutes)
        elif scenario_name == "中位蓄势突破":
            return self._generate_mid_level_accumulation(duration_minutes)
        elif scenario_name == "高位横盘突破":
            return self._generate_high_platform_breakout(duration_minutes)
        elif scenario_name == "隔夜挂单强势":
            return self._generate_overnight_strong_orders(duration_minutes)
        elif scenario_name == "隔夜挂单破板":
            return self._generate_overnight_weak_orders(duration_minutes)
        elif scenario_name == "V型反转":
            return self._generate_v_shape_reversal(duration_minutes)
        else:
            return self._generate_random_walk(duration_minutes)
    
    def _generate_flat_open_rise(self, duration: int) -> List[Dict[str, Any]]:
        """生成平开上涨场景"""
        data_points = []
        current_price = self.prev_close * (1 + random.uniform(-0.005, 0.005))  # 平开±0.5%
        
        for i in range(duration):
            # 模拟平开后逐步上涨
            if i < 30:  # 前30分钟缓慢上涨
                price_change = random.uniform(0.001, 0.003)
            elif i < 60:  # 30-60分钟加速上涨
                price_change = random.uniform(0.002, 0.005)
            else:  # 后续稳步上涨
                price_change = random.uniform(0.001, 0.004)
            
            current_price *= (1 + price_change)
            
            # 生成对应的成交量和盘口数据
            volume_ratio = self._calculate_volume_ratio(i, "上涨")
            bid_volumes, ask_volumes = self._generate_order_book(current_price, "买盘强势")
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_high_open_correction(self, duration: int) -> List[Dict[str, Any]]:
        """生成高开回落反转场景"""
        data_points = []
        current_price = self.prev_close * (1 + random.uniform(0.03, 0.06))  # 高开3-6%
        
        for i in range(duration):
            if i < 20:  # 前20分钟回落
                price_change = random.uniform(-0.004, -0.001)
            elif i < 40:  # 20-40分钟企稳
                price_change = random.uniform(-0.001, 0.001)
            else:  # 40分钟后反转上涨
                price_change = random.uniform(0.002, 0.006)
            
            current_price *= (1 + price_change)
            
            # 根据阶段调整盘口特征
            if i < 20:
                order_type = "卖压较重"
                volume_type = "回落"
            elif i < 40:
                order_type = "平衡"
                volume_type = "缩量"
            else:
                order_type = "买盘强势"
                volume_type = "放量"
            
            volume_ratio = self._calculate_volume_ratio(i, volume_type)
            bid_volumes, ask_volumes = self._generate_order_book(current_price, order_type)
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_low_open_reversal(self, duration: int) -> List[Dict[str, Any]]:
        """生成低开反转场景"""
        data_points = []
        current_price = self.prev_close * (1 + random.uniform(-0.04, -0.01))  # 低开1-4%
        
        for i in range(duration):
            if i < 15:  # 前15分钟继续下跌
                price_change = random.uniform(-0.003, -0.001)
            elif i < 25:  # 15-25分钟探底
                price_change = random.uniform(-0.002, 0.001)
            else:  # 25分钟后V型反转
                price_change = random.uniform(0.003, 0.008)
            
            current_price *= (1 + price_change)
            
            volume_ratio = self._calculate_volume_ratio(i, "反转" if i >= 25 else "下跌")
            order_type = "买盘强势" if i >= 25 else "卖压较重"
            bid_volumes, ask_volumes = self._generate_order_book(current_price, order_type)
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_mid_level_accumulation(self, duration: int) -> List[Dict[str, Any]]:
        """生成中位蓄势突破场景"""
        data_points = []
        current_price = self.prev_close * (1 + random.uniform(0.02, 0.04))  # 开盘涨2-4%
        
        for i in range(duration):
            if i < 45:  # 前45分钟横盘蓄势
                price_change = random.uniform(-0.002, 0.002)  # 小幅波动
            else:  # 45分钟后突破
                price_change = random.uniform(0.003, 0.007)
            
            current_price *= (1 + price_change)
            
            if i < 45:
                volume_type = "缩量"
                order_type = "平衡"
            else:
                volume_type = "放量"
                order_type = "买盘强势"
            
            volume_ratio = self._calculate_volume_ratio(i, volume_type)
            bid_volumes, ask_volumes = self._generate_order_book(current_price, order_type)
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_high_platform_breakout(self, duration: int) -> List[Dict[str, Any]]:
        """生成高位横盘突破场景"""
        data_points = []
        current_price = self.prev_close * (1 + random.uniform(0.06, 0.08))  # 开盘涨6-8%
        
        for i in range(duration):
            if i < 60:  # 前60分钟高位横盘
                price_change = random.uniform(-0.001, 0.001)
            else:  # 60分钟后突破
                price_change = random.uniform(0.002, 0.005)
            
            current_price *= (1 + price_change)
            
            volume_ratio = self._calculate_volume_ratio(i, "突破" if i >= 60 else "横盘")
            order_type = "买盘强势" if i >= 60 else "平衡"
            bid_volumes, ask_volumes = self._generate_order_book(current_price, order_type)
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_overnight_strong_orders(self, duration: int) -> List[Dict[str, Any]]:
        """生成隔夜挂单强势场景（模拟9:20数据）"""
        data_points = []
        current_price = self.prev_close * 1.099  # 涨停价
        
        # 生成强势隔夜挂单特征
        for i in range(min(duration, 10)):  # 只生成10分钟数据模拟集合竞价
            volume_ratio = random.uniform(2.0, 5.0)  # 高量比
            
            # 强势买盘特征：泰山压顶式
            bid_volumes = [
                random.randint(50000, 100000),  # 买一：巨量封单
                random.randint(20000, 40000),   # 买二
                random.randint(15000, 30000),   # 买三
                random.randint(10000, 25000),   # 买四
                random.randint(8000, 20000)     # 买五
            ]
            
            # 卖盘真空
            ask_volumes = [0, 0, 0, 0, 0]  # 完全真空
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_overnight_weak_orders(self, duration: int) -> List[Dict[str, Any]]:
        """生成隔夜挂单破板风险场景"""
        data_points = []
        current_price = self.prev_close * 1.095  # 接近涨停但未封死
        
        for i in range(min(duration, 10)):
            volume_ratio = random.uniform(0.5, 1.2)  # 低量比
            
            # 破板风险特征：纸老虎式
            bid_volumes = [
                random.randint(1000, 5000),    # 买一：封单很薄
                random.randint(500, 2000),     # 买二
                random.randint(300, 1500),     # 买三
                random.randint(200, 1000),     # 买四
                random.randint(100, 800)       # 买五
            ]
            
            # 卖盘突现
            ask_volumes = [
                random.randint(5000, 15000),   # 卖一：突现大单
                random.randint(3000, 8000),    # 卖二
                random.randint(2000, 5000),    # 卖三
                random.randint(1000, 3000),    # 卖四
                random.randint(500, 2000)      # 卖五
            ]
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_v_shape_reversal(self, duration: int) -> List[Dict[str, Any]]:
        """生成V型反转场景"""
        data_points = []
        current_price = self.prev_close * (1 + random.uniform(0.02, 0.04))  # 开盘小涨
        
        for i in range(duration):
            if i < 20:  # 前20分钟深度回落
                price_change = random.uniform(-0.006, -0.003)
            elif i < 30:  # 20-30分钟探底
                price_change = random.uniform(-0.002, 0.001)
            else:  # 30分钟后V型反转
                price_change = random.uniform(0.004, 0.009)
            
            current_price *= (1 + price_change)
            
            volume_ratio = self._calculate_volume_ratio(i, "V型反转" if i >= 30 else "下跌")
            order_type = "买盘强势" if i >= 30 else "卖压较重"
            bid_volumes, ask_volumes = self._generate_order_book(current_price, order_type)
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points
    
    def _generate_random_walk(self, duration: int) -> List[Dict[str, Any]]:
        """生成随机游走数据"""
        data_points = []
        current_price = self.prev_close
        
        for i in range(duration):
            price_change = random.uniform(-0.003, 0.003)
            current_price *= (1 + price_change)
            
            volume_ratio = random.uniform(0.5, 2.0)
            bid_volumes, ask_volumes = self._generate_order_book(current_price, "平衡")
            
            data_point = self._create_data_point(
                current_price, volume_ratio, bid_volumes, ask_volumes, i
            )
            data_points.append(data_point)
        
        return data_points

    def _calculate_volume_ratio(self, time_index: int, volume_type: str) -> float:
        """计算量比"""
        base_ratio = 1.0

        if volume_type == "上涨":
            base_ratio = random.uniform(1.2, 2.5)
        elif volume_type == "下跌":
            base_ratio = random.uniform(0.8, 1.8)
        elif volume_type == "反转":
            base_ratio = random.uniform(2.0, 4.0)
        elif volume_type == "放量":
            base_ratio = random.uniform(1.5, 3.0)
        elif volume_type == "缩量":
            base_ratio = random.uniform(0.3, 0.8)
        elif volume_type == "横盘":
            base_ratio = random.uniform(0.6, 1.2)
        elif volume_type == "突破":
            base_ratio = random.uniform(2.5, 5.0)
        elif volume_type == "V型反转":
            base_ratio = random.uniform(3.0, 6.0)
        elif volume_type == "回落":
            base_ratio = random.uniform(1.0, 2.0)

        # 添加随机波动
        return base_ratio * random.uniform(0.8, 1.2)

    def _generate_order_book(self, current_price: float, order_type: str) -> tuple:
        """生成买卖五档数据"""
        if order_type == "买盘强势":
            bid_volumes = [
                random.randint(5000, 20000),
                random.randint(3000, 15000),
                random.randint(2000, 10000),
                random.randint(1000, 8000),
                random.randint(500, 5000)
            ]
            ask_volumes = [
                random.randint(100, 2000),
                random.randint(200, 3000),
                random.randint(300, 4000),
                random.randint(400, 5000),
                random.randint(500, 6000)
            ]
        elif order_type == "卖压较重":
            bid_volumes = [
                random.randint(500, 3000),
                random.randint(400, 2500),
                random.randint(300, 2000),
                random.randint(200, 1500),
                random.randint(100, 1000)
            ]
            ask_volumes = [
                random.randint(5000, 20000),
                random.randint(4000, 15000),
                random.randint(3000, 12000),
                random.randint(2000, 10000),
                random.randint(1000, 8000)
            ]
        else:  # 平衡
            bid_volumes = [
                random.randint(2000, 8000),
                random.randint(1500, 6000),
                random.randint(1000, 5000),
                random.randint(800, 4000),
                random.randint(500, 3000)
            ]
            ask_volumes = [
                random.randint(2000, 8000),
                random.randint(1500, 6000),
                random.randint(1000, 5000),
                random.randint(800, 4000),
                random.randint(500, 3000)
            ]

        return bid_volumes, ask_volumes

    def _create_data_point(self, current_price: float, volume_ratio: float,
                          bid_volumes: List[int], ask_volumes: List[int],
                          time_index: int) -> Dict[str, Any]:
        """创建单个数据点"""
        # 计算涨跌幅
        change_pct = (current_price - self.prev_close) / self.prev_close * 100

        # 生成时间戳（从9:30开始）
        start_time = datetime.now().replace(hour=9, minute=30, second=0, microsecond=0)
        current_time = start_time + timedelta(minutes=time_index)

        # 生成成交量
        volume = int(volume_ratio * random.randint(100000, 500000))

        return {
            'current_price': round(current_price, 2),
            'prev_close': self.prev_close,
            'change_pct': round(change_pct, 2),
            'volume_ratio': round(volume_ratio, 2),
            'volume': volume,
            'bid_volumes': bid_volumes,
            'ask_volumes': ask_volumes,
            'timestamp': current_time.strftime('%H:%M:%S'),
            'open': self.prev_close * (1 + random.uniform(-0.01, 0.01)) if time_index == 0 else None
        }

    def get_test_stock_info(self) -> Dict[str, str]:
        """获取测试股票信息"""
        return {
            'code': 'TEST001',
            'name': '测试股票',
            'prev_close': self.prev_close
        }
