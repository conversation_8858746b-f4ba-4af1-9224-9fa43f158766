#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时分时数据测试文件
测试从主应用中提取的分时数据获取方法
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta
import time

# 添加上级目录到路径，以便导入主应用的模块
sys.path.append('..')

def test_minute_data_fetching():
    """测试分时数据获取功能"""
    print("🚀 开始测试分时数据获取功能...")
    print("=" * 60)
    
    # 测试股票代码（选择一些活跃股票）
    test_codes = ['000001', '000002', '600036', '002415']  # 平安银行、万科A、招商银行、海康威视
    
    try:
        # 导入增强数据获取器
        from enhanced_data_fetcher import enhanced_stock_fetcher
        print("✅ 成功导入 enhanced_stock_fetcher")
        
        for i, code in enumerate(test_codes, 1):
            print(f"\n📊 测试 {i}/{len(test_codes)}: {code}")
            print("-" * 40)
            
            # 测试1: 获取当日/最近1天分时数据
            print("🔍 测试1: 获取历史分时数据（当日或最近交易日）")
            try:
                minute_result = enhanced_stock_fetcher.get_historical_minute_data(code, period='1')
                
                if minute_result.get('data_available', False):
                    minute_data = minute_result['minute_data']
                    print(f"  ✅ 成功获取分时数据: {len(minute_data)} 条记录")
                    print(f"  📅 数据摘要: {minute_result.get('data_summary', '无摘要')}")
                    
                    # 显示数据样本
                    if not minute_data.empty:
                        print(f"  📈 数据列: {list(minute_data.columns)}")
                        print(f"  ⏰ 时间范围: {minute_data.index[0]} 到 {minute_data.index[-1]}")
                        print(f"  💰 价格范围: {minute_data['收盘'].min():.2f} - {minute_data['收盘'].max():.2f}")
                        print(f"  📊 成交量总计: {minute_data['成交量'].sum():,.0f}")
                        
                        # 显示前5条数据
                        print("  📋 前5条数据:")
                        print(minute_data.head().to_string())
                else:
                    print(f"  ❌ 获取分时数据失败: {minute_result.get('data_summary', '未知错误')}")
                    
            except Exception as e:
                print(f"  ❌ 测试1异常: {e}")
            
            # 测试2: 获取最近7天分时数据
            print(f"\n🔍 测试2: 获取最近7天分时数据")
            try:
                result_7days = enhanced_stock_fetcher.get_recent_7days_minute_data(code, period='1')
                
                if result_7days.get('data_available', False):
                    trading_days = result_7days.get('total_trading_days', 0)
                    date_range = result_7days.get('date_range', '未知')
                    print(f"  ✅ 成功获取7天数据: {trading_days} 个交易日")
                    print(f"  📅 日期范围: {date_range}")
                    print(f"  📊 数据摘要: {result_7days.get('summary', '无摘要')}")
                    
                    # 显示每个交易日的数据概况
                    trading_days_data = result_7days.get('trading_days_data', [])
                    for day_idx, day_data in enumerate(trading_days_data):
                        day_minute_data = day_data['minute_data']
                        print(f"    📅 第{day_idx+1}天 {day_data['date_formatted']} ({day_data['weekday']}): {len(day_minute_data)} 条分时数据")
                        
                        if not day_minute_data.empty:
                            day_volume = day_minute_data['成交量'].sum()
                            day_price_range = f"{day_minute_data['收盘'].min():.2f}-{day_minute_data['收盘'].max():.2f}"
                            print(f"      💰 价格区间: {day_price_range}, 成交量: {day_volume:,.0f}")
                else:
                    print(f"  ❌ 获取7天数据失败: {result_7days.get('summary', '未知错误')}")
                    
            except Exception as e:
                print(f"  ❌ 测试2异常: {e}")
            
            # 测试3: 获取5分钟K线数据（作为对比）
            print(f"\n🔍 测试3: 获取5分钟K线数据")
            try:
                minute_kline = enhanced_stock_fetcher.get_minute_kline(code, frequency='5m', count=48)
                
                if not minute_kline.empty:
                    print(f"  ✅ 成功获取5分钟K线: {len(minute_kline)} 条记录")
                    print(f"  📈 数据列: {list(minute_kline.columns)}")
                    
                    if 'trade_time' in minute_kline.columns:
                        print(f"  ⏰ 时间范围: {minute_kline['trade_time'].iloc[0]} 到 {minute_kline['trade_time'].iloc[-1]}")
                    
                    # 显示前3条数据
                    print("  📋 前3条数据:")
                    print(minute_kline.head(3).to_string())
                else:
                    print(f"  ❌ 获取5分钟K线失败")
                    
            except Exception as e:
                print(f"  ❌ 测试3异常: {e}")
            
            # 为了避免API限制，每个股票测试后暂停
            if i < len(test_codes):
                print(f"\n⏱️ 暂停10秒避免API限制...")
                time.sleep(10)
    
    except ImportError as e:
        print(f"❌ 导入失败: {e}")
        print("请确保在正确的目录下运行此脚本，并且主应用的模块可以正常导入")
        return False
    
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    print(f"\n🎉 分时数据获取功能测试完成!")
    return True

def test_specific_stock_minute_data(stock_code='000001'):
    """测试特定股票的详细分时数据"""
    print(f"\n🎯 详细测试股票 {stock_code} 的分时数据获取...")
    print("=" * 60)
    
    try:
        from enhanced_data_fetcher import enhanced_stock_fetcher
        
        # 测试获取指定日期的分时数据
        target_date = datetime.now().strftime('%Y%m%d')
        print(f"📅 目标日期: {target_date}")
        
        result = enhanced_stock_fetcher.get_historical_minute_data(stock_code, target_date, period='1')
        
        if result.get('data_available', False):
            minute_data = result['minute_data']
            analysis = result.get('analysis', {})
            
            print(f"✅ 成功获取 {stock_code} 的分时数据")
            print(f"📊 数据量: {len(minute_data)} 条记录")
            print(f"📈 分析摘要: {analysis.get('summary', '无摘要')}")
            
            # 详细分析数据结构
            if not minute_data.empty:
                print(f"\n📋 数据结构分析:")
                print(f"  列名: {list(minute_data.columns)}")
                print(f"  数据类型:")
                for col in minute_data.columns:
                    print(f"    {col}: {minute_data[col].dtype}")
                
                print(f"\n📊 数据统计:")
                print(f"  时间范围: {minute_data.index[0]} 到 {minute_data.index[-1]}")
                print(f"  开盘价: {minute_data['开盘'].iloc[0]:.2f}")
                print(f"  收盘价: {minute_data['收盘'].iloc[-1]:.2f}")
                print(f"  最高价: {minute_data['最高'].max():.2f}")
                print(f"  最低价: {minute_data['最低'].min():.2f}")
                print(f"  总成交量: {minute_data['成交量'].sum():,.0f}")
                print(f"  总成交额: {minute_data['成交额'].sum():,.0f}")
                
                # 分析关键时间点（如果有尾盘数据）
                print(f"\n⏰ 关键时间点分析:")
                
                # 尾盘30分钟数据（14:30-15:00）
                try:
                    minute_data_copy = minute_data.copy()
                    minute_data_copy['时间'] = pd.to_datetime(minute_data_copy.index)
                    minute_data_copy['时分'] = minute_data_copy['时间'].dt.time
                    
                    # 筛选尾盘30分钟
                    tail_data = minute_data_copy[
                        (minute_data_copy['时分'] >= pd.to_datetime('14:30').time()) &
                        (minute_data_copy['时分'] <= pd.to_datetime('15:00').time())
                    ]
                    
                    if not tail_data.empty:
                        tail_volume = tail_data['成交量'].sum()
                        total_volume = minute_data['成交量'].sum()
                        tail_ratio = tail_volume / total_volume if total_volume > 0 else 0
                        
                        print(f"  📊 尾盘30分钟成交量: {tail_volume:,.0f}")
                        print(f"  📊 尾盘量能占比: {tail_ratio:.1%}")
                        print(f"  📊 尾盘是否放量: {'是' if tail_ratio > 0.35 else '否'}")
                    else:
                        print(f"  ⚠️ 无尾盘数据（可能非交易时间）")
                        
                except Exception as e:
                    print(f"  ❌ 尾盘分析失败: {e}")
                
                # 显示最后10条数据
                print(f"\n📋 最后10条分时数据:")
                print(minute_data.tail(10).to_string())
                
        else:
            print(f"❌ 获取 {stock_code} 分时数据失败")
            print(f"错误信息: {result.get('data_summary', '未知错误')}")
    
    except Exception as e:
        print(f"❌ 详细测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🧪 分时数据获取功能测试程序")
    print("=" * 60)
    
    # 基础功能测试
    success = test_minute_data_fetching()
    
    if success:
        # 详细测试
        test_specific_stock_minute_data('000001')  # 平安银行
    
    print(f"\n✅ 测试程序执行完成!")
