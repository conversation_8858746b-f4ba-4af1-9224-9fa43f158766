#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块轮动监控器
建立板块轮动监控系统，识别热点板块、资金流向和板块联动效应
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import akshare as ak
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SectorRotationMonitor:
    """板块轮动监控器"""
    
    def __init__(self):
        self.akshare_available = self._check_akshare_availability()
        self.data_cache = {}  # 数据缓存
        self.cache_timeout = 1800  # 缓存30分钟
        
        # 板块分类配置
        self.sector_categories = {
            'concept_sectors': {
                'name': '概念板块',
                'hot_concepts': ['人工智能', '新能源', '芯片', '医药', '军工', '5G', '新基建', '碳中和'],
                'weight': 0.4
            },
            'industry_sectors': {
                'name': '行业板块', 
                'main_industries': ['电子', '医药生物', '计算机', '电力设备', '机械设备', '化工', '汽车'],
                'weight': 0.35
            },
            'regional_sectors': {
                'name': '地域板块',
                'key_regions': ['深圳', '上海', '北京', '雄安', '海南', '粤港澳'],
                'weight': 0.25
            }
        }
        
        # 轮动分析参数
        self.rotation_params = {
            'analysis_days': 20,        # 分析天数
            'hot_threshold': 0.03,      # 热点阈值（涨幅3%以上）
            'volume_threshold': 1.5,    # 成交量放大阈值
            'correlation_threshold': 0.6, # 联动相关性阈值
            'momentum_period': 5        # 动量分析周期
        }
        
        logger.info("📊 板块轮动监控器初始化完成")
    
    def _check_akshare_availability(self) -> bool:
        """检查AKShare可用性"""
        try:
            import akshare as ak
            # 简单测试
            ak.stock_board_concept_name_em()
            return True
        except Exception as e:
            logger.warning(f"AKShare不可用: {e}")
            return False
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (datetime.now().timestamp() - cache_time) < self.cache_timeout
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now().timestamp()
        }
    
    def _get_cache(self, cache_key: str) -> Any:
        """获取缓存数据"""
        return self.data_cache[cache_key]['data']
    
    def get_concept_sectors_data(self) -> Dict[str, Any]:
        """获取概念板块数据"""
        cache_key = 'concept_sectors'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_sector_data('concept')
            
            # 获取概念板块列表
            concept_list = ak.stock_board_concept_name_em()
            
            if concept_list.empty:
                return self._get_empty_sector_data('concept')
            
            # 获取概念板块行情数据
            concept_data = ak.stock_board_concept_cons_em()
            
            # 处理数据
            processed_data = self._process_concept_data(concept_list, concept_data)
            
            result = {
                'sector_type': 'concept',
                'total_sectors': len(concept_list),
                'hot_sectors': processed_data['hot_sectors'],
                'sector_rankings': processed_data['rankings'],
                'capital_flow': processed_data['capital_flow'],
                'rotation_signals': processed_data['rotation_signals'],
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 概念板块数据获取成功: {result['total_sectors']}个板块")
            return result
            
        except Exception as e:
            logger.error(f"获取概念板块数据失败: {e}")
            return self._get_empty_sector_data('concept')
    
    def get_industry_sectors_data(self) -> Dict[str, Any]:
        """获取行业板块数据"""
        cache_key = 'industry_sectors'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_sector_data('industry')
            
            # 获取行业板块数据
            industry_data = ak.stock_board_industry_name_em()
            
            if industry_data.empty:
                return self._get_empty_sector_data('industry')
            
            # 获取行业板块详细行情
            industry_cons = ak.stock_board_industry_cons_em()
            
            # 处理数据
            processed_data = self._process_industry_data(industry_data, industry_cons)
            
            result = {
                'sector_type': 'industry',
                'total_sectors': len(industry_data),
                'hot_sectors': processed_data['hot_sectors'],
                'sector_rankings': processed_data['rankings'],
                'capital_flow': processed_data['capital_flow'],
                'rotation_signals': processed_data['rotation_signals'],
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 行业板块数据获取成功: {result['total_sectors']}个板块")
            return result
            
        except Exception as e:
            logger.error(f"获取行业板块数据失败: {e}")
            return self._get_empty_sector_data('industry')
    
    def _process_concept_data(self, concept_list: pd.DataFrame, concept_data: pd.DataFrame) -> Dict[str, Any]:
        """处理概念板块数据"""
        try:
            hot_sectors = []
            rankings = []
            capital_flow = {}
            rotation_signals = []
            
            # 分析热点概念
            if not concept_data.empty and '涨跌幅' in concept_data.columns:
                # 按涨跌幅排序
                sorted_concepts = concept_data.sort_values('涨跌幅', ascending=False)
                
                # 识别热点板块（涨幅超过阈值）
                hot_threshold = self.rotation_params['hot_threshold'] * 100  # 转换为百分比
                hot_concepts = sorted_concepts[sorted_concepts['涨跌幅'] > hot_threshold]
                
                for _, row in hot_concepts.head(10).iterrows():
                    hot_sectors.append({
                        'name': row.get('板块名称', ''),
                        'change_pct': float(row.get('涨跌幅', 0)),
                        'volume_ratio': float(row.get('换手率', 0)),
                        'leading_stocks': self._get_leading_stocks(row.get('板块名称', '')),
                        'heat_score': self._calculate_heat_score(row)
                    })
                
                # 生成排名
                for i, (_, row) in enumerate(sorted_concepts.head(20).iterrows()):
                    rankings.append({
                        'rank': i + 1,
                        'name': row.get('板块名称', ''),
                        'change_pct': float(row.get('涨跌幅', 0)),
                        'volume': float(row.get('成交额', 0)),
                        'stock_count': int(row.get('公司家数', 0))
                    })
                
                # 分析资金流向
                capital_flow = self._analyze_capital_flow(sorted_concepts)
                
                # 生成轮动信号
                rotation_signals = self._generate_rotation_signals(sorted_concepts)
            
            return {
                'hot_sectors': hot_sectors,
                'rankings': rankings,
                'capital_flow': capital_flow,
                'rotation_signals': rotation_signals
            }
            
        except Exception as e:
            logger.error(f"处理概念板块数据失败: {e}")
            return {
                'hot_sectors': [],
                'rankings': [],
                'capital_flow': {},
                'rotation_signals': []
            }
    
    def _process_industry_data(self, industry_data: pd.DataFrame, industry_cons: pd.DataFrame) -> Dict[str, Any]:
        """处理行业板块数据"""
        try:
            # 类似概念板块的处理逻辑
            hot_sectors = []
            rankings = []
            capital_flow = {}
            rotation_signals = []
            
            if not industry_data.empty and '涨跌幅' in industry_data.columns:
                sorted_industries = industry_data.sort_values('涨跌幅', ascending=False)
                
                hot_threshold = self.rotation_params['hot_threshold'] * 100
                hot_industries = sorted_industries[sorted_industries['涨跌幅'] > hot_threshold]
                
                for _, row in hot_industries.head(10).iterrows():
                    hot_sectors.append({
                        'name': row.get('板块名称', ''),
                        'change_pct': float(row.get('涨跌幅', 0)),
                        'volume_ratio': float(row.get('换手率', 0)),
                        'leading_stocks': self._get_leading_stocks(row.get('板块名称', '')),
                        'heat_score': self._calculate_heat_score(row)
                    })
                
                for i, (_, row) in enumerate(sorted_industries.head(20).iterrows()):
                    rankings.append({
                        'rank': i + 1,
                        'name': row.get('板块名称', ''),
                        'change_pct': float(row.get('涨跌幅', 0)),
                        'volume': float(row.get('成交额', 0)),
                        'stock_count': int(row.get('公司家数', 0))
                    })
                
                capital_flow = self._analyze_capital_flow(sorted_industries)
                rotation_signals = self._generate_rotation_signals(sorted_industries)
            
            return {
                'hot_sectors': hot_sectors,
                'rankings': rankings,
                'capital_flow': capital_flow,
                'rotation_signals': rotation_signals
            }
            
        except Exception as e:
            logger.error(f"处理行业板块数据失败: {e}")
            return {
                'hot_sectors': [],
                'rankings': [],
                'capital_flow': {},
                'rotation_signals': []
            }
    
    def _get_leading_stocks(self, sector_name: str) -> List[Dict[str, Any]]:
        """获取板块龙头股票"""
        try:
            # 简化实现，返回空列表
            # 实际应用中可以通过AKShare获取板块成分股并分析
            return []
        except Exception as e:
            logger.error(f"获取{sector_name}龙头股失败: {e}")
            return []
    
    def _calculate_heat_score(self, sector_row: pd.Series) -> float:
        """计算板块热度评分"""
        try:
            change_pct = float(sector_row.get('涨跌幅', 0))
            volume = float(sector_row.get('成交额', 0))
            turnover = float(sector_row.get('换手率', 0))
            
            # 综合评分算法
            heat_score = (
                change_pct * 0.4 +           # 涨跌幅权重40%
                min(turnover / 5.0, 10) * 0.3 +  # 换手率权重30%
                min(volume / 1e10, 10) * 0.3      # 成交额权重30%
            )
            
            return max(0, min(heat_score, 100))  # 限制在0-100范围
            
        except Exception as e:
            logger.error(f"计算热度评分失败: {e}")
            return 0.0

    def _analyze_capital_flow(self, sector_data: pd.DataFrame) -> Dict[str, Any]:
        """分析板块资金流向"""
        try:
            if sector_data.empty:
                return {'total_inflow': 0, 'total_outflow': 0, 'net_flow': 0, 'flow_direction': 'neutral'}

            # 计算资金流向（简化算法）
            total_volume = sector_data['成交额'].sum() if '成交额' in sector_data.columns else 0
            positive_sectors = sector_data[sector_data['涨跌幅'] > 0] if '涨跌幅' in sector_data.columns else pd.DataFrame()
            negative_sectors = sector_data[sector_data['涨跌幅'] < 0] if '涨跌幅' in sector_data.columns else pd.DataFrame()

            inflow_volume = positive_sectors['成交额'].sum() if not positive_sectors.empty and '成交额' in positive_sectors.columns else 0
            outflow_volume = negative_sectors['成交额'].sum() if not negative_sectors.empty and '成交额' in negative_sectors.columns else 0

            net_flow = inflow_volume - outflow_volume

            # 确定流向
            if net_flow > total_volume * 0.1:
                flow_direction = 'inflow'
            elif net_flow < -total_volume * 0.1:
                flow_direction = 'outflow'
            else:
                flow_direction = 'neutral'

            return {
                'total_inflow': float(inflow_volume),
                'total_outflow': float(outflow_volume),
                'net_flow': float(net_flow),
                'flow_direction': flow_direction,
                'flow_strength': abs(net_flow) / total_volume if total_volume > 0 else 0
            }

        except Exception as e:
            logger.error(f"资金流向分析失败: {e}")
            return {'total_inflow': 0, 'total_outflow': 0, 'net_flow': 0, 'flow_direction': 'neutral'}

    def _generate_rotation_signals(self, sector_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """生成板块轮动信号"""
        try:
            signals = []

            if sector_data.empty or '涨跌幅' not in sector_data.columns:
                return signals

            # 强势板块信号
            strong_sectors = sector_data[sector_data['涨跌幅'] > 3.0].head(5)
            for _, sector in strong_sectors.iterrows():
                signals.append({
                    'signal_type': 'strong_momentum',
                    'sector_name': sector.get('板块名称', ''),
                    'strength': 'high',
                    'change_pct': float(sector.get('涨跌幅', 0)),
                    'description': f"{sector.get('板块名称', '')}板块强势上涨，关注轮动机会",
                    'confidence': min(sector.get('涨跌幅', 0) / 5.0, 1.0)
                })

            # 资金流入信号
            high_volume_sectors = sector_data.nlargest(5, '成交额') if '成交额' in sector_data.columns else pd.DataFrame()
            for _, sector in high_volume_sectors.iterrows():
                if sector.get('涨跌幅', 0) > 1.0:  # 上涨且成交量大
                    signals.append({
                        'signal_type': 'capital_inflow',
                        'sector_name': sector.get('板块名称', ''),
                        'strength': 'medium',
                        'volume': float(sector.get('成交额', 0)),
                        'description': f"{sector.get('板块名称', '')}板块资金流入明显",
                        'confidence': 0.7
                    })

            # 轮动切换信号
            rotation_candidates = self._identify_rotation_candidates(sector_data)
            for candidate in rotation_candidates:
                signals.append({
                    'signal_type': 'rotation_switch',
                    'sector_name': candidate['name'],
                    'strength': candidate['strength'],
                    'description': candidate['description'],
                    'confidence': candidate['confidence']
                })

            return signals

        except Exception as e:
            logger.error(f"生成轮动信号失败: {e}")
            return []

    def _identify_rotation_candidates(self, sector_data: pd.DataFrame) -> List[Dict[str, Any]]:
        """识别轮动候选板块"""
        try:
            candidates = []

            if sector_data.empty:
                return candidates

            # 寻找相对强势但尚未大涨的板块
            moderate_sectors = sector_data[
                (sector_data['涨跌幅'] > 0.5) &
                (sector_data['涨跌幅'] < 2.0)
            ] if '涨跌幅' in sector_data.columns else pd.DataFrame()

            for _, sector in moderate_sectors.head(3).iterrows():
                candidates.append({
                    'name': sector.get('板块名称', ''),
                    'strength': 'medium',
                    'description': f"{sector.get('板块名称', '')}板块显示轮动潜力",
                    'confidence': 0.6
                })

            return candidates

        except Exception as e:
            logger.error(f"识别轮动候选失败: {e}")
            return []

    def _get_empty_sector_data(self, sector_type: str) -> Dict[str, Any]:
        """获取空的板块数据结构"""
        return {
            'sector_type': sector_type,
            'total_sectors': 0,
            'hot_sectors': [],
            'sector_rankings': [],
            'capital_flow': {'total_inflow': 0, 'total_outflow': 0, 'net_flow': 0, 'flow_direction': 'neutral'},
            'rotation_signals': [],
            'data_available': False,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def get_comprehensive_sector_analysis(self) -> Dict[str, Any]:
        """获取综合板块分析"""
        try:
            logger.info("🔍 开始综合板块轮动分析...")

            # 获取各类板块数据
            concept_data = self.get_concept_sectors_data()
            industry_data = self.get_industry_sectors_data()

            # 综合分析
            all_hot_sectors = []
            all_hot_sectors.extend(concept_data.get('hot_sectors', []))
            all_hot_sectors.extend(industry_data.get('hot_sectors', []))

            # 按热度评分排序
            all_hot_sectors.sort(key=lambda x: x.get('heat_score', 0), reverse=True)

            # 计算整体市场轮动状态
            market_rotation_state = self._calculate_market_rotation_state(concept_data, industry_data)

            # 生成投资建议
            investment_suggestions = self._generate_investment_suggestions(all_hot_sectors[:10])

            result = {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_rotation_state': market_rotation_state,
                'top_hot_sectors': all_hot_sectors[:10],
                'concept_analysis': concept_data,
                'industry_analysis': industry_data,
                'investment_suggestions': investment_suggestions,
                'data_quality': 'good' if concept_data.get('data_available') and industry_data.get('data_available') else 'limited'
            }

            logger.info(f"✅ 综合板块分析完成，发现{len(all_hot_sectors)}个热点板块")
            return result

        except Exception as e:
            logger.error(f"综合板块分析失败: {e}")
            return {
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'market_rotation_state': 'unknown',
                'top_hot_sectors': [],
                'concept_analysis': {},
                'industry_analysis': {},
                'investment_suggestions': [],
                'data_quality': 'error'
            }

    def _calculate_market_rotation_state(self, concept_data: Dict, industry_data: Dict) -> str:
        """计算市场轮动状态"""
        try:
            concept_hot_count = len(concept_data.get('hot_sectors', []))
            industry_hot_count = len(industry_data.get('hot_sectors', []))

            total_hot_sectors = concept_hot_count + industry_hot_count

            if total_hot_sectors >= 8:
                return 'active_rotation'  # 活跃轮动
            elif total_hot_sectors >= 4:
                return 'moderate_rotation'  # 温和轮动
            elif total_hot_sectors >= 1:
                return 'limited_rotation'  # 有限轮动
            else:
                return 'no_rotation'  # 无明显轮动

        except Exception as e:
            logger.error(f"计算市场轮动状态失败: {e}")
            return 'unknown'

    def _generate_investment_suggestions(self, hot_sectors: List[Dict]) -> List[Dict[str, Any]]:
        """生成投资建议"""
        try:
            suggestions = []

            for sector in hot_sectors[:5]:  # 取前5个热点板块
                heat_score = sector.get('heat_score', 0)
                change_pct = sector.get('change_pct', 0)

                if heat_score > 15 and change_pct > 2:
                    suggestion_type = 'strong_buy'
                    description = f"强烈关注{sector['name']}板块，热度和涨幅双高"
                elif heat_score > 10 and change_pct > 1:
                    suggestion_type = 'moderate_buy'
                    description = f"适度关注{sector['name']}板块，显示上涨动能"
                else:
                    suggestion_type = 'watch'
                    description = f"观察{sector['name']}板块，等待更好时机"

                suggestions.append({
                    'sector_name': sector['name'],
                    'suggestion_type': suggestion_type,
                    'description': description,
                    'heat_score': heat_score,
                    'confidence': min(heat_score / 20.0, 1.0)
                })

            return suggestions

        except Exception as e:
            logger.error(f"生成投资建议失败: {e}")
            return []

def main():
    """测试板块轮动监控器"""
    print("📊 测试板块轮动监控器...")

    monitor = SectorRotationMonitor()

    # 测试概念板块
    print("\n🔍 测试概念板块数据...")
    concept_data = monitor.get_concept_sectors_data()
    print(f"概念板块总数: {concept_data.get('total_sectors', 0)}")
    print(f"热点板块数: {len(concept_data.get('hot_sectors', []))}")

    # 测试行业板块
    print("\n🔍 测试行业板块数据...")
    industry_data = monitor.get_industry_sectors_data()
    print(f"行业板块总数: {industry_data.get('total_sectors', 0)}")
    print(f"热点板块数: {len(industry_data.get('hot_sectors', []))}")

    # 综合分析
    print("\n🔍 综合板块轮动分析...")
    comprehensive = monitor.get_comprehensive_sector_analysis()
    print(f"市场轮动状态: {comprehensive['market_rotation_state']}")
    print(f"顶级热点板块数: {len(comprehensive['top_hot_sectors'])}")
    print(f"投资建议数: {len(comprehensive['investment_suggestions'])}")

if __name__ == "__main__":
    main()
