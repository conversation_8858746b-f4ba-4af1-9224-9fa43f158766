#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试添加股票功能修复
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
import threading
import time

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_add_stock_functionality():
    """测试添加股票功能"""
    print("🧪 开始测试添加股票功能修复...")
    
    try:
        # 导入修复后的模块
        from 潜在股进一步筛选 import StockFurtherScreening

        # 创建筛选器实例（它会自己创建窗口）
        screener = StockFurtherScreening()
        screener.root.withdraw()  # 隐藏主窗口
        
        # 测试添加一个新股票
        test_code = "000001"  # 平安银行
        print(f"🔍 测试添加股票: {test_code}")
        
        # 模拟添加股票的过程
        result = screener.add_stock_to_monitoring(test_code)
        
        if result:
            print(f"✅ 股票 {test_code} 添加成功！")
            
            # 检查是否正确添加到监控列表
            if test_code in screener.monitored_stocks:
                stock_info = screener.monitored_stocks[test_code]
                print(f"📊 股票信息: {stock_info['name']}")
                print(f"🕐 添加时间: {stock_info.get('added_time', '未知')}")
                
                # 检查历史数据是否正确初始化
                if test_code in screener.historical_base_data:
                    historical = screener.historical_base_data[test_code]
                    print(f"📈 历史数据: 包含 {len(historical)} 个字段")
                    print(f"📊 股票名称: {historical.get('stock_name', '未知')}")
                    print(f"💰 前日收盘价: {historical.get('prev_close', 0)}")
                else:
                    print("⚠️ 历史数据未正确初始化")
            else:
                print("❌ 股票未正确添加到监控列表")
        else:
            print(f"❌ 股票 {test_code} 添加失败")
        
        # 清理
        screener.root.destroy()

    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

def test_initialize_stock_monitoring():
    """测试股票监控初始化功能"""
    print("\n🧪 开始测试股票监控初始化功能...")

    try:
        # 导入修复后的模块
        from 潜在股进一步筛选 import StockFurtherScreening

        # 创建筛选器实例（它会自己创建窗口）
        screener = StockFurtherScreening()
        screener.root.withdraw()  # 隐藏主窗口
        
        # 测试直接调用初始化方法
        test_code = "000002"  # 万科A
        test_name = "万科A"
        
        print(f"🔍 测试初始化股票监控: {test_code} {test_name}")
        
        # 调用修复后的初始化方法
        result = screener.initialize_stock_monitoring(test_code, test_name)
        
        if result:
            print(f"✅ 股票 {test_code} 初始化成功！")
            
            # 检查历史数据
            if test_code in screener.historical_base_data:
                historical = screener.historical_base_data[test_code]
                print(f"📊 股票名称: {historical.get('stock_name', '未知')}")
                print(f"💰 前日收盘价: {historical.get('prev_close', 0)}")
                print(f"📈 MA5: {historical.get('ma5', 0)}")
                print(f"📊 成交量基准: {historical.get('avg_volume_5d', 0)}")
            else:
                print("❌ 历史数据未正确保存")
        else:
            print(f"❌ 股票 {test_code} 初始化失败")
        
        # 清理
        screener.root.destroy()
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试添加股票功能修复")
    print("=" * 50)
    
    # 测试1: 添加股票功能
    test_add_stock_functionality()
    
    # 测试2: 初始化股票监控功能
    test_initialize_stock_monitoring()
    
    print("\n" + "=" * 50)
    print("🏁 测试完成")
