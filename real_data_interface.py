#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据接口模块
集成主应用的数据获取功能，为连板潜力股实时评分应用提供真实数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple

# 导入主应用的数据获取器
try:
    from enhanced_data_fetcher import enhanced_stock_fetcher
    MAIN_APP_AVAILABLE = True
    print("✅ 主应用数据接口连接成功")
except ImportError as e:
    MAIN_APP_AVAILABLE = False
    print(f"❌ 主应用数据接口连接失败: {e}")

logger = logging.getLogger(__name__)

class RealDataInterface:
    """真实数据接口 - 连接主应用的数据获取功能"""
    
    def __init__(self):
        """初始化"""
        self.main_app_available = MAIN_APP_AVAILABLE
        if not self.main_app_available:
            logger.warning("主应用数据接口不可用，部分功能将无法使用")
    
    def get_historical_minute_data(self, code: str, target_date: str = None) -> Dict:
        """获取历史分时数据"""
        try:
            if not self.main_app_available:
                logger.error("主应用数据接口不可用")
                return {
                    'minute_data': pd.DataFrame(),
                    'data_available': False,
                    'error': '主应用数据接口不可用'
                }
            
            # 如果没有指定日期，使用当日
            if target_date is None:
                target_date = datetime.now().strftime('%Y%m%d')
            
            logger.info(f"获取股票{code}在{target_date}的历史分时数据")
            
            # 调用主应用的历史分时数据获取方法
            result = enhanced_stock_fetcher.get_historical_minute_data(code, target_date, '1')
            
            if result.get('data_available', False):
                minute_data = result.get('minute_data', pd.DataFrame())
                logger.info(f"成功获取{code}的{len(minute_data)}条分时数据")
                return {
                    'minute_data': minute_data,
                    'data_available': True,
                    'target_date': target_date,
                    'analysis': result.get('analysis', {})
                }
            else:
                logger.warning(f"未能获取{code}在{target_date}的分时数据")
                return {
                    'minute_data': pd.DataFrame(),
                    'data_available': False,
                    'error': result.get('data_summary', '数据获取失败')
                }
                
        except Exception as e:
            logger.error(f"获取历史分时数据失败: {e}")
            return {
                'minute_data': pd.DataFrame(),
                'data_available': False,
                'error': str(e)
            }
    
    def get_recent_7days_minute_data(self, code: str) -> Dict:
        """获取最近7天分时数据"""
        try:
            if not self.main_app_available:
                logger.error("主应用数据接口不可用")
                return {
                    'trading_days_data': [],
                    'data_available': False,
                    'error': '主应用数据接口不可用'
                }
            
            logger.info(f"获取股票{code}最近7天分时数据")
            
            # 调用主应用的7天分时数据获取方法
            result = enhanced_stock_fetcher.get_recent_7days_minute_data(code, '1')
            
            if result.get('data_available', False):
                trading_days_data = result.get('trading_days_data', [])
                logger.info(f"成功获取{code}最近{len(trading_days_data)}天分时数据")
                return result
            else:
                logger.warning(f"未能获取{code}的7天分时数据")
                return {
                    'trading_days_data': [],
                    'data_available': False,
                    'error': result.get('summary', '数据获取失败')
                }
                
        except Exception as e:
            logger.error(f"获取7天分时数据失败: {e}")
            return {
                'trading_days_data': [],
                'data_available': False,
                'error': str(e)
            }
    
    def get_stock_basic_info(self, code: str) -> Dict:
        """获取股票基本信息（使用主应用接口）"""
        try:
            if not self.main_app_available:
                logger.error("主应用数据接口不可用")
                return {}

            logger.info(f"获取股票{code}基本信息")

            # 直接调用主应用的基本信息获取方法
            basic_info = enhanced_stock_fetcher.get_stock_basic_info(code)
            realtime_quote = enhanced_stock_fetcher.get_realtime_quote([code])

            # 提取关键信息
            result = {
                'code': basic_info.get('code', code),
                'name': basic_info.get('name', '未知'),
                'industry': basic_info.get('industry', '未知'),
                'exchange': basic_info.get('exchange', '未知'),
                'list_date': basic_info.get('list_date', '未知'),
                'total_market_value': basic_info.get('total_market_value', 0),
                'circulation_market_value': basic_info.get('circulation_market_value', 0)
            }

            # 添加实时行情信息
            if not realtime_quote.empty:
                quote = realtime_quote.iloc[0]
                result.update({
                    'current_price': self._safe_get_float(quote, ['current_price', 'price', '最新价']),
                    'prev_close': self._safe_get_float(quote, ['prev_close', '昨收盘']),
                    'change_pct': self._safe_get_float(quote, ['change_pct', '涨跌幅'])
                })

            logger.info(f"成功获取{code}基本信息")
            return result

        except Exception as e:
            logger.error(f"获取股票基本信息失败: {e}")
            return {}
    
    def get_sector_info(self, code: str) -> Dict:
        """获取股票所属真正行业板块信息（不是概念板块）"""
        try:
            logger.info(f"获取股票{code}真正行业板块信息")

            # 使用AKShare获取股票个股信息，包含真正的行业信息
            import akshare as ak

            # 获取个股信息
            stock_info = ak.stock_individual_info_em(symbol=code)

            if not stock_info.empty:
                # 查找行业信息
                industry_row = stock_info[stock_info['item'] == '行业']

                if not industry_row.empty:
                    sector_name = industry_row['value'].iloc[0]
                    logger.info(f"获取到{code}的真正行业: {sector_name}")

                    return {
                        'has_sector': True,
                        'sector_name': sector_name,
                        'data_source': 'akshare_individual_info'
                    }
                else:
                    logger.warning(f"未找到{code}的行业信息")
                    # 尝试备用方法：从概念信息推断
                    return self._get_sector_from_concept_backup(code)
            else:
                logger.warning(f"无法获取{code}的个股信息")
                return self._get_sector_from_concept_backup(code)

        except Exception as e:
            logger.error(f"获取真正行业板块信息失败: {e}")
            # 备用方法：使用概念信息
            return self._get_sector_from_concept_backup(code)

    def _get_sector_from_concept_backup(self, code: str) -> Dict:
        """备用方法：从概念信息获取板块（当无法获取真正行业时使用）"""
        try:
            if not self.main_app_available:
                return {'has_sector': False, 'sector_name': None}

            # 使用概念信息作为备用
            concept_info = enhanced_stock_fetcher.get_concept_info(code)

            if not concept_info.empty:
                # 取第一个概念作为备用板块
                main_concept = concept_info.iloc[0]
                sector_name = main_concept.get('name', main_concept.get('concept_name', '未知板块'))

                logger.info(f"使用概念信息作为备用板块: {sector_name}")

                return {
                    'has_sector': True,
                    'sector_name': f"{sector_name}(概念)",  # 标注这是概念板块
                    'data_source': 'concept_backup'
                }
            else:
                # 最后的备用方法：根据股票代码推断
                return self._infer_sector_from_code(code)

        except Exception as e:
            logger.error(f"备用板块获取失败: {e}")
            return {'has_sector': False, 'sector_name': None}

    def get_prev_close_price(self, code: str) -> float:
        """获取前收盘价"""
        try:
            if not self.main_app_available:
                logger.error("主应用数据接口不可用")
                return 0.0

            logger.info(f"获取股票{code}前收盘价")

            # 获取实时行情数据
            realtime_quote = enhanced_stock_fetcher.get_realtime_quote([code])

            if not realtime_quote.empty:
                quote = realtime_quote.iloc[0]
                prev_close = self._safe_get_float(quote, ['prev_close', '昨收盘', '昨收'])

                if prev_close > 0:
                    logger.info(f"✅ 获取{code}前收盘价: {prev_close:.2f}")
                    return prev_close

            # 如果实时行情没有前收盘价，尝试从K线数据获取
            kline_data = enhanced_stock_fetcher.get_daily_kline(code, 2)
            if not kline_data.empty and len(kline_data) >= 2:
                prev_close = kline_data.iloc[-2]['close']  # 倒数第二天的收盘价
                logger.info(f"✅ 从K线获取{code}前收盘价: {prev_close:.2f}")
                return prev_close

            logger.warning(f"⚠️ 无法获取{code}的前收盘价")
            return 0.0

        except Exception as e:
            logger.error(f"获取前收盘价失败: {e}")
            return 0.0

    def get_sector_performance(self, sector_name: str) -> Dict:
        """获取板块表现数据"""
        try:
            if not self.main_app_available:
                logger.error("主应用数据接口不可用")
                return {
                    'morning_gain': 0.0,
                    'limit_up_count': 0,
                    'total_stocks': 0,
                    'limit_up_ratio': 0.0,
                    'sector_strength': 'unknown'
                }

            logger.info(f"获取板块{sector_name}表现数据")

            # 尝试使用AKShare获取板块数据
            try:
                import akshare as ak

                # 获取概念板块数据
                concept_data = ak.stock_board_concept_name_em()

                if not concept_data.empty:
                    # 查找匹配的板块
                    sector_row = concept_data[concept_data['板块名称'].str.contains(sector_name, na=False)]

                    if not sector_row.empty:
                        sector_info = sector_row.iloc[0]

                        # 提取板块表现数据
                        morning_gain = self._safe_get_float(sector_info, ['涨跌幅'], 0.0)
                        total_stocks = self._safe_get_float(sector_info, ['总市值'], 0)  # 这里可能需要调整

                        # 获取板块成分股来计算涨停数量
                        limit_up_count = 0
                        try:
                            concept_stocks = ak.stock_board_concept_cons_em(symbol=sector_name)
                            if not concept_stocks.empty:
                                total_stocks = len(concept_stocks)
                                # 统计涨停股数量（涨跌幅接近10%或20%）
                                if '涨跌幅' in concept_stocks.columns:
                                    limit_up_count = len(concept_stocks[concept_stocks['涨跌幅'] >= 9.5])
                        except:
                            pass

                        limit_up_ratio = (limit_up_count / total_stocks * 100) if total_stocks > 0 else 0.0

                        # 判断板块强度
                        if morning_gain >= 3:
                            sector_strength = 'strong'
                        elif morning_gain >= 1:
                            sector_strength = 'moderate'
                        elif morning_gain >= -1:
                            sector_strength = 'neutral'
                        else:
                            sector_strength = 'weak'

                        result = {
                            'morning_gain': morning_gain,
                            'limit_up_count': limit_up_count,
                            'total_stocks': total_stocks,
                            'limit_up_ratio': limit_up_ratio,
                            'sector_strength': sector_strength
                        }

                        logger.info(f"✅ 获取板块{sector_name}表现: 涨跌幅{morning_gain:.2f}%, 涨停{limit_up_count}/{total_stocks}只")
                        return result

            except Exception as e:
                logger.warning(f"AKShare获取板块数据失败: {e}")

            # 第一备用方案：使用同花顺API
            logger.info(f"使用同花顺备用方案获取板块{sector_name}数据")
            ths_result = self._get_sector_performance_from_ths(sector_name)

            if ths_result['data_available']:
                return ths_result

            # 第二备用方案：使用新浪财经API
            logger.info(f"使用新浪财经备用方案获取板块{sector_name}数据")
            sina_result = self._get_sector_performance_from_sina(sector_name)

            if sina_result['data_available']:
                return sina_result

            # 如果所有方案都失败，返回默认值
            logger.warning(f"⚠️ 所有数据源均无法获取板块{sector_name}的表现数据")
            return {
                'morning_gain': 0.0,
                'limit_up_count': 0,
                'total_stocks': 0,
                'limit_up_ratio': 0.0,
                'sector_strength': 'unknown'
            }

        except Exception as e:
            logger.error(f"获取板块表现失败: {e}")
            return {
                'morning_gain': 0.0,
                'limit_up_count': 0,
                'total_stocks': 0,
                'limit_up_ratio': 0.0,
                'sector_strength': 'unknown'
            }

    def _get_sector_performance_from_sina(self, sector_name: str) -> Dict:
        """从新浪财经获取板块表现数据"""
        try:
            import requests
            import re

            # 获取新浪财经板块数据
            url = "http://vip.stock.finance.sina.com.cn/q/view/newSinaHy.php"
            response = requests.get(url, timeout=10)

            if response.status_code != 200:
                return {'data_available': False, 'morning_gain': 0.0}

            content = response.text

            # 解析板块数据
            sectors = self._parse_sina_sector_data(content)

            if not sectors:
                return {'data_available': False, 'morning_gain': 0.0}

            # 查找目标板块
            target_sector = self._find_sina_sector_by_name(sectors, sector_name)

            if target_sector:
                change_pct = target_sector['change']

                # 判断板块强度
                if change_pct >= 3:
                    sector_strength = 'strong'
                elif change_pct >= 1:
                    sector_strength = 'moderate'
                elif change_pct >= -1:
                    sector_strength = 'neutral'
                else:
                    sector_strength = 'weak'

                result = {
                    'morning_gain': change_pct,
                    'limit_up_count': 0,  # 新浪数据不包含涨停统计
                    'total_stocks': target_sector['count'],
                    'limit_up_ratio': 0.0,
                    'sector_strength': sector_strength,
                    'data_available': True,
                    'data_source': 'sina_finance',
                    'sector_matched': target_sector['name'],
                    'data_accuracy_warning': True,  # 添加数据准确性警告
                    'warning_message': '新浪财经板块数据可能存在延迟，建议参考其他数据源验证'
                }

                logger.warning(f"⚠️ 新浪财经获取板块{sector_name}表现: {change_pct:.2f}% ({target_sector['count']}只股票) - 数据可能存在延迟")
                return result
            else:
                logger.warning(f"⚠️ 新浪财经未找到板块: {sector_name}")
                return {'data_available': False, 'morning_gain': 0.0}

        except Exception as e:
            logger.error(f"新浪财经获取板块数据失败: {e}")
            return {'data_available': False, 'morning_gain': 0.0}

    def _get_sector_performance_from_ths(self, sector_name: str) -> Dict:
        """从同花顺获取板块表现数据"""
        try:
            import requests
            from bs4 import BeautifulSoup

            # 获取同花顺板块数据
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Referer': 'http://q.10jqka.com.cn/',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8'
            }

            url = "http://q.10jqka.com.cn/thshy/index/field/199112/order/desc/page/1/ajax/1/"
            response = requests.get(url, headers=headers, timeout=15)

            if response.status_code != 200:
                return {'data_available': False, 'morning_gain': 0.0}

            # 解析HTML数据
            soup = BeautifulSoup(response.text, 'html.parser')
            table = soup.find('table')

            if not table:
                return {'data_available': False, 'morning_gain': 0.0}

            # 解析板块数据
            sectors = {}
            tbody = table.find('tbody')
            if tbody:
                rows = tbody.find_all('tr')
                for row in rows:
                    cells = row.find_all('td')
                    if len(cells) >= 3:
                        # 板块名称（第二列）
                        name_link = cells[1].find('a')
                        name = name_link.get_text(strip=True) if name_link else cells[1].get_text(strip=True)

                        # 涨跌幅（第三列）
                        change_text = cells[2].get_text(strip=True).replace('%', '').replace('+', '').replace('--', '0')
                        try:
                            change_value = float(change_text)
                            # 注意：同花顺返回的是百分比数值，需要除以100转换为小数
                            # 例如：1.23% -> 1.23 -> 0.0123
                            change_value = change_value / 100
                            sectors[name] = {'name': name, 'change': change_value}
                        except ValueError:
                            continue

            # 查找目标板块
            target_sector = self._find_ths_sector_by_name(sectors, sector_name)

            if target_sector:
                change_pct = target_sector['change']

                # 判断板块强度（change_pct已经是小数格式，0.03表示3%）
                if change_pct >= 0.03:
                    sector_strength = 'strong'
                elif change_pct >= 0.01:
                    sector_strength = 'moderate'
                elif change_pct >= -0.01:
                    sector_strength = 'neutral'
                else:
                    sector_strength = 'weak'

                result = {
                    'morning_gain': change_pct,
                    'limit_up_count': 0,
                    'total_stocks': 0,
                    'limit_up_ratio': 0.0,
                    'sector_strength': sector_strength,
                    'data_available': True,
                    'data_source': 'tonghuashun',
                    'sector_matched': target_sector['name']
                }

                logger.info(f"✅ 同花顺获取板块{sector_name}表现: {change_pct:.2f}% (匹配: {target_sector['name']})")
                return result
            else:
                logger.warning(f"⚠️ 同花顺未找到板块: {sector_name}")
                return {'data_available': False, 'morning_gain': 0.0}

        except Exception as e:
            logger.error(f"同花顺获取板块数据失败: {e}")
            return {'data_available': False, 'morning_gain': 0.0}

    def _find_ths_sector_by_name(self, sectors: Dict, target_name: str) -> Dict:
        """根据名称查找同花顺板块"""
        # 精确匹配
        if target_name in sectors:
            return sectors[target_name]

        # 同花顺板块名称映射
        ths_mapping = {
            '汽车零部件': ['汽车零部件'],
            '汽车整车': ['汽车整车'],
            '汽车行业': ['汽车零部件', '汽车整车', '汽车服务及其他'],
            '汽车服务': ['汽车服务及其他'],
            '文化传媒': ['传媒', '影视音像', '广告包装', '文化传播', '其他社会服务'],
            '通用设备': ['通用设备'],
            '专用设备': ['工程机械', '自动化设备'],
            '机械设备': ['通用设备', '工程机械', '自动化设备'],
            '造纸印刷': ['包装印刷'],
            '包装印刷': ['包装印刷'],
            '电子元器件': ['元件', '其他电子'],
            '电子设备': ['消费电子', '光学光电子'],
            '半导体': ['元件'],
            '计算机设备': ['计算机设备'],
            '通信设备': ['通信设备'],
            '医药生物': ['化学制药', '生物制品', '中药', '医疗器械'],
            '医疗保健': ['医疗服务', '医药商业'],
            '中药': ['中药'],
            '化学制药': ['化学制药'],
            '银行': ['银行'],
            '保险': ['保险'],
            '证券': ['证券'],
            '多元金融': ['多元金融'],
            '房地产': ['房地产'],
            '化工': ['化学制品', '电子化学品', '农化制品'],
            '建筑': ['建筑材料'],
            '食品饮料': ['饮料制造', '食品加工制造'],
            '纺织': ['纺织制造', '化学纤维'],
            '交通运输': ['机场航运'],
            '电力': ['电力'],
            '商贸': ['零售'],
            '钢铁': ['钢铁'],
            '有色金属': ['工业金属', '能源金属', '金属新材料']
        }

        # 尝试映射匹配
        mapped_names = ths_mapping.get(target_name, [])
        for mapped_name in mapped_names:
            if mapped_name in sectors:
                return sectors[mapped_name]

        # 模糊匹配
        for name, data in sectors.items():
            if target_name in name or name in target_name:
                return data

        return None

    def _parse_sina_sector_data(self, content: str) -> Dict:
        """解析新浪财经板块数据"""
        try:
            import re

            # 查找行业板块数据变量
            if 'S_Finance_bankuai_sinaindustry' not in content:
                return {}

            # 提取完整的JavaScript对象
            start_marker = 'S_Finance_bankuai_sinaindustry = {'
            start_pos = content.find(start_marker)

            if start_pos == -1:
                return {}

            # 从开始位置查找完整的对象
            brace_count = 0
            data_start = content.find('{', start_pos)
            current_pos = data_start

            while current_pos < len(content):
                char = content[current_pos]
                if char == '{':
                    brace_count += 1
                elif char == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        break
                current_pos += 1

            if brace_count != 0:
                return {}

            # 提取数据字符串
            data_str = content[data_start:current_pos+1]

            # 手动解析数据
            sectors = {}

            # 使用正则表达式提取每个板块的数据
            # 格式: "code":"code,name,count,price,change,..."
            pattern = r'"([^"]+)":"[^,]+,([^,]+),([^,]+),([^,]+),([^,]+),'
            matches = re.findall(pattern, data_str)

            for match in matches:
                code, name, count, price, change = match
                try:
                    change_float = float(change)
                    price_float = float(price)
                    count_int = int(count)

                    # 注意：新浪财经返回的也是百分比数值，需要除以100转换为小数
                    # 例如：1.23 -> 0.0123 (表示1.23%)
                    change_float = change_float / 100

                    sectors[name] = {
                        'code': code,
                        'name': name,
                        'count': count_int,
                        'price': price_float,
                        'change': change_float
                    }
                except ValueError:
                    continue

            return sectors

        except Exception as e:
            logger.error(f"解析新浪数据失败: {e}")
            return {}

    def _find_sina_sector_by_name(self, sectors: Dict, target_name: str) -> Dict:
        """根据名称查找新浪财经板块"""
        # 精确匹配
        if target_name in sectors:
            return sectors[target_name]

        # 板块名称映射（处理常见的名称差异）
        name_mapping = {
            # 汽车相关
            '汽车零部件': '汽车制造',
            '汽车整车': '汽车制造',
            '汽车行业': '汽车制造',
            '汽车服务': '汽车制造',

            # 电子相关
            '电子元器件': '电子器件',
            '电子设备': '电子器件',
            '半导体': '电子器件',
            '计算机设备': '电子信息',
            '通信设备': '电子信息',

            # 医药相关
            '医药生物': '生物制药',
            '医疗保健': '生物制药',
            '医疗服务': '医疗器械',
            '中药': '生物制药',
            '化学制药': '生物制药',

            # 传媒相关
            '文化传媒': '传媒娱乐',
            '广播电视': '传媒娱乐',
            '互联网传媒': '传媒娱乐',
            '游戏': '传媒娱乐',
            '影视动漫': '传媒娱乐',

            # 食品相关
            '食品饮料': '食品行业',
            '饮料制造': '食品行业',
            '食品加工': '食品行业',
            '农产品加工': '食品行业',

            # 房地产相关
            '房地产开发': '房地产',
            '房地产服务': '房地产',
            '园区开发': '房地产',

            # 金融相关
            '银行': '金融行业',
            '保险': '金融行业',
            '证券': '金融行业',
            '信托': '金融行业',
            '多元金融': '金融行业',

            # 化工相关
            '化学原料': '化工行业',
            '化学制品': '化工行业',
            '石油化工': '化工行业',
            '精细化工': '化工行业',

            # 机械相关
            '机械设备': '机械行业',
            '通用机械': '机械行业',
            '专用设备': '机械行业',
            '通用设备': '机械行业',  # 新增
            '仪器仪表': '仪器仪表',

            # 建筑相关
            '建筑装饰': '建筑建材',
            '建筑工程': '建筑建材',
            '装修装饰': '建筑建材',
            '水泥制造': '水泥行业',

            # 纺织相关
            '纺织制造': '纺织行业',
            '服装制造': '服装鞋类',
            '纺织服装': '纺织行业',

            # 造纸相关
            '造纸印刷': '造纸行业',  # 新增
            '包装印刷': '造纸行业',  # 新增
            '印刷': '造纸行业',      # 新增

            # 交通相关
            '航空运输': '交通运输',
            '铁路运输': '交通运输',
            '水上运输': '交通运输',
            '物流': '交通运输',

            # 公用事业
            '电力': '电力行业',
            '燃气': '供水供气',
            '水务': '供水供气',
            '环保工程': '环保行业',

            # 零售相关
            '商业贸易': '商业百货',
            '专业零售': '商业百货',
            '一般零售': '商业百货',
            '贸易': '物资外贸'
        }

        # 尝试映射匹配
        mapped_name = name_mapping.get(target_name)
        if mapped_name and mapped_name in sectors:
            return sectors[mapped_name]

        # 模糊匹配
        for name, data in sectors.items():
            if target_name in name or name in target_name:
                return data

        return None

    def get_previous_day_data(self, code: str) -> Dict:
        """获取前一交易日数据"""
        try:
            if not self.main_app_available:
                logger.error("主应用数据接口不可用")
                return {'data_available': False, 'error': '主应用数据接口不可用'}
            
            # 计算前一交易日日期
            current_date = datetime.now()
            for i in range(1, 8):  # 最多往前找7天
                prev_date = current_date - timedelta(days=i)
                # 跳过周末
                if prev_date.weekday() < 5:  # 0-4是周一到周五
                    target_date = prev_date.strftime('%Y%m%d')
                    break
            else:
                return {'data_available': False, 'error': '无法找到前一交易日'}
            
            logger.info(f"获取股票{code}前一交易日({target_date})数据")
            
            # 获取前一交易日的分时数据
            result = self.get_historical_minute_data(code, target_date)
            
            if result.get('data_available', False):
                minute_data = result.get('minute_data', pd.DataFrame())
                
                # 分析前日数据，计算炸板评分
                zhaban_score = self._calculate_zhaban_score(minute_data)
                
                return {
                    'data_available': True,
                    'target_date': target_date,
                    'minute_data': minute_data,
                    'zhaban_score': zhaban_score
                }
            else:
                return {
                    'data_available': False,
                    'error': result.get('error', '前日数据获取失败')
                }
                
        except Exception as e:
            logger.error(f"获取前一交易日数据失败: {e}")
            return {'data_available': False, 'error': str(e)}
    
    def _safe_get_float(self, data, keys, default=0):
        """安全获取浮点数，支持多个可能的字段名"""
        if isinstance(keys, str):
            keys = [keys]
        for key in keys:
            value = data.get(key, default)
            if value is not None and value != '':
                try:
                    return float(value)
                except (ValueError, TypeError):
                    continue
        return default
    
    def _infer_sector_from_code(self, code: str) -> Dict:
        """根据股票代码推断板块"""
        try:
            if code.startswith('000'):
                return {'has_sector': True, 'sector_name': '深市主板'}
            elif code.startswith('002'):
                return {'has_sector': True, 'sector_name': '中小板'}
            elif code.startswith('300'):
                return {'has_sector': True, 'sector_name': '创业板'}
            elif code.startswith('600') or code.startswith('601') or code.startswith('603'):
                return {'has_sector': True, 'sector_name': '沪市主板'}
            elif code.startswith('688'):
                return {'has_sector': True, 'sector_name': '科创板'}
            else:
                return {'has_sector': False, 'sector_name': None}
        except:
            return {'has_sector': False, 'sector_name': None}
    
    def _calculate_zhaban_score(self, minute_data: pd.DataFrame) -> float:
        """计算炸板评分（简化实现）"""
        try:
            if minute_data.empty:
                return 0.0
            
            # 检测是否有涨停和开板行为
            if '收盘' not in minute_data.columns:
                return 0.0
            
            prices = minute_data['收盘'].values
            if len(prices) < 10:
                return 0.0
            
            # 简化的炸板检测：检查是否有大幅上涨后回落
            max_price = np.max(prices)
            min_price = np.min(prices)
            final_price = prices[-1]
            
            # 计算最大涨幅
            max_gain = (max_price - prices[0]) / prices[0] * 100
            
            # 计算回落幅度
            pullback = (max_price - final_price) / max_price * 100
            
            # 简化的炸板评分逻辑
            if max_gain > 8 and pullback > 3:  # 大涨后大幅回落
                return 30.0  # 低评分，表示炸板质量差
            elif max_gain > 5 and pullback > 2:  # 中等涨幅后回落
                return 55.0  # 中等评分
            elif max_gain > 3:  # 有一定涨幅
                return 75.0  # 较高评分
            else:
                return 85.0  # 高评分，表示没有明显炸板
                
        except Exception as e:
            logger.error(f"计算炸板评分失败: {e}")
            return 0.0

# 全局实例
real_data_interface = RealDataInterface()
