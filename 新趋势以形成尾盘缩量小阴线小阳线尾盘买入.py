#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
趋势以形成尾盘缩量小阴线小阳线尾盘买入策略
基于Day1（放量中阳突破压力位）→ Day2（缩量小阳线整理）策略
严格按照报告算法实现
"""

import os
import json
import pandas as pd
import numpy as np
import requests
import time
import re
from datetime import datetime, timedelta
from collections import defaultdict
import warnings
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
warnings.filterwarnings('ignore')

# 导入efinance库（用于快速获取K线数据）
try:
    import efinance as ef
    EFINANCE_AVAILABLE = True
    print("✅ 成功导入efinance库")
except ImportError as e:
    EFINANCE_AVAILABLE = False
    print(f"❌ efinance库导入失败: {e}")
    print("💡 请安装: pip install efinance")

class TrendBreakthroughStrategy:
    """趋势突破策略筛选器"""
    
    def __init__(self):
        self.current_date = datetime.now()
        self.efinance_available = EFINANCE_AVAILABLE

        # 添加数据缓存，避免重复获取
        self.kline_cache = {}  # 缓存K线数据
        self.minute_cache = {}  # 缓存分时数据
        
        # 报告中的严格参数配置
        self.config = {
            # Day1 放量中阳突破条件
            'day1_min_change': 3.5,        # 最小涨幅3.5%
            'day1_max_change': 7.0,        # 最大涨幅7.0%
            'volume_ratio_min': 1.8,       # 量能超5日均量1.8倍
            'volume_ratio_max': 2.5,       # 避免天量>2.5倍
            'breakthrough_margin': 1.005,  # 收盘价>压力位×1.005
            
            # Day2 缩量小阳线整理条件
            'day2_min_change': -0.5,       # 最小涨幅-0.5%
            'day2_max_change': 2.5,        # 最大涨幅2.5%
            'day2_max_amplitude': 4.0,     # 振幅<4%
            'day2_volume_ratio': 0.7,      # 量能<昨日×0.7
            'tail_volume_ratio': 0.15,     # 尾盘30分钟量能<日均量×15%
            
            # 市值筛选
            'min_market_cap': 50,          # 最小市值50亿
            'max_market_cap': 300,         # 最大市值300亿
            
            # 风控条件
            'max_turnover_rate': 20,       # 排除换手率>20%
            
            # 数据获取配置
            'max_workers': 4,              # 线程数（降低以减少API压力）
            'batch_size': 1000             # 批量处理大小（增加到1000）
        }
        
        print("✅ 趋势突破策略筛选器初始化完成")
        print(f"📊 Day1条件: 涨幅{self.config['day1_min_change']}%-{self.config['day1_max_change']}%, 量比{self.config['volume_ratio_min']}-{self.config['volume_ratio_max']}倍")
        print(f"📊 Day2条件: 涨幅{self.config['day2_min_change']}%-{self.config['day2_max_change']}%, 振幅<{self.config['day2_max_amplitude']}%, 缩量<{self.config['day2_volume_ratio']}倍")
        print(f"💰 市值范围: {self.config['min_market_cap']}-{self.config['max_market_cap']}亿元")
        print(f"🚀 优化配置: 批量大小{self.config['batch_size']}, 线程数{self.config['max_workers']}, 数据缓存已启用")
        print(f"⚡ 数据源: {'efinance(快速)' if self.efinance_available else '不可用'}")
    
    def run_strategy(self):
        """运行策略主流程"""
        print("🚀 趋势突破策略筛选开始...")
        print("=" * 80)
        
        # 选择运行模式
        mode = self._select_mode()
        
        if mode == "1":
            # 当前开盘模式
            day1_date, day2_date = self._get_trading_mode_dates()
            print(f"📅 开盘模式: Day1={day1_date}, Day2={day2_date}(实时)")
        else:
            # 当前关盘模式
            day1_date, day2_date = self._get_after_hours_mode_dates()
            print(f"📅 关盘模式: Day1={day1_date}, Day2={day2_date}")
        
        try:
            # 第一步：获取全市场数据并基础筛选
            print(f"\n📊 第一步：获取全市场数据并基础筛选...")
            market_candidates = self._get_market_candidates()
            
            if not market_candidates:
                print("❌ 基础筛选后无候选股票")
                return []
            
            print(f"✅ 基础筛选完成，获得 {len(market_candidates)} 只候选股票")
            
            # 第二步：市值筛选
            print(f"\n💰 第二步：市值筛选({self.config['min_market_cap']}-{self.config['max_market_cap']}亿)...")
            market_cap_candidates = self._filter_by_market_cap(market_candidates)
            
            if not market_cap_candidates:
                print("❌ 市值筛选后无候选股票")
                return []
            
            print(f"✅ 市值筛选完成，剩余 {len(market_cap_candidates)} 只股票")
            
            # 第三步：批量预加载K线数据
            print(f"\n📦 第三步：批量预加载K线数据...")
            self._batch_preload_kline_data(market_cap_candidates)

            # 第四步：Day1筛选（放量中阳突破）
            print(f"\n🎯 第四步：Day1筛选 - 放量中阳突破({day1_date})...")
            day1_candidates = self._filter_day1_breakthrough(market_cap_candidates, day1_date)
            
            if not day1_candidates:
                print("❌ Day1筛选后无候选股票")
                return []
            
            print(f"✅ Day1筛选完成，发现 {len(day1_candidates)} 只突破股票")
            
            # 第五步：Day2筛选（缩量小阳线整理）
            print(f"\n🎯 第五步：Day2筛选 - 缩量小阳线整理({day2_date})...")
            final_candidates = self._filter_day2_consolidation(day1_candidates, day1_date, day2_date, mode)
            
            print(f"✅ Day2筛选完成，最终发现 {len(final_candidates)} 只符合策略的股票")

            # 第六步：结果展示和导出
            self._display_results(final_candidates, day1_date, day2_date)
            self._export_results(final_candidates, day1_date, day2_date)
            
            return final_candidates
            
        except Exception as e:
            print(f"❌ 策略筛选失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def _select_mode(self):
        """选择运行模式"""
        print("\n🔧 请选择运行模式:")
        print("1. 当前开盘 - 实时分析当日数据")
        print("2. 当前关盘 - 分析昨日+今日完整数据")
        
        while True:
            try:
                choice = input("\n请输入选择 (1 或 2): ").strip()
                if choice in ['1', '2']:
                    return choice
                else:
                    print("❌ 请输入 1 或 2")
            except KeyboardInterrupt:
                print("\n👋 用户取消操作")
                exit(0)
            except Exception as e:
                print(f"❌ 输入错误: {e}")
    
    def _get_trading_mode_dates(self):
        """获取开盘模式的日期"""
        today = datetime.now()
        
        # Day1是昨日，Day2是今日(实时)
        day1_date = (today - timedelta(days=1)).strftime('%Y%m%d')
        day2_date = today.strftime('%Y%m%d')
        
        return day1_date, day2_date
    
    def _get_after_hours_mode_dates(self):
        """获取关盘模式的日期"""
        today = datetime.now()
        
        # 如果是周末，需要回推到工作日
        if today.weekday() == 5:  # 周六
            day2_date = (today - timedelta(days=1)).strftime('%Y%m%d')  # 周五
            day1_date = (today - timedelta(days=2)).strftime('%Y%m%d')  # 周四
        elif today.weekday() == 6:  # 周日
            day2_date = (today - timedelta(days=2)).strftime('%Y%m%d')  # 周五
            day1_date = (today - timedelta(days=3)).strftime('%Y%m%d')  # 周四
        else:
            # 工作日：Day1是昨日，Day2是今日
            day1_date = (today - timedelta(days=1)).strftime('%Y%m%d')
            day2_date = today.strftime('%Y%m%d')
        
        return day1_date, day2_date
    
    def _get_market_candidates(self):
        """获取全市场数据并进行基础筛选（复用游资洗盘系统的逻辑）"""
        try:
            print("📡 获取全市场股票数据...")
            
            # 使用新浪财经API获取全市场数据
            market_data = self._get_sina_market_data_with_retry()
            
            if not market_data or len(market_data) == 0:
                print("❌ 无法获取市场数据")
                return []
            
            print(f"✅ 获取全市场数据: {len(market_data)} 只股票")
            
            # 基础筛选：排除ST、创业板、科创板、北交所
            filtered_candidates = []
            excluded_count = 0

            for stock in market_data:
                try:
                    original_code = stock.get('代码', '') or stock.get('symbol', '')
                    name = stock.get('名称', '') or stock.get('name', '')

                    # 标准化股票代码
                    code = self._normalize_stock_code(original_code)

                    # 检查代码是否有效
                    if not code:
                        continue

                    # 基础筛选
                    if self._check_market_scope(code, name):
                        # 获取市值（亿元）
                        market_cap = self._get_market_cap(stock)

                        candidate = {
                            'code': code,
                            'name': name,
                            'market_cap': market_cap,
                            'latest_price': stock.get('最新价', 0) or stock.get('trade', 0),
                            'change_pct': stock.get('涨跌幅', 0) or stock.get('changepercent', 0),
                            'volume': stock.get('成交量', 0) or stock.get('volume', 0),
                            'turnover_rate': stock.get('换手率', 0) or stock.get('turnoverratio', 0),
                            'original_data': stock
                        }
                        filtered_candidates.append(candidate)
                    else:
                        excluded_count += 1

                except Exception as e:
                    continue
            
            print(f"✅ 基础筛选完成: {len(market_data)} → {len(filtered_candidates)} 只股票")
            print(f"   排除: ST股票、创业板(300)、科创板(688)、北交所(8/4)")
            print(f"   统计: 被排除 {excluded_count} 只, 通过筛选 {len(filtered_candidates)} 只")
            
            return filtered_candidates
            
        except Exception as e:
            print(f"❌ 获取市场候选股票失败: {e}")
            return []
    
    def _get_sina_market_data_with_retry(self):
        """从新浪财经获取全市场数据（带重试机制）"""
        max_retries = 2
        
        for attempt in range(max_retries + 1):
            try:
                print(f"📡 从新浪财经获取全市场数据... (尝试 {attempt + 1}/{max_retries + 1})")
                
                all_data = []
                page = 1
                max_pages = 60
                
                while page <= max_pages:
                    page_data = self._get_sina_page_data_with_retry(page)
                    
                    if page_data is None:
                        print(f"⚠️ 第 {page} 页获取失败，停止获取")
                        break
                    elif len(page_data) == 0:
                        print(f"📄 已获取到最后一页，共 {page} 页")
                        break
                    else:
                        all_data.extend(page_data)
                        print(f"✅ 第 {page} 页获取成功: {len(page_data)} 只股票")
                        
                        if len(page_data) < 100:
                            print(f"📄 已获取到最后一页，共 {page} 页")
                            break
                        
                        page += 1
                        time.sleep(0.5)  # 减少页面间隔到0.5秒
                
                if not all_data:
                    if attempt < max_retries:
                        wait_time = 10 if attempt == 0 else 60
                        print(f"❌ 第{attempt + 1}次获取数据为空，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print("❌ 重试后仍未获取到任何数据")
                        return []
                
                print(f"✅ 新浪财经数据获取完成: 共 {len(all_data)} 只股票")
                
                # 标准化数据格式
                standardized_data = []
                for item in all_data:
                    try:
                        standardized_item = {
                            '代码': item.get('symbol', ''),
                            '名称': item.get('name', ''),
                            '最新价': float(item.get('trade', 0)) if item.get('trade') else 0,
                            '涨跌幅': float(item.get('changepercent', 0)) if item.get('changepercent') else 0,
                            '成交量': int(item.get('volume', 0)) if item.get('volume') else 0,
                            '总市值': float(item.get('mktcap', 0)) if item.get('mktcap') else 0,
                            '流通市值': float(item.get('nmc', 0)) if item.get('nmc') else 0,
                            '换手率': float(item.get('turnoverratio', 0)) if item.get('turnoverratio') else 0
                        }
                        standardized_data.append(standardized_item)
                    except Exception as e:
                        continue
                
                return standardized_data
                
            except Exception as e:
                if attempt < max_retries:
                    wait_time = 10 if attempt == 0 else 60
                    print(f"❌ 第{attempt + 1}次获取新浪财经数据失败: {e}")
                    print(f"⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ 重试{max_retries}次后仍失败: {e}")
                    return []
        
        return []

    def _get_sina_page_data_with_retry(self, page, max_retries=2):
        """获取新浪财经单页数据（带重试机制）"""
        for attempt in range(max_retries + 1):
            try:
                url = "http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
                params = {
                    'page': page,
                    'num': 100,
                    'sort': 'symbol',
                    'asc': 1,
                    'node': 'hs_a'
                }

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'http://vip.stock.finance.sina.com.cn/',
                    'Accept': 'application/json, text/javascript, */*; q=0.01'
                }

                response = requests.get(url, params=params, headers=headers, timeout=15)

                if response.status_code == 200:
                    data_text = response.text
                    data_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', data_text)

                    try:
                        page_data = json.loads(data_text)
                        if page_data and isinstance(page_data, list):
                            return page_data
                        else:
                            return []  # 空数据，表示到达最后一页
                    except json.JSONDecodeError as e:
                        if attempt < max_retries:
                            time.sleep(10)
                            continue
                        else:
                            return None
                else:
                    if attempt < max_retries:
                        time.sleep(10)
                        continue
                    else:
                        return None
            except Exception as e:
                if attempt < max_retries:
                    time.sleep(10)
                    continue
                else:
                    return None
        return None

    def _batch_preload_kline_data(self, candidates):
        """批量预加载K线数据，避免重复获取"""
        try:
            print(f"📦 开始批量预加载 {len(candidates)} 只股票的K线数据...")

            # 分批处理，避免内存过大
            batch_size = self.config['batch_size']
            total_batches = (len(candidates) + batch_size - 1) // batch_size

            success_count = 0

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(candidates))
                batch_candidates = candidates[start_idx:end_idx]

                print(f"📦 处理第 {batch_idx + 1}/{total_batches} 批次: {len(batch_candidates)} 只股票")

                # 使用线程池并发获取数据，设置超时避免阻塞
                with ThreadPoolExecutor(max_workers=self.config['max_workers']) as executor:
                    # 提交所有任务
                    future_to_code = {
                        executor.submit(self._get_single_stock_kline_with_cache, candidate['code']): candidate['code']
                        for candidate in batch_candidates
                    }

                    # 收集结果，设置超时
                    batch_success = 0
                    batch_timeout = 600  # 10分钟超时
                    completed_count = 0

                    try:
                        for future in as_completed(future_to_code, timeout=batch_timeout):
                            code = future_to_code[future]
                            completed_count += 1
                            try:
                                success = future.result(timeout=30)  # 单个任务30秒超时
                                if success:
                                    batch_success += 1
                                    success_count += 1

                                # 显示进度
                                if completed_count % 50 == 0 or completed_count == len(batch_candidates):
                                    print(f"  📊 批次进度: {completed_count}/{len(batch_candidates)} ({completed_count/len(batch_candidates)*100:.1f}%)")

                            except Exception as e:
                                print(f"⚠️ {code}: 任务执行异常，跳过")
                                continue

                    except Exception as e:
                        print(f"⚠️ 第 {batch_idx + 1} 批次超时或异常，已完成 {completed_count}/{len(batch_candidates)} 只股票")

                    print(f"✅ 第 {batch_idx + 1} 批次完成: {batch_success}/{len(batch_candidates)} 只股票成功")

                # 批次间隔，避免API限制
                if batch_idx < total_batches - 1:
                    print(f"⏱️ 批次间隔5秒...")
                    time.sleep(5)

            print(f"✅ 批量预加载完成: {success_count}/{len(candidates)} 只股票成功获取K线数据")

        except Exception as e:
            print(f"❌ 批量预加载失败: {e}")

    def _get_single_stock_kline_with_cache(self, code):
        """获取单只股票K线数据并缓存（只使用efinance数据源）"""
        try:
            # 检查缓存
            if code in self.kline_cache:
                return True

            # 只使用efinance获取K线数据（带重试机制）
            if self.efinance_available:
                kline_data = self._get_efinance_kline_with_retry(code)

                if kline_data is not None and not kline_data.empty and len(kline_data) >= 30:
                    # 标准化列名
                    kline_data = self._standardize_kline_columns(kline_data)
                    # 缓存数据
                    self.kline_cache[code] = kline_data
                    return True

            # 不再使用备用方案
            return False

        except Exception as e:
            return False

    def _get_efinance_kline_with_retry(self, code):
        """使用efinance获取K线数据，智能重试机制"""
        # 重试配置：等待时间（秒）- 优化为更短的等待时间
        retry_delays = [5, 15, 30]  # 5秒, 15秒, 30秒
        max_retries = len(retry_delays)

        for attempt in range(max_retries + 1):
            try:
                # 第一次尝试不等待，后续尝试需要等待
                if attempt > 0:
                    wait_time = retry_delays[attempt - 1]
                    print(f"⏱️ {code}: API连接问题，第{attempt + 1}次尝试前等待{wait_time}秒...")
                    time.sleep(wait_time)

                # 尝试获取数据
                kline_data = ef.stock.get_quote_history(code)

                if kline_data is not None and not kline_data.empty and len(kline_data) >= 30:
                    if attempt > 0:
                        print(f"✅ {code}: 第{attempt + 1}次尝试成功获取K线数据")
                    return kline_data
                else:
                    # 数据为空通常是单只股票问题，直接跳过不重试
                    print(f"⚠️ {code}: 数据为空或不足30条，跳过该股票")
                    return None

            except Exception as e:
                error_str = str(e).lower()

                # 判断是否为需要重试的API连接问题
                if self._is_api_connection_error(error_str):
                    if attempt < max_retries:
                        print(f"⚠️ {code}: API连接问题 ({e}), 准备重试...")
                    else:
                        print(f"❌ {code}: API连接重试失败 ({e})")
                else:
                    # 单只股票问题，直接跳过
                    print(f"⚠️ {code}: 股票数据问题 ({e}), 跳过该股票")
                    return None

        return None

    def _is_api_connection_error(self, error_str):
        """判断是否为需要重试的API连接错误"""
        # 需要重试的错误类型
        retry_error_keywords = [
            'remoteDisconnected',
            'connection broken',
            'connection aborted',
            'timeout',
            'network',
            'connection reset',
            'connection refused',
            'ssl',
            'certificate',
            'proxy',
            'dns',
            'resolve',
            'unreachable'
        ]

        for keyword in retry_error_keywords:
            if keyword in error_str:
                return True

        return False

    def _standardize_kline_columns(self, df):
        """标准化K线数据列名"""
        try:
            # efinance的列名映射到标准列名
            column_mapping = {
                '股票名称': '名称',
                '股票代码': '代码',
                '日期': '日期',
                '开盘': '开盘',
                '收盘': '收盘',
                '最高': '最高',
                '最低': '最低',
                '成交量': '成交量',
                '成交额': '成交额',
                '涨跌幅': '涨跌幅',
                '涨跌额': '涨跌额',
                '换手率': '换手率'
            }

            # 重命名列
            df_renamed = df.rename(columns=column_mapping)

            # 确保日期列格式正确
            if '日期' in df_renamed.columns:
                df_renamed['日期'] = pd.to_datetime(df_renamed['日期'])

            return df_renamed

        except Exception as e:
            return df

    def _filter_by_market_cap(self, candidates):
        """按市值筛选股票"""
        try:
            filtered_candidates = []

            for candidate in candidates:
                market_cap = candidate.get('market_cap', 0)

                # 按报告要求：50-300亿市值
                if self.config['min_market_cap'] <= market_cap <= self.config['max_market_cap']:
                    filtered_candidates.append(candidate)

            print(f"✅ 市值筛选: {len(candidates)} → {len(filtered_candidates)} 只股票")
            return filtered_candidates

        except Exception as e:
            print(f"❌ 市值筛选失败: {e}")
            return []

    def _filter_day1_breakthrough(self, candidates, day1_date):
        """Day1筛选：放量中阳突破压力位"""
        try:
            print(f"🎯 开始Day1筛选，目标日期: {day1_date}")
            day1_candidates = []

            for i, candidate in enumerate(candidates, 1):
                try:
                    code = candidate['code']
                    name = candidate['name']

                    if i % 50 == 0:
                        print(f"  📊 Day1筛选进度: {i}/{len(candidates)} ({i/len(candidates)*100:.1f}%)")

                    # 获取历史K线数据（降低要求到30天数据）
                    kline_data = self._get_stock_kline_data(code, 30)

                    if kline_data is None or kline_data.empty or len(kline_data) < 20:
                        if i <= 100:  # 对前100只股票显示详细日志
                            print(f"  ⚠️  {code} {name}: 无法获取足够的K线数据，跳过")
                        continue

                    # 添加数据长度日志
                    if i <= 100:  # 对前100只股票显示详细日志
                        print(f"  📊 {code} {name}: 获取到{len(kline_data)}天K线数据")

                    # 获取Day1当日数据
                    day1_data = self._get_day_data_from_kline(kline_data, day1_date)
                    if day1_data is None:
                        if i <= 100:  # 对前100只股票显示详细日志
                            print(f"  ⚠️  {code} {name}: 无法获取{day1_date}的K线数据，跳过")
                        continue

                    # 检查Day1条件
                    day1_result = self._check_day1_conditions(code, day1_data, kline_data, day1_date)

                    if day1_result['passed']:
                        candidate['day1_analysis'] = day1_result
                        day1_candidates.append(candidate)
                        print(f"  ✅ {code} {name}: 涨幅{day1_result['change_pct']:.1f}%, 量比{day1_result['volume_ratio']:.1f}, 突破{day1_result['breakthrough_type']}")
                    else:
                        # 显示不符合条件的详细原因
                        if i <= 100:  # 对前100只股票显示详细日志
                            print(f"  ❌ {code} {name}: 不符合Day1条件")
                            for reason in day1_result['reasons']:
                                print(f"     - {reason}")

                except Exception as e:
                    if i <= 100:  # 对前100只股票显示详细日志
                        print(f"  ⚠️  {code} {name}: Day1筛选异常: {e}")
                    continue

            print(f"✅ Day1筛选完成: {len(candidates)} → {len(day1_candidates)} 只股票")
            return day1_candidates

        except Exception as e:
            print(f"❌ Day1筛选失败: {e}")
            return []

    def _get_stock_kline_data(self, code, days=250):
        """获取股票K线数据（优先使用缓存，然后efinance带重试）"""
        try:
            # 优先使用缓存
            if code in self.kline_cache:
                cached_data = self.kline_cache[code]
                if not cached_data.empty and len(cached_data) >= min(days, 30):
                    return cached_data.tail(days) if len(cached_data) > days else cached_data

            # 缓存中没有，优先使用efinance实时获取（带重试机制）
            if self.efinance_available:
                kline_data = self._get_efinance_kline_with_retry(code)

                if kline_data is not None and not kline_data.empty:
                    # 标准化列名
                    kline_data = self._standardize_kline_columns(kline_data)
                    # 更新缓存
                    self.kline_cache[code] = kline_data
                    return kline_data.tail(days) if len(kline_data) > days else kline_data

            # 不再使用备用方案
            return pd.DataFrame()

        except Exception as e:
            return pd.DataFrame()

    def _get_day_data_from_kline(self, kline_data, target_date):
        """从K线数据中获取指定日期的数据"""
        try:
            if kline_data.empty:
                return None

            date_column = self._get_date_column(kline_data)
            if date_column is None:
                return None

            # 标准化日期格式 - 将目标日期转换为与K线数据相同格式
            target_date_obj = datetime.strptime(target_date, '%Y%m%d').date()

            for idx, row in kline_data.iterrows():
                try:
                    # 使用pd.to_datetime确保正确解析日期格式
                    row_date = pd.to_datetime(row[date_column]).date()
                    if row_date == target_date_obj:
                        return row.to_dict()
                except Exception as e:
                    continue

            return None

        except Exception as e:
            return None

    def _check_day1_conditions(self, code, day1_data, kline_data, day1_date):
        """检查Day1的所有条件"""
        try:
            result = {
                'passed': False,
                'change_pct': 0,
                'volume_ratio': 0,
                'breakthrough_type': '无',
                'close_price': 0,
                'volume': 0,
                'reasons': []
            }

            # 提取Day1数据
            open_price = day1_data.get('开盘', day1_data.get('open', 0))
            close_price = day1_data.get('收盘', day1_data.get('close', 0))
            high_price = day1_data.get('最高', day1_data.get('high', 0))
            low_price = day1_data.get('最低', day1_data.get('low', 0))
            volume = day1_data.get('成交量', day1_data.get('volume', 0))

            # 获取前一日收盘价计算涨跌幅
            prev_close = self._get_previous_close(kline_data, day1_date)
            if prev_close == 0:
                result['reasons'].append("无法获取前一日收盘价")
                return result

            # 条件1: 涨幅3.5%-7.0%
            change_pct = (close_price - prev_close) / prev_close * 100
            result['change_pct'] = change_pct

            if not (self.config['day1_min_change'] <= change_pct <= self.config['day1_max_change']):
                result['reasons'].append(f"涨幅{change_pct:.1f}%不在{self.config['day1_min_change']}-{self.config['day1_max_change']}%范围")
                return result

            # 条件2: 量能验证（成交量 > 前5日均量×1.8）
            avg_volume_5 = self._get_average_volume(kline_data, day1_date, 5)
            if avg_volume_5 == 0:
                result['reasons'].append("无法获取5日平均成交量")
                return result

            volume_ratio = volume / avg_volume_5
            result['volume_ratio'] = volume_ratio

            if not (self.config['volume_ratio_min'] <= volume_ratio <= self.config['volume_ratio_max']):
                result['reasons'].append(f"量比{volume_ratio:.1f}不在{self.config['volume_ratio_min']}-{self.config['volume_ratio_max']}范围")
                return result

            # 条件3: 突破关键价格位
            breakthrough_result = self._check_breakthrough(close_price, kline_data, day1_date)
            if not breakthrough_result['breakthrough']:
                result['reasons'].append("未突破关键价格位")
                return result

            result['breakthrough_type'] = breakthrough_result['type']

            # 条件4: 收盘价站稳压力位上方0.5%
            pressure_price = breakthrough_result['pressure_price']
            if close_price < pressure_price * self.config['breakthrough_margin']:
                result['reasons'].append(f"收盘价{close_price:.2f}未站稳压力位{pressure_price:.2f}上方0.5%")
                return result

            # 所有条件通过
            result['passed'] = True
            result['close_price'] = close_price
            result['volume'] = volume

            return result

        except Exception as e:
            return {
                'passed': False,
                'change_pct': 0,
                'volume_ratio': 0,
                'breakthrough_type': '无',
                'close_price': 0,
                'volume': 0,
                'reasons': [f'Day1条件检查异常: {e}']
            }

    def _get_previous_close(self, kline_data, target_date):
        """获取指定日期前一日的收盘价"""
        try:
            if kline_data.empty:
                return 0

            target_date_obj = datetime.strptime(target_date, '%Y%m%d').date()

            # 找到目标日期的索引
            date_column = self._get_date_column(kline_data)
            if date_column is None:
                return 0

            target_idx = None
            for idx, row in kline_data.iterrows():
                try:
                    # 使用pd.to_datetime确保正确解析日期格式
                    row_date = pd.to_datetime(row[date_column]).date()
                    if row_date == target_date_obj:
                        target_idx = idx
                        break
                except:
                    continue

            if target_idx is None:
                return 0

            # 获取前一行数据
            kline_list = kline_data.reset_index(drop=True)
            target_pos = kline_list[kline_list.index == target_idx].index[0]

            if target_pos > 0:
                prev_row = kline_list.iloc[target_pos - 1]
                return prev_row.get('收盘', prev_row.get('close', 0))

            return 0

        except Exception as e:
            return 0

    def _get_average_volume(self, kline_data, target_date, days):
        """获取指定日期前N日的平均成交量"""
        try:
            if kline_data.empty:
                return 0

            target_date_obj = datetime.strptime(target_date, '%Y%m%d').date()

            # 找到目标日期的索引
            date_column = self._get_date_column(kline_data)
            if date_column is None:
                return 0

            target_idx = None
            for idx, row in kline_data.iterrows():
                try:
                    # 使用pd.to_datetime确保正确解析日期格式
                    row_date = pd.to_datetime(row[date_column]).date()
                    if row_date == target_date_obj:
                        target_idx = idx
                        break
                except:
                    continue

            if target_idx is None:
                return 0

            # 获取前N日数据
            kline_list = kline_data.reset_index(drop=True)
            target_pos = kline_list[kline_list.index == target_idx].index[0]

            if target_pos >= days:
                prev_data = kline_list.iloc[target_pos - days:target_pos]
                volume_column = '成交量' if '成交量' in prev_data.columns else 'volume'
                if volume_column in prev_data.columns:
                    return prev_data[volume_column].mean()

            return 0

        except Exception as e:
            return 0

    def _check_breakthrough(self, close_price, kline_data, target_date):
        """检查是否突破关键价格位"""
        try:
            result = {
                'breakthrough': False,
                'type': '无',
                'pressure_price': 0
            }

            if kline_data.empty:
                return result

            target_date_obj = datetime.strptime(target_date, '%Y%m%d').date()

            # 获取目标日期之前的数据
            date_column = self._get_date_column(kline_data)
            if date_column is None:
                return result

            historical_data = []
            for idx, row in kline_data.iterrows():
                try:
                    row_date = pd.to_datetime(row[date_column]).date()
                    if row_date < target_date_obj:
                        historical_data.append(row)
                except:
                    continue

            if len(historical_data) < 30:
                return result

            historical_df = pd.DataFrame(historical_data)
            high_column = '最高' if '最高' in historical_df.columns else 'high'

            if high_column not in historical_df.columns:
                return result

            # 按您的要求：降低数据要求，只检查突破前30日最高价
            if len(historical_df) >= 30:
                high_30 = historical_df[high_column].tail(30).max()
                result['pressure_price'] = high_30
                if close_price > high_30 * self.config['breakthrough_margin']:
                    result['breakthrough'] = True
                    result['type'] = '30日最高价'
                    return result

            return result

        except Exception as e:
            return {
                'breakthrough': False,
                'type': '检查失败',
                'pressure_price': 0
            }

    def _filter_day2_consolidation(self, candidates, day1_date, day2_date, mode):
        """Day2筛选：缩量小阳线整理"""
        try:
            print(f"🎯 开始Day2筛选，目标日期: {day2_date}")
            day2_candidates = []

            for i, candidate in enumerate(candidates, 1):
                try:
                    code = candidate['code']
                    name = candidate['name']

                    if i % 50 == 0:
                        print(f"  📊 Day2筛选进度: {i}/{len(candidates)} ({i/len(candidates)*100:.1f}%)")

                    # 根据模式选择不同的处理方式
                    if mode == "1":
                        # 开盘模式 - 使用实时数据
                        day2_result = self._check_day2_conditions_realtime(code, candidate, day1_date, day2_date)
                    else:
                        # 关盘模式 - 使用历史数据
                        day2_result = self._check_day2_conditions(code, candidate, day1_date, day2_date)

                    if day2_result['passed']:
                        candidate['day2_analysis'] = day2_result
                        day2_candidates.append(candidate)
                        print(f"  ✅ {code} {name}: 涨幅{day2_result['change_pct']:.1f}%, 振幅{day2_result['amplitude']:.1f}%, 缩量{day2_result['volume_ratio']:.1f}")
                    else:
                        if i <= 100:  # 对前100只股票显示详细日志
                            print(f"  ❌ {code} {name}: 不符合Day2条件")
                            for reason in day2_result['reasons']:
                                print(f"     - {reason}")

                except Exception as e:
                    if i <= 100:  # 对前100只股票显示详细日志
                        print(f"  ⚠️  {code} {name}: Day2筛选异常: {e}")
                    continue

            print(f"✅ Day2筛选完成: {len(candidates)} → {len(day2_candidates)} 只股票")
            return day2_candidates

        except Exception as e:
            print(f"❌ Day2筛选失败: {e}")
            return []

    def _check_day2_conditions(self, code, candidate, day1_date, day2_date):
        """检查Day2条件（关盘模式，使用历史数据）"""
        try:
            result = {
                'passed': False,
                'change_pct': 0,
                'amplitude': 0,
                'volume_ratio': 0,
                'reasons': []
            }

            # 获取Day2历史K线数据
            kline_data = self._get_stock_kline_data(code, 30)
            if kline_data is None or kline_data.empty:
                result['reasons'].append("无法获取K线数据")
                return result

            # 获取Day2数据
            day2_data = self._get_day_data_from_kline(kline_data, day2_date)
            if day2_data is None:
                result['reasons'].append(f"无法获取{day2_date}的K线数据")
                return result

            # 提取Day2数据
            open_price = day2_data.get('开盘', day2_data.get('open', 0))
            close_price = day2_data.get('收盘', day2_data.get('close', 0))
            high_price = day2_data.get('最高', day2_data.get('high', 0))
            low_price = day2_data.get('最低', day2_data.get('low', 0))
            volume = day2_data.get('成交量', day2_data.get('volume', 0))

            # 获取Day1数据
            day1_analysis = candidate.get('day1_analysis', {})
            day1_close = day1_analysis.get('close_price', 0)
            day1_volume = day1_analysis.get('volume', 0)

            # 获取Day1最低价
            day1_data = self._get_day_data_from_kline(kline_data, day1_date)
            day1_low = day1_data.get('最低', day1_data.get('low', 0)) if day1_data else 0

            # 条件1: 涨幅-0.5%~+2.5%
            change_pct = (close_price - day1_close) / day1_close * 100 if day1_close > 0 else 0
            result['change_pct'] = change_pct

            if not (self.config['day2_min_change'] <= change_pct <= self.config['day2_max_change']):
                result['reasons'].append(f"涨幅{change_pct:.1f}%不在{self.config['day2_min_change']}-{self.config['day2_max_change']}%范围")
                return result

            # 条件2: 振幅<4%
            amplitude = (high_price - low_price) / day1_close * 100 if day1_close > 0 else 0
            result['amplitude'] = amplitude

            if amplitude >= self.config['day2_max_amplitude']:
                result['reasons'].append(f"振幅{amplitude:.1f}%超过{self.config['day2_max_amplitude']}%")
                return result

            # 条件3: 量能萎缩
            volume_ratio = day2_volume / day1_volume if day1_volume > 0 else 0
            result['volume_ratio'] = volume_ratio

            if volume_ratio >= self.config['day2_volume_ratio']:
                result['reasons'].append(f"量比{volume_ratio:.1f}未缩量至{self.config['day2_volume_ratio']}以下")
                return result

            # 条件4: 不破支撑
            not_break_support = day2_low > day1_low if day1_low > 0 else True
            if not not_break_support:
                result['reasons'].append(f"Day2最低价{day2_low:.2f}破了Day1最低价{day1_low:.2f}")
                return result

            # 条件5: 尾盘量能检查
            tail_volume_result = self._check_tail_volume(code, day2_date)
            # 尾盘量能作为加分项，不作为必要条件

            # 所有条件通过
            result['passed'] = True
            result['reasons'] = ['所有Day2条件满足']

            return result

        except Exception as e:
            return {
                'passed': False,
                'change_pct': 0,
                'amplitude': 0,
                'volume_ratio': 0,
                'reasons': [f'Day2条件检查异常: {e}']
            }

    def _check_day2_conditions_realtime(self, code, candidate, day1_date, day2_date):
        """检查Day2条件（开盘模式，使用实时数据）"""
        try:
            result = {
                'passed': False,
                'change_pct': 0,
                'amplitude': 0,
                'volume_ratio': 0,
                'tail_volume_ratio': 0,
                'not_break_support': False,
                'reasons': []
            }

            # 获取实时分时数据（带缓存）
            cache_key = f"{code}_{day2_date}"
            if cache_key in self.minute_cache:
                minute_data = self.minute_cache[cache_key]
            else:
                # 由于移除了AKShare，这里直接返回默认值
                result['reasons'].append("无法获取实时分时数据")
                return result

            # 从分时数据计算当日指标
            day2_open = minute_data['开盘'].iloc[0]
            day2_close = minute_data['收盘'].iloc[-1]
            day2_high = minute_data['最高'].max()
            day2_low = minute_data['最低'].min()
            day2_volume = minute_data['成交量'].sum()

            # 获取Day1数据
            day1_analysis = candidate.get('day1_analysis', {})
            day1_close = day1_analysis.get('close_price', 0)
            day1_volume = day1_analysis.get('volume', 0)

            # 获取Day1最低价
            kline_data = self._get_stock_kline_data(code, 30)
            day1_data = self._get_day_data_from_kline(kline_data, day1_date)
            day1_low = day1_data.get('最低', day1_data.get('low', 0)) if day1_data else 0

            # 执行相同的条件检查
            # 条件1: 涨幅-0.5%~+2.5%
            change_pct = (day2_close - day1_close) / day1_close * 100 if day1_close > 0 else 0
            result['change_pct'] = change_pct

            if not (self.config['day2_min_change'] <= change_pct <= self.config['day2_max_change']):
                result['reasons'].append(f"涨幅{change_pct:.1f}%不在{self.config['day2_min_change']}-{self.config['day2_max_change']}%范围")
                return result

            # 条件2: 振幅<4%
            amplitude = (day2_high - day2_low) / day1_close * 100 if day1_close > 0 else 0
            result['amplitude'] = amplitude

            if amplitude >= self.config['day2_max_amplitude']:
                result['reasons'].append(f"振幅{amplitude:.1f}%超过{self.config['day2_max_amplitude']}%")
                return result

            # 条件3: 量能萎缩
            volume_ratio = day2_volume / day1_volume if day1_volume > 0 else 0
            result['volume_ratio'] = volume_ratio

            if volume_ratio >= self.config['day2_volume_ratio']:
                result['reasons'].append(f"量比{volume_ratio:.1f}未缩量至{self.config['day2_volume_ratio']}以下")
                return result

            # 条件4: 不破支撑
            not_break_support = day2_low > day1_low if day1_low > 0 else True
            result['not_break_support'] = not_break_support

            if not not_break_support:
                result['reasons'].append(f"Day2最低价{day2_low:.2f}破了Day1最低价{day1_low:.2f}")
                return result

            # 条件5: 尾盘量能检查
            tail_volume_result = self._check_tail_volume_from_minute_data(minute_data)
            result['tail_volume_ratio'] = tail_volume_result.get('tail_ratio', 0)

            # 所有条件通过
            result['passed'] = True
            result['reasons'] = ['所有Day2条件满足']

            return result

        except Exception as e:
            return {
                'passed': False,
                'change_pct': 0,
                'amplitude': 0,
                'volume_ratio': 0,
                'reasons': [f'Day2条件检查异常: {e}']
            }

    def _check_tail_volume(self, code, day2_date):
        """检查尾盘量能（历史数据）"""
        try:
            result = {
                'tail_ratio': 0
            }

            # 由于移除了AKShare，这里直接返回默认值
            return result

        except Exception as e:
            return {
                'tail_ratio': 0
            }

    def _check_tail_volume_from_minute_data(self, minute_data):
        """从分时数据检查尾盘量能"""
        try:
            result = {
                'tail_ratio': 0
            }

            if minute_data.empty:
                return result

            # 由于移除了AKShare，这里直接返回默认值
            return result

        except Exception as e:
            return {
                'tail_ratio': 0
            }

    def _get_date_column(self, df):
        """获取数据框中的日期列名"""
        date_columns = ['日期', 'date', 'Date', 'DATE']
        for col in date_columns:
            if col in df.columns:
                return col
        return None

    def _normalize_stock_code(self, code):
        """标准化股票代码"""
        if not code:
            return ""

        # 转换为字符串并清理
        code = str(code).strip().upper()

        # 移除点号分隔符
        if '.' in code:
            parts = code.split('.')
            if len(parts) >= 2:
                code = parts[-1]

        # 移除常见前缀
        if code.startswith(('SH', 'SZ')):
            code = code[2:]
        elif code.startswith(('sh', 'sz')):
            code = code[2:]

        # 确保是6位数字
        if code.isdigit() and len(code) == 6:
            return code

        # 如果不足6位，尝试补零
        if code.isdigit() and len(code) < 6:
            code = code.zfill(6)
            return code

        return ""

    def _check_market_scope(self, code, name):
        """检查股票是否在分析范围内"""
        if not code or len(code) != 6:
            return False

        # 排除ST股票
        if 'ST' in name or '*ST' in name:
            return False

        # 排除创业板（300开头）
        if code.startswith('300'):
            return False

        # 排除科创板（688开头）
        if code.startswith('688'):
            return False

        # 排除北交所（8开头或4开头）
        if code.startswith(('8', '4')):
            return False

        return True

    def _get_market_cap(self, stock_data):
        """获取市值（亿元）"""
        try:
            # 尝试获取总市值
            market_cap = stock_data.get('总市值', 0) or stock_data.get('mktcap', 0)

            if market_cap and market_cap > 0:
                # 转换为亿元（假设原始数据是万元）
                return market_cap / 10000

            return 0

        except Exception as e:
            return 0

    def _display_results(self, final_candidates, day1_date, day2_date):
        """显示分析结果"""
        try:
            if not final_candidates:
                print("\n📊 分析结果：未发现符合条件的趋势突破股票")
                return

            print(f"\n📊 分析结果：发现 {len(final_candidates)} 只符合趋势突破策略的股票")
            print("=" * 80)

            # 按Day1涨幅排序
            sorted_candidates = sorted(final_candidates,
                                     key=lambda x: x.get('day1_analysis', {}).get('change_pct', 0),
                                     reverse=True)

            for i, candidate in enumerate(sorted_candidates[:10], 1):  # 显示前10只
                code = candidate['code']
                name = candidate['name']
                day1_analysis = candidate.get('day1_analysis', {})
                day2_analysis = candidate.get('day2_analysis', {})

                print(f"\n🏆 第{i}名: {code} {name}")
                print(f"   💰 市值: {candidate.get('market_cap', 0):.1f}亿元")
                print(f"   📈 Day1({day1_date}): 涨幅{day1_analysis.get('change_pct', 0):.1f}%, 量比{day1_analysis.get('volume_ratio', 0):.1f}, 突破{day1_analysis.get('breakthrough_type', '无')}")
                print(f"   📊 Day2({day2_date}): 涨幅{day2_analysis.get('change_pct', 0):.1f}%, 振幅{day2_analysis.get('amplitude', 0):.1f}%, 缩量{day2_analysis.get('volume_ratio', 0):.1f}")

                if day2_analysis.get('tail_volume_ratio', 0) > 0:
                    print(f"   ⏰ 尾盘量能占比: {day2_analysis.get('tail_volume_ratio', 0):.1%}")

            if len(sorted_candidates) > 10:
                print(f"\n... 还有 {len(sorted_candidates) - 10} 只股票")

        except Exception as e:
            print(f"❌ 结果显示失败: {e}")

    def _export_results(self, candidates, day1_date, day2_date):
        """导出结果到Excel"""
        try:
            if not candidates:
                return

            print("\n📁 导出分析结果...")

            # 准备导出数据
            export_data = []

            for candidate in candidates:
                try:
                    day1_analysis = candidate.get('day1_analysis', {})
                    day2_analysis = candidate.get('day2_analysis', {})

                    row = {
                        # 基础信息
                        '股票代码': candidate.get('code', 'N/A'),
                        '股票名称': candidate.get('name', 'N/A'),
                        '市值(亿元)': candidate.get('market_cap', 0),

                        # Day1分析
                        f'Day1日期({day1_date})': day1_date,
                        'Day1涨幅(%)': day1_analysis.get('change_pct', 0),
                        'Day1量比': day1_analysis.get('volume_ratio', 0),
                        'Day1突破类型': day1_analysis.get('breakthrough_type', '无'),
                        'Day1收盘价': day1_analysis.get('close_price', 0),
                        'Day1成交量': day1_analysis.get('volume', 0),

                        # Day2分析
                        f'Day2日期({day2_date})': day2_date,
                        'Day2涨幅(%)': day2_analysis.get('change_pct', 0),
                        'Day2振幅(%)': day2_analysis.get('amplitude', 0),
                        'Day2量比': day2_analysis.get('volume_ratio', 0),
                        'Day2尾盘量能占比(%)': day2_analysis.get('tail_volume_ratio', 0) * 100,
                        'Day2不破支撑': '是' if day2_analysis.get('not_break_support', False) else '否',

                        # 策略评估
                        '策略符合度': '完全符合',
                        '分析时间': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                    }

                    export_data.append(row)

                except Exception as e:
                    continue

            if export_data:
                df = pd.DataFrame(export_data)

                # 生成文件名
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                filename = f"趋势突破策略筛选结果_{day1_date}_{day2_date}_{timestamp}.xlsx"

                # 导出Excel
                df.to_excel(filename, index=False, engine='openpyxl')
                print(f"✅ 结果已导出到: {filename}")
                print(f"📊 共导出 {len(export_data)} 只股票的详细分析数据")

        except Exception as e:
            print(f"❌ 导出失败: {e}")


def main():
    """主函数"""
    try:
        print("🚀 趋势突破策略筛选器")
        print("基于Day1（放量中阳突破压力位）→ Day2（缩量小阳线整理）策略")
        print("=" * 80)

        # 创建策略实例
        strategy = TrendBreakthroughStrategy()

        # 运行策略
        results = strategy.run_strategy()

        if results:
            print(f"\n🎉 策略筛选完成！发现 {len(results)} 只符合条件的股票")
        else:
            print(f"\n📊 策略筛选完成，未发现符合条件的股票")

    except KeyboardInterrupt:
        print("\n👋 用户取消操作")
    except Exception as e:
        print(f"\n❌ 程序运行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()