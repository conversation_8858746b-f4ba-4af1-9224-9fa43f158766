#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标特征工程
扩展技术指标特征，包括MACD、KDJ、BOLL、RSI等技术指标的组合特征
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TechnicalIndicatorsFeatureEngine:
    """技术指标特征工程器"""
    
    def __init__(self):
        # 技术指标参数配置
        self.indicator_params = {
            'ma_periods': [5, 10, 20, 30, 60],
            'ema_periods': [12, 26],
            'rsi_period': 14,
            'macd_params': {'fast': 12, 'slow': 26, 'signal': 9},
            'kdj_params': {'k_period': 9, 'd_period': 3, 'j_period': 3},
            'bollinger_params': {'period': 20, 'std_dev': 2},
            'volume_ma_periods': [5, 10, 20],
            'atr_period': 14,
            'obv_period': 10
        }
        
        # 核心特征权重（基于25只连板股分析结果）
        self.core_feature_weights = {
            'morning_volume_active': 0.25,      # 100%特征
            'active_volume_pattern': 0.25,      # 100%特征
            'near_recent_high': 0.16,           # 64%特征
            'stable_price_pattern': 0.14,       # 56%特征
            'volume_price_positive_correlation': 0.12,  # 48%特征
            'price_breakthrough': 0.08          # 44%特征
        }
        
        logger.info("📈 技术指标特征工程器初始化完成")
    
    def extract_all_technical_features(self, kline_data: pd.DataFrame, minute_data: pd.DataFrame = None) -> Dict[str, Any]:
        """提取所有技术指标特征"""
        try:
            if kline_data.empty:
                return self._get_empty_features()
            
            features = {}
            
            # 1. 基础价格特征
            price_features = self._extract_price_features(kline_data)
            features.update(price_features)
            
            # 2. 移动平均线特征
            ma_features = self._extract_ma_features(kline_data)
            features.update(ma_features)
            
            # 3. MACD特征
            macd_features = self._extract_macd_features(kline_data)
            features.update(macd_features)
            
            # 4. RSI特征
            rsi_features = self._extract_rsi_features(kline_data)
            features.update(rsi_features)
            
            # 5. KDJ特征
            kdj_features = self._extract_kdj_features(kline_data)
            features.update(kdj_features)
            
            # 6. 布林带特征
            bollinger_features = self._extract_bollinger_features(kline_data)
            features.update(bollinger_features)
            
            # 7. 成交量技术特征
            volume_features = self._extract_volume_technical_features(kline_data)
            features.update(volume_features)
            
            # 8. 波动率特征
            volatility_features = self._extract_volatility_features(kline_data)
            features.update(volatility_features)
            
            # 9. 趋势强度特征
            trend_features = self._extract_trend_features(kline_data)
            features.update(trend_features)
            
            # 10. 分时特征（如果有分时数据）
            if minute_data is not None and not minute_data.empty:
                minute_features = self._extract_minute_technical_features(minute_data)
                features.update(minute_features)
            
            # 11. 核心连板特征（基于分析结果）
            core_features = self._extract_core_limit_up_features(kline_data, minute_data)
            features.update(core_features)
            
            features['feature_extraction_success'] = True
            features['total_features'] = len([k for k in features.keys() if not k.startswith('feature_')])
            
            logger.info(f"✅ 技术指标特征提取完成，共{features['total_features']}个特征")
            return features
            
        except Exception as e:
            logger.error(f"技术指标特征提取失败: {e}")
            return self._get_empty_features()
    
    def _extract_price_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取基础价格特征"""
        try:
            features = {}
            
            if len(df) < 5:
                return features
            
            # 价格变化特征
            current_price = df['close'].iloc[-1]
            prev_price = df['close'].iloc[-2] if len(df) >= 2 else current_price
            
            features.update({
                'price_change_1d': (current_price - prev_price) / prev_price if prev_price > 0 else 0,
                'price_change_5d': (current_price - df['close'].iloc[-6]) / df['close'].iloc[-6] if len(df) >= 6 and df['close'].iloc[-6] > 0 else 0,
                'price_change_10d': (current_price - df['close'].iloc[-11]) / df['close'].iloc[-11] if len(df) >= 11 and df['close'].iloc[-11] > 0 else 0,
            })
            
            # 价格位置特征
            recent_high = df['high'].tail(20).max() if len(df) >= 20 else df['high'].max()
            recent_low = df['low'].tail(20).min() if len(df) >= 20 else df['low'].min()
            
            if recent_high > recent_low:
                features['price_position_in_range'] = (current_price - recent_low) / (recent_high - recent_low)
            else:
                features['price_position_in_range'] = 0.5
            
            # 价格突破特征
            features['near_recent_high'] = current_price / recent_high if recent_high > 0 else 0
            features['price_breakthrough'] = 1.0 if current_price >= recent_high * 0.99 else 0.0
            
            # 价格形态特征
            if len(df) >= 10:
                recent_closes = df['close'].tail(10)
                price_std = recent_closes.std()
                price_mean = recent_closes.mean()
                features['stable_price_pattern'] = 1.0 if price_std / price_mean < 0.02 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"价格特征提取失败: {e}")
            return {}
    
    def _extract_ma_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取移动平均线特征"""
        try:
            features = {}
            
            if len(df) < max(self.indicator_params['ma_periods']):
                return features
            
            # 计算各周期移动平均线
            mas = {}
            for period in self.indicator_params['ma_periods']:
                if len(df) >= period:
                    mas[f'ma{period}'] = df['close'].rolling(window=period).mean().iloc[-1]
            
            current_price = df['close'].iloc[-1]
            
            # MA位置特征
            for period in self.indicator_params['ma_periods']:
                ma_key = f'ma{period}'
                if ma_key in mas and mas[ma_key] > 0:
                    features[f'price_above_{ma_key}'] = 1.0 if current_price > mas[ma_key] else 0.0
                    features[f'price_distance_to_{ma_key}'] = (current_price - mas[ma_key]) / mas[ma_key]
            
            # MA排列特征
            if 'ma5' in mas and 'ma10' in mas and 'ma20' in mas:
                features['ma5_above_ma10'] = 1.0 if mas['ma5'] > mas['ma10'] else 0.0
                features['ma10_above_ma20'] = 1.0 if mas['ma10'] > mas['ma20'] else 0.0
                features['ma_bullish_alignment'] = 1.0 if mas['ma5'] > mas['ma10'] > mas['ma20'] else 0.0
            
            # MA斜率特征
            if len(df) >= 25:
                ma5_slope = (mas.get('ma5', 0) - df['close'].rolling(window=5).mean().iloc[-6]) / mas.get('ma5', 1) if mas.get('ma5', 0) > 0 else 0
                features['ma5_slope'] = ma5_slope
                features['ma5_uptrend'] = 1.0 if ma5_slope > 0.01 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"移动平均线特征提取失败: {e}")
            return {}
    
    def _extract_macd_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取MACD特征"""
        try:
            features = {}
            
            if len(df) < 35:  # MACD需要足够的数据
                return features
            
            # 计算MACD
            exp1 = df['close'].ewm(span=self.indicator_params['macd_params']['fast']).mean()
            exp2 = df['close'].ewm(span=self.indicator_params['macd_params']['slow']).mean()
            macd_line = exp1 - exp2
            signal_line = macd_line.ewm(span=self.indicator_params['macd_params']['signal']).mean()
            histogram = macd_line - signal_line
            
            # MACD特征
            features.update({
                'macd_line': macd_line.iloc[-1],
                'macd_signal': signal_line.iloc[-1],
                'macd_histogram': histogram.iloc[-1],
                'macd_above_signal': 1.0 if macd_line.iloc[-1] > signal_line.iloc[-1] else 0.0,
                'macd_above_zero': 1.0 if macd_line.iloc[-1] > 0 else 0.0,
                'macd_histogram_positive': 1.0 if histogram.iloc[-1] > 0 else 0.0
            })
            
            # MACD金叉死叉
            if len(macd_line) >= 2 and len(signal_line) >= 2:
                prev_macd_above = macd_line.iloc[-2] > signal_line.iloc[-2]
                curr_macd_above = macd_line.iloc[-1] > signal_line.iloc[-1]
                
                features['macd_golden_cross'] = 1.0 if not prev_macd_above and curr_macd_above else 0.0
                features['macd_death_cross'] = 1.0 if prev_macd_above and not curr_macd_above else 0.0
            
            # MACD背离特征
            if len(df) >= 10:
                price_trend = df['close'].iloc[-1] - df['close'].iloc[-10]
                macd_trend = macd_line.iloc[-1] - macd_line.iloc[-10]
                
                # 顶背离：价格新高但MACD不创新高
                features['macd_bearish_divergence'] = 1.0 if price_trend > 0 and macd_trend < 0 else 0.0
                # 底背离：价格新低但MACD不创新低
                features['macd_bullish_divergence'] = 1.0 if price_trend < 0 and macd_trend > 0 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"MACD特征提取失败: {e}")
            return {}
    
    def _extract_rsi_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取RSI特征"""
        try:
            features = {}
            
            if len(df) < self.indicator_params['rsi_period'] + 1:
                return features
            
            # 计算RSI
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.indicator_params['rsi_period']).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.indicator_params['rsi_period']).mean()
            
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            current_rsi = rsi.iloc[-1]
            
            features.update({
                'rsi_14': current_rsi,
                'rsi_oversold': 1.0 if current_rsi < 30 else 0.0,
                'rsi_overbought': 1.0 if current_rsi > 70 else 0.0,
                'rsi_moderate': 1.0 if 30 <= current_rsi <= 70 else 0.0,
                'rsi_bullish_zone': 1.0 if 50 < current_rsi < 80 else 0.0
            })
            
            # RSI趋势
            if len(rsi) >= 5:
                rsi_trend = rsi.iloc[-1] - rsi.iloc[-5]
                features['rsi_trend'] = rsi_trend
                features['rsi_uptrend'] = 1.0 if rsi_trend > 5 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"RSI特征提取失败: {e}")
            return {}

    def _extract_kdj_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取KDJ特征"""
        try:
            features = {}

            if len(df) < self.indicator_params['kdj_params']['k_period'] + 5:
                return features

            # 计算KDJ
            k_period = self.indicator_params['kdj_params']['k_period']
            d_period = self.indicator_params['kdj_params']['d_period']

            low_min = df['low'].rolling(window=k_period).min()
            high_max = df['high'].rolling(window=k_period).max()

            rsv = (df['close'] - low_min) / (high_max - low_min) * 100
            rsv = rsv.fillna(50)  # 填充NaN值

            k_values = []
            d_values = []

            # 初始值
            k_prev = 50
            d_prev = 50

            for rsv_val in rsv:
                k_curr = (2/3) * k_prev + (1/3) * rsv_val
                d_curr = (2/3) * d_prev + (1/3) * k_curr

                k_values.append(k_curr)
                d_values.append(d_curr)

                k_prev = k_curr
                d_prev = d_curr

            k_line = pd.Series(k_values, index=df.index)
            d_line = pd.Series(d_values, index=df.index)
            j_line = 3 * k_line - 2 * d_line

            current_k = k_line.iloc[-1]
            current_d = d_line.iloc[-1]
            current_j = j_line.iloc[-1]

            features.update({
                'kdj_k': current_k,
                'kdj_d': current_d,
                'kdj_j': current_j,
                'kdj_k_above_d': 1.0 if current_k > current_d else 0.0,
                'kdj_oversold': 1.0 if current_k < 20 and current_d < 20 else 0.0,
                'kdj_overbought': 1.0 if current_k > 80 and current_d > 80 else 0.0
            })

            # KDJ金叉死叉
            if len(k_line) >= 2 and len(d_line) >= 2:
                prev_k_above_d = k_line.iloc[-2] > d_line.iloc[-2]
                curr_k_above_d = k_line.iloc[-1] > d_line.iloc[-1]

                features['kdj_golden_cross'] = 1.0 if not prev_k_above_d and curr_k_above_d else 0.0
                features['kdj_death_cross'] = 1.0 if prev_k_above_d and not curr_k_above_d else 0.0

            return features

        except Exception as e:
            logger.error(f"KDJ特征提取失败: {e}")
            return {}

    def _extract_bollinger_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取布林带特征"""
        try:
            features = {}

            period = self.indicator_params['bollinger_params']['period']
            std_dev = self.indicator_params['bollinger_params']['std_dev']

            if len(df) < period:
                return features

            # 计算布林带
            ma = df['close'].rolling(window=period).mean()
            std = df['close'].rolling(window=period).std()

            upper_band = ma + (std * std_dev)
            lower_band = ma - (std * std_dev)

            current_price = df['close'].iloc[-1]
            current_upper = upper_band.iloc[-1]
            current_lower = lower_band.iloc[-1]
            current_ma = ma.iloc[-1]

            # 布林带位置特征
            if current_upper > current_lower:
                bb_position = (current_price - current_lower) / (current_upper - current_lower)
            else:
                bb_position = 0.5

            features.update({
                'bb_position': bb_position,
                'bb_above_upper': 1.0 if current_price > current_upper else 0.0,
                'bb_below_lower': 1.0 if current_price < current_lower else 0.0,
                'bb_near_upper': 1.0 if bb_position > 0.8 else 0.0,
                'bb_near_lower': 1.0 if bb_position < 0.2 else 0.0,
                'bb_width': (current_upper - current_lower) / current_ma if current_ma > 0 else 0
            })

            # 布林带收口/扩张
            if len(upper_band) >= 5:
                prev_width = (upper_band.iloc[-5] - lower_band.iloc[-5]) / ma.iloc[-5] if ma.iloc[-5] > 0 else 0
                curr_width = features['bb_width']

                features['bb_expanding'] = 1.0 if curr_width > prev_width * 1.1 else 0.0
                features['bb_contracting'] = 1.0 if curr_width < prev_width * 0.9 else 0.0

            return features

        except Exception as e:
            logger.error(f"布林带特征提取失败: {e}")
            return {}

    def _extract_volume_technical_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取成交量技术特征"""
        try:
            features = {}

            if len(df) < 10:
                return features

            # 成交量移动平均
            volume_ma5 = df['volume'].rolling(window=5).mean().iloc[-1] if len(df) >= 5 else df['volume'].iloc[-1]
            volume_ma10 = df['volume'].rolling(window=10).mean().iloc[-1] if len(df) >= 10 else df['volume'].iloc[-1]

            current_volume = df['volume'].iloc[-1]

            # 成交量放大特征
            features.update({
                'volume_ratio_ma5': current_volume / volume_ma5 if volume_ma5 > 0 else 1.0,
                'volume_ratio_ma10': current_volume / volume_ma10 if volume_ma10 > 0 else 1.0,
                'volume_significant_increase': 1.0 if current_volume > volume_ma5 * 2 else 0.0,
                'volume_above_ma5': 1.0 if current_volume > volume_ma5 else 0.0
            })

            # 量价配合特征
            if len(df) >= 5:
                price_changes = df['close'].pct_change().tail(5)
                volume_changes = df['volume'].pct_change().tail(5)

                # 去除NaN值
                valid_data = pd.DataFrame({'price': price_changes, 'volume': volume_changes}).dropna()

                if len(valid_data) >= 3:
                    correlation = valid_data['price'].corr(valid_data['volume'])
                    features['volume_price_correlation'] = correlation if not pd.isna(correlation) else 0
                    features['volume_price_positive_correlation'] = 1.0 if correlation > 0.3 else 0.0

            # 成交量模式特征
            if len(df) >= 10:
                volume_std = df['volume'].tail(10).std()
                volume_mean = df['volume'].tail(10).mean()

                if volume_mean > 0:
                    volume_cv = volume_std / volume_mean  # 变异系数
                    features['active_volume_pattern'] = 1.0 if volume_cv > 1.0 else 0.0
                    features['volume_volatility'] = volume_cv

            return features

        except Exception as e:
            logger.error(f"成交量技术特征提取失败: {e}")
            return {}

    def _extract_volatility_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取波动率特征"""
        try:
            features = {}

            if len(df) < 15:
                return features

            # 真实波动幅度ATR
            atr_period = self.indicator_params['atr_period']

            high_low = df['high'] - df['low']
            high_close_prev = abs(df['high'] - df['close'].shift(1))
            low_close_prev = abs(df['low'] - df['close'].shift(1))

            true_range = pd.concat([high_low, high_close_prev, low_close_prev], axis=1).max(axis=1)
            atr = true_range.rolling(window=atr_period).mean().iloc[-1]

            current_price = df['close'].iloc[-1]

            features.update({
                'atr': atr,
                'atr_ratio': atr / current_price if current_price > 0 else 0,
                'high_volatility': 1.0 if atr / current_price > 0.05 else 0.0
            })

            # 价格波动率
            if len(df) >= 10:
                price_returns = df['close'].pct_change().tail(10)
                price_volatility = price_returns.std()

                features.update({
                    'price_volatility': price_volatility,
                    'low_volatility': 1.0 if price_volatility < 0.02 else 0.0
                })

            return features

        except Exception as e:
            logger.error(f"波动率特征提取失败: {e}")
            return {}

    def _extract_trend_features(self, df: pd.DataFrame) -> Dict[str, float]:
        """提取趋势强度特征"""
        try:
            features = {}

            if len(df) < 10:
                return features

            # 连续上涨天数
            consecutive_up_days = 0
            for i in range(len(df) - 1, 0, -1):
                if df['close'].iloc[i] > df['close'].iloc[i-1]:
                    consecutive_up_days += 1
                else:
                    break

            features.update({
                'consecutive_up_days': consecutive_up_days,
                'has_consecutive_up': 1.0 if consecutive_up_days >= 2 else 0.0,
                'strong_consecutive_up': 1.0 if consecutive_up_days >= 3 else 0.0
            })

            # 趋势强度
            if len(df) >= 20:
                # 线性回归斜率
                x = np.arange(20)
                y = df['close'].tail(20).values
                slope = np.polyfit(x, y, 1)[0]

                features.update({
                    'trend_slope': slope / df['close'].iloc[-1] if df['close'].iloc[-1] > 0 else 0,
                    'uptrend_strong': 1.0 if slope > 0 and abs(slope) / df['close'].iloc[-1] > 0.01 else 0.0
                })

            # 新高新低特征
            if len(df) >= 20:
                recent_20_high = df['high'].tail(20).max()
                recent_20_low = df['low'].tail(20).min()
                current_high = df['high'].iloc[-1]
                current_low = df['low'].iloc[-1]

                features.update({
                    'new_20d_high': 1.0 if current_high >= recent_20_high else 0.0,
                    'new_20d_low': 1.0 if current_low <= recent_20_low else 0.0
                })

            return features

        except Exception as e:
            logger.error(f"趋势特征提取失败: {e}")
            return {}

    def _extract_minute_technical_features(self, minute_data: pd.DataFrame) -> Dict[str, float]:
        """提取分时技术特征"""
        try:
            features = {}

            if minute_data.empty:
                return features

            # 早盘成交量特征（前60分钟）
            if len(minute_data) >= 60:
                morning_volume = minute_data['volume'].head(60).sum()
                total_volume = minute_data['volume'].sum()

                if total_volume > 0:
                    morning_volume_ratio = morning_volume / total_volume
                    features['morning_volume_ratio'] = morning_volume_ratio
                    features['morning_volume_active'] = 1.0 if morning_volume_ratio > 0.35 else 0.0

            # 尾盘成交量特征（后30分钟）
            if len(minute_data) >= 30:
                tail_volume = minute_data['volume'].tail(30).sum()
                total_volume = minute_data['volume'].sum()

                if total_volume > 0:
                    tail_volume_ratio = tail_volume / total_volume
                    features['tail_volume_ratio'] = tail_volume_ratio
                    features['tail_volume_concentration'] = 1.0 if tail_volume_ratio > 0.3 else 0.0

            # 分时价格特征
            if len(minute_data) >= 10:
                minute_price_changes = minute_data['close'].pct_change().dropna()

                if len(minute_price_changes) > 0:
                    minute_volatility = minute_price_changes.std()
                    features['minute_price_volatility'] = minute_volatility
                    features['stable_minute_pattern'] = 1.0 if minute_volatility < 0.01 else 0.0

            # 分时量能特征
            if len(minute_data) >= 10:
                minute_volume_changes = minute_data['volume'].pct_change().dropna()

                if len(minute_volume_changes) > 0:
                    minute_volume_volatility = minute_volume_changes.std()
                    features['minute_volume_volatility'] = minute_volume_volatility
                    features['active_minute_volume_pattern'] = 1.0 if minute_volume_volatility > 1.0 else 0.0

            return features

        except Exception as e:
            logger.error(f"分时技术特征提取失败: {e}")
            return {}

    def _extract_core_limit_up_features(self, kline_data: pd.DataFrame, minute_data: pd.DataFrame = None) -> Dict[str, float]:
        """提取核心连板特征（基于25只连板股分析结果）"""
        try:
            features = {}

            # 1. 早盘成交量活跃（100%特征）
            if minute_data is not None and not minute_data.empty and len(minute_data) >= 60:
                morning_volume = minute_data['volume'].head(60).sum()
                total_volume = minute_data['volume'].sum()

                if total_volume > 0:
                    morning_ratio = morning_volume / total_volume
                    features['morning_volume_active'] = 1.0 if morning_ratio > 0.35 else 0.0
                else:
                    features['morning_volume_active'] = 0.0
            else:
                # 如果没有分时数据，使用日K线数据估算
                if len(kline_data) >= 5:
                    recent_volume = kline_data['volume'].tail(3).mean()
                    avg_volume = kline_data['volume'].tail(10).mean()
                    features['morning_volume_active'] = 1.0 if recent_volume > avg_volume * 1.2 else 0.0
                else:
                    features['morning_volume_active'] = 0.0

            # 2. 活跃成交量模式（100%特征）
            if len(kline_data) >= 10:
                volume_std = kline_data['volume'].tail(10).std()
                volume_mean = kline_data['volume'].tail(10).mean()

                if volume_mean > 0:
                    volume_cv = volume_std / volume_mean
                    features['active_volume_pattern'] = 1.0 if volume_cv > 1.0 else 0.0
                else:
                    features['active_volume_pattern'] = 0.0
            else:
                features['active_volume_pattern'] = 0.0

            # 3. 接近近期高点（64%特征）
            if len(kline_data) >= 20:
                recent_high = kline_data['high'].tail(20).max()
                current_price = kline_data['close'].iloc[-1]
                features['near_recent_high'] = 1.0 if current_price / recent_high > 0.95 else 0.0
            else:
                features['near_recent_high'] = 0.0

            # 4. 稳定价格模式（56%特征）
            if len(kline_data) >= 10:
                recent_closes = kline_data['close'].tail(10)
                price_std = recent_closes.std()
                price_mean = recent_closes.mean()

                if price_mean > 0:
                    price_cv = price_std / price_mean
                    features['stable_price_pattern'] = 1.0 if price_cv < 0.03 else 0.0
                else:
                    features['stable_price_pattern'] = 0.0
            else:
                features['stable_price_pattern'] = 0.0

            # 5. 量价正相关（48%特征）
            if len(kline_data) >= 5:
                price_changes = kline_data['close'].pct_change().tail(5).dropna()
                volume_changes = kline_data['volume'].pct_change().tail(5).dropna()

                if len(price_changes) >= 3 and len(volume_changes) >= 3:
                    correlation = price_changes.corr(volume_changes)
                    features['volume_price_positive_correlation'] = 1.0 if correlation > 0.3 else 0.0
                else:
                    features['volume_price_positive_correlation'] = 0.0
            else:
                features['volume_price_positive_correlation'] = 0.0

            # 6. 价格突破（44%特征）
            if len(kline_data) >= 10:
                recent_high = kline_data['high'].tail(10).max()
                current_price = kline_data['close'].iloc[-1]
                features['price_breakthrough'] = 1.0 if current_price >= recent_high * 0.99 else 0.0
            else:
                features['price_breakthrough'] = 0.0

            # 计算核心特征综合评分
            core_score = 0.0
            for feature_name, weight in self.core_feature_weights.items():
                if feature_name in features:
                    core_score += features[feature_name] * weight

            features['core_limit_up_score'] = core_score
            features['high_limit_up_potential'] = 1.0 if core_score > 0.6 else 0.0

            return features

        except Exception as e:
            logger.error(f"核心连板特征提取失败: {e}")
            return {}

    def _get_empty_features(self) -> Dict[str, Any]:
        """获取空的特征字典"""
        return {
            'feature_extraction_success': False,
            'total_features': 0,
            'error': '特征提取失败'
        }

def main():
    """测试技术指标特征工程器"""
    print("📈 测试技术指标特征工程器...")

    # 创建测试数据
    dates = pd.date_range('2024-06-01', periods=30, freq='D')
    np.random.seed(42)

    test_kline = pd.DataFrame({
        'date': dates,
        'open': 10 + np.random.randn(30) * 0.5,
        'high': 10.5 + np.random.randn(30) * 0.5,
        'low': 9.5 + np.random.randn(30) * 0.5,
        'close': 10 + np.random.randn(30) * 0.5,
        'volume': 1000000 + np.random.randn(30) * 200000
    })

    # 确保价格逻辑正确
    test_kline['high'] = test_kline[['open', 'close', 'high']].max(axis=1)
    test_kline['low'] = test_kline[['open', 'close', 'low']].min(axis=1)

    engine = TechnicalIndicatorsFeatureEngine()

    # 提取特征
    features = engine.extract_all_technical_features(test_kline)

    print(f"\n✅ 特征提取完成")
    print(f"总特征数: {features.get('total_features', 0)}")
    print(f"提取成功: {features.get('feature_extraction_success', False)}")

    # 显示核心连板特征
    print(f"\n🎯 核心连板特征:")
    core_features = ['morning_volume_active', 'active_volume_pattern', 'near_recent_high',
                    'stable_price_pattern', 'volume_price_positive_correlation', 'price_breakthrough']

    for feature in core_features:
        if feature in features:
            print(f"  {feature}: {features[feature]}")

    if 'core_limit_up_score' in features:
        print(f"\n📊 核心连板评分: {features['core_limit_up_score']:.3f}")
        print(f"高连板潜力: {'是' if features.get('high_limit_up_potential', 0) else '否'}")

if __name__ == "__main__":
    main()
