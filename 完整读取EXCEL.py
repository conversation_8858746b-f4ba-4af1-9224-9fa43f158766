#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整读取EXCEL应用 - 高效预处理版
专门用于将Excel文件预处理为CSV文件，支持AI高效分析
作者：AI助手
版本：2.1 - 数据格式保护版

更新说明：
- 修复股票代码格式问题：使用dtype=str确保002424等代码不会变成2424
- 添加CSV引用保护：使用quoting=1确保所有字段都被正确引用
- 保持原始数据格式：避免pandas自动类型转换导致的数据丢失
"""

import pandas as pd
import openpyxl
import sys
import os
from datetime import datetime
import json
import shutil

class ExcelPreprocessor:
    """Excel预处理器 - 将Excel文件转换为CSV文件集合"""

    def __init__(self):
        self.current_file = None
        self.sheet_names = []
        self.total_sheets = 0
        self.stock_code = None
        self.temp_folder = None
        self.csv_files = {}  # 存储生成的CSV文件路径

        # 工作表→分析阶段映射
        self.sheet_mapping = {}
        self.phase_sheets = {
            'PHASE1': [],  # 游资历史追踪
            'PHASE2': [],  # 技术面分析
            'PHASE3': [],  # 资金流向分析
            'PHASE4': [],  # 筹码分布分析
            'PHASE5': [],  # 分时数据分析
            'PHASE6': []   # 综合研判
        }

    def preprocess_excel_to_csv(self, stock_code):
        """主要功能：将Excel文件预处理为CSV文件集合"""
        self.stock_code = stock_code
        excel_file = f"{stock_code}.xlsx"

        print(f"🚀 Excel预处理器 v2.1 - 数据格式保护版")
        print("=" * 60)
        print(f"📊 目标股票: {stock_code}")
        print(f"📁 源文件: {excel_file}")

        # 步骤1：检查Excel文件
        if not self._check_excel_file(excel_file):
            return False

        # 步骤2：创建临时文件夹
        if not self._create_temp_folder():
            return False

        # 步骤3：提取所有工作表为CSV
        if not self._extract_all_sheets_to_csv():
            return False

        # 步骤4：建立阶段映射
        self._build_phase_mapping()

        # 步骤5：显示处理结果
        self._show_processing_results()

        print(f"\n✅ 预处理完成！AI可直接读取CSV文件进行高效分析")
        return True

    def _check_excel_file(self, excel_file):
        """检查Excel文件是否存在"""
        try:
            if not os.path.exists(excel_file):
                print(f"❌ 文件不存在: {excel_file}")
                return False

            self.current_file = excel_file

            # 获取文件基本信息
            file_size = os.path.getsize(excel_file)
            file_size_mb = file_size / (1024 * 1024)

            print(f"📁 Excel文件检查:")
            print(f"  文件路径: {excel_file}")
            print(f"  文件大小: {file_size_mb:.2f} MB")
            print(f"  检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

            # 获取所有工作表名称
            workbook = openpyxl.load_workbook(excel_file, read_only=True)
            self.sheet_names = workbook.sheetnames
            self.total_sheets = len(self.sheet_names)
            workbook.close()

            print(f"  工作表总数: {self.total_sheets}")
            print(f"  ✅ 文件检查通过")

            return True

        except Exception as e:
            print(f"❌ 检查Excel文件失败: {e}")
            return False

    def _create_temp_folder(self):
        """创建临时文件夹"""
        try:
            self.temp_folder = f"${self.stock_code}"

            # 如果文件夹已存在，先删除
            if os.path.exists(self.temp_folder):
                print(f"🗑️ 删除已存在的临时文件夹: {self.temp_folder}")
                shutil.rmtree(self.temp_folder)

            # 创建新文件夹
            os.makedirs(self.temp_folder)
            print(f"📁 创建临时文件夹: {self.temp_folder}")

            return True

        except Exception as e:
            print(f"❌ 创建临时文件夹失败: {e}")
            return False
    
    def _extract_all_sheets_to_csv(self):
        """提取所有工作表为CSV文件"""
        try:
            print(f"\n📊 开始提取工作表数据...")
            print("=" * 60)

            success_count = 0

            for i, sheet_name in enumerate(self.sheet_names, 1):
                try:
                    print(f"� [{i}/{self.total_sheets}] 处理工作表: {sheet_name}")

                    # 读取工作表数据（保持原始格式，避免数据类型自动转换）
                    df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)

                    # 生成CSV文件名（清理特殊字符）
                    safe_sheet_name = sheet_name.replace('/', '-').replace('\\', '-').replace(':', '-')
                    csv_filename = f"{safe_sheet_name}.csv"
                    csv_path = os.path.join(self.temp_folder, csv_filename)

                    # 保存为CSV（确保无损转换，保持原始格式）
                    df.to_csv(csv_path, index=False, encoding='utf-8-sig', quoting=1)

                    # 记录文件信息
                    self.csv_files[sheet_name] = {
                        'csv_path': csv_path,
                        'csv_filename': csv_filename,
                        'rows': len(df),
                        'columns': len(df.columns)
                    }

                    print(f"  ✅ 已保存: {csv_filename} ({len(df)}行 × {len(df.columns)}列)")
                    success_count += 1

                except Exception as e:
                    print(f"  ❌ 处理失败: {e}")
                    continue

            print(f"\n📊 提取完成: {success_count}/{self.total_sheets} 个工作表成功转换")
            return success_count > 0

        except Exception as e:
            print(f"❌ 提取工作表失败: {e}")
            return False

    def _build_phase_mapping(self):
        """自动建立工作表到分析阶段的映射"""
        # 定义关键词映射规则
        phase_keywords = {
            'PHASE1': ['龙虎榜', '游资', '席位'],
            'PHASE2': ['K线', '技术分析', '价格统计', '技术指标', '实时行情'],
            'PHASE3': ['资金流向', '主力', '大单', '超大单'],
            'PHASE4': ['筹码分布', '成本', '持仓'],
            'PHASE5': ['分时', '逐笔', '成交明细', '时间周期'],
            'PHASE6': ['板块联动', '市场情绪', '开盘特征', '概念板块', '数据质量', '五档行情']
        }

        # 清空之前的映射
        self.sheet_mapping = {}
        for phase in self.phase_sheets:
            self.phase_sheets[phase] = []

        # 为每个工作表分配阶段
        for sheet_name in self.sheet_names:
            assigned = False
            for phase, keywords in phase_keywords.items():
                if any(keyword in sheet_name for keyword in keywords):
                    self.sheet_mapping[sheet_name] = phase
                    self.phase_sheets[phase].append(sheet_name)
                    assigned = True
                    break

            # 未匹配的工作表分配到PHASE6
            if not assigned:
                self.sheet_mapping[sheet_name] = 'PHASE6'
                self.phase_sheets['PHASE6'].append(sheet_name)

    def _show_processing_results(self):
        """显示处理结果"""
        print(f"\n📋 CSV文件生成结果:")
        print("=" * 60)

        # 显示基本信息
        print(f"📁 临时文件夹: {self.temp_folder}")
        print(f"📊 成功转换: {len(self.csv_files)}/{self.total_sheets} 个工作表")

        # 显示阶段分组
        print(f"\n📊 阶段分组映射:")
        print("=" * 60)
        for phase, sheets in self.phase_sheets.items():
            if sheets:
                print(f"{phase} ({len(sheets)}个CSV文件):")
                for sheet in sheets:
                    csv_info = self.csv_files.get(sheet, {})
                    csv_filename = csv_info.get('csv_filename', 'unknown.csv')
                    rows = csv_info.get('rows', 0)
                    cols = csv_info.get('columns', 0)
                    print(f"  - {csv_filename} ({rows}行×{cols}列)")
                print()

        # 显示AI使用指南
        print(f"\n🤖 AI分析指南:")
        print("=" * 60)
        print(f"现在可以使用以下命令直接读取CSV文件:")
        print(f"view {self.temp_folder}/基本信息.csv")
        print(f"view {self.temp_folder}/龙虎榜-当日.csv")
        print(f"view {self.temp_folder}/K线数据.csv")
        print(f"... 等等")
        print(f"\n每个CSV文件都包含完整的原始数据，可直接进行分析！")

    # 以下方法保留用于兼容性，但主要功能已转移到预处理器
    def read_batch(self, batch_number: int):
        """读取指定批次的工作表"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return
        
        if batch_number < 1:
            print("❌ 批次号必须从1开始")
            return
        
        # 计算批次范围
        start_idx = (batch_number - 1) * self.batch_size
        end_idx = min(start_idx + self.batch_size, self.total_sheets)
        
        if start_idx >= self.total_sheets:
            print(f"❌ 批次{batch_number}超出范围，总共只有{(self.total_sheets + self.batch_size - 1) // self.batch_size}个批次")
            return
        
        batch_sheets = self.sheet_names[start_idx:end_idx]
        
        print(f"\n📊 读取批次 {batch_number} (工作表 {start_idx+1}-{end_idx}):")
        print("=" * 60)
        
        for i, sheet_name in enumerate(batch_sheets):
            try:
                print(f"\n🔍 工作表 {start_idx + i + 1}: {sheet_name}")
                print("-" * 40)
                
                # 读取工作表数据（保持原始格式）
                df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)
                
                # 显示基本信息
                print(f"  📏 数据维度: {df.shape[0]}行 × {df.shape[1]}列")
                
                if not df.empty:
                    # 显示列名
                    print(f"  📋 列名: {list(df.columns)}")

                    # 完整输出模式（无限制）
                    print(f"  📄 完整数据 (共{len(df)}行):")

                    for idx, row in df.iterrows():
                        print(f"    行{idx+1}: {dict(row)}")

                    # 数据类型信息
                    print(f"  🔢 数据类型:")
                    for col, dtype in df.dtypes.items():
                        print(f"    {col}: {dtype}")

                    # 缺失值统计
                    null_counts = df.isnull().sum()
                    if null_counts.sum() > 0:
                        print(f"  ⚠️ 缺失值:")
                        for col, count in null_counts.items():
                            if count > 0:
                                print(f"    {col}: {count}个缺失值")
                    else:
                        print(f"  ✅ 无缺失值")
                else:
                    print(f"  ⚠️ 工作表为空")
                
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")
        
        print(f"\n✅ 批次 {batch_number} 读取完成")
        print(f"📝 提示: 使用 read_batch({batch_number + 1}) 读取下一批次")
    
    def read_specific_sheets(self, sheet_names_or_numbers):
        """读取指定的工作表"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return
        
        if isinstance(sheet_names_or_numbers, (int, str)):
            sheet_names_or_numbers = [sheet_names_or_numbers]
        
        print(f"\n📊 读取指定工作表:")
        print("=" * 60)
        
        for item in sheet_names_or_numbers:
            try:
                if isinstance(item, int):
                    # 按序号读取
                    if 1 <= item <= self.total_sheets:
                        sheet_name = self.sheet_names[item - 1]
                        print(f"\n🔍 工作表 {item}: {sheet_name}")
                    else:
                        print(f"❌ 工作表序号{item}超出范围(1-{self.total_sheets})")
                        continue
                else:
                    # 按名称读取
                    sheet_name = item
                    if sheet_name not in self.sheet_names:
                        print(f"❌ 工作表'{sheet_name}'不存在")
                        continue
                    print(f"\n🔍 工作表: {sheet_name}")
                
                print("-" * 40)
                
                # 读取数据（保持原始格式）
                df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)
                
                # 显示详细信息（与read_batch相同的格式）
                print(f"  📏 数据维度: {df.shape[0]}行 × {df.shape[1]}列")
                
                if not df.empty:
                    print(f"  📋 列名: {list(df.columns)}")

                    # 完整输出模式（无限制）
                    print(f"  📄 完整数据 (共{len(df)}行):")

                    for idx, row in df.iterrows():
                        print(f"    行{idx+1}: {dict(row)}")

                    print(f"  🔢 数据类型:")
                    for col, dtype in df.dtypes.items():
                        print(f"    {col}: {dtype}")

                    null_counts = df.isnull().sum()
                    if null_counts.sum() > 0:
                        print(f"  ⚠️ 缺失值:")
                        for col, count in null_counts.items():
                            if count > 0:
                                print(f"    {col}: {count}个缺失值")
                    else:
                        print(f"  ✅ 无缺失值")
                else:
                    print(f"  ⚠️ 工作表为空")
                
            except Exception as e:
                print(f"  ❌ 读取失败: {e}")

    def read_sheet_complete(self, sheet_name):
        """完整读取单个工作表的所有数据，逐行输出（无限制）"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return None

        # 检查缓存
        if sheet_name in self.data_cache:
            print(f"📦 从缓存读取工作表: {sheet_name}")
            return self.data_cache[sheet_name]

        try:
            print(f"\n🔥 完整读取工作表: {sheet_name}")
            print("=" * 60)

            # 读取数据（保持原始格式）
            df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)

            if df.empty:
                print("⚠️ 工作表为空")
                self.data_cache[sheet_name] = df
                return df

            # 显示基本信息
            print(f"📏 数据维度: {df.shape[0]}行 × {df.shape[1]}列")
            print(f"📋 列名: {list(df.columns)}")
            print(f"📄 逐行数据 (共{len(df)}行):")
            print("-" * 60)

            # 逐行输出所有数据
            for idx, row in df.iterrows():
                print(f"第{idx+1}行数据:")
                for col, value in row.items():
                    print(f"  {col}: {value}")
                print("-" * 40)

            # 数据类型和统计信息
            print(f"\n🔢 数据类型:")
            for col, dtype in df.dtypes.items():
                print(f"  {col}: {dtype}")

            # 缺失值统计
            null_counts = df.isnull().sum()
            if null_counts.sum() > 0:
                print(f"\n⚠️ 缺失值统计:")
                for col, count in null_counts.items():
                    if count > 0:
                        print(f"  {col}: {count}个缺失值")
            else:
                print(f"\n✅ 无缺失值")

            # 缓存数据
            self.data_cache[sheet_name] = df
            return df

        except Exception as e:
            print(f"❌ 读取失败: {e}")
            return None

    def read_phase_sheets(self, phase_name, compact_mode=False):
        """读取指定阶段的所有工作表"""
        if phase_name not in self.phase_sheets:
            print(f"❌ 无效的阶段名称: {phase_name}")
            print(f"   有效阶段: {list(self.phase_sheets.keys())}")
            return {}

        sheets_to_read = self.phase_sheets[phase_name]
        if not sheets_to_read:
            print(f"⚠️ {phase_name} 没有分配的工作表")
            return {}

        print(f"\n🎯 读取 {phase_name} 阶段工作表 (共{len(sheets_to_read)}个):")
        print("=" * 60)

        phase_data = {}
        for i, sheet_name in enumerate(sheets_to_read, 1):
            print(f"\n📊 [{i}/{len(sheets_to_read)}] 读取工作表: {sheet_name}")
            print("-" * 40)

            if compact_mode:
                # 紧凑模式：只显示基本信息和关键数据
                df = self._read_sheet_compact(sheet_name)
            else:
                # 完整模式：显示所有数据
                df = self.read_sheet_complete(sheet_name)

            if df is not None:
                phase_data[sheet_name] = df

        print(f"\n✅ {phase_name} 阶段读取完成，共获取 {len(phase_data)} 个工作表数据")
        return phase_data

    def _read_sheet_compact(self, sheet_name):
        """紧凑模式读取工作表（只显示关键信息）"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return None

        # 检查缓存
        if sheet_name in self.data_cache:
            df = self.data_cache[sheet_name]
            print(f"📦 从缓存获取: {sheet_name} ({df.shape[0]}行 × {df.shape[1]}列)")
            return df

        try:
            # 读取数据（保持原始格式）
            df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)

            if df.empty:
                print("⚠️ 工作表为空")
                self.data_cache[sheet_name] = df
                return df

            # 显示基本信息
            print(f"📏 数据维度: {df.shape[0]}行 × {df.shape[1]}列")
            print(f"📋 列名: {list(df.columns)}")

            # 只显示前3行和后3行数据
            if len(df) > 6:
                print(f"📄 数据预览 (前3行 + 后3行):")
                print("前3行:")
                for idx in range(3):
                    row = df.iloc[idx]
                    print(f"  第{idx+1}行: {dict(row)}")
                print("  ...")
                print("后3行:")
                for idx in range(len(df)-3, len(df)):
                    row = df.iloc[idx]
                    print(f"  第{idx+1}行: {dict(row)}")
            else:
                print(f"📄 完整数据 (共{len(df)}行):")
                for idx, row in df.iterrows():
                    print(f"  第{idx+1}行: {dict(row)}")

            # 缓存数据
            self.data_cache[sheet_name] = df
            return df

        except Exception as e:
            print(f"❌ 读取失败: {e}")
            return None

    def get_phase_summary(self, phase_name):
        """获取指定阶段的工作表摘要"""
        if phase_name not in self.phase_sheets:
            print(f"❌ 无效的阶段名称: {phase_name}")
            return

        sheets = self.phase_sheets[phase_name]
        print(f"\n📋 {phase_name} 阶段摘要:")
        print("=" * 50)
        print(f"工作表数量: {len(sheets)}")

        if sheets:
            print("包含工作表:")
            for i, sheet in enumerate(sheets, 1):
                print(f"  {i}. {sheet}")
        else:
            print("⚠️ 该阶段暂无分配的工作表")

    def search_sheets(self, keyword: str):
        """搜索包含关键词的工作表"""
        if not self.sheet_names:
            print("❌ 请先加载Excel文件")
            return
        
        matching_sheets = []
        for i, sheet_name in enumerate(self.sheet_names, 1):
            if keyword.lower() in sheet_name.lower():
                matching_sheets.append((i, sheet_name))
        
        if matching_sheets:
            print(f"\n🔍 搜索结果 (关键词: '{keyword}'):")
            print("=" * 50)
            for num, name in matching_sheets:
                batch_num = (num - 1) // self.batch_size + 1
                print(f"  {num:2d}. {name} (批次{batch_num})")
        else:
            print(f"❌ 未找到包含'{keyword}'的工作表")
    
    def get_summary(self):
        """获取Excel文件摘要"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return
        
        print(f"\n📊 Excel文件摘要:")
        print("=" * 50)
        print(f"文件: {os.path.basename(self.current_file)}")
        print(f"工作表总数: {self.total_sheets}")
        print(f"批次总数: {(self.total_sheets + self.batch_size - 1) // self.batch_size}")
        
        # 按类别分组显示工作表
        categories = {
            '基础数据': ['数据概览', '基本信息', '实时行情', 'K线数据'],
            '技术分析': ['技术分析', '价格统计', '技术指标'],
            '龙虎榜': ['龙虎榜'],
            '资金流向': ['资金流向'],
            '筹码分布': ['筹码分布'],
            '板块联动': ['板块联动', '概念板块'],
            '市场情绪': ['市场情绪'],
            '开盘特征': ['开盘特征'],
            '分时数据': ['分时数据'],
            '其他': ['时间周期', '数据质量', '五档行情']
        }
        
        for category, keywords in categories.items():
            matching = []
            for sheet in self.sheet_names:
                if any(keyword in sheet for keyword in keywords):
                    matching.append(sheet)
            if matching:
                print(f"\n{category} ({len(matching)}个):")
                for sheet in matching:
                    print(f"  - {sheet}")


    def export_sheet_data(self, sheet_name: str, output_format='json'):
        """导出指定工作表数据为JSON或CSV格式"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return

        try:
            df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)

            if output_format.lower() == 'json':
                # 导出为JSON
                output_file = f"{sheet_name}_data.json"
                data = df.to_dict('records')
                with open(output_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2, default=str)
                print(f"✅ 数据已导出到: {output_file}")

            elif output_format.lower() == 'csv':
                # 导出为CSV（保持原始格式）
                output_file = f"{sheet_name}_data.csv"
                df.to_csv(output_file, index=False, encoding='utf-8-sig', quoting=1)
                print(f"✅ 数据已导出到: {output_file}")

            else:
                print(f"❌ 不支持的格式: {output_format}")

        except Exception as e:
            print(f"❌ 导出失败: {e}")

    def analyze_data_patterns(self, sheet_name: str):
        """分析数据模式和特征"""
        if not self.current_file:
            print("❌ 请先加载Excel文件")
            return

        try:
            df = pd.read_excel(self.current_file, sheet_name=sheet_name, dtype=str)

            print(f"\n📈 数据模式分析: {sheet_name}")
            print("=" * 50)

            if df.empty:
                print("⚠️ 工作表为空")
                return

            # 数值列统计
            numeric_cols = df.select_dtypes(include=['number']).columns
            if len(numeric_cols) > 0:
                print(f"🔢 数值列统计:")
                for col in numeric_cols:
                    stats = df[col].describe()
                    print(f"  {col}:")
                    print(f"    均值: {stats['mean']:.2f}")
                    print(f"    中位数: {stats['50%']:.2f}")
                    print(f"    标准差: {stats['std']:.2f}")
                    print(f"    最小值: {stats['min']:.2f}")
                    print(f"    最大值: {stats['max']:.2f}")

            # 文本列分析
            text_cols = df.select_dtypes(include=['object']).columns
            if len(text_cols) > 0:
                print(f"\n📝 文本列分析:")
                for col in text_cols:
                    unique_count = df[col].nunique()
                    print(f"  {col}: {unique_count}个唯一值")
                    if unique_count <= 10:
                        print(f"    值: {list(df[col].unique())}")

            # 日期列分析
            date_cols = df.select_dtypes(include=['datetime']).columns
            if len(date_cols) > 0:
                print(f"\n📅 日期列分析:")
                for col in date_cols:
                    print(f"  {col}:")
                    print(f"    最早: {df[col].min()}")
                    print(f"    最晚: {df[col].max()}")
                    print(f"    跨度: {(df[col].max() - df[col].min()).days}天")

        except Exception as e:
            print(f"❌ 分析失败: {e}")

    def find_latest_file(self, pattern='*.xlsx'):
        """查找最新的Excel文件"""
        import glob

        files = glob.glob(pattern)
        # 排除临时文件（以~$开头的文件）
        files = [f for f in files if not os.path.basename(f).startswith('~$')]

        if not files:
            print(f"❌ 未找到匹配的文件: {pattern}")
            return None

        # 按修改时间排序
        latest_file = max(files, key=os.path.getmtime)
        mod_time = datetime.fromtimestamp(os.path.getmtime(latest_file))

        print(f"📁 找到最新文件:")
        print(f"  文件: {latest_file}")
        print(f"  修改时间: {mod_time.strftime('%Y-%m-%d %H:%M:%S')}")

        return latest_file


def main():
    """主函数 - Excel预处理器"""
    preprocessor = ExcelPreprocessor()

    print("🚀 Excel预处理器 v2.1 - 数据格式保护版")
    print("=" * 60)
    print("📖 使用方法:")
    print("python 完整读取EXCEL.py <股票代码>")
    print("例如: python 完整读取EXCEL.py 000065")
    print()
    print("🎯 功能说明:")
    print("1. 自动读取 <股票代码>.xlsx 文件")
    print("2. 创建临时文件夹 $<股票代码>")
    print("3. 将所有工作表转换为CSV文件")
    print("4. 建立阶段分析映射关系")
    print("5. 为AI分析做好准备")
    print()
    print("📊 阶段说明:")
    print("   PHASE1: 游资历史追踪 (龙虎榜相关)")
    print("   PHASE2: 技术面分析 (K线、技术指标)")
    print("   PHASE3: 资金流向分析 (主力资金)")
    print("   PHASE4: 筹码分布分析 (成本分析)")
    print("   PHASE5: 分时数据分析 (操作手法)")
    print("   PHASE6: 综合研判 (其他数据)")

    # 检查命令行参数
    if len(sys.argv) > 1:
        stock_code = sys.argv[1]
        # 如果包含.xlsx扩展名，去掉它
        if stock_code.endswith('.xlsx'):
            stock_code = stock_code[:-5]

        print(f"\n🎯 开始处理股票: {stock_code}")
        success = preprocessor.preprocess_excel_to_csv(stock_code)

        if success:
            print(f"\n🎉 预处理成功完成！")
            print(f"📁 CSV文件已保存到: ${stock_code}/")
            print(f"🤖 AI现在可以直接读取CSV文件进行高效分析")
        else:
            print(f"\n❌ 预处理失败，请检查文件和参数")

    else:
        print(f"\n⚠️ 请提供股票代码参数")
        print(f"使用方法: python 完整读取EXCEL.py 000065")

    return preprocessor

if __name__ == "__main__":
    preprocessor = main()
