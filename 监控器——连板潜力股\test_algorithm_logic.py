#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试算法逻辑修正
验证两种算法类型的正确切换
"""

import sys
import os
from datetime import datetime
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_algorithm_switching():
    """测试算法切换逻辑"""
    print("🚀 测试算法切换逻辑")
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        from bought_stocks_manager import bought_stocks_manager
        
        # 测试股票
        test_code = "600230"
        test_name = "沧州大化"
        buy_price = 14.43
        
        print("📊 算法类型说明:")
        print("   1️⃣ 普通股票分析算法 - 用于未买入的股票")
        print("   2️⃣ 已买入股票算法 - 用于已买入的股票（买入当天停止分析）")
        
        # 场景1：没有已买入股票
        print(f"\n🔍 场景1：没有已买入股票")
        bought_stocks = bought_stocks_manager.get_all_bought_stocks()
        print(f"   已买入股票数量: {len(bought_stocks)}")
        print(f"   预期行为: 所有股票使用 '普通股票分析算法'")
        
        # 模拟股票数据
        stock_data = {
            'stock_code': test_code,
            'stock_name': test_name,
            'current_price': 14.50,
            'change_pct': 2.5,
            'volume_ratio': 1.2,
            'prev_close': 14.15,
            'high': 14.80,
            'low': 14.20,
            'volume': 1000000,
            'amount': 14500000
        }
        
        # 检查算法选择逻辑
        is_bought = bought_stocks_manager.is_bought_stock(test_code)
        print(f"   {test_code} 是否已买入: {is_bought}")
        print(f"   应该使用的算法: {'已买入股票算法' if is_bought else '普通股票分析算法'}")
        
        # 场景2：添加已买入股票
        print(f"\n🔍 场景2：添加已买入股票")
        success = bought_stocks_manager.add_bought_stock(test_code, test_name, buy_price)
        print(f"   添加结果: {'成功' if success else '失败'}")
        
        if success:
            # 检查买入当天的处理
            stock_info = bought_stocks_manager.get_stock_info(test_code)
            buy_date = stock_info.get('buy_date', '')
            current_date = datetime.now().strftime('%Y-%m-%d')
            
            print(f"   买入日期: {buy_date}")
            print(f"   当前日期: {current_date}")
            print(f"   是否买入当天: {buy_date == current_date}")
            
            if buy_date == current_date:
                print(f"   预期行为: 买入当天停止分析")
            else:
                print(f"   预期行为: 使用 '已买入股票算法'")
            
            # 检查算法选择
            is_bought = bought_stocks_manager.is_bought_stock(test_code)
            print(f"   {test_code} 是否已买入: {is_bought}")
            print(f"   应该使用的算法: {'已买入股票算法' if is_bought else '普通股票分析算法'}")
        
        # 场景3：其他普通股票
        print(f"\n🔍 场景3：其他普通股票")
        other_code = "000001"
        other_name = "平安银行"
        
        is_bought_other = bought_stocks_manager.is_bought_stock(other_code)
        print(f"   {other_code} 是否已买入: {is_bought_other}")
        print(f"   应该使用的算法: {'已买入股票算法' if is_bought_other else '普通股票分析算法'}")
        
        print(f"\n✅ 算法切换逻辑测试完成")
        
        # 清理测试数据
        if success:
            bought_stocks_manager.remove_bought_stock(test_code)
            print(f"🧹 已清理测试数据")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def test_algorithm_differences():
    """测试两种算法的区别"""
    print(f"\n" + "=" * 60)
    print("🔍 测试两种算法的区别")
    print("=" * 60)
    
    print("📊 普通股票分析算法特点:")
    print("   🎯 目标: 分析股票是否值得买入")
    print("   📈 输出: 主力意图、行为评分、买入建议")
    print("   💡 建议类型: 建议买入、谨慎买入、不建议买入、观察等")
    print("   🔍 分析重点: 买入时机、风险评估、市场环境")
    
    print(f"\n📊 已买入股票算法特点:")
    print("   🎯 目标: 决定是否继续持有或卖出")
    print("   📈 输出: 持有/卖出决策、止损/止盈建议")
    print("   💡 建议类型: 坚定持有、减仓观察、立即止损等")
    print("   🔍 分析重点: 洗盘vs出货、持有天数、盈亏状况")
    print("   ⚠️ 特殊规则: 买入当天停止分析")
    
    print(f"\n🎯 关键区别:")
    print("   🔸 普通股票: 关注买入机会，评估投资价值")
    print("   🔸 已买入股票: 关注卖出时机，保护投资收益")
    print("   🔸 普通股票: 基于当前状态分析")
    print("   🔸 已买入股票: 基于持有历史和记忆系统分析")

def test_current_logic():
    """测试当前修正后的逻辑"""
    print(f"\n" + "=" * 60)
    print("🚀 测试修正后的逻辑")
    print("=" * 60)
    
    print("✅ 修正后的逻辑:")
    print("   📊 9:30开盘后，系统获取所有监控股票")
    print("   🔄 对每只股票进行算法选择:")
    print("      - 如果是已买入股票 → 使用 '已买入股票算法'")
    print("      - 如果不是已买入股票 → 使用 '普通股票分析算法'")
    print("   ❌ 不再有所谓的 '选股模式'")
    print("   ✅ 只有两种明确的分析算法")
    
    print(f"\n🎯 解决的问题:")
    print("   ❌ 之前: 没有已买入股票时，所有股票进入'选股模式'")
    print("   ✅ 现在: 没有已买入股票时，所有股票使用'普通股票分析算法'")
    print("   ❌ 之前: 算法类型混乱，有三种模式")
    print("   ✅ 现在: 算法类型清晰，只有两种算法")
    
    print(f"\n💡 实际运行效果:")
    print("   🔸 没有已买入股票: 所有股票显示分析结果和买入建议")
    print("   🔸 有已买入股票: 已买入的显示持有决策，其他显示买入建议")
    print("   🔸 买入当天: 该股票停止分析")
    print("   🔸 买入第二天开始: 该股票使用专门的卖出监控算法")

def main():
    """主测试函数"""
    try:
        # 测试算法切换逻辑
        test_algorithm_switching()
        
        # 测试算法区别
        test_algorithm_differences()
        
        # 测试修正后的逻辑
        test_current_logic()
        
        print(f"\n" + "=" * 60)
        print("🎉 算法逻辑修正完成")
        print("=" * 60)
        
        print("✅ 修正总结:")
        print("   🎯 明确了两种算法类型")
        print("   🎯 消除了混乱的'选股模式'")
        print("   🎯 确保了算法切换的正确性")
        print("   🎯 解决了9:30开盘后的算法选择问题")
        
        print(f"\n🚀 现在系统逻辑清晰:")
        print("   1️⃣ 普通股票 → 普通股票分析算法 → 买入建议")
        print("   2️⃣ 已买入股票 → 已买入股票算法 → 持有/卖出决策")
        print("   3️⃣ 买入当天 → 停止分析")
        
    except Exception as e:
        print(f"❌ 测试过程中出现错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
