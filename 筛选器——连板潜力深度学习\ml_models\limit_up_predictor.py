#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板潜力股预测模型
基于280+个特征的机器学习模型，预测股票连板潜力
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import joblib
import os
import warnings
warnings.filterwarnings('ignore')

# 机器学习库
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.metrics import classification_report, confusion_matrix, roc_auc_score, precision_recall_curve

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 可选的机器学习库
try:
    import xgboost as xgb
    XGBOOST_AVAILABLE = True
except ImportError:
    XGBOOST_AVAILABLE = False
    print("⚠️ XGBoost不可用，将跳过XGBoost模型")

try:
    import lightgbm as lgb
    LIGHTGBM_AVAILABLE = True
except ImportError:
    LIGHTGBM_AVAILABLE = False
    print("⚠️ LightGBM不可用，将跳过LightGBM模型")

class LimitUpPredictor:
    """连板潜力股预测器"""
    
    def __init__(self, model_save_dir: str = "trained_models"):
        self.model_save_dir = model_save_dir
        os.makedirs(model_save_dir, exist_ok=True)
        
        # 模型配置
        self.models_config = {
            'random_forest': {
                'model': RandomForestClassifier,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 15,
                    'min_samples_split': 5,
                    'min_samples_leaf': 2,
                    'random_state': 42,
                    'n_jobs': -1
                },
                'grid_params': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [10, 15, 20],
                    'min_samples_split': [2, 5, 10]
                }
            },
            'gradient_boosting': {
                'model': GradientBoostingClassifier,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'random_state': 42
                },
                'grid_params': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [6, 8, 10],
                    'learning_rate': [0.05, 0.1, 0.15]
                }
            },
            'logistic_regression': {
                'model': LogisticRegression,
                'params': {
                    'random_state': 42,
                    'max_iter': 1000
                },
                'grid_params': {
                    'C': [0.1, 1.0, 10.0],
                    'penalty': ['l1', 'l2']
                }
            }
        }

        # 添加可选模型
        if XGBOOST_AVAILABLE:
            self.models_config['xgboost'] = {
                'model': xgb.XGBClassifier,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'random_state': 42,
                    'eval_metric': 'logloss'
                },
                'grid_params': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [6, 8, 10],
                    'learning_rate': [0.05, 0.1, 0.15]
                }
            }

        if LIGHTGBM_AVAILABLE:
            self.models_config['lightgbm'] = {
                'model': lgb.LGBMClassifier,
                'params': {
                    'n_estimators': 200,
                    'max_depth': 8,
                    'learning_rate': 0.1,
                    'subsample': 0.8,
                    'colsample_bytree': 0.8,
                    'random_state': 42,
                    'verbose': -1
                },
                'grid_params': {
                    'n_estimators': [100, 200, 300],
                    'max_depth': [6, 8, 10],
                    'learning_rate': [0.05, 0.1, 0.15]
                }
            }
        
        # 特征重要性配置
        self.feature_importance_config = {
            'core_features': [
                'morning_volume_active', 'active_volume_pattern', 'near_recent_high',
                'stable_price_pattern', 'volume_price_positive_correlation', 'price_breakthrough'
            ],
            'technical_features': [
                'ma5_above_ma10', 'macd_golden_cross', 'rsi_bullish_zone', 'kdj_golden_cross',
                'bb_near_upper', 'volume_significant_increase'
            ],
            'capital_features': [
                'main_inflow_positive', 'institutional_inflow_positive', 'large_inflow_positive'
            ],
            'youzi_features': [
                'famous_seats_count', 'top_tier_seats_count', 'operation_consistency'
            ]
        }
        
        # 训练好的模型
        self.trained_models = {}
        self.scaler = None
        self.feature_names = None
        
        logger.info("🤖 连板潜力股预测器初始化完成")
    
    def prepare_training_data(self, features_data: List[Dict[str, Any]], 
                            labels: List[int]) -> Tuple[pd.DataFrame, np.ndarray]:
        """准备训练数据"""
        try:
            logger.info(f"🔄 准备训练数据，样本数: {len(features_data)}")
            
            if len(features_data) != len(labels):
                raise ValueError("特征数据和标签数量不匹配")
            
            # 转换为DataFrame
            features_df = pd.DataFrame(features_data)
            
            # 处理缺失值
            features_df = self._handle_missing_values(features_df)
            
            # 特征选择和工程
            features_df = self._feature_engineering(features_df)
            
            # 保存特征名称
            self.feature_names = features_df.columns.tolist()
            
            logger.info(f"✅ 训练数据准备完成，特征数: {len(self.feature_names)}")
            return features_df, np.array(labels)
            
        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            raise
    
    def _handle_missing_values(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理缺失值"""
        try:
            # 数值型特征用中位数填充
            numeric_columns = df.select_dtypes(include=[np.number]).columns
            for col in numeric_columns:
                if df[col].isnull().sum() > 0:
                    median_value = df[col].median()
                    df[col].fillna(median_value, inplace=True)
            
            # 布尔型特征用False填充
            boolean_columns = df.select_dtypes(include=[bool]).columns
            for col in boolean_columns:
                df[col].fillna(False, inplace=True)
            
            # 删除全为NaN的列
            df.dropna(axis=1, how='all', inplace=True)
            
            return df
            
        except Exception as e:
            logger.error(f"处理缺失值失败: {e}")
            return df
    
    def _feature_engineering(self, df: pd.DataFrame) -> pd.DataFrame:
        """特征工程"""
        try:
            # 创建组合特征
            if 'morning_volume_active' in df.columns and 'active_volume_pattern' in df.columns:
                df['core_volume_signal'] = df['morning_volume_active'] * df['active_volume_pattern']
            
            if 'near_recent_high' in df.columns and 'price_breakthrough' in df.columns:
                df['price_momentum_signal'] = df['near_recent_high'] * df['price_breakthrough']
            
            # 技术指标组合
            technical_cols = [col for col in df.columns if any(tech in col for tech in ['ma', 'macd', 'rsi', 'kdj'])]
            if technical_cols:
                df['technical_signal_count'] = df[technical_cols].sum(axis=1)
            
            # 资金流向组合
            capital_cols = [col for col in df.columns if 'inflow' in col and 'positive' in col]
            if capital_cols:
                df['capital_signal_count'] = df[capital_cols].sum(axis=1)
            
            # 游资信号组合
            youzi_cols = [col for col in df.columns if any(youzi in col for youzi in ['famous', 'seats', 'consistency'])]
            if youzi_cols:
                df['youzi_signal_strength'] = df[youzi_cols].mean(axis=1)
            
            return df
            
        except Exception as e:
            logger.error(f"特征工程失败: {e}")
            return df
    
    def train_models(self, X: pd.DataFrame, y: np.ndarray, 
                    test_size: float = 0.2, use_grid_search: bool = False) -> Dict[str, Any]:
        """训练多个模型"""
        try:
            logger.info(f"🚀 开始训练模型，样本数: {len(X)}, 特征数: {len(X.columns)}")
            
            # 数据标准化
            self.scaler = StandardScaler()
            X_scaled = self.scaler.fit_transform(X)
            X_scaled = pd.DataFrame(X_scaled, columns=X.columns)
            
            # 分割训练集和测试集
            X_train, X_test, y_train, y_test = train_test_split(
                X_scaled, y, test_size=test_size, random_state=42, stratify=y
            )
            
            logger.info(f"训练集: {len(X_train)}, 测试集: {len(X_test)}")
            logger.info(f"正样本比例: {y.mean():.3f}")
            
            results = {}
            
            # 训练每个模型
            for model_name, config in self.models_config.items():
                try:
                    logger.info(f"🔄 训练{model_name}模型...")
                    
                    if use_grid_search:
                        # 网格搜索最优参数
                        model = self._train_with_grid_search(
                            config, X_train, y_train, model_name
                        )
                    else:
                        # 使用默认参数
                        model = config['model'](**config['params'])
                        model.fit(X_train, y_train)
                    
                    # 预测和评估
                    train_pred = model.predict(X_train)
                    test_pred = model.predict(X_test)
                    train_proba = model.predict_proba(X_train)[:, 1] if hasattr(model, 'predict_proba') else None
                    test_proba = model.predict_proba(X_test)[:, 1] if hasattr(model, 'predict_proba') else None
                    
                    # 计算评估指标
                    model_results = self._evaluate_model(
                        model, model_name, 
                        X_train, y_train, train_pred, train_proba,
                        X_test, y_test, test_pred, test_proba
                    )
                    
                    results[model_name] = model_results
                    self.trained_models[model_name] = model
                    
                    logger.info(f"✅ {model_name}训练完成，测试AUC: {model_results['test_auc']:.3f}")
                    
                except Exception as e:
                    logger.error(f"训练{model_name}失败: {e}")
                    continue
            
            # 保存模型
            self._save_models()
            
            logger.info(f"🎉 模型训练完成，共训练{len(results)}个模型")
            return results
            
        except Exception as e:
            logger.error(f"训练模型失败: {e}")
            return {}
    
    def _train_with_grid_search(self, config: Dict, X_train: pd.DataFrame, 
                              y_train: np.ndarray, model_name: str):
        """使用网格搜索训练模型"""
        try:
            logger.info(f"🔍 {model_name}网格搜索中...")
            
            model = config['model'](**config['params'])
            grid_search = GridSearchCV(
                model, config['grid_params'], 
                cv=5, scoring='roc_auc', n_jobs=-1, verbose=0
            )
            
            grid_search.fit(X_train, y_train)
            
            logger.info(f"✅ {model_name}最优参数: {grid_search.best_params_}")
            logger.info(f"✅ {model_name}最优CV分数: {grid_search.best_score_:.3f}")
            
            return grid_search.best_estimator_
            
        except Exception as e:
            logger.error(f"{model_name}网格搜索失败: {e}")
            # 返回默认参数模型
            model = config['model'](**config['params'])
            model.fit(X_train, y_train)
            return model

    def _evaluate_model(self, model, model_name: str,
                       X_train: pd.DataFrame, y_train: np.ndarray, train_pred: np.ndarray, train_proba: Optional[np.ndarray],
                       X_test: pd.DataFrame, y_test: np.ndarray, test_pred: np.ndarray, test_proba: Optional[np.ndarray]) -> Dict[str, Any]:
        """评估模型性能"""
        try:
            from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score

            results = {
                'model_name': model_name,
                'train_accuracy': accuracy_score(y_train, train_pred),
                'test_accuracy': accuracy_score(y_test, test_pred),
                'train_precision': precision_score(y_train, train_pred, zero_division=0),
                'test_precision': precision_score(y_test, test_pred, zero_division=0),
                'train_recall': recall_score(y_train, train_pred, zero_division=0),
                'test_recall': recall_score(y_test, test_pred, zero_division=0),
                'train_f1': f1_score(y_train, train_pred, zero_division=0),
                'test_f1': f1_score(y_test, test_pred, zero_division=0)
            }

            # AUC分数
            if train_proba is not None:
                results['train_auc'] = roc_auc_score(y_train, train_proba)
            if test_proba is not None:
                results['test_auc'] = roc_auc_score(y_test, test_proba)

            # 特征重要性
            if hasattr(model, 'feature_importances_'):
                feature_importance = pd.DataFrame({
                    'feature': X_train.columns,
                    'importance': model.feature_importances_
                }).sort_values('importance', ascending=False)

                results['feature_importance'] = feature_importance.head(20).to_dict('records')

            return results

        except Exception as e:
            logger.error(f"评估模型{model_name}失败: {e}")
            return {'model_name': model_name, 'error': str(e)}

    def predict_limit_up_probability(self, features: Dict[str, Any],
                                   model_names: Optional[List[str]] = None) -> Dict[str, Any]:
        """预测连板概率"""
        try:
            if not self.trained_models:
                logger.error("没有训练好的模型")
                return {'error': '没有训练好的模型'}

            # 准备特征数据
            features_df = pd.DataFrame([features])
            features_df = self._handle_missing_values(features_df)

            # 确保特征顺序一致
            if self.feature_names:
                missing_features = set(self.feature_names) - set(features_df.columns)
                for feature in missing_features:
                    features_df[feature] = 0  # 缺失特征用0填充

                features_df = features_df[self.feature_names]

            # 标准化
            if self.scaler:
                features_scaled = self.scaler.transform(features_df)
                features_df = pd.DataFrame(features_scaled, columns=features_df.columns)

            # 使用指定模型或所有模型进行预测
            if model_names is None:
                model_names = list(self.trained_models.keys())

            predictions = {}
            probabilities = {}

            for model_name in model_names:
                if model_name in self.trained_models:
                    model = self.trained_models[model_name]

                    # 预测
                    pred = model.predict(features_df)[0]
                    predictions[model_name] = int(pred)

                    # 预测概率
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(features_df)[0, 1]
                        probabilities[model_name] = float(proba)

            # 集成预测
            if probabilities:
                ensemble_probability = np.mean(list(probabilities.values()))
                ensemble_prediction = 1 if ensemble_probability > 0.5 else 0
            else:
                ensemble_probability = np.mean(list(predictions.values()))
                ensemble_prediction = 1 if ensemble_probability > 0.5 else 0

            return {
                'ensemble_prediction': ensemble_prediction,
                'ensemble_probability': ensemble_probability,
                'individual_predictions': predictions,
                'individual_probabilities': probabilities,
                'prediction_confidence': self._calculate_prediction_confidence(probabilities)
            }

        except Exception as e:
            logger.error(f"预测连板概率失败: {e}")
            return {'error': str(e)}

    def _calculate_prediction_confidence(self, probabilities: Dict[str, float]) -> str:
        """计算预测置信度"""
        try:
            if not probabilities:
                return 'unknown'

            proba_values = list(probabilities.values())
            mean_proba = np.mean(proba_values)
            std_proba = np.std(proba_values)

            # 基于概率值和一致性判断置信度
            if mean_proba > 0.8 and std_proba < 0.1:
                return 'very_high'
            elif mean_proba > 0.7 and std_proba < 0.15:
                return 'high'
            elif mean_proba > 0.6 and std_proba < 0.2:
                return 'medium'
            elif mean_proba > 0.5:
                return 'low'
            else:
                return 'very_low'

        except Exception as e:
            logger.error(f"计算预测置信度失败: {e}")
            return 'unknown'

    def get_feature_importance_analysis(self, top_n: int = 20) -> Dict[str, Any]:
        """获取特征重要性分析"""
        try:
            if not self.trained_models:
                return {'error': '没有训练好的模型'}

            all_importances = []

            for model_name, model in self.trained_models.items():
                if hasattr(model, 'feature_importances_') and self.feature_names:
                    importance_df = pd.DataFrame({
                        'feature': self.feature_names,
                        'importance': model.feature_importances_,
                        'model': model_name
                    })
                    all_importances.append(importance_df)

            if not all_importances:
                return {'error': '没有可用的特征重要性信息'}

            # 合并所有模型的特征重要性
            combined_importance = pd.concat(all_importances, ignore_index=True)

            # 计算平均重要性
            avg_importance = combined_importance.groupby('feature')['importance'].mean().reset_index()
            avg_importance = avg_importance.sort_values('importance', ascending=False)

            # 分析核心特征的重要性
            core_features_importance = []
            for feature_group, features in self.feature_importance_config.items():
                group_importance = avg_importance[avg_importance['feature'].isin(features)]
                if not group_importance.empty:
                    core_features_importance.append({
                        'feature_group': feature_group,
                        'avg_importance': group_importance['importance'].mean(),
                        'top_features': group_importance.head(5).to_dict('records')
                    })

            return {
                'top_features': avg_importance.head(top_n).to_dict('records'),
                'core_features_analysis': core_features_importance,
                'total_features': len(avg_importance),
                'models_analyzed': len(self.trained_models)
            }

        except Exception as e:
            logger.error(f"特征重要性分析失败: {e}")
            return {'error': str(e)}

    def _save_models(self):
        """保存训练好的模型"""
        try:
            # 保存模型
            for model_name, model in self.trained_models.items():
                model_file = os.path.join(self.model_save_dir, f"{model_name}_model.joblib")
                joblib.dump(model, model_file)

            # 保存标准化器
            if self.scaler:
                scaler_file = os.path.join(self.model_save_dir, "scaler.joblib")
                joblib.dump(self.scaler, scaler_file)

            # 保存特征名称
            if self.feature_names:
                features_file = os.path.join(self.model_save_dir, "feature_names.joblib")
                joblib.dump(self.feature_names, features_file)

            logger.info(f"✅ 模型保存完成: {self.model_save_dir}")

        except Exception as e:
            logger.error(f"保存模型失败: {e}")

    def load_models(self):
        """加载训练好的模型"""
        try:
            # 加载模型
            for model_name in self.models_config.keys():
                model_file = os.path.join(self.model_save_dir, f"{model_name}_model.joblib")
                if os.path.exists(model_file):
                    self.trained_models[model_name] = joblib.load(model_file)

            # 加载标准化器
            scaler_file = os.path.join(self.model_save_dir, "scaler.joblib")
            if os.path.exists(scaler_file):
                self.scaler = joblib.load(scaler_file)

            # 加载特征名称
            features_file = os.path.join(self.model_save_dir, "feature_names.joblib")
            if os.path.exists(features_file):
                self.feature_names = joblib.load(features_file)

            logger.info(f"✅ 模型加载完成，共加载{len(self.trained_models)}个模型")

        except Exception as e:
            logger.error(f"加载模型失败: {e}")

def main():
    """测试连板潜力股预测器"""
    print("🤖 测试连板潜力股预测器...")

    predictor = LimitUpPredictor()

    # 创建模拟训练数据
    print("\n🔄 创建模拟训练数据...")
    np.random.seed(42)

    # 模拟100个样本的特征数据
    n_samples = 100
    features_data = []
    labels = []

    for i in range(n_samples):
        # 模拟特征
        features = {
            'morning_volume_active': np.random.choice([0, 1], p=[0.7, 0.3]),
            'active_volume_pattern': np.random.choice([0, 1], p=[0.6, 0.4]),
            'near_recent_high': np.random.choice([0, 1], p=[0.5, 0.5]),
            'stable_price_pattern': np.random.choice([0, 1], p=[0.6, 0.4]),
            'volume_price_positive_correlation': np.random.choice([0, 1], p=[0.7, 0.3]),
            'price_breakthrough': np.random.choice([0, 1], p=[0.8, 0.2]),
            'ma5_above_ma10': np.random.choice([0, 1], p=[0.6, 0.4]),
            'macd_golden_cross': np.random.choice([0, 1], p=[0.8, 0.2]),
            'rsi_bullish_zone': np.random.choice([0, 1], p=[0.7, 0.3]),
            'main_inflow_positive': np.random.choice([0, 1], p=[0.6, 0.4]),
            'famous_seats_count': np.random.randint(0, 5),
            'technical_signal_score': np.random.uniform(0, 1),
            'capital_flow_score': np.random.uniform(0, 1),
            'chip_concentration': np.random.uniform(0, 1)
        }

        # 基于特征生成标签（模拟连板概率）
        core_score = (features['morning_volume_active'] + features['active_volume_pattern'] +
                     features['near_recent_high']) / 3

        # 连板概率与核心特征相关
        limit_up_prob = core_score * 0.6 + np.random.uniform(0, 0.4)
        label = 1 if limit_up_prob > 0.5 else 0

        features_data.append(features)
        labels.append(label)

    print(f"✅ 创建了{len(features_data)}个样本，正样本比例: {np.mean(labels):.2%}")

    # 准备训练数据
    print("\n🔄 准备训练数据...")
    X, y = predictor.prepare_training_data(features_data, labels)
    print(f"特征矩阵形状: {X.shape}")

    # 训练模型
    print("\n🚀 开始训练模型...")
    results = predictor.train_models(X, y, test_size=0.3)

    # 显示训练结果
    print("\n📊 训练结果:")
    for model_name, result in results.items():
        if 'error' not in result:
            print(f"{model_name}:")
            print(f"  测试准确率: {result['test_accuracy']:.3f}")
            print(f"  测试AUC: {result.get('test_auc', 'N/A')}")
            print(f"  测试F1: {result['test_f1']:.3f}")

    # 测试预测
    print("\n🔮 测试预测功能...")
    test_features = {
        'morning_volume_active': 1,
        'active_volume_pattern': 1,
        'near_recent_high': 1,
        'stable_price_pattern': 1,
        'volume_price_positive_correlation': 1,
        'price_breakthrough': 1,
        'ma5_above_ma10': 1,
        'macd_golden_cross': 1,
        'rsi_bullish_zone': 1,
        'main_inflow_positive': 1,
        'famous_seats_count': 3,
        'technical_signal_score': 0.8,
        'capital_flow_score': 0.7,
        'chip_concentration': 0.6
    }

    prediction = predictor.predict_limit_up_probability(test_features)
    print(f"预测结果: {prediction['ensemble_prediction']}")
    print(f"预测概率: {prediction['ensemble_probability']:.3f}")
    print(f"置信度: {prediction['prediction_confidence']}")

if __name__ == "__main__":
    main()
