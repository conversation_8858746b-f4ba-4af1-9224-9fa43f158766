#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示增强版通知系统
展示Windows Toast通知样式的效果
"""

import sys
import os
from datetime import datetime
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_enhanced_notifications():
    """演示增强版通知系统"""
    print("🚀 增强版通知系统演示")
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    try:
        from enhanced_notification_system import enhanced_notification_system
        
        # 显示系统状态
        status = enhanced_notification_system.get_notification_status()
        print("📊 通知系统状态:")
        print(f"   📱 Toast通知可用: {'是' if status['toast_available'] else '否'}")
        print(f"   📱 Toast通知开启: {'是' if status['toast_enabled'] else '否'}")
        print(f"   💬 弹窗通知开启: {'是' if status['popup_enabled'] else '否'}")
        print(f"   🔊 声音通知开启: {'是' if status['sound_enabled'] else '否'}")
        
        if status['toast_available']:
            print(f"\n✅ 将使用Windows Toast通知样式")
        else:
            print(f"\n⚠️ Toast不可用，将使用弹窗通知")
            print(f"   💡 安装Toast支持: pip install win10toast")
        
        # 演示不同类型的通知
        notifications = [
            {
                'type': 'stop_loss',
                'title': '止损警报演示',
                'func': enhanced_notification_system.send_stop_loss_alert,
                'args': ("600230", "沧州大化", 13.20, -8.5, "跌破主力成本区"),
                'desc': '🚨 止损警报 - 出现在Windows通知区域，红色警告样式'
            },
            {
                'type': 'take_profit',
                'title': '止盈提醒演示', 
                'func': enhanced_notification_system.send_take_profit_alert,
                'args': ("600230", "沧州大化", 16.80, 15.2, "分批止盈"),
                'desc': '🎉 止盈提醒 - 绿色成功样式，建议获利了结'
            },
            {
                'type': 'critical_decision',
                'title': '关键决策演示',
                'func': enhanced_notification_system.send_critical_decision_alert,
                'args': ("600230", "沧州大化", 13.50, -6.4, "坚定持有", "第3天洗盘确认", 3),
                'desc': '🎯 关键决策 - 第3天关键洗盘日，橙色提醒样式'
            },
            {
                'type': 'main_force_escape',
                'title': '主力出逃演示',
                'func': enhanced_notification_system.send_main_force_escape_alert,
                'args': ("600230", "沧州大化", 12.80, -11.3, ["量能枯竭", "钓鱼线形态", "连续缩量"]),
                'desc': '🚨 主力出逃 - 紧急红色警报，连续声音提醒'
            },
            {
                'type': 'wash_trading',
                'title': '洗盘确认演示',
                'func': enhanced_notification_system.send_wash_trading_alert,
                'args': ("600230", "沧州大化", 13.50, -6.4, 0.85, ["缩量精准调整", "量比健康", "第3天关键洗盘日"]),
                'desc': '🌊 洗盘确认 - 蓝色信息样式，坚定持有提醒'
            }
        ]
        
        print(f"\n📢 开始演示通知（每个通知间隔5秒）:")
        print(f"💡 请注意观察Windows系统右下角的通知区域")
        
        for i, notif in enumerate(notifications, 1):
            print(f"\n{i}. {notif['title']}")
            print(f"   {notif['desc']}")
            print(f"   ⏳ 发送中...")
            
            try:
                success = notif['func'](*notif['args'])
                if success:
                    print(f"   ✅ 发送成功")
                else:
                    print(f"   ❌ 发送失败")
            except Exception as e:
                print(f"   ❌ 发送异常: {e}")
            
            if i < len(notifications):
                print(f"   ⏰ 等待5秒后发送下一个通知...")
                time.sleep(5)
        
        # 显示通知历史
        print(f"\n📋 通知历史记录:")
        history = enhanced_notification_system.get_notification_history(hours=1)
        print(f"   最近1小时通知数量: {len(history)}")
        
        for i, notif in enumerate(history[-5:], 1):  # 显示最近5条
            timestamp = notif['timestamp'].strftime('%H:%M:%S')
            stock_name = notif['stock_name']
            alert_type = notif['alert_type']
            profit_pct = notif['profit_pct']
            print(f"   {i}. {timestamp} | {stock_name} | {alert_type} | {profit_pct:+.1f}%")
        
        print(f"\n🎯 Windows Toast通知特点:")
        print(f"   ✅ 出现在Windows系统通知区域（右下角）")
        print(f"   ✅ 非阻塞式，不影响程序运行")
        print(f"   ✅ 自动消失（15秒后）")
        print(f"   ✅ 可以点击查看详情")
        print(f"   ✅ 支持不同的图标和样式")
        print(f"   ✅ 专业的股票软件风格")
        
        print(f"\n💡 与普通弹窗的区别:")
        print(f"   🔸 普通弹窗: 阻塞式，需要点击确定，样式简陋")
        print(f"   🔸 Toast通知: 非阻塞式，自动消失，系统原生样式")
        print(f"   🔸 Toast通知: 更符合现代软件的用户体验")
        
        # 清理演示数据
        enhanced_notification_system.clear_notification_history()
        print(f"\n🧹 演示数据已清理")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def demo_notification_settings():
    """演示通知设置功能"""
    print(f"\n" + "=" * 60)
    print("⚙️ 通知设置功能演示")
    print("=" * 60)
    
    try:
        from enhanced_notification_system import enhanced_notification_system
        
        print("📊 可配置的通知选项:")
        
        # 演示声音开关
        print(f"\n🔊 声音通知控制:")
        enhanced_notification_system.set_sound_enabled(False)
        print(f"   关闭声音后发送测试通知...")
        enhanced_notification_system.send_critical_decision_alert(
            "TEST001", "测试股票", 10.0, 5.0, "测试决策", "声音已关闭", 2
        )
        time.sleep(2)
        enhanced_notification_system.set_sound_enabled(True)
        print(f"   重新开启声音")
        
        # 演示Toast开关
        print(f"\n📱 Toast通知控制:")
        status = enhanced_notification_system.get_notification_status()
        if status['toast_available']:
            enhanced_notification_system.set_toast_enabled(False)
            print(f"   关闭Toast，使用弹窗通知...")
            enhanced_notification_system.send_critical_decision_alert(
                "TEST002", "测试股票", 10.0, 5.0, "测试决策", "使用弹窗样式", 2
            )
            time.sleep(2)
            enhanced_notification_system.set_toast_enabled(True)
            print(f"   重新开启Toast通知")
        else:
            print(f"   Toast通知不可用，跳过演示")
        
        print(f"\n✅ 通知设置演示完成")
        
    except Exception as e:
        print(f"❌ 设置演示失败: {e}")

def main():
    """主演示函数"""
    try:
        # 演示增强版通知系统
        demo_enhanced_notifications()
        
        # 演示通知设置
        demo_notification_settings()
        
        print(f"\n" + "=" * 60)
        print("🎉 增强版通知系统演示完成")
        print("=" * 60)
        
        print(f"📊 总结:")
        print(f"   ✅ Windows Toast通知样式更专业")
        print(f"   ✅ 非阻塞式，用户体验更好")
        print(f"   ✅ 支持多种通知类型和样式")
        print(f"   ✅ 可配置的通知选项")
        print(f"   ✅ 完整的通知历史记录")
        
        print(f"\n💡 建议:")
        print(f"   🎯 在主应用中使用增强版通知系统")
        print(f"   🎯 替换原有的简单弹窗通知")
        print(f"   🎯 提供更专业的股票监控体验")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

if __name__ == "__main__":
    main()
