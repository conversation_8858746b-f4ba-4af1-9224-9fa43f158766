#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的历史数据测试脚本
专门测试涨停前历史数据获取能力
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_akshare_historical_data():
    """测试AKShare历史数据获取"""
    print("🔍 测试AKShare历史数据获取能力...")
    
    try:
        import akshare as ak
    except ImportError:
        print("❌ AKShare未安装，请运行: pip install akshare")
        return
    
    # 测试股票
    test_stock = "000001"  # 平安银行
    
    # 测试时间点：3个月前的某一天
    target_date = datetime.now() - timedelta(days=90)
    start_date = target_date - timedelta(days=7)  # 目标日期前一周
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 目标日期: {target_date.strftime('%Y-%m-%d')}")
    print(f"📅 数据范围: {start_date.strftime('%Y-%m-%d')} 到 {target_date.strftime('%Y-%m-%d')}")
    
    # 1. 测试日K线数据
    print("\n1️⃣ 测试日K线数据...")
    try:
        kline_data = ak.stock_zh_a_hist(
            symbol=test_stock,
            period="daily",
            start_date=start_date.strftime('%Y%m%d'),
            end_date=target_date.strftime('%Y%m%d'),
            adjust=""
        )
        
        if not kline_data.empty:
            print(f"✅ 日K线数据获取成功: {len(kline_data)}条记录")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   时间范围: {kline_data.index[0]} 到 {kline_data.index[-1]}")
            print(f"   样本数据:\n{kline_data.head(2)}")
        else:
            print("❌ 日K线数据为空")
            
    except Exception as e:
        print(f"❌ 日K线数据获取失败: {e}")
    
    time.sleep(2)
    
    # 2. 测试分时数据
    print("\n2️⃣ 测试分时数据...")
    try:
        minute_data = ak.stock_zh_a_minute(
            symbol=test_stock,
            period='1',
            adjust=''
        )
        
        if not minute_data.empty:
            print(f"✅ 分时数据获取成功: {len(minute_data)}条记录")
            print(f"   数据列: {list(minute_data.columns)}")
            print(f"   最新时间: {minute_data.index[-1]}")
            print(f"   ⚠️ 注意: AKShare分时数据通常只提供最近几天的数据")
        else:
            print("❌ 分时数据为空")
            
    except Exception as e:
        print(f"❌ 分时数据获取失败: {e}")
    
    time.sleep(2)
    
    # 3. 测试资金流向数据
    print("\n3️⃣ 测试资金流向数据...")
    try:
        capital_flow = ak.stock_individual_fund_flow(
            stock=test_stock,
            market="sz"
        )
        
        if not capital_flow.empty:
            print(f"✅ 资金流向数据获取成功: {len(capital_flow)}条记录")
            print(f"   数据列: {list(capital_flow.columns)}")
            print(f"   时间范围: {capital_flow.index[0]} 到 {capital_flow.index[-1]}")
            
            # 检查是否包含目标日期附近的数据
            target_date_str = target_date.strftime('%Y-%m-%d')
            if target_date_str in capital_flow.index:
                print(f"✅ 包含目标日期 {target_date_str} 的数据")
            else:
                print(f"⚠️ 不包含目标日期 {target_date_str} 的数据")
                
        else:
            print("❌ 资金流向数据为空")
            
    except Exception as e:
        print(f"❌ 资金流向数据获取失败: {e}")

def test_efinance_historical_data():
    """测试efinance历史数据获取"""
    print("\n🔍 测试efinance历史数据获取能力...")
    
    try:
        import efinance as ef
    except ImportError:
        print("❌ efinance未安装，请运行: pip install efinance")
        return
    
    # 测试股票
    test_stock = "000001"  # 平安银行
    
    # 测试时间点：3个月前的某一天
    target_date = datetime.now() - timedelta(days=90)
    start_date = target_date - timedelta(days=7)  # 目标日期前一周
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 目标日期: {target_date.strftime('%Y-%m-%d')}")
    print(f"📅 数据范围: {start_date.strftime('%Y-%m-%d')} 到 {target_date.strftime('%Y-%m-%d')}")
    
    # 1. 测试日K线数据
    print("\n1️⃣ 测试日K线数据...")
    try:
        kline_data = ef.stock.get_quote_history(
            stock_codes=test_stock,
            beg=start_date.strftime('%Y%m%d'),
            end=target_date.strftime('%Y%m%d')
        )
        
        if not kline_data.empty:
            print(f"✅ 日K线数据获取成功: {len(kline_data)}条记录")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   样本数据:\n{kline_data.head(2)}")
        else:
            print("❌ 日K线数据为空")
            
    except Exception as e:
        print(f"❌ 日K线数据获取失败: {e}")
    
    time.sleep(2)
    
    # 2. 测试分时数据
    print("\n2️⃣ 测试分时数据...")
    try:
        minute_data = ef.stock.get_quote_history(
            stock_codes=test_stock,
            klt=1,  # 1分钟K线
            beg=target_date.strftime('%Y%m%d'),
            end=target_date.strftime('%Y%m%d')
        )
        
        if not minute_data.empty:
            print(f"✅ 分时数据获取成功: {len(minute_data)}条记录")
            print(f"   数据列: {list(minute_data.columns)}")
            print(f"   样本数据:\n{minute_data.head(2)}")
        else:
            print("❌ 分时数据为空")
            
    except Exception as e:
        print(f"❌ 分时数据获取失败: {e}")

def test_data_availability_summary():
    """测试数据可用性总结"""
    print("\n" + "="*60)
    print("📋 数据可用性总结")
    print("="*60)
    
    print("基于测试结果，以下是各类历史数据的获取能力评估：")
    print()
    
    print("📈 日K线数据:")
    print("   ✅ AKShare: 支持获取较长时间的历史日K线数据")
    print("   ✅ efinance: 支持获取历史日K线数据")
    print("   💡 建议: 日K线数据获取相对容易，两个数据源都可用")
    print()
    
    print("📊 分时数据:")
    print("   ⚠️ AKShare: 分时数据通常只提供最近几天")
    print("   ❓ efinance: 需要测试具体的历史分时数据获取能力")
    print("   💡 建议: 分时历史数据可能是最大挑战，需要进一步验证")
    print()
    
    print("💰 资金流向数据:")
    print("   ✅ AKShare: 提供个股资金流向历史数据")
    print("   ❓ efinance: 需要查看是否有相应接口")
    print("   💡 建议: AKShare的资金流向数据相对可靠")
    print()
    
    print("🎯 筹码分布数据:")
    print("   ❌ AKShare: 不直接提供筹码分布数据")
    print("   ❌ efinance: 需要查看是否有相应接口")
    print("   💡 建议: 筹码分布数据可能需要其他数据源或替代方案")
    print()
    
    print("🔥 关键建议:")
    print("   1. 日K线数据: 使用AKShare或efinance都可以")
    print("   2. 分时数据: 这是最大挑战，可能需要付费数据源")
    print("   3. 资金流向: 优先使用AKShare")
    print("   4. 筹码分布: 考虑使用成交量分布等替代指标")
    print("   5. 建议先用日K线数据进行初步训练和验证")

def main():
    """主函数"""
    print("🧪 简化历史数据测试开始...")
    print("="*60)
    
    # 测试AKShare
    test_akshare_historical_data()
    
    # 测试efinance
    test_efinance_historical_data()
    
    # 总结
    test_data_availability_summary()
    
    print("\n🎉 测试完成！")
    print("💡 建议: 根据测试结果，优先使用日K线数据开始训练")

if __name__ == "__main__":
    main()
