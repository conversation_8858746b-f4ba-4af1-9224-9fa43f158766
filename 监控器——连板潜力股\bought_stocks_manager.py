#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已买入股票管理器
负责已买入股票的数据存储、状态管理和持仓监控
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BoughtStocksManager:
    """已买入股票管理器"""
    
    def __init__(self, data_dir: str = "bought_stocks_data"):
        """
        初始化已买入股票管理器
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 数据文件路径
        self.bought_stocks_file = self.data_dir / "bought_stocks.json"
        self.trading_records_file = self.data_dir / "trading_records.json"
        
        # 内存数据
        self.bought_stocks = {}  # {code: stock_info}
        self.trading_records = []  # 交易记录列表
        
        # 加载现有数据
        self.load_data()
        
        logger.info(f"✅ 已买入股票管理器初始化完成，数据目录: {self.data_dir}")
    
    def add_bought_stock(self, code: str, name: str, buy_price: Optional[float] = None) -> bool:
        """
        添加已买入股票
        
        Args:
            code: 股票代码
            name: 股票名称
            buy_price: 买入价格（可选）
            
        Returns:
            bool: 是否添加成功
        """
        try:
            current_time = datetime.now()
            
            # 检查是否已存在
            if code in self.bought_stocks:
                logger.warning(f"股票 {code} 已在已买入列表中")
                return False
            
            # 创建股票信息
            stock_info = {
                'code': code,
                'name': name,
                'buy_date': current_time.strftime('%Y-%m-%d'),
                'buy_time': current_time.strftime('%H:%M:%S'),
                'buy_datetime': current_time.isoformat(),
                'buy_price': buy_price,
                'status': 'holding',  # holding/sold
                'hold_days': 1,
                'created_at': current_time.isoformat(),
                'updated_at': current_time.isoformat(),
                'sell_date': None,
                'sell_time': None,
                'sell_price': None,
                'profit_loss': None,
                'profit_loss_pct': None,
                'notes': ''
            }
            
            # 添加到内存
            self.bought_stocks[code] = stock_info
            
            # 添加交易记录
            trading_record = {
                'id': len(self.trading_records) + 1,
                'code': code,
                'name': name,
                'action': 'buy',
                'price': buy_price,
                'date': current_time.strftime('%Y-%m-%d'),
                'time': current_time.strftime('%H:%M:%S'),
                'datetime': current_time.isoformat(),
                'notes': f'通过监控系统标记买入'
            }
            
            self.trading_records.append(trading_record)
            
            # 保存到文件
            self.save_data()
            
            logger.info(f"✅ 添加已买入股票: {code} {name}")
            return True
            
        except Exception as e:
            logger.error(f"添加已买入股票失败 {code}: {e}")
            return False
    
    def mark_as_sold(self, code: str, sell_price: Optional[float] = None, notes: str = '') -> bool:
        """
        标记股票为已卖出
        
        Args:
            code: 股票代码
            sell_price: 卖出价格（可选）
            notes: 备注
            
        Returns:
            bool: 是否标记成功
        """
        try:
            if code not in self.bought_stocks:
                logger.warning(f"股票 {code} 不在已买入列表中")
                return False
            
            current_time = datetime.now()
            stock_info = self.bought_stocks[code]
            
            # 更新股票状态
            stock_info['status'] = 'sold'
            stock_info['sell_date'] = current_time.strftime('%Y-%m-%d')
            stock_info['sell_time'] = current_time.strftime('%H:%M:%S')
            stock_info['sell_price'] = sell_price
            stock_info['updated_at'] = current_time.isoformat()
            stock_info['notes'] = notes
            
            # 计算盈亏
            if stock_info['buy_price'] and sell_price:
                profit_loss = sell_price - stock_info['buy_price']
                profit_loss_pct = (profit_loss / stock_info['buy_price']) * 100
                stock_info['profit_loss'] = profit_loss
                stock_info['profit_loss_pct'] = profit_loss_pct
            
            # 添加卖出交易记录
            trading_record = {
                'id': len(self.trading_records) + 1,
                'code': code,
                'name': stock_info['name'],
                'action': 'sell',
                'price': sell_price,
                'date': current_time.strftime('%Y-%m-%d'),
                'time': current_time.strftime('%H:%M:%S'),
                'datetime': current_time.isoformat(),
                'notes': notes
            }
            
            self.trading_records.append(trading_record)
            
            # 保存到文件
            self.save_data()
            
            logger.info(f"✅ 标记股票已卖出: {code} {stock_info['name']}")
            return True
            
        except Exception as e:
            logger.error(f"标记股票卖出失败 {code}: {e}")
            return False
    
    def remove_bought_stock(self, code: str) -> bool:
        """
        移除已买入股票（从列表中删除）
        
        Args:
            code: 股票代码
            
        Returns:
            bool: 是否移除成功
        """
        try:
            if code not in self.bought_stocks:
                logger.warning(f"股票 {code} 不在已买入列表中")
                return False
            
            stock_info = self.bought_stocks[code]
            del self.bought_stocks[code]
            
            # 保存到文件
            self.save_data()
            
            logger.info(f"✅ 移除已买入股票: {code} {stock_info['name']}")
            return True
            
        except Exception as e:
            logger.error(f"移除已买入股票失败 {code}: {e}")
            return False
    
    def get_holding_stocks(self) -> Dict[str, Dict]:
        """
        获取当前持仓股票
        
        Returns:
            Dict: 持仓股票字典
        """
        return {code: info for code, info in self.bought_stocks.items() 
                if info['status'] == 'holding'}
    
    def get_sold_stocks(self) -> Dict[str, Dict]:
        """
        获取已卖出股票
        
        Returns:
            Dict: 已卖出股票字典
        """
        return {code: info for code, info in self.bought_stocks.items() 
                if info['status'] == 'sold'}
    
    def get_stock_info(self, code: str) -> Optional[Dict]:
        """
        获取股票信息
        
        Args:
            code: 股票代码
            
        Returns:
            Optional[Dict]: 股票信息，如果不存在返回None
        """
        return self.bought_stocks.get(code)
    
    def is_bought_stock(self, code: str) -> bool:
        """
        检查是否为已买入股票
        
        Args:
            code: 股票代码
            
        Returns:
            bool: 是否为已买入股票
        """
        return code in self.bought_stocks and self.bought_stocks[code]['status'] == 'holding'
    
    def update_hold_days(self):
        """更新持有天数"""
        try:
            current_date = datetime.now().date()
            
            for code, stock_info in self.bought_stocks.items():
                if stock_info['status'] == 'holding':
                    buy_date = datetime.fromisoformat(stock_info['buy_datetime']).date()
                    hold_days = (current_date - buy_date).days + 1  # 包含买入当天
                    stock_info['hold_days'] = hold_days
                    stock_info['updated_at'] = datetime.now().isoformat()
            
            # 保存更新
            self.save_data()
            
        except Exception as e:
            logger.error(f"更新持有天数失败: {e}")

    def get_all_bought_stocks(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有已买入股票的信息

        Returns:
            Dict: 所有已买入股票的详细信息
        """
        try:
            all_stocks = {}

            for stock_code in self.bought_stocks:
                if self.bought_stocks[stock_code].get('status') == 'holding':
                    # 只返回持有中的股票
                    stock_info = self.bought_stocks[stock_code].copy()

                    # 计算持有天数
                    buy_date = datetime.fromisoformat(stock_info['buy_date'])
                    hold_days = (datetime.now() - buy_date).days + 1
                    stock_info['hold_days'] = hold_days

                    all_stocks[stock_code] = stock_info

            return all_stocks

        except Exception as e:
            logger.error(f"获取所有已买入股票失败: {e}")
            return {}

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            holding_stocks = self.get_holding_stocks()
            sold_stocks = self.get_sold_stocks()
            
            # 计算盈亏统计
            total_profit_loss = 0
            profitable_count = 0
            loss_count = 0
            
            for stock_info in sold_stocks.values():
                if stock_info.get('profit_loss') is not None:
                    profit_loss = stock_info['profit_loss']
                    total_profit_loss += profit_loss
                    
                    if profit_loss > 0:
                        profitable_count += 1
                    elif profit_loss < 0:
                        loss_count += 1
            
            win_rate = (profitable_count / len(sold_stocks) * 100) if sold_stocks else 0
            
            return {
                'total_stocks': len(self.bought_stocks),
                'holding_stocks': len(holding_stocks),
                'sold_stocks': len(sold_stocks),
                'total_profit_loss': total_profit_loss,
                'profitable_count': profitable_count,
                'loss_count': loss_count,
                'win_rate': win_rate,
                'trading_records': len(self.trading_records)
            }
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}
    
    def save_data(self):
        """保存数据到文件"""
        try:
            # 保存已买入股票数据
            with open(self.bought_stocks_file, 'w', encoding='utf-8') as f:
                json.dump(self.bought_stocks, f, ensure_ascii=False, indent=2)
            
            # 保存交易记录
            with open(self.trading_records_file, 'w', encoding='utf-8') as f:
                json.dump(self.trading_records, f, ensure_ascii=False, indent=2)
            
            logger.debug("数据保存成功")
            
        except Exception as e:
            logger.error(f"保存数据失败: {e}")
    
    def load_data(self):
        """从文件加载数据"""
        try:
            # 加载已买入股票数据
            if self.bought_stocks_file.exists():
                with open(self.bought_stocks_file, 'r', encoding='utf-8') as f:
                    self.bought_stocks = json.load(f)
                logger.info(f"加载已买入股票数据: {len(self.bought_stocks)} 只")
            
            # 加载交易记录
            if self.trading_records_file.exists():
                with open(self.trading_records_file, 'r', encoding='utf-8') as f:
                    self.trading_records = json.load(f)
                logger.info(f"加载交易记录: {len(self.trading_records)} 条")
            
        except Exception as e:
            logger.error(f"加载数据失败: {e}")
            self.bought_stocks = {}
            self.trading_records = []

# 创建全局实例
bought_stocks_manager = BoughtStocksManager()
