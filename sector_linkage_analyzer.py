#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块联动分析模块
分析同板块个股的联动效应、概念热度变化、资金流向等
这是识别游资板块操作的重要数据模块
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging
from collections import defaultdict

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class SectorLinkageAnalyzer:
    """板块联动分析器"""
    
    def __init__(self):
        self.analysis_days = 20  # 分析天数
        self.correlation_threshold = 0.6  # 联动相关性阈值
        self.hot_threshold = 0.03  # 热度阈值（3%）
        self.leader_volume_ratio = 1.5  # 龙头股成交量倍数
        
    def get_sector_linkage_analysis(self, stock_code: str) -> Dict:
        """
        获取完整的板块联动分析
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 板块联动分析结果
        """
        try:
            logger.info(f"开始分析{stock_code}的板块联动")
            
            # 1. 获取股票基本信息和所属板块
            stock_info = self._get_stock_sector_info(stock_code)
            if not stock_info:
                return {'data_available': False, 'error': '无法获取股票板块信息'}
            
            # 2. 获取同板块股票列表
            sector_stocks = self._get_sector_stocks(stock_info['sectors'])
            if not sector_stocks:
                return {'data_available': False, 'error': '无法获取同板块股票'}
            
            # 3. 获取板块内股票的历史数据
            sector_data = self._get_sector_historical_data(sector_stocks)
            if not sector_data:
                return {'data_available': False, 'error': '无法获取板块历史数据'}
            
            # 4. 分析同板块个股联动
            linkage_analysis = self._analyze_sector_linkage(stock_code, sector_data)
            
            # 5. 评估概念热度
            concept_heat = self._evaluate_concept_heat(sector_data, stock_info['sectors'])
            
            # 6. 追踪板块资金流向
            capital_flow = self._track_sector_capital_flow(sector_data)
            
            # 7. 识别龙头股和跟风股
            leader_follower = self._identify_leader_follower(sector_data, stock_code)
            
            # 8. 生成板块联动综合评估
            comprehensive_assessment = self._generate_sector_assessment(
                linkage_analysis, concept_heat, capital_flow, leader_follower, stock_code
            )
            
            return {
                'data_available': True,
                'stock_code': stock_code,
                'analysis_date': datetime.now().strftime('%Y-%m-%d'),
                'stock_info': stock_info,
                'sector_stocks_count': len(sector_stocks),
                'linkage_analysis': linkage_analysis,
                'concept_heat': concept_heat,
                'capital_flow': capital_flow,
                'leader_follower': leader_follower,
                'comprehensive_assessment': comprehensive_assessment,
                'data_quality': {
                    'analysis_days': self.analysis_days,
                    'sector_coverage': len(sector_data),
                    'data_completeness': '完整' if len(sector_data) >= 5 else '部分'
                }
            }
            
        except Exception as e:
            logger.error(f"板块联动分析失败: {e}")
            return {'data_available': False, 'error': str(e)}
    
    def _get_stock_sector_info(self, stock_code: str) -> Dict:
        """获取股票基本信息和所属板块"""
        try:
            logger.info(f"获取{stock_code}的板块信息")
            
            # 获取股票基本信息
            try:
                stock_info = ak.stock_individual_info_em(symbol=stock_code)
                if stock_info.empty:
                    return {}
                
                # 提取关键信息
                info_dict = {}
                for _, row in stock_info.iterrows():
                    key = row['item']
                    value = row['value']
                    info_dict[key] = value
                
                # 获取概念板块信息
                concept_info = self._get_concept_info(stock_code)
                
                return {
                    'stock_name': info_dict.get('股票简称', ''),
                    'industry': info_dict.get('所属行业', ''),
                    'market_cap': info_dict.get('总市值', ''),
                    'sectors': concept_info,
                    'pe_ratio': info_dict.get('市盈率-动态', ''),
                    'pb_ratio': info_dict.get('市净率', '')
                }
                
            except Exception as e:
                logger.warning(f"获取股票基本信息失败: {e}")
                # 使用备用方法
                return self._get_basic_sector_info(stock_code)
                
        except Exception as e:
            logger.error(f"获取股票板块信息失败: {e}")
            return {}
    
    def _get_concept_info(self, stock_code: str) -> List[str]:
        """获取股票概念信息"""
        try:
            # 获取概念板块数据
            concept_data = ak.stock_board_concept_name_em()
            
            # 查找包含该股票的概念
            concepts = []
            for _, concept_row in concept_data.iterrows():
                concept_code = concept_row['板块代码']
                concept_name = concept_row['板块名称']
                
                try:
                    # 获取概念内的股票
                    concept_stocks = ak.stock_board_concept_cons_em(symbol=concept_name)
                    if not concept_stocks.empty:
                        stock_codes = concept_stocks['代码'].tolist()
                        if stock_code in stock_codes:
                            concepts.append(concept_name)
                except:
                    continue
                
                # 限制概念数量，避免过多
                if len(concepts) >= 5:
                    break
            
            return concepts[:5]  # 返回前5个概念
            
        except Exception as e:
            logger.warning(f"获取概念信息失败: {e}")
            return []
    
    def _get_basic_sector_info(self, stock_code: str) -> Dict:
        """获取基本板块信息（备用方法）"""
        try:
            # 使用行业分类作为板块
            industry_data = ak.stock_board_industry_name_em()
            
            for _, row in industry_data.iterrows():
                industry_name = row['板块名称']
                try:
                    industry_stocks = ak.stock_board_industry_cons_em(symbol=industry_name)
                    if not industry_stocks.empty:
                        stock_codes = industry_stocks['代码'].tolist()
                        if stock_code in stock_codes:
                            return {
                                'stock_name': '',
                                'industry': industry_name,
                                'market_cap': '',
                                'sectors': [industry_name],
                                'pe_ratio': '',
                                'pb_ratio': ''
                            }
                except:
                    continue
            
            return {}
            
        except Exception as e:
            logger.error(f"获取基本板块信息失败: {e}")
            return {}
    
    def _get_sector_stocks(self, sectors: List[str]) -> List[str]:
        """获取同板块股票列表"""
        try:
            if not sectors:
                return []
            
            all_stocks = set()
            
            for sector in sectors:
                try:
                    # 尝试作为概念板块获取
                    try:
                        concept_stocks = ak.stock_board_concept_cons_em(symbol=sector)
                        if not concept_stocks.empty:
                            stocks = concept_stocks['代码'].tolist()
                            all_stocks.update(stocks)
                            continue
                    except:
                        pass
                    
                    # 尝试作为行业板块获取
                    try:
                        industry_stocks = ak.stock_board_industry_cons_em(symbol=sector)
                        if not industry_stocks.empty:
                            stocks = industry_stocks['代码'].tolist()
                            all_stocks.update(stocks)
                    except:
                        pass
                        
                except Exception as e:
                    logger.warning(f"获取板块{sector}股票失败: {e}")
                    continue
            
            # 限制股票数量，避免数据过多
            sector_stocks = list(all_stocks)[:20]
            logger.info(f"获取到{len(sector_stocks)}只同板块股票")
            
            return sector_stocks
            
        except Exception as e:
            logger.error(f"获取同板块股票失败: {e}")
            return []
    
    def _get_sector_historical_data(self, sector_stocks: List[str]) -> Dict[str, pd.DataFrame]:
        """获取板块内股票的历史数据"""
        try:
            logger.info(f"获取{len(sector_stocks)}只股票的历史数据")
            
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.analysis_days + 10)
            
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            
            sector_data = {}
            
            for i, stock_code in enumerate(sector_stocks):
                try:
                    # 获取历史数据
                    df = ak.stock_zh_a_hist(
                        symbol=stock_code,
                        start_date=start_date_str,
                        end_date=end_date_str,
                        adjust="qfq"
                    )
                    
                    if not df.empty:
                        # 标准化列名
                        if '收盘' in df.columns:
                            df['close'] = pd.to_numeric(df['收盘'], errors='coerce')
                        if '开盘' in df.columns:
                            df['open'] = pd.to_numeric(df['开盘'], errors='coerce')
                        if '最高' in df.columns:
                            df['high'] = pd.to_numeric(df['最高'], errors='coerce')
                        if '最低' in df.columns:
                            df['low'] = pd.to_numeric(df['最低'], errors='coerce')
                        if '成交量' in df.columns:
                            df['volume'] = pd.to_numeric(df['成交量'], errors='coerce')
                        if '成交额' in df.columns:
                            df['amount'] = pd.to_numeric(df['成交额'], errors='coerce')
                        if '日期' in df.columns:
                            df['date'] = pd.to_datetime(df['日期'])
                        
                        # 计算涨跌幅
                        df['pct_change'] = df['close'].pct_change() * 100
                        
                        # 限制数据天数
                        if len(df) > self.analysis_days:
                            df = df.tail(self.analysis_days)
                        
                        sector_data[stock_code] = df
                        
                        # 显示进度
                        if (i + 1) % 5 == 0:
                            logger.info(f"已获取{i + 1}/{len(sector_stocks)}只股票数据")
                    
                except Exception as e:
                    logger.warning(f"获取{stock_code}历史数据失败: {e}")
                    continue
                
                # 限制获取数量，避免过多API调用
                if len(sector_data) >= 15:
                    break
            
            logger.info(f"成功获取{len(sector_data)}只股票的历史数据")
            return sector_data
            
        except Exception as e:
            logger.error(f"获取板块历史数据失败: {e}")
            return {}
    
    def _analyze_sector_linkage(self, target_stock: str, sector_data: Dict[str, pd.DataFrame]) -> Dict:
        """分析同板块个股联动"""
        try:
            logger.info("分析板块个股联动")
            
            if target_stock not in sector_data or len(sector_data) < 2:
                return {}
            
            target_data = sector_data[target_stock]
            target_returns = target_data['pct_change'].dropna()
            
            linkage_results = {}
            correlations = {}
            
            # 计算与其他股票的相关性
            for stock_code, stock_data in sector_data.items():
                if stock_code == target_stock:
                    continue
                
                stock_returns = stock_data['pct_change'].dropna()
                
                # 确保数据长度一致
                min_length = min(len(target_returns), len(stock_returns))
                if min_length < 10:  # 至少需要10个数据点
                    continue
                
                target_aligned = target_returns.tail(min_length)
                stock_aligned = stock_returns.tail(min_length)
                
                # 计算相关系数
                correlation = target_aligned.corr(stock_aligned)
                if not np.isnan(correlation):
                    correlations[stock_code] = correlation
            
            # 分析联动强度
            strong_linkage = {k: v for k, v in correlations.items() if v > self.correlation_threshold}
            weak_linkage = {k: v for k, v in correlations.items() if 0.3 < v <= self.correlation_threshold}
            negative_linkage = {k: v for k, v in correlations.items() if v < -0.3}
            
            # 计算平均联动度
            avg_correlation = np.mean(list(correlations.values())) if correlations else 0
            
            # 分析同步性
            synchronization = self._analyze_synchronization(target_stock, sector_data)
            
            return {
                'total_analyzed_stocks': len(correlations),
                'avg_correlation': round(avg_correlation, 3),
                'strong_linkage_count': len(strong_linkage),
                'strong_linkage_stocks': dict(sorted(strong_linkage.items(), key=lambda x: x[1], reverse=True)[:5]),
                'weak_linkage_count': len(weak_linkage),
                'negative_linkage_count': len(negative_linkage),
                'synchronization': synchronization,
                'linkage_level': self._determine_linkage_level(avg_correlation, len(strong_linkage), len(correlations))
            }
            
        except Exception as e:
            logger.error(f"分析板块联动失败: {e}")
            return {}

    def _analyze_synchronization(self, target_stock: str, sector_data: Dict[str, pd.DataFrame]) -> Dict:
        """分析同步性"""
        try:
            if target_stock not in sector_data:
                return {}

            target_data = sector_data[target_stock]
            target_changes = target_data['pct_change'].dropna()

            sync_stats = {
                'same_direction_days': 0,
                'opposite_direction_days': 0,
                'leading_days': 0,
                'lagging_days': 0
            }

            # 分析同向和反向天数
            for stock_code, stock_data in sector_data.items():
                if stock_code == target_stock:
                    continue

                stock_changes = stock_data['pct_change'].dropna()
                min_length = min(len(target_changes), len(stock_changes))

                if min_length < 5:
                    continue

                target_aligned = target_changes.tail(min_length)
                stock_aligned = stock_changes.tail(min_length)

                # 计算同向天数
                same_direction = ((target_aligned > 0) & (stock_aligned > 0)) | ((target_aligned < 0) & (stock_aligned < 0))
                sync_stats['same_direction_days'] += same_direction.sum()
                sync_stats['opposite_direction_days'] += (~same_direction).sum()

                # 分析领先滞后关系
                if len(target_aligned) > 1:
                    target_shift = target_aligned.shift(1).dropna()
                    stock_current = stock_aligned[1:len(target_shift)+1]

                    if len(target_shift) == len(stock_current):
                        leading_corr = target_shift.corr(stock_current)
                        if not np.isnan(leading_corr) and leading_corr > 0.5:
                            sync_stats['leading_days'] += 1

                break  # 只分析一只代表性股票

            total_days = sync_stats['same_direction_days'] + sync_stats['opposite_direction_days']
            sync_ratio = sync_stats['same_direction_days'] / total_days if total_days > 0 else 0

            return {
                'sync_ratio': round(sync_ratio, 3),
                'same_direction_days': sync_stats['same_direction_days'],
                'opposite_direction_days': sync_stats['opposite_direction_days'],
                'sync_level': '高度同步' if sync_ratio > 0.8 else '中度同步' if sync_ratio > 0.6 else '低度同步'
            }

        except Exception as e:
            logger.error(f"分析同步性失败: {e}")
            return {}

    def _determine_linkage_level(self, avg_correlation: float, strong_count: int, total_count: int) -> str:
        """确定联动等级"""
        if avg_correlation > 0.7 and strong_count / total_count > 0.5:
            return "高度联动"
        elif avg_correlation > 0.5 and strong_count / total_count > 0.3:
            return "中度联动"
        elif avg_correlation > 0.3:
            return "弱联动"
        else:
            return "独立走势"

    def _evaluate_concept_heat(self, sector_data: Dict[str, pd.DataFrame], sectors: List[str]) -> Dict:
        """评估概念热度"""
        try:
            logger.info("评估概念热度")

            if not sector_data:
                return {}

            # 计算板块整体表现
            recent_performance = {}
            volume_performance = {}

            for stock_code, stock_data in sector_data.items():
                if len(stock_data) < 5:
                    continue

                # 计算近期涨跌幅
                recent_data = stock_data.tail(5)
                total_return = (recent_data['close'].iloc[-1] / recent_data['close'].iloc[0] - 1) * 100
                recent_performance[stock_code] = total_return

                # 计算成交量变化
                recent_volume = recent_data['volume'].mean()
                historical_volume = stock_data['volume'].mean()
                volume_ratio = recent_volume / historical_volume if historical_volume > 0 else 1
                volume_performance[stock_code] = volume_ratio

            # 计算热度指标
            heat_metrics = self._calculate_heat_metrics(recent_performance, volume_performance)

            # 评估热度等级
            heat_level = self._determine_heat_level(heat_metrics)

            # 分析热度趋势
            heat_trend = self._analyze_heat_trend(sector_data)

            return {
                'heat_metrics': heat_metrics,
                'heat_level': heat_level,
                'heat_trend': heat_trend,
                'participating_stocks': len(recent_performance),
                'rising_stocks': len([x for x in recent_performance.values() if x > 0]),
                'falling_stocks': len([x for x in recent_performance.values() if x < 0]),
                'top_performers': dict(sorted(recent_performance.items(), key=lambda x: x[1], reverse=True)[:5])
            }

        except Exception as e:
            logger.error(f"评估概念热度失败: {e}")
            return {}

    def _calculate_heat_metrics(self, performance: Dict, volume: Dict) -> Dict:
        """计算热度指标"""
        try:
            if not performance or not volume:
                return {}

            # 价格热度
            avg_return = np.mean(list(performance.values()))
            positive_ratio = len([x for x in performance.values() if x > 0]) / len(performance)

            # 成交量热度
            avg_volume_ratio = np.mean(list(volume.values()))
            active_ratio = len([x for x in volume.values() if x > 1.2]) / len(volume)

            # 综合热度评分
            price_score = min(100, max(0, (avg_return + 10) * 5))  # -10%到+10%映射到0-100
            volume_score = min(100, max(0, (avg_volume_ratio - 0.5) * 100))  # 0.5-1.5倍映射到0-100

            comprehensive_score = (price_score * 0.6 + volume_score * 0.4)

            return {
                'avg_return': round(avg_return, 2),
                'positive_ratio': round(positive_ratio, 3),
                'avg_volume_ratio': round(avg_volume_ratio, 2),
                'active_ratio': round(active_ratio, 3),
                'price_score': round(price_score, 1),
                'volume_score': round(volume_score, 1),
                'comprehensive_score': round(comprehensive_score, 1)
            }

        except Exception as e:
            logger.error(f"计算热度指标失败: {e}")
            return {}

    def _determine_heat_level(self, metrics: Dict) -> str:
        """确定热度等级"""
        score = metrics.get('comprehensive_score', 0)
        positive_ratio = metrics.get('positive_ratio', 0)

        if score > 80 and positive_ratio > 0.8:
            return "极热"
        elif score > 60 and positive_ratio > 0.6:
            return "较热"
        elif score > 40 and positive_ratio > 0.4:
            return "温热"
        elif score > 20:
            return "偏冷"
        else:
            return "冰冷"

    def _analyze_heat_trend(self, sector_data: Dict[str, pd.DataFrame]) -> Dict:
        """分析热度趋势"""
        try:
            if not sector_data:
                return {}

            # 分析不同时期的热度
            periods = {
                'recent_3_days': 3,
                'recent_5_days': 5,
                'recent_10_days': 10
            }

            trend_data = {}

            for period_name, days in periods.items():
                period_performance = {}

                for stock_code, stock_data in sector_data.items():
                    if len(stock_data) < days:
                        continue

                    period_data = stock_data.tail(days)
                    period_return = (period_data['close'].iloc[-1] / period_data['close'].iloc[0] - 1) * 100
                    period_performance[stock_code] = period_return

                if period_performance:
                    avg_return = np.mean(list(period_performance.values()))
                    positive_ratio = len([x for x in period_performance.values() if x > 0]) / len(period_performance)
                    trend_data[period_name] = {
                        'avg_return': round(avg_return, 2),
                        'positive_ratio': round(positive_ratio, 3)
                    }

            # 判断趋势方向
            if len(trend_data) >= 2:
                recent_scores = []
                for period in ['recent_3_days', 'recent_5_days', 'recent_10_days']:
                    if period in trend_data:
                        score = trend_data[period]['avg_return'] * trend_data[period]['positive_ratio']
                        recent_scores.append(score)

                if len(recent_scores) >= 2:
                    if recent_scores[0] > recent_scores[-1]:
                        trend_direction = "升温"
                    elif recent_scores[0] < recent_scores[-1]:
                        trend_direction = "降温"
                    else:
                        trend_direction = "稳定"
                else:
                    trend_direction = "稳定"
            else:
                trend_direction = "稳定"

            return {
                'trend_data': trend_data,
                'trend_direction': trend_direction
            }

        except Exception as e:
            logger.error(f"分析热度趋势失败: {e}")
            return {}

    def _track_sector_capital_flow(self, sector_data: Dict[str, pd.DataFrame]) -> Dict:
        """追踪板块资金流向"""
        try:
            logger.info("追踪板块资金流向")

            if not sector_data:
                return {}

            # 计算资金流向指标
            capital_metrics = {}

            for stock_code, stock_data in sector_data.items():
                if len(stock_data) < 5:
                    continue

                # 计算资金流向（简化版本：成交额 * 涨跌幅）
                recent_data = stock_data.tail(5)

                capital_flow = 0
                for _, row in recent_data.iterrows():
                    if not pd.isna(row['amount']) and not pd.isna(row['pct_change']):
                        daily_flow = row['amount'] * (row['pct_change'] / 100)
                        capital_flow += daily_flow

                capital_metrics[stock_code] = capital_flow

            # 分析整体资金流向
            total_inflow = sum([x for x in capital_metrics.values() if x > 0])
            total_outflow = abs(sum([x for x in capital_metrics.values() if x < 0]))
            net_flow = sum(capital_metrics.values())

            # 识别资金流向最大的股票
            top_inflow = dict(sorted([(k, v) for k, v in capital_metrics.items() if v > 0],
                                   key=lambda x: x[1], reverse=True)[:5])
            top_outflow = dict(sorted([(k, v) for k, v in capital_metrics.items() if v < 0],
                                    key=lambda x: x[1])[:5])

            # 计算资金流向强度
            flow_intensity = self._calculate_flow_intensity(capital_metrics, sector_data)

            return {
                'net_flow': round(net_flow, 0),
                'total_inflow': round(total_inflow, 0),
                'total_outflow': round(total_outflow, 0),
                'flow_ratio': round(total_inflow / (total_inflow + total_outflow), 3) if (total_inflow + total_outflow) > 0 else 0.5,
                'top_inflow_stocks': top_inflow,
                'top_outflow_stocks': top_outflow,
                'flow_intensity': flow_intensity,
                'flow_direction': '净流入' if net_flow > 0 else '净流出' if net_flow < 0 else '平衡'
            }

        except Exception as e:
            logger.error(f"追踪板块资金流向失败: {e}")
            return {}

    def _calculate_flow_intensity(self, capital_metrics: Dict, sector_data: Dict) -> str:
        """计算资金流向强度"""
        try:
            if not capital_metrics:
                return "无数据"

            # 计算流向强度指标
            flow_values = list(capital_metrics.values())
            flow_std = np.std(flow_values)
            flow_mean = np.mean([abs(x) for x in flow_values])

            # 计算活跃股票比例
            active_stocks = len([x for x in flow_values if abs(x) > flow_mean])
            total_stocks = len(flow_values)
            active_ratio = active_stocks / total_stocks if total_stocks > 0 else 0

            # 综合判断强度
            if flow_std > flow_mean * 2 and active_ratio > 0.6:
                return "强烈"
            elif flow_std > flow_mean and active_ratio > 0.4:
                return "中等"
            elif active_ratio > 0.2:
                return "温和"
            else:
                return "微弱"

        except Exception as e:
            logger.error(f"计算资金流向强度失败: {e}")
            return "无数据"

    def _identify_leader_follower(self, sector_data: Dict[str, pd.DataFrame], target_stock: str) -> Dict:
        """识别龙头股和跟风股"""
        try:
            logger.info("识别龙头股和跟风股")

            if not sector_data or len(sector_data) < 3:
                return {}

            # 计算各股票的综合指标
            stock_metrics = {}

            for stock_code, stock_data in sector_data.items():
                if len(stock_data) < 10:
                    continue

                recent_data = stock_data.tail(10)

                # 计算指标
                avg_return = recent_data['pct_change'].mean()
                return_volatility = recent_data['pct_change'].std()
                avg_volume = recent_data['volume'].mean()
                avg_amount = recent_data['amount'].mean()

                # 计算领先指标（价格变化的时序性）
                leading_score = self._calculate_leading_score(stock_code, sector_data)

                stock_metrics[stock_code] = {
                    'avg_return': avg_return,
                    'volatility': return_volatility,
                    'avg_volume': avg_volume,
                    'avg_amount': avg_amount,
                    'leading_score': leading_score
                }

            # 识别龙头股
            leaders = self._identify_leaders(stock_metrics)

            # 识别跟风股
            followers = self._identify_followers(stock_metrics, leaders)

            # 分析目标股票的角色
            target_role = self._determine_target_role(target_stock, leaders, followers, stock_metrics)

            return {
                'leaders': leaders,
                'followers': followers,
                'target_stock_role': target_role,
                'leadership_analysis': self._analyze_leadership_pattern(leaders, followers, stock_metrics)
            }

        except Exception as e:
            logger.error(f"识别龙头股和跟风股失败: {e}")
            return {}

    def _calculate_leading_score(self, stock_code: str, sector_data: Dict[str, pd.DataFrame]) -> float:
        """计算领先得分"""
        try:
            if stock_code not in sector_data:
                return 0

            target_data = sector_data[stock_code]
            target_changes = target_data['pct_change'].dropna()

            leading_correlations = []

            # 与其他股票比较领先性
            for other_code, other_data in sector_data.items():
                if other_code == stock_code:
                    continue

                other_changes = other_data['pct_change'].dropna()
                min_length = min(len(target_changes), len(other_changes))

                if min_length < 5:
                    continue

                # 计算领先相关性（目标股票今天 vs 其他股票明天）
                target_today = target_changes.tail(min_length-1)
                other_tomorrow = other_changes.tail(min_length-1).shift(-1).dropna()

                if len(target_today) == len(other_tomorrow):
                    correlation = target_today.corr(other_tomorrow)
                    if not np.isnan(correlation):
                        leading_correlations.append(correlation)

            return np.mean(leading_correlations) if leading_correlations else 0

        except Exception as e:
            logger.error(f"计算领先得分失败: {e}")
            return 0

    def _identify_leaders(self, stock_metrics: Dict) -> List[Dict]:
        """识别龙头股"""
        try:
            leaders = []

            for stock_code, metrics in stock_metrics.items():
                # 龙头股特征：高收益、高成交额、高领先性
                leader_score = 0

                # 收益得分
                if metrics['avg_return'] > 2:
                    leader_score += 3
                elif metrics['avg_return'] > 0:
                    leader_score += 1

                # 成交额得分
                avg_amounts = [m['avg_amount'] for m in stock_metrics.values()]
                amount_rank = sorted(avg_amounts, reverse=True).index(metrics['avg_amount'])
                if amount_rank < len(avg_amounts) * 0.3:  # 前30%
                    leader_score += 2

                # 领先性得分
                if metrics['leading_score'] > 0.3:
                    leader_score += 3
                elif metrics['leading_score'] > 0.1:
                    leader_score += 1

                # 波动性得分（适度波动）
                if 1 < metrics['volatility'] < 3:
                    leader_score += 1

                if leader_score >= 5:  # 龙头股阈值
                    leaders.append({
                        'stock_code': stock_code,
                        'leader_score': leader_score,
                        'avg_return': round(metrics['avg_return'], 2),
                        'leading_score': round(metrics['leading_score'], 3),
                        'avg_amount': round(metrics['avg_amount'], 0)
                    })

            # 按得分排序
            leaders.sort(key=lambda x: x['leader_score'], reverse=True)
            return leaders[:3]  # 返回前3个龙头股

        except Exception as e:
            logger.error(f"识别龙头股失败: {e}")
            return []

    def _identify_followers(self, stock_metrics: Dict, leaders: List) -> List[Dict]:
        """识别跟风股"""
        try:
            followers = []
            leader_codes = [leader['stock_code'] for leader in leaders]

            for stock_code, metrics in stock_metrics.items():
                if stock_code in leader_codes:
                    continue

                # 跟风股特征：中等收益、低领先性、高相关性
                follower_score = 0

                # 收益得分（跟风股通常收益较龙头股低）
                if 0 < metrics['avg_return'] < 3:
                    follower_score += 2
                elif metrics['avg_return'] > 0:
                    follower_score += 1

                # 领先性得分（跟风股领先性低）
                if metrics['leading_score'] < 0.1:
                    follower_score += 2
                elif metrics['leading_score'] < 0.2:
                    follower_score += 1

                # 成交量得分（跟风股成交量通常较高）
                avg_volumes = [m['avg_volume'] for m in stock_metrics.values()]
                volume_rank = sorted(avg_volumes, reverse=True).index(metrics['avg_volume'])
                if volume_rank < len(avg_volumes) * 0.5:  # 前50%
                    follower_score += 1

                if follower_score >= 3:  # 跟风股阈值
                    followers.append({
                        'stock_code': stock_code,
                        'follower_score': follower_score,
                        'avg_return': round(metrics['avg_return'], 2),
                        'leading_score': round(metrics['leading_score'], 3),
                        'avg_volume': round(metrics['avg_volume'], 0)
                    })

            # 按得分排序
            followers.sort(key=lambda x: x['follower_score'], reverse=True)
            return followers[:5]  # 返回前5个跟风股

        except Exception as e:
            logger.error(f"识别跟风股失败: {e}")
            return []

    def _determine_target_role(self, target_stock: str, leaders: List, followers: List, stock_metrics: Dict) -> Dict:
        """确定目标股票的角色"""
        try:
            leader_codes = [leader['stock_code'] for leader in leaders]
            follower_codes = [follower['stock_code'] for follower in followers]

            if target_stock in leader_codes:
                leader_info = next(leader for leader in leaders if leader['stock_code'] == target_stock)
                return {
                    'role': '龙头股',
                    'rank': leader_codes.index(target_stock) + 1,
                    'score': leader_info['leader_score'],
                    'characteristics': ['高收益', '强领先性', '大成交额']
                }
            elif target_stock in follower_codes:
                follower_info = next(follower for follower in followers if follower['stock_code'] == target_stock)
                return {
                    'role': '跟风股',
                    'rank': follower_codes.index(target_stock) + 1,
                    'score': follower_info['follower_score'],
                    'characteristics': ['中等收益', '弱领先性', '高成交量']
                }
            else:
                # 分析为什么不是龙头或跟风
                if target_stock in stock_metrics:
                    metrics = stock_metrics[target_stock]
                    characteristics = []

                    if metrics['avg_return'] < 0:
                        characteristics.append('负收益')
                    elif metrics['avg_return'] < 1:
                        characteristics.append('低收益')

                    if metrics['leading_score'] < 0.1:
                        characteristics.append('无领先性')

                    avg_amounts = [m['avg_amount'] for m in stock_metrics.values()]
                    amount_rank = sorted(avg_amounts, reverse=True).index(metrics['avg_amount'])
                    if amount_rank > len(avg_amounts) * 0.7:
                        characteristics.append('低成交额')

                    return {
                        'role': '独立个股',
                        'rank': 0,
                        'score': 0,
                        'characteristics': characteristics if characteristics else ['表现平平']
                    }
                else:
                    return {
                        'role': '数据不足',
                        'rank': 0,
                        'score': 0,
                        'characteristics': ['数据不足']
                    }

        except Exception as e:
            logger.error(f"确定目标股票角色失败: {e}")
            return {}

    def _analyze_leadership_pattern(self, leaders: List, followers: List, stock_metrics: Dict) -> Dict:
        """分析领导模式"""
        try:
            pattern_analysis = {}

            # 分析龙头股特征
            if leaders:
                leader_returns = [leader['avg_return'] for leader in leaders]
                leader_leading_scores = [leader['leading_score'] for leader in leaders]

                pattern_analysis['leader_pattern'] = {
                    'count': len(leaders),
                    'avg_return': round(np.mean(leader_returns), 2),
                    'avg_leading_score': round(np.mean(leader_leading_scores), 3),
                    'leadership_strength': '强' if np.mean(leader_leading_scores) > 0.3 else '中' if np.mean(leader_leading_scores) > 0.1 else '弱'
                }

            # 分析跟风股特征
            if followers:
                follower_returns = [follower['avg_return'] for follower in followers]
                follower_leading_scores = [follower['leading_score'] for follower in followers]

                pattern_analysis['follower_pattern'] = {
                    'count': len(followers),
                    'avg_return': round(np.mean(follower_returns), 2),
                    'avg_leading_score': round(np.mean(follower_leading_scores), 3),
                    'follow_strength': '强' if len(followers) > 3 else '中' if len(followers) > 1 else '弱'
                }

            # 分析板块整体模式
            total_stocks = len(stock_metrics)
            leader_ratio = len(leaders) / total_stocks if total_stocks > 0 else 0
            follower_ratio = len(followers) / total_stocks if total_stocks > 0 else 0

            if leader_ratio > 0.2:
                sector_pattern = "多龙头模式"
            elif leader_ratio > 0.1 and follower_ratio > 0.3:
                sector_pattern = "龙头带动模式"
            elif follower_ratio > 0.5:
                sector_pattern = "跟风为主模式"
            else:
                sector_pattern = "分散模式"

            pattern_analysis['sector_pattern'] = {
                'pattern_type': sector_pattern,
                'leader_ratio': round(leader_ratio, 3),
                'follower_ratio': round(follower_ratio, 3)
            }

            return pattern_analysis

        except Exception as e:
            logger.error(f"分析领导模式失败: {e}")
            return {}

    def _generate_sector_assessment(self, linkage: Dict, heat: Dict, capital: Dict,
                                  leader_follower: Dict, target_stock: str) -> Dict:
        """生成板块联动综合评估"""
        try:
            logger.info("生成板块联动综合评估")

            assessment = {
                'overall_score': 0,
                'key_findings': [],
                'sector_signals': [],
                'operation_suggestions': [],
                'risk_warnings': []
            }

            # 1. 计算综合评分
            assessment['overall_score'] = self._calculate_sector_score(linkage, heat, capital, leader_follower)

            # 2. 提取关键发现
            assessment['key_findings'] = self._extract_sector_findings(linkage, heat, capital, leader_follower, target_stock)

            # 3. 生成板块信号
            assessment['sector_signals'] = self._generate_sector_signals(linkage, heat, capital, leader_follower)

            # 4. 生成操作建议
            assessment['operation_suggestions'] = self._generate_sector_suggestions(
                assessment['overall_score'], linkage, heat, capital, leader_follower, target_stock
            )

            # 5. 识别风险警告
            assessment['risk_warnings'] = self._identify_sector_risks(linkage, heat, capital, leader_follower)

            return assessment

        except Exception as e:
            logger.error(f"生成板块联动综合评估失败: {e}")
            return {}

    def _calculate_sector_score(self, linkage: Dict, heat: Dict, capital: Dict, leader_follower: Dict) -> int:
        """计算板块综合评分"""
        try:
            score = 50  # 基础分

            # 联动性评分
            linkage_level = linkage.get('linkage_level', '')
            if linkage_level == '高度联动':
                score += 20
            elif linkage_level == '中度联动':
                score += 10
            elif linkage_level == '弱联动':
                score += 5

            # 热度评分
            heat_level = heat.get('heat_level', '')
            if heat_level == '极热':
                score += 25
            elif heat_level == '较热':
                score += 15
            elif heat_level == '温热':
                score += 10
            elif heat_level == '偏冷':
                score -= 10
            elif heat_level == '冰冷':
                score -= 20

            # 资金流向评分
            flow_direction = capital.get('flow_direction', '')
            flow_intensity = capital.get('flow_intensity', '')

            if flow_direction == '净流入':
                if flow_intensity == '强烈':
                    score += 20
                elif flow_intensity == '中等':
                    score += 10
                else:
                    score += 5
            elif flow_direction == '净流出':
                if flow_intensity == '强烈':
                    score -= 20
                elif flow_intensity == '中等':
                    score -= 10
                else:
                    score -= 5

            # 龙头跟风评分
            leaders = leader_follower.get('leaders', [])
            followers = leader_follower.get('followers', [])

            if len(leaders) > 0 and len(followers) > 2:
                score += 15  # 有明确的龙头带动
            elif len(leaders) > 0:
                score += 10
            elif len(followers) > 3:
                score += 5

            return max(0, min(100, score))

        except Exception as e:
            logger.error(f"计算板块综合评分失败: {e}")
            return 50

    def _extract_sector_findings(self, linkage: Dict, heat: Dict, capital: Dict,
                               leader_follower: Dict, target_stock: str) -> List[str]:
        """提取板块关键发现"""
        findings = []

        # 联动发现
        linkage_level = linkage.get('linkage_level', '')
        avg_correlation = linkage.get('avg_correlation', 0)
        findings.append(f"板块联动: {linkage_level}, 平均相关性{avg_correlation}")

        # 热度发现
        heat_level = heat.get('heat_level', '')
        heat_trend = heat.get('heat_trend', {}).get('trend_direction', '')
        findings.append(f"概念热度: {heat_level}, 趋势{heat_trend}")

        # 资金发现
        flow_direction = capital.get('flow_direction', '')
        flow_intensity = capital.get('flow_intensity', '')
        findings.append(f"资金流向: {flow_direction}, 强度{flow_intensity}")

        # 角色发现
        target_role = leader_follower.get('target_stock_role', {})
        if target_role:
            role = target_role.get('role', '')
            rank = target_role.get('rank', 0)
            if rank > 0:
                findings.append(f"目标股票: {role}第{rank}位")
            else:
                findings.append(f"目标股票: {role}")

        return findings[:4]  # 返回前4个关键发现

    def _generate_sector_signals(self, linkage: Dict, heat: Dict, capital: Dict, leader_follower: Dict) -> List[str]:
        """生成板块信号"""
        signals = []

        # 基于热度的信号
        heat_level = heat.get('heat_level', '')
        heat_trend = heat.get('heat_trend', {}).get('trend_direction', '')

        if heat_level in ['极热', '较热'] and heat_trend == '升温':
            signals.append("板块热度极高且持续升温，关注追涨风险")
        elif heat_level == '温热' and heat_trend == '升温':
            signals.append("板块热度温和上升，可能存在机会")
        elif heat_level in ['偏冷', '冰冷'] and heat_trend == '升温':
            signals.append("板块从低位升温，可能是底部机会")

        # 基于资金流向的信号
        flow_direction = capital.get('flow_direction', '')
        flow_intensity = capital.get('flow_intensity', '')

        if flow_direction == '净流入' and flow_intensity in ['强烈', '中等']:
            signals.append("板块资金净流入明显，资金关注度高")
        elif flow_direction == '净流出' and flow_intensity == '强烈':
            signals.append("板块资金大幅流出，需要谨慎")

        # 基于龙头跟风的信号
        leaders = leader_follower.get('leaders', [])
        leadership_pattern = leader_follower.get('leadership_analysis', {})

        if len(leaders) > 0:
            leadership_strength = leadership_pattern.get('leader_pattern', {}).get('leadership_strength', '')
            if leadership_strength == '强':
                signals.append("板块龙头股领导力强，板块效应明显")
            elif leadership_strength == '中':
                signals.append("板块有龙头股带动，关注跟风机会")

        return signals

    def _generate_sector_suggestions(self, score: int, linkage: Dict, heat: Dict, capital: Dict,
                                   leader_follower: Dict, target_stock: str) -> List[str]:
        """生成板块操作建议"""
        suggestions = []

        # 基于综合评分的建议
        if score >= 80:
            suggestions.append("板块综合评分很高，可以积极关注板块机会")
        elif score >= 60:
            suggestions.append("板块综合评分中等偏上，可以适度参与")
        elif score >= 40:
            suggestions.append("板块综合评分一般，建议谨慎观察")
        else:
            suggestions.append("板块综合评分较低，建议回避或等待")

        # 基于目标股票角色的建议
        target_role = leader_follower.get('target_stock_role', {})
        if target_role:
            role = target_role.get('role', '')
            if role == '龙头股':
                suggestions.append("目标股票为板块龙头，可以重点关注")
            elif role == '跟风股':
                suggestions.append("目标股票为跟风股，建议关注龙头股动向")
            elif role == '独立个股':
                suggestions.append("目标股票走势相对独立，板块效应有限")

        # 基于热度趋势的建议
        heat_trend = heat.get('heat_trend', {}).get('trend_direction', '')
        if heat_trend == '升温':
            suggestions.append("板块热度上升，可以关注趋势延续")
        elif heat_trend == '降温':
            suggestions.append("板块热度下降，注意及时止盈")

        return suggestions[:3]  # 返回前3个建议

    def _identify_sector_risks(self, linkage: Dict, heat: Dict, capital: Dict, leader_follower: Dict) -> List[str]:
        """识别板块风险"""
        risks = []

        # 热度风险
        heat_level = heat.get('heat_level', '')
        if heat_level == '极热':
            risks.append("板块热度过高，存在泡沫风险")
        elif heat_level == '冰冷':
            risks.append("板块热度极低，缺乏资金关注")

        # 资金风险
        flow_direction = capital.get('flow_direction', '')
        flow_intensity = capital.get('flow_intensity', '')

        if flow_direction == '净流出' and flow_intensity in ['强烈', '中等']:
            risks.append("板块资金持续流出，下跌风险较大")

        # 联动风险
        linkage_level = linkage.get('linkage_level', '')
        if linkage_level == '独立走势':
            risks.append("板块内个股走势分化，缺乏联动效应")

        # 龙头风险
        leaders = leader_follower.get('leaders', [])
        if len(leaders) == 0:
            risks.append("板块缺乏明确龙头，难以形成持续行情")

        return risks


# 创建全局实例
sector_analyzer = SectorLinkageAnalyzer()


def test_sector_linkage_analyzer():
    """测试板块联动分析器"""
    print("🚀 测试板块联动分析器")
    print("=" * 60)

    # 测试股票代码
    test_codes = ["000001", "601519", "300229"]

    for stock_code in test_codes:
        print(f"\n📊 测试股票: {stock_code}")
        print("-" * 40)

        try:
            result = sector_analyzer.get_sector_linkage_analysis(stock_code)

            if result.get('data_available', False):
                print(f"✅ 板块联动分析成功")

                # 显示基本信息
                stock_info = result.get('stock_info', {})
                print(f"📋 股票信息:")
                print(f"  股票名称: {stock_info.get('stock_name', '未知')}")
                print(f"  所属行业: {stock_info.get('industry', '未知')}")
                print(f"  概念板块: {', '.join(stock_info.get('sectors', []))}")
                print(f"  同板块股票数: {result.get('sector_stocks_count', 0)}")

                # 显示综合评估
                assessment = result.get('comprehensive_assessment', {})
                if assessment:
                    print(f"\n📈 综合评估:")
                    print(f"  综合评分: {assessment.get('overall_score', 0)}/100")

                    key_findings = assessment.get('key_findings', [])
                    if key_findings:
                        print(f"  关键发现:")
                        for finding in key_findings:
                            print(f"    • {finding}")

                    sector_signals = assessment.get('sector_signals', [])
                    if sector_signals:
                        print(f"  板块信号:")
                        for signal in sector_signals[:2]:
                            print(f"    • {signal}")

                    suggestions = assessment.get('operation_suggestions', [])
                    if suggestions:
                        print(f"  操作建议:")
                        for suggestion in suggestions[:2]:
                            print(f"    • {suggestion}")

                print(f"✅ {stock_code} 分析完成")
            else:
                print(f"❌ {stock_code} 分析失败: {result.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ {stock_code} 测试失败: {str(e)}")

    print(f"\n🎯 板块联动分析器测试完成")


if __name__ == "__main__":
    test_sector_linkage_analyzer()
