#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
多数据源股票数据获取器
支持AKShare、efinance、easyquotation、腾讯财经等多个数据源
提供自动切换和数据验证功能
"""

import time
import pandas as pd
import requests
from datetime import datetime
from typing import List, Dict, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MultiSourceDataFetcher:
    """多数据源股票数据获取器"""
    
    def __init__(self):
        """初始化"""
        self.last_request_time = 0
        self.min_request_interval = 2.0  # 最小请求间隔（秒）
        
        # 数据源可用性检查
        self.sources_available = {
            'akshare': self._check_akshare(),
            'efinance': self._check_efinance(),
            'easyquotation': self._check_easyquotation(),
            'tencent_direct': True,  # 直接API，通常可用
        }
        
        print("📊 多数据源初始化完成:")
        for source, available in self.sources_available.items():
            status = "✅" if available else "❌"
            print(f"   {status} {source}")
    
    def _check_akshare(self) -> bool:
        """检查AKShare可用性"""
        try:
            import akshare as ak
            return True
        except ImportError:
            return False
    
    def _check_efinance(self) -> bool:
        """检查efinance可用性"""
        try:
            import efinance as ef
            return True
        except ImportError:
            return False
    
    def _check_easyquotation(self) -> bool:
        """检查easyquotation可用性"""
        try:
            import easyquotation
            return True
        except ImportError:
            return False
    
    def _wait_for_rate_limit(self):
        """等待以满足请求频率限制"""
        current_time = time.time()
        time_since_last_request = current_time - self.last_request_time
        
        if time_since_last_request < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last_request
            time.sleep(wait_time)
        
        self.last_request_time = time.time()
    
    def _standardize_data(self, data: Dict, source: str) -> Dict:
        """标准化不同数据源的数据格式"""
        try:
            # 确保所有必需字段存在
            standard_data = {
                'stock_code': data.get('code', ''),
                'stock_name': data.get('name', ''),
                'current_price': float(data.get('current_price', 0)),
                'open': float(data.get('open', 0)),
                'high': float(data.get('high', 0)),
                'low': float(data.get('low', 0)),
                'prev_close': float(data.get('prev_close', 0)),
                'change': float(data.get('change', 0)),
                'change_pct': float(data.get('change_pct', 0)),
                'volume': int(data.get('volume', 0)),
                'amount': float(data.get('amount', 0)),
                'update_time': data.get('timestamp', datetime.now().strftime('%H:%M:%S')),
                'data_source': source
            }
            
            # 数据验证和修正
            if standard_data['prev_close'] > 0 and standard_data['current_price'] > 0:
                # 重新计算涨跌额和涨跌幅，确保一致性
                calculated_change = standard_data['current_price'] - standard_data['prev_close']
                calculated_change_pct = (calculated_change / standard_data['prev_close']) * 100
                
                # 如果计算值与原值差异较大，使用计算值
                if abs(calculated_change - standard_data['change']) > 0.1:
                    standard_data['change'] = calculated_change
                if abs(calculated_change_pct - standard_data['change_pct']) > 0.5:
                    standard_data['change_pct'] = calculated_change_pct
            
            return standard_data
            
        except Exception as e:
            logger.error(f"数据标准化失败: {e}")
            return data
    
    def _fetch_akshare(self, codes: List[str]) -> Optional[pd.DataFrame]:
        """使用AKShare获取数据"""
        try:
            if not self.sources_available['akshare']:
                return None
            
            import akshare as ak
            
            print("📊 使用AKShare获取数据...")
            df = ak.stock_zh_a_spot_em()
            
            results = []
            for code in codes:
                stock_data = df[df['代码'] == code]
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    data = {
                        'code': code,
                        'name': row['名称'],
                        'current_price': row['最新价'],
                        'open': row['今开'],
                        'high': row['最高'],
                        'low': row['最低'],
                        'prev_close': row['昨收'],
                        'change': row['涨跌额'],
                        'change_pct': row['涨跌幅'],
                        'volume': row['成交量'],
                        'amount': row['成交额'],
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }
                    results.append(self._standardize_data(data, 'akshare'))
            
            return pd.DataFrame(results) if results else pd.DataFrame()
            
        except Exception as e:
            logger.error(f"AKShare获取失败: {e}")
            return None
    
    def _fetch_efinance(self, codes: List[str]) -> Optional[pd.DataFrame]:
        """使用efinance获取数据"""
        try:
            if not self.sources_available['efinance']:
                return None
            
            import efinance as ef
            
            print("📊 使用efinance获取数据...")
            all_stocks = ef.stock.get_realtime_quotes()
            
            results = []
            for code in codes:
                stock_data = all_stocks[all_stocks['股票代码'] == code]
                if not stock_data.empty:
                    row = stock_data.iloc[0]
                    data = {
                        'code': code,
                        'name': row.get('股票名称', ''),
                        'current_price': row.get('最新价', 0),
                        'open': row.get('今开', 0),
                        'high': row.get('最高', 0),
                        'low': row.get('最低', 0),
                        'prev_close': row.get('昨收', 0),
                        'change': row.get('涨跌额', 0),
                        'change_pct': row.get('涨跌幅', 0),
                        'volume': row.get('成交量', 0),
                        'amount': row.get('成交额', 0),
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }
                    results.append(self._standardize_data(data, 'efinance'))
            
            return pd.DataFrame(results) if results else pd.DataFrame()
            
        except Exception as e:
            logger.error(f"efinance获取失败: {e}")
            return None
    
    def _fetch_easyquotation(self, codes: List[str]) -> Optional[pd.DataFrame]:
        """使用easyquotation获取数据"""
        try:
            if not self.sources_available['easyquotation']:
                return None
            
            import easyquotation
            
            print("📊 使用easyquotation获取数据...")
            quotation = easyquotation.use('sina')
            data_dict = quotation.real(codes)
            
            results = []
            for code in codes:
                if code in data_dict:
                    quote = data_dict[code]
                    data = {
                        'code': code,
                        'name': quote.get('name', ''),
                        'current_price': float(quote.get('now', 0)),
                        'open': float(quote.get('open', 0)),
                        'high': float(quote.get('high', 0)),
                        'low': float(quote.get('low', 0)),
                        'prev_close': float(quote.get('close', 0)),
                        'change': float(quote.get('now', 0)) - float(quote.get('close', 0)),
                        'change_pct': ((float(quote.get('now', 0)) - float(quote.get('close', 0))) / float(quote.get('close', 1))) * 100,
                        'volume': int(quote.get('volume', 0)),
                        'amount': float(quote.get('turnover', 0)),
                        'timestamp': datetime.now().strftime('%H:%M:%S')
                    }
                    results.append(self._standardize_data(data, 'easyquotation'))
            
            return pd.DataFrame(results) if results else pd.DataFrame()
            
        except Exception as e:
            logger.error(f"easyquotation获取失败: {e}")
            return None
    
    def _fetch_tencent_direct(self, codes: List[str]) -> Optional[pd.DataFrame]:
        """使用腾讯财经直接API获取数据"""
        try:
            print("📊 使用腾讯财经直接API获取数据...")
            
            results = []
            for code in codes:
                try:
                    market_code = f"sh{code}" if code.startswith('6') else f"sz{code}"
                    url = f"https://qt.gtimg.cn/q={market_code}"
                    
                    response = requests.get(url, timeout=10)
                    
                    if response.status_code == 200 and response.text and '~' in response.text:
                        parts = response.text.split('~')
                        
                        if len(parts) >= 32:
                            current_price = float(parts[3]) if parts[3] else 0
                            prev_close = float(parts[4]) if parts[4] else 0
                            
                            data = {
                                'code': code,
                                'name': parts[1],
                                'current_price': current_price,
                                'open': float(parts[5]) if parts[5] else 0,
                                'high': float(parts[33]) if len(parts) > 33 and parts[33] else 0,
                                'low': float(parts[34]) if len(parts) > 34 and parts[34] else 0,
                                'prev_close': prev_close,
                                'change': current_price - prev_close,
                                'change_pct': ((current_price - prev_close) / prev_close * 100) if prev_close > 0 else 0,
                                'volume': int(float(parts[6]) * 100) if parts[6] else 0,
                                'amount': float(parts[37]) * 10000 if len(parts) > 37 and parts[37] else 0,
                                'timestamp': datetime.now().strftime('%H:%M:%S')
                            }
                            results.append(self._standardize_data(data, 'tencent_direct'))
                    
                    time.sleep(0.1)  # 避免请求过快
                    
                except Exception as e:
                    logger.warning(f"腾讯API获取{code}失败: {e}")
                    continue
            
            return pd.DataFrame(results) if results else pd.DataFrame()
            
        except Exception as e:
            logger.error(f"腾讯财经API获取失败: {e}")
            return None
    
    def get_realtime_quote(self, codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据 - 多数据源自动切换"""
        try:
            # 应用请求频率控制
            self._wait_for_rate_limit()
            
            # 数据源优先级（基于测试结果）
            sources = [
                ('akshare', self._fetch_akshare),
                ('efinance', self._fetch_efinance),
                ('tencent_direct', self._fetch_tencent_direct),
                ('easyquotation', self._fetch_easyquotation),
            ]
            
            for source_name, fetch_func in sources:
                if not self.sources_available.get(source_name, False):
                    continue
                
                try:
                    print(f"🔄 尝试使用{source_name}获取数据...")
                    result_df = fetch_func(codes)
                    
                    if result_df is not None and not result_df.empty:
                        print(f"✅ {source_name}获取成功: {len(result_df)}只股票")
                        return result_df
                    else:
                        print(f"⚠️ {source_name}返回空数据")
                        
                except Exception as e:
                    print(f"⚠️ {source_name}获取失败: {e}")
                    # 标记该数据源暂时不可用
                    self.sources_available[source_name] = False
                    continue
            
            print("❌ 所有数据源都失败")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取实时行情完全失败: {e}")
            return pd.DataFrame()

# 创建全局实例
multi_source_fetcher = MultiSourceDataFetcher()
