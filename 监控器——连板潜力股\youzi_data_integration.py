#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游资数据集成模块
集成youzi_seats_data文件夹中的游资数据，实现股票名称到代码的映射
用于龙虎榜分析和资金潜伏检测
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class YouziDataIntegration:
    """游资数据集成器"""
    
    def __init__(self, data_dir: str = "../youzi_seats_data"):
        """
        初始化游资数据集成器
        
        Args:
            data_dir: 游资数据目录
        """
        self.data_dir = Path(data_dir)
        self.stock_name_to_code_cache = {}  # 股票名称到代码的缓存
        self.code_to_name_cache = {}        # 股票代码到名称的缓存
        
        # 初始化股票名称映射
        self._initialize_stock_mapping()
        
        logger.info(f"✅ 游资数据集成器初始化完成，数据目录: {self.data_dir}")
    
    def _initialize_stock_mapping(self):
        """初始化股票名称到代码的映射"""
        try:
            # 尝试从AKShare获取股票列表建立映射
            try:
                import akshare as ak
                stock_list = ak.stock_zh_a_spot_em()
                
                for _, row in stock_list.iterrows():
                    code = row['代码']
                    name = row['名称']
                    self.stock_name_to_code_cache[name] = code
                    self.code_to_name_cache[code] = name
                
                logger.info(f"✅ 从AKShare获取股票映射: {len(self.stock_name_to_code_cache)} 只股票")
                
            except Exception as e:
                logger.warning(f"⚠️ 无法从AKShare获取股票列表: {e}")
                # 使用备用的常见股票映射
                self._load_backup_stock_mapping()
                
        except Exception as e:
            logger.error(f"初始化股票映射失败: {e}")
    
    def _load_backup_stock_mapping(self):
        """加载备用股票映射（常见股票）"""
        backup_mapping = {
            # 这里可以添加一些常见的股票名称到代码的映射
            "许继电气": "000400",
            "中电港": "301191",
            "智微智能": "001339",
            "平安电工": "300989",
            "豫能控股": "001896",
            "中材科技": "002080",
            "恒宝股份": "002104",
            "东方锆业": "002167",
            "建设工业": "600515",
            "中化岩土": "002542",
            "康弘药业": "002773",
            "中大力德": "002896",
            "安靠智电": "300617",
            "长盛轴承": "300718",
            "新余国科": "300722",
            "一品红": "300723",
            "铜冠铜箔": "301217",
            # 可以根据需要继续添加
        }
        
        self.stock_name_to_code_cache.update(backup_mapping)
        for name, code in backup_mapping.items():
            self.code_to_name_cache[code] = name
        
        logger.info(f"✅ 加载备用股票映射: {len(backup_mapping)} 只股票")
    
    def get_stock_code_by_name(self, stock_name: str) -> Optional[str]:
        """
        根据股票名称获取股票代码
        
        Args:
            stock_name: 股票名称
            
        Returns:
            Optional[str]: 股票代码，如果找不到返回None
        """
        return self.stock_name_to_code_cache.get(stock_name)
    
    def get_stock_name_by_code(self, stock_code: str) -> Optional[str]:
        """
        根据股票代码获取股票名称
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Optional[str]: 股票名称，如果找不到返回None
        """
        return self.code_to_name_cache.get(stock_code)
    
    def load_youzi_data(self, date: str = None) -> Optional[Dict]:
        """
        加载指定日期的游资数据
        
        Args:
            date: 日期字符串，格式YYYYMMDD，如果为None则加载最新数据
            
        Returns:
            Optional[Dict]: 游资数据，如果找不到返回None
        """
        try:
            if date is None:
                # 获取最新的数据文件
                json_files = list(self.data_dir.glob("*.json"))
                if not json_files:
                    logger.warning("未找到游资数据文件")
                    return None
                
                # 按文件名排序，获取最新的
                latest_file = max(json_files, key=lambda x: x.stem)
                date = latest_file.stem
            
            data_file = self.data_dir / f"{date}.json"
            
            if not data_file.exists():
                logger.warning(f"游资数据文件不存在: {data_file}")
                return None
            
            with open(data_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            logger.info(f"✅ 加载游资数据: {date}")
            return data
            
        except Exception as e:
            logger.error(f"加载游资数据失败 {date}: {e}")
            return None
    
    def analyze_stock_youzi_activity(self, stock_code: str, days: int = 5) -> Dict:
        """
        分析指定股票的游资活动情况
        
        Args:
            stock_code: 股票代码
            days: 分析天数
            
        Returns:
            Dict: 游资活动分析结果
        """
        try:
            stock_name = self.get_stock_name_by_code(stock_code)
            if not stock_name:
                return {
                    'stock_code': stock_code,
                    'stock_name': None,
                    'has_youzi_activity': False,
                    'error': '未找到股票名称映射'
                }
            
            # 获取最近几天的数据
            end_date = datetime.now()
            youzi_activities = []
            
            for i in range(days):
                check_date = end_date - timedelta(days=i)
                date_str = check_date.strftime('%Y%m%d')
                
                youzi_data = self.load_youzi_data(date_str)
                if not youzi_data:
                    continue
                
                # 分析该日期的游资活动
                daily_activity = self._analyze_daily_youzi_activity(stock_name, youzi_data, date_str)
                if daily_activity['has_activity']:
                    youzi_activities.append(daily_activity)
            
            # 综合分析
            analysis_result = self._summarize_youzi_activity(stock_code, stock_name, youzi_activities)
            
            return analysis_result
            
        except Exception as e:
            logger.error(f"分析游资活动失败 {stock_code}: {e}")
            return {
                'stock_code': stock_code,
                'has_youzi_activity': False,
                'error': str(e)
            }
    
    def _analyze_daily_youzi_activity(self, stock_name: str, youzi_data: Dict, date: str) -> Dict:
        """
        分析单日游资活动
        
        Args:
            stock_name: 股票名称
            youzi_data: 游资数据
            date: 日期
            
        Returns:
            Dict: 单日活动分析结果
        """
        try:
            activity = {
                'date': date,
                'has_activity': False,
                'buy_seats': [],
                'sell_seats': [],
                'net_buy_amount': 0,
                'famous_seats': [],
                'institution_seats': []
            }
            
            seats_detail = youzi_data.get('processed_data', {}).get('seats_detail', [])
            
            for seat in seats_detail:
                buy_stocks = seat.get('buy_stocks', '')
                sell_stocks = seat.get('sell_stocks', '')
                
                # 检查是否涉及目标股票
                is_buy_target = stock_name in buy_stocks if buy_stocks else False
                is_sell_target = stock_name in sell_stocks if sell_stocks else False
                
                if is_buy_target or is_sell_target:
                    activity['has_activity'] = True
                    
                    seat_info = {
                        'seat_name': seat.get('seat_name', ''),
                        'seat_type': seat.get('seat_type', ''),
                        'buy_amount': seat.get('buy_amount', 0),
                        'sell_amount': seat.get('sell_amount', 0),
                        'net_amount': seat.get('net_amount', 0),
                        'is_famous': seat.get('is_famous', False)
                    }
                    
                    if is_buy_target:
                        activity['buy_seats'].append(seat_info)
                        activity['net_buy_amount'] += seat_info['net_amount']
                    
                    if is_sell_target:
                        activity['sell_seats'].append(seat_info)
                    
                    # 识别知名席位和机构席位
                    seat_name = seat_info['seat_name']
                    if seat_info['is_famous'] or any(keyword in seat_name for keyword in ['机构专用', '深股通专用', '沪股通专用']):
                        if is_buy_target:
                            activity['famous_seats'].append(seat_info)
                    
                    if '机构专用' in seat_name:
                        activity['institution_seats'].append(seat_info)
            
            return activity
            
        except Exception as e:
            logger.error(f"分析单日游资活动失败: {e}")
            return {'date': date, 'has_activity': False, 'error': str(e)}
    
    def _summarize_youzi_activity(self, stock_code: str, stock_name: str, activities: List[Dict]) -> Dict:
        """
        汇总游资活动分析
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            activities: 活动列表
            
        Returns:
            Dict: 汇总分析结果
        """
        try:
            summary = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'has_youzi_activity': len(activities) > 0,
                'activity_days': len(activities),
                'total_net_buy': sum(act.get('net_buy_amount', 0) for act in activities),
                'famous_seats_count': sum(len(act.get('famous_seats', [])) for act in activities),
                'institution_seats_count': sum(len(act.get('institution_seats', [])) for act in activities),
                'recent_activities': activities[:3],  # 最近3天的活动
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 资金潜伏评分
            hidden_capital_score = 0
            
            # 1. 机构席位加分
            if summary['institution_seats_count'] > 0:
                hidden_capital_score += 0.3
            
            # 2. 知名席位加分
            if summary['famous_seats_count'] > 0:
                hidden_capital_score += 0.2
            
            # 3. 净买入金额加分
            if summary['total_net_buy'] > 50000000:  # 5000万以上
                hidden_capital_score += 0.3
            elif summary['total_net_buy'] > 10000000:  # 1000万以上
                hidden_capital_score += 0.2
            
            # 4. 活动天数加分
            if summary['activity_days'] >= 3:
                hidden_capital_score += 0.2
            elif summary['activity_days'] >= 2:
                hidden_capital_score += 0.1
            
            summary['hidden_capital_score'] = min(1.0, hidden_capital_score)
            summary['has_hidden_capital'] = hidden_capital_score > 0.5
            
            return summary
            
        except Exception as e:
            logger.error(f"汇总游资活动失败: {e}")
            return {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'has_youzi_activity': False,
                'error': str(e)
            }
    
    def get_dragon_tiger_signals(self, stock_code: str) -> List[str]:
        """
        获取龙虎榜暗号信号
        
        Args:
            stock_code: 股票代码
            
        Returns:
            List[str]: 检测到的暗号信号列表
        """
        try:
            activity_analysis = self.analyze_stock_youzi_activity(stock_code, days=3)
            
            signals = []
            
            # 检查机构专用席位
            if activity_analysis.get('institution_seats_count', 0) > 0:
                signals.append('机构专用')
            
            # 检查深股通/沪股通
            recent_activities = activity_analysis.get('recent_activities', [])
            for activity in recent_activities:
                for seat in activity.get('famous_seats', []):
                    seat_name = seat.get('seat_name', '')
                    if '深股通专用' in seat_name:
                        signals.append('深股通专用')
                    elif '沪股通专用' in seat_name:
                        signals.append('沪股通专用')
            
            # 检查量化基金（通过席位名称模糊匹配）
            for activity in recent_activities:
                for seat in activity.get('buy_seats', []):
                    seat_name = seat.get('seat_name', '')
                    if any(keyword in seat_name for keyword in ['量化', '基金', '资管']):
                        signals.append('量化基金')
                        break
            
            return list(set(signals))  # 去重
            
        except Exception as e:
            logger.error(f"获取龙虎榜信号失败 {stock_code}: {e}")
            return []

# 创建全局实例
youzi_data_integration = YouziDataIntegration("../youzi_seats_data")
