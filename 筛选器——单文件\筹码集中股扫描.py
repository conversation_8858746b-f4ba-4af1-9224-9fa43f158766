#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
筹码集中度上升股票筛选测试
基于收盘后基础数据的零Level2数据解决方案
复制调查潜在操作股按钮的前面步骤代码
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class ChipConcentrationAnalyzer:
    """筹码集中度分析器"""

    def __init__(self):
        # 修改为指向上级目录的路径
        self.data_dir = os.path.join("..", "youzi_seats_data")
        self.current_date = datetime.now()
        self.stock_name_to_code = {}
        self.market_data = None
        self.akshare_available = True

        # 简化AKShare检查
        self.akshare_available = True
        print("✅ AKShare 模块设置为可用")
        
    def run_analysis(self):
        """运行完整分析流程"""
        print("🚀 开始筹码集中度上升股票筛选分析...")
        print("=" * 60)

        try:
            # 第一步：基础筛选
            print("\n📊 第一步：执行基础筛选...")
            basic_candidates = self._apply_basic_screening()
        except Exception as e:
            print(f"❌ 基础筛选异常: {e}")
            return []
        
        if not basic_candidates:
            print("❌ 基础筛选后无候选股票")
            return []
        
        print(f"✅ 基础筛选完成，获得 {len(basic_candidates)} 只候选股票")
        
        # 第二步：筹码集中度分析
        print("\n🔍 第二步：筹码集中度分析...")
        concentration_candidates = self._analyze_chip_concentration(basic_candidates)
        
        if not concentration_candidates:
            print("❌ 筹码集中度分析后无候选股票")
            return []
        
        print(f"✅ 筹码集中度分析完成，剩余 {len(concentration_candidates)} 只股票")
        
        # 第三步：二次启动信号检测
        print("\n🎯 第三步：二次启动信号检测...")
        final_candidates = self._detect_rebound_signals(concentration_candidates)
        
        print(f"✅ 最终筛选完成，推荐 {len(final_candidates)} 只股票")
        
        # 第四步：结果展示和导出
        self._display_results(final_candidates)
        self._export_results(final_candidates)
        
        return final_candidates
    
    def _apply_basic_screening(self):
        """基础筛选：市场范围+市值+龙虎榜历史（复制主应用方法）"""
        try:
            print("\n📊 第一步：执行基础筛选...")

            # 1. 获取全市场股票数据（使用新浪财经，包含市值）
            print("📊 获取全市场股票数据（包含市值）...")
            market_data_df = self._get_market_data_with_filtering_fallback()

            if market_data_df is None or market_data_df.empty:
                print("❌ 无法获取全市场股票数据")
                return []

            print(f"✅ 获取全市场股票: {len(market_data_df)} 只（已包含市值信息）")

            # 转换DataFrame为字典列表格式
            market_data = market_data_df.to_dict('records')
            print(f"✅ 数据格式转换完成: DataFrame → 字典列表")

            # 2. 基础筛选（市值+涨停历史，无额外数据获取）
            print("📊 执行基础筛选（市值+涨停历史）...")
            basic_candidates = self._apply_basic_screening_with_market_data(market_data)

            if not basic_candidates:
                print("❌ 基础筛选后无候选股票")
                return []

            print(f"✅ 基础筛选完成: {len(basic_candidates)} 只候选股票")
            return basic_candidates

        except Exception as e:
            print(f"❌ 基础筛选失败: {e}")
            return []

    def _apply_basic_screening_with_market_data(self, market_data):
        """基础筛选：使用预处理的龙虎榜数据与市场数据交集筛选（复制主应用方法）"""
        try:
            print("🔍 基础筛选：预处理龙虎榜数据 + 市值交集筛选...")

            # 步骤1: 预处理龙虎榜数据，获取符合条件的股票
            qualified_dragon_stocks = self._preprocess_dragon_tiger_data()

            if not qualified_dragon_stocks:
                print("❌ 预处理后无符合条件的龙虎榜股票")
                return []

            print(f"✅ 预处理完成: 发现 {len(qualified_dragon_stocks)} 只符合龙虎榜条件的股票")

            # 步骤2: 与市场数据交集筛选（市值30-130亿）- 复制主应用逻辑
            basic_candidates = []
            qualified_codes = {stock['code'] for stock in qualified_dragon_stocks}

            # 调试：查看第一个股票的数据结构和市值格式
            if market_data:
                sample_stock = market_data[0]
                print(f"📊 数据结构示例: {list(sample_stock.keys())[:15]}")

                # 详细检查市值字段
                print(f"📊 市值字段详细信息:")
                print(f"    总市值: {sample_stock.get('总市值', 'N/A')} (类型: {type(sample_stock.get('总市值', 'N/A'))})")
                print(f"    market_cap: {sample_stock.get('market_cap', 'N/A')} (类型: {type(sample_stock.get('market_cap', 'N/A'))})")
                print(f"    市值: {sample_stock.get('市值', 'N/A')} (类型: {type(sample_stock.get('市值', 'N/A'))})")
                print(f"    流通市值: {sample_stock.get('流通市值', 'N/A')} (类型: {type(sample_stock.get('流通市值', 'N/A'))})")

                # 检查股票代码格式
                sample_code = sample_stock.get('代码', '') or sample_stock.get('code', '')
                print(f"📊 股票代码示例: '{sample_code}' (类型: {type(sample_code)})")

                # 显示龙虎榜股票代码样本
                print(f"📊 龙虎榜股票代码样本: {list(qualified_codes)[:5]}")
                print(f"📊 市场数据股票代码样本: {[stock.get('代码', '') or stock.get('code', '') for stock in market_data[:5]]}")

            # 统计各个筛选步骤的数量
            dragon_found_count = 0
            market_scope_passed = 0
            market_cap_passed = 0
            final_passed = 0

            for i, stock in enumerate(market_data):
                try:
                    stock_code_raw = stock.get('代码', '') or stock.get('code', '')
                    stock_name = stock.get('名称', '') or stock.get('name', '')

                    # 标准化股票代码（移除市场前缀）
                    stock_code = self._normalize_stock_code(stock_code_raw)

                    # 步骤1：只处理在龙虎榜候选名单中的股票
                    if stock_code not in qualified_codes:
                        continue
                    dragon_found_count += 1

                    # 步骤2：市场范围筛选（使用统一的检查函数）
                    if not self._check_market_scope(stock_code, stock_name):
                        continue
                    market_scope_passed += 1

                    # 步骤3：获取市值（新浪财经返回的已经是亿元单位）
                    market_cap = (stock.get('总市值', 0) or
                                stock.get('market_cap', 0) or
                                stock.get('市值', 0) or
                                stock.get('流通市值', 0))

                    # 确保市值是数值类型
                    try:
                        market_cap = float(market_cap) if market_cap else 0
                    except (ValueError, TypeError):
                        market_cap = 0

                    # 详细调试：输出所有龙虎榜股票的市值信息
                    print(f"    🔍 龙虎榜股票 {stock_code} {stock_name}: 市值={market_cap:.2f}亿")

                    # 步骤4：市值筛选：30-130亿
                    if not (30 <= market_cap <= 130):
                        print(f"    ❌ 市值不符合: {stock_code} {stock_name} 市值={market_cap:.2f}亿 (不在30-130亿范围)")
                        continue
                    market_cap_passed += 1

                    # 步骤5：获取对应的龙虎榜信息
                    dragon_info = next((s for s in qualified_dragon_stocks if s['code'] == stock_code), None)
                    if not dragon_info:
                        continue
                    final_passed += 1

                    print(f"✅ 匹配成功: {stock_code} {stock_name} (市值{market_cap:.1f}亿, {dragon_info['famous_seats_count']}家知名游资)")

                    # 通过筛选，保存完整数据
                    candidate = {
                        'code': stock_code,
                        'name': stock_name,
                        'market_cap': market_cap,
                        'limit_up_count': dragon_info['appearance_count'],
                        'dragon_tiger_info': dragon_info,  # 保存龙虎榜信息
                        'original_data': stock,
                        # 初始化导出字段
                        'enhanced_score': 0,
                        'youzi_score': 0,
                        'minute_score': 0,
                        'volume_price_score': 0,
                        'money_flow_score': 0,
                        'grade': 'D级',
                        'latest_price': stock.get('最新价', 0),
                        'change_pct': stock.get('涨跌幅', 0),
                        'volume': stock.get('成交量', 0),
                        'turnover_rate': stock.get('换手率', 0),
                        'amount': stock.get('成交额', 0),
                        'pe_ratio': stock.get('市盈率-动态', 0)
                    }
                    basic_candidates.append(candidate)

                except Exception as e:
                    print(f"    ⚠️ 处理股票 {stock_code} 失败: {e}")
                    continue

            # 输出详细的筛选统计
            print(f"\n📊 详细筛选统计:")
            print(f"    龙虎榜候选股票总数: {len(qualified_codes)} 只")
            print(f"    在市场数据中找到的龙虎榜股票: {dragon_found_count} 只")
            print(f"    通过市场范围筛选: {market_scope_passed} 只")
            print(f"    通过市值筛选(30-130亿): {market_cap_passed} 只")
            print(f"    最终通过所有筛选: {final_passed} 只")

            print(f"✅ 交集筛选完成: {len(qualified_dragon_stocks)} 龙虎榜股票 × {len(market_data)} 市场股票 → {len(basic_candidates)} 只最终候选")
            return basic_candidates

        except Exception as e:
            print(f"❌ 基础筛选失败: {e}")
            return []
    
    def _get_dragon_tiger_stocks(self):
        """获取符合龙虎榜条件的股票"""
        try:
            # 获取最近15天的交易日
            date_list = self._get_recent_trading_days(15)
            print(f"📅 分析日期范围: {min(date_list)} 到 {max(date_list)}")
            
            # 股票龙虎榜记录汇总
            stock_records = defaultdict(list)
            
            for date_str in date_list:
                file_path = os.path.join(self.data_dir, f"{date_str}.json")
                if not os.path.exists(file_path):
                    continue
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                    
                    seats_detail = data.get('processed_data', {}).get('seats_detail', [])
                    
                    for seat in seats_detail:
                        seat_name = seat.get('seat_name', '')
                        is_famous = seat.get('is_famous', False)
                        buy_stocks = seat.get('buy_stocks', '')
                        
                        # 只处理买入股票且是知名游资
                        if buy_stocks and is_famous:
                            stock_names = buy_stocks.split(' ')
                            for stock_name in stock_names:
                                if stock_name.strip():
                                    stock_records[stock_name].append({
                                        'date': date_str,
                                        'seat_name': seat_name,
                                        'is_famous': is_famous
                                    })
                
                except Exception as e:
                    continue
            
            # 筛选符合条件的股票
            qualified_stocks = []
            
            for stock_name, records in stock_records.items():
                # 统计知名游资数量
                famous_seats = set()
                all_dates = set()
                
                for record in records:
                    all_dates.add(record['date'])
                    if record['is_famous']:
                        famous_seats.add(record['seat_name'])
                
                # 条件1: 出现≥2家知名游资（按您的要求修改）
                if len(famous_seats) < 2:
                    continue
                
                # 条件2: 计算冷却期
                latest_date = max(all_dates)
                latest_datetime = datetime.strptime(latest_date, '%Y%m%d')
                cooling_days = (self.current_date - latest_datetime).days
                
                # 条件3: 冷却期10-20天
                if not (10 <= cooling_days <= 20):
                    continue
                
                qualified_stocks.append({
                    'stock_name': stock_name,
                    'famous_seats_count': len(famous_seats),
                    'famous_seats': list(famous_seats),
                    'latest_date': latest_date,
                    'cooling_days': cooling_days,
                    'appearance_count': len(all_dates)
                })
            
            # 转换为股票代码
            final_candidates = []
            name_to_code = self._get_stock_name_to_code_mapping()
            
            for stock in qualified_stocks:
                stock_name = stock['stock_name']
                stock_code = name_to_code.get(stock_name)
                
                if stock_code:
                    final_candidates.append({
                        'code': stock_code,
                        'name': stock_name,
                        'famous_seats_count': stock['famous_seats_count'],
                        'famous_seats': stock['famous_seats'],
                        'latest_date': stock['latest_date'],
                        'cooling_days': stock['cooling_days'],
                        'appearance_count': stock['appearance_count']
                    })
            
            return final_candidates

        except Exception as e:
            print(f"❌ 获取龙虎榜数据失败: {e}")
            return []

    def _preprocess_dragon_tiger_data(self):
        """预处理龙虎榜数据（复制主应用方法）"""
        try:
            print("🔍 预处理龙虎榜数据...")

            # 获取过去半年的交易日（约120个交易日）
            date_list = self._get_recent_trading_days(120)
            print(f"📅 分析日期范围: {min(date_list)} 到 {max(date_list)} (半年约{len(date_list)}个交易日)")

            # 股票龙虎榜记录汇总
            stock_records = defaultdict(list)

            for date_str in date_list:
                file_path = os.path.join(self.data_dir, f"{date_str}.json")
                if not os.path.exists(file_path):
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    seats_detail = data.get('processed_data', {}).get('seats_detail', [])

                    for seat in seats_detail:
                        seat_name = seat.get('seat_name', '')
                        is_famous = seat.get('is_famous', False)
                        buy_stocks = seat.get('buy_stocks', '')

                        # 只处理买入股票且是知名游资
                        if buy_stocks and is_famous:
                            stock_names = buy_stocks.split(' ')
                            for stock_name in stock_names:
                                if stock_name.strip():
                                    stock_records[stock_name].append({
                                        'date': date_str,
                                        'seat_name': seat_name,
                                        'is_famous': is_famous
                                    })

                except Exception as e:
                    continue

            # 筛选符合条件的股票
            qualified_stocks = []

            for stock_name, records in stock_records.items():
                # 统计知名游资数量
                famous_seats = set()
                all_dates = set()

                for record in records:
                    all_dates.add(record['date'])
                    if record['is_famous']:
                        famous_seats.add(record['seat_name'])

                # 条件1: 出现≥2家知名游资（按您的要求修改）
                if len(famous_seats) < 2:
                    continue

                # 条件2: 计算冷却期
                latest_date = max(all_dates)
                latest_datetime = datetime.strptime(latest_date, '%Y%m%d')
                cooling_days = (self.current_date - latest_datetime).days

                # 条件3: 冷却期10-20天
                if not (10 <= cooling_days <= 20):
                    continue

                qualified_stocks.append({
                    'stock_name': stock_name,
                    'famous_seats_count': len(famous_seats),
                    'famous_seats': list(famous_seats),
                    'latest_date': latest_date,
                    'cooling_days': cooling_days,
                    'appearance_count': len(all_dates)
                })

            # 转换为股票代码
            final_candidates = []
            name_to_code = self._get_stock_name_to_code_mapping()

            for stock in qualified_stocks:
                stock_name = stock['stock_name']
                stock_code = name_to_code.get(stock_name)

                if stock_code:
                    final_candidates.append({
                        'code': stock_code,
                        'name': stock_name,
                        'famous_seats_count': stock['famous_seats_count'],
                        'famous_seats': stock['famous_seats'],
                        'latest_date': stock['latest_date'],
                        'cooling_days': stock['cooling_days'],
                        'appearance_count': stock['appearance_count']
                    })

            return final_candidates

        except Exception as e:
            print(f"❌ 预处理龙虎榜数据失败: {e}")
            return []

    def _normalize_stock_code(self, code):
        """标准化股票代码（移除市场前缀）"""
        if not code:
            return ''

        # 移除常见的市场前缀
        code = str(code).lower()

        # 移除北交所前缀
        if code.startswith('bj'):
            code = code[2:]

        # 移除其他可能的前缀
        prefixes = ['sh', 'sz', 'hk']
        for prefix in prefixes:
            if code.startswith(prefix):
                code = code[len(prefix):]
                break

        # 确保是6位数字代码
        if code.isdigit() and len(code) == 6:
            return code

        return code

    def _get_recent_trading_days(self, days):
        """获取最近N个交易日"""
        date_list = []
        current = self.current_date

        # 对于大天数（如120天），需要扩大搜索范围
        search_range = max(days * 2, 200) if days > 50 else days * 2

        for i in range(search_range):
            check_date = current - timedelta(days=i)
            # 跳过周末
            if check_date.weekday() < 5:  # 0-4是周一到周五
                date_str = check_date.strftime('%Y%m%d')
                date_list.append(date_str)
                if len(date_list) >= days:
                    break

        return sorted(date_list)

    def _get_stock_name_to_code_mapping(self):
        """获取股票名称到代码的映射"""
        try:
            # 使用缓存避免重复获取
            if self.stock_name_to_code:
                return self.stock_name_to_code

            # 尝试使用AKShare获取股票列表
            try:
                import akshare as ak
                stock_list = ak.stock_info_a_code_name()

                name_to_code = {}
                for _, row in stock_list.iterrows():
                    code = row['code']
                    name = row['name']
                    name_to_code[name] = code

                self.stock_name_to_code = name_to_code
                return name_to_code

            except Exception as e:
                print(f"⚠️ 获取股票映射失败，使用备用映射: {e}")

                # 备用映射（常见股票）
                backup_mapping = {
                    '海航科技': '600751',
                    '新通联': '603022',
                    '元隆雅图': '002878',
                    '四创电子': '600990',
                    '万里马': '300591',
                    '大位科技': '600589',
                    '天际股份': '002759',
                    '建业股份': '603948',
                    '中远海科': '002401',
                    '华鑫股份': '600621'
                }
                self.stock_name_to_code = backup_mapping
                return backup_mapping

        except Exception as e:
            print(f"❌ 获取股票名称映射失败: {e}")
            return {}

    def _get_market_data_with_filtering_fallback(self):
        """获取全市场股票数据（复制主应用方法）"""
        try:
            print("📊 获取全市场股票数据（包含市值信息）...")

            # 方案1：尝试使用新浪财经数据
            try:
                market_df = self._get_sina_market_data()
                if market_df is not None and not market_df.empty:
                    print(f"✅ 新浪财经数据获取成功: {len(market_df)} 只股票")
                    return market_df
            except Exception as e:
                print(f"⚠️ 新浪财经数据获取失败: {e}")

            # 方案2：尝试使用AKShare数据
            if self.akshare_available:
                try:
                    market_df = self._get_akshare_market_data()
                    if market_df is not None and not market_df.empty:
                        print(f"✅ AKShare数据获取成功: {len(market_df)} 只股票")
                        return market_df
                except Exception as e:
                    print(f"⚠️ AKShare数据获取失败: {e}")

            # 方案3：使用备用数据
            print("🔄 使用备用数据方案...")
            return self._get_backup_market_data()

        except Exception as e:
            print(f"❌ 获取市场数据失败: {e}")
            return None

    def _get_sina_market_data(self):
        """从新浪财经获取全市场数据（分页获取）"""
        try:
            import requests
            import re
            import time

            print("📡 尝试从新浪财经获取全市场数据...")

            all_data = []
            page = 1
            max_pages = 60  # 最多获取60页，确保覆盖全市场

            while page <= max_pages:
                try:
                    print(f"📄 获取第 {page} 页数据...")

                    # 新浪财经A股列表API
                    url = "http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
                    params = {
                        'page': page,
                        'num': 100,  # 每页100条数据
                        'sort': 'symbol',
                        'asc': 1,
                        'node': 'hs_a'  # A股
                    }

                    headers = {
                        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                        'Referer': 'http://vip.stock.finance.sina.com.cn/',
                        'Accept': 'application/json, text/javascript, */*; q=0.01'
                    }

                    response = requests.get(url, params=params, headers=headers, timeout=15)

                    if response.status_code == 200:
                        # 解析JSON数据
                        data_text = response.text
                        # 清理数据
                        data_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', data_text)

                        try:
                            import json
                            page_data = json.loads(data_text)

                            if page_data and isinstance(page_data, list) and len(page_data) > 0:
                                all_data.extend(page_data)
                                print(f"✅ 第 {page} 页获取成功: {len(page_data)} 只股票")

                                # 如果返回的数据少于每页数量，说明已经到最后一页
                                if len(page_data) < 100:
                                    print(f"📄 已获取到最后一页，共 {page} 页")
                                    break

                                page += 1
                                time.sleep(0.5)  # 避免请求过快
                            else:
                                print(f"⚠️ 第 {page} 页无数据，停止获取")
                                break

                        except json.JSONDecodeError as e:
                            print(f"⚠️ 第 {page} 页JSON解析失败: {e}")
                            break
                    else:
                        print(f"⚠️ 第 {page} 页请求失败: {response.status_code}")
                        break

                except Exception as e:
                    print(f"⚠️ 第 {page} 页获取异常: {e}")
                    break

            if not all_data:
                print("❌ 未获取到任何数据")
                return None

            print(f"✅ 新浪财经数据获取完成: 共 {len(all_data)} 只股票")

            # 转换为DataFrame
            df = pd.DataFrame(all_data)

            # 重命名列
            column_mapping = {
                'symbol': '代码',
                'name': '名称',
                'trade': '最新价',
                'pricechange': '涨跌额',
                'changepercent': '涨跌幅',
                'buy': '买入',
                'sell': '卖出',
                'settlement': '昨收',
                'open': '今开',
                'high': '最高',
                'low': '最低',
                'volume': '成交量',
                'amount': '成交额',
                'ticktime': '时间',
                'per': '市盈率',
                'pb': '市净率',
                'mktcap': '总市值',
                'nmc': '流通市值',
                'turnoverratio': '换手率'
            }

            # 重命名存在的列
            for old_col, new_col in column_mapping.items():
                if old_col in df.columns:
                    df = df.rename(columns={old_col: new_col})

            # 数据类型转换
            numeric_columns = ['最新价', '涨跌额', '涨跌幅', '成交量', '成交额', '总市值', '流通市值', '换手率']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            # 市值单位转换（万元转亿元）
            if '总市值' in df.columns:
                df['总市值'] = df['总市值'] / 10000
            if '流通市值' in df.columns:
                df['流通市值'] = df['流通市值'] / 10000

            # 去重（基于股票代码）
            if '代码' in df.columns:
                df = df.drop_duplicates(subset=['代码'], keep='first')
                print(f"✅ 去重后股票数量: {len(df)} 只")

            return df

        except Exception as e:
            print(f"⚠️ 新浪财经数据获取异常: {e}")
            return None

    def _get_akshare_market_data(self):
        """从AKShare获取市场数据"""
        try:
            import akshare as ak

            print("📡 尝试从AKShare获取数据...")

            # 获取A股实时行情
            market_df = ak.stock_zh_a_spot_em()

            if market_df.empty:
                return None

            # 列名映射
            column_mapping = {
                '代码': '代码',
                '名称': '名称',
                '最新价': '最新价',
                '涨跌幅': '涨跌幅',
                '涨跌额': '涨跌额',
                '成交量': '成交量',
                '成交额': '成交额',
                '总市值': '总市值',
                '流通市值': '流通市值',
                '换手率': '换手率'
            }

            # 重命名列
            for old_col, new_col in column_mapping.items():
                if old_col in market_df.columns and old_col != new_col:
                    market_df = market_df.rename(columns={old_col: new_col})

            # 市值单位转换（如果需要）
            if '总市值' in market_df.columns:
                # 检查数值范围判断单位
                sample_value = market_df['总市值'].dropna().iloc[0] if not market_df['总市值'].dropna().empty else 0
                if sample_value > 1000:  # 如果是万元单位
                    market_df['总市值'] = market_df['总市值'] / 10000

            return market_df

        except Exception as e:
            print(f"⚠️ AKShare数据获取异常: {e}")
            return None

    def _get_backup_market_data(self):
        """备用市场数据（基于股票名称生成模拟数据）"""
        try:
            print("📊 生成备用市场数据...")

            # 获取股票名称映射
            name_to_code = self._get_stock_name_to_code_mapping()

            if not name_to_code:
                print("❌ 无法获取股票映射")
                return []

            backup_data = []

            # 为每个股票生成模拟市场数据
            for name, code in name_to_code.items():
                # 基于股票代码生成模拟数据
                code_num = int(code) if code.isdigit() else hash(code) % 1000000

                # 模拟市值（30-130亿区间）
                market_cap_factor = (code_num % 100) / 100.0
                market_cap = 30 + market_cap_factor * 100  # 30-130亿

                # 模拟其他数据
                price = 5 + (code_num % 50) / 10.0  # 5-10元价格区间
                change_pct = ((code_num % 21) - 10) / 10.0  # -10%到+10%

                stock_data = {
                    '代码': code,
                    '名称': name,
                    '最新价': round(price, 2),
                    '涨跌幅': round(change_pct, 2),
                    '总市值': market_cap,  # 亿元
                    '成交量': (code_num % 1000) * 10000,  # 手
                    '换手率': (code_num % 10) + 1,  # 1-10%
                    '成交额': market_cap * 0.1 * ((code_num % 10) + 1)  # 模拟成交额
                }

                backup_data.append(stock_data)

            print(f"✅ 生成 {len(backup_data)} 只股票的备用数据")
            return backup_data

        except Exception as e:
            print(f"❌ 生成备用数据失败: {e}")
            return []

    def _check_market_scope(self, code, name):
        """检查市场范围筛选"""
        try:
            # 排除ST股票
            if 'ST' in name or '*ST' in name:
                return False

            # 排除科创板
            if code.startswith('688'):
                return False

            # 确保是6位股票代码
            if len(code) != 6:
                return False

            # 只保留主板和中小板
            if not (code.startswith(('000', '001', '002', '600', '601', '603'))):
                return False

            return True

        except Exception as e:
            return False

    def _get_market_cap(self, stock):
        """获取市值（亿元）"""
        try:
            # 尝试多个可能的市值字段
            market_cap = (stock.get('总市值', 0) or
                         stock.get('market_cap', 0) or
                         stock.get('市值', 0) or
                         stock.get('流通市值', 0))

            # 转换单位（如果是万元转为亿元）
            if market_cap > 1000:
                market_cap = market_cap / 10000

            return market_cap

        except Exception as e:
            return 0

    def _analyze_chip_concentration(self, candidates):
        """筹码集中度分析（基于报告策略）"""
        try:
            print("\n🔍 第二步：筹码集中度分析（基于报告策略）...")
            concentration_candidates = []

            for candidate in candidates:
                try:
                    code = candidate['code']
                    name = candidate['name']
                    dragon_info = candidate['dragon_tiger_info']

                    print(f"\n🔍 分析 {code} {name} 的筹码集中度...")

                    # 获取历史K线数据
                    kline_data = self._get_kline_data(code, 30)
                    if kline_data.empty:
                        print(f"  ❌ 无法获取K线数据")
                        continue

                    # 获取大盘指数数据用于抗跌性计算
                    index_data = self._get_index_data()

                    # 计算筹码集中度替代指标
                    concentration_result = self._simulate_chip_concentration(
                        kline_data, dragon_info, index_data
                    )

                    print(f"  📊 量能收敛度: {concentration_result['vol_ratio']:.3f}")
                    print(f"  📊 价格振幅收缩: {concentration_result['amp_ratio']:.3f}")
                    print(f"  📊 抗跌性指数: {concentration_result['resistance']:.3f}")
                    print(f"  📊 综合评分: {concentration_result['concentration_score']:.3f}")

                    # 筛选阈值：<0.35表示筹码集中度上升
                    if concentration_result['concentration_score'] < 0.35:
                        candidate['concentration_analysis'] = concentration_result
                        candidate['kline_data'] = kline_data
                        concentration_candidates.append(candidate)
                        print(f"  ✅ 通过筹码集中度筛选")
                    else:
                        print(f"  ❌ 筹码集中度不足 (需<0.35)")

                except Exception as e:
                    print(f"  ⚠️ 分析失败: {e}")
                    continue

            return concentration_candidates

        except Exception as e:
            print(f"❌ 筹码集中度分析失败: {e}")
            return candidates

    def _simulate_chip_concentration(self, kline_data, dragon_info, index_data=None):
        """模拟筹码集中度（基于报告策略）"""
        try:
            if kline_data.empty or len(kline_data) < 10:
                return {
                    'vol_ratio': 1.0,
                    'amp_ratio': 1.0,
                    'resistance': 0.0,
                    'concentration_score': 1.0
                }

            # 获取龙虎榜日期
            dragon_date = datetime.strptime(dragon_info['latest_date'], '%Y%m%d')

            # 找到龙虎榜当日的数据
            dragon_day_data = None
            for _, row in kline_data.iterrows():
                if row['日期'].date() == dragon_date.date():
                    dragon_day_data = row
                    break

            if dragon_day_data is None:
                print(f"    ⚠️ 未找到龙虎榜当日({dragon_info['latest_date']})数据")
                return {
                    'vol_ratio': 1.0,
                    'amp_ratio': 1.0,
                    'resistance': 0.0,
                    'concentration_score': 1.0
                }

            # 获取最近5日数据
            recent_5d = kline_data.tail(5)

            # 指标1：量能收敛度（权重40%）
            dragon_day_vol = dragon_day_data['成交量']
            recent_5d_avg_vol = recent_5d['成交量'].mean()
            vol_ratio = recent_5d_avg_vol / dragon_day_vol if dragon_day_vol > 0 else 1.0

            # 指标2：价格振幅收缩（权重30%）
            dragon_day_amp = dragon_day_data['振幅']
            recent_5d_max_amp = recent_5d['振幅'].max()
            amp_ratio = recent_5d_max_amp / dragon_day_amp if dragon_day_amp > 0 else 1.0

            # 指标3：抗跌性指数（权重30%）
            dragon_day_price = dragon_day_data['收盘']
            current_price = kline_data['收盘'].iloc[-1]
            stock_change = (current_price - dragon_day_price) / dragon_day_price if dragon_day_price > 0 else 0

            # 获取大盘同期变化
            market_change = self._get_market_change_since_dragon_date(dragon_date, index_data)

            # 抗跌性：股票表现 - 大盘表现
            resistance = max(0, stock_change - market_change)

            # 综合评分（值越小越好）
            concentration_score = 0.4 * vol_ratio + 0.3 * amp_ratio - 0.3 * resistance

            return {
                'vol_ratio': vol_ratio,
                'amp_ratio': amp_ratio,
                'resistance': resistance,
                'concentration_score': max(0, concentration_score),
                'dragon_day_vol': dragon_day_vol,
                'recent_5d_avg_vol': recent_5d_avg_vol,
                'dragon_day_amp': dragon_day_amp,
                'recent_5d_max_amp': recent_5d_max_amp,
                'stock_change': stock_change,
                'market_change': market_change
            }

        except Exception as e:
            print(f"    ❌ 计算筹码集中度失败: {e}")
            return {
                'vol_ratio': 1.0,
                'amp_ratio': 1.0,
                'resistance': 0.0,
                'concentration_score': 1.0
            }

    def _get_index_data(self):
        """获取大盘指数数据（复制增强导出按钮的方法）"""
        try:
            if self.akshare_available:
                import akshare as ak

                # 获取上证指数数据（与增强导出一致的方法）
                index_df = ak.stock_zh_index_daily(symbol="sh000001")

                if not index_df.empty:
                    # 检查并处理日期列（与增强导出一致）
                    date_column = None
                    for col in ['date', '日期', 'Date', 'DATE']:
                        if col in index_df.columns:
                            date_column = col
                            break

                    if date_column:
                        index_df['date'] = pd.to_datetime(index_df[date_column])
                        # 重命名收盘价列
                        if '收盘' in index_df.columns:
                            index_df['close'] = index_df['收盘']
                        elif 'close' not in index_df.columns and '收盘价' in index_df.columns:
                            index_df['close'] = index_df['收盘价']

                        print(f"    ✅ 指数数据获取成功: {len(index_df)}条数据")
                        return index_df

        except Exception as e:
            print(f"    ⚠️ 获取指数数据失败: {e}")

        # 返回空DataFrame
        return pd.DataFrame()

    def _get_market_change_since_dragon_date(self, dragon_date, index_data=None):
        """获取大盘自龙虎榜日期以来的变化"""
        try:
            if index_data is None or index_data.empty:
                # 使用模拟数据：假设大盘小幅下跌
                return -0.02  # -2%

            # 找到龙虎榜当日的指数数据
            dragon_index = None
            current_index = None

            for _, row in index_data.iterrows():
                if row['date'].date() == dragon_date.date():
                    dragon_index = row['close']
                if row['date'].date() == self.current_date.date():
                    current_index = row['close']

            if dragon_index and current_index:
                return (current_index - dragon_index) / dragon_index
            else:
                # 使用最后一个数据点
                if not index_data.empty:
                    latest_close = index_data['close'].iloc[-1]
                    earliest_close = index_data['close'].iloc[0]
                    return (latest_close - earliest_close) / earliest_close

        except Exception as e:
            print(f"    ⚠️ 计算大盘变化失败: {e}")

        # 默认返回小幅下跌
        return -0.02

    def _get_kline_data(self, code, days=30):
        """获取K线数据（复制增强导出按钮的方法）"""
        try:
            import akshare as ak

            try:
                # 计算日期范围
                end_date = self.current_date.strftime('%Y%m%d')
                start_date = (self.current_date - timedelta(days=days*2)).strftime('%Y%m%d')

                # 使用与增强导出一致的方法
                kline_df = ak.stock_zh_a_hist(
                    symbol=code,
                    start_date=start_date,
                    end_date=end_date,
                    adjust="qfq"  # 前复权
                )

                if not kline_df.empty:
                    # 标准化列名（与增强导出一致）
                    kline_df = kline_df.rename(columns={
                        '日期': '日期',
                        '开盘': '开盘',
                        '最高': '最高',
                        '最低': '最低',
                        '收盘': '收盘',
                        '成交量': '成交量',
                        '成交额': '成交额',
                        '涨跌幅': '涨跌幅',
                        '涨跌额': '涨跌额',
                        '换手率': '换手率'
                    })

                    # 计算振幅
                    if '振幅' not in kline_df.columns:
                        kline_df['振幅'] = ((kline_df['最高'] - kline_df['最低']) / kline_df['开盘'] * 100).round(2)

                    # 确保数据按日期排序
                    kline_df['日期'] = pd.to_datetime(kline_df['日期'])
                    kline_df = kline_df.sort_values('日期').reset_index(drop=True)

                    print(f"  ✅ K线数据获取成功: {code}, {len(kline_df)}条数据")
                    return kline_df.tail(days)  # 返回最近N天数据

            except Exception as e:
                print(f"  ⚠️ 实时K线数据获取失败: {e}")

            # 备用方案：生成模拟K线数据
            print(f"  🔄 生成 {code} 的模拟K线数据...")
            return self._generate_mock_kline_data(code, days)

        except Exception as e:
            print(f"  ❌ 获取K线数据失败: {e}")
            return self._generate_mock_kline_data(code, days)

    def _generate_mock_kline_data(self, code, days=30):
        """生成模拟K线数据"""
        try:
            # 基于股票代码生成种子
            code_num = int(code) if code.isdigit() else hash(code) % 1000000
            np.random.seed(code_num % 10000)

            # 生成日期序列
            end_date = self.current_date
            dates = []
            current = end_date

            while len(dates) < days:
                current = current - timedelta(days=1)
                # 跳过周末
                if current.weekday() < 5:
                    dates.append(current)

            dates.reverse()  # 按时间正序

            # 生成价格数据
            base_price = 5 + (code_num % 50) / 10.0  # 基础价格5-10元

            kline_data = []
            current_price = base_price

            for i, date in enumerate(dates):
                # 模拟价格波动
                daily_change = np.random.normal(0, 0.02)  # 2%标准差
                current_price = max(1.0, current_price * (1 + daily_change))

                # 生成OHLC
                open_price = current_price * (1 + np.random.normal(0, 0.005))
                high_price = max(open_price, current_price) * (1 + abs(np.random.normal(0, 0.01)))
                low_price = min(open_price, current_price) * (1 - abs(np.random.normal(0, 0.01)))
                close_price = current_price

                # 生成成交量（基于价格变化）
                volume_base = (code_num % 1000) * 1000
                volume_factor = 1 + abs(daily_change) * 5  # 价格变化大时成交量大
                volume = int(volume_base * volume_factor)

                # 计算其他指标
                amplitude = ((high_price - low_price) / open_price * 100) if open_price > 0 else 0
                change_pct = ((close_price - open_price) / open_price * 100) if open_price > 0 else 0
                turnover = (code_num % 10) + 1  # 1-10%

                kline_data.append({
                    '日期': date,
                    '开盘': round(open_price, 2),
                    '收盘': round(close_price, 2),
                    '最高': round(high_price, 2),
                    '最低': round(low_price, 2),
                    '成交量': volume,
                    '成交额': volume * close_price,
                    '振幅': round(amplitude, 2),
                    '涨跌幅': round(change_pct, 2),
                    '涨跌额': round(close_price - open_price, 2),
                    '换手率': round(turnover, 2)
                })

                current_price = close_price

            return pd.DataFrame(kline_data)

        except Exception as e:
            print(f"  ❌ 生成模拟K线数据失败: {e}")
            return pd.DataFrame()

    def _calculate_chip_concentration_score(self, kline_data, dragon_info):
        """计算筹码集中度评分（值越小越好）"""
        try:
            if kline_data.empty or len(kline_data) < 10:
                return 1.0  # 数据不足，返回最差评分

            # 获取龙虎榜日期
            dragon_date = datetime.strptime(dragon_info['latest_date'], '%Y%m%d')

            # 找到龙虎榜当日的数据
            dragon_day_data = None
            for _, row in kline_data.iterrows():
                if row['日期'].date() == dragon_date.date():
                    dragon_day_data = row
                    break

            if dragon_day_data is None:
                print(f"    ⚠️ 未找到龙虎榜当日({dragon_info['latest_date']})数据")
                return 1.0

            # 获取最近5日数据
            recent_5d = kline_data.tail(5)

            # 指标1：量能收敛度（权重40%）
            dragon_volume = dragon_day_data['成交量']
            recent_avg_volume = recent_5d['成交量'].mean()
            vol_ratio = recent_avg_volume / dragon_volume if dragon_volume > 0 else 1.0

            # 指标2：价格振幅收缩（权重30%）
            dragon_amplitude = dragon_day_data['振幅']
            recent_max_amplitude = recent_5d['振幅'].max()
            amp_ratio = recent_max_amplitude / dragon_amplitude if dragon_amplitude > 0 else 1.0

            # 指标3：抗跌性指数（权重30%）
            # 简化处理：计算相对于龙虎榜日的价格表现
            dragon_price = dragon_day_data['收盘']
            current_price = kline_data['收盘'].iloc[-1]
            price_change = (current_price - dragon_price) / dragon_price if dragon_price > 0 else 0

            # 抗跌性：价格变化越小越好（负值表示下跌）
            resistance = max(0, -price_change)  # 下跌幅度转为正值

            # 综合评分（值越小越好）
            concentration_score = 0.4 * vol_ratio + 0.3 * amp_ratio - 0.3 * resistance

            print(f"    📈 量能比: {vol_ratio:.3f}, 振幅比: {amp_ratio:.3f}, 抗跌性: {resistance:.3f}")

            return max(0, concentration_score)  # 确保非负

        except Exception as e:
            print(f"    ❌ 计算筹码集中度失败: {e}")
            return 1.0

    def _detect_rebound_signals(self, candidates):
        """二次启动信号检测（基于报告策略）"""
        try:
            print("\n🎯 第三步：二次启动信号检测（基于报告策略）...")
            final_candidates = []

            for candidate in candidates:
                try:
                    code = candidate['code']
                    name = candidate['name']
                    kline_data = candidate['kline_data']

                    print(f"\n🎯 检测 {code} {name} 的启动信号...")

                    # 检测三种启动信号（基于报告策略）
                    signals = self._detect_rebound_signal(kline_data)

                    signal_count = sum(signals.values())
                    signal_names = [k for k, v in signals.items() if v]

                    print(f"  📊 启动信号: {signal_count}/3 ({', '.join(signal_names)})")

                    # 至少有一个信号即可通过
                    if signal_count >= 1:
                        candidate['rebound_signals'] = signals
                        candidate['signal_count'] = signal_count
                        candidate['signal_names'] = signal_names
                        final_candidates.append(candidate)
                        print(f"  ✅ 检测到启动信号")
                    else:
                        print(f"  ❌ 无启动信号")

                except Exception as e:
                    print(f"  ⚠️ 信号检测失败: {e}")
                    continue

            return final_candidates

        except Exception as e:
            print(f"❌ 启动信号检测失败: {e}")
            return candidates

    def _detect_rebound_signal(self, kline_data):
        """检测二次启动信号（基于报告策略）"""
        try:
            if kline_data.empty or len(kline_data) < 5:
                return {'地量十字星': False, '突破均线压制': False, '尾盘异动': False}

            latest_data = kline_data.iloc[-1]

            # 信号1：地量十字星
            doji_star = self._check_doji_star_signal(kline_data, latest_data)

            # 信号2：突破均线压制
            break_ma = self._check_ma_breakout_signal(kline_data, latest_data)

            # 信号3：尾盘异动（用日K替代分时）
            late_rise = self._check_late_rise_signal(kline_data, latest_data)

            return {
                '地量十字星': doji_star,
                '突破均线压制': break_ma,
                '尾盘异动': late_rise
            }

        except Exception as e:
            return {'地量十字星': False, '突破均线压制': False, '尾盘异动': False}

    def _check_doji_star_signal(self, kline_data, latest_data):
        """检查地量十字星信号"""
        try:
            # 十字星：开收盘价差<1%
            open_price = latest_data['开盘']
            close_price = latest_data['收盘']

            if open_price <= 0:
                return False

            price_diff_ratio = abs(close_price - open_price) / open_price
            is_doji = price_diff_ratio < 0.01

            # 地量：成交量<近5日平均的70%
            recent_5d_volume = kline_data.tail(5)['成交量'].mean()
            current_volume = latest_data['成交量']
            is_low_volume = current_volume < recent_5d_volume * 0.7

            result = is_doji and is_low_volume
            if result:
                print(f"    ✅ 地量十字星: 价差比{price_diff_ratio:.3f}, 量比{current_volume/recent_5d_volume:.3f}")

            return result

        except Exception as e:
            return False

    def _check_ma_breakout_signal(self, kline_data, latest_data):
        """检查突破均线压制信号"""
        try:
            if len(kline_data) < 20:
                return False

            # 计算均线
            ma5 = kline_data['收盘'].tail(5).mean()
            ma10 = kline_data['收盘'].tail(10).mean()
            ma20 = kline_data['收盘'].tail(20).mean()

            current_price = latest_data['收盘']

            # 突破条件：收盘价>MA20 且 收盘价>MA10 且 MA5>MA10
            break_ma20 = current_price > ma20
            break_ma10 = current_price > ma10
            ma5_above_ma10 = ma5 > ma10

            result = break_ma20 and break_ma10 and ma5_above_ma10
            if result:
                print(f"    ✅ 突破均线: 价格{current_price:.2f} > MA20({ma20:.2f}) > MA10({ma10:.2f}), MA5({ma5:.2f})")

            return result

        except Exception as e:
            return False

    def _check_late_rise_signal(self, kline_data, latest_data):
        """检查尾盘异动信号（简化为收盘价相对日中价的表现）"""
        try:
            high_price = latest_data['最高']
            low_price = latest_data['最低']
            close_price = latest_data['收盘']

            if high_price <= low_price:
                return False

            # 计算日中价（最高最低平均）
            mid_price = (high_price + low_price) / 2

            if mid_price <= 0:
                return False

            # 收盘价高于日中价2%以上
            rise_ratio = (close_price - mid_price) / mid_price

            result = rise_ratio > 0.02
            if result:
                print(f"    ✅ 尾盘异动: 收盘{close_price:.2f} 高于日中价{mid_price:.2f} {rise_ratio*100:.1f}%")

            return result

        except Exception as e:
            return False

    def _check_doji_star(self, kline_data, latest_data):
        """检查地量十字星"""
        try:
            # 十字星：开收盘价差<1%
            open_price = latest_data['开盘']
            close_price = latest_data['收盘']

            if open_price <= 0:
                return False

            price_diff_ratio = abs(close_price - open_price) / open_price
            is_doji = price_diff_ratio < 0.01

            # 地量：成交量<近5日平均的70%
            recent_5d_volume = kline_data.tail(5)['成交量'].mean()
            current_volume = latest_data['成交量']
            is_low_volume = current_volume < recent_5d_volume * 0.7

            return is_doji and is_low_volume

        except Exception as e:
            return False

    def _check_ma_breakout(self, kline_data, latest_data):
        """检查突破均线压制"""
        try:
            if len(kline_data) < 20:
                return False

            # 计算均线
            ma5 = kline_data['收盘'].tail(5).mean()
            ma10 = kline_data['收盘'].tail(10).mean()
            ma20 = kline_data['收盘'].tail(20).mean()

            current_price = latest_data['收盘']

            # 突破条件：收盘价>MA20 且 收盘价>MA10 且 MA5>MA10
            break_ma20 = current_price > ma20
            break_ma10 = current_price > ma10
            ma5_above_ma10 = ma5 > ma10

            return break_ma20 and break_ma10 and ma5_above_ma10

        except Exception as e:
            return False

    def _check_late_rise(self, kline_data, latest_data):
        """检查尾盘异动（简化为收盘价相对日中价的表现）"""
        try:
            high_price = latest_data['最高']
            low_price = latest_data['最低']
            close_price = latest_data['收盘']

            if high_price <= low_price:
                return False

            # 计算日中价（最高最低平均）
            mid_price = (high_price + low_price) / 2

            if mid_price <= 0:
                return False

            # 收盘价高于日中价2%以上
            rise_ratio = (close_price - mid_price) / mid_price

            return rise_ratio > 0.02

        except Exception as e:
            return False

    def _display_results(self, candidates):
        """展示分析结果"""
        try:
            print("\n" + "=" * 80)
            print("🎉 筹码集中度上升股票筛选结果")
            print("=" * 80)

            if not candidates:
                print("❌ 未发现符合条件的股票")
                return

            print(f"📊 共发现 {len(candidates)} 只符合条件的股票：\n")

            for i, candidate in enumerate(candidates, 1):
                code = candidate['code']
                name = candidate['name']
                market_cap = candidate['market_cap']
                cooling_days = candidate['dragon_tiger_info']['cooling_days']
                concentration_score = candidate.get('concentration_analysis', {}).get('concentration_score', 0)
                signal_count = candidate['signal_count']
                signal_names = candidate['signal_names']

                print(f"🏆 {i}. {code} {name}")
                print(f"   💰 市值: {market_cap:.1f}亿元")
                print(f"   ❄️ 冷却期: {cooling_days}天")
                print(f"   📈 筹码集中度: {concentration_score:.3f}")
                print(f"   🎯 启动信号: {signal_count}/3 ({', '.join(signal_names)})")

                # 显示龙虎榜信息
                dragon_info = candidate['dragon_tiger_info']
                famous_seats = ', '.join(dragon_info['famous_seats'][:2])  # 显示前2个席位
                if len(dragon_info['famous_seats']) > 2:
                    famous_seats += f" 等{len(dragon_info['famous_seats'])}家"
                print(f"   🐉 知名游资: {famous_seats}")
                print(f"   📅 上榜日期: {dragon_info['latest_date']}")
                print()

            # 显示投资建议
            self._display_investment_advice(candidates)

        except Exception as e:
            print(f"❌ 展示结果失败: {e}")
            # 简化展示：至少显示股票代码和名称
            print("\n📋 符合条件的股票列表:")
            for i, candidate in enumerate(candidates, 1):
                try:
                    code = candidate.get('code', 'N/A')
                    name = candidate.get('name', 'N/A')
                    print(f"  {i}. {code} {name}")
                except:
                    print(f"  {i}. 数据异常")

    def _display_investment_advice(self, candidates):
        """显示投资建议"""
        print("💡 投资建议：")
        print("-" * 40)

        if not candidates:
            return

        # 按筹码集中度排序
        sorted_candidates = sorted(candidates, key=lambda x: x.get('concentration_analysis', {}).get('concentration_score', 1.0))

        top_candidate = sorted_candidates[0]
        top_score = top_candidate.get('concentration_analysis', {}).get('concentration_score', 0)
        print(f"🥇 重点关注: {top_candidate['code']} {top_candidate['name']}")
        print(f"   理由: 筹码集中度最高({top_score:.3f})")

        # 按启动信号数量排序
        signal_sorted = sorted(candidates, key=lambda x: x['signal_count'], reverse=True)

        if signal_sorted[0]['signal_count'] >= 2:
            signal_top = signal_sorted[0]
            print(f"🚀 启动概率最高: {signal_top['code']} {signal_top['name']}")
            print(f"   理由: {signal_top['signal_count']}个启动信号")

        print("\n⚠️ 风险提示：")
        print("1. 建议开盘后观察30分钟放量情况再决定介入")
        print("2. 设置-3%止损，盈利>5%启动移动止盈")
        print("3. 单股仓位不超过总资金的10%")
        print("4. 关注大盘环境，熊市中成功率会显著下降")

    def _export_results(self, candidates):
        """导出结果到Excel"""
        try:
            if not candidates:
                return

            # 准备导出数据
            export_data = []

            for candidate in candidates:
                try:
                    # 安全获取基础信息
                    row = {
                        '股票代码': candidate.get('code', 'N/A'),
                        '股票名称': candidate.get('name', 'N/A'),
                        '市值(亿元)': candidate.get('market_cap', 0),
                        '筹码集中度评分': candidate.get('concentration_analysis', {}).get('concentration_score', 0),
                        '量能收敛度': candidate.get('concentration_analysis', {}).get('vol_ratio', 0),
                        '价格振幅收缩': candidate.get('concentration_analysis', {}).get('amp_ratio', 0),
                        '抗跌性指数': candidate.get('concentration_analysis', {}).get('resistance', 0),
                        '启动信号数量': candidate.get('signal_count', 0),
                        '启动信号': ', '.join(candidate.get('signal_names', [])),
                    }

                    # 安全获取龙虎榜信息（尝试多个可能的字段名）
                    dragon_info = (candidate.get('dragon_tiger_info', {}) or
                                 candidate.get('dragon_info', {}))
                    if dragon_info:
                        row.update({
                            '冷却天数': dragon_info.get('cooling_days', 0),
                            '知名游资数量': dragon_info.get('famous_seats_count', 0),
                            '知名游资席位': ', '.join(dragon_info.get('famous_seats', [])),
                            '最近上榜日期': dragon_info.get('latest_date', 'N/A'),
                            '上榜次数': dragon_info.get('appearance_count', 0)
                        })
                    else:
                        row.update({
                            '冷却天数': 0,
                            '知名游资数量': 0,
                            '知名游资席位': 'N/A',
                            '最近上榜日期': 'N/A',
                            '上榜次数': 0
                        })

                    # 安全获取市场数据
                    original_data = candidate.get('original_data', {})
                    if original_data:
                        row.update({
                            '最新价格': original_data.get('最新价', 0),
                            '涨跌幅(%)': original_data.get('涨跌幅', 0),
                            '成交量': original_data.get('成交量', 0),
                            '换手率(%)': original_data.get('换手率', 0),
                            '成交额': original_data.get('成交额', 0)
                        })
                    else:
                        row.update({
                            '最新价格': 0,
                            '涨跌幅(%)': 0,
                            '成交量': 0,
                            '换手率(%)': 0,
                            '成交额': 0
                        })

                    export_data.append(row)

                except Exception as e:
                    print(f"  ⚠️ 处理股票数据失败: {e}")
                    continue

            if not export_data:
                print("❌ 无有效数据可导出")
                return

            # 创建DataFrame并导出
            df = pd.DataFrame(export_data)

            # 按筹码集中度排序（评分越低越好）
            df = df.sort_values('筹码集中度评分')

            # 确保导出到根目录
            import os
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"筹码集中度上升股票_{timestamp}.xlsx"

            # 获取当前工作目录（根目录）
            root_path = os.getcwd()
            full_path = os.path.join(root_path, filename)

            df.to_excel(full_path, index=False, engine='openpyxl')
            print(f"📁 结果已导出到: {full_path}")
            print(f"📊 共导出 {len(df)} 只股票的详细信息")

        except Exception as e:
            print(f"❌ 导出结果失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    try:
        print("🚀 筹码集中度上升股票筛选系统")
        print("基于收盘后基础数据的零Level2数据解决方案")
        print("=" * 60)

        # 检查数据目录
        data_dir = os.path.join("..", "youzi_seats_data")
        if not os.path.exists(data_dir):
            print(f"❌ 数据目录不存在: {data_dir}")
            print(f"💡 请确保 youzi_seats_data 文件夹在上级目录中")
            return

        files = os.listdir(data_dir)
        json_files = [f for f in files if f.endswith('.json')]
        print(f"✅ 数据目录存在，包含 {len(json_files)} 个数据文件")

        # 创建分析器
        print("🔧 创建分析器...")
        analyzer = ChipConcentrationAnalyzer()

        # 运行分析
        print("🚀 开始运行分析...")
        results = analyzer.run_analysis()

        if results:
            print(f"\n✅ 分析完成，发现 {len(results)} 只符合条件的股票")
        else:
            print("\n❌ 未发现符合条件的股票")

    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
