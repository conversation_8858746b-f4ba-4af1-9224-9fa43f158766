#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库K线数据
"""

import sqlite3
import pandas as pd

def check_database_kline():
    """检查数据库K线数据"""
    print("📊 检查数据库K线数据...")
    
    # 连接数据库
    conn = sqlite3.connect('historical_data.db')
    
    try:
        # 检查所有K线数据
        print('📊 数据库中的K线数据统计:')
        stock_counts = pd.read_sql(
            'SELECT stock_code, COUNT(*) as count, MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_kline_data GROUP BY stock_code ORDER BY count DESC', 
            conn
        )
        print(stock_counts)
        
        total_stocks = len(stock_counts)
        total_records = stock_counts['count'].sum() if not stock_counts.empty else 0
        print(f'\n📊 总计: {total_stocks}只股票，{total_records}条记录')
        
        # 检查603880相关的所有数据
        print(f'\n📊 603880相关数据:')
        print('K线数据:')
        kline_603880 = pd.read_sql('SELECT * FROM stock_kline_data WHERE stock_code = "603880"', conn)
        print(f'   记录数: {len(kline_603880)}')
        
        print('特征数据:')
        feature_603880 = pd.read_sql('SELECT * FROM stock_features WHERE stock_code = "603880"', conn)
        print(f'   记录数: {len(feature_603880)}')
        if not feature_603880.empty:
            print('   样本数据:')
            print(feature_603880[['stock_code', 'feature_date', 'label']].head())
        
        # 检查训练日志中提到的其他股票
        test_stocks = ['002691', '002471', '002052', '603880']
        print(f'\n📊 训练日志中的股票K线数据:')
        for stock in test_stocks:
            count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (stock,)).fetchone()[0]
            print(f'   {stock}: {count}条')
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_database_kline()
