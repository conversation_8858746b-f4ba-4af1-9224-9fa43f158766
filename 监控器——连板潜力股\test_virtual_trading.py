#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
虚拟交易测试系统
用于测试主应用的各种算法和通知系统
"""

import sys
import os
import time
import threading
from datetime import datetime
from typing import Dict, List, Any

# 添加主应用路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入虚拟数据生成器
from virtual_data_generator import VirtualDataGenerator

# 导入主应用的核心类（需要修改导入方式以避免GUI启动）
try:
    # 临时禁用GUI相关导入
    import tkinter
    tkinter.Tk = lambda: None  # 防止创建GUI窗口
    
    # 导入主应用类
    from 连板潜力股实时监控 import LimitUpStockMonitor
except ImportError as e:
    print(f"❌ 导入主应用失败: {e}")
    sys.exit(1)

class VirtualTradingTester:
    """虚拟交易测试器"""
    
    def __init__(self):
        self.data_generator = VirtualDataGenerator()
        self.monitor = None
        self.test_results = []
        self.notification_received = []
        
        # 测试场景列表
        self.test_scenarios = [
            "平开上涨",
            "高开回落反转", 
            "低开反转",
            "中位蓄势突破",
            "高位横盘突破",
            "隔夜挂单强势",
            "隔夜挂单破板",
            "V型反转"
        ]
    
    def setup_monitor(self):
        """设置监控器（不启动GUI）"""
        try:
            # 创建监控器实例，但不启动GUI
            self.monitor = LimitUpStockMonitor()
            
            # 添加测试股票
            test_stock = self.data_generator.get_test_stock_info()
            self.monitor.monitored_stocks[test_stock['code']] = {
                'name': test_stock['name'],
                'initialized': True,
                'data': {},
                'signals': {},
                'alert_history': []
            }
            
            # 模拟历史数据
            self.monitor.historical_base_data[test_stock['code']] = {
                'k_line_data': [],
                'volume_data': {'avg_volume_5d': 1000000, 'avg_volume_10d': 1200000},
                'price_stats': {'avg_price_5d': 10.0},
                'circulating_shares': 50000
            }
            
            # 重写通知方法以捕获通知
            original_send_notification = self.monitor.send_intelligent_buy_notification
            def capture_notification(*args, **kwargs):
                self.notification_received.append({
                    'time': datetime.now(),
                    'args': args,
                    'kwargs': kwargs
                })
                print(f"🔔 捕获到买入通知: {args[0]} {args[2]}")
                return original_send_notification(*args, **kwargs)
            
            self.monitor.send_intelligent_buy_notification = capture_notification
            
            print("✅ 虚拟监控器设置完成")
            return True
            
        except Exception as e:
            print(f"❌ 监控器设置失败: {e}")
            return False
    
    def run_scenario_test(self, scenario_name: str, duration: int = 60) -> Dict[str, Any]:
        """运行单个场景测试"""
        print(f"\n🎬 开始测试场景: {scenario_name}")
        print("=" * 60)
        
        # 生成虚拟数据
        virtual_data = self.data_generator.generate_scenario_data(scenario_name, duration)
        
        if not virtual_data:
            return {'scenario': scenario_name, 'success': False, 'error': '数据生成失败'}
        
        # 清空之前的通知记录
        self.notification_received.clear()
        
        # 测试结果记录
        test_result = {
            'scenario': scenario_name,
            'start_time': datetime.now(),
            'data_points': len(virtual_data),
            'algorithms_triggered': [],
            'notifications_sent': 0,
            'max_signal_strength': 0,
            'success': False,
            'details': []
        }
        
        try:
            # 逐个数据点进行测试
            for i, data_point in enumerate(virtual_data):
                print(f"⏰ {data_point['timestamp']} | 价格: {data_point['current_price']:.2f} | 涨幅: {data_point['change_pct']:.2f}%")
                
                # 调用主应用的算法
                result = self.test_single_data_point(data_point)
                
                if result:
                    test_result['details'].append(result)
                    
                    # 记录最大信号强度
                    signal_strength = result.get('signal_strength', 0)
                    if signal_strength > test_result['max_signal_strength']:
                        test_result['max_signal_strength'] = signal_strength
                    
                    # 记录触发的算法
                    algorithm_type = result.get('algorithm_type', '')
                    if algorithm_type and algorithm_type not in test_result['algorithms_triggered']:
                        test_result['algorithms_triggered'].append(algorithm_type)
                    
                    # 检查是否有买入信号
                    if result.get('buy_signal', False):
                        print(f"   🚀 触发买入信号! 算法: {algorithm_type}, 强度: {signal_strength:.2f}")
                
                # 模拟时间间隔（加速测试）
                time.sleep(0.1)
            
            # 统计通知数量
            test_result['notifications_sent'] = len(self.notification_received)
            test_result['success'] = True
            
            print(f"\n📊 场景测试完成:")
            print(f"   • 数据点数: {test_result['data_points']}")
            print(f"   • 触发算法: {', '.join(test_result['algorithms_triggered']) if test_result['algorithms_triggered'] else '无'}")
            print(f"   • 最大信号强度: {test_result['max_signal_strength']:.2f}")
            print(f"   • 发送通知: {test_result['notifications_sent']} 次")
            
        except Exception as e:
            test_result['error'] = str(e)
            print(f"❌ 场景测试失败: {e}")
        
        return test_result
    
    def test_single_data_point(self, data_point: Dict[str, Any]) -> Dict[str, Any]:
        """测试单个数据点"""
        try:
            test_code = 'TEST001'
            
            # 更新实时数据缓存
            if not hasattr(self.monitor, 'realtime_data_series'):
                self.monitor.realtime_data_series = {}
            
            if test_code not in self.monitor.realtime_data_series:
                self.monitor.realtime_data_series[test_code] = []
            
            self.monitor.realtime_data_series[test_code].append(data_point)
            
            # 保持最近50个数据点
            if len(self.monitor.realtime_data_series[test_code]) > 50:
                self.monitor.realtime_data_series[test_code] = self.monitor.realtime_data_series[test_code][-50:]
            
            # 调用智能算法选择
            algorithm_info = self.monitor.select_intelligent_algorithm(test_code, data_point)
            
            # 调用时段策略分析
            period_analysis = self.monitor.apply_time_period_strategy(test_code, data_point)
            
            # 调用智能信号检测
            signal_result = self.monitor.execute_intelligent_signal_detection(
                test_code, data_point, algorithm_info, period_analysis
            )
            
            return {
                'timestamp': data_point['timestamp'],
                'price': data_point['current_price'],
                'change_pct': data_point['change_pct'],
                'algorithm_type': algorithm_info.get('algorithm_name', ''),
                'algorithm_confidence': algorithm_info.get('confidence', 0),
                'period_name': period_analysis.get('period_name', ''),
                'signal_strength': signal_result.get('signal_strength', 0),
                'buy_signal': signal_result.get('buy_signal', False),
                'recommendation': signal_result.get('recommendation', ''),
                'position_size': signal_result.get('position_size', 0)
            }
            
        except Exception as e:
            print(f"   ⚠️ 数据点测试失败: {e}")
            return None
    
    def run_all_tests(self):
        """运行所有测试场景"""
        print("🚀 开始虚拟交易测试系统")
        print("=" * 80)
        
        if not self.setup_monitor():
            print("❌ 监控器设置失败，测试终止")
            return
        
        # 运行所有场景测试
        for scenario in self.test_scenarios:
            try:
                result = self.run_scenario_test(scenario)
                self.test_results.append(result)
                
                # 场景间暂停
                time.sleep(2)
                
            except Exception as e:
                print(f"❌ 场景 {scenario} 测试异常: {e}")
                continue
        
        # 生成测试报告
        self.generate_test_report()
    
    def generate_test_report(self):
        """生成测试报告"""
        print("\n" + "=" * 80)
        print("📊 虚拟交易测试报告")
        print("=" * 80)
        
        total_scenarios = len(self.test_results)
        successful_scenarios = len([r for r in self.test_results if r.get('success', False)])
        total_notifications = sum(r.get('notifications_sent', 0) for r in self.test_results)
        
        print(f"📈 测试概览:")
        print(f"   • 总测试场景: {total_scenarios}")
        print(f"   • 成功场景: {successful_scenarios}")
        print(f"   • 成功率: {successful_scenarios/total_scenarios*100:.1f}%")
        print(f"   • 总通知数: {total_notifications}")
        
        print(f"\n📋 详细结果:")
        for result in self.test_results:
            status = "✅" if result.get('success', False) else "❌"
            scenario = result['scenario']
            algorithms = ', '.join(result.get('algorithms_triggered', [])) or '无'
            max_strength = result.get('max_signal_strength', 0)
            notifications = result.get('notifications_sent', 0)
            
            print(f"   {status} {scenario}:")
            print(f"      算法触发: {algorithms}")
            print(f"      最大信号强度: {max_strength:.2f}")
            print(f"      通知次数: {notifications}")
        
        # 算法覆盖率统计
        all_algorithms = set()
        for result in self.test_results:
            all_algorithms.update(result.get('algorithms_triggered', []))
        
        print(f"\n🎯 算法覆盖情况:")
        print(f"   • 触发的算法: {len(all_algorithms)} 种")
        print(f"   • 算法列表: {', '.join(sorted(all_algorithms))}")
        
        print(f"\n🎉 测试完成! 系统运行正常，可以在交易时间使用。")

if __name__ == "__main__":
    tester = VirtualTradingTester()
    tester.run_all_tests()
