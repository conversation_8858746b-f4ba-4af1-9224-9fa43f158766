#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试筹码分布数据管理机制
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path

def test_chip_data_completeness():
    """测试筹码分布数据完整性检查"""
    print("🧪 测试筹码分布数据完整性检查")
    print("=" * 60)
    
    cache_dir = Path("screening_historical_data_cache")
    if not cache_dir.exists():
        print("❌ 缓存目录不存在")
        return False
    
    cache_files = list(cache_dir.glob("*_historical_data.json"))
    if not cache_files:
        print("❌ 没有找到历史数据缓存文件")
        return False
    
    print(f"📁 找到 {len(cache_files)} 个缓存文件")
    
    complete_count = 0
    chip_data_count = 0
    
    for cache_file in cache_files[:5]:  # 检查前5个文件
        try:
            with open(cache_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            stock_code = cache_file.stem.replace('_historical_data', '')
            print(f"\n📊 检查 {stock_code}:")
            
            # 检查必需字段
            required_fields = [
                'prev_close', 'avg_volume_5d', 'ma5', 'ma10', 'ma20', 
                'chip_distribution_30d', 'cache_time'
            ]
            
            missing_fields = [field for field in required_fields if field not in data]
            if missing_fields:
                print(f"   ❌ 缺少字段: {missing_fields}")
                continue
            
            # 检查筹码分布数据
            chip_data = data.get('chip_distribution_30d')
            if not chip_data or not isinstance(chip_data, dict) or len(chip_data) == 0:
                print(f"   ❌ 筹码分布数据无效")
                continue
            
            # 检查数据时效性
            cache_time = data.get('cache_time')
            if cache_time:
                try:
                    cache_datetime = datetime.fromisoformat(cache_time)
                    age = datetime.now() - cache_datetime
                    print(f"   📅 数据时间: {cache_datetime.strftime('%Y-%m-%d %H:%M')}")
                    print(f"   ⏰ 数据年龄: {age.days}天 {age.seconds//3600}小时")
                    
                    if age > timedelta(days=1):
                        print(f"   ⚠️ 数据已过期")
                    else:
                        print(f"   ✅ 数据新鲜")
                except:
                    print(f"   ❌ 时间格式异常")
                    continue
            
            print(f"   📊 筹码分布数据点: {len(chip_data)} 个")
            print(f"   💰 价格范围: {min(chip_data.keys())} - {max(chip_data.keys())}")
            print(f"   ✅ 数据完整")
            
            complete_count += 1
            chip_data_count += len(chip_data)
            
        except Exception as e:
            print(f"   ❌ 读取失败: {e}")
    
    print(f"\n📊 检查结果:")
    print(f"   ✅ 完整文件: {complete_count}/{min(len(cache_files), 5)}")
    print(f"   📊 筹码数据点总数: {chip_data_count}")
    
    return complete_count > 0

def test_data_refresh_logic():
    """测试数据刷新逻辑"""
    print("\n🧪 测试数据刷新逻辑")
    print("=" * 60)
    
    # 模拟不同的数据状态
    test_cases = [
        {
            'name': '数据完整且新鲜',
            'data': {
                'loaded_date': datetime.now().date(),
                'chip_distribution_30d': {'10.5': 1000, '11.0': 1500},
                'cache_time': datetime.now().isoformat()
            },
            'expected': False
        },
        {
            'name': '数据过期',
            'data': {
                'loaded_date': (datetime.now() - timedelta(days=2)).date(),
                'chip_distribution_30d': {'10.5': 1000, '11.0': 1500},
                'cache_time': (datetime.now() - timedelta(days=2)).isoformat()
            },
            'expected': True
        },
        {
            'name': '筹码分布数据缺失',
            'data': {
                'loaded_date': datetime.now().date(),
                'chip_distribution_30d': {},
                'cache_time': datetime.now().isoformat()
            },
            'expected': True
        },
        {
            'name': '筹码分布数据为None',
            'data': {
                'loaded_date': datetime.now().date(),
                'chip_distribution_30d': None,
                'cache_time': datetime.now().isoformat()
            },
            'expected': True
        }
    ]
    
    passed = 0
    for i, case in enumerate(test_cases, 1):
        print(f"\n{i}️⃣ 测试: {case['name']}")
        
        # 模拟刷新判断逻辑
        data = case['data']
        loaded_date = data.get('loaded_date')
        current_date = datetime.now().date()
        
        # 检查日期
        if not loaded_date or loaded_date < current_date:
            should_refresh = True
            reason = "日期过期"
        else:
            # 检查筹码分布
            chip_data = data.get('chip_distribution_30d')
            if not chip_data or not isinstance(chip_data, dict) or len(chip_data) == 0:
                should_refresh = True
                reason = "筹码分布无效"
            else:
                # 检查时效性
                cache_time = data.get('cache_time')
                if cache_time:
                    try:
                        cache_datetime = datetime.fromisoformat(cache_time)
                        if datetime.now() - cache_datetime > timedelta(days=1):
                            should_refresh = True
                            reason = "缓存过期"
                        else:
                            should_refresh = False
                            reason = "数据完整"
                    except:
                        should_refresh = True
                        reason = "时间格式错误"
                else:
                    should_refresh = True
                    reason = "缺少时间戳"
        
        print(f"   判断结果: {'需要刷新' if should_refresh else '无需刷新'} ({reason})")
        print(f"   预期结果: {'需要刷新' if case['expected'] else '无需刷新'}")
        
        if should_refresh == case['expected']:
            print(f"   ✅ 测试通过")
            passed += 1
        else:
            print(f"   ❌ 测试失败")
    
    print(f"\n📊 刷新逻辑测试结果: {passed}/{len(test_cases)} 通过")
    return passed == len(test_cases)

def main():
    """主测试函数"""
    print("🚀 筹码分布数据管理机制测试")
    print("=" * 80)
    
    test_results = []
    
    # 测试数据完整性检查
    test_results.append(test_chip_data_completeness())
    
    # 测试刷新逻辑
    test_results.append(test_data_refresh_logic())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 测试结果汇总:")
    
    passed = sum(test_results)
    total = len(test_results)
    
    print(f"   ✅ 通过测试: {passed}/{total}")
    print(f"   📈 成功率: {passed/total:.1%}")
    
    if passed == total:
        print("🎉 筹码分布数据管理机制测试通过！")
        print("💡 数据完整性检查已完善")
        print("💡 避免重复获取筹码分布数据")
        print("💡 每日自动刷新机制正常")
    else:
        print("⚠️ 部分测试失败，需要进一步检查")

if __name__ == "__main__":
    main()
