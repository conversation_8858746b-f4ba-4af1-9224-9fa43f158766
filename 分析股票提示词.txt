忽略之前的信息，重新开始。

请你使用专业金融知识，结合完整阅读游资炒作全链路.txt（共2730行），使用本地应用"完整读取EXCEL.py"来预处理和分析EXCEL文件。

首先明确一个基本定义——符合游资炒作全链路.txt的偏好的主力身份是游资，不符合该身份的其他主力属于机构，私募，基金。

🎯 TARGET ANALYSIS - 分析目标
股票代码：000953
分析要求：指定对[目标股票]进行结构化深度游资分析

📋 MANDATORY STAGED EXECUTION - 强制分阶段执行
⚠️ 核心执行原则
本次分析必须分为4-5次对话完成，每次对话专注特定阶段，确保完整逐行分析

第1次对话：STEP 0 + PHASE 1-2（数据预处理 + 游资历史追踪 + 技术面分析）
第2次对话：PHASE 3-4（资金流向分析 + 筹码分布分析）
第3次对话：PHASE 5（分时数据深度挖掘）
第4次对话：PHASE 6-7（综合研判 + 真假出逃辨析）
第5次对话：最终总结报告

🚀 STEP 0 - 数据预处理阶段（第1次对话执行）
运行命令：python "完整读取EXCEL.py" [目标股票代码]
系统将自动：

创建临时文件夹 $[目标股票代码]
读取 [目标股票代码].xlsx 文件
提取所有38个工作表为独立CSV文件
建立工作表→分析阶段映射关系
核对股票基本信息
任务管理系统初始化：
使用任务管理工具创建7个主要阶段任务，为每个阶段分配具体的CSV文件清单

📊 PHASE 1 - 游资历史追踪阶段（第1次对话执行）
🔍 指定读取CSV文件（必须逐行完整分析）：
$[目标股票代码]/龙虎榜-当日.csv → 逐行统计所有上榜记录
$[目标股票代码]/龙虎榜-6个月历史.csv → 逐条分析每次上榜详情
$[目标股票代码]/龙虎榜-综合分析.csv → 完整统计所有分析指标
$[目标股票代码]/分时数据-游资信号.csv → 逐条记录每个游资信号
$[目标股票代码]/分时数据-最新详情-游资分析.csv → 详细分析每个操作建议
📋 强制逐行分析要求：
龙虎榜数据：统计总上榜次数、每次净买卖金额、涉及席位、时间分布
游资信号：记录信号总数、每个信号的时间、类型、重要程度
非游资信号：如判定为游资介入概率低，则在下一个步骤里进一步判断
操作手法：识别每种手法的出现频次、成功率、时间特征
目标：快速定位是否有游资历史活动记录
输出：游资历史活动总结报告（包含完整数据统计）

📈 PHASE 2 - 技术面基础分析阶段（第1次对话执行）
📊 指定读取CSV文件（必须逐行完整分析）：
$[目标股票代码]/实时行情.csv → 分析每个实时数据点
$[目标股票代码]/K线数据.csv → 逐日分析每根K线
$[目标股票代码]/技术分析.csv → 计算每个技术指标
$[目标股票代码]/价格统计.csv → 统计每个价格区间数据
📋 强制逐行分析要求：
K线数据：分析每日开高低收、成交量、涨跌幅，识别关键K线形态
技术指标：计算每日均线、支撑阻力位、技术信号
价格统计：统计价格分布、波动特征、关键价位
非游资信号：如判定为游资介入概率低，则在下一个步骤里进一步判断
目标：建立股票技术面基础认知
输出：技术面概况总结（包含完整技术指标计算）

💰 PHASE 3 - 资金流向深度分析阶段（第2次对话执行）
🔍 指定读取CSV文件（必须逐行完整分析）：
$[目标股票代码]/资金流向-历史数据.csv → 逐日统计资金流向
$[目标股票代码]/资金流向-最新分析.csv → 详细分析每笔大单
📋 强制逐行分析要求：
历史数据：统计每日主力、超大单、大单、中单、小单净流入/流出
累计计算：计算30日累计净流入、最大单日流入/流出、连续流入/流出天数
异常识别：标记每个异常资金流向日期、金额、占比
目标：识别主力资金动向和介入程度
非游资信号：如判定为游资介入概率低，则开始使用自身专业金融知识，结合机构，私募，基金等主力操作手法分析
输出：资金流向分析报告（包含完整统计数据）

🎯 PHASE 4 - 筹码分布精确分析阶段（第2次对话执行）
📊 指定读取CSV文件（必须逐行完整分析）：
$[目标股票代码]/筹码分布-概览.csv → 分析每个概览指标
$[目标股票代码]/筹码分布-详细数据.csv → 逐价位分析筹码分布
$[目标股票代码]/筹码分布-变化趋势.csv → 跟踪每个变化指标
📋 强制逐行分析要求：
详细数据：分析每个价位的筹码占比，识别密集区域
成本计算：基于筹码分布计算加权平均成本、核心成本区间
变化趋势：跟踪筹码集中度、迁移方向、分布变化
目标：精确计算主力成本区间
非游资信号：如判定为游资介入概率低，则开始使用自身专业金融知识，结合机构，私募，基金等主力操作手法分析
输出：主力成本分析报告（包含精确成本计算）

⏰ PHASE 5 - 分时数据深度挖掘阶段（第3次对话执行）
🔍 指定读取CSV文件（必须逐行完整分析）：
$[目标股票代码]/时间周期.csv → 分析每个时间周期
$[目标股票代码]/分时数据-7天概览.csv → 统计7天所有数据
$[目标股票代码]/分时数据-每日汇总.csv → 分析每日汇总指标
$[目标股票代码]/分时数据-详细记录(7天).csv → 逐分钟分析分时数据
$[目标股票代码]/分时数据-[具体日期].csv → 分析每个可用日期的分时数据
$[目标股票代码]/分时数据-关键时段分析.csv → 分析每个关键时段
$[目标股票代码]/分时数据-最新详情-成交量异常.csv → 记录每个异常点
$[目标股票代码]/分时数据-最新详情-操作建议.csv → 分析每个操作建议
📋 强制逐行分析要求：
分时数据：逐分钟分析价格、成交量、异常信号
非游资信号：如判定为游资介入概率低，则开始使用自身专业金融知识，结合机构，私募，基金等主力操作手法分析
异常统计：记录每个成交量异常的时间、倍数、价格变化
手法识别：识别每种游资操作手法的具体时间点和特征
目标：识别游资操作手法和建仓轨迹
输出：操作手法识别报告（包含完整分时分析）

🔬 PHASE 6 - 综合研判阶段（第4次对话执行）
📊 指定读取CSV文件（必须逐行完整分析）：
$[目标股票代码]/数据概览.csv → 分析每个概览指标
$[目标股票代码]/基本信息.csv → 核实每个基本信息
$[目标股票代码]/五档行情.csv → 分析盘口每档数据
$[目标股票代码]/概念板块.csv → 分析每个概念标签
$[目标股票代码]/板块联动-错误.csv → 检查数据质量
$[目标股票代码]/市场情绪-概览.csv → 分析情绪概览
$[目标股票代码]/市场情绪-指数数据.csv → 逐日分析指数数据
$[目标股票代码]/市场情绪-市场广度.csv → 分析市场广度指标
$[目标股票代码]/市场情绪-波动率.csv → 计算每日波动率
$[目标股票代码]/市场情绪-恐慌贪婪.csv → 分析情绪指标
$[目标股票代码]/开盘特征-概览.csv → 分析开盘特征
$[目标股票代码]/数据质量.csv → 评估数据质量
目标：结合游资炒作全链路进行最终研判
非游资信号：如最终判定为非游资介入，则开始使用自身专业金融知识，结合机构，私募，基金等主力操作手法分析
输出：完整投资分析报告

🚨 PHASE 7 - 真假出逃专业辨析阶段（第4次对话执行，条件触发）
强制执行条件检查：

主力资金大幅净流出（>5000万元）
股价下跌或涨幅微弱（<2%）
成交量异常放大但价格疲软
筹码集中度显著下降
技术形态出现破位信号
如触发条件，必须执行完整的真假出逃辨析框架

🔥 CRITICAL REQUIREMENTS - 硬性要求
📊 数据读取效率要求：
严禁重复读取：每个CSV文件在整个分析过程中只能读取一次，但如果后续阶段确实需要回顾前面的数据，而你又没有保存的情况下，可以重新读取，单要精准定位需要读取的内容，不过出现过多读取浪费时间的情况。
阶段性读取：每个PHASE只能读取该阶段指定的CSV文件，但如果后续阶段确实需要回顾前面的数据，而你又没有保存的情况下，可以重新读取，单要精准定位需要读取的内容，不过出现过多读取浪费时间的情况。
一次性完整读取：每个CSV文件读取时必须完整读取所有数据
📋 分析质量要求：
完整数据分析：每个阶段必须分析相关CSV文件的每一行数据
数据统计证明：必须提供具体的行数统计、数值汇总、异常计数
阶段性总结：每个阶段结束后必须输出详细的阶段性总结
专业对比分析：必须基于游资炒作全链路2271行内容进行专业对比
非游资信号：如判定为非游资介入，则使用自身专业金融知识，结合机构，私募，基金等主力操作手法分析
🎯 分阶段执行要求：
第1次对话：完成STEP 0 + PHASE 1-2，提供完整数据统计
第2次对话：完成PHASE 3-4，提供精确成本计算
第3次对话：完成PHASE 5，提供完整分时分析
第4次对话：完成PHASE 6-7，提供综合研判
第5次对话：最终总结报告
📊 数据完整性证明要求：
每个阶段必须提供：

文件行数统计：每个CSV文件的总行数
数据点计数：分析的具体数据点数量
异常记录数：识别的异常信号数量
关键指标汇总：重要数值的完整统计
⚡ EXECUTION PROTOCOL - 执行协议
现在开始执行第1次对话内容：

立即执行STEP 0数据预处理
创建任务管理系统
完整执行PHASE 1游资历史追踪（逐行分析所有龙虎榜和游资信号数据）
完整执行PHASE 2技术面分析（逐行分析所有K线和技术指标数据）
提供完整的数据统计证明和阶段性总结
请确认：本次对话将专注于PHASE 1-2的完整逐行分析，后续阶段将在接下来的对话中继续。