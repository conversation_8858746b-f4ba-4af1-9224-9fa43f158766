#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
筹码分布分析模块
基于历史成交数据计算筹码分布，分析套牢盘获利盘，识别主力成本区间
这是游资操作分析的基础模块
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class ChipDistributionAnalyzer:
    """筹码分布分析器"""
    
    def __init__(self):
        self.chip_decay_factor = 0.95  # 筹码衰减因子，每天衰减5%
        self.min_analysis_days = 60    # 最少分析天数
        self.max_analysis_days = 120   # 最多分析天数
        
    def get_chip_distribution_analysis(self, stock_code: str) -> Dict:
        """
        获取完整的筹码分布分析
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 筹码分布分析结果
        """
        try:
            logger.info(f"开始分析{stock_code}的筹码分布")
            
            # 1. 获取历史K线数据
            historical_data = self._get_historical_kline_data(stock_code)
            if historical_data.empty:
                return {'data_available': False, 'error': '无法获取历史K线数据'}
            
            # 2. 计算筹码分布
            chip_distribution = self._calculate_chip_distribution(historical_data)
            if not chip_distribution:
                return {'data_available': False, 'error': '筹码分布计算失败'}
            
            # 3. 分析套牢盘和获利盘
            current_price = historical_data.iloc[-1]['close']
            profit_loss_analysis = self._analyze_profit_loss_chips(
                chip_distribution, current_price
            )
            
            # 4. 识别主力成本区间
            main_cost_analysis = self._identify_main_cost_zones(
                chip_distribution, historical_data
            )
            
            # 5. 计算筹码集中度
            concentration_analysis = self._calculate_chip_concentration(chip_distribution)
            
            # 6. 分析筹码变化趋势
            trend_analysis = self._analyze_chip_trend(historical_data)
            
            # 7. 生成综合分析报告
            comprehensive_report = self._generate_comprehensive_report(
                chip_distribution, profit_loss_analysis, main_cost_analysis,
                concentration_analysis, trend_analysis, current_price
            )
            
            return {
                'data_available': True,
                'stock_code': stock_code,
                'analysis_date': datetime.now().strftime('%Y-%m-%d'),
                'current_price': current_price,
                'chip_distribution': chip_distribution,
                'profit_loss_analysis': profit_loss_analysis,
                'main_cost_analysis': main_cost_analysis,
                'concentration_analysis': concentration_analysis,
                'trend_analysis': trend_analysis,
                'comprehensive_report': comprehensive_report,
                'data_quality': {
                    'analysis_days': len(historical_data),
                    'data_completeness': '完整' if len(historical_data) >= self.min_analysis_days else '不完整',
                    'reliability': '高' if len(historical_data) >= 90 else '中等'
                }
            }
            
        except Exception as e:
            logger.error(f"筹码分布分析失败: {e}")
            return {'data_available': False, 'error': str(e)}
    
    def _get_historical_kline_data(self, stock_code: str) -> pd.DataFrame:
        """获取历史K线数据"""
        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.max_analysis_days + 30)  # 多获取一些数据以防节假日
            
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            
            logger.info(f"获取{stock_code}历史数据: {start_date_str} - {end_date_str}")
            
            # 使用AKShare获取历史数据
            df = ak.stock_zh_a_hist(
                symbol=stock_code,
                start_date=start_date_str,
                end_date=end_date_str,
                adjust="qfq"  # 前复权
            )
            
            if df.empty:
                logger.warning(f"未获取到{stock_code}的历史数据")
                return pd.DataFrame()
            
            # 数据清洗和格式化
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            })

            # 如果重命名失败，使用原始列名
            if '收盘' in df.columns:
                df['close'] = df['收盘']
            if '开盘' in df.columns:
                df['open'] = df['开盘']
            if '最高' in df.columns:
                df['high'] = df['最高']
            if '最低' in df.columns:
                df['low'] = df['最低']
            if '成交量' in df.columns:
                df['volume'] = df['成交量']
            
            # 确保数据类型正确
            df['date'] = pd.to_datetime(df['date'])
            for col in ['open', 'close', 'high', 'low']:
                df[col] = pd.to_numeric(df[col], errors='coerce')
            df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
            
            # 按日期排序
            df = df.sort_values('date').reset_index(drop=True)
            
            # 限制分析天数
            if len(df) > self.max_analysis_days:
                df = df.tail(self.max_analysis_days)
            
            logger.info(f"获取到{len(df)}天的历史数据")
            return df
            
        except Exception as e:
            logger.error(f"获取历史K线数据失败: {e}")
            return pd.DataFrame()
    
    def _calculate_chip_distribution(self, historical_data: pd.DataFrame) -> Dict[float, float]:
        """
        计算筹码分布
        基于历史成交数据，考虑时间衰减因子
        """
        try:
            logger.info("开始计算筹码分布")
            
            chip_distribution = {}
            total_days = len(historical_data)
            
            for i, row in historical_data.iterrows():
                # 计算时间衰减权重（越近的数据权重越大）
                days_ago = total_days - i - 1
                decay_weight = (self.chip_decay_factor ** days_ago)
                
                # 计算当日价格区间的筹码分布
                high_price = row['high']
                low_price = row['low']
                volume = row['volume']
                
                if pd.isna(volume) or volume <= 0:
                    continue
                
                # 将当日成交量按价格区间分布
                # 假设成交量在高低价之间均匀分布
                price_range = high_price - low_price
                if price_range <= 0:
                    # 一字板情况，所有成交量集中在收盘价
                    price = row['close']
                    price_key = round(price, 2)
                    if price_key not in chip_distribution:
                        chip_distribution[price_key] = 0
                    chip_distribution[price_key] += volume * decay_weight
                else:
                    # 按价格区间分布成交量
                    # 使用更细的价格粒度
                    price_step = 0.01  # 1分钱为一个价格档位
                    num_steps = max(1, int(price_range / price_step))
                    volume_per_step = volume / num_steps
                    
                    for step in range(num_steps):
                        price = low_price + (step + 0.5) * price_step
                        price_key = round(price, 2)
                        
                        if price_key not in chip_distribution:
                            chip_distribution[price_key] = 0
                        chip_distribution[price_key] += volume_per_step * decay_weight
            
            # 标准化筹码分布（转换为百分比）
            total_chips = sum(chip_distribution.values())
            if total_chips > 0:
                for price in chip_distribution:
                    chip_distribution[price] = (chip_distribution[price] / total_chips) * 100
            
            logger.info(f"计算完成，共{len(chip_distribution)}个价格档位")
            return chip_distribution
            
        except Exception as e:
            logger.error(f"计算筹码分布失败: {e}")
            return {}
    
    def _analyze_profit_loss_chips(self, chip_distribution: Dict[float, float], 
                                 current_price: float) -> Dict:
        """分析套牢盘和获利盘比例"""
        try:
            logger.info(f"分析套牢盘和获利盘，当前价格: {current_price}")
            
            profit_chips = 0  # 获利盘
            loss_chips = 0    # 套牢盘
            equal_chips = 0   # 平盘
            
            profit_details = []  # 获利盘详细分布
            loss_details = []    # 套牢盘详细分布
            
            for price, chip_ratio in chip_distribution.items():
                if price < current_price:
                    profit_chips += chip_ratio
                    profit_margin = (current_price - price) / price * 100
                    profit_details.append({
                        'price': price,
                        'chip_ratio': chip_ratio,
                        'profit_margin': profit_margin
                    })
                elif price > current_price:
                    loss_chips += chip_ratio
                    loss_margin = (price - current_price) / current_price * 100
                    loss_details.append({
                        'price': price,
                        'chip_ratio': chip_ratio,
                        'loss_margin': loss_margin
                    })
                else:
                    equal_chips += chip_ratio
            
            # 按盈亏幅度排序
            profit_details.sort(key=lambda x: x['profit_margin'], reverse=True)
            loss_details.sort(key=lambda x: x['loss_margin'])
            
            # 计算平均盈亏
            avg_profit_margin = 0
            avg_loss_margin = 0
            
            if profit_details:
                weighted_profit = sum(d['profit_margin'] * d['chip_ratio'] for d in profit_details)
                avg_profit_margin = weighted_profit / profit_chips if profit_chips > 0 else 0
            
            if loss_details:
                weighted_loss = sum(d['loss_margin'] * d['chip_ratio'] for d in loss_details)
                avg_loss_margin = weighted_loss / loss_chips if loss_chips > 0 else 0
            
            return {
                'current_price': current_price,
                'profit_chips_ratio': round(profit_chips, 2),
                'loss_chips_ratio': round(loss_chips, 2),
                'equal_chips_ratio': round(equal_chips, 2),
                'avg_profit_margin': round(avg_profit_margin, 2),
                'avg_loss_margin': round(avg_loss_margin, 2),
                'profit_details': profit_details[:10],  # 前10个最大获利区间
                'loss_details': loss_details[:10],      # 前10个最大套牢区间
                'analysis': self._generate_profit_loss_analysis(
                    profit_chips, loss_chips, avg_profit_margin, avg_loss_margin
                )
            }
            
        except Exception as e:
            logger.error(f"分析套牢盘和获利盘失败: {e}")
            return {}
    
    def _generate_profit_loss_analysis(self, profit_ratio: float, loss_ratio: float,
                                     avg_profit: float, avg_loss: float) -> List[str]:
        """生成盈亏分析结论"""
        analysis = []
        
        if profit_ratio > 70:
            analysis.append("获利盘占比超过70%，抛压较重")
        elif profit_ratio > 50:
            analysis.append("获利盘占比过半，存在一定抛压")
        elif profit_ratio < 30:
            analysis.append("获利盘占比较低，抛压相对较轻")
        
        if loss_ratio > 50:
            analysis.append("套牢盘占比过半，上方压力较大")
        elif loss_ratio < 30:
            analysis.append("套牢盘占比较低，上方压力较轻")
        
        if avg_profit > 20:
            analysis.append("平均获利幅度较大，获利了结意愿强烈")
        elif avg_profit < 5:
            analysis.append("平均获利幅度较小，获利了结压力不大")
        
        if avg_loss > 20:
            analysis.append("平均套牢幅度较大，解套压力沉重")
        elif avg_loss < 10:
            analysis.append("平均套牢幅度较小，解套压力相对较轻")
        
        return analysis

    def _identify_main_cost_zones(self, chip_distribution: Dict[float, float],
                                historical_data: pd.DataFrame) -> Dict:
        """识别主力成本区间"""
        try:
            logger.info("识别主力成本区间")

            if not chip_distribution:
                return {}

            # 1. 找出筹码密集区域
            sorted_chips = sorted(chip_distribution.items(), key=lambda x: x[1], reverse=True)

            # 2. 识别主要成本区间（筹码占比前70%的价格区间）
            total_chips = sum(chip_distribution.values())
            accumulated_chips = 0
            main_cost_zones = []

            for price, chip_ratio in sorted_chips:
                accumulated_chips += chip_ratio
                main_cost_zones.append({
                    'price': price,
                    'chip_ratio': chip_ratio,
                    'accumulated_ratio': accumulated_chips
                })

                if accumulated_chips >= 70:  # 前70%的筹码
                    break

            # 3. 计算主力成本区间统计
            if main_cost_zones:
                prices = [zone['price'] for zone in main_cost_zones]
                min_cost = min(prices)
                max_cost = max(prices)
                avg_cost = sum(zone['price'] * zone['chip_ratio'] for zone in main_cost_zones) / sum(zone['chip_ratio'] for zone in main_cost_zones)

                # 4. 识别最密集的成本区间（连续价格区间内筹码最多的区域）
                dense_zones = self._find_dense_cost_zones(chip_distribution)

                # 5. 分析主力持仓成本
                current_price = historical_data.iloc[-1]['close']
                cost_analysis = self._analyze_main_cost_position(
                    avg_cost, min_cost, max_cost, current_price
                )

                return {
                    'main_cost_range': {
                        'min_cost': round(min_cost, 2),
                        'max_cost': round(max_cost, 2),
                        'avg_cost': round(avg_cost, 2),
                        'cost_range_width': round(max_cost - min_cost, 2)
                    },
                    'dense_zones': dense_zones,
                    'cost_analysis': cost_analysis,
                    'main_cost_zones': main_cost_zones[:10],  # 前10个主要成本区间
                    'current_vs_cost': {
                        'current_price': current_price,
                        'vs_avg_cost': round((current_price - avg_cost) / avg_cost * 100, 2),
                        'vs_min_cost': round((current_price - min_cost) / min_cost * 100, 2),
                        'vs_max_cost': round((current_price - max_cost) / max_cost * 100, 2)
                    }
                }
            else:
                return {}

        except Exception as e:
            logger.error(f"识别主力成本区间失败: {e}")
            return {}

    def _find_dense_cost_zones(self, chip_distribution: Dict[float, float]) -> List[Dict]:
        """找出筹码密集区域"""
        try:
            # 按价格排序
            sorted_prices = sorted(chip_distribution.keys())
            dense_zones = []

            # 使用滑动窗口找出密集区域
            window_size = 0.5  # 0.5元的价格窗口

            for i, base_price in enumerate(sorted_prices):
                # 计算窗口内的筹码总量
                window_chips = 0
                window_prices = []

                for price in sorted_prices:
                    if abs(price - base_price) <= window_size:
                        window_chips += chip_distribution[price]
                        window_prices.append(price)

                # 如果窗口内筹码占比超过5%，认为是密集区域
                if window_chips >= 5:
                    dense_zones.append({
                        'center_price': base_price,
                        'price_range': [min(window_prices), max(window_prices)],
                        'chip_ratio': round(window_chips, 2),
                        'price_count': len(window_prices)
                    })

            # 去重和排序
            unique_zones = []
            for zone in dense_zones:
                # 检查是否与已有区域重叠
                is_duplicate = False
                for existing in unique_zones:
                    if abs(zone['center_price'] - existing['center_price']) <= window_size:
                        # 保留筹码更多的区域
                        if zone['chip_ratio'] > existing['chip_ratio']:
                            unique_zones.remove(existing)
                            unique_zones.append(zone)
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_zones.append(zone)

            # 按筹码占比排序
            unique_zones.sort(key=lambda x: x['chip_ratio'], reverse=True)

            return unique_zones[:5]  # 返回前5个最密集的区域

        except Exception as e:
            logger.error(f"找出筹码密集区域失败: {e}")
            return []

    def _analyze_main_cost_position(self, avg_cost: float, min_cost: float,
                                  max_cost: float, current_price: float) -> List[str]:
        """分析主力持仓成本位置"""
        analysis = []

        # 当前价格相对于主力成本的位置
        vs_avg = (current_price - avg_cost) / avg_cost * 100

        if vs_avg > 20:
            analysis.append("当前价格远高于主力平均成本，主力获利丰厚")
        elif vs_avg > 10:
            analysis.append("当前价格高于主力平均成本，主力处于获利状态")
        elif vs_avg > -5:
            analysis.append("当前价格接近主力平均成本，主力盈亏平衡")
        elif vs_avg > -15:
            analysis.append("当前价格低于主力平均成本，主力轻度套牢")
        else:
            analysis.append("当前价格远低于主力平均成本，主力深度套牢")

        # 成本区间分析
        cost_range_ratio = (max_cost - min_cost) / avg_cost * 100
        if cost_range_ratio > 30:
            analysis.append("主力成本区间较宽，建仓周期较长")
        elif cost_range_ratio < 10:
            analysis.append("主力成本区间较窄，建仓周期较短")

        # 当前价格在成本区间的位置
        if current_price > max_cost:
            analysis.append("当前价格突破主力成本区间上沿，技术面偏强")
        elif current_price < min_cost:
            analysis.append("当前价格跌破主力成本区间下沿，技术面偏弱")
        else:
            analysis.append("当前价格在主力成本区间内，处于震荡整理状态")

        return analysis

    def _calculate_chip_concentration(self, chip_distribution: Dict[float, float]) -> Dict:
        """计算筹码集中度"""
        try:
            logger.info("计算筹码集中度")

            if not chip_distribution:
                return {}

            # 1. 计算基尼系数（衡量筹码分布的不均匀程度）
            chip_values = list(chip_distribution.values())
            chip_values.sort()

            n = len(chip_values)
            cumsum = np.cumsum(chip_values)
            gini_coefficient = (n + 1 - 2 * sum((n + 1 - i) * y for i, y in enumerate(cumsum))) / (n * sum(chip_values))

            # 2. 计算前20%价格区间的筹码占比
            sorted_chips = sorted(chip_distribution.items(), key=lambda x: x[1], reverse=True)
            top_20_percent_count = max(1, len(sorted_chips) // 5)
            top_20_percent_chips = sum(chip[1] for chip in sorted_chips[:top_20_percent_count])

            # 3. 计算前50%价格区间的筹码占比
            top_50_percent_count = max(1, len(sorted_chips) // 2)
            top_50_percent_chips = sum(chip[1] for chip in sorted_chips[:top_50_percent_count])

            # 4. 计算筹码分散度
            price_range = max(chip_distribution.keys()) - min(chip_distribution.keys())
            avg_price = sum(price * ratio for price, ratio in chip_distribution.items()) / sum(chip_distribution.values())

            # 计算价格标准差
            variance = sum(ratio * (price - avg_price) ** 2 for price, ratio in chip_distribution.items()) / sum(chip_distribution.values())
            price_std = np.sqrt(variance)

            # 5. 生成集中度分析
            concentration_level = self._determine_concentration_level(
                gini_coefficient, top_20_percent_chips, top_50_percent_chips
            )

            return {
                'gini_coefficient': round(gini_coefficient, 4),
                'top_20_percent_chips': round(top_20_percent_chips, 2),
                'top_50_percent_chips': round(top_50_percent_chips, 2),
                'price_range': round(price_range, 2),
                'price_std': round(price_std, 2),
                'avg_price': round(avg_price, 2),
                'concentration_level': concentration_level,
                'analysis': self._generate_concentration_analysis(
                    gini_coefficient, top_20_percent_chips, top_50_percent_chips, concentration_level
                )
            }

        except Exception as e:
            logger.error(f"计算筹码集中度失败: {e}")
            return {}

    def _determine_concentration_level(self, gini: float, top_20: float, top_50: float) -> str:
        """确定筹码集中度等级"""
        if gini > 0.7 and top_20 > 60:
            return "高度集中"
        elif gini > 0.5 and top_20 > 40:
            return "较为集中"
        elif gini > 0.3 and top_20 > 25:
            return "适度集中"
        elif gini > 0.2:
            return "相对分散"
        else:
            return "高度分散"

    def _generate_concentration_analysis(self, gini: float, top_20: float,
                                       top_50: float, level: str) -> List[str]:
        """生成筹码集中度分析"""
        analysis = []

        analysis.append(f"筹码集中度等级: {level}")

        if level == "高度集中":
            analysis.append("筹码高度集中，主力控盘能力强")
            analysis.append("股价易受主力操控，波动性较大")
        elif level == "较为集中":
            analysis.append("筹码较为集中，存在明显的主力资金")
            analysis.append("主力对股价有一定控制力")
        elif level == "适度集中":
            analysis.append("筹码分布相对均衡，市场参与度较好")
        elif level == "相对分散":
            analysis.append("筹码相对分散，散户参与度较高")
            analysis.append("主力控盘能力有限，股价走势相对理性")
        else:
            analysis.append("筹码高度分散，缺乏明显的主力资金")
            analysis.append("股价走势主要受市场情绪影响")

        if top_20 > 50:
            analysis.append("前20%价格区间集中了过半筹码，存在明显的成本密集区")

        return analysis

    def _analyze_chip_trend(self, historical_data: pd.DataFrame) -> Dict:
        """分析筹码变化趋势"""
        try:
            logger.info("分析筹码变化趋势")

            if len(historical_data) < 30:
                return {'error': '数据不足，无法分析趋势'}

            # 1. 计算不同时期的筹码分布
            recent_30_days = historical_data.tail(30)
            recent_60_days = historical_data.tail(60)

            chip_30_days = self._calculate_chip_distribution(recent_30_days)
            chip_60_days = self._calculate_chip_distribution(recent_60_days)

            # 2. 分析筹码迁移趋势
            migration_analysis = self._analyze_chip_migration(chip_30_days, chip_60_days)

            # 3. 计算筹码集中度变化
            concentration_30 = self._calculate_chip_concentration(chip_30_days)
            concentration_60 = self._calculate_chip_concentration(chip_60_days)

            concentration_change = self._analyze_concentration_change(
                concentration_30, concentration_60
            )

            # 4. 分析成交量变化趋势
            volume_trend = self._analyze_volume_trend(historical_data)

            # 5. 识别筹码异动信号
            anomaly_signals = self._identify_chip_anomaly_signals(historical_data)

            return {
                'migration_analysis': migration_analysis,
                'concentration_change': concentration_change,
                'volume_trend': volume_trend,
                'anomaly_signals': anomaly_signals,
                'trend_summary': self._generate_trend_summary(
                    migration_analysis, concentration_change, volume_trend, anomaly_signals
                )
            }

        except Exception as e:
            logger.error(f"分析筹码变化趋势失败: {e}")
            return {'error': str(e)}

    def _analyze_chip_migration(self, chip_30: Dict, chip_60: Dict) -> Dict:
        """分析筹码迁移趋势"""
        try:
            if not chip_30 or not chip_60:
                return {}

            # 计算平均成本变化
            avg_cost_30 = sum(price * ratio for price, ratio in chip_30.items()) / sum(chip_30.values())
            avg_cost_60 = sum(price * ratio for price, ratio in chip_60.items()) / sum(chip_60.values())

            cost_change = (avg_cost_30 - avg_cost_60) / avg_cost_60 * 100

            # 分析筹码向上或向下迁移
            migration_direction = "向上迁移" if cost_change > 2 else "向下迁移" if cost_change < -2 else "相对稳定"

            # 计算筹码分布重心变化
            price_range_30 = max(chip_30.keys()) - min(chip_30.keys())
            price_range_60 = max(chip_60.keys()) - min(chip_60.keys())

            range_change = (price_range_30 - price_range_60) / price_range_60 * 100

            return {
                'avg_cost_change': round(cost_change, 2),
                'migration_direction': migration_direction,
                'price_range_change': round(range_change, 2),
                'analysis': self._generate_migration_analysis(cost_change, migration_direction, range_change)
            }

        except Exception as e:
            logger.error(f"分析筹码迁移失败: {e}")
            return {}

    def _generate_migration_analysis(self, cost_change: float, direction: str, range_change: float) -> List[str]:
        """生成筹码迁移分析"""
        analysis = []

        analysis.append(f"筹码迁移方向: {direction}")

        if direction == "向上迁移":
            analysis.append("筹码重心上移，表明有资金在高位接盘")
            if cost_change > 5:
                analysis.append("筹码迁移幅度较大，可能存在主力拉升行为")
        elif direction == "向下迁移":
            analysis.append("筹码重心下移，表明有资金在低位割肉")
            if cost_change < -5:
                analysis.append("筹码迁移幅度较大，可能存在恐慌性抛售")
        else:
            analysis.append("筹码分布相对稳定，市场处于平衡状态")

        if range_change > 20:
            analysis.append("筹码分布区间扩大，市场分歧加大")
        elif range_change < -20:
            analysis.append("筹码分布区间收窄，市场共识增强")

        return analysis

    def _analyze_concentration_change(self, conc_30: Dict, conc_60: Dict) -> Dict:
        """分析筹码集中度变化"""
        try:
            if not conc_30 or not conc_60:
                return {}

            gini_change = conc_30.get('gini_coefficient', 0) - conc_60.get('gini_coefficient', 0)
            top_20_change = conc_30.get('top_20_percent_chips', 0) - conc_60.get('top_50_percent_chips', 0)

            concentration_trend = "集中度上升" if gini_change > 0.05 else "集中度下降" if gini_change < -0.05 else "集中度稳定"

            return {
                'gini_change': round(gini_change, 4),
                'top_20_change': round(top_20_change, 2),
                'concentration_trend': concentration_trend,
                'analysis': self._generate_concentration_change_analysis(gini_change, concentration_trend)
            }

        except Exception as e:
            logger.error(f"分析筹码集中度变化失败: {e}")
            return {}

    def _generate_concentration_change_analysis(self, gini_change: float, trend: str) -> List[str]:
        """生成筹码集中度变化分析"""
        analysis = []

        analysis.append(f"筹码集中度趋势: {trend}")

        if trend == "集中度上升":
            analysis.append("筹码向少数价格区间集中，可能有主力在收集筹码")
            if gini_change > 0.1:
                analysis.append("集中度上升明显，主力收集筹码的迹象较为明显")
        elif trend == "集中度下降":
            analysis.append("筹码分布趋于分散，可能有主力在派发筹码")
            if gini_change < -0.1:
                analysis.append("集中度下降明显，主力派发筹码的迹象较为明显")
        else:
            analysis.append("筹码集中度保持稳定，市场处于平衡状态")

        return analysis

    def _analyze_volume_trend(self, historical_data: pd.DataFrame) -> Dict:
        """分析成交量变化趋势"""
        try:
            recent_10_days = historical_data.tail(10)
            recent_30_days = historical_data.tail(30)

            avg_volume_10 = recent_10_days['volume'].mean()
            avg_volume_30 = recent_30_days['volume'].mean()

            volume_change = (avg_volume_10 - avg_volume_30) / avg_volume_30 * 100

            volume_trend = "放量" if volume_change > 20 else "缩量" if volume_change < -20 else "平量"

            # 分析价量关系
            price_change = (recent_10_days['close'].iloc[-1] - recent_10_days['close'].iloc[0]) / recent_10_days['close'].iloc[0] * 100

            price_volume_relation = self._analyze_price_volume_relation(price_change, volume_change)

            return {
                'volume_change': round(volume_change, 2),
                'volume_trend': volume_trend,
                'price_change': round(price_change, 2),
                'price_volume_relation': price_volume_relation,
                'analysis': self._generate_volume_trend_analysis(volume_trend, price_volume_relation)
            }

        except Exception as e:
            logger.error(f"分析成交量趋势失败: {e}")
            return {}

    def _analyze_price_volume_relation(self, price_change: float, volume_change: float) -> str:
        """分析价量关系"""
        if price_change > 0 and volume_change > 0:
            return "量价齐升"
        elif price_change > 0 and volume_change < 0:
            return "价升量缩"
        elif price_change < 0 and volume_change > 0:
            return "价跌量增"
        elif price_change < 0 and volume_change < 0:
            return "量价齐跌"
        else:
            return "价量平衡"

    def _generate_volume_trend_analysis(self, volume_trend: str, price_volume_relation: str) -> List[str]:
        """生成成交量趋势分析"""
        analysis = []

        analysis.append(f"成交量趋势: {volume_trend}")
        analysis.append(f"价量关系: {price_volume_relation}")

        if price_volume_relation == "量价齐升":
            analysis.append("量价配合良好，上涨趋势较为健康")
        elif price_volume_relation == "价升量缩":
            analysis.append("上涨缺乏成交量支撑，需要警惕回调风险")
        elif price_volume_relation == "价跌量增":
            analysis.append("下跌伴随放量，可能存在恐慌性抛售")
        elif price_volume_relation == "量价齐跌":
            analysis.append("量价齐跌，下跌趋势可能延续")

        return analysis

    def _identify_chip_anomaly_signals(self, historical_data: pd.DataFrame) -> List[Dict]:
        """识别筹码异动信号"""
        try:
            signals = []

            # 1. 识别异常放量
            recent_data = historical_data.tail(20)
            avg_volume = recent_data['volume'].mean()

            for i, row in recent_data.iterrows():
                if row['volume'] > avg_volume * 2:  # 成交量超过平均值2倍
                    signals.append({
                        'date': row['date'].strftime('%Y-%m-%d'),
                        'type': '异常放量',
                        'description': f"成交量{row['volume']:,.0f}，为近期平均值的{row['volume']/avg_volume:.1f}倍",
                        'price': row['close'],
                        'significance': '高' if row['volume'] > avg_volume * 3 else '中'
                    })

            # 2. 识别价格突破
            recent_prices = recent_data['close'].tolist()
            for i in range(1, len(recent_prices)):
                price_change = (recent_prices[i] - recent_prices[i-1]) / recent_prices[i-1] * 100
                if abs(price_change) > 5:  # 单日涨跌幅超过5%
                    signals.append({
                        'date': recent_data.iloc[i]['date'].strftime('%Y-%m-%d'),
                        'type': '价格异动',
                        'description': f"单日{'上涨' if price_change > 0 else '下跌'}{abs(price_change):.1f}%",
                        'price': recent_prices[i],
                        'significance': '高' if abs(price_change) > 8 else '中'
                    })

            # 按日期排序
            signals.sort(key=lambda x: x['date'], reverse=True)

            return signals[:10]  # 返回最近10个信号

        except Exception as e:
            logger.error(f"识别筹码异动信号失败: {e}")
            return []

    def _generate_trend_summary(self, migration: Dict, concentration: Dict,
                              volume: Dict, signals: List) -> List[str]:
        """生成趋势分析总结"""
        summary = []

        # 筹码迁移总结
        if migration.get('migration_direction') == "向上迁移":
            summary.append("筹码重心上移，资金在高位接盘")
        elif migration.get('migration_direction') == "向下迁移":
            summary.append("筹码重心下移，存在低位割肉现象")

        # 集中度变化总结
        if concentration.get('concentration_trend') == "集中度上升":
            summary.append("筹码集中度上升，可能有主力收集")
        elif concentration.get('concentration_trend') == "集中度下降":
            summary.append("筹码集中度下降，可能有主力派发")

        # 成交量总结
        volume_relation = volume.get('price_volume_relation', '')
        if volume_relation == "量价齐升":
            summary.append("量价配合良好，趋势较为健康")
        elif volume_relation == "价升量缩":
            summary.append("上涨缺乏量能支撑，需要谨慎")

        # 异动信号总结
        if len(signals) > 3:
            summary.append("近期异动信号较多，市场活跃度较高")
        elif len(signals) == 0:
            summary.append("近期无明显异动信号，市场相对平静")

        return summary

    def _generate_comprehensive_report(self, chip_distribution: Dict, profit_loss: Dict,
                                     main_cost: Dict, concentration: Dict, trend: Dict,
                                     current_price: float) -> Dict:
        """生成综合分析报告"""
        try:
            logger.info("生成筹码分布综合分析报告")

            # 1. 总体评估
            overall_assessment = self._generate_overall_assessment(
                profit_loss, main_cost, concentration, trend
            )

            # 2. 风险评估
            risk_assessment = self._generate_risk_assessment(
                profit_loss, main_cost, concentration
            )

            # 3. 操作建议
            operation_suggestions = self._generate_operation_suggestions(
                profit_loss, main_cost, concentration, trend, current_price
            )

            # 4. 关键指标摘要
            key_metrics = {
                '当前价格': current_price,
                '获利盘占比': f"{profit_loss.get('profit_chips_ratio', 0)}%",
                '套牢盘占比': f"{profit_loss.get('loss_chips_ratio', 0)}%",
                '主力平均成本': main_cost.get('main_cost_range', {}).get('avg_cost', 0),
                '筹码集中度': concentration.get('concentration_level', '未知'),
                '筹码迁移方向': trend.get('migration_analysis', {}).get('migration_direction', '未知')
            }

            return {
                'overall_assessment': overall_assessment,
                'risk_assessment': risk_assessment,
                'operation_suggestions': operation_suggestions,
                'key_metrics': key_metrics,
                'analysis_summary': self._generate_analysis_summary(
                    overall_assessment, risk_assessment, operation_suggestions
                )
            }

        except Exception as e:
            logger.error(f"生成综合分析报告失败: {e}")
            return {}

    def _generate_overall_assessment(self, profit_loss: Dict, main_cost: Dict,
                                   concentration: Dict, trend: Dict) -> List[str]:
        """生成总体评估"""
        assessment = []

        # 基于获利盘比例评估
        profit_ratio = profit_loss.get('profit_chips_ratio', 0)
        if profit_ratio > 70:
            assessment.append("获利盘占比过高，抛压沉重")
        elif profit_ratio < 30:
            assessment.append("获利盘占比较低，上涨阻力相对较小")

        # 基于筹码集中度评估
        concentration_level = concentration.get('concentration_level', '')
        if concentration_level in ['高度集中', '较为集中']:
            assessment.append("筹码集中度较高，主力控盘能力强")
        elif concentration_level in ['相对分散', '高度分散']:
            assessment.append("筹码相对分散，散户参与度较高")

        # 基于趋势评估
        migration_direction = trend.get('migration_analysis', {}).get('migration_direction', '')
        if migration_direction == "向上迁移":
            assessment.append("筹码重心上移，有资金在高位接盘")
        elif migration_direction == "向下迁移":
            assessment.append("筹码重心下移，存在低位抛售压力")

        return assessment

    def _generate_risk_assessment(self, profit_loss: Dict, main_cost: Dict,
                                concentration: Dict) -> Dict:
        """生成风险评估"""
        risks = []
        risk_level = "低"

        # 获利盘风险
        profit_ratio = profit_loss.get('profit_chips_ratio', 0)
        if profit_ratio > 80:
            risks.append("获利盘占比过高，存在较大抛压风险")
            risk_level = "高"
        elif profit_ratio > 60:
            risks.append("获利盘占比较高，需要警惕获利了结压力")
            if risk_level == "低":
                risk_level = "中"

        # 主力成本风险
        vs_avg_cost = main_cost.get('current_vs_cost', {}).get('vs_avg_cost', 0)
        if vs_avg_cost < -20:
            risks.append("当前价格远低于主力成本，主力深度套牢")
            risk_level = "高"
        elif vs_avg_cost < -10:
            risks.append("当前价格低于主力成本，主力处于亏损状态")
            if risk_level == "低":
                risk_level = "中"

        # 筹码集中度风险
        concentration_level = concentration.get('concentration_level', '')
        if concentration_level == "高度集中":
            risks.append("筹码高度集中，股价波动性较大")
            if risk_level == "低":
                risk_level = "中"

        return {
            'risk_level': risk_level,
            'risk_factors': risks,
            'risk_score': self._calculate_risk_score(profit_ratio, vs_avg_cost, concentration_level)
        }

    def _calculate_risk_score(self, profit_ratio: float, vs_avg_cost: float, concentration_level: str) -> int:
        """计算风险评分（0-100，分数越高风险越大）"""
        score = 0

        # 获利盘风险评分
        if profit_ratio > 80:
            score += 40
        elif profit_ratio > 60:
            score += 25
        elif profit_ratio > 40:
            score += 10

        # 主力成本风险评分
        if vs_avg_cost < -20:
            score += 30
        elif vs_avg_cost < -10:
            score += 15
        elif vs_avg_cost > 30:
            score += 20  # 价格过高也有风险

        # 筹码集中度风险评分
        if concentration_level == "高度集中":
            score += 20
        elif concentration_level == "较为集中":
            score += 10
        elif concentration_level == "高度分散":
            score += 15

        return min(100, score)

    def _generate_operation_suggestions(self, profit_loss: Dict, main_cost: Dict,
                                      concentration: Dict, trend: Dict, current_price: float) -> List[str]:
        """生成操作建议"""
        suggestions = []

        profit_ratio = profit_loss.get('profit_chips_ratio', 0)
        vs_avg_cost = main_cost.get('current_vs_cost', {}).get('vs_avg_cost', 0)
        concentration_level = concentration.get('concentration_level', '')
        migration_direction = trend.get('migration_analysis', {}).get('migration_direction', '')

        # 基于获利盘比例的建议
        if profit_ratio > 70:
            suggestions.append("获利盘占比过高，建议谨慎操作，注意止盈")
        elif profit_ratio < 30:
            suggestions.append("获利盘占比较低，可以考虑逢低布局")

        # 基于主力成本的建议
        if vs_avg_cost > 20:
            suggestions.append("当前价格远高于主力成本，建议注意风险控制")
        elif vs_avg_cost < -15:
            suggestions.append("当前价格低于主力成本较多，可能存在反弹机会")
        elif abs(vs_avg_cost) < 5:
            suggestions.append("当前价格接近主力成本，可以关注突破方向")

        # 基于筹码集中度的建议
        if concentration_level == "高度集中":
            suggestions.append("筹码高度集中，注意主力动向，控制仓位")
        elif concentration_level in ["相对分散", "高度分散"]:
            suggestions.append("筹码相对分散，可以采用分批建仓策略")

        # 基于趋势的建议
        if migration_direction == "向上迁移":
            suggestions.append("筹码重心上移，可以关注强势股的机会")
        elif migration_direction == "向下迁移":
            suggestions.append("筹码重心下移，建议等待企稳信号")

        return suggestions

    def _generate_analysis_summary(self, overall: List, risk: Dict, suggestions: List) -> List[str]:
        """生成分析总结"""
        summary = []

        # 总体情况
        if overall:
            summary.append("总体情况: " + "; ".join(overall[:2]))

        # 风险评估
        risk_level = risk.get('risk_level', '未知')
        summary.append(f"风险等级: {risk_level}")

        # 核心建议
        if suggestions:
            summary.append("核心建议: " + suggestions[0])

        return summary


# 创建全局实例
chip_analyzer = ChipDistributionAnalyzer()


def test_chip_distribution_analyzer():
    """测试筹码分布分析器"""
    print("🚀 测试筹码分布分析器")
    print("=" * 60)

    # 测试股票代码
    test_codes = ["000001", "601519", "300229"]

    for stock_code in test_codes:
        print(f"\n📊 测试股票: {stock_code}")
        print("-" * 40)

        try:
            result = chip_analyzer.get_chip_distribution_analysis(stock_code)

            if result.get('data_available', False):
                print(f"✅ 筹码分布分析成功")

                # 显示关键指标
                key_metrics = result.get('key_metrics', {})
                print(f"📋 关键指标:")
                for key, value in key_metrics.items():
                    print(f"  {key}: {value}")

                # 显示风险评估
                risk = result.get('comprehensive_report', {}).get('risk_assessment', {})
                if risk:
                    print(f"\n⚠️ 风险评估:")
                    print(f"  风险等级: {risk.get('risk_level', '未知')}")
                    print(f"  风险评分: {risk.get('risk_score', 0)}/100")

                # 显示操作建议
                suggestions = result.get('comprehensive_report', {}).get('operation_suggestions', [])
                if suggestions:
                    print(f"\n💡 操作建议:")
                    for suggestion in suggestions[:3]:
                        print(f"  • {suggestion}")

                print(f"✅ {stock_code} 分析完成")
            else:
                print(f"❌ {stock_code} 分析失败: {result.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ {stock_code} 测试失败: {str(e)}")

    print(f"\n🎯 筹码分布分析器测试完成")


if __name__ == "__main__":
    test_chip_distribution_analyzer()
