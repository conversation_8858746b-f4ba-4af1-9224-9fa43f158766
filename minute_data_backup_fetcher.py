#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分时数据备用获取器
专门为获取历史分时数据提供备用方案，不影响其他功能
"""

import pandas as pd
import requests
import json
import re
from datetime import datetime, timedelta
import time

class MinuteDataBackupFetcher:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })
    
    def get_minute_data_backup(self, code, period='1', count=240, target_date=None):
        """
        获取分时数据的备用方案

        重要说明：由于免费API的限制，历史分时数据获取受限：
        - 当日数据：可以获取完整的分时数据
        - 历史数据：由于API限制，只能获取当日数据作为参考

        Args:
            code: 股票代码
            period: 周期 ('1', '5', '15', '30', '60')
            count: 数据条数
            target_date: 目标日期 (YYYYMMDD格式)

        Returns:
            pandas.DataFrame: 分时数据，格式兼容主方案
        """
        try:
            today = datetime.now().strftime('%Y%m%d')
            is_today = target_date is None or target_date == today

            if is_today:
                print(f"🔄 使用备用方案获取 {code} 当日分时数据...")
            else:
                print(f"⚠️ 备用方案限制：{code} 历史日期 {target_date} 将使用当日数据作为参考")

            # 对于历史日期，由于API限制，返回空数据
            if not is_today:
                print(f"❌ 备用方案不支持历史日期 {target_date}，返回空数据")
                return pd.DataFrame()

            # 仅对当日数据使用腾讯API
            result = self._get_tencent_minute_data(code, period, count, target_date)
            if result is not None and not result.empty:
                print(f"✅ 腾讯备用API成功: {code}, {len(result)}条当日数据")
                return result

            # 备用方案：使用腾讯实时API（仅当日）
            result = self._get_tencent_realtime_data(code)
            if result is not None and not result.empty:
                print(f"✅ 腾讯实时API成功: {code}, {len(result)}条实时数据")
                return result

            print(f"❌ 所有备用方案都失败: {code}")
            return pd.DataFrame()

        except Exception as e:
            print(f"❌ 备用分时数据获取异常: {code}, {e}")
            return pd.DataFrame()
    
    def _get_tencent_minute_data(self, code, period='1', count=240, target_date=None):
        """获取腾讯历史分时数据"""
        try:
            # 腾讯财经历史分时数据API
            market = 'sz' if code.startswith(('000', '002', '300')) else 'sh'
            url = f"http://web.ifzq.gtimg.cn/appstock/app/minute/query"
            
            params = {
                'code': f'{market}{code}',
                '_var': f'min_data_{code}',
                'uin': '0',
                'format': 'json',
                'platform': 'jssdk',
                'enviroment': 'jssdk'
            }
            
            # 如果指定了日期，添加日期参数
            if target_date:
                params['date'] = target_date
            
            response = self.session.get(url, params=params, timeout=15)
            if response.status_code != 200:
                return None
            
            # 解析腾讯返回的数据
            text = response.text
            if not text or 'data' not in text:
                return None
            
            # 提取JSON数据
            json_match = re.search(r'min_data_\d+=(.*)', text)
            if not json_match:
                return None
            
            data = json.loads(json_match.group(1))
            if data.get('code') != 0:
                return None
            
            # 获取分时数据
            stock_data = data.get('data', {}).get(f'{market}{code}', {})
            minute_data = stock_data.get('data', {}).get('data', [])
            
            if not minute_data:
                return None
            
            # 转换为DataFrame格式，兼容主方案
            return self._parse_tencent_minute_data(minute_data, code, period, target_date)
            
        except Exception as e:
            print(f"    腾讯历史分时API异常: {e}")
            return None
    
    def _get_tencent_realtime_data(self, code):
        """获取腾讯实时数据作为备用"""
        try:
            # 腾讯实时数据API
            market = 'sz' if code.startswith(('000', '002', '300')) else 'sh'
            url = f"http://qt.gtimg.cn/q={market}{code}"
            
            response = self.session.get(url, timeout=10)
            if response.status_code != 200:
                return None
            
            data = response.text
            if not data or code not in data:
                return None
            
            # 解析实时数据，构造简单的分时数据
            return self._parse_tencent_realtime_data(data, code)
            
        except Exception as e:
            print(f"    腾讯实时API异常: {e}")
            return None
    
    def _parse_tencent_minute_data(self, minute_data, code, period, target_date=None):
        """解析腾讯分时数据为标准格式"""
        try:
            records = []

            # 确定使用的日期
            if target_date:
                # 将YYYYMMDD格式转换为YYYY-MM-DD格式
                if len(target_date) == 8:
                    date_formatted = f"{target_date[:4]}-{target_date[4:6]}-{target_date[6:8]}"
                else:
                    date_formatted = target_date
            else:
                date_formatted = datetime.now().strftime('%Y-%m-%d')

            for item in minute_data:
                # 腾讯分时数据格式: "0930 15.09 22912 34574208.00"
                # 时间 价格 成交量 成交额
                parts = item.split(' ')
                if len(parts) >= 4:
                    time_str = parts[0]
                    price = float(parts[1])
                    volume = int(parts[2])
                    amount = float(parts[3])

                    # 构造时间戳，使用正确的日期
                    time_formatted = f"{time_str[:2]}:{time_str[2:]}"
                    timestamp = f"{date_formatted} {time_formatted}:00"
                    
                    records.append({
                        'time': timestamp,
                        'open': price,
                        'high': price,
                        'low': price,
                        'close': price,
                        'volume': volume,
                        'amount': amount
                    })
            
            if not records:
                return pd.DataFrame()
            
            df = pd.DataFrame(records)
            df['time'] = pd.to_datetime(df['time'])
            df = df.set_index('time')
            
            # 根据period参数重采样
            if period != '1':
                freq_map = {'5': '5T', '15': '15T', '30': '30T', '60': '60T'}
                if period in freq_map:
                    df = df.resample(freq_map[period]).agg({
                        'open': 'first',
                        'high': 'max',
                        'low': 'min',
                        'close': 'last',
                        'volume': 'sum',
                        'amount': 'sum'
                    }).dropna()
            
            return df
            
        except Exception as e:
            print(f"    腾讯分时数据解析异常: {e}")
            return pd.DataFrame()
    
    def _parse_tencent_realtime_data(self, data, code):
        """解析腾讯实时数据为简单分时格式"""
        try:
            # 腾讯实时数据格式解析
            # v_sz002636="51~金安国纪~002636~15.09~13.72~15.09~71644~0~71644~15.09~..."
            
            # 提取数据部分
            match = re.search(r'"([^"]+)"', data)
            if not match:
                return pd.DataFrame()
            
            parts = match.group(1).split('~')
            if len(parts) < 10:
                return pd.DataFrame()
            
            # 解析关键字段
            current_price = float(parts[3])
            volume = int(parts[6]) * 100  # 腾讯数据是手，需要转换为股
            
            # 构造简单的当前时刻数据
            now = datetime.now()
            
            record = {
                'time': now,
                'open': current_price,
                'high': current_price,
                'low': current_price,
                'close': current_price,
                'volume': volume,
                'amount': volume * current_price
            }
            
            df = pd.DataFrame([record])
            df = df.set_index('time')
            
            return df
            
        except Exception as e:
            print(f"    腾讯实时数据解析异常: {e}")
            return pd.DataFrame()
    
    def test_backup_api(self, code='002636'):
        """测试备用API的可用性"""
        print(f"🔍 测试备用API可用性: {code}")
        
        # 测试历史分时数据
        result1 = self._get_tencent_minute_data(code, '1', 240)
        if result1 is not None and not result1.empty:
            print(f"✅ 腾讯历史分时API可用: {len(result1)}条数据")
        else:
            print(f"❌ 腾讯历史分时API不可用")
        
        # 测试实时数据
        result2 = self._get_tencent_realtime_data(code)
        if result2 is not None and not result2.empty:
            print(f"✅ 腾讯实时API可用: {len(result2)}条数据")
        else:
            print(f"❌ 腾讯实时API不可用")
        
        return result1 if result1 is not None and not result1.empty else result2

if __name__ == "__main__":
    # 测试备用获取器
    fetcher = MinuteDataBackupFetcher()
    result = fetcher.test_backup_api('002636')
    if result is not None and not result.empty:
        print(f"\n📊 测试结果:")
        print(result.head())
        print(f"\n数据格式: {result.columns.tolist()}")
    else:
        print("❌ 备用API测试失败")
