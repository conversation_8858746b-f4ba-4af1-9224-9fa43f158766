#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件：检查efinance获取的股票数据格式
使用项目中已有的数据获取方法
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入项目中的数据获取方法
from 趋势以形成尾盘缩量小阴线小阳线尾盘买入 import TrendBreakthroughStrategy

def check_data_format():
    """检查efinance获取的股票数据格式"""
    print("🔍 检查efinance股票数据格式")
    print("=" * 50)
    
    # 创建策略实例以复用其数据获取方法
    strategy = TrendBreakthroughStrategy()
    
    # 选择一只股票进行测试（这里使用中国国贸600007）
    stock_code = '600007'
    print(f"📈 测试股票: {stock_code} 中国国贸")
    
    try:
        # 使用项目中的方法获取K线数据
        kline_data = strategy._get_efinance_kline_with_retry(stock_code)
        
        if kline_data is None or kline_data.empty:
            print("❌ 无法获取K线数据")
            return
            
        print(f"✅ 成功获取K线数据，数据形状: {kline_data.shape}")
        
        # 标准化列名
        kline_data = strategy._standardize_kline_columns(kline_data)
        
        # 显示列名
        print(f"\n📋 数据列名:")
        for i, column in enumerate(kline_data.columns, 1):
            print(f"   {i}. {column}")
        
        # 显示数据类型
        print(f"\n📊 数据类型:")
        print(kline_data.dtypes)
        
        # 显示前几行数据
        print(f"\n📋 前5行数据:")
        print(kline_data.head())
        
        # 检查日期列格式
        date_column = strategy._get_date_column(kline_data)
        if date_column:
            print(f"\n📅 日期列信息:")
            print(f"   - 日期列名: {date_column}")
            print(f"   - 第一个日期: {kline_data[date_column].iloc[0]} (类型: {type(kline_data[date_column].iloc[0])})")
            print(f"   - 最后一个日期: {kline_data[date_column].iloc[-1]} (类型: {type(kline_data[date_column].iloc[-1])})")
            print(f"   - 日期列类型: {kline_data[date_column].dtype}")
            
            # 检查日期是否为datetime类型
            if pd.api.types.is_datetime64_any_dtype(kline_data[date_column]):
                print(f"   ✅ 日期列已经是datetime类型")
            else:
                print(f"   ⚠️ 日期列不是datetime类型，需要转换")
        else:
            print(f"\n❌ 未找到日期列")
        
        # 检查关键数据列
        key_columns = ['开盘', '收盘', '最高', '最低', '成交量']
        print(f"\n🔑 关键数据列检查:")
        for col in key_columns:
            if col in kline_data.columns:
                print(f"   ✅ {col}: 存在 (类型: {kline_data[col].dtype})")
            else:
                print(f"   ❌ {col}: 不存在")
        
        # 检查是否有空值
        print(f"\n🔍 空值检查:")
        null_counts = kline_data.isnull().sum()
        has_nulls = False
        for col, count in null_counts.items():
            if count > 0:
                print(f"   ⚠️ {col}: {count} 个空值")
                has_nulls = True
        if not has_nulls:
            print("   ✅ 无空值")
            
        # 获取最近几天的数据用于测试
        print(f"\n📅 最近5天数据:")
        recent_data = kline_data.tail()
        for idx, row in recent_data.iterrows():
            print(f"   日期: {row[date_column] if date_column else 'N/A'}")
            print(f"     开盘: {row['开盘'] if '开盘' in row else 'N/A'}")
            print(f"     收盘: {row['收盘'] if '收盘' in row else 'N/A'}")
            print(f"     最高: {row['最高'] if '最高' in row else 'N/A'}")
            print(f"     最低: {row['最低'] if '最低' in row else 'N/A'}")
            print(f"     成交量: {row['成交量'] if '成交量' in row else 'N/A'}")
            print()
            
    except Exception as e:
        print(f"❌ 获取数据时出错: {e}")
        import traceback
        traceback.print_exc()

def check_date_matching_logic():
    """检查日期匹配逻辑"""
    print("\n" + "=" * 50)
    print("🔍 检查日期匹配逻辑")
    print("=" * 50)
    
    # 创建策略实例以复用其数据获取方法
    strategy = TrendBreakthroughStrategy()
    
    stock_code = '600007'
    target_date = '20230101'  # 示例日期
    
    try:
        # 使用项目中的方法获取K线数据
        kline_data = strategy._get_efinance_kline_with_retry(stock_code)
        
        if kline_data is None or kline_data.empty:
            print("❌ 无法获取K线数据")
            return
            
        print(f"📈 获取到 {len(kline_data)} 条K线数据")
        
        # 标准化列名
        kline_data = strategy._standardize_kline_columns(kline_data)
        
        # 使用项目中的方法获取指定日期的数据
        day_data = strategy._get_day_data_from_kline(kline_data, target_date)
        
        if day_data:
            print(f"✅ 成功找到 {target_date} 的数据:")
            print(f"   开盘: {day_data.get('开盘', 'N/A')}")
            print(f"   收盘: {day_data.get('收盘', 'N/A')}")
            print(f"   最高: {day_data.get('最高', 'N/A')}")
            print(f"   最低: {day_data.get('最低', 'N/A')}")
            print(f"   成交量: {day_data.get('成交量', 'N/A')}")
        else:
            print(f"⚠️ 未找到 {target_date} 的数据")
            
        # 检查前一日收盘价获取逻辑
        prev_close = strategy._get_previous_close(kline_data, target_date)
        print(f"📊 {target_date} 的前一日收盘价: {prev_close}")
        
    except Exception as e:
        print(f"❌ 检查日期匹配时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    check_data_format()
    check_date_matching_logic()