#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高频数据测试器 - 生成真实游资炒作的高频走势数据
按照主应用的监控频率：开盘5分钟每15秒，之后每30秒
"""

import sys
import os
import time
import random
import math
from datetime import datetime, timedelta
from typing import Dict, List, Any, Tuple

class HighFrequencyTester:
    """高频数据测试器 - 模拟真实游资炒作走势"""
    
    def __init__(self):
        self.notifications_captured = []
        self.realtime_data_series = {}
        self.historical_base_data = {}
        self.monitored_stocks = {}

        # 初始化专业系统
        self.confirmation_system = self.ContinuousConfirmationSystem(required_confirmations=2)
        self.algorithm_switcher = self.ProfessionalAlgorithmSwitcher()
        self.price_tracker = self.PriceTrajectoryTracker(max_history=30)

        # 初始化测试股票
        self.setup_test_stock()
    
    def setup_test_stock(self):
        """设置测试股票"""
        test_code = 'TEST001'
        
        self.monitored_stocks[test_code] = {
            'name': '游资标的',
            'initialized': True
        }
        
        self.historical_base_data[test_code] = {
            'k_line_data': [],
            'volume_data': {'avg_volume_5d': 1000000, 'avg_volume_10d': 1200000},
            'price_stats': {'avg_price_5d': 10.0},
            'circulating_shares': 50000
        }
        
        self.realtime_data_series[test_code] = []
    
    def generate_gaming_scenario_1(self) -> List[Dict[str, Any]]:
        """
        场景1：开盘5分钟从上涨5%上涨到上涨10%并一字板

        特征：开盘直接高开5%，然后5分钟内快速拉涨停
        真实案例：游资集中发力的强势股
        基于真实数据：量比1-8倍，买卖盘合理分布，涨停后有少量卖盘
        """
        base_price = 10.0
        data_points = []

        # 修复后的真实数据：基于实际股票交易规律
        time_points = [
            # 集合竞价确定开盘价
            ("09:25:00", 5.0, 2.8, 15000, 45000, 8000, 25000, "集合竞价确定开盘价5%"),

            # 开盘价确认（价格不变）
            ("09:30:00", 5.0, 2.8, 15000, 45000, 8000, 25000, "开盘价确认"),

            # 开盘后快速拉升（每15秒，量比逐步放大但不超过8倍）
            ("09:30:15", 5.3, 3.2, 18000, 52000, 7000, 22000, "开盘后微涨"),
            ("09:30:30", 5.8, 3.8, 22000, 65000, 6000, 18000, "30秒后拉升"),
            ("09:30:45", 6.5, 4.5, 28000, 82000, 5000, 15000, "45秒后加速"),
            ("09:31:00", 7.2, 5.2, 35000, 105000, 4000, 12000, "1分钟强势冲高"),
            ("09:31:15", 8.0, 6.0, 45000, 135000, 3000, 9000, "1分15秒突破8%"),
            ("09:31:30", 8.8, 6.8, 58000, 175000, 2000, 6000, "1分30秒接近涨停"),
            ("09:31:45", 9.5, 7.5, 75000, 225000, 1500, 4500, "1分45秒即将封板"),

            # 涨停封板（仍有少量卖盘，符合实际）
            ("09:32:00", 10.0, 8.0, 95000, 285000, 1000, 3000, "2分钟封涨停"),
            ("09:32:15", 10.0, 7.8, 110000, 330000, 800, 2400, "封板稳固"),
            ("09:32:30", 10.0, 7.5, 125000, 375000, 600, 1800, "封单增厚"),
            ("09:32:45", 10.0, 7.2, 140000, 420000, 500, 1500, "封死涨停"),
            ("09:33:00", 10.0, 7.0, 155000, 465000, 400, 1200, "维持封板"),
            ("09:33:15", 10.0, 6.8, 170000, 510000, 300, 900, "稳定封死"),
            ("09:33:30", 10.0, 6.5, 185000, 555000, 200, 600, "封单如山"),
            ("09:33:45", 10.0, 6.2, 200000, 600000, 150, 450, "不可撼动"),
            ("09:34:00", 10.0, 6.0, 215000, 645000, 100, 300, "游资集结"),
            ("09:34:15", 10.0, 5.8, 230000, 690000, 80, 240, "封死如铁"),
            ("09:34:30", 10.0, 5.5, 245000, 735000, 50, 150, "完全封死"),
            ("09:35:00", 10.0, 5.2, 260000, 780000, 30, 90, "一字板形成")
        ]
        
        for time_str, gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in time_points:
            current_price = base_price * (1 + gain / 100)
            
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1) if sell5_total > 0 else [0, 0, 0, 0, 0]
            
            # 买入信号预期：在快速拉升阶段应该发信号
            expected_signal = time_str in ["09:25:00", "09:30:00", "09:30:30", "09:31:00"]
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc,
                'expected_signal': expected_signal,
                'time_period': '开盘量能监控' if time_str <= "09:35:00" else '均价征服监控'
            }
            data_points.append(data_point)
        
        return data_points
    
    def generate_gaming_scenario_2(self) -> List[Dict[str, Any]]:
        """
        场景2：开盘上涨1%到5%，等待数分钟后上涨10%并一字板

        特征：开盘温和上涨，横盘蓄势，然后突然发力涨停
        真实案例：主力控盘后突然发力
        修复：量比合理范围1-6倍，买卖盘真实分布
        """
        base_price = 10.0
        data_points = []

        time_points = [
            # 开盘温和上涨阶段（每15秒）
            ("09:30:00", 1.2, 1.5, 8000, 24000, 7000, 21000, "温和开盘"),
            ("09:30:15", 1.6, 1.8, 9000, 27000, 6500, 19500, "缓慢上涨"),
            ("09:30:30", 2.1, 2.0, 10000, 30000, 6000, 18000, "持续推升"),
            ("09:30:45", 2.6, 2.2, 11000, 33000, 5500, 16500, "稳步上行"),
            ("09:31:00", 3.0, 2.4, 12000, 36000, 5000, 15000, "温和突破3%"),
            ("09:31:15", 3.4, 2.6, 13000, 39000, 4800, 14400, "继续上涨"),
            ("09:31:30", 3.8, 2.8, 14000, 42000, 4500, 13500, "接近4%"),
            ("09:31:45", 4.2, 3.0, 15000, 45000, 4200, 12600, "突破4%"),
            ("09:32:00", 4.6, 3.2, 16000, 48000, 4000, 12000, "接近5%"),

            # 横盘蓄势阶段（每15秒，量能收敛）
            ("09:32:15", 4.7, 3.0, 15500, 46500, 4100, 12300, "开始横盘"),
            ("09:32:30", 4.5, 2.8, 15000, 45000, 4300, 12900, "小幅回调"),
            ("09:32:45", 4.6, 2.9, 15200, 45600, 4200, 12600, "重新上行"),
            ("09:33:00", 4.7, 3.0, 15400, 46200, 4100, 12300, "横盘整理"),
            ("09:33:15", 4.4, 2.7, 14800, 44400, 4400, 13200, "再次回调"),
            ("09:33:30", 4.6, 2.9, 15100, 45300, 4200, 12600, "重新站稳"),
            ("09:33:45", 4.8, 3.1, 15600, 46800, 4000, 12000, "突破4.8%"),
            ("09:34:00", 4.9, 3.2, 15800, 47400, 3900, 11700, "站稳接近5%"),

            # 突然发力阶段（每15秒，量能爆发）
            ("09:34:15", 5.6, 4.2, 22000, 66000, 3000, 9000, "突然发力"),
            ("09:34:30", 6.5, 5.0, 28000, 84000, 2500, 7500, "快速拉升"),
            ("09:34:45", 7.6, 5.8, 35000, 105000, 2000, 6000, "加速冲高"),
            ("09:35:00", 8.8, 6.5, 45000, 135000, 1500, 4500, "接近9%"),

            # 切换到30秒频率，冲击涨停
            ("09:35:30", 9.6, 7.2, 58000, 174000, 1000, 3000, "即将封板"),
            ("09:36:00", 10.0, 7.8, 75000, 225000, 600, 1800, "封涨停板"),
            ("09:36:30", 10.0, 7.5, 85000, 255000, 400, 1200, "封板稳固"),
            ("09:37:00", 10.0, 7.2, 95000, 285000, 300, 900, "稳固封板")
        ]
        
        for time_str, gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in time_points:
            current_price = base_price * (1 + gain / 100)
            
            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1) if sell5_total > 0 else [0, 0, 0, 0, 0]
            
            # 买入信号预期：在突然发力阶段应该发信号
            expected_signal = time_str in ["09:34:15", "09:34:30", "09:35:00"]
            
            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc,
                'expected_signal': expected_signal,
                'time_period': '开盘量能监控' if time_str <= "09:40:00" else '均价征服监控'
            }
            data_points.append(data_point)
        
        return data_points
    
    def _distribute_volume(self, total_volume: int, first_level_volume: int) -> List[int]:
        """将总量分配到五个档位"""
        if total_volume <= 0:
            return [0, 0, 0, 0, 0]
        
        volumes = [first_level_volume]
        remaining = max(0, total_volume - first_level_volume)
        
        # 按递减比例分配剩余量到其他四档
        ratios = [0.4, 0.25, 0.2, 0.15]  # 买二到买五的比例
        
        for ratio in ratios:
            volume = int(remaining * ratio)
            volumes.append(volume)
            remaining -= volume
        
        return volumes[:5]

    def generate_gaming_scenario_3(self) -> List[Dict[str, Any]]:
        """
        场景3：开盘上涨1%直到20分钟后上涨到1.8%，然后数分钟内上涨10%并一字板

        特征：长时间低位横盘，突然爆发涨停
        真实案例：主力长时间吸筹后突然拉升
        """
        base_price = 10.0
        data_points = []

        # 开盘低位横盘阶段（前20分钟，每30秒采样关键点）
        early_points = [
            ("09:30:00", 1.0, 1.2, 12000, 35000, 10000, 30000, "开盘微涨1%"),
            ("09:30:30", 1.1, 1.1, 11000, 33000, 10500, 31000, "横盘整理"),
            ("09:31:00", 0.9, 1.0, 10000, 30000, 11000, 32000, "小幅回调"),
            ("09:31:30", 1.0, 1.1, 11500, 34000, 10200, 30500, "重回1%"),
            ("09:32:00", 1.2, 1.2, 12500, 36000, 9800, 29000, "微幅上涨"),
            ("09:33:00", 1.1, 1.1, 11800, 35000, 10100, 30200, "继续横盘"),
            ("09:34:00", 1.3, 1.2, 13000, 37000, 9500, 28500, "缓慢推升"),
            ("09:35:00", 1.2, 1.1, 12200, 36000, 9900, 29500, "回到横盘"),

            # 切换到30秒频率（9:35后）
            ("09:36:00", 1.4, 1.3, 13500, 38000, 9200, 27500, "小幅突破"),
            ("09:37:00", 1.3, 1.2, 12800, 37000, 9600, 28800, "再次回调"),
            ("09:38:00", 1.5, 1.3, 14000, 39000, 8900, 26800, "重新上行"),
            ("09:39:00", 1.4, 1.2, 13200, 38000, 9300, 28000, "横盘震荡"),
            ("09:40:00", 1.6, 1.4, 14500, 40000, 8600, 25800, "缓慢上行"),
            ("09:41:00", 1.5, 1.3, 13800, 39000, 8800, 26500, "小幅回调"),
            ("09:42:00", 1.7, 1.4, 15000, 41000, 8300, 25000, "继续推升"),
            ("09:43:00", 1.6, 1.3, 14200, 40000, 8500, 25500, "再次回调"),
            ("09:44:00", 1.8, 1.5, 15500, 42000, 8000, 24000, "达到1.8%"),
            ("09:45:00", 1.7, 1.4, 14800, 41000, 8200, 24500, "小幅回调"),
            ("09:46:00", 1.8, 1.5, 15200, 42000, 7900, 23800, "重回1.8%"),
            ("09:47:00", 1.8, 1.5, 15300, 42500, 7800, 23500, "横盘1.8%"),
            ("09:48:00", 1.8, 1.5, 15100, 42000, 7900, 23800, "维持1.8%"),
            ("09:49:00", 1.8, 1.5, 15400, 42800, 7700, 23200, "稳定1.8%"),
            ("09:50:00", 1.8, 1.5, 15200, 42200, 7800, 23500, "继续横盘")
        ]

        # 突然爆发阶段（每15秒）
        breakout_points = [
            ("09:50:15", 2.5, 2.8, 28000, 75000, 5000, 15000, "突然发力"),
            ("09:50:30", 3.8, 4.2, 45000, 120000, 3000, 9000, "快速拉升"),
            ("09:50:45", 5.2, 6.0, 70000, 180000, 2000, 6000, "加速冲高"),
            ("09:51:00", 6.8, 8.0, 110000, 280000, 1200, 3500, "强势突破"),
            ("09:51:15", 8.5, 10.5, 170000, 420000, 800, 2200, "接近涨停"),
            ("09:51:30", 9.6, 12.0, 250000, 600000, 400, 1000, "即将封板"),
            ("09:51:45", 10.0, 15.0, 350000, 850000, 0, 0, "封涨停板"),
            ("09:52:00", 10.0, 16.0, 400000, 950000, 0, 0, "封死涨停"),
            ("09:52:15", 10.0, 17.0, 450000, 1100000, 0, 0, "稳固封板")
        ]

        all_points = early_points + breakout_points

        for time_str, gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in all_points:
            current_price = base_price * (1 + gain / 100)

            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1) if sell5_total > 0 else [0, 0, 0, 0, 0]

            # 买入信号预期：在突然爆发阶段应该发信号
            expected_signal = time_str in ["09:50:15", "09:50:30", "09:51:00"]

            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc,
                'expected_signal': expected_signal,
                'time_period': '开盘量能监控' if time_str <= "09:40:00" else '均价征服监控'
            }
            data_points.append(data_point)

        return data_points

    def generate_gaming_scenario_4(self) -> List[Dict[str, Any]]:
        """
        场景4：开盘上涨0.5%直到10分钟后上涨到1%，横盘到下午1点开盘，5分钟内从1%上涨到8%，随后2小时后回落到3%

        特征：全天弱势，午后突然拉升，但最终回落（假突破）
        真实案例：主力诱多出货
        """
        base_price = 10.0
        data_points = []

        # 开盘弱势阶段（前10分钟）
        morning_weak = [
            ("09:30:00", 0.5, 0.8, 8000, 24000, 12000, 35000, "弱势开盘"),
            ("09:30:30", 0.6, 0.9, 8500, 25000, 11500, 34000, "微幅上涨"),
            ("09:31:00", 0.4, 0.7, 7500, 22000, 12500, 36000, "小幅回调"),
            ("09:31:30", 0.7, 0.9, 9000, 26000, 11000, 32000, "重新上行"),
            ("09:32:00", 0.8, 1.0, 9500, 27000, 10500, 31000, "缓慢推升"),
            ("09:33:00", 0.9, 1.0, 10000, 28000, 10000, 30000, "接近1%"),
            ("09:34:00", 1.0, 1.1, 10500, 29000, 9500, 28500, "突破1%"),
            ("09:35:00", 0.9, 1.0, 9800, 27500, 9800, 29000, "回到1%以下"),
            ("09:36:00", 1.0, 1.1, 10200, 28500, 9600, 28800, "重回1%"),
            ("09:37:00", 1.0, 1.1, 10300, 28800, 9500, 28500, "维持1%"),
            ("09:38:00", 1.1, 1.1, 10600, 29500, 9200, 27500, "小幅突破"),
            ("09:39:00", 1.0, 1.0, 10100, 28200, 9600, 28800, "回到1%"),
            ("09:40:00", 1.0, 1.0, 10000, 28000, 9700, 29000, "稳定1%")
        ]

        # 上午横盘（每分钟采样）
        morning_flat = [
            ("09:45:00", 1.0, 1.0, 10000, 28000, 9700, 29000, "上午横盘"),
            ("09:50:00", 1.1, 1.0, 10200, 28500, 9500, 28500, "微幅波动"),
            ("10:00:00", 0.9, 0.9, 9800, 27500, 9900, 29500, "小幅回调"),
            ("10:30:00", 1.0, 1.0, 10000, 28000, 9700, 29000, "重回1%"),
            ("11:00:00", 1.0, 1.0, 10100, 28200, 9600, 28800, "维持横盘"),
            ("11:30:00", 1.1, 1.0, 10300, 28800, 9400, 28200, "收盘前微涨")
        ]

        # 午后突然拉升（每30秒）- 修复：假突破特征，价涨量缩
        afternoon_surge = [
            ("13:00:00", 1.0, 1.0, 10000, 28000, 9500, 28500, "午后开盘"),
            ("13:00:30", 1.8, 1.5, 12000, 36000, 8000, 24000, "温和拉升"),
            ("13:01:00", 2.5, 1.8, 14000, 42000, 7000, 21000, "继续上涨"),
            ("13:01:30", 3.2, 2.0, 15000, 45000, 6500, 19500, "突破3%"),
            ("13:02:00", 4.0, 2.2, 16000, 48000, 6000, 18000, "冲击4%"),
            ("13:02:30", 5.0, 2.3, 17000, 51000, 5500, 16500, "突破5%"),
            ("13:03:00", 6.2, 2.4, 18000, 54000, 5000, 15000, "冲高6%"),
            ("13:03:30", 7.5, 2.3, 17500, 52500, 4800, 14400, "价涨量缩"),
            ("13:04:00", 8.2, 2.1, 16000, 48000, 4500, 13500, "达到8%但量缩"),
            ("13:04:30", 8.0, 1.9, 15000, 45000, 4800, 14400, "开始回调"),
            ("13:05:00", 7.8, 1.8, 14500, 43500, 5000, 15000, "量价背离明显")
        ]

        # 随后回落（每分钟采样关键点）- 修复：卖盘逐渐增加
        afternoon_fall = [
            ("13:10:00", 7.2, 1.6, 12000, 36000, 6000, 18000, "开始回落"),
            ("13:20:00", 6.5, 1.4, 10000, 30000, 7000, 21000, "继续下跌"),
            ("13:30:00", 5.8, 1.2, 8000, 24000, 8000, 24000, "跌破6%"),
            ("14:00:00", 5.0, 1.1, 7000, 21000, 8500, 25500, "午后弱势"),
            ("14:30:00", 4.2, 1.0, 6000, 18000, 9000, 27000, "继续回落"),
            ("15:00:00", 3.0, 0.9, 5000, 15000, 10000, 30000, "收盘回落到3%")
        ]

        all_points = morning_weak + morning_flat + afternoon_surge + afternoon_fall

        for time_str, gain, vol_ratio, buy1, buy5_total, sell1, sell5_total, desc in all_points:
            current_price = base_price * (1 + gain / 100)

            bid_volumes = self._distribute_volume(buy5_total, buy1)
            ask_volumes = self._distribute_volume(sell5_total, sell1) if sell5_total > 0 else [0, 0, 0, 0, 0]

            # 买入信号预期：午后拉升时可能发信号，但这是假突破，不应该发信号
            expected_signal = False  # 这是假突破场景，不应该发任何买入信号

            data_point = {
                'current_price': round(current_price, 2),
                'prev_close': base_price,
                'change_pct': round(gain, 2),
                'volume_ratio': round(vol_ratio, 2),
                'volume': int(vol_ratio * 800000),
                'bid_volumes': bid_volumes,
                'ask_volumes': ask_volumes,
                'timestamp': time_str,
                'scenario_desc': desc,
                'expected_signal': expected_signal,
                'time_period': self._get_time_period(time_str)
            }
            data_points.append(data_point)

        return data_points

    def _get_time_period(self, time_str):
        """根据时间确定监控时段"""
        if time_str <= "09:40:00":
            return "开盘量能监控"
        elif time_str <= "10:30:00":
            return "均价征服监控"
        else:
            return "盘中突击监控"

    # ==================== 价格轨迹记忆系统 ====================

    class PriceTrajectoryTracker:
        """价格轨迹跟踪器 - 记忆股票历史状态"""

        def __init__(self, max_history=30):
            self.price_history = []      # 价格历史
            self.gain_history = []       # 涨幅历史
            self.volume_history = []     # 量比历史
            self.time_history = []       # 时间历史
            self.max_history = max_history  # 最大历史记录数（约7.5分钟数据）

        def add_data_point(self, timestamp, price, gain, volume):
            """添加新的数据点"""
            self.price_history.append(price)
            self.gain_history.append(gain)
            self.volume_history.append(volume)
            self.time_history.append(timestamp)

            # 保持历史记录在合理范围内
            if len(self.price_history) > self.max_history:
                self.price_history.pop(0)
                self.gain_history.pop(0)
                self.volume_history.pop(0)
                self.time_history.pop(0)

        def get_stock_state(self):
            """分析股票当前状态"""
            if len(self.gain_history) < 5:
                return "数据不足", {}

            current_gain = self.gain_history[-1]

            # 判断各种状态
            if self.is_v_shaped_reversal():
                low_point = min(self.gain_history[-15:]) if len(self.gain_history) >= 15 else min(self.gain_history)
                return "V字反转", {
                    'low_point': low_point,
                    'current_gain': current_gain,
                    'reversal_strength': current_gain - low_point
                }

            elif self.is_sideways_trading():
                duration = self.get_sideways_duration()
                avg_gain = sum(self.gain_history[-10:]) / len(self.gain_history[-10:])
                return "横盘状态", {
                    'avg_gain': avg_gain,
                    'duration_points': duration,
                    'current_gain': current_gain
                }

            elif self.is_continuous_uptrend():
                start_gain = self.gain_history[-10] if len(self.gain_history) >= 10 else self.gain_history[0]
                return "持续上涨", {
                    'start_gain': start_gain,
                    'current_gain': current_gain,
                    'total_rise': current_gain - start_gain
                }

            elif self.is_pullback_from_high():
                high_point = max(self.gain_history[-15:]) if len(self.gain_history) >= 15 else max(self.gain_history)
                return "冲高回落", {
                    'high_point': high_point,
                    'current_gain': current_gain,
                    'pullback_amount': high_point - current_gain
                }

            else:
                return "普通状态", {'current_gain': current_gain}

        def is_v_shaped_reversal(self):
            """判断是否V字反转"""
            if len(self.gain_history) < 10:
                return False

            # 检查最近15个点（如果有的话）
            check_length = min(15, len(self.gain_history))
            recent_gains = self.gain_history[-check_length:]

            # 找到最低点
            min_gain = min(recent_gains)
            min_index = recent_gains.index(min_gain)

            # V字特征：
            # 1. 最低点不在最后几个点（已经开始反转）
            # 2. 最低点是负数或很小的正数
            # 3. 从最低点反转幅度足够大
            if (min_index < check_length - 3 and  # 最低点不在最后3个点
                min_gain < 1.0 and  # 最低点涨幅小于1%
                self.gain_history[-1] > min_gain + 1.5):  # 反转幅度超过1.5%
                return True

            return False

        def is_sideways_trading(self, threshold=0.8):
            """判断是否在横盘"""
            if len(self.gain_history) < 8:
                return False

            # 检查最近8个点的波动范围
            recent_gains = self.gain_history[-8:]
            max_gain = max(recent_gains)
            min_gain = min(recent_gains)

            # 横盘特征：波动范围小于阈值
            return (max_gain - min_gain) <= threshold

        def get_sideways_duration(self):
            """获取横盘持续时间（数据点数）"""
            if not self.is_sideways_trading():
                return 0

            # 从后往前找，直到找到不符合横盘条件的点
            duration = 0
            for i in range(len(self.gain_history) - 1, 0, -1):
                if i < 8:
                    break

                check_gains = self.gain_history[i-7:i+1]  # 检查8个点
                if max(check_gains) - min(check_gains) <= 0.8:
                    duration += 1
                else:
                    break

            return duration

        def is_continuous_uptrend(self):
            """判断是否持续上涨"""
            if len(self.gain_history) < 8:
                return False

            recent_gains = self.gain_history[-8:]

            # 检查上涨趋势：大部分时间在上涨
            uptrend_count = 0
            for i in range(1, len(recent_gains)):
                if recent_gains[i] >= recent_gains[i-1] - 0.3:  # 允许小幅回调
                    uptrend_count += 1

            # 至少70%的时间在上涨，且总体涨幅为正
            return (uptrend_count >= 5 and
                    recent_gains[-1] > recent_gains[0] + 0.5)

        def is_pullback_from_high(self):
            """判断是否冲高回落"""
            if len(self.gain_history) < 10:
                return False

            # 检查最近15个点中是否有明显高点
            check_length = min(15, len(self.gain_history))
            recent_gains = self.gain_history[-check_length:]

            max_gain = max(recent_gains)
            current_gain = self.gain_history[-1]

            # 冲高回落特征：曾经冲到高点，现在明显回落
            return (max_gain > current_gain + 1.2 and  # 从高点回落超过1.2%
                    max_gain > 3.0)  # 高点至少达到3%

        def get_state_description(self):
            """获取状态的详细描述"""
            state, details = self.get_stock_state()

            if state == "数据不足":
                return "数据不足: 等待更多数据点"
            elif state == "V字反转":
                return f"V字反转: 从{details['low_point']:.1f}%反转到{details['current_gain']:.1f}% (反转{details['reversal_strength']:.1f}%)"
            elif state == "横盘状态":
                return f"横盘状态: {details['avg_gain']:.1f}%附近横盘{details['duration_points']}个点"
            elif state == "持续上涨":
                return f"持续上涨: 从{details['start_gain']:.1f}%涨到{details['current_gain']:.1f}% (累计{details['total_rise']:.1f}%)"
            elif state == "冲高回落":
                return f"冲高回落: 从{details['high_point']:.1f}%回落到{details['current_gain']:.1f}% (回落{details['pullback_amount']:.1f}%)"
            else:
                current_gain = details.get('current_gain', 0)
                return f"普通状态: 当前{current_gain:.1f}%"

    # ==================== 连续确认机制 ====================

    class ContinuousConfirmationSystem:
        """连续确认系统 - 避免瞬间波动的误判"""

        def __init__(self, required_confirmations=2):
            self.confirmation_queue = []  # 确认队列
            self.required_confirmations = required_confirmations  # 需要连续确认次数
            self.algorithm_history = []  # 算法历史记录
            self.time_in_range_tracker = {}  # 涨幅区间持续时间跟踪

        def add_signal(self, timestamp, signal_data, algorithm_info):
            """添加信号到确认队列（基于算法类型调整确认要求）"""
            confirmation_entry = {
                'timestamp': timestamp,
                'buy_signal': signal_data.get('buy_signal', False),
                'signal_strength': signal_data.get('signal_strength', 0),
                'algorithm_type': algorithm_info.get('algorithm_type', ''),
                'algorithm_confidence': algorithm_info.get('confidence', 0),
                'current_gain': signal_data.get('current_gain', 0),
                'volume_ratio': signal_data.get('volume_ratio', 0),
                'reason': signal_data.get('reason', ''),
                'stock_state': algorithm_info.get('stock_state', '普通状态')
            }

            self.confirmation_queue.append(confirmation_entry)

            # 根据算法类型调整确认要求
            algorithm_type = algorithm_info.get('algorithm_type', '')
            stock_state = algorithm_info.get('stock_state', '普通状态')

            # 动态调整确认次数
            if algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
                # V字反转：降低确认要求，机会稍纵即逝
                required_confirmations = 1
            elif stock_state == "横盘状态":
                # 横盘突破：中等确认要求
                required_confirmations = 2
            elif algorithm_type in ['trend_following', 'momentum_continuation']:
                # 趋势跟随：标准确认要求
                required_confirmations = 2
            else:
                # 其他情况：标准确认要求
                required_confirmations = 2

            # 保持队列长度
            if len(self.confirmation_queue) > required_confirmations:
                self.confirmation_queue.pop(0)

            # 检查是否满足连续确认
            if len(self.confirmation_queue) >= required_confirmations:
                return self.check_continuous_confirmation()

            return False, f"等待更多确认 ({len(self.confirmation_queue)}/{required_confirmations})"

        def check_continuous_confirmation(self):
            """增强的连续确认检查"""
            try:
                # 1. 所有信号都必须为买入信号
                all_buy_signals = all(entry['buy_signal'] for entry in self.confirmation_queue)
                if not all_buy_signals:
                    return False, "存在非买入信号"

                # 2. 信号强度必须稳定或递增（根据算法类型调整要求）
                strengths = [entry['signal_strength'] for entry in self.confirmation_queue]
                min_strength = min(strengths)

                # 根据算法类型调整信号强度要求
                latest_entry = self.confirmation_queue[-1]
                algorithm_type = latest_entry['algorithm_type']
                stock_state = latest_entry.get('stock_state', '普通状态')

                if algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
                    # V字反转：最低信号强度要求（机会稍纵即逝）
                    min_required_strength = 0.55
                elif stock_state == "横盘状态":
                    # 横盘突破：最低信号强度要求（突破确认重要）
                    min_required_strength = 0.55
                elif algorithm_type in ['trend_following', 'momentum_continuation']:
                    # 趋势跟随：降低信号强度要求
                    min_required_strength = 0.60
                else:
                    # 其他情况：标准信号强度要求
                    min_required_strength = 0.65

                if min_strength < min_required_strength:
                    return False, f"信号强度不足: 最低{min_strength:.2f} < {min_required_strength:.2f}"

                # 3. 算法置信度必须稳定（根据算法类型调整）
                confidences = [entry['algorithm_confidence'] for entry in self.confirmation_queue]
                min_confidence = min(confidences)

                if algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
                    # V字反转：保持高置信度要求
                    min_required_confidence = 0.85
                else:
                    # 其他情况：标准置信度要求
                    min_required_confidence = 0.8

                if min_confidence < min_required_confidence:
                    return False, f"算法置信度不足: 最低{min_confidence:.2f} < {min_required_confidence:.2f}"

                # 4. 涨幅必须保持上升趋势或稳定
                gains = [entry['current_gain'] for entry in self.confirmation_queue]
                if gains[-1] < gains[0] - 0.5:  # 允许小幅回调0.5%
                    return False, f"涨幅回调过大: {gains[0]:.2f}% → {gains[-1]:.2f}%"

                # 5. 量能必须保持健康
                volumes = [entry['volume_ratio'] for entry in self.confirmation_queue]
                if any(v < 1.5 for v in volumes):
                    return False, "量能不足"

                # ==================== 新增风险检查 ====================

                latest_entry = self.confirmation_queue[-1]
                current_gain = latest_entry['current_gain']
                current_volume = latest_entry['volume_ratio']
                current_time = latest_entry['timestamp']

                # 6. 高位追涨风险检查（涨幅>7%时更加谨慎）
                if current_gain > 7.0:
                    return False, f"高位追涨风险: 涨幅{current_gain:.2f}%过高，避免追涨"

                # 7. 涨停风险检查（涨幅>9.5%时立即停止）
                if current_gain >= 9.5:
                    return False, f"涨停风险: 涨幅{current_gain:.2f}%已达涨停，无法买入"

                # 8. 量价背离检测（价格上涨但量能萎缩）
                if len(self.confirmation_queue) >= 2:
                    prev_gain = self.confirmation_queue[-2]['current_gain']
                    prev_volume = self.confirmation_queue[-2]['volume_ratio']

                    # 价格上涨但量能下降超过20%
                    if current_gain > prev_gain and current_volume < prev_volume * 0.8:
                        return False, f"量价背离: 价格{prev_gain:.2f}%→{current_gain:.2f}%, 量比{prev_volume:.2f}→{current_volume:.2f}"

                # 9. 午后拉升风险检查（13:00-15:00时段更加谨慎）
                if current_time >= "13:00:00" and current_time <= "15:00:00":
                    # 午后时段要求更高的量能支撑
                    if current_volume < 2.5:
                        return False, f"午后拉升风险: 量比{current_volume:.2f}不足，可能是诱多"

                    # 午后涨幅超过6%时高度警惕
                    if current_gain > 6.0:
                        return False, f"午后高位风险: 涨幅{current_gain:.2f}%，午后拉升风险较高"

                # 10. 连续性验证（确保不是瞬间脉冲）
                if len(self.confirmation_queue) >= 2:
                    # 检查涨幅是否过于跳跃
                    gain_jump = abs(current_gain - self.confirmation_queue[-2]['current_gain'])
                    if gain_jump > 2.0:  # 单次涨幅跳跃超过2%
                        return False, f"涨幅跳跃过大: {gain_jump:.2f}%，可能是异常波动"

                # 11. 时间窗口检查（避免开盘前5分钟外的激进操作）
                if current_time > "09:35:00" and current_time < "13:00:00":
                    # 上午时段要求更稳定的信号
                    if current_gain > 5.0 and current_volume < 3.0:
                        return False, f"上午时段风险: 涨幅{current_gain:.2f}%但量比{current_volume:.2f}不足"

                # 连续确认成功
                return True, f"增强确认成功: 强度{latest_entry['signal_strength']:.2f}, 置信度{latest_entry['algorithm_confidence']:.2f}, 涨幅{current_gain:.2f}%"

            except Exception as e:
                return False, f"确认检查异常: {e}"

        def reset(self):
            """重置确认系统"""
            self.confirmation_queue.clear()

    # ==================== 专业算法切换系统 ====================

    class ProfessionalAlgorithmSwitcher:
        """专业算法切换系统 - 基于完整算法指南"""

        def __init__(self):
            self.current_algorithm = None
            self.algorithm_start_time = None
            self.time_in_range_tracker = {}  # 涨幅区间持续时间跟踪
            self.market_start_time = "09:30:00"

        def track_time_in_range(self, current_gain, timestamp):
            """跟踪在当前涨幅区间的持续时间"""
            # 定义涨幅区间
            if current_gain < 0:
                range_key = "negative"
            elif current_gain < 1:
                range_key = "flat"
            elif current_gain < 3:
                range_key = "low_gain"
            elif current_gain < 5:
                range_key = "mid_gain"
            elif current_gain < 7:
                range_key = "high_gain"
            else:
                range_key = "very_high"

            # 更新时间跟踪
            if range_key not in self.time_in_range_tracker:
                self.time_in_range_tracker[range_key] = {
                    'start_time': timestamp,
                    'duration_minutes': 0
                }
            else:
                # 计算持续时间（简化：每个数据点间隔0.25分钟）
                self.time_in_range_tracker[range_key]['duration_minutes'] += 0.25

            # 清除其他区间的记录
            for key in list(self.time_in_range_tracker.keys()):
                if key != range_key:
                    del self.time_in_range_tracker[key]

            return self.time_in_range_tracker[range_key]['duration_minutes']

        def get_market_duration_minutes(self, current_time):
            """获取开盘后的时间（分钟）"""
            try:
                from datetime import datetime
                market_start = datetime.strptime(self.market_start_time, "%H:%M:%S")
                current = datetime.strptime(current_time.split(':')[0] + ':' + current_time.split(':')[1] + ':00', "%H:%M:%S")
                duration = (current - market_start).total_seconds() / 60
                return max(0, duration)
            except:
                return 0

        def select_professional_algorithm(self, stock_data, price_tracker):
            """
            基于股票状态的专业算法选择

            核心改进：
            1. 基于价格轨迹记忆判断股票真实状态
            2. 根据状态选择最合适的算法
            3. 避免盲目的涨幅判断
            """
            try:
                current_gain = stock_data.get('change_pct', 0)
                timestamp = stock_data.get('timestamp', '09:30:00')
                volume_ratio = stock_data.get('volume_ratio', 0)

                # 获取股票状态
                stock_state, state_details = price_tracker.get_stock_state()
                market_duration = self.get_market_duration_minutes(timestamp)

                # 基于股票状态的算法选择

                # 1. 涨停股专门处理（涨幅≥9.5%）
                if current_gain >= 9.5:
                    algorithm_info = {
                        'algorithm_type': 'limit_up_sealed',
                        'algorithm_name': '涨停封板算法',
                        'confidence': 0.95,
                        'reason': f'涨停{current_gain:.2f}%，停止买入信号',
                        'stock_state': stock_state
                    }

                # 2. V字反转 - 最佳买入机会
                elif stock_state == "V字反转":
                    reversal_strength = state_details.get('reversal_strength', 0)
                    if reversal_strength >= 2.0:  # 反转幅度足够大
                        algorithm_info = {
                            'algorithm_type': 'v_reversal_momentum',
                            'algorithm_name': 'V字反转动量算法',
                            'confidence': 0.95,
                            'reason': f'V字反转确认，反转幅度{reversal_strength:.1f}%',
                            'stock_state': stock_state
                        }
                    else:
                        algorithm_info = {
                            'algorithm_type': 'v_reversal_early',
                            'algorithm_name': 'V字反转早期算法',
                            'confidence': 0.85,
                            'reason': f'V字反转初期，反转幅度{reversal_strength:.1f}%',
                            'stock_state': stock_state
                        }

                # 3. 横盘状态 - 等待突破
                elif stock_state == "横盘状态":
                    avg_gain = state_details.get('avg_gain', 0)
                    duration = state_details.get('duration_points', 0)

                    if avg_gain >= 5.0:
                        algorithm_info = {
                            'algorithm_type': 'high_level_breakout',
                            'algorithm_name': '高位横盘突破算法',
                            'confidence': 0.9,
                            'reason': f'高位{avg_gain:.1f}%横盘{duration}点，监控突破',
                            'stock_state': stock_state
                        }
                    elif avg_gain >= 3.0:
                        algorithm_info = {
                            'algorithm_type': 'mid_level_accumulation',
                            'algorithm_name': '中位横盘蓄势算法',
                            'confidence': 0.85,
                            'reason': f'中位{avg_gain:.1f}%横盘{duration}点，蓄势待发',
                            'stock_state': stock_state
                        }
                    else:
                        algorithm_info = {
                            'algorithm_type': 'low_level_consolidation',
                            'algorithm_name': '低位横盘整理算法',
                            'confidence': 0.8,
                            'reason': f'低位{avg_gain:.1f}%横盘{duration}点，整理中',
                            'stock_state': stock_state
                        }

                # 4. 持续上涨 - 趋势跟随
                elif stock_state == "持续上涨":
                    total_rise = state_details.get('total_rise', 0)
                    if current_gain >= 6.0:
                        algorithm_info = {
                            'algorithm_type': 'momentum_continuation',
                            'algorithm_name': '高位动量延续算法',
                            'confidence': 0.85,
                            'reason': f'持续上涨{total_rise:.1f}%，高位动量',
                            'stock_state': stock_state
                        }
                    else:
                        algorithm_info = {
                            'algorithm_type': 'trend_following',
                            'algorithm_name': '趋势跟随算法',
                            'confidence': 0.9,
                            'reason': f'持续上涨{total_rise:.1f}%，趋势良好',
                            'stock_state': stock_state
                        }

                # 5. 冲高回落 - 谨慎观望
                elif stock_state == "冲高回落":
                    pullback_amount = state_details.get('pullback_amount', 0)
                    high_point = state_details.get('high_point', 0)

                    if pullback_amount <= 1.5 and current_gain >= 3.0:
                        algorithm_info = {
                            'algorithm_type': 'pullback_recovery',
                            'algorithm_name': '回调修复算法',
                            'confidence': 0.7,
                            'reason': f'从{high_point:.1f}%小幅回调{pullback_amount:.1f}%，等待修复',
                            'stock_state': stock_state
                        }
                    else:
                        algorithm_info = {
                            'algorithm_type': 'pullback_observation',
                            'algorithm_name': '回调观望算法',
                            'confidence': 0.6,
                            'reason': f'从{high_point:.1f}%大幅回调{pullback_amount:.1f}%，观望为主',
                            'stock_state': stock_state
                        }

                # 6. 普通状态 - 传统判断
                else:
                    if current_gain >= 3.0:
                        algorithm_info = {
                            'algorithm_type': 'high_open_correction',
                            'algorithm_name': '高开修正算法',
                            'confidence': 0.8,
                            'reason': f'普通状态，涨幅{current_gain:.1f}%',
                            'stock_state': stock_state
                        }
                    elif current_gain >= 1.0:
                        algorithm_info = {
                            'algorithm_type': 'standard_high_open',
                            'algorithm_name': '标准高开算法',
                            'confidence': 0.8,
                            'reason': f'普通状态，涨幅{current_gain:.1f}%',
                            'stock_state': stock_state
                        }
                    elif current_gain >= -1.0:
                        algorithm_info = {
                            'algorithm_type': 'standard_flat_open',
                            'algorithm_name': '标准平开算法',
                            'confidence': 0.8,
                            'reason': f'普通状态，涨幅{current_gain:.1f}%',
                            'stock_state': stock_state
                        }
                    else:
                        algorithm_info = {
                            'algorithm_type': 'drop_reversal',
                            'algorithm_name': '下跌反转算法',
                            'confidence': 0.8,
                            'reason': f'普通状态，跌幅{abs(current_gain):.1f}%',
                            'stock_state': stock_state
                        }

                # 检查是否需要切换算法
                should_switch = self.should_switch_algorithm(algorithm_info, market_duration)

                if should_switch:
                    self.current_algorithm = algorithm_info
                    self.algorithm_start_time = timestamp
                    return algorithm_info, True  # 返回切换标志
                else:
                    return self.current_algorithm or algorithm_info, False

            except Exception as e:
                return {
                    'algorithm_type': 'error',
                    'algorithm_name': '算法错误',
                    'confidence': 0.0,
                    'reason': f'选择失败: {e}',
                    'stock_state': '错误'
                }, False

        def should_switch_algorithm(self, new_algorithm, market_duration):
            """判断是否应该切换算法"""
            # 如果没有当前算法，直接切换
            if not self.current_algorithm:
                return True

            # 如果算法类型相同，不切换
            if self.current_algorithm.get('algorithm_type') == new_algorithm.get('algorithm_type'):
                return False

            # 涨停股算法一旦确定，不再切换
            if self.current_algorithm.get('algorithm_type') == 'limit_up_sealed':
                return False

            # 开盘10分钟内，减少切换频率（除非是重大变化）
            if market_duration <= 10:
                current_confidence = self.current_algorithm.get('confidence', 0)
                new_confidence = new_algorithm.get('confidence', 0)
                # 只有置信度提升0.1以上才切换
                if new_confidence <= current_confidence + 0.1:
                    return False

            # 其他情况允许切换
            return True

    # ==================== 复刻主应用的算法逻辑 ====================

    # 旧的算法选择方法已被专业算法切换系统替代

    def analyze_algorithm_signals(self, code, stock_data, algorithm_info, price_tracker):
        """基于股票状态的算法信号分析"""
        try:
            algorithm_type = algorithm_info.get('algorithm_type', '')
            stock_state = algorithm_info.get('stock_state', '普通状态')
            current_gain = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])
            timestamp = stock_data.get('timestamp', '')

            # 基于股票状态的信号分析

            # 1. 涨停股：停止所有买入信号
            if algorithm_type == 'limit_up_sealed':
                return {
                    'buy_signal': False,
                    'signal_strength': 0.0,
                    'reason': f"涨停{current_gain:.1f}%，停止买入信号"
                }

            # 2. V字反转：最佳买入机会（修复信号强度计算）
            elif algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
                volume_strong = volume_ratio > 2.0
                bid_strong = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False
                reversal_confirmed = current_gain > 1.0

                # V字反转需要强量能确认，但降低门槛
                if algorithm_type == 'v_reversal_momentum':
                    # 动量确认阶段，修复信号强度计算
                    if volume_strong and bid_strong and reversal_confirmed:
                        buy_signal = True
                        signal_strength = 0.95
                        reason = f"V字反转动量(完美): 量比{volume_ratio:.1f}✓, 买盘强度✓, 反转确认✓"
                    elif volume_strong and reversal_confirmed:  # 量能和反转确认，买盘稍弱
                        buy_signal = True
                        signal_strength = 0.85
                        reason = f"V字反转动量(良好): 量比{volume_ratio:.1f}✓, 买盘强度{bid_strong}, 反转确认✓"
                    elif volume_ratio > 1.8 and reversal_confirmed:  # 进一步降低量比要求
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"V字反转动量(可接受): 量比{volume_ratio:.1f}(需>1.8), 反转确认✓"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"V字反转动量(不足): 量比{volume_ratio:.1f}(需>2.0), 买盘{bid_strong}, 反转{reversal_confirmed}"
                else:
                    # 早期阶段，更加宽松
                    if volume_strong and reversal_confirmed:
                        buy_signal = True
                        signal_strength = 0.85
                        reason = f"V字反转早期(优秀): 量比{volume_ratio:.1f}✓, 反转确认✓"
                    elif volume_ratio > 1.5 and reversal_confirmed:  # 进一步降低量比要求
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"V字反转早期(良好): 量比{volume_ratio:.1f}(需>1.5), 反转确认✓"
                    elif reversal_confirmed:  # 只要反转确认就给基础分
                        buy_signal = True
                        signal_strength = 0.65
                        reason = f"V字反转早期(基础): 量比{volume_ratio:.1f}不足, 但反转确认✓"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"V字反转早期(不足): 量比{volume_ratio:.1f}, 反转确认{reversal_confirmed}"

                return {
                    'buy_signal': buy_signal,
                    'signal_strength': signal_strength,
                    'reason': reason
                }

            # 3. 横盘突破：等待确认突破（修复信号强度计算）
            elif algorithm_type in ['high_level_breakout', 'mid_level_accumulation', 'low_level_consolidation']:
                # 横盘后的突破需要量能确认，修复信号强度计算
                price_breakout = current_gain > 0  # 价格突破横盘区间
                bid_strong = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False

                # 不同级别的横盘要求不同，修复信号强度计算
                if algorithm_type == 'high_level_breakout':
                    # 高位横盘突破
                    if volume_ratio > 2.5 and price_breakout and bid_strong:
                        buy_signal = True
                        signal_strength = 0.9
                        reason = f"高位横盘突破(完美): 量比{volume_ratio:.1f}✓, 突破✓, 买盘强✓"
                    elif volume_ratio > 2.0 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.8
                        reason = f"高位横盘突破(良好): 量比{volume_ratio:.1f}✓, 突破✓, 买盘{bid_strong}"
                    elif volume_ratio > 1.8 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"高位横盘突破(可接受): 量比{volume_ratio:.1f}(需>1.8), 突破✓"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"高位横盘突破(不足): 量比{volume_ratio:.1f}(需>2.0), 突破{price_breakout}"

                elif algorithm_type == 'mid_level_accumulation':
                    # 中位蓄势突破
                    if volume_ratio > 2.0 and price_breakout and bid_strong:
                        buy_signal = True
                        signal_strength = 0.85
                        reason = f"中位蓄势突破(完美): 量比{volume_ratio:.1f}✓, 突破✓, 买盘强✓"
                    elif volume_ratio > 1.8 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.8
                        reason = f"中位蓄势突破(良好): 量比{volume_ratio:.1f}✓, 突破✓"
                    elif volume_ratio > 1.5 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"中位蓄势突破(可接受): 量比{volume_ratio:.1f}(需>1.5), 突破✓"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"中位蓄势突破(不足): 量比{volume_ratio:.1f}(需>1.8), 突破{price_breakout}"

                else:
                    # 低位整理突破 - 按指南调整量比要求
                    if volume_ratio > 2.5 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.8
                        reason = f"低位整理突破(优秀): 量比{volume_ratio:.1f}✓, 突破✓"
                    elif volume_ratio > 2.0 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"低位整理突破(良好): 量比{volume_ratio:.1f}✓, 突破✓"
                    elif volume_ratio > 1.5 and price_breakout:
                        buy_signal = True
                        signal_strength = 0.7
                        reason = f"低位整理突破(可接受): 量比{volume_ratio:.1f}(需>1.5), 突破✓"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"低位整理突破(不足): 量比{volume_ratio:.1f}(需>2.0), 突破{price_breakout}"

                return {
                    'buy_signal': buy_signal,
                    'signal_strength': signal_strength,
                    'reason': reason
                }

            # 4. 趋势跟随：谨慎跟随（修复信号强度计算）
            elif algorithm_type in ['trend_following', 'momentum_continuation']:
                bid_ok = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False

                # 高位动量要更谨慎
                if algorithm_type == 'momentum_continuation':
                    # 高位动量，避免追涨
                    if current_gain > 6.0:
                        return {
                            'buy_signal': False,
                            'signal_strength': 0.0,
                            'reason': f"高位动量但涨幅{current_gain:.1f}%过高，避免追涨"
                        }

                    if volume_ratio > 3.0 and bid_ok:
                        buy_signal = True
                        signal_strength = 0.8
                        reason = f"高位动量延续(优秀): 量比{volume_ratio:.1f}✓, 买盘强✓"
                    elif volume_ratio > 2.5 and bid_ok:
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"高位动量延续(良好): 量比{volume_ratio:.1f}✓, 买盘强✓"
                    elif volume_ratio > 2.0:
                        buy_signal = True
                        signal_strength = 0.7
                        reason = f"高位动量延续(可接受): 量比{volume_ratio:.1f}✓, 买盘{bid_ok}"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"高位动量延续(不足): 量比{volume_ratio:.1f}(需>2.5), 买盘{bid_ok}"
                else:
                    # 趋势跟随 - 按指南调整量比要求
                    if volume_ratio > 3.5 and bid_ok:
                        buy_signal = True
                        signal_strength = 0.85
                        reason = f"趋势跟随(完美): 量比{volume_ratio:.1f}✓, 买盘强✓"
                    elif volume_ratio > 3.0 and bid_ok:
                        buy_signal = True
                        signal_strength = 0.8
                        reason = f"趋势跟随(优秀): 量比{volume_ratio:.1f}✓, 买盘强✓"
                    elif volume_ratio > 2.5:
                        buy_signal = True
                        signal_strength = 0.75
                        reason = f"趋势跟随(良好): 量比{volume_ratio:.1f}✓, 买盘{bid_ok}"
                    else:
                        buy_signal = False
                        signal_strength = 0.0
                        reason = f"趋势跟随(不足): 量比{volume_ratio:.1f}(需>3.0), 买盘{bid_ok}"

                return {
                    'buy_signal': buy_signal,
                    'signal_strength': signal_strength,
                    'reason': reason
                }

            # 5. 回调修复：非常谨慎
            elif algorithm_type in ['pullback_recovery', 'pullback_observation']:
                if algorithm_type == 'pullback_observation':
                    # 观望为主，不发买入信号
                    return {
                        'buy_signal': False,
                        'signal_strength': 0.0,
                        'reason': f"冲高回落观望，不发买入信号"
                    }
                else:
                    # 回调修复需要非常强的确认
                    volume_strong = volume_ratio > 3.0
                    bid_very_strong = sum(bid_volumes) > sum(ask_volumes) * 2.5 if bid_volumes and ask_volumes else False

                    buy_signal = volume_strong and bid_very_strong
                    signal_strength = 0.7 if buy_signal else 0.0
                    reason = f"回调修复: 量比{volume_ratio:.1f}(需>3.0), 强买盘{bid_very_strong}"

                    return {
                        'buy_signal': buy_signal,
                        'signal_strength': signal_strength,
                        'reason': reason
                    }

            # 6. 传统算法：按照指南要求调整数值
            else:
                # 午后风险检查
                if timestamp >= "13:00:00" and current_gain > 5.0 and volume_ratio < 3.0:
                    return {
                        'buy_signal': False,
                        'signal_strength': 0.0,
                        'reason': f"午后风险: 涨幅{current_gain:.1f}%但量比{volume_ratio:.1f}不足"
                    }

                # 高位风险检查
                if current_gain > 7.0:
                    return {
                        'buy_signal': False,
                        'signal_strength': 0.0,
                        'reason': f"高位风险: 涨幅{current_gain:.1f}%过高"
                    }

                # 按照指南调整的判断逻辑
                if algorithm_type in ['standard_flat_open', 'enhanced_flat_open']:
                    # 平开算法：按指南要求量比>3.0，突击时>5.0
                    if volume_ratio > 5.0:  # 核爆级别
                        volume_ok = True
                        signal_strength_base = 0.9
                        reason_vol = f"核爆量比{volume_ratio:.1f}"
                    elif volume_ratio > 3.0:  # 基础要求
                        volume_ok = True
                        signal_strength_base = 0.8
                        reason_vol = f"强势量比{volume_ratio:.1f}"
                    else:
                        volume_ok = False
                        signal_strength_base = 0.0
                        reason_vol = f"量比{volume_ratio:.1f}不足(需>3.0)"
                elif algorithm_type in ['standard_high_open', 'high_open_correction']:
                    # 高开算法：按指南要求量比>3.5持续
                    if volume_ratio > 3.5:
                        volume_ok = True
                        signal_strength_base = 0.85
                        reason_vol = f"高开量比{volume_ratio:.1f}"
                    else:
                        volume_ok = False
                        signal_strength_base = 0.0
                        reason_vol = f"量比{volume_ratio:.1f}不足(需>3.5)"
                else:
                    # 其他算法保持原有逻辑
                    volume_ok = volume_ratio > 2.0
                    signal_strength_base = 0.75 if volume_ok else 0.0
                    reason_vol = f"量比{volume_ratio:.1f}"

                # 买盘厚度：按指南要求买一突增>300%
                bid_strong = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False
                price_ok = current_gain > 1.0

                buy_signal = volume_ok and bid_strong and price_ok
                signal_strength = signal_strength_base if buy_signal else 0.0

                return {
                    'buy_signal': buy_signal,
                    'signal_strength': signal_strength,
                    'reason': f"指南标准: {reason_vol}, 买盘{bid_strong}, 涨幅{current_gain:.1f}%"
                }

        except Exception as e:
            return {
                'buy_signal': False,
                'signal_strength': 0.0,
                'reason': f'分析失败: {e}'
            }

    def judge_intelligent_buy_signal(self, core_signals, algorithm_info, total_strength):
        """智能买入信号判断（复刻主应用逻辑）"""
        try:
            # 核心原则：完全信任算法内部的买入判断
            algorithm_buy_signal = core_signals.get('buy_signal', False)

            # 如果算法内部判断不买入，直接返回False
            if not algorithm_buy_signal:
                return False, "算法内部判断不买入"

            # 算法内部判断买入时，进行安全检查

            # 1. 算法置信度检查（必须≥0.8）
            algorithm_confidence = algorithm_info.get('confidence', 0.0)
            if algorithm_confidence < 0.8:
                return False, f"算法置信度不足: {algorithm_confidence:.2f} < 0.8"

            # 2. 最终信号强度检查（必须≥0.7）
            if total_strength < 0.7:
                return False, f"总信号强度不足: {total_strength:.2f} < 0.7"

            return True, f"买入信号确认: 置信度={algorithm_confidence:.2f}, 强度={total_strength:.2f}"

        except Exception as e:
            return False, f"判断异常: {e}"

    def run_high_frequency_test(self, scenario_name: str, scenario_data: List[Dict[str, Any]]):
        """运行高频测试"""
        print(f"\n🎬 高频测试场景: {scenario_name}")
        print("=" * 100)

        test_code = 'TEST001'
        self.realtime_data_series[test_code] = []
        self.notifications_captured.clear()

        # 重置专业系统
        self.confirmation_system.reset()
        self.algorithm_switcher = self.ProfessionalAlgorithmSwitcher()
        self.price_tracker = self.PriceTrajectoryTracker(max_history=30)

        results = {
            'scenario': scenario_name,
            'total_points': len(scenario_data),
            'buy_signals': 0,
            'correct_signals': 0,
            'false_signals': 0,
            'notifications': [],
            'algorithm_switches': 0
        }

        for i, data_point in enumerate(scenario_data):
            # 添加到实时数据序列
            self.realtime_data_series[test_code].append(data_point)

            # 添加到价格轨迹跟踪器
            self.price_tracker.add_data_point(
                data_point['timestamp'],
                data_point['current_price'],
                data_point['change_pct'],
                data_point['volume_ratio']
            )

            print(f"\n⏰ {data_point['timestamp']} - {data_point['scenario_desc']}")
            print(f"   💰 价格: {data_point['current_price']:.2f}元 | 涨幅: {data_point['change_pct']:.2f}% | 量比: {data_point['volume_ratio']:.2f}")

            # 显示盘口数据（简化）
            bid_volumes = data_point.get('bid_volumes', [])
            ask_volumes = data_point.get('ask_volumes', [])
            print(f"   📊 买盘: {bid_volumes[0]:,} | 卖盘: {ask_volumes[0]:,} | 买卖比: {bid_volumes[0]/(ask_volumes[0]+1):.1f}")

            # 显示股票状态
            stock_state_desc = self.price_tracker.get_state_description()
            print(f"   📈 股票状态: {stock_state_desc}")

            # 基于股票状态的专业算法选择
            algorithm_info, algorithm_switched = self.algorithm_switcher.select_professional_algorithm(data_point, self.price_tracker)

            # 记录算法切换
            if algorithm_switched:
                results['algorithm_switches'] += 1
                print(f"   🔄 算法切换: {algorithm_info.get('algorithm_name')} (原因: {algorithm_info.get('reason')})")

            print(f"   🧮 当前算法: {algorithm_info.get('algorithm_name')} (置信度: {algorithm_info.get('confidence', 0):.2f})")
            print(f"   📝 算法原因: {algorithm_info.get('reason', '')}")

            # 基于股票状态的算法分析
            core_signals = self.analyze_algorithm_signals(test_code, data_point, algorithm_info, self.price_tracker)

            # 计算总信号强度
            total_strength = core_signals.get('signal_strength', 0) * algorithm_info.get('confidence', 0)

            # 准备信号数据用于连续确认
            signal_data_for_confirmation = {
                'buy_signal': core_signals.get('buy_signal', False),
                'signal_strength': total_strength,
                'current_gain': data_point.get('change_pct', 0),
                'volume_ratio': data_point.get('volume_ratio', 0),
                'reason': core_signals.get('reason', '')
            }

            # 连续确认机制
            continuous_confirmed, confirmation_reason = self.confirmation_system.add_signal(
                data_point['timestamp'], signal_data_for_confirmation, algorithm_info
            )

            print(f"   🎯 算法分析: {core_signals.get('reason', '')}")
            print(f"   📈 信号强度: {total_strength:.2f}")
            print(f"   🔍 连续确认: {confirmation_reason}")

            # 最终买入判断：只有连续确认通过才发送通知
            if continuous_confirmed:
                buy_signal = True
                reason = f"连续确认成功: {confirmation_reason}"
                print(f"   💡 买入判断: ✅ 发送买入通知 - {reason}")
            else:
                buy_signal = False
                reason = f"连续确认未通过: {confirmation_reason}"
                print(f"   💡 买入判断: ❌ 不发送通知 - {reason}")

            # 检查信号正确性
            expected_signal = data_point.get('expected_signal', False)

            if buy_signal:
                results['buy_signals'] += 1

                # 发送通知
                notification = {
                    'time': data_point['timestamp'],
                    'price': data_point['current_price'],
                    'algorithm': algorithm_info.get('algorithm_name', ''),
                    'strength': total_strength,
                    'reason': reason,
                    'confirmation_type': '连续确认通过'
                }
                results['notifications'].append(notification)
                self.notifications_captured.append(notification)

                print(f"   🔔 发送买入通知! 价格: {data_point['current_price']:.2f}元")

                # 判断信号正确性
                if expected_signal:
                    results['correct_signals'] += 1
                    print(f"   ✅ 正确信号")
                else:
                    results['false_signals'] += 1
                    print(f"   ❌ 错误信号 (不应该买入)")
            else:
                if expected_signal:
                    print(f"   ⚠️ 漏掉信号 (应该买入但未发信号)")

            print("-" * 80)
            time.sleep(0.3)  # 暂停观察

        # 计算准确率
        if results['buy_signals'] > 0:
            accuracy = results['correct_signals'] / results['buy_signals'] * 100
        else:
            accuracy = 100 if results['total_points'] == sum(1 for d in scenario_data if not d.get('expected_signal', False)) else 0

        results['accuracy'] = accuracy

        print(f"\n📊 场景 '{scenario_name}' 测试结果:")
        print(f"   • 数据点数: {results['total_points']}")
        print(f"   • 发出信号: {results['buy_signals']} 次")
        print(f"   • 正确信号: {results['correct_signals']} 次")
        print(f"   • 错误信号: {results['false_signals']} 次")
        print(f"   • 算法切换: {results['algorithm_switches']} 次")
        print(f"   • 准确率: {accuracy:.1f}%")

        return results

    def run_all_high_frequency_tests(self):
        """运行所有高频测试"""
        print("🚀 开始高频算法测试系统")
        print("🎯 目标: 验证算法在高频数据下的实时表现")
        print("📊 特点: 15秒/30秒高频数据，实时算法切换")
        print("=" * 100)

        # 生成所有场景数据
        scenarios = {
            "场景1：开盘5分钟快速涨停": self.generate_gaming_scenario_1(),
            "场景2：温和上涨后突然发力": self.generate_gaming_scenario_2(),
            "场景3：长时间横盘后爆发": self.generate_gaming_scenario_3(),
            "场景4：午后拉升后回落": self.generate_gaming_scenario_4()
        }

        all_results = []
        total_signals = 0
        total_correct = 0
        total_false = 0
        total_switches = 0

        for scenario_name, scenario_data in scenarios.items():
            result = self.run_high_frequency_test(scenario_name, scenario_data)
            all_results.append(result)

            total_signals += result['buy_signals']
            total_correct += result['correct_signals']
            total_false += result['false_signals']
            total_switches += result['algorithm_switches']

            print(f"\n{'='*50} 场景间隔 {'='*50}")
            time.sleep(1)

        # 生成总体报告
        self.generate_high_frequency_report(all_results, total_signals, total_correct, total_false, total_switches)

    def generate_high_frequency_report(self, all_results, total_signals, total_correct, total_false, total_switches):
        """生成高频测试报告"""
        print(f"\n🎉 高频算法测试报告")
        print("=" * 100)

        overall_accuracy = (total_correct / total_signals * 100) if total_signals > 0 else 100

        print(f"📈 总体表现:")
        print(f"   • 测试场景: {len(all_results)} 个")
        print(f"   • 总发信号: {total_signals} 次")
        print(f"   • 正确信号: {total_correct} 次")
        print(f"   • 错误信号: {total_false} 次")
        print(f"   • 算法切换: {total_switches} 次")
        print(f"   • 总体准确率: {overall_accuracy:.1f}%")

        print(f"\n📋 各场景详情:")
        for result in all_results:
            scenario = result['scenario']
            accuracy = result['accuracy']
            buy_signals = result['buy_signals']
            switches = result['algorithm_switches']

            status = "✅" if accuracy >= 80 else "⚠️" if accuracy >= 60 else "❌"
            print(f"   {status} {scenario}")
            print(f"      准确率: {accuracy:.1f}% ({result['correct_signals']}/{buy_signals})")
            print(f"      算法切换: {switches} 次")

        print(f"\n🎯 算法切换分析:")
        if total_switches > 0:
            avg_switches = total_switches / len(all_results)
            print(f"   • 平均每场景切换: {avg_switches:.1f} 次")
            print(f"   • 算法切换体现了实时性和适应性")
        else:
            print(f"   • 无算法切换，可能算法选择过于稳定")

        print(f"\n💡 实战建议:")
        if overall_accuracy >= 90:
            print(f"   🚀 算法表现优秀，可以放心在实盘使用")
        elif overall_accuracy >= 80:
            print(f"   📈 算法表现良好，可以小仓位试用")
        elif overall_accuracy >= 70:
            print(f"   ⚠️ 算法需要优化，建议模拟盘测试")
        else:
            print(f"   ❌ 算法存在问题，需要重新调整")

if __name__ == "__main__":
    tester = HighFrequencyTester()
    tester.run_all_high_frequency_tests()
