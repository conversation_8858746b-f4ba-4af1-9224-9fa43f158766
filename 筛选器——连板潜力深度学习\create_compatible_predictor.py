#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建兼容的预测器
"""

import joblib
import numpy as np
import json
import os

def create_feature_mapping():
    """创建特征映射"""
    print("🔧 创建特征映射...")
    
    # 从训练结果获取28个特征
    results_dir = "ui_integration/training_results"
    result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
    latest_file = sorted(result_files)[-1]
    result_path = os.path.join(results_dir, latest_file)
    
    with open(result_path, 'r', encoding='utf-8') as f:
        results = json.load(f)
    
    # 获取训练时的特征名称
    training_features = results.get('data_preparation', {}).get('feature_names', [])
    
    # 模型期望的10个特征（基于常见的重要特征）
    model_expected_features = [
        'close_price',
        'price_change_5d', 
        'volume_ratio',
        'main_net_inflow',
        'turnover_rate',
        'price_vs_ma5',
        'amplitude',
        'rsi',
        'is_shenzhen',
        'technical_signal_count'
    ]
    
    print(f"📊 特征映射:")
    print(f"  训练特征数量: {len(training_features)}")
    print(f"  模型期望特征数量: {len(model_expected_features)}")
    
    # 创建映射关系
    feature_mapping = {}
    for i, expected_feature in enumerate(model_expected_features):
        if expected_feature in training_features:
            training_index = training_features.index(expected_feature)
            feature_mapping[i] = training_index
            print(f"  {i}: {expected_feature} -> 训练特征[{training_index}]")
        else:
            feature_mapping[i] = None
            print(f"  {i}: {expected_feature} -> 缺失，使用默认值")
    
    return feature_mapping, model_expected_features, training_features

def create_compatible_feature_names():
    """创建兼容的特征名称文件"""
    print("\n🔧 创建兼容的特征名称文件...")
    
    try:
        feature_mapping, model_features, training_features = create_feature_mapping()
        
        # 保存兼容的特征名称（10个特征）
        compatible_features = model_features
        
        # 备份当前文件
        current_file = 'trained_models/feature_names.joblib'
        backup_file = 'trained_models/feature_names_28.joblib'
        
        if os.path.exists(current_file):
            import shutil
            shutil.copy2(current_file, backup_file)
            print(f"✅ 备份28特征文件: {backup_file}")
        
        # 保存10个特征的文件
        joblib.dump(compatible_features, current_file)
        print(f"✅ 保存兼容特征文件: {current_file}")
        
        # 保存特征映射
        mapping_file = 'trained_models/feature_mapping.joblib'
        joblib.dump(feature_mapping, mapping_file)
        print(f"✅ 保存特征映射: {mapping_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 创建兼容特征文件失败: {e}")
        return False

def test_compatible_predictor():
    """测试兼容的预测器"""
    print("\n🧪 测试兼容的预测器...")
    
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from training.model_predictor import ModelPredictor
        
        # 重新创建预测器
        predictor = ModelPredictor()
        
        print(f"📊 预测器状态:")
        print(f"  模型数量: {len(predictor.models)}")
        print(f"  特征数量: {len(predictor.feature_columns)}")
        
        if len(predictor.models) > 0 and len(predictor.feature_columns) == 10:
            # 创建测试特征（只包含10个特征）
            test_features = {
                'close_price': 15.5,
                'price_change_5d': 0.05,
                'volume_ratio': 1.2,
                'main_net_inflow': 1000000,
                'turnover_rate': 3.5,
                'price_vs_ma5': 1.02,
                'amplitude': 5.2,
                'rsi': 65.0,
                'is_shenzhen': 1,
                'technical_signal_count': 3
            }
            
            print(f"📊 测试特征: {list(test_features.keys())}")
            
            # 进行预测
            result = predictor.predict_single_stock(test_features)
            
            print(f"📈 预测结果:")
            print(f"  成功: {result.get('success', False)}")
            
            if result.get('success', False):
                print(f"  预测分数: {result.get('prediction_score', 0):.3f}")
                print(f"  预测标签: {result.get('prediction_label', 0)}")
                print("✅ 兼容预测器工作正常！")
                return True
            else:
                print(f"  错误: {result.get('error', 'Unknown')}")
                print("❌ 预测仍有问题")
                return False
        else:
            print("❌ 预测器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 创建兼容的预测器...")
    print("="*60)
    
    # 创建兼容的特征文件
    create_success = create_compatible_feature_names()
    
    if not create_success:
        print("❌ 创建兼容文件失败")
        return
    
    # 测试兼容的预测器
    test_success = test_compatible_predictor()
    
    print("\n" + "="*60)
    print("📋 创建结论:")
    
    if test_success:
        print("✅ 兼容预测器创建成功")
        print("🎉 模型预测功能恢复正常")
        print("💡 现在可以重新运行潜在股调查")
        print("\n📝 注意：这是临时解决方案，建议后续重新训练完整模型")
    else:
        print("❌ 兼容预测器仍有问题")
        print("💡 建议重新训练模型以彻底解决问题")
    
    print("\n🎉 创建完成！")

if __name__ == "__main__":
    main()
