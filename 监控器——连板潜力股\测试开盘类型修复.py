#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试开盘类型修复效果
"""

import sys
import os
from datetime import datetime

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def test_historical_data_supplement():
    """测试历史数据补充"""
    print("🔍 测试历史数据补充")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        from enhanced_data_fetcher import EnhancedStockDataFetcher
        
        monitor = LimitUpPotentialMonitor()
        fetcher = EnhancedStockDataFetcher()
        
        print("✅ 系统导入成功")
        
        # 选择一只监控股票进行测试
        if monitor.monitored_stocks:
            test_code = list(monitor.monitored_stocks.keys())[0]
            stock_name = monitor.monitored_stocks[test_code]['name']
            
            print(f"📊 测试股票: {test_code} {stock_name}")
            
            # 1. 获取批量数据（模拟监控循环中的逻辑）
            batch_df = fetcher.get_realtime_quote([test_code])
            
            if not batch_df.empty:
                # 转换为字典格式
                stock_data = batch_df.iloc[0].to_dict()
                
                print(f"\n📊 批量获取的原始数据:")
                print(f"   current_price: {stock_data.get('current_price', '未找到')}")
                print(f"   open: {stock_data.get('open', '未找到')}")
                print(f"   prev_close: {stock_data.get('prev_close', '未找到')}")
                print(f"   change_pct: {stock_data.get('change_pct', '未找到')}")
                
                # 2. 补充历史数据（修复后的逻辑）
                if test_code in monitor.historical_base_data:
                    historical_data = monitor.historical_base_data[test_code]
                    stock_data['prev_close'] = historical_data.get('prev_close', 0)
                    stock_data['prev_high'] = historical_data.get('prev_high', 0)
                    stock_data['prev_low'] = historical_data.get('prev_low', 0)
                    stock_data['prev_open'] = historical_data.get('prev_open', 0)
                    
                    print(f"\n📊 补充历史数据后:")
                    print(f"   prev_close: {stock_data.get('prev_close')}")
                    print(f"   prev_high: {stock_data.get('prev_high')}")
                    print(f"   prev_low: {stock_data.get('prev_low')}")
                    print(f"   prev_open: {stock_data.get('prev_open')}")
                else:
                    print(f"\n❌ {test_code} 历史数据不存在")
                
                # 3. 测试开盘类型分类
                result = monitor.classify_open_type(stock_data)
                print(f"\n🔧 开盘类型分类结果:")
                print(f"   type: {result.get('type')}")
                print(f"   type_name: {result.get('type_name')}")
                print(f"   open_change_pct: {result.get('open_change_pct'):.2f}%")
                print(f"   current_change_pct: {result.get('current_change_pct'):.2f}%")
                print(f"   description: {result.get('description')}")
                
                # 4. 验证修复效果
                if result.get('type_name') == '未知':
                    print(f"\n❌ 修复失败：仍然显示'未知'")
                    
                    # 诊断问题
                    current_price = stock_data.get('current_price', 0)
                    open_price = stock_data.get('open', 0)
                    prev_close = stock_data.get('prev_close', 0)
                    
                    print(f"🔍 问题诊断:")
                    print(f"   current_price: {current_price} (>0: {current_price > 0})")
                    print(f"   open_price: {open_price} (>0: {open_price > 0})")
                    print(f"   prev_close: {prev_close} (>0: {prev_close > 0})")
                    
                    if prev_close <= 0:
                        print(f"   🎯 问题原因: prev_close <= 0")
                    elif open_price <= 0:
                        print(f"   🎯 问题原因: open_price <= 0")
                    else:
                        print(f"   🎯 问题原因: 其他未知原因")
                else:
                    print(f"\n✅ 修复成功：显示'{result.get('type_name')}'")
                
            else:
                print(f"❌ 未获取到批量数据")
        else:
            print(f"❌ 没有监控股票")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

def test_multiple_stocks():
    """测试多只股票的开盘类型"""
    print("\n🔍 测试多只股票的开盘类型")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        from enhanced_data_fetcher import EnhancedStockDataFetcher
        
        monitor = LimitUpPotentialMonitor()
        fetcher = EnhancedStockDataFetcher()
        
        # 获取前5只监控股票
        test_codes = list(monitor.monitored_stocks.keys())[:5]
        
        if test_codes:
            print(f"📊 测试股票: {len(test_codes)} 只")
            
            # 批量获取数据
            batch_df = fetcher.get_realtime_quote(test_codes)
            
            if not batch_df.empty:
                print(f"✅ 批量获取成功，共 {len(batch_df)} 只股票")
                
                for code in test_codes:
                    stock_name = monitor.monitored_stocks[code]['name']
                    
                    # 获取该股票的数据
                    stock_rows = batch_df[batch_df['stock_code'] == code]
                    if stock_rows.empty:
                        print(f"⚠️ {code} {stock_name}: 数据缺失")
                        continue
                    
                    stock_data = stock_rows.iloc[0].to_dict()
                    
                    # 补充历史数据
                    if code in monitor.historical_base_data:
                        historical_data = monitor.historical_base_data[code]
                        stock_data['prev_close'] = historical_data.get('prev_close', 0)
                        stock_data['prev_high'] = historical_data.get('prev_high', 0)
                        stock_data['prev_low'] = historical_data.get('prev_low', 0)
                        stock_data['prev_open'] = historical_data.get('prev_open', 0)
                    
                    # 分类开盘类型
                    result = monitor.classify_open_type(stock_data)
                    type_name = result.get('type_name', '未知')
                    open_change = result.get('open_change_pct', 0)
                    current_change = result.get('current_change_pct', 0)
                    
                    # 显示结果
                    status = "✅" if type_name != '未知' else "❌"
                    print(f"   {status} {code} {stock_name}: {type_name} (开盘{open_change:+.2f}%, 当前{current_change:+.2f}%)")
                
            else:
                print(f"❌ 批量获取失败")
        else:
            print(f"❌ 没有监控股票")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_edge_cases():
    """测试边界情况"""
    print("\n🔍 测试边界情况")
    print("-" * 40)
    
    try:
        from 连板潜力股实时监控 import LimitUpPotentialMonitor
        
        monitor = LimitUpPotentialMonitor()
        
        # 测试各种边界情况
        test_cases = [
            {
                'name': '正常高开',
                'data': {
                    'current_price': 10.5,
                    'open': 10.3,
                    'prev_close': 10.0,
                    'change_pct': 5.0
                },
                'expected': '高开'
            },
            {
                'name': '边界高开',
                'data': {
                    'current_price': 10.2,
                    'open': 10.2,
                    'prev_close': 10.0,
                    'change_pct': 2.0
                },
                'expected': '高开'
            },
            {
                'name': '边界平开',
                'data': {
                    'current_price': 10.19,
                    'open': 10.19,
                    'prev_close': 10.0,
                    'change_pct': 1.9
                },
                'expected': '平开'
            },
            {
                'name': '边界低开',
                'data': {
                    'current_price': 9.8,
                    'open': 9.8,
                    'prev_close': 10.0,
                    'change_pct': -2.0
                },
                'expected': '低开'
            },
            {
                'name': '缺少开盘价',
                'data': {
                    'current_price': 10.1,
                    'open': 0,  # 缺少开盘价
                    'prev_close': 10.0,
                    'change_pct': 1.0
                },
                'expected': '平开'  # 应该使用当前价格计算
            },
            {
                'name': '缺少前收盘价',
                'data': {
                    'current_price': 10.1,
                    'open': 10.05,
                    'prev_close': 0,  # 缺少前收盘价
                    'change_pct': 1.0
                },
                'expected': '未知'  # 应该返回未知
            }
        ]
        
        print("📊 边界情况测试:")
        for case in test_cases:
            result = monitor.classify_open_type(case['data'])
            type_name = result.get('type_name', '未知')
            
            status = "✅" if type_name == case['expected'] else "❌"
            print(f"   {status} {case['name']}: {type_name} (预期: {case['expected']})")
        
        print("✅ 边界情况测试完成")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def main():
    """主测试函数"""
    print("🚀 开始开盘类型修复测试")
    print("=" * 50)
    
    # 运行所有测试
    test_historical_data_supplement()
    test_multiple_stocks()
    test_edge_cases()
    
    print("\n📋 修复总结:")
    print("=" * 50)
    print("1. ✅ 问题定位: 批量数据缺少历史数据补充")
    print("2. ✅ 修复方案: 在监控循环中添加历史数据补充逻辑")
    print("3. ✅ 修复位置: monitor_loop() 第7698-7703行")
    print("4. ✅ 修复内容: 从historical_base_data补充prev_close等字段")
    
    print("\n🎯 预期效果:")
    print("- 开盘类型不再显示'未知'")
    print("- 正确显示'高开'、'平开'、'低开'")
    print("- 基于开盘涨跌幅的准确分类")
    
    print("\n🏁 测试完成")
    print("=" * 50)

if __name__ == "__main__":
    main()
