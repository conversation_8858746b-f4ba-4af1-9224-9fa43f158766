#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开盘特征增强分析模块
深度分析集合竞价表现、开盘异动识别、早盘试盘行为等
这是识别游资开盘操作的关键数据模块
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpeningFeaturesAnalyzer:
    """开盘特征增强分析器"""
    
    def __init__(self):
        self.analysis_days = 30  # 分析天数
        self.large_order_threshold = 200000  # 大单阈值（20万元）
        self.abnormal_volume_ratio = 2.0  # 异常成交量倍数
        self.price_change_threshold = 0.03  # 价格异动阈值（3%）
        
    def get_opening_features_analysis(self, stock_code: str) -> Dict:
        """
        获取完整的开盘特征分析
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 开盘特征分析结果
        """
        try:
            logger.info(f"开始分析{stock_code}的开盘特征")
            
            # 1. 获取历史数据
            historical_data = self._get_historical_data(stock_code)
            if historical_data.empty:
                return {'data_available': False, 'error': '无法获取历史数据'}
            
            # 2. 获取最新的分时数据（用于开盘分析）
            latest_minute_data = self._get_latest_minute_data(stock_code)
            
            # 3. 分析集合竞价表现
            auction_analysis = self._analyze_auction_performance(historical_data, latest_minute_data)
            
            # 4. 识别开盘异动
            opening_anomaly = self._identify_opening_anomaly(historical_data, latest_minute_data)
            
            # 5. 分析早盘试盘行为
            trial_analysis = self._analyze_early_trial_behavior(latest_minute_data, historical_data)
            
            # 6. 追踪开盘大单
            large_order_tracking = self._track_opening_large_orders(latest_minute_data)
            
            # 7. 生成开盘特征综合评估
            comprehensive_assessment = self._generate_comprehensive_assessment(
                auction_analysis, opening_anomaly, trial_analysis, large_order_tracking
            )
            
            return {
                'data_available': True,
                'stock_code': stock_code,
                'analysis_date': datetime.now().strftime('%Y-%m-%d'),
                'auction_analysis': auction_analysis,
                'opening_anomaly': opening_anomaly,
                'trial_analysis': trial_analysis,
                'large_order_tracking': large_order_tracking,
                'comprehensive_assessment': comprehensive_assessment,
                'data_quality': {
                    'historical_days': len(historical_data),
                    'minute_data_available': not latest_minute_data.empty,
                    'analysis_completeness': '完整' if not latest_minute_data.empty else '部分'
                }
            }
            
        except Exception as e:
            logger.error(f"开盘特征分析失败: {e}")
            return {'data_available': False, 'error': str(e)}
    
    def _get_historical_data(self, stock_code: str) -> pd.DataFrame:
        """获取历史K线数据"""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.analysis_days + 10)
            
            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')
            
            logger.info(f"获取{stock_code}历史数据: {start_date_str} - {end_date_str}")
            
            df = ak.stock_zh_a_hist(
                symbol=stock_code,
                start_date=start_date_str,
                end_date=end_date_str,
                adjust="qfq"
            )
            
            if df.empty:
                return pd.DataFrame()
            
            # 标准化列名
            if '收盘' in df.columns:
                df['close'] = df['收盘']
            if '开盘' in df.columns:
                df['open'] = df['开盘']
            if '最高' in df.columns:
                df['high'] = df['最高']
            if '最低' in df.columns:
                df['low'] = df['最低']
            if '成交量' in df.columns:
                df['volume'] = df['成交量']
            if '成交额' in df.columns:
                df['amount'] = df['成交额']
            if '日期' in df.columns:
                df['date'] = pd.to_datetime(df['日期'])
            
            # 确保数据类型
            for col in ['open', 'close', 'high', 'low']:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
            
            df = df.sort_values('date').reset_index(drop=True)
            
            if len(df) > self.analysis_days:
                df = df.tail(self.analysis_days)
            
            logger.info(f"获取到{len(df)}天的历史数据")
            return df
            
        except Exception as e:
            logger.error(f"获取历史数据失败: {e}")
            return pd.DataFrame()
    
    def _get_latest_minute_data(self, stock_code: str) -> pd.DataFrame:
        """获取最新的分时数据"""
        try:
            logger.info(f"获取{stock_code}的最新分时数据")
            
            # 尝试获取今天的分时数据
            today = datetime.now().strftime('%Y%m%d')
            
            try:
                minute_data = ak.stock_zh_a_hist_min_em(
                    symbol=stock_code,
                    start_date=today,
                    end_date=today,
                    period='1',
                    adjust=''
                )
                
                if not minute_data.empty:
                    # 标准化列名 - 处理不同的列名格式
                    column_mapping = {
                        '时间': 'time',
                        '开盘': 'open',
                        '收盘': 'close',
                        '最高': 'high',
                        '最低': 'low',
                        '成交量': 'volume',
                        '成交额': 'amount'
                    }

                    # 重命名列
                    for chinese_name, english_name in column_mapping.items():
                        if chinese_name in minute_data.columns:
                            minute_data[english_name] = minute_data[chinese_name]

                    # 确保数据类型正确
                    if 'time' in minute_data.columns:
                        minute_data['time'] = pd.to_datetime(minute_data['time'])

                    for col in ['open', 'close', 'high', 'low']:
                        if col in minute_data.columns:
                            minute_data[col] = pd.to_numeric(minute_data[col], errors='coerce')

                    if 'volume' in minute_data.columns:
                        minute_data['volume'] = pd.to_numeric(minute_data['volume'], errors='coerce')
                    if 'amount' in minute_data.columns:
                        minute_data['amount'] = pd.to_numeric(minute_data['amount'], errors='coerce')
                    
                    logger.info(f"获取到{len(minute_data)}条分时数据")
                    return minute_data
                    
            except Exception as e:
                logger.warning(f"获取今日分时数据失败: {e}")
            
            # 如果获取今日数据失败，尝试获取最近一个交易日的数据
            yesterday = datetime.now() - timedelta(days=1)
            yesterday_str = yesterday.strftime('%Y%m%d')
            
            try:
                minute_data = ak.stock_zh_a_hist_min_em(
                    symbol=stock_code,
                    start_date=yesterday_str,
                    end_date=yesterday_str,
                    period='1',
                    adjust=''
                )
                
                if not minute_data.empty:
                    # 标准化昨日数据的列名
                    column_mapping = {
                        '时间': 'time',
                        '开盘': 'open',
                        '收盘': 'close',
                        '最高': 'high',
                        '最低': 'low',
                        '成交量': 'volume',
                        '成交额': 'amount'
                    }

                    for chinese_name, english_name in column_mapping.items():
                        if chinese_name in minute_data.columns:
                            minute_data[english_name] = minute_data[chinese_name]

                    # 确保数据类型正确
                    if 'time' in minute_data.columns:
                        minute_data['time'] = pd.to_datetime(minute_data['time'])

                    for col in ['open', 'close', 'high', 'low']:
                        if col in minute_data.columns:
                            minute_data[col] = pd.to_numeric(minute_data[col], errors='coerce')

                    if 'volume' in minute_data.columns:
                        minute_data['volume'] = pd.to_numeric(minute_data['volume'], errors='coerce')
                    if 'amount' in minute_data.columns:
                        minute_data['amount'] = pd.to_numeric(minute_data['amount'], errors='coerce')

                    logger.info(f"获取到昨日{len(minute_data)}条分时数据")
                    return minute_data
                    
            except Exception as e:
                logger.warning(f"获取昨日分时数据失败: {e}")
            
            logger.warning("无法获取分时数据")
            return pd.DataFrame()
            
        except Exception as e:
            logger.error(f"获取分时数据失败: {e}")
            return pd.DataFrame()
    
    def _analyze_auction_performance(self, historical_data: pd.DataFrame, 
                                   minute_data: pd.DataFrame) -> Dict:
        """分析集合竞价表现"""
        try:
            logger.info("分析集合竞价表现")
            
            analysis = {
                'historical_auction_stats': {},
                'latest_auction_performance': {},
                'auction_patterns': [],
                'auction_signals': []
            }
            
            # 1. 分析历史集合竞价统计
            if not historical_data.empty:
                analysis['historical_auction_stats'] = self._calculate_historical_auction_stats(historical_data)
            
            # 2. 分析最新集合竞价表现
            if not minute_data.empty:
                analysis['latest_auction_performance'] = self._analyze_latest_auction(minute_data, historical_data)
            
            # 3. 识别集合竞价模式
            if not historical_data.empty:
                analysis['auction_patterns'] = self._identify_auction_patterns(historical_data)
            
            # 4. 生成集合竞价信号
            analysis['auction_signals'] = self._generate_auction_signals(
                analysis['historical_auction_stats'],
                analysis['latest_auction_performance'],
                analysis['auction_patterns']
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"分析集合竞价表现失败: {e}")
            return {}
    
    def _calculate_historical_auction_stats(self, historical_data: pd.DataFrame) -> Dict:
        """计算历史集合竞价统计"""
        try:
            stats = {}
            
            # 计算开盘价相对于前收盘价的表现
            historical_data['prev_close'] = historical_data['close'].shift(1)
            historical_data['open_change'] = (historical_data['open'] - historical_data['prev_close']) / historical_data['prev_close'] * 100
            
            # 计算开盘价在当日价格区间的位置
            historical_data['price_range'] = historical_data['high'] - historical_data['low']
            historical_data['open_position'] = np.where(
                historical_data['price_range'] > 0,
                (historical_data['open'] - historical_data['low']) / historical_data['price_range'] * 100,
                50  # 一字板情况
            )
            
            # 统计开盘表现
            stats['avg_open_change'] = historical_data['open_change'].mean()
            stats['open_change_std'] = historical_data['open_change'].std()
            stats['avg_open_position'] = historical_data['open_position'].mean()
            
            # 统计开盘类型分布
            high_open_count = len(historical_data[historical_data['open_change'] > 2])
            low_open_count = len(historical_data[historical_data['open_change'] < -2])
            flat_open_count = len(historical_data[abs(historical_data['open_change']) <= 2])
            
            total_days = len(historical_data)
            stats['high_open_ratio'] = high_open_count / total_days * 100 if total_days > 0 else 0
            stats['low_open_ratio'] = low_open_count / total_days * 100 if total_days > 0 else 0
            stats['flat_open_ratio'] = flat_open_count / total_days * 100 if total_days > 0 else 0
            
            # 分析开盘后表现
            historical_data['open_to_close'] = (historical_data['close'] - historical_data['open']) / historical_data['open'] * 100
            stats['avg_open_to_close'] = historical_data['open_to_close'].mean()
            
            # 分析开盘成交量特征
            if 'volume' in historical_data.columns:
                stats['avg_volume'] = historical_data['volume'].mean()
                stats['volume_std'] = historical_data['volume'].std()
            
            return {k: round(v, 2) if isinstance(v, (int, float)) else v for k, v in stats.items()}
            
        except Exception as e:
            logger.error(f"计算历史集合竞价统计失败: {e}")
            return {}
    
    def _analyze_latest_auction(self, minute_data: pd.DataFrame, historical_data: pd.DataFrame) -> Dict:
        """分析最新集合竞价表现"""
        try:
            if minute_data.empty:
                return {}
            
            # 获取开盘第一分钟数据（集合竞价结果）
            first_minute = minute_data.iloc[0]
            
            # 获取昨日收盘价
            prev_close = historical_data.iloc[-1]['close'] if not historical_data.empty else first_minute['open']
            
            # 计算集合竞价表现
            open_change = (first_minute['open'] - prev_close) / prev_close * 100
            
            # 分析集合竞价成交量
            auction_volume = first_minute['volume'] if 'volume' in first_minute else 0
            auction_amount = first_minute['amount'] if 'amount' in first_minute else 0
            
            # 计算相对于历史平均的表现
            historical_stats = self._calculate_historical_auction_stats(historical_data)
            avg_volume = historical_stats.get('avg_volume', 0)
            
            volume_ratio = auction_volume / avg_volume if avg_volume > 0 else 0
            
            return {
                'open_price': first_minute['open'],
                'prev_close': prev_close,
                'open_change': round(open_change, 2),
                'auction_volume': auction_volume,
                'auction_amount': auction_amount,
                'volume_ratio_vs_avg': round(volume_ratio, 2),
                'auction_type': self._classify_auction_type(open_change),
                'volume_significance': self._classify_volume_significance(volume_ratio)
            }
            
        except Exception as e:
            logger.error(f"分析最新集合竞价失败: {e}")
            return {}
    
    def _classify_auction_type(self, open_change: float) -> str:
        """分类集合竞价类型"""
        if open_change > 5:
            return "大幅高开"
        elif open_change > 2:
            return "高开"
        elif open_change > 0.5:
            return "微幅高开"
        elif open_change > -0.5:
            return "平开"
        elif open_change > -2:
            return "微幅低开"
        elif open_change > -5:
            return "低开"
        else:
            return "大幅低开"
    
    def _classify_volume_significance(self, volume_ratio: float) -> str:
        """分类成交量显著性"""
        if volume_ratio > 3:
            return "巨量"
        elif volume_ratio > 2:
            return "放量"
        elif volume_ratio > 1.5:
            return "温和放量"
        elif volume_ratio > 0.5:
            return "正常"
        else:
            return "缩量"

    def _identify_auction_patterns(self, historical_data: pd.DataFrame) -> List[str]:
        """识别集合竞价模式"""
        try:
            patterns = []

            if historical_data.empty:
                return patterns

            # 计算开盘变化
            historical_data['prev_close'] = historical_data['close'].shift(1)
            historical_data['open_change'] = (historical_data['open'] - historical_data['prev_close']) / historical_data['prev_close'] * 100

            recent_opens = historical_data['open_change'].tail(10)

            # 识别连续高开模式
            high_opens = (recent_opens > 1).sum()
            if high_opens >= 3:
                patterns.append(f"近期连续{high_opens}次高开，可能存在资金推动")

            # 识别连续低开模式
            low_opens = (recent_opens < -1).sum()
            if low_opens >= 3:
                patterns.append(f"近期连续{low_opens}次低开，可能存在抛压")

            # 识别开盘波动加大
            recent_volatility = recent_opens.std()
            historical_volatility = historical_data['open_change'].std()

            if recent_volatility > historical_volatility * 1.5:
                patterns.append("近期开盘波动加大，市场分歧增加")

            # 识别开盘一致性
            if recent_volatility < historical_volatility * 0.5:
                patterns.append("近期开盘相对稳定，市场预期一致")

            return patterns

        except Exception as e:
            logger.error(f"识别集合竞价模式失败: {e}")
            return []

    def _generate_auction_signals(self, historical_stats: Dict, latest_auction: Dict, patterns: List) -> List[str]:
        """生成集合竞价信号"""
        signals = []

        if not latest_auction:
            return signals

        open_change = latest_auction.get('open_change', 0)
        volume_ratio = latest_auction.get('volume_ratio_vs_avg', 0)
        auction_type = latest_auction.get('auction_type', '')

        # 基于开盘幅度的信号
        if open_change > 3 and volume_ratio > 2:
            signals.append("大幅高开+放量，可能有重大利好或主力拉升")
        elif open_change < -3 and volume_ratio > 2:
            signals.append("大幅低开+放量，可能有重大利空或主力出货")
        elif abs(open_change) > 2 and volume_ratio < 0.5:
            signals.append("开盘异动但成交量不足，需要观察后续表现")

        # 基于历史对比的信号
        avg_open_change = historical_stats.get('avg_open_change', 0)
        if open_change > avg_open_change + 2:
            signals.append("开盘表现明显强于历史平均，关注强势信号")
        elif open_change < avg_open_change - 2:
            signals.append("开盘表现明显弱于历史平均，关注弱势信号")

        # 基于模式的信号
        for pattern in patterns:
            if "连续" in pattern and "高开" in pattern:
                signals.append("连续高开模式，注意主力操作迹象")
            elif "连续" in pattern and "低开" in pattern:
                signals.append("连续低开模式，注意资金流出迹象")

        return signals

    def _identify_opening_anomaly(self, historical_data: pd.DataFrame, minute_data: pd.DataFrame) -> Dict:
        """识别开盘异动"""
        try:
            logger.info("识别开盘异动")

            anomaly_analysis = {
                'price_anomaly': {},
                'volume_anomaly': {},
                'pattern_anomaly': {},
                'anomaly_signals': []
            }

            if minute_data.empty:
                return anomaly_analysis

            # 1. 价格异动分析
            anomaly_analysis['price_anomaly'] = self._analyze_price_anomaly(minute_data, historical_data)

            # 2. 成交量异动分析
            anomaly_analysis['volume_anomaly'] = self._analyze_volume_anomaly(minute_data, historical_data)

            # 3. 模式异动分析
            anomaly_analysis['pattern_anomaly'] = self._analyze_pattern_anomaly(minute_data)

            # 4. 生成异动信号
            anomaly_analysis['anomaly_signals'] = self._generate_anomaly_signals(
                anomaly_analysis['price_anomaly'],
                anomaly_analysis['volume_anomaly'],
                anomaly_analysis['pattern_anomaly']
            )

            return anomaly_analysis

        except Exception as e:
            logger.error(f"识别开盘异动失败: {e}")
            return {}

    def _analyze_price_anomaly(self, minute_data: pd.DataFrame, historical_data: pd.DataFrame) -> Dict:
        """分析价格异动"""
        try:
            if minute_data.empty:
                return {}

            # 获取开盘后前30分钟的数据
            early_data = minute_data.head(30)

            # 计算价格波动
            price_changes = []
            for i in range(1, len(early_data)):
                change = (early_data.iloc[i]['close'] - early_data.iloc[i-1]['close']) / early_data.iloc[i-1]['close'] * 100
                price_changes.append(abs(change))

            if not price_changes:
                return {}

            max_change = max(price_changes)
            avg_change = np.mean(price_changes)

            # 计算开盘后的价格区间
            open_price = early_data.iloc[0]['open']
            max_price = early_data['high'].max()
            min_price = early_data['low'].min()

            price_range = (max_price - min_price) / open_price * 100

            # 判断异动程度
            anomaly_level = "正常"
            if max_change > 3 or price_range > 5:
                anomaly_level = "高度异动"
            elif max_change > 1.5 or price_range > 3:
                anomaly_level = "中度异动"
            elif max_change > 0.8 or price_range > 1.5:
                anomaly_level = "轻度异动"

            return {
                'max_minute_change': round(max_change, 2),
                'avg_minute_change': round(avg_change, 2),
                'early_price_range': round(price_range, 2),
                'anomaly_level': anomaly_level,
                'open_price': open_price,
                'early_high': max_price,
                'early_low': min_price
            }

        except Exception as e:
            logger.error(f"分析价格异动失败: {e}")
            return {}

    def _analyze_volume_anomaly(self, minute_data: pd.DataFrame, historical_data: pd.DataFrame) -> Dict:
        """分析成交量异动"""
        try:
            if minute_data.empty or 'volume' not in minute_data.columns:
                return {}

            # 获取开盘后前30分钟的成交量
            early_data = minute_data.head(30)
            early_volumes = early_data['volume'].tolist()

            # 计算成交量统计
            total_early_volume = sum(early_volumes)
            max_minute_volume = max(early_volumes) if early_volumes else 0
            avg_minute_volume = np.mean(early_volumes) if early_volumes else 0

            # 与历史平均对比
            if not historical_data.empty and 'volume' in historical_data.columns:
                historical_avg_volume = historical_data['volume'].mean()
                # 估算历史平均30分钟成交量（假设均匀分布）
                estimated_30min_volume = historical_avg_volume * 30 / 240  # 240分钟交易时间

                volume_ratio = total_early_volume / estimated_30min_volume if estimated_30min_volume > 0 else 0
            else:
                volume_ratio = 0

            # 判断成交量异动程度
            volume_anomaly_level = "正常"
            if volume_ratio > 3:
                volume_anomaly_level = "巨量异动"
            elif volume_ratio > 2:
                volume_anomaly_level = "明显放量"
            elif volume_ratio > 1.5:
                volume_anomaly_level = "温和放量"
            elif volume_ratio < 0.5:
                volume_anomaly_level = "明显缩量"

            return {
                'total_early_volume': total_early_volume,
                'max_minute_volume': max_minute_volume,
                'avg_minute_volume': round(avg_minute_volume, 0),
                'volume_ratio_vs_historical': round(volume_ratio, 2),
                'volume_anomaly_level': volume_anomaly_level
            }

        except Exception as e:
            logger.error(f"分析成交量异动失败: {e}")
            return {}

    def _analyze_pattern_anomaly(self, minute_data: pd.DataFrame) -> Dict:
        """分析模式异动"""
        try:
            if minute_data.empty:
                return {}

            # 获取开盘后前30分钟数据
            early_data = minute_data.head(30)

            patterns = []

            # 1. 检测连续涨停或跌停
            limit_up_count = 0
            limit_down_count = 0

            for i, row in early_data.iterrows():
                change = (row['close'] - early_data.iloc[0]['open']) / early_data.iloc[0]['open'] * 100
                if change >= 9.8:  # 接近涨停
                    limit_up_count += 1
                elif change <= -9.8:  # 接近跌停
                    limit_down_count += 1

            if limit_up_count > 5:
                patterns.append("开盘后快速拉升至涨停附近")
            elif limit_down_count > 5:
                patterns.append("开盘后快速下跌至跌停附近")

            # 2. 检测震荡模式
            price_changes = []
            for i in range(1, len(early_data)):
                change = (early_data.iloc[i]['close'] - early_data.iloc[i-1]['close']) / early_data.iloc[i-1]['close'] * 100
                price_changes.append(change)

            if price_changes:
                volatility = np.std(price_changes)
                if volatility > 1:
                    patterns.append("开盘后剧烈震荡")
                elif volatility < 0.2:
                    patterns.append("开盘后走势平稳")

            # 3. 检测单边走势
            if len(early_data) > 10:
                first_half = early_data.head(15)['close'].mean()
                second_half = early_data.tail(15)['close'].mean()

                trend_change = (second_half - first_half) / first_half * 100
                if trend_change > 2:
                    patterns.append("开盘后持续上涨")
                elif trend_change < -2:
                    patterns.append("开盘后持续下跌")

            return {
                'detected_patterns': patterns,
                'limit_up_minutes': limit_up_count,
                'limit_down_minutes': limit_down_count,
                'early_volatility': round(np.std(price_changes), 3) if price_changes else 0
            }

        except Exception as e:
            logger.error(f"分析模式异动失败: {e}")
            return {}

    def _generate_anomaly_signals(self, price_anomaly: Dict, volume_anomaly: Dict, pattern_anomaly: Dict) -> List[str]:
        """生成异动信号"""
        signals = []

        # 价格异动信号
        price_level = price_anomaly.get('anomaly_level', '')
        if price_level == "高度异动":
            signals.append("开盘后价格高度异动，关注重大消息或主力操作")
        elif price_level == "中度异动":
            signals.append("开盘后价格中度异动，市场活跃度较高")

        # 成交量异动信号
        volume_level = volume_anomaly.get('volume_anomaly_level', '')
        if volume_level == "巨量异动":
            signals.append("开盘后巨量异动，可能有重大资金进出")
        elif volume_level == "明显放量":
            signals.append("开盘后明显放量，关注资金动向")
        elif volume_level == "明显缩量":
            signals.append("开盘后明显缩量，市场观望情绪浓厚")

        # 模式异动信号
        patterns = pattern_anomaly.get('detected_patterns', [])
        for pattern in patterns:
            if "涨停" in pattern:
                signals.append("开盘后快速拉升，可能有强势主力介入")
            elif "跌停" in pattern:
                signals.append("开盘后快速下跌，可能有恐慌性抛售")
            elif "剧烈震荡" in pattern:
                signals.append("开盘后剧烈震荡，多空分歧较大")

        return signals

    def _analyze_early_trial_behavior(self, minute_data: pd.DataFrame, historical_data: pd.DataFrame) -> Dict:
        """分析早盘试盘行为"""
        try:
            logger.info("分析早盘试盘行为")

            if minute_data.empty:
                return {}

            # 获取开盘后前60分钟数据（早盘试盘关键时段）
            early_data = minute_data.head(60)

            trial_analysis = {
                'trial_patterns': [],
                'support_resistance': {},
                'volume_distribution': {},
                'trial_signals': []
            }

            # 1. 识别试盘模式
            trial_analysis['trial_patterns'] = self._identify_trial_patterns(early_data)

            # 2. 分析支撑阻力测试
            trial_analysis['support_resistance'] = self._analyze_support_resistance_test(early_data, historical_data)

            # 3. 分析成交量分布特征
            trial_analysis['volume_distribution'] = self._analyze_early_volume_distribution(early_data)

            # 4. 生成试盘信号
            trial_analysis['trial_signals'] = self._generate_trial_signals(
                trial_analysis['trial_patterns'],
                trial_analysis['support_resistance'],
                trial_analysis['volume_distribution']
            )

            return trial_analysis

        except Exception as e:
            logger.error(f"分析早盘试盘行为失败: {e}")
            return {}

    def _identify_trial_patterns(self, early_data: pd.DataFrame) -> List[Dict]:
        """识别试盘模式"""
        try:
            patterns = []

            if len(early_data) < 10:
                return patterns

            open_price = early_data.iloc[0]['open']

            # 1. 识别上试模式（向上试探）
            up_trial_count = 0
            max_up_ratio = 0

            for i, row in early_data.iterrows():
                up_ratio = (row['high'] - open_price) / open_price * 100
                if up_ratio > 1:  # 向上试探超过1%
                    up_trial_count += 1
                    max_up_ratio = max(max_up_ratio, up_ratio)

            if up_trial_count >= 5:
                patterns.append({
                    'type': '上试模式',
                    'description': f'向上试探{up_trial_count}次，最高试探{max_up_ratio:.2f}%',
                    'strength': '强' if max_up_ratio > 3 else '中' if max_up_ratio > 1.5 else '弱'
                })

            # 2. 识别下试模式（向下试探）
            down_trial_count = 0
            max_down_ratio = 0

            for i, row in early_data.iterrows():
                down_ratio = (open_price - row['low']) / open_price * 100
                if down_ratio > 1:  # 向下试探超过1%
                    down_trial_count += 1
                    max_down_ratio = max(max_down_ratio, down_ratio)

            if down_trial_count >= 5:
                patterns.append({
                    'type': '下试模式',
                    'description': f'向下试探{down_trial_count}次，最低试探{max_down_ratio:.2f}%',
                    'strength': '强' if max_down_ratio > 3 else '中' if max_down_ratio > 1.5 else '弱'
                })

            # 3. 识别震荡试盘模式
            price_changes = []
            for i in range(1, len(early_data)):
                change = abs((early_data.iloc[i]['close'] - early_data.iloc[i-1]['close']) / early_data.iloc[i-1]['close'] * 100)
                price_changes.append(change)

            if price_changes:
                avg_volatility = np.mean(price_changes)
                volatility_count = sum(1 for change in price_changes if change > 0.5)

                if volatility_count >= 10 and avg_volatility > 0.3:
                    patterns.append({
                        'type': '震荡试盘',
                        'description': f'频繁震荡{volatility_count}次，平均波动{avg_volatility:.2f}%',
                        'strength': '强' if avg_volatility > 0.8 else '中' if avg_volatility > 0.4 else '弱'
                    })

            # 4. 识别单边试盘模式
            first_quarter = early_data.head(15)['close'].mean()
            last_quarter = early_data.tail(15)['close'].mean()

            trend_ratio = (last_quarter - first_quarter) / first_quarter * 100

            if abs(trend_ratio) > 1:
                direction = '上涨' if trend_ratio > 0 else '下跌'
                patterns.append({
                    'type': f'单边{direction}试盘',
                    'description': f'早盘单边{direction}{abs(trend_ratio):.2f}%',
                    'strength': '强' if abs(trend_ratio) > 2 else '中' if abs(trend_ratio) > 1.5 else '弱'
                })

            return patterns

        except Exception as e:
            logger.error(f"识别试盘模式失败: {e}")
            return []

    def _analyze_support_resistance_test(self, early_data: pd.DataFrame, historical_data: pd.DataFrame) -> Dict:
        """分析支撑阻力测试"""
        try:
            if early_data.empty:
                return {}

            open_price = early_data.iloc[0]['open']

            # 计算关键价位
            key_levels = {}

            # 1. 基于历史数据计算关键价位
            if not historical_data.empty:
                recent_high = historical_data['high'].tail(10).max()
                recent_low = historical_data['low'].tail(10).min()
                prev_close = historical_data.iloc[-1]['close']

                key_levels['recent_high'] = recent_high
                key_levels['recent_low'] = recent_low
                key_levels['prev_close'] = prev_close
                key_levels['resistance_1'] = prev_close * 1.03  # 3%阻力位
                key_levels['support_1'] = prev_close * 0.97    # 3%支撑位

            # 2. 分析早盘对关键价位的测试
            test_results = {}

            for level_name, level_price in key_levels.items():
                if level_price <= 0:
                    continue

                # 计算触及次数和最接近程度
                touch_count = 0
                min_distance = float('inf')

                for i, row in early_data.iterrows():
                    high_distance = abs(row['high'] - level_price) / level_price * 100
                    low_distance = abs(row['low'] - level_price) / level_price * 100

                    min_distance = min(min_distance, high_distance, low_distance)

                    # 如果价格在关键位附近0.5%范围内，认为是触及
                    if high_distance <= 0.5 or low_distance <= 0.5:
                        touch_count += 1

                test_results[level_name] = {
                    'level_price': round(level_price, 2),
                    'touch_count': touch_count,
                    'min_distance': round(min_distance, 2),
                    'test_strength': '强' if touch_count >= 3 else '中' if touch_count >= 1 else '弱'
                }

            return {
                'key_levels': key_levels,
                'test_results': test_results,
                'most_tested_level': max(test_results.items(), key=lambda x: x[1]['touch_count'])[0] if test_results else None
            }

        except Exception as e:
            logger.error(f"分析支撑阻力测试失败: {e}")
            return {}

    def _analyze_early_volume_distribution(self, early_data: pd.DataFrame) -> Dict:
        """分析早盘成交量分布特征"""
        try:
            if early_data.empty or 'volume' not in early_data.columns:
                return {}

            volumes = early_data['volume'].tolist()
            total_volume = sum(volumes)

            if total_volume == 0:
                return {}

            # 1. 按时间段分析成交量分布
            first_15min = sum(volumes[:15]) if len(volumes) >= 15 else sum(volumes)
            first_30min = sum(volumes[:30]) if len(volumes) >= 30 else sum(volumes)
            first_60min = sum(volumes[:60]) if len(volumes) >= 60 else sum(volumes)

            # 2. 计算成交量集中度
            max_volume = max(volumes) if volumes else 0
            avg_volume = total_volume / len(volumes) if volumes else 0

            volume_concentration = max_volume / avg_volume if avg_volume > 0 else 0

            # 3. 识别成交量模式
            volume_patterns = []

            # 前重后轻模式
            if len(volumes) >= 30:
                first_half_avg = np.mean(volumes[:30])
                second_half_avg = np.mean(volumes[30:]) if len(volumes) > 30 else first_half_avg

                if first_half_avg > second_half_avg * 1.5:
                    volume_patterns.append("前重后轻")
                elif second_half_avg > first_half_avg * 1.5:
                    volume_patterns.append("前轻后重")
                else:
                    volume_patterns.append("分布均匀")

            # 4. 计算成交量变化趋势
            if len(volumes) >= 10:
                early_volumes = volumes[:10]
                late_volumes = volumes[-10:]

                early_avg = np.mean(early_volumes)
                late_avg = np.mean(late_volumes)

                volume_trend = (late_avg - early_avg) / early_avg * 100 if early_avg > 0 else 0
            else:
                volume_trend = 0

            return {
                'total_early_volume': total_volume,
                'first_15min_volume': first_15min,
                'first_30min_volume': first_30min,
                'first_60min_volume': first_60min,
                'volume_concentration': round(volume_concentration, 2),
                'volume_patterns': volume_patterns,
                'volume_trend': round(volume_trend, 2),
                'avg_minute_volume': round(avg_volume, 0),
                'max_minute_volume': max_volume
            }

        except Exception as e:
            logger.error(f"分析早盘成交量分布失败: {e}")
            return {}

    def _generate_trial_signals(self, patterns: List, support_resistance: Dict, volume_dist: Dict) -> List[str]:
        """生成试盘信号"""
        signals = []

        # 基于试盘模式的信号
        for pattern in patterns:
            pattern_type = pattern.get('type', '')
            strength = pattern.get('strength', '')

            if pattern_type == '上试模式' and strength in ['强', '中']:
                signals.append("早盘向上试盘明显，可能测试上方阻力")
            elif pattern_type == '下试模式' and strength in ['强', '中']:
                signals.append("早盘向下试盘明显，可能测试下方支撑")
            elif pattern_type == '震荡试盘' and strength == '强':
                signals.append("早盘震荡试盘激烈，多空分歧较大")
            elif '单边' in pattern_type and strength in ['强', '中']:
                direction = '看多' if '上涨' in pattern_type else '看空'
                signals.append(f"早盘单边试盘，市场倾向{direction}")

        # 基于支撑阻力测试的信号
        test_results = support_resistance.get('test_results', {})
        for level_name, result in test_results.items():
            if result['test_strength'] == '强':
                if 'resistance' in level_name:
                    signals.append(f"早盘多次测试{level_name}阻力位，关注突破情况")
                elif 'support' in level_name:
                    signals.append(f"早盘多次测试{level_name}支撑位，关注破位风险")

        # 基于成交量分布的信号
        volume_patterns = volume_dist.get('volume_patterns', [])
        volume_trend = volume_dist.get('volume_trend', 0)

        if '前重后轻' in volume_patterns:
            signals.append("早盘成交量前重后轻，可能是冲高回落模式")
        elif '前轻后重' in volume_patterns:
            signals.append("早盘成交量前轻后重，可能是蓄势待发模式")

        if volume_trend > 50:
            signals.append("早盘成交量递增明显，市场活跃度上升")
        elif volume_trend < -50:
            signals.append("早盘成交量递减明显，市场活跃度下降")

        return signals

    def _track_opening_large_orders(self, minute_data: pd.DataFrame) -> Dict:
        """追踪开盘大单"""
        try:
            logger.info("追踪开盘大单")

            if minute_data.empty or 'amount' not in minute_data.columns:
                return {}

            # 获取开盘后前30分钟数据
            early_data = minute_data.head(30)

            large_order_analysis = {
                'large_orders': [],
                'order_statistics': {},
                'order_patterns': [],
                'order_signals': []
            }

            # 1. 识别大单
            large_order_analysis['large_orders'] = self._identify_large_orders(early_data)

            # 2. 计算大单统计
            large_order_analysis['order_statistics'] = self._calculate_order_statistics(
                early_data, large_order_analysis['large_orders']
            )

            # 3. 识别大单模式
            large_order_analysis['order_patterns'] = self._identify_order_patterns(
                large_order_analysis['large_orders']
            )

            # 4. 生成大单信号
            large_order_analysis['order_signals'] = self._generate_order_signals(
                large_order_analysis['order_statistics'],
                large_order_analysis['order_patterns']
            )

            return large_order_analysis

        except Exception as e:
            logger.error(f"追踪开盘大单失败: {e}")
            return {}

    def _identify_large_orders(self, early_data: pd.DataFrame) -> List[Dict]:
        """识别大单"""
        try:
            large_orders = []

            if 'amount' not in early_data.columns or 'volume' not in early_data.columns:
                return large_orders

            for i, row in early_data.iterrows():
                amount = row['amount']
                volume = row['volume']

                if amount >= self.large_order_threshold:
                    # 计算平均价格
                    avg_price = amount / volume if volume > 0 else row['close']

                    # 判断大单类型
                    if i == 0:
                        order_type = "开盘大单"
                    elif amount >= self.large_order_threshold * 2:
                        order_type = "超大单"
                    else:
                        order_type = "大单"

                    large_orders.append({
                        'time_index': i,
                        'amount': amount,
                        'volume': volume,
                        'avg_price': round(avg_price, 2),
                        'order_type': order_type,
                        'price': row['close']
                    })

            return large_orders

        except Exception as e:
            logger.error(f"识别大单失败: {e}")
            return []

    def _calculate_order_statistics(self, early_data: pd.DataFrame, large_orders: List) -> Dict:
        """计算大单统计"""
        try:
            if not large_orders:
                return {}

            total_large_amount = sum(order['amount'] for order in large_orders)
            total_early_amount = early_data['amount'].sum() if 'amount' in early_data.columns else 0

            large_order_ratio = total_large_amount / total_early_amount * 100 if total_early_amount > 0 else 0

            # 按类型统计
            order_type_stats = {}
            for order in large_orders:
                order_type = order['order_type']
                if order_type not in order_type_stats:
                    order_type_stats[order_type] = {'count': 0, 'total_amount': 0}

                order_type_stats[order_type]['count'] += 1
                order_type_stats[order_type]['total_amount'] += order['amount']

            # 计算大单频率
            large_order_frequency = len(large_orders) / len(early_data) * 100 if len(early_data) > 0 else 0

            # 计算平均大单金额
            avg_large_order_amount = total_large_amount / len(large_orders) if large_orders else 0

            return {
                'total_large_orders': len(large_orders),
                'total_large_amount': total_large_amount,
                'large_order_ratio': round(large_order_ratio, 2),
                'large_order_frequency': round(large_order_frequency, 2),
                'avg_large_order_amount': round(avg_large_order_amount, 0),
                'order_type_stats': order_type_stats
            }

        except Exception as e:
            logger.error(f"计算大单统计失败: {e}")
            return {}

    def _identify_order_patterns(self, large_orders: List) -> List[str]:
        """识别大单模式"""
        try:
            patterns = []

            if not large_orders:
                return patterns

            # 1. 识别连续大单
            consecutive_count = 0
            max_consecutive = 0

            for i in range(len(large_orders)):
                if i == 0 or large_orders[i]['time_index'] == large_orders[i-1]['time_index'] + 1:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 1

            if max_consecutive >= 3:
                patterns.append(f"连续{max_consecutive}分钟大单")

            # 2. 识别开盘集中大单
            opening_large_orders = [order for order in large_orders if order['time_index'] <= 5]
            if len(opening_large_orders) >= 3:
                patterns.append("开盘5分钟内集中大单")

            # 3. 识别递增大单模式
            if len(large_orders) >= 3:
                amounts = [order['amount'] for order in large_orders]
                increasing_count = 0

                for i in range(1, len(amounts)):
                    if amounts[i] > amounts[i-1]:
                        increasing_count += 1

                if increasing_count >= len(amounts) * 0.7:
                    patterns.append("大单金额递增模式")

            # 4. 识别超大单出现
            super_large_orders = [order for order in large_orders if order['order_type'] == '超大单']
            if super_large_orders:
                patterns.append(f"出现{len(super_large_orders)}笔超大单")

            return patterns

        except Exception as e:
            logger.error(f"识别大单模式失败: {e}")
            return []

    def _generate_order_signals(self, statistics: Dict, patterns: List) -> List[str]:
        """生成大单信号"""
        signals = []

        # 基于统计数据的信号
        large_order_ratio = statistics.get('large_order_ratio', 0)
        large_order_frequency = statistics.get('large_order_frequency', 0)

        if large_order_ratio > 50:
            signals.append("早盘大单占比过半，可能有主力资金活跃")
        elif large_order_ratio > 30:
            signals.append("早盘大单占比较高，关注主力动向")

        if large_order_frequency > 20:
            signals.append("早盘大单频率较高，市场活跃度强")

        # 基于模式的信号
        for pattern in patterns:
            if "连续" in pattern and "大单" in pattern:
                signals.append("早盘连续大单出现，可能有持续资金介入")
            elif "开盘5分钟内集中大单" in pattern:
                signals.append("开盘集中大单，可能有预谋性操作")
            elif "递增模式" in pattern:
                signals.append("大单金额递增，资金介入力度加强")
            elif "超大单" in pattern:
                signals.append("出现超大单，可能有重要资金进场")

        return signals

    def _generate_comprehensive_assessment(self, auction_analysis: Dict, opening_anomaly: Dict,
                                         trial_analysis: Dict, large_order_tracking: Dict) -> Dict:
        """生成开盘特征综合评估"""
        try:
            logger.info("生成开盘特征综合评估")

            assessment = {
                'overall_score': 0,
                'key_findings': [],
                'risk_warnings': [],
                'opportunity_signals': [],
                'operation_suggestions': []
            }

            # 1. 计算综合评分
            assessment['overall_score'] = self._calculate_overall_score(
                auction_analysis, opening_anomaly, trial_analysis, large_order_tracking
            )

            # 2. 提取关键发现
            assessment['key_findings'] = self._extract_key_findings(
                auction_analysis, opening_anomaly, trial_analysis, large_order_tracking
            )

            # 3. 识别风险警告
            assessment['risk_warnings'] = self._identify_risk_warnings(
                auction_analysis, opening_anomaly, trial_analysis, large_order_tracking
            )

            # 4. 发现机会信号
            assessment['opportunity_signals'] = self._identify_opportunity_signals(
                auction_analysis, opening_anomaly, trial_analysis, large_order_tracking
            )

            # 5. 生成操作建议
            assessment['operation_suggestions'] = self._generate_operation_suggestions_comprehensive(
                assessment['overall_score'], assessment['key_findings'],
                assessment['risk_warnings'], assessment['opportunity_signals']
            )

            return assessment

        except Exception as e:
            logger.error(f"生成开盘特征综合评估失败: {e}")
            return {}

    def _calculate_overall_score(self, auction: Dict, anomaly: Dict, trial: Dict, orders: Dict) -> int:
        """计算综合评分（0-100分）"""
        try:
            score = 50  # 基础分

            # 集合竞价评分
            auction_signals = auction.get('auction_signals', [])
            if any('强势' in signal for signal in auction_signals):
                score += 15
            elif any('弱势' in signal for signal in auction_signals):
                score -= 15

            # 异动评分
            price_anomaly_level = anomaly.get('price_anomaly', {}).get('anomaly_level', '')
            volume_anomaly_level = anomaly.get('volume_anomaly', {}).get('volume_anomaly_level', '')

            if price_anomaly_level == '高度异动':
                score += 10
            elif price_anomaly_level == '中度异动':
                score += 5

            if volume_anomaly_level in ['巨量异动', '明显放量']:
                score += 10
            elif volume_anomaly_level == '明显缩量':
                score -= 10

            # 试盘行为评分
            trial_signals = trial.get('trial_signals', [])
            if any('看多' in signal for signal in trial_signals):
                score += 10
            elif any('看空' in signal for signal in trial_signals):
                score -= 10

            # 大单评分
            order_signals = orders.get('order_signals', [])
            if any('主力' in signal for signal in order_signals):
                score += 15

            return max(0, min(100, score))

        except Exception as e:
            logger.error(f"计算综合评分失败: {e}")
            return 50

    def _extract_key_findings(self, auction: Dict, anomaly: Dict, trial: Dict, orders: Dict) -> List[str]:
        """提取关键发现"""
        findings = []

        # 集合竞价关键发现
        latest_auction = auction.get('latest_auction_performance', {})
        if latest_auction:
            auction_type = latest_auction.get('auction_type', '')
            volume_significance = latest_auction.get('volume_significance', '')
            findings.append(f"集合竞价: {auction_type}, 成交量{volume_significance}")

        # 异动关键发现
        price_anomaly = anomaly.get('price_anomaly', {})
        if price_anomaly.get('anomaly_level') in ['高度异动', '中度异动']:
            findings.append(f"价格异动: {price_anomaly.get('anomaly_level')}, 最大分钟变化{price_anomaly.get('max_minute_change', 0)}%")

        volume_anomaly = anomaly.get('volume_anomaly', {})
        if volume_anomaly.get('volume_anomaly_level') in ['巨量异动', '明显放量']:
            findings.append(f"成交量异动: {volume_anomaly.get('volume_anomaly_level')}")

        # 试盘关键发现
        trial_patterns = trial.get('trial_patterns', [])
        for pattern in trial_patterns[:2]:  # 取前2个最重要的模式
            findings.append(f"试盘行为: {pattern.get('type', '')}, 强度{pattern.get('strength', '')}")

        # 大单关键发现
        order_stats = orders.get('order_statistics', {})
        if order_stats.get('total_large_orders', 0) > 0:
            findings.append(f"大单追踪: {order_stats.get('total_large_orders')}笔大单, 占比{order_stats.get('large_order_ratio', 0)}%")

        return findings[:5]  # 返回最重要的5个发现

    def _identify_risk_warnings(self, auction: Dict, anomaly: Dict, trial: Dict, orders: Dict) -> List[str]:
        """识别风险警告"""
        warnings = []

        # 集合竞价风险
        auction_signals = auction.get('auction_signals', [])
        for signal in auction_signals:
            if '利空' in signal or '弱势' in signal:
                warnings.append(f"集合竞价风险: {signal}")

        # 异动风险
        anomaly_signals = anomaly.get('anomaly_signals', [])
        for signal in anomaly_signals:
            if '恐慌' in signal or '抛售' in signal:
                warnings.append(f"异动风险: {signal}")

        # 试盘风险
        trial_signals = trial.get('trial_signals', [])
        for signal in trial_signals:
            if '破位' in signal or '看空' in signal:
                warnings.append(f"试盘风险: {signal}")

        return warnings

    def _identify_opportunity_signals(self, auction: Dict, anomaly: Dict, trial: Dict, orders: Dict) -> List[str]:
        """发现机会信号"""
        opportunities = []

        # 集合竞价机会
        auction_signals = auction.get('auction_signals', [])
        for signal in auction_signals:
            if '利好' in signal or '强势' in signal:
                opportunities.append(f"集合竞价机会: {signal}")

        # 异动机会
        anomaly_signals = anomaly.get('anomaly_signals', [])
        for signal in anomaly_signals:
            if '主力' in signal and '介入' in signal:
                opportunities.append(f"异动机会: {signal}")

        # 试盘机会
        trial_signals = trial.get('trial_signals', [])
        for signal in trial_signals:
            if '看多' in signal or '突破' in signal:
                opportunities.append(f"试盘机会: {signal}")

        # 大单机会
        order_signals = orders.get('order_signals', [])
        for signal in order_signals:
            if '主力' in signal or '资金介入' in signal:
                opportunities.append(f"大单机会: {signal}")

        return opportunities

    def _generate_operation_suggestions_comprehensive(self, score: int, findings: List,
                                                    warnings: List, opportunities: List) -> List[str]:
        """生成操作建议"""
        suggestions = []

        # 基于综合评分的建议
        if score >= 80:
            suggestions.append("综合评分较高，可以考虑积极关注")
        elif score >= 60:
            suggestions.append("综合评分中等，建议谨慎观察")
        elif score >= 40:
            suggestions.append("综合评分偏低，建议保持观望")
        else:
            suggestions.append("综合评分较低，建议规避风险")

        # 基于风险警告的建议
        if len(warnings) >= 2:
            suggestions.append("风险信号较多，建议降低仓位或观望")
        elif len(warnings) == 1:
            suggestions.append("存在风险信号，建议控制仓位")

        # 基于机会信号的建议
        if len(opportunities) >= 2:
            suggestions.append("机会信号较多，可以考虑适当参与")
        elif len(opportunities) == 1:
            suggestions.append("存在机会信号，可以小仓位试探")

        # 基于关键发现的建议
        for finding in findings:
            if '巨量' in finding or '大幅' in finding:
                suggestions.append("出现重要信号，建议密切关注后续走势")
                break

        return suggestions[:3]  # 返回最重要的3个建议


# 创建全局实例
opening_analyzer = OpeningFeaturesAnalyzer()


def test_opening_features_analyzer():
    """测试开盘特征分析器"""
    print("🚀 测试开盘特征分析器")
    print("=" * 60)

    # 测试股票代码
    test_codes = ["000001", "601519", "300229"]

    for stock_code in test_codes:
        print(f"\n📊 测试股票: {stock_code}")
        print("-" * 40)

        try:
            result = opening_analyzer.get_opening_features_analysis(stock_code)

            if result.get('data_available', False):
                print(f"✅ 开盘特征分析成功")

                # 显示综合评估
                assessment = result.get('comprehensive_assessment', {})
                if assessment:
                    print(f"📋 综合评估:")
                    print(f"  综合评分: {assessment.get('overall_score', 0)}/100")

                    key_findings = assessment.get('key_findings', [])
                    if key_findings:
                        print(f"  关键发现:")
                        for finding in key_findings[:3]:
                            print(f"    • {finding}")

                    opportunities = assessment.get('opportunity_signals', [])
                    if opportunities:
                        print(f"  机会信号:")
                        for opportunity in opportunities[:2]:
                            print(f"    • {opportunity}")

                    warnings = assessment.get('risk_warnings', [])
                    if warnings:
                        print(f"  风险警告:")
                        for warning in warnings[:2]:
                            print(f"    • {warning}")

                    suggestions = assessment.get('operation_suggestions', [])
                    if suggestions:
                        print(f"  操作建议:")
                        for suggestion in suggestions[:2]:
                            print(f"    • {suggestion}")

                print(f"✅ {stock_code} 分析完成")
            else:
                print(f"❌ {stock_code} 分析失败: {result.get('error', '未知错误')}")

        except Exception as e:
            print(f"❌ {stock_code} 测试失败: {str(e)}")

    print(f"\n🎯 开盘特征分析器测试完成")


if __name__ == "__main__":
    test_opening_features_analyzer()
