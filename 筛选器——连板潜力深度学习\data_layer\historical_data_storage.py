#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据存储系统
建立本地历史数据存储系统，存储连板股的历史数据供分析使用
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import threading
from typing import Dict, List, Any, Optional, Tuple
import sqlite3
import os
import json
import pickle
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HistoricalDataStorage:
    """历史数据存储系统"""
    
    def __init__(self, db_path: str = "historical_data.db", data_dir: str = "historical_data"):
        self.db_path = db_path
        self.data_dir = data_dir

        # 创建数据目录
        os.makedirs(data_dir, exist_ok=True)

        # 线程安全的数据库连接
        self.conn = None
        self._thread_local = threading.local()
        self._lock = threading.Lock()
        
        # 表结构配置
        self.table_schemas = {
            'limit_up_stocks': '''
                CREATE TABLE IF NOT EXISTS limit_up_stocks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT NOT NULL,
                    limit_up_date DATE NOT NULL,
                    close_price REAL,
                    change_pct REAL,
                    turnover_rate REAL,
                    volume REAL,
                    amount REAL,
                    consecutive_days INTEGER DEFAULT 1,
                    reason TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, limit_up_date)
                )
            ''',
            'stock_kline_data': '''
                CREATE TABLE IF NOT EXISTS stock_kline_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date DATE NOT NULL,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume REAL,
                    amount REAL,
                    change_pct REAL,
                    turnover_rate REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, trade_date)
                )
            ''',
            'stock_minute_data': '''
                CREATE TABLE IF NOT EXISTS stock_minute_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date DATE NOT NULL,
                    trade_time TIME NOT NULL,
                    open_price REAL,
                    high_price REAL,
                    low_price REAL,
                    close_price REAL,
                    volume REAL,
                    amount REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, trade_date, trade_time)
                )
            ''',
            'capital_flow_data': '''
                CREATE TABLE IF NOT EXISTS capital_flow_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    trade_date DATE NOT NULL,
                    main_net_inflow REAL,
                    main_net_inflow_ratio REAL,
                    super_large_net_inflow REAL,
                    large_net_inflow REAL,
                    medium_net_inflow REAL,
                    small_net_inflow REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, trade_date)
                )
            ''',
            'stock_features': '''
                CREATE TABLE IF NOT EXISTS stock_features (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    feature_date DATE NOT NULL,
                    features_json TEXT NOT NULL,
                    feature_count INTEGER,
                    label INTEGER,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, feature_date)
                )
            '''
        }
        
        # 初始化数据库
        self._init_database()
        
        logger.info("💾 历史数据存储系统初始化完成")

    def get_connection(self):
        """获取线程安全的数据库连接"""
        try:
            # 检查当前线程是否已有连接
            if not hasattr(self._thread_local, 'conn') or self._thread_local.conn is None:
                self._thread_local.conn = sqlite3.connect(self.db_path, check_same_thread=False)
                self._thread_local.conn.execute("PRAGMA foreign_keys = ON")

            return self._thread_local.conn

        except Exception as e:
            logger.error(f"获取数据库连接失败: {e}")
            return None

    def _init_database(self):
        """初始化数据库"""
        try:
            self.conn = sqlite3.connect(self.db_path, check_same_thread=False)
            self.conn.execute("PRAGMA foreign_keys = ON")

            # 创建表
            for table_name, schema in self.table_schemas.items():
                self.conn.execute(schema)
                logger.info(f"✅ 创建/验证表: {table_name}")

            self.conn.commit()

            # 创建索引
            self._create_indexes()
            
        except Exception as e:
            logger.error(f"初始化数据库失败: {e}")
            raise
    
    def _create_indexes(self):
        """创建数据库索引"""
        try:
            indexes = [
                "CREATE INDEX IF NOT EXISTS idx_limit_up_date ON limit_up_stocks(limit_up_date)",
                "CREATE INDEX IF NOT EXISTS idx_limit_up_code ON limit_up_stocks(stock_code)",
                "CREATE INDEX IF NOT EXISTS idx_kline_code_date ON stock_kline_data(stock_code, trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_minute_code_date ON stock_minute_data(stock_code, trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_capital_code_date ON capital_flow_data(stock_code, trade_date)",
                "CREATE INDEX IF NOT EXISTS idx_features_code_date ON stock_features(stock_code, feature_date)"
            ]
            
            for index_sql in indexes:
                self.conn.execute(index_sql)
            
            self.conn.commit()
            logger.info("✅ 数据库索引创建完成")
            
        except Exception as e:
            logger.error(f"创建索引失败: {e}")
    
    def store_limit_up_stocks(self, limit_up_data: List[Dict[str, Any]]) -> int:
        """存储涨停股票数据"""
        try:
            if not limit_up_data:
                return 0
            
            stored_count = 0
            
            for stock_data in limit_up_data:
                try:
                    sql = '''
                        INSERT OR REPLACE INTO limit_up_stocks 
                        (stock_code, stock_name, limit_up_date, close_price, change_pct, 
                         turnover_rate, volume, amount, consecutive_days, reason)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    '''
                    
                    values = (
                        stock_data.get('stock_code', ''),
                        stock_data.get('stock_name', ''),
                        stock_data.get('limit_up_date', ''),
                        stock_data.get('close_price', 0),
                        stock_data.get('change_pct', 0),
                        stock_data.get('turnover_rate', 0),
                        stock_data.get('volume', 0),
                        stock_data.get('amount', 0),
                        stock_data.get('consecutive_days', 1),
                        stock_data.get('reason', '')
                    )
                    
                    conn = self.get_connection()
                    if conn:
                        conn.execute(sql, values)
                        stored_count += 1

                except Exception as e:
                    logger.warning(f"存储涨停股票{stock_data.get('stock_code', 'unknown')}失败: {e}")
                    continue

            conn = self.get_connection()
            if conn:
                conn.commit()
            logger.info(f"✅ 存储涨停股票数据完成: {stored_count}/{len(limit_up_data)}")
            return stored_count
            
        except Exception as e:
            logger.error(f"存储涨停股票数据失败: {e}")
            return 0
    
    def store_kline_data(self, stock_code: str, kline_data: pd.DataFrame) -> int:
        """存储K线数据"""
        try:
            if kline_data.empty:
                return 0
            
            stored_count = 0
            
            for _, row in kline_data.iterrows():
                try:
                    sql = '''
                        INSERT OR REPLACE INTO stock_kline_data 
                        (stock_code, trade_date, open_price, high_price, low_price, close_price,
                         volume, amount, change_pct, turnover_rate)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                    '''
                    
                    # 处理日期格式，确保兼容SQLite
                    trade_date = row.get('date', '')
                    if hasattr(trade_date, 'strftime'):
                        # 如果是pandas Timestamp或datetime对象，转换为字符串
                        trade_date = trade_date.strftime('%Y-%m-%d')
                    elif hasattr(trade_date, 'date'):
                        # 如果是datetime对象，获取日期部分
                        trade_date = trade_date.date().strftime('%Y-%m-%d')
                    else:
                        # 如果已经是字符串，直接使用
                        trade_date = str(trade_date)

                    values = (
                        stock_code,
                        trade_date,
                        float(row.get('open', 0)) if row.get('open') is not None else 0,
                        float(row.get('high', 0)) if row.get('high') is not None else 0,
                        float(row.get('low', 0)) if row.get('low') is not None else 0,
                        float(row.get('close', 0)) if row.get('close') is not None else 0,
                        float(row.get('volume', 0)) if row.get('volume') is not None else 0,
                        float(row.get('amount', 0)) if row.get('amount') is not None else 0,
                        float(row.get('change_pct', 0)) if row.get('change_pct') is not None else 0,
                        float(row.get('turnover_rate', 0)) if row.get('turnover_rate') is not None else 0
                    )
                    
                    self.conn.execute(sql, values)
                    stored_count += 1
                    
                except Exception as e:
                    logger.warning(f"存储K线数据失败: {e}")
                    continue
            
            self.conn.commit()
            logger.info(f"✅ 存储{stock_code}的K线数据完成: {stored_count}条")
            return stored_count
            
        except Exception as e:
            logger.error(f"存储{stock_code}的K线数据失败: {e}")
            return 0
    
    def store_minute_data(self, stock_code: str, trade_date: str, minute_data: pd.DataFrame) -> int:
        """存储分时数据"""
        try:
            if minute_data.empty:
                return 0
            
            stored_count = 0
            
            for _, row in minute_data.iterrows():
                try:
                    sql = '''
                        INSERT OR REPLACE INTO stock_minute_data 
                        (stock_code, trade_date, trade_time, open_price, high_price, low_price, 
                         close_price, volume, amount)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                    '''
                    
                    # 提取时间，处理各种时间格式
                    trade_time = row.get('time', '09:30')
                    if hasattr(trade_time, 'strftime'):
                        # 如果是datetime对象，提取时间部分
                        trade_time = trade_time.strftime('%H:%M')
                    elif isinstance(trade_time, str) and ':' not in trade_time:
                        trade_time = '09:30'
                    else:
                        trade_time = str(trade_time)

                    values = (
                        stock_code,
                        trade_date,
                        trade_time,
                        float(row.get('open', 0)) if row.get('open') is not None else 0,
                        float(row.get('high', 0)) if row.get('high') is not None else 0,
                        float(row.get('low', 0)) if row.get('low') is not None else 0,
                        float(row.get('close', 0)) if row.get('close') is not None else 0,
                        float(row.get('volume', 0)) if row.get('volume') is not None else 0,
                        float(row.get('amount', 0)) if row.get('amount') is not None else 0
                    )
                    
                    self.conn.execute(sql, values)
                    stored_count += 1
                    
                except Exception as e:
                    logger.warning(f"存储分时数据失败: {e}")
                    continue
            
            self.conn.commit()
            logger.info(f"✅ 存储{stock_code}在{trade_date}的分时数据完成: {stored_count}条")
            return stored_count
            
        except Exception as e:
            logger.error(f"存储{stock_code}的分时数据失败: {e}")
            return 0
    
    def store_capital_flow_data(self, stock_code: str, capital_data: pd.DataFrame) -> int:
        """存储资金流向数据"""
        try:
            if capital_data.empty:
                return 0
            
            stored_count = 0
            
            for _, row in capital_data.iterrows():
                try:
                    sql = '''
                        INSERT OR REPLACE INTO capital_flow_data 
                        (stock_code, trade_date, main_net_inflow, main_net_inflow_ratio,
                         super_large_net_inflow, large_net_inflow, medium_net_inflow, small_net_inflow)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
                    '''
                    
                    # 处理日期格式
                    trade_date = row.get('日期', '')
                    if hasattr(trade_date, 'strftime'):
                        trade_date = trade_date.strftime('%Y-%m-%d')
                    elif hasattr(trade_date, 'date'):
                        trade_date = trade_date.date().strftime('%Y-%m-%d')
                    else:
                        trade_date = str(trade_date)

                    values = (
                        stock_code,
                        trade_date,
                        float(row.get('主力净流入-净额', 0)) if row.get('主力净流入-净额') is not None else 0,
                        float(row.get('主力净流入-净占比', 0)) if row.get('主力净流入-净占比') is not None else 0,
                        float(row.get('超大单净流入-净额', 0)) if row.get('超大单净流入-净额') is not None else 0,
                        float(row.get('大单净流入-净额', 0)) if row.get('大单净流入-净额') is not None else 0,
                        float(row.get('中单净流入-净额', 0)) if row.get('中单净流入-净额') is not None else 0,
                        float(row.get('小单净流入-净额', 0)) if row.get('小单净流入-净额') is not None else 0
                    )
                    
                    self.conn.execute(sql, values)
                    stored_count += 1
                    
                except Exception as e:
                    logger.warning(f"存储资金流向数据失败: {e}")
                    continue
            
            self.conn.commit()
            logger.info(f"✅ 存储{stock_code}的资金流向数据完成: {stored_count}条")
            return stored_count
            
        except Exception as e:
            logger.error(f"存储{stock_code}的资金流向数据失败: {e}")
            return 0
    
    def store_stock_features(self, stock_code: str, feature_date: str, 
                           features: Dict[str, Any], label: Optional[int] = None) -> bool:
        """存储股票特征数据"""
        try:
            sql = '''
                INSERT OR REPLACE INTO stock_features 
                (stock_code, feature_date, features_json, feature_count, label)
                VALUES (?, ?, ?, ?, ?)
            '''
            
            features_json = json.dumps(features, ensure_ascii=False)
            feature_count = len(features)
            
            values = (stock_code, feature_date, features_json, feature_count, label)
            
            self.conn.execute(sql, values)
            self.conn.commit()
            
            logger.info(f"✅ 存储{stock_code}在{feature_date}的特征数据完成: {feature_count}个特征")
            return True
            
        except Exception as e:
            logger.error(f"存储{stock_code}的特征数据失败: {e}")
            return False

    def get_limit_up_stocks(self, start_date: str = None, end_date: str = None,
                           stock_code: str = None) -> pd.DataFrame:
        """查询涨停股票数据"""
        try:
            sql = "SELECT * FROM limit_up_stocks WHERE 1=1"
            params = []

            if start_date:
                sql += " AND limit_up_date >= ?"
                params.append(start_date)

            if end_date:
                sql += " AND limit_up_date <= ?"
                params.append(end_date)

            if stock_code:
                sql += " AND stock_code = ?"
                params.append(stock_code)

            sql += " ORDER BY limit_up_date DESC, stock_code"

            df = pd.read_sql_query(sql, self.conn, params=params)
            logger.info(f"✅ 查询涨停股票数据完成: {len(df)}条记录")
            return df

        except Exception as e:
            logger.error(f"查询涨停股票数据失败: {e}")
            return pd.DataFrame()

    def get_kline_data(self, stock_code: str, start_date: str = None,
                      end_date: str = None) -> pd.DataFrame:
        """查询K线数据"""
        try:
            sql = "SELECT * FROM stock_kline_data WHERE stock_code = ?"
            params = [stock_code]

            if start_date:
                sql += " AND trade_date >= ?"
                params.append(start_date)

            if end_date:
                sql += " AND trade_date <= ?"
                params.append(end_date)

            sql += " ORDER BY trade_date"

            df = pd.read_sql_query(sql, self.conn, params=params)
            logger.info(f"✅ 查询{stock_code}的K线数据完成: {len(df)}条记录")
            return df

        except Exception as e:
            logger.error(f"查询{stock_code}的K线数据失败: {e}")
            return pd.DataFrame()

    def get_minute_data(self, stock_code: str, trade_date: str) -> pd.DataFrame:
        """查询分时数据"""
        try:
            sql = '''
                SELECT * FROM stock_minute_data
                WHERE stock_code = ? AND trade_date = ?
                ORDER BY trade_time
            '''

            df = pd.read_sql_query(sql, self.conn, params=[stock_code, trade_date])
            logger.info(f"✅ 查询{stock_code}在{trade_date}的分时数据完成: {len(df)}条记录")
            return df

        except Exception as e:
            logger.error(f"查询{stock_code}的分时数据失败: {e}")
            return pd.DataFrame()

    def get_capital_flow_data(self, stock_code: str, start_date: str = None,
                            end_date: str = None) -> pd.DataFrame:
        """查询资金流向数据"""
        try:
            sql = "SELECT * FROM capital_flow_data WHERE stock_code = ?"
            params = [stock_code]

            if start_date:
                sql += " AND trade_date >= ?"
                params.append(start_date)

            if end_date:
                sql += " AND trade_date <= ?"
                params.append(end_date)

            sql += " ORDER BY trade_date"

            df = pd.read_sql_query(sql, self.conn, params=params)
            logger.info(f"✅ 查询{stock_code}的资金流向数据完成: {len(df)}条记录")
            return df

        except Exception as e:
            logger.error(f"查询{stock_code}的资金流向数据失败: {e}")
            return pd.DataFrame()

    def get_stock_features(self, stock_code: str = None, feature_date: str = None,
                          start_date: str = None, end_date: str = None) -> List[Dict[str, Any]]:
        """查询股票特征数据"""
        try:
            sql = "SELECT * FROM stock_features WHERE 1=1"
            params = []

            if stock_code:
                sql += " AND stock_code = ?"
                params.append(stock_code)

            if feature_date:
                sql += " AND feature_date = ?"
                params.append(feature_date)

            if start_date:
                sql += " AND feature_date >= ?"
                params.append(start_date)

            if end_date:
                sql += " AND feature_date <= ?"
                params.append(end_date)

            sql += " ORDER BY feature_date DESC, stock_code"

            cursor = self.conn.execute(sql, params)
            results = []

            for row in cursor.fetchall():
                features_dict = json.loads(row[3])  # features_json
                result = {
                    'stock_code': row[1],
                    'feature_date': row[2],
                    'features': features_dict,
                    'feature_count': row[4],
                    'label': row[5]
                }
                results.append(result)

            logger.info(f"✅ 查询股票特征数据完成: {len(results)}条记录")
            return results

        except Exception as e:
            logger.error(f"查询股票特征数据失败: {e}")
            return []

    def get_training_dataset(self, start_date: str = None, end_date: str = None,
                           min_features: int = 50) -> Tuple[List[Dict[str, Any]], List[int]]:
        """获取训练数据集"""
        try:
            sql = '''
                SELECT stock_code, feature_date, features_json, label
                FROM stock_features
                WHERE label IS NOT NULL AND feature_count >= ?
            '''
            params = [min_features]

            if start_date:
                sql += " AND feature_date >= ?"
                params.append(start_date)

            if end_date:
                sql += " AND feature_date <= ?"
                params.append(end_date)

            sql += " ORDER BY feature_date, stock_code"

            cursor = self.conn.execute(sql, params)

            features_list = []
            labels_list = []

            for row in cursor.fetchall():
                features_dict = json.loads(row[2])  # features_json
                label = row[3]  # label

                features_list.append(features_dict)
                labels_list.append(label)

            logger.info(f"✅ 获取训练数据集完成: {len(features_list)}个样本")
            return features_list, labels_list

        except Exception as e:
            logger.error(f"获取训练数据集失败: {e}")
            return [], []

    def get_database_statistics(self) -> Dict[str, Any]:
        """获取数据库统计信息"""
        try:
            stats = {}

            # 涨停股票统计
            cursor = self.conn.execute("SELECT COUNT(*) FROM limit_up_stocks")
            stats['limit_up_stocks_count'] = cursor.fetchone()[0]

            cursor = self.conn.execute("SELECT COUNT(DISTINCT stock_code) FROM limit_up_stocks")
            stats['unique_limit_up_stocks'] = cursor.fetchone()[0]

            cursor = self.conn.execute("SELECT MIN(limit_up_date), MAX(limit_up_date) FROM limit_up_stocks")
            date_range = cursor.fetchone()
            stats['limit_up_date_range'] = f"{date_range[0]} - {date_range[1]}"

            # K线数据统计
            cursor = self.conn.execute("SELECT COUNT(*) FROM stock_kline_data")
            stats['kline_records_count'] = cursor.fetchone()[0]

            cursor = self.conn.execute("SELECT COUNT(DISTINCT stock_code) FROM stock_kline_data")
            stats['kline_stocks_count'] = cursor.fetchone()[0]

            # 分时数据统计
            cursor = self.conn.execute("SELECT COUNT(*) FROM stock_minute_data")
            stats['minute_records_count'] = cursor.fetchone()[0]

            # 资金流向数据统计
            cursor = self.conn.execute("SELECT COUNT(*) FROM capital_flow_data")
            stats['capital_flow_records_count'] = cursor.fetchone()[0]

            # 特征数据统计
            cursor = self.conn.execute("SELECT COUNT(*) FROM stock_features")
            stats['features_records_count'] = cursor.fetchone()[0]

            cursor = self.conn.execute("SELECT COUNT(*) FROM stock_features WHERE label IS NOT NULL")
            stats['labeled_features_count'] = cursor.fetchone()[0]

            logger.info(f"✅ 数据库统计信息获取完成")
            return stats

        except Exception as e:
            logger.error(f"获取数据库统计信息失败: {e}")
            return {}

    def cleanup_old_data(self, days_to_keep: int = 365) -> Dict[str, int]:
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')

            cleanup_stats = {}

            # 清理旧的K线数据
            cursor = self.conn.execute("DELETE FROM stock_kline_data WHERE trade_date < ?", [cutoff_date])
            cleanup_stats['kline_deleted'] = cursor.rowcount

            # 清理旧的分时数据
            cursor = self.conn.execute("DELETE FROM stock_minute_data WHERE trade_date < ?", [cutoff_date])
            cleanup_stats['minute_deleted'] = cursor.rowcount

            # 清理旧的资金流向数据
            cursor = self.conn.execute("DELETE FROM capital_flow_data WHERE trade_date < ?", [cutoff_date])
            cleanup_stats['capital_flow_deleted'] = cursor.rowcount

            # 清理旧的特征数据
            cursor = self.conn.execute("DELETE FROM stock_features WHERE feature_date < ?", [cutoff_date])
            cleanup_stats['features_deleted'] = cursor.rowcount

            self.conn.commit()

            # 优化数据库
            self.conn.execute("VACUUM")

            logger.info(f"✅ 数据清理完成: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return {}

    def close(self):
        """关闭数据库连接"""
        try:
            if self.conn:
                self.conn.close()
                logger.info("✅ 数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭数据库连接失败: {e}")

    def __del__(self):
        """析构函数"""
        self.close()

def main():
    """测试历史数据存储系统"""
    print("💾 测试历史数据存储系统...")

    storage = HistoricalDataStorage()

    # 测试存储涨停股票数据
    print("\n🔄 测试存储涨停股票数据...")
    test_limit_up_data = [
        {
            'stock_code': '000001',
            'stock_name': '平安银行',
            'limit_up_date': '2024-07-10',
            'close_price': 12.50,
            'change_pct': 10.0,
            'turnover_rate': 3.5,
            'volume': 50000000,
            'amount': 625000000,
            'consecutive_days': 1,
            'reason': '金融板块上涨'
        },
        {
            'stock_code': '000002',
            'stock_name': '万科A',
            'limit_up_date': '2024-07-10',
            'close_price': 8.80,
            'change_pct': 10.0,
            'turnover_rate': 5.2,
            'volume': 80000000,
            'amount': 704000000,
            'consecutive_days': 2,
            'reason': '地产板块反弹'
        }
    ]

    stored_count = storage.store_limit_up_stocks(test_limit_up_data)
    print(f"✅ 存储涨停股票数据: {stored_count}条")

    # 测试存储K线数据
    print("\n🔄 测试存储K线数据...")
    test_kline_data = pd.DataFrame({
        'date': ['2024-07-08', '2024-07-09', '2024-07-10'],
        'open': [11.20, 11.50, 11.80],
        'high': [11.60, 11.90, 12.50],
        'low': [11.10, 11.40, 11.70],
        'close': [11.50, 11.80, 12.50],
        'volume': [30000000, 40000000, 50000000],
        'amount': [345000000, 472000000, 625000000],
        'change_pct': [2.68, 2.61, 5.93],
        'turnover_rate': [2.1, 2.8, 3.5]
    })

    stored_count = storage.store_kline_data('000001', test_kline_data)
    print(f"✅ 存储K线数据: {stored_count}条")

    # 测试存储特征数据
    print("\n🔄 测试存储特征数据...")
    test_features = {
        'morning_volume_active': 1,
        'active_volume_pattern': 1,
        'near_recent_high': 1,
        'ma5_above_ma10': 1,
        'macd_golden_cross': 0,
        'rsi_bullish_zone': 1,
        'main_inflow_positive': 1,
        'technical_signal_score': 0.75,
        'capital_flow_score': 0.68
    }

    success = storage.store_stock_features('000001', '2024-07-09', test_features, label=1)
    print(f"✅ 存储特征数据: {'成功' if success else '失败'}")

    # 测试查询功能
    print("\n🔍 测试查询功能...")

    # 查询涨停股票
    limit_up_df = storage.get_limit_up_stocks(start_date='2024-07-01')
    print(f"查询涨停股票: {len(limit_up_df)}条记录")

    # 查询K线数据
    kline_df = storage.get_kline_data('000001', start_date='2024-07-01')
    print(f"查询K线数据: {len(kline_df)}条记录")

    # 查询特征数据
    features_list, labels_list = storage.get_training_dataset(min_features=5)
    print(f"查询训练数据集: {len(features_list)}个样本")

    # 获取数据库统计
    print("\n📊 数据库统计信息:")
    stats = storage.get_database_statistics()
    for key, value in stats.items():
        print(f"  {key}: {value}")

    # 关闭连接
    storage.close()

if __name__ == "__main__":
    main()
