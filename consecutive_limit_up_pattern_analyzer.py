#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板潜力股特征模式分析器
深度分析连板股票在首次涨停前的共同特征和信号
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Any
import akshare as ak
from enhanced_data_fetcher import EnhancedStockDataFetcher
from youzi_data_processor import YouziDataProcessor
from youzi_seats_manager import YouziSeatsManager
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ConsecutiveLimitUpPatternAnalyzer:
    """连板潜力股特征模式分析器"""
    
    def __init__(self):
        self.data_fetcher = EnhancedStockDataFetcher()

        # 初始化游资席位管理器和处理器
        self.youzi_seats_manager = YouziSeatsManager()
        self.youzi_processor = YouziDataProcessor(self.youzi_seats_manager)
        
        # 样本股票数据
        self.sample_stocks = [
            ('000010', '2024-07-09'),
            ('600604', '2024-07-11'),
            ('601908', '2024-07-10'),
            ('605399', '2024-07-10'),
            ('605389', '2024-06-30'),
            ('002209', '2024-07-11'),
            ('002121', '2024-07-09'),
            ('002633', '2024-07-10'),
            ('600962', '2024-07-07'),
            ('605122', '2024-07-07'),
            ('000607', '2024-07-08'),
            ('603856', '2024-07-10'),
            ('000635', '2024-07-10'),
            ('002218', '2024-07-08'),
            ('002636', '2024-07-03'),
            ('000655', '2024-07-08'),
            ('002951', '2024-07-07'),
            ('605018', '2024-07-09'),
            ('605500', '2024-07-03'),
            ('000920', '2024-07-07'),
            ('002900', '2024-07-08'),
            ('603716', '2024-07-01'),
            ('000601', '2024-07-04'),
            ('000037', '2024-07-04'),
            ('605162', '2024-07-04')
        ]
        
        # 分析维度配置
        self.analysis_dimensions = {
            'pre_limit_days': 5,  # 涨停前分析天数
            'volume_analysis_days': 10,  # 成交量分析天数
            'technical_analysis_days': 20,  # 技术指标分析天数
            'youzi_analysis_months': 6  # 游资分析月数
        }
        
        # 特征统计结果
        self.pattern_results = {}
        
    def analyze_all_patterns(self) -> Dict[str, Any]:
        """分析所有样本股票的共同特征模式"""
        logger.info("🚀 开始连板潜力股特征模式深度分析...")
        
        # 存储所有股票的特征数据
        all_features = []
        
        for i, (stock_code, limit_up_date) in enumerate(self.sample_stocks, 1):
            logger.info(f"📊 分析第{i}/25只股票: {stock_code} (涨停日期: {limit_up_date})")
            
            try:
                # 获取单只股票的特征
                stock_features = self._analyze_single_stock_features(stock_code, limit_up_date)
                if stock_features:
                    stock_features['stock_code'] = stock_code
                    stock_features['limit_up_date'] = limit_up_date
                    all_features.append(stock_features)
                    logger.info(f"✅ {stock_code} 特征提取完成")
                else:
                    logger.warning(f"⚠️ {stock_code} 特征提取失败")
                    
            except Exception as e:
                logger.error(f"❌ {stock_code} 分析出错: {e}")
                continue
        
        if not all_features:
            logger.error("❌ 没有成功分析的股票数据")
            return {}
            
        logger.info(f"✅ 成功分析了 {len(all_features)}/25 只股票")
        
        # 统计共同特征模式
        pattern_analysis = self._extract_common_patterns(all_features)
        
        # 生成分析报告
        report = self._generate_analysis_report(pattern_analysis, all_features)
        
        return {
            'total_analyzed': len(all_features),
            'pattern_analysis': pattern_analysis,
            'detailed_report': report,
            'raw_features': all_features
        }
    
    def _analyze_single_stock_features(self, stock_code: str, limit_up_date: str) -> Dict[str, Any]:
        """分析单只股票的特征"""
        try:
            # 转换日期格式
            limit_date = datetime.strptime(limit_up_date, '%Y-%m-%d')
            
            # 计算分析时间范围
            analysis_start = limit_date - timedelta(days=self.analysis_dimensions['technical_analysis_days'])
            analysis_end = limit_date - timedelta(days=1)  # 涨停前一天
            
            features = {
                'stock_code': stock_code,
                'limit_up_date': limit_up_date,
                'analysis_success': False
            }
            
            # 1. 获取历史K线数据
            kline_data = self._get_historical_kline_data(stock_code, analysis_start, limit_date)
            if kline_data is None or kline_data.empty:
                logger.warning(f"{stock_code}: 无法获取K线数据")
                return features
            
            # 2. 技术指标特征分析
            technical_features = self._analyze_technical_features(kline_data, limit_date)
            features.update(technical_features)
            
            # 3. 成交量特征分析
            volume_features = self._analyze_volume_features(kline_data, limit_date)
            features.update(volume_features)
            
            # 4. 价格形态特征分析
            price_features = self._analyze_price_pattern_features(kline_data, limit_date)
            features.update(price_features)
            
            # 5. 游资活动特征分析
            youzi_features = self._analyze_youzi_activity_features(stock_code, limit_date)
            features.update(youzi_features)
            
            # 6. 涨停前分时特征分析
            minute_features = self._analyze_pre_limit_minute_features(stock_code, limit_date)
            features.update(minute_features)
            
            features['analysis_success'] = True
            return features
            
        except Exception as e:
            logger.error(f"{stock_code} 特征分析失败: {e}")
            return {'stock_code': stock_code, 'analysis_success': False, 'error': str(e)}
    
    def _get_historical_kline_data(self, stock_code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """获取历史K线数据"""
        try:
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')
            
            df = ak.stock_zh_a_hist(
                symbol=stock_code,
                start_date=start_str,
                end_date=end_str,
                adjust="qfq"
            )
            
            if df.empty:
                return pd.DataFrame()
            
            # 标准化列名
            df = df.rename(columns={
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '换手率': 'turnover_rate'
            })
            
            df['date'] = pd.to_datetime(df['date'])
            df = df.sort_values('date').reset_index(drop=True)
            
            return df
            
        except Exception as e:
            logger.error(f"获取{stock_code}历史数据失败: {e}")
            return pd.DataFrame()

    def _analyze_technical_features(self, kline_data: pd.DataFrame, limit_date: datetime) -> Dict[str, Any]:
        """分析技术指标特征"""
        features = {}

        try:
            if len(kline_data) < 20:
                return features

            # 计算技术指标
            close_prices = kline_data['close'].values
            volumes = kline_data['volume'].values

            # 1. 均线特征
            ma5 = np.mean(close_prices[-5:]) if len(close_prices) >= 5 else 0
            ma10 = np.mean(close_prices[-10:]) if len(close_prices) >= 10 else 0
            ma20 = np.mean(close_prices[-20:]) if len(close_prices) >= 20 else 0

            current_price = close_prices[-1]
            features.update({
                'price_above_ma5': current_price > ma5 if ma5 > 0 else False,
                'price_above_ma10': current_price > ma10 if ma10 > 0 else False,
                'price_above_ma20': current_price > ma20 if ma20 > 0 else False,
                'ma5_above_ma10': ma5 > ma10 if ma5 > 0 and ma10 > 0 else False,
                'ma10_above_ma20': ma10 > ma20 if ma10 > 0 and ma20 > 0 else False,
                'ma_distance_ratio': (current_price - ma20) / ma20 if ma20 > 0 else 0
            })

            # 2. 价格波动特征
            price_changes = np.diff(close_prices[-10:]) if len(close_prices) >= 10 else []
            if len(price_changes) > 0:
                features.update({
                    'recent_volatility': np.std(price_changes),
                    'positive_days_ratio': np.sum(price_changes > 0) / len(price_changes),
                    'max_single_day_gain': np.max(price_changes) / close_prices[-10] if close_prices[-10] > 0 else 0
                })

            # 3. RSI特征
            if len(close_prices) >= 14:
                rsi = self._calculate_rsi(close_prices, 14)
                features['rsi_14'] = rsi
                features['rsi_oversold'] = rsi < 30
                features['rsi_moderate'] = 30 <= rsi <= 70

            return features

        except Exception as e:
            logger.error(f"技术指标分析失败: {e}")
            return features

    def _analyze_volume_features(self, kline_data: pd.DataFrame, limit_date: datetime) -> Dict[str, Any]:
        """分析成交量特征"""
        features = {}

        try:
            if len(kline_data) < 10:
                return features

            volumes = kline_data['volume'].values
            turnover_rates = kline_data.get('turnover_rate', pd.Series([0] * len(kline_data))).values

            # 1. 成交量放大特征
            recent_volume = np.mean(volumes[-3:]) if len(volumes) >= 3 else 0
            avg_volume = np.mean(volumes[-10:-3]) if len(volumes) >= 10 else 0

            if avg_volume > 0:
                features['volume_amplification'] = recent_volume / avg_volume
                features['volume_significant_increase'] = recent_volume / avg_volume > 2.0
            else:
                features['volume_amplification'] = 1.0
                features['volume_significant_increase'] = False

            # 2. 换手率特征
            if len(turnover_rates) > 0 and np.any(turnover_rates > 0):
                recent_turnover = np.mean(turnover_rates[-3:])
                avg_turnover = np.mean(turnover_rates[-10:-3]) if len(turnover_rates) >= 10 else recent_turnover

                features.update({
                    'recent_turnover_rate': recent_turnover,
                    'avg_turnover_rate': avg_turnover,
                    'turnover_acceleration': recent_turnover / avg_turnover if avg_turnover > 0 else 1.0,
                    'high_turnover': recent_turnover > 5.0,
                    'moderate_turnover': 2.0 <= recent_turnover <= 8.0
                })

            # 3. 量价配合特征
            if len(kline_data) >= 5:
                price_changes = np.diff(kline_data['close'].values[-6:])
                volume_changes = np.diff(volumes[-6:])

                if len(price_changes) > 0 and len(volume_changes) > 0:
                    # 计算量价相关性
                    correlation = np.corrcoef(price_changes, volume_changes)[0, 1] if not np.isnan(np.corrcoef(price_changes, volume_changes)[0, 1]) else 0
                    features['volume_price_correlation'] = correlation
                    features['volume_price_positive_correlation'] = correlation > 0.3

            return features

        except Exception as e:
            logger.error(f"成交量特征分析失败: {e}")
            return features

    def _analyze_price_pattern_features(self, kline_data: pd.DataFrame, limit_date: datetime) -> Dict[str, Any]:
        """分析价格形态特征"""
        features = {}

        try:
            if len(kline_data) < 5:
                return features

            # 获取最近几天的价格数据
            recent_data = kline_data.tail(10)

            # 1. 连续上涨特征
            close_prices = recent_data['close'].values
            consecutive_up_days = 0
            for i in range(len(close_prices) - 1, 0, -1):
                if close_prices[i] > close_prices[i-1]:
                    consecutive_up_days += 1
                else:
                    break

            features.update({
                'consecutive_up_days': consecutive_up_days,
                'has_consecutive_up': consecutive_up_days >= 2,
                'strong_consecutive_up': consecutive_up_days >= 3
            })

            # 2. 价格突破特征
            if len(close_prices) >= 10:
                recent_high = np.max(close_prices[-5:])
                previous_high = np.max(close_prices[-10:-5])
                current_price = close_prices[-1]

                features.update({
                    'price_breakthrough': recent_high > previous_high,
                    'near_recent_high': current_price / recent_high > 0.95 if recent_high > 0 else False,
                    'breakthrough_strength': (recent_high - previous_high) / previous_high if previous_high > 0 else 0
                })

            # 3. 价格形态特征
            highs = recent_data['high'].values
            lows = recent_data['low'].values

            if len(highs) >= 5 and len(lows) >= 5:
                # 计算价格波动范围收窄
                recent_range = np.mean(highs[-3:] - lows[-3:])
                previous_range = np.mean(highs[-6:-3] - lows[-6:-3])

                features.update({
                    'price_range_narrowing': recent_range < previous_range * 0.8 if previous_range > 0 else False,
                    'price_consolidation': recent_range / np.mean(close_prices[-3:]) < 0.03 if np.mean(close_prices[-3:]) > 0 else False
                })

            return features

        except Exception as e:
            logger.error(f"价格形态分析失败: {e}")
            return features

    def _analyze_youzi_activity_features(self, stock_code: str, limit_date: datetime) -> Dict[str, Any]:
        """分析游资活动特征"""
        features = {}

        try:
            # 1. 获取龙虎榜历史数据
            lhb_records = self._get_recent_lhb_records(stock_code, limit_date)

            features.update({
                'has_recent_lhb': len(lhb_records) > 0,
                'lhb_frequency': len(lhb_records),
                'lhb_within_week': any(
                    (limit_date - datetime.strptime(record.get('date', '1900-01-01'), '%Y-%m-%d')).days <= 7
                    for record in lhb_records if record.get('date')
                )
            })

            # 2. 游资席位质量分析
            if lhb_records:
                famous_seats = self._identify_famous_youzi_seats(lhb_records)
                features.update({
                    'has_famous_youzi': len(famous_seats) > 0,
                    'famous_youzi_count': len(famous_seats),
                    'youzi_quality_score': self._calculate_youzi_quality_score(famous_seats)
                })
            else:
                features.update({
                    'has_famous_youzi': False,
                    'famous_youzi_count': 0,
                    'youzi_quality_score': 0.0
                })

            # 3. 使用现有游资处理器获取评分
            try:
                youzi_score = self.youzi_processor.get_stock_youzi_score(stock_code)
                features['youzi_processor_score'] = youzi_score
                features['high_youzi_activity'] = youzi_score > 0.3
            except:
                features['youzi_processor_score'] = 0.0
                features['high_youzi_activity'] = False

            return features

        except Exception as e:
            logger.error(f"游资活动分析失败: {e}")
            return features

    def _analyze_pre_limit_minute_features(self, stock_code: str, limit_date: datetime) -> Dict[str, Any]:
        """分析涨停前分时特征 - 使用当前可获得的分时数据进行模式分析"""
        features = {}

        try:
            # 由于历史分时数据获取困难，我们使用当前可获得的分时数据来分析分时特征模式
            # 这样可以识别该股票的分时交易特征，虽然不是历史特定日期的数据
            minute_data_result = self.data_fetcher.get_recent_7days_minute_data(stock_code)

            if not minute_data_result.get('data_available', False):
                logger.warning(f"{stock_code}: 无法获取分时数据")
                return features

            # 使用最近可获得的交易日数据进行分时特征分析
            trading_days_data = minute_data_result.get('trading_days_data', [])

            if not trading_days_data:
                logger.warning(f"{stock_code}: 没有可用的分时数据")
                return features

            # 使用最近的交易日数据作为分析样本
            latest_day_data = trading_days_data[-1]  # 最新的交易日
            df = latest_day_data.get('minute_data', pd.DataFrame())

            if df.empty:
                logger.warning(f"{stock_code}: 分时数据为空")
                return features

            logger.info(f"{stock_code}: 使用{latest_day_data.get('date', 'unknown')}的分时数据进行特征分析")

            # 标准化列名 - 处理中文列名
            column_mapping = {
                '时间': 'time',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }

            # 重命名列
            for chinese_name, english_name in column_mapping.items():
                if chinese_name in df.columns:
                    df[english_name] = df[chinese_name]

            # 检查必要的列是否存在
            if 'volume' not in df.columns or 'close' not in df.columns:
                logger.warning(f"{stock_code}: 分时数据缺少必要列，可用列: {list(df.columns)}")
                return features

            # 1. 尾盘特征分析
            tail_data = df.tail(30)  # 最后30分钟
            if not tail_data.empty:
                tail_volume_ratio = tail_data['volume'].sum() / df['volume'].sum() if df['volume'].sum() > 0 else 0
                tail_price_change = (tail_data['close'].iloc[-1] - tail_data['close'].iloc[0]) / tail_data['close'].iloc[0] if len(tail_data) > 0 and tail_data['close'].iloc[0] > 0 else 0

                features.update({
                    'tail_volume_ratio': tail_volume_ratio,
                    'tail_price_surge': tail_price_change > 0.02,  # 尾盘涨幅超过2%
                    'tail_volume_concentration': tail_volume_ratio > 0.3
                })

            # 2. 早盘特征分析
            morning_data = df.head(60)  # 前60分钟
            if not morning_data.empty:
                morning_volume_ratio = morning_data['volume'].sum() / df['volume'].sum() if df['volume'].sum() > 0 else 0
                morning_high = morning_data['high'].max()
                daily_high = df['high'].max()

                features.update({
                    'morning_volume_ratio': morning_volume_ratio,
                    'morning_volume_active': morning_volume_ratio > 0.35,
                    'morning_price_strength': morning_high / daily_high if daily_high > 0 else 0
                })

            # 3. 分时波动特征
            if len(df) > 10:
                price_volatility = df['close'].std() / df['close'].mean() if df['close'].mean() > 0 else 0
                volume_volatility = df['volume'].std() / df['volume'].mean() if df['volume'].mean() > 0 else 0

                features.update({
                    'minute_price_volatility': price_volatility,
                    'minute_volume_volatility': volume_volatility,
                    'stable_price_pattern': price_volatility < 0.01,
                    'active_volume_pattern': volume_volatility > 1.0
                })

            return features

        except Exception as e:
            logger.error(f"分时特征分析失败: {e}")
            return features

    def _get_recent_lhb_records(self, stock_code: str, limit_date: datetime) -> List[Dict]:
        """获取最近的龙虎榜记录"""
        try:
            # 查询最近3个月的龙虎榜数据
            start_date = limit_date - timedelta(days=90)
            start_str = start_date.strftime('%Y%m%d')
            end_str = (limit_date - timedelta(days=1)).strftime('%Y%m%d')

            lhb_data = ak.stock_lhb_detail_em(start_date=start_str, end_date=end_str)

            if lhb_data.empty:
                return []

            # 筛选目标股票
            stock_lhb = lhb_data[lhb_data['代码'] == stock_code]

            records = []
            for _, row in stock_lhb.iterrows():
                records.append({
                    'date': row.get('交易日期', ''),
                    'reason': row.get('上榜原因', ''),
                    'seat_name': row.get('营业部名称', ''),
                    'buy_amount': row.get('买入金额', 0),
                    'sell_amount': row.get('卖出金额', 0)
                })

            return records

        except Exception as e:
            logger.error(f"获取龙虎榜记录失败: {e}")
            return []

    def _identify_famous_youzi_seats(self, lhb_records: List[Dict]) -> List[str]:
        """识别知名游资席位"""
        famous_keywords = [
            '中信证券', '华泰证券', '国泰君安', '招商证券', '中金公司',
            '东方财富', '方正证券', '光大证券', '海通证券', '申万宏源',
            '长江证券', '兴业证券', '中信建投', '国信证券', '广发证券',
            '银河证券', '东北证券', '西南证券', '东吴证券', '华西证券'
        ]

        famous_seats = []
        for record in lhb_records:
            seat_name = record.get('seat_name', '')
            for keyword in famous_keywords:
                if keyword in seat_name:
                    famous_seats.append(seat_name)
                    break

        return list(set(famous_seats))

    def _calculate_youzi_quality_score(self, famous_seats: List[str]) -> float:
        """计算游资质量评分"""
        if not famous_seats:
            return 0.0

        # 简单的质量评分逻辑
        base_score = min(len(famous_seats) * 0.2, 1.0)
        return base_score

    def _calculate_rsi(self, prices: np.ndarray, period: int = 14) -> float:
        """计算RSI指标"""
        try:
            if len(prices) < period + 1:
                return 50.0

            deltas = np.diff(prices)
            gains = np.where(deltas > 0, deltas, 0)
            losses = np.where(deltas < 0, -deltas, 0)

            avg_gain = np.mean(gains[-period:])
            avg_loss = np.mean(losses[-period:])

            if avg_loss == 0:
                return 100.0

            rs = avg_gain / avg_loss
            rsi = 100 - (100 / (1 + rs))

            return rsi

        except Exception:
            return 50.0

    def _extract_common_patterns(self, all_features: List[Dict]) -> Dict[str, Any]:
        """提取共同特征模式"""
        logger.info("🔍 开始提取共同特征模式...")

        if not all_features:
            return {}

        # 统计各特征的出现频率
        pattern_stats = {}

        # 布尔型特征统计
        boolean_features = [
            'price_above_ma5', 'price_above_ma10', 'price_above_ma20',
            'ma5_above_ma10', 'ma10_above_ma20', 'volume_significant_increase',
            'high_turnover', 'moderate_turnover', 'volume_price_positive_correlation',
            'has_consecutive_up', 'strong_consecutive_up', 'price_breakthrough',
            'near_recent_high', 'price_range_narrowing', 'price_consolidation',
            'has_recent_lhb', 'lhb_within_week', 'has_famous_youzi',
            'high_youzi_activity', 'tail_price_surge', 'tail_volume_concentration',
            'morning_volume_active', 'stable_price_pattern', 'active_volume_pattern'
        ]

        for feature in boolean_features:
            true_count = sum(1 for f in all_features if f.get(feature, False))
            pattern_stats[feature] = {
                'frequency': true_count / len(all_features),
                'count': true_count,
                'total': len(all_features),
                'significance': 'high' if true_count / len(all_features) > 0.7 else
                              'medium' if true_count / len(all_features) > 0.4 else 'low'
            }

        # 数值型特征统计
        numeric_features = [
            'volume_amplification', 'recent_turnover_rate', 'turnover_acceleration',
            'consecutive_up_days', 'youzi_quality_score', 'tail_volume_ratio',
            'morning_volume_ratio', 'minute_price_volatility'
        ]

        for feature in numeric_features:
            values = [f.get(feature, 0) for f in all_features if f.get(feature) is not None]
            if values:
                pattern_stats[feature] = {
                    'mean': np.mean(values),
                    'median': np.median(values),
                    'std': np.std(values),
                    'min': np.min(values),
                    'max': np.max(values),
                    'q25': np.percentile(values, 25),
                    'q75': np.percentile(values, 75)
                }

        # 识别高频特征（出现频率>70%的特征）
        high_frequency_features = {
            k: v for k, v in pattern_stats.items()
            if isinstance(v, dict) and v.get('significance') == 'high'
        }

        # 识别中频特征（出现频率40-70%的特征）
        medium_frequency_features = {
            k: v for k, v in pattern_stats.items()
            if isinstance(v, dict) and v.get('significance') == 'medium'
        }

        return {
            'total_samples': len(all_features),
            'all_pattern_stats': pattern_stats,
            'high_frequency_features': high_frequency_features,
            'medium_frequency_features': medium_frequency_features,
            'key_insights': self._generate_key_insights(pattern_stats)
        }

    def _generate_key_insights(self, pattern_stats: Dict) -> List[str]:
        """生成关键洞察"""
        insights = []

        # 技术面洞察
        if pattern_stats.get('price_above_ma5', {}).get('frequency', 0) > 0.7:
            insights.append("✅ 70%以上的连板股在涨停前价格位于5日均线之上")

        if pattern_stats.get('volume_significant_increase', {}).get('frequency', 0) > 0.6:
            insights.append("📈 60%以上的连板股在涨停前出现显著放量")

        if pattern_stats.get('has_consecutive_up', {}).get('frequency', 0) > 0.5:
            insights.append("🔥 50%以上的连板股在涨停前有连续上涨形态")

        # 资金面洞察
        if pattern_stats.get('high_turnover', {}).get('frequency', 0) > 0.4:
            insights.append("💰 40%以上的连板股在涨停前换手率较高")

        if pattern_stats.get('tail_volume_concentration', {}).get('frequency', 0) > 0.3:
            insights.append("⏰ 30%以上的连板股在涨停前一天尾盘成交量集中")

        # 游资面洞察
        if pattern_stats.get('has_recent_lhb', {}).get('frequency', 0) > 0.3:
            insights.append("🎯 30%以上的连板股在涨停前有龙虎榜记录")

        return insights

    def _generate_analysis_report(self, pattern_analysis: Dict, all_features: List[Dict]) -> str:
        """生成详细分析报告"""
        report = []

        report.append("=" * 80)
        report.append("🚀 连板潜力股特征模式深度分析报告")
        report.append("=" * 80)
        report.append(f"📊 分析样本: {pattern_analysis.get('total_samples', 0)}/25 只股票")
        report.append(f"📅 分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 关键洞察
        report.append("🔍 关键洞察:")
        for insight in pattern_analysis.get('key_insights', []):
            report.append(f"  {insight}")
        report.append("")

        # 高频特征分析
        high_freq = pattern_analysis.get('high_frequency_features', {})
        if high_freq:
            report.append("🔥 高频特征 (出现频率>70%):")
            for feature, stats in high_freq.items():
                report.append(f"  • {feature}: {stats['frequency']:.1%} ({stats['count']}/{stats['total']})")
            report.append("")

        # 中频特征分析
        medium_freq = pattern_analysis.get('medium_frequency_features', {})
        if medium_freq:
            report.append("📈 中频特征 (出现频率40-70%):")
            for feature, stats in medium_freq.items():
                report.append(f"  • {feature}: {stats['frequency']:.1%} ({stats['count']}/{stats['total']})")
            report.append("")

        # 数值特征统计
        report.append("📊 关键数值特征统计:")
        numeric_features = ['volume_amplification', 'recent_turnover_rate', 'consecutive_up_days']
        for feature in numeric_features:
            stats = pattern_analysis.get('all_pattern_stats', {}).get(feature, {})
            if stats and 'mean' in stats:
                report.append(f"  • {feature}:")
                report.append(f"    平均值: {stats['mean']:.2f}, 中位数: {stats['median']:.2f}")
                report.append(f"    范围: {stats['min']:.2f} - {stats['max']:.2f}")
        report.append("")

        # 成功案例展示
        report.append("💡 典型成功案例:")
        successful_cases = [f for f in all_features if f.get('analysis_success', False)][:5]
        for case in successful_cases:
            report.append(f"  • {case.get('stock_code', 'N/A')} ({case.get('limit_up_date', 'N/A')})")
            key_features = []
            if case.get('price_above_ma5', False):
                key_features.append("价格>MA5")
            if case.get('volume_significant_increase', False):
                key_features.append("显著放量")
            if case.get('has_consecutive_up', False):
                key_features.append("连续上涨")
            if case.get('has_recent_lhb', False):
                key_features.append("龙虎榜")
            report.append(f"    特征: {', '.join(key_features) if key_features else '基础特征'}")

        report.append("")
        report.append("=" * 80)

        return "\n".join(report)

def main():
    """主函数 - 执行连板潜力股特征分析"""
    print("🚀 启动连板潜力股特征模式分析器...")

    try:
        print("📊 初始化分析器...")
        analyzer = ConsecutiveLimitUpPatternAnalyzer()
        print("✅ 分析器初始化完成")

        # 执行深度分析
        results = analyzer.analyze_all_patterns()

        if not results:
            print("❌ 分析失败，未获得有效结果")
            return

        # 显示分析报告
        print("\n" + results.get('detailed_report', ''))

        # 保存详细结果到文件
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

        # 保存分析报告
        report_file = f"连板潜力股特征分析报告_{timestamp}.txt"
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(results.get('detailed_report', ''))

        # 保存原始数据
        if results.get('raw_features'):
            import json
            data_file = f"连板潜力股原始特征数据_{timestamp}.json"
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(results['raw_features'], f, ensure_ascii=False, indent=2, default=str)

        print(f"\n📁 分析结果已保存:")
        print(f"  📄 报告文件: {report_file}")
        if results.get('raw_features'):
            print(f"  📊 数据文件: {data_file}")

        # 显示核心发现
        pattern_analysis = results.get('pattern_analysis', {})
        high_freq_count = len(pattern_analysis.get('high_frequency_features', {}))
        medium_freq_count = len(pattern_analysis.get('medium_frequency_features', {}))

        print(f"\n🎯 核心发现:")
        print(f"  • 成功分析: {results.get('total_analyzed', 0)}/25 只股票")
        print(f"  • 高频特征: {high_freq_count} 个")
        print(f"  • 中频特征: {medium_freq_count} 个")
        print(f"  • 关键洞察: {len(pattern_analysis.get('key_insights', []))} 条")

    except Exception as e:
        logger.error(f"分析过程出错: {e}")
        print(f"❌ 分析失败: {e}")

if __name__ == "__main__":
    main()
