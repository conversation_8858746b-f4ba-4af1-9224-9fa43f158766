#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
通知系统
在关键时刻发送止损、止盈通知
"""

import tkinter as tk
from tkinter import messagebox
import winsound
import threading
import time
from datetime import datetime
from typing import Dict, List, Optional, Any
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class NotificationSystem:
    """通知系统"""
    
    def __init__(self):
        """初始化通知系统"""
        self.notification_history = []
        self.sound_enabled = True
        self.popup_enabled = True
        logger.info("✅ 通知系统初始化完成")
    
    def send_critical_alert(self, stock_code: str, stock_name: str, 
                          alert_type: str, message: str, 
                          current_price: float, profit_pct: float,
                          confidence: str = "高") -> bool:
        """
        发送关键警报
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            alert_type: 警报类型 (stop_loss, take_profit, critical_decision)
            message: 警报消息
            current_price: 当前价格
            profit_pct: 盈亏百分比
            confidence: 置信度
            
        Returns:
            bool: 发送是否成功
        """
        try:
            # 构建通知内容
            notification = {
                'timestamp': datetime.now(),
                'stock_code': stock_code,
                'stock_name': stock_name,
                'alert_type': alert_type,
                'message': message,
                'current_price': current_price,
                'profit_pct': profit_pct,
                'confidence': confidence
            }
            
            # 记录通知历史
            self.notification_history.append(notification)
            
            # 发送通知
            self._send_popup_notification(notification)
            self._play_alert_sound(alert_type)
            self._log_notification(notification)
            
            return True
            
        except Exception as e:
            logger.error(f"发送关键警报失败: {e}")
            return False
    
    def send_stop_loss_alert(self, stock_code: str, stock_name: str, 
                           current_price: float, loss_pct: float,
                           reason: str) -> bool:
        """
        发送止损警报
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            current_price: 当前价格
            loss_pct: 亏损百分比
            reason: 止损原因
            
        Returns:
            bool: 发送是否成功
        """
        message = f"🚨 止损警报！\n\n" \
                 f"股票: {stock_name} ({stock_code})\n" \
                 f"当前价格: {current_price:.2f}\n" \
                 f"亏损幅度: {loss_pct:.2f}%\n" \
                 f"止损原因: {reason}\n\n" \
                 f"⚠️ 建议立即卖出止损！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'stop_loss', message, 
            current_price, loss_pct, '高'
        )
    
    def send_take_profit_alert(self, stock_code: str, stock_name: str,
                             current_price: float, profit_pct: float,
                             strategy: str) -> bool:
        """
        发送止盈警报
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            current_price: 当前价格
            profit_pct: 盈利百分比
            strategy: 止盈策略
            
        Returns:
            bool: 发送是否成功
        """
        message = f"🎉 止盈提醒！\n\n" \
                 f"股票: {stock_name} ({stock_code})\n" \
                 f"当前价格: {current_price:.2f}\n" \
                 f"盈利幅度: {profit_pct:.2f}%\n" \
                 f"建议策略: {strategy}\n\n" \
                 f"💰 考虑获利了结！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'take_profit', message,
            current_price, profit_pct, '高'
        )
    
    def send_critical_decision_alert(self, stock_code: str, stock_name: str,
                                   current_price: float, profit_pct: float,
                                   decision: str, reason: str, hold_days: int) -> bool:
        """
        发送关键决策警报
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            current_price: 当前价格
            profit_pct: 盈亏百分比
            decision: 决策建议
            reason: 决策原因
            hold_days: 持有天数
            
        Returns:
            bool: 发送是否成功
        """
        # 根据持有天数确定关键性
        if hold_days == 3:
            day_desc = "第3天关键洗盘日"
            icon = "🎯"
        elif hold_days == 4:
            day_desc = "第4天拉升确认日"
            icon = "🚀"
        elif hold_days >= 5:
            day_desc = f"第{hold_days}天超期持有"
            icon = "⚠️"
        else:
            day_desc = f"第{hold_days}天"
            icon = "📊"
        
        message = f"{icon} {day_desc}决策！\n\n" \
                 f"股票: {stock_name} ({stock_code})\n" \
                 f"当前价格: {current_price:.2f}\n" \
                 f"持仓盈亏: {profit_pct:+.2f}%\n" \
                 f"决策建议: {decision}\n" \
                 f"决策原因: {reason}\n\n" \
                 f"🔔 请及时关注并执行！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'critical_decision', message,
            current_price, profit_pct, '高'
        )
    
    def send_main_force_escape_alert(self, stock_code: str, stock_name: str,
                                   current_price: float, loss_pct: float,
                                   escape_signals: List[str]) -> bool:
        """
        发送主力出逃警报
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            current_price: 当前价格
            loss_pct: 亏损百分比
            escape_signals: 出逃信号列表
            
        Returns:
            bool: 发送是否成功
        """
        signals_text = "\n".join([f"• {signal}" for signal in escape_signals])
        
        message = f"🚨 主力出逃警报！\n\n" \
                 f"股票: {stock_name} ({stock_code})\n" \
                 f"当前价格: {current_price:.2f}\n" \
                 f"亏损幅度: {loss_pct:.2f}%\n\n" \
                 f"出逃信号:\n{signals_text}\n\n" \
                 f"⚠️ 主力已出逃，立即止损！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'main_force_escape', message,
            current_price, loss_pct, '极高'
        )
    
    def send_wash_trading_alert(self, stock_code: str, stock_name: str,
                              current_price: float, profit_pct: float,
                              wash_probability: float, wash_signals: List[str]) -> bool:
        """
        发送洗盘确认警报
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            current_price: 当前价格
            profit_pct: 盈亏百分比
            wash_probability: 洗盘概率
            wash_signals: 洗盘信号列表
            
        Returns:
            bool: 发送是否成功
        """
        signals_text = "\n".join([f"• {signal}" for signal in wash_signals])
        
        message = f"🌊 洗盘确认通知！\n\n" \
                 f"股票: {stock_name} ({stock_code})\n" \
                 f"当前价格: {current_price:.2f}\n" \
                 f"持仓盈亏: {profit_pct:+.2f}%\n" \
                 f"洗盘概率: {wash_probability:.1%}\n\n" \
                 f"洗盘信号:\n{signals_text}\n\n" \
                 f"💪 坚定持有，不要被洗出去！"
        
        return self.send_critical_alert(
            stock_code, stock_name, 'wash_trading', message,
            current_price, profit_pct, '高'
        )
    
    def _send_popup_notification(self, notification: Dict) -> bool:
        """发送弹窗通知"""
        try:
            if not self.popup_enabled:
                return True
            
            def show_popup():
                try:
                    # 创建弹窗
                    root = tk.Tk()
                    root.withdraw()  # 隐藏主窗口
                    
                    # 设置弹窗属性
                    root.attributes('-topmost', True)  # 置顶
                    root.lift()
                    root.focus_force()
                    
                    # 根据警报类型选择图标和标题
                    alert_type = notification['alert_type']
                    if alert_type == 'stop_loss':
                        title = "🚨 止损警报"
                        icon = messagebox.ERROR
                    elif alert_type == 'take_profit':
                        title = "🎉 止盈提醒"
                        icon = messagebox.INFO
                    elif alert_type == 'main_force_escape':
                        title = "🚨 主力出逃"
                        icon = messagebox.ERROR
                    elif alert_type == 'wash_trading':
                        title = "🌊 洗盘确认"
                        icon = messagebox.WARNING
                    else:
                        title = "📊 关键决策"
                        icon = messagebox.WARNING
                    
                    # 显示消息框
                    messagebox.showinfo(title, notification['message'], icon=icon)
                    
                    root.destroy()
                    
                except Exception as e:
                    logger.error(f"显示弹窗失败: {e}")
            
            # 在新线程中显示弹窗，避免阻塞主线程
            popup_thread = threading.Thread(target=show_popup, daemon=True)
            popup_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"发送弹窗通知失败: {e}")
            return False
    
    def _play_alert_sound(self, alert_type: str) -> bool:
        """播放警报声音"""
        try:
            if not self.sound_enabled:
                return True
            
            def play_sound():
                try:
                    if alert_type in ['stop_loss', 'main_force_escape']:
                        # 紧急警报声 - 连续3声
                        for _ in range(3):
                            winsound.Beep(1000, 500)  # 1000Hz, 500ms
                            time.sleep(0.2)
                    elif alert_type == 'take_profit':
                        # 成功提示声 - 上升音调
                        winsound.Beep(800, 300)
                        winsound.Beep(1000, 300)
                        winsound.Beep(1200, 300)
                    else:
                        # 普通提示声
                        winsound.Beep(800, 400)
                        
                except Exception as e:
                    logger.error(f"播放声音失败: {e}")
            
            # 在新线程中播放声音
            sound_thread = threading.Thread(target=play_sound, daemon=True)
            sound_thread.start()
            
            return True
            
        except Exception as e:
            logger.error(f"播放警报声音失败: {e}")
            return False
    
    def _log_notification(self, notification: Dict) -> None:
        """记录通知日志"""
        try:
            timestamp = notification['timestamp'].strftime('%Y-%m-%d %H:%M:%S')
            stock_info = f"{notification['stock_name']}({notification['stock_code']})"
            alert_type = notification['alert_type']
            profit_pct = notification['profit_pct']
            
            log_message = f"📢 通知发送: {timestamp} | {stock_info} | {alert_type} | {profit_pct:+.2f}%"
            logger.info(log_message)
            
            # 控制台输出
            print(f"\n{'='*60}")
            print(f"📢 {alert_type.upper()} 通知已发送")
            print(f"📊 股票: {stock_info}")
            print(f"💰 盈亏: {profit_pct:+.2f}%")
            print(f"⏰ 时间: {timestamp}")
            print(f"{'='*60}\n")
            
        except Exception as e:
            logger.error(f"记录通知日志失败: {e}")
    
    def get_notification_history(self, hours: int = 24) -> List[Dict]:
        """
        获取通知历史
        
        Args:
            hours: 获取最近几小时的通知
            
        Returns:
            List[Dict]: 通知历史列表
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)
            recent_notifications = [
                notif for notif in self.notification_history
                if notif['timestamp'] > cutoff_time
            ]
            
            return recent_notifications
            
        except Exception as e:
            logger.error(f"获取通知历史失败: {e}")
            return []
    
    def clear_notification_history(self) -> bool:
        """清空通知历史"""
        try:
            self.notification_history.clear()
            logger.info("✅ 通知历史已清空")
            return True
        except Exception as e:
            logger.error(f"清空通知历史失败: {e}")
            return False
    
    def set_sound_enabled(self, enabled: bool) -> None:
        """设置声音开关"""
        self.sound_enabled = enabled
        logger.info(f"🔊 声音通知: {'开启' if enabled else '关闭'}")
    
    def set_popup_enabled(self, enabled: bool) -> None:
        """设置弹窗开关"""
        self.popup_enabled = enabled
        logger.info(f"💬 弹窗通知: {'开启' if enabled else '关闭'}")

# 创建全局实例
notification_system = NotificationSystem()
