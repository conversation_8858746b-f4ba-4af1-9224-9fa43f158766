#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的股票数据获取模块
集成AData和AKShare双数据源
"""

import adata
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import re
import logging
from typing import Dict, List, Optional, Tuple

# 尝试导入AKShare
try:
    import akshare as ak
    AKSHARE_AVAILABLE = True
except ImportError:
    AKSHARE_AVAILABLE = False
    print("⚠️ AKShare未安装，部分功能将不可用")

# 尝试导入备用分时数据获取器
try:
    from minute_data_backup_fetcher import MinuteDataBackupFetcher
    BACKUP_FETCHER_AVAILABLE = True
except ImportError:
    BACKUP_FETCHER_AVAILABLE = False

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EnhancedStockDataFetcher:
    """增强的股票数据获取器 - 集成AData和AKShare"""
    
    def __init__(self):
        """初始化"""
        self.last_update_time = None
        self.akshare_available = AKSHARE_AVAILABLE

        # 初始化备用分时数据获取器
        self.backup_fetcher = None
        if BACKUP_FETCHER_AVAILABLE:
            try:
                self.backup_fetcher = MinuteDataBackupFetcher()
                logger.info("✅ 分时数据备用获取器初始化成功")
            except Exception as e:
                logger.warning(f"⚠️ 分时数据备用获取器初始化失败: {e}")
                self.backup_fetcher = None
        
    def normalize_stock_code(self, code: str) -> str:
        """
        标准化股票代码格式 - 返回纯数字格式
        支持多种输入格式：000001, sh000001, 000001.XSHG等
        """
        if not code:
            return ""

        # 移除空格和转换为大写
        code = code.strip().upper()

        # 如果已经是6位数字，直接返回
        if re.match(r'^\d{6}$', code):
            return code

        # 🔥 修复：如果是不足6位的纯数字，补齐到6位
        if re.match(r'^\d{1,5}$', code):
            return code.zfill(6)

        # 如果是sh/sz开头的格式，提取数字部分
        if re.match(r'^(SH|SZ)\d{6}$', code):
            return code[2:]

        # 如果是聚宽格式 000001.XSHG
        if '.XSHG' in code:
            return code.replace('.XSHG', '')
        elif '.XSHE' in code:
            return code.replace('.XSHE', '')

        # 默认返回原格式
        return code

    def standardize_data_units(self, data: pd.DataFrame, data_source: str) -> pd.DataFrame:
        """
        数据单位标准化函数
        统一不同API的数据单位格式

        标准单位：
        - 价格: 元
        - 成交量: 股
        - 成交额: 万元
        - 股本: 股
        - 市值: 元
        - 资金流向: 万元
        """
        if data.empty:
            return data

        data = data.copy()

        try:
            # 根据数据源进行单位标准化
            if data_source == 'tencent':
                # 腾讯财经API标准化
                if 'volume' in data.columns:
                    # 成交量：如果小于100万，可能是万股单位，转换为股
                    mask = (data['volume'] < 1000000) & (data['volume'] > 0)
                    if mask.any():
                        data.loc[mask, 'volume'] = data.loc[mask, 'volume'] * 10000
                        print(f"📊 腾讯API: {mask.sum()}条记录的成交量从万股转换为股")

            elif data_source == 'eastmoney':
                # 东方财富API标准化
                if 'amount' in data.columns:
                    # 成交额：从元转换为万元
                    mask = data['amount'] > 100000000  # 大于1亿的可能是元单位
                    if mask.any():
                        data.loc[mask, 'amount'] = data.loc[mask, 'amount'] / 10000
                        print(f"📊 东方财富API: {mask.sum()}条记录的成交额从元转换为万元")

            # 通用验证：股本数据合理性检查
            if 'total_shares' in data.columns and 'circulation_shares' in data.columns:
                # 总股本应该 >= 流通股本
                mask = (data['circulation_shares'] > data['total_shares']) & (data['total_shares'] > 0)
                if mask.any():
                    print(f"⚠️ {data_source}API: {mask.sum()}条记录的股本数据异常，交换总股本和流通股本")
                    # 交换数据
                    temp = data.loc[mask, 'total_shares'].copy()
                    data.loc[mask, 'total_shares'] = data.loc[mask, 'circulation_shares']
                    data.loc[mask, 'circulation_shares'] = temp

            # 添加数据源标记
            if 'data_source' not in data.columns:
                data['data_source'] = data_source

        except Exception as e:
            logger.warning(f"数据单位标准化失败 {data_source}: {e}")

        return data

    def get_stock_basic_info(self, code: str) -> Dict:
        """获取股票基本信息 - 融合AData和AKShare数据"""
        try:
            normalized_code = self.normalize_stock_code(code)
            result = {
                'code': normalized_code,
                'name': '未知',
                'exchange': 'Unknown',
                'total_market_value': 0,
                'circulation_market_value': 0,
                'total_shares': 0,
                'circulation_shares': 0,
                'industry': '未知',
                'list_date': '未知'
            }
            
            # 1. 优先从AKShare代码对照表获取股票名称（最稳定）
            if self.akshare_available:
                try:
                    df = ak.stock_info_a_code_name()
                    if not df.empty:
                        df['code'] = df['code'].astype(str).str.zfill(6)
                        stock_row = df[df['code'] == normalized_code]
                        if not stock_row.empty:
                            stock_name = stock_row['name'].iloc[0]
                            if stock_name and stock_name.strip():
                                result['name'] = stock_name.strip()
                                logger.info(f"AKShare代码对照表获取股票名称: {normalized_code} -> {stock_name.strip()}")
                except Exception as e:
                    logger.warning(f"AKShare代码对照表获取股票名称失败: {e}")

            # 2. 从AData获取基本信息（备用）
            try:
                all_stocks = adata.stock.info.all_code()
                stock_info = all_stocks[all_stocks['stock_code'] == normalized_code]

                if not stock_info.empty:
                    info = stock_info.iloc[0]
                    # 只有在AKShare没有获取到名称时才使用AData的名称
                    if result['name'] == '未知':
                        result['name'] = info.get('short_name', '未知')
                    result.update({
                        'exchange': info.get('exchange', 'Unknown'),
                        'list_date': str(info.get('list_date', '未知'))
                    })
            except Exception as e:
                logger.warning(f"AData获取基本信息失败: {e}")

            # 3. 从AKShare获取详细信息
            if self.akshare_available:
                try:
                    ak_info = ak.stock_individual_info_em(symbol=normalized_code)
                    if ak_info is not None and not ak_info.empty:
                        info_dict = {}
                        for _, row in ak_info.iterrows():
                            info_dict[row['item']] = row['value']

                        # 如果前面没有获取到股票名称，尝试从个股信息获取
                        if result['name'] == '未知' and '股票简称' in info_dict:
                            result['name'] = info_dict['股票简称']

                        result.update({
                            'total_market_value': float(info_dict.get('总市值', 0)),
                            'circulation_market_value': float(info_dict.get('流通市值', 0)),
                            'total_shares': float(info_dict.get('总股本', 0)),
                            'circulation_shares': float(info_dict.get('流通股', 0)),
                            'industry': info_dict.get('行业', '未知')
                        })
                except Exception as e:
                    logger.warning(f"AKShare获取详细信息失败: {e}")
            
            return result
                
        except Exception as e:
            logger.error(f"获取股票基本信息失败 {code}: {e}")
            return {
                'code': code,
                'name': '获取失败',
                'exchange': 'Error',
                'total_market_value': 0,
                'circulation_market_value': 0,
                'total_shares': 0,
                'circulation_shares': 0,
                'industry': '未知',
                'list_date': '未知'
            }
    
    def get_realtime_quote(self, codes: List[str]) -> pd.DataFrame:
        """获取实时行情数据 - 增强版本"""
        try:
            normalized_codes = [self.normalize_stock_code(code) for code in codes]
            print(f"🔄 获取实时行情: {normalized_codes}")
            
            # 方法1: 使用腾讯财经获取实时行情（验证过的有效API）
            try:
                import requests

                result_df = pd.DataFrame()

                for code in normalized_codes:
                    try:
                        url = f"http://qt.gtimg.cn/q=sz{code}"
                        response = requests.get(url, timeout=10)

                        if response.status_code == 200:
                            data = response.text
                            if data and '~' in data:
                                parts = data.split('~')
                                if len(parts) > 10:
                                    # 获取基础数据
                                    current_price = float(parts[3]) if parts[3] else 0
                                    prev_close = float(parts[4]) if parts[4] else 0
                                    open_price = float(parts[5]) if parts[5] else 0
                                    volume = float(parts[6]) if parts[6] else 0

                                    # 🔧 修复：正确计算涨跌额和涨跌幅
                                    change = current_price - prev_close if prev_close > 0 else 0
                                    change_pct = (change / prev_close * 100) if prev_close > 0 else 0

                                    # 获取总股本、流通股本和市值（腾讯API的正确位置）
                                    total_shares = 0
                                    circulation_shares = 0
                                    market_cap = 0

                                    # 腾讯API中的正确字段位置（基于调试结果）
                                    # [72]: 总股本（股）
                                    if len(parts) > 72 and parts[72]:
                                        try:
                                            total_shares = float(parts[72])  # 已经是股数
                                        except:
                                            pass

                                    # [73]: 流通股本（股）
                                    if len(parts) > 73 and parts[73]:
                                        try:
                                            circulation_shares = float(parts[73])  # 已经是股数
                                        except:
                                            pass

                                    # [44]: 总市值（根据调试结果，应该已经是亿元）
                                    if len(parts) > 44 and parts[44]:
                                        try:
                                            market_cap = float(parts[44])  # 已经是亿元单位
                                        except:
                                            pass

                                    # 如果没有直接市值，用股本计算
                                    if market_cap == 0 and current_price > 0 and total_shares > 0:
                                        market_cap = (current_price * total_shares) / 100000000  # 转换为亿元

                                    # 🔧 修复：数据合理性验证
                                    # 涨跌幅异常检查（超过20%可能是数据错误）
                                    if abs(change_pct) > 20:
                                        print(f"⚠️ 涨跌幅异常 {code}: {change_pct:.2f}%, 当前价:{current_price}, 昨收:{prev_close}")
                                        # 如果涨跌幅异常，重新计算或使用0
                                        if prev_close > 0:
                                            change = current_price - prev_close
                                            change_pct = (change / prev_close * 100)
                                        else:
                                            change = 0
                                            change_pct = 0

                                    # 🔧 新增：腾讯财经API数据单位标准化
                                    # 基于测试结果：腾讯API成交量可能是万股单位，需要转换为股
                                    if volume < 1000000 and volume > 0:  # 如果成交量小于100万，可能是万股单位
                                        volume = volume * 10000  # 转换为股
                                        print(f"📊 {code}: 腾讯API成交量单位转换 {volume/10000:.1f}万股 -> {volume:,.0f}股")

                                    # 成交额已经是万元单位，保持不变
                                    amount = float(parts[37]) if len(parts) > 37 and parts[37] else 0

                                    # 🔧 修复：股本数据字段映射（测试发现字段颠倒）
                                    # 根据测试结果，腾讯API中parts[72]和parts[73]的映射是正确的
                                    # 但在数据输出时需要确保字段名正确
                                    actual_total_shares = total_shares  # parts[72]是总股本
                                    actual_circulation_shares = circulation_shares  # parts[73]是流通股本

                                    # 验证股本数据合理性（总股本应该>=流通股本）
                                    if actual_circulation_shares > actual_total_shares and actual_total_shares > 0:
                                        print(f"⚠️ {code}: 股本数据异常，交换总股本和流通股本")
                                        actual_total_shares, actual_circulation_shares = actual_circulation_shares, actual_total_shares

                                    standard_row = {
                                        'stock_code': code,
                                        'stock_name': parts[1],
                                        'current_price': current_price,
                                        'change': change,  # 🔧 修复：使用计算后的涨跌额
                                        'change_pct': change_pct,  # 🔧 修复：使用计算后的涨跌幅
                                        'open': open_price,  # 🔧 修复：使用正确的开盘价字段
                                        'high': float(parts[33]) if len(parts) > 33 and parts[33] else 0,
                                        'low': float(parts[34]) if len(parts) > 34 and parts[34] else 0,
                                        'prev_close': prev_close,  # 🔧 修复：使用正确的昨收盘价
                                        'volume': volume,  # 🔧 标准化：统一为股单位
                                        'amount': amount,  # 🔧 标准化：万元单位
                                        'total_shares': actual_total_shares,  # 🔧 修复：确保字段映射正确
                                        'circulation_shares': actual_circulation_shares,  # 🔧 修复：确保字段映射正确
                                        'market_cap': market_cap,
                                        'update_time': datetime.now().strftime('%H:%M:%S'),
                                        'data_source': 'tencent'  # 🔧 新增：标记数据源
                                    }
                                    result_df = pd.concat([result_df, pd.DataFrame([standard_row])], ignore_index=True)
                    except Exception as e:
                        print(f"⚠️ 腾讯财经获取 {code} 失败: {e}")
                        continue

                if not result_df.empty:
                    print(f"✅ 腾讯财经获取成功: {result_df.shape}")
                    # 🔧 新增：应用数据单位标准化
                    result_df = self.standardize_data_units(result_df, 'tencent')
                    return result_df
                else:
                    print("⚠️ 腾讯财经返回空数据")

            except Exception as e:
                print(f"⚠️ 腾讯财经获取失败: {e}")

            # 方法2: 尝试东方财富API（市值数据更准确）
            try:
                print("🔄 尝试使用东方财富API获取数据...")
                eastmoney_data = self._get_eastmoney_realtime_data(normalized_codes)
                if eastmoney_data is not None and not eastmoney_data.empty:
                    print(f"✅ 东方财富API获取成功: {eastmoney_data.shape}")
                    # 🔧 新增：应用数据单位标准化
                    eastmoney_data = self.standardize_data_units(eastmoney_data, 'eastmoney')
                    return eastmoney_data
                else:
                    print("⚠️ 东方财富API返回空数据")
            except Exception as e:
                print(f"⚠️ 东方财富API获取失败: {e}")

            # 方法3: 如果前面都失败，尝试使用AKShare
            if self.akshare_available:
                try:
                    print("🔄 尝试使用AKShare获取数据...")
                    result_df = pd.DataFrame()
                    
                    for code in normalized_codes:
                        try:
                            # 获取实时行情
                            realtime_data = ak.stock_zh_a_spot_em()
                            if realtime_data is not None and not realtime_data.empty:
                                # 查找对应股票
                                stock_data = realtime_data[realtime_data['代码'] == code]
                                if not stock_data.empty:
                                    row = stock_data.iloc[0]

                                    # 获取基础数据
                                    current_price = row.get('最新价', 0)

                                    # AKShare通常包含市值数据
                                    market_cap = row.get('总市值', 0)
                                    total_shares = row.get('总股本', 0)
                                    circulation_shares = row.get('流通股', 0)

                                    # 如果没有直接的市值，尝试计算
                                    if market_cap == 0 and current_price > 0 and total_shares > 0:
                                        market_cap = (current_price * total_shares) / 100000000  # 转换为亿元

                                    # 转换为标准格式
                                    standard_row = {
                                        'stock_code': code,
                                        'stock_name': row.get('名称', 'N/A'),
                                        'current_price': current_price,
                                        'change': row.get('涨跌额', 0),
                                        'change_pct': row.get('涨跌幅', 0),
                                        'open': row.get('今开', 0),
                                        'high': row.get('最高', 0),
                                        'low': row.get('最低', 0),
                                        'prev_close': row.get('昨收', 0),
                                        'volume': row.get('成交量', 0),
                                        'amount': row.get('成交额', 0),
                                        'total_shares': total_shares,
                                        'circulation_shares': circulation_shares,
                                        'market_cap': market_cap,
                                        'update_time': datetime.now().strftime('%H:%M:%S')
                                    }
                                    result_df = pd.concat([result_df, pd.DataFrame([standard_row])], ignore_index=True)
                        except Exception as e:
                            print(f"⚠️ AKShare获取 {code} 失败: {e}")
                            continue
                    
                    if not result_df.empty:
                        print(f"✅ AKShare获取成功: {result_df.shape}")
                        return result_df
                    else:
                        print("⚠️ AKShare返回空数据")
                        
                except Exception as e:
                    print(f"⚠️ AKShare获取失败: {e}")
            
            # 方法3: 所有数据源都失败，返回空DataFrame
            print("❌ 所有数据源都失败，无法获取实时行情数据")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取实时行情完全失败: {e}")
            return pd.DataFrame()

    def _get_eastmoney_realtime_data(self, codes: List[str]) -> pd.DataFrame:
        """使用东方财富API获取实时数据（包含准确的市值信息）"""
        try:
            import requests

            result_df = pd.DataFrame()

            for code in codes:
                try:
                    # 东方财富API
                    secid = f"0.{code}" if code.startswith('0') or code.startswith('3') else f"1.{code}"
                    url = f"http://push2.eastmoney.com/api/qt/stock/get?secid={secid}&fields=f43,f44,f45,f46,f47,f48,f49,f50,f51,f52,f57,f58,f116,f117"

                    response = requests.get(url, timeout=10)
                    if response.status_code == 200:
                        data = response.json()
                        if data and 'data' in data and data['data']:
                            stock_data = data['data']

                            # 解析东方财富的字段
                            current_price = stock_data.get('f43', 0) / 100 if stock_data.get('f43') else 0  # 价格（分转元）
                            volume = stock_data.get('f47', 0)  # 成交量
                            total_market_cap = stock_data.get('f116', 0) / 100000000 if stock_data.get('f116') else 0  # 总市值（元转亿元）
                            circulation_market_cap = stock_data.get('f117', 0) / 100000000 if stock_data.get('f117') else 0  # 流通市值

                            # 计算股本（从市值反推）
                            total_shares = (stock_data.get('f116', 0) / stock_data.get('f43', 1)) if stock_data.get('f43') and stock_data.get('f116') else 0
                            circulation_shares = (stock_data.get('f117', 0) / stock_data.get('f43', 1)) if stock_data.get('f43') and stock_data.get('f117') else 0

                            # 🔧 新增：东方财富API数据单位标准化
                            # 基于测试结果：东方财富API成交额是元单位，需要转换为万元
                            amount_raw = stock_data.get('f48', 0)
                            amount_wan_yuan = amount_raw / 10000 if amount_raw > 0 else 0  # 转换为万元

                            # 成交量已经是股单位，保持不变
                            # 股本数据已经是股单位，但需要验证合理性
                            if circulation_shares > total_shares and total_shares > 0:
                                print(f"⚠️ {code}: 东方财富API股本数据异常，交换总股本和流通股本")
                                total_shares, circulation_shares = circulation_shares, total_shares

                            standard_row = {
                                'stock_code': code,
                                'stock_name': f'股票{code}',  # 东方财富API可能不包含名称
                                'current_price': current_price,
                                'change': stock_data.get('f44', 0) / 100 if stock_data.get('f44') else 0,
                                'change_pct': stock_data.get('f45', 0) / 100 if stock_data.get('f45') else 0,
                                'open': stock_data.get('f46', 0) / 100 if stock_data.get('f46') else 0,
                                'high': stock_data.get('f49', 0) / 100 if stock_data.get('f49') else 0,
                                'low': stock_data.get('f50', 0) / 100 if stock_data.get('f50') else 0,
                                'prev_close': (current_price - stock_data.get('f44', 0) / 100) if stock_data.get('f44') else current_price,
                                'volume': volume,  # 🔧 标准化：股单位
                                'amount': amount_wan_yuan,  # 🔧 标准化：转换为万元单位
                                'total_shares': total_shares,  # 🔧 标准化：股单位
                                'circulation_shares': circulation_shares,  # 🔧 标准化：股单位
                                'market_cap': total_market_cap,
                                'update_time': datetime.now().strftime('%H:%M:%S'),
                                'data_source': 'eastmoney'  # 🔧 新增：标记数据源
                            }

                            result_df = pd.concat([result_df, pd.DataFrame([standard_row])], ignore_index=True)

                except Exception as e:
                    continue

            return result_df if not result_df.empty else pd.DataFrame()

        except Exception as e:
            return pd.DataFrame()

    def get_daily_kline(self, code: str, count: int = 30) -> pd.DataFrame:
        """获取日K线数据 - 使用AData主力，从分时数据构造备用"""
        try:
            normalized_code = self.normalize_stock_code(code)

            # 方法1: 优先使用新浪财经（验证过的有效API）
            try:
                import requests

                url = f"http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData?symbol=sz{normalized_code}&scale=240&ma=no&datalen={count}"

                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data and len(data) > 0:
                        df_data = []
                        for item in data:
                            df_data.append({
                                'trade_date': item['day'],
                                'open': float(item['open']),
                                'high': float(item['high']),
                                'low': float(item['low']),
                                'close': float(item['close']),
                                'volume': float(item['volume']),
                                'amount': float(item.get('amount', 0))
                            })

                        df = pd.DataFrame(df_data)
                        df['trade_date'] = pd.to_datetime(df['trade_date'])
                        df = df.sort_values('trade_date')

                        print(f"✅ 新浪财经获取K线成功: {code}, {len(df)}条数据")
                        return df

            except Exception as e:
                print(f"⚠️ 新浪财经获取K线失败 {code}: {e}")

            # 方法2: 从分时数据构造日K线（新的备用方案）
            print(f"🔄 使用分时数据构造日K线: {code}")
            daily_kline = self._construct_daily_from_minute(code, count)
            if daily_kline is not None and not daily_kline.empty:
                print(f"✅ 分时数据构造K线成功: {code}, {len(daily_kline)}条数据")
                return daily_kline

            # 方法3: 备用AKShare（保留原有逻辑）
            if self.akshare_available:
                try:
                    print(f"🔄 使用AKShare备用获取K线: {code}")
                    import akshare as ak

                    # 计算日期范围
                    end_date_ak = datetime.now().strftime('%Y%m%d')
                    start_date_ak = (datetime.now() - timedelta(days=count*2)).strftime('%Y%m%d')

                    df_ak = ak.stock_zh_a_hist(
                        symbol=normalized_code,
                        start_date=start_date_ak,
                        end_date=end_date_ak,
                        adjust="qfq"  # 前复权
                    )

                    if df_ak is not None and not df_ak.empty:
                        # 标准化列名以匹配AData格式
                        df_ak = df_ak.rename(columns={
                            '日期': 'trade_date',
                            '开盘': 'open',
                            '最高': 'high',
                            '最低': 'low',
                            '收盘': 'close',
                            '成交量': 'volume',
                            '成交额': 'amount',
                            '涨跌幅': 'pct_change'
                        })

                        df_ak['trade_date'] = pd.to_datetime(df_ak['trade_date'])
                        df_ak = df_ak.sort_values('trade_date')
                        print(f"✅ AKShare备用获取成功: {code}, {len(df_ak)}条数据")
                        return df_ak.tail(count)
                    else:
                        print(f"⚠️ AKShare返回空数据: {code}")

                except Exception as e:
                    print(f"⚠️ AKShare备用方案失败 {code}: {e}")

            # 所有方法都失败
            logger.error(f"所有数据源都失败，无法获取K线数据: {code}")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取日K线数据完全失败 {code}: {e}")
            return pd.DataFrame()

    def _construct_daily_from_minute(self, code: str, count: int = 30) -> pd.DataFrame:
        """从分时数据构造日K线数据"""
        try:
            if not self.akshare_available:
                return pd.DataFrame()

            import akshare as ak
            import time

            # 获取需要的交易日期
            end_date = datetime.now()
            daily_data = []

            # 向前推算更多天数以确保获取足够的交易日
            for i in range(count * 2):  # 推算更多天数
                current_date = end_date - timedelta(days=i)
                date_str = current_date.strftime('%Y%m%d')

                try:
                    # 获取当日分时数据
                    minute_data = ak.stock_zh_a_hist_min_em(
                        symbol=code,
                        start_date=date_str,
                        end_date=date_str,
                        period='1',
                        adjust=''
                    )

                    if minute_data is not None and not minute_data.empty:
                        # 构造日K线数据
                        daily_record = {
                            'trade_date': current_date.strftime('%Y-%m-%d'),
                            'open': float(minute_data['开盘'].iloc[0]),
                            'high': float(minute_data['最高'].max()),
                            'low': float(minute_data['最低'].min()),
                            'close': float(minute_data['收盘'].iloc[-1]),
                            'volume': float(minute_data['成交量'].sum()),
                            'amount': float(minute_data['成交额'].sum())
                        }

                        # 计算涨跌幅（如果有前一日数据）
                        if daily_data:
                            prev_close = daily_data[-1]['close']
                            daily_record['pct_change'] = ((daily_record['close'] - prev_close) / prev_close) * 100
                        else:
                            daily_record['pct_change'] = 0.0

                        daily_data.append(daily_record)

                        # 如果已经获取足够的交易日数据就停止
                        if len(daily_data) >= count:
                            break

                    # 避免请求过快
                    time.sleep(0.1)

                except Exception as e:
                    # 单日获取失败不影响整体流程
                    continue

            if daily_data:
                # 转换为DataFrame
                df = pd.DataFrame(daily_data)
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.sort_values('trade_date')

                # 重新计算涨跌幅（按正确顺序）
                for i in range(1, len(df)):
                    prev_close = df.iloc[i-1]['close']
                    current_close = df.iloc[i]['close']
                    df.iloc[i, df.columns.get_loc('pct_change')] = ((current_close - prev_close) / prev_close) * 100

                return df.tail(count)
            else:
                return pd.DataFrame()

        except Exception as e:
            print(f"❌ 从分时数据构造日K线失败 {code}: {e}")
            return pd.DataFrame()

    def get_weekly_kline(self, code: str, count: int = 20) -> pd.DataFrame:
        """获取周K线数据 - 使用AData"""
        try:
            normalized_code = self.normalize_stock_code(code)

            # 计算开始日期（往前推count周，大概是count*10天）
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=count*10)).strftime('%Y-%m-%d')

            df = adata.stock.market.get_market(
                stock_code=normalized_code,
                start_date=start_date,
                end_date=end_date,
                k_type=2  # 周K
            )

            if df is not None and not df.empty:
                df['trade_date'] = pd.to_datetime(df['trade_date'])
                df = df.sort_values('trade_date')
                return df.tail(count)
            else:
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取周K线数据失败 {code}: {e}")
            return pd.DataFrame()

    def get_minute_kline(self, code: str, frequency: str = '5m', count: int = 48) -> pd.DataFrame:
        """获取分钟K线数据 - 增强版本"""
        try:
            normalized_code = self.normalize_stock_code(code)
            print(f"🔄 获取分钟K线数据: {normalized_code}, 频率: {frequency}")

            # 方法1: 使用AKShare获取分钟K线数据（修复版本）
            if self.akshare_available:
                try:
                    # 根据频率设置period参数
                    period_map = {
                        '1m': '1',
                        '5m': '5',
                        '15m': '15',
                        '30m': '30',
                        '60m': '60'
                    }
                    period = period_map.get(frequency, '5')

                    # 获取今日分钟K线数据
                    from datetime import datetime, timedelta
                    today = datetime.now().strftime('%Y-%m-%d')

                    # 尝试获取今日数据
                    df = ak.stock_zh_a_hist_min_em(
                        symbol=normalized_code,
                        period=period,
                        start_date=f"{today} 09:30:00",
                        end_date=f"{today} 15:00:00"
                    )

                    if df is not None and not df.empty:
                        print(f"✅ AKShare获取{frequency}K线成功: {df.shape}")

                        # 标准化列名
                        df_standard = pd.DataFrame()
                        df_standard['trade_time'] = pd.to_datetime(df['时间'])
                        df_standard['open'] = pd.to_numeric(df['开盘'], errors='coerce')
                        df_standard['high'] = pd.to_numeric(df['最高'], errors='coerce')
                        df_standard['low'] = pd.to_numeric(df['最低'], errors='coerce')
                        df_standard['close'] = pd.to_numeric(df['收盘'], errors='coerce')
                        df_standard['price'] = df_standard['close']  # 使用收盘价作为价格
                        df_standard['volume'] = pd.to_numeric(df['成交量'], errors='coerce')
                        df_standard['amount'] = pd.to_numeric(df['成交额'], errors='coerce')
                        df_standard['change_pct'] = pd.to_numeric(df['涨跌幅'], errors='coerce')

                        # 按时间排序并返回最新的数据
                        df_standard = df_standard.sort_values('trade_time')
                        return df_standard.tail(count)
                    else:
                        print(f"⚠️ AKShare返回空数据，尝试获取昨日数据...")
                        # 如果今日数据为空，尝试获取昨日数据
                        yesterday = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d')
                        df = ak.stock_zh_a_hist_min_em(
                            symbol=normalized_code,
                            period=period,
                            start_date=f"{yesterday} 09:30:00",
                            end_date=f"{yesterday} 15:00:00"
                        )

                        if df is not None and not df.empty:
                            print(f"✅ AKShare获取昨日{frequency}K线成功: {df.shape}")
                            # 同样的处理逻辑
                            df_standard = pd.DataFrame()
                            df_standard['trade_time'] = pd.to_datetime(df['时间'])
                            df_standard['open'] = pd.to_numeric(df['开盘'], errors='coerce')
                            df_standard['high'] = pd.to_numeric(df['最高'], errors='coerce')
                            df_standard['low'] = pd.to_numeric(df['最低'], errors='coerce')
                            df_standard['close'] = pd.to_numeric(df['收盘'], errors='coerce')
                            df_standard['price'] = df_standard['close']
                            df_standard['volume'] = pd.to_numeric(df['成交量'], errors='coerce')
                            df_standard['amount'] = pd.to_numeric(df['成交额'], errors='coerce')
                            df_standard['change_pct'] = pd.to_numeric(df['涨跌幅'], errors='coerce')

                            df_standard = df_standard.sort_values('trade_time')
                            return df_standard.tail(count)

                except Exception as e:
                    print(f"⚠️ AKShare获取{frequency}K线失败: {e}")

            # 方法2: 使用AData获取分时数据并处理
            try:
                df = adata.stock.market.get_market_min(stock_code=normalized_code)

                if df is not None and not df.empty:
                    print(f"✅ AData获取分时数据成功: {df.shape}")

                    df['trade_time'] = pd.to_datetime(df['trade_time'])
                    df = df.sort_values('trade_time')

                    # 根据频率进行数据处理
                    if frequency == '5m':
                        # 每5分钟取一条数据
                        df_filtered = df.iloc[::5]
                    elif frequency == '15m':
                        # 每15分钟取一条数据
                        df_filtered = df.iloc[::15]
                    elif frequency == '30m':
                        # 每30分钟取一条数据
                        df_filtered = df.iloc[::30]
                    elif frequency == '60m':
                        # 每60分钟取一条数据
                        df_filtered = df.iloc[::60]
                    else:
                        df_filtered = df

                    print(f"✅ AData数据处理完成: {df_filtered.shape}")
                    return df_filtered.tail(count)

            except Exception as e:
                print(f"⚠️ AData获取分时数据失败: {e}")

            # 方法3: 所有数据源都失败，返回空DataFrame
            print(f"❌ 所有数据源都失败，无法获取分钟K线数据")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取分钟K线数据完全失败 {code}: {e}")
            return pd.DataFrame()



    def get_market_depth(self, code: str) -> pd.DataFrame:
        """获取五档行情数据 - 使用AData"""
        try:
            normalized_code = self.normalize_stock_code(code)
            
            # 获取五档行情
            depth_data = adata.stock.market.get_market_five(stock_code=normalized_code)
            
            if depth_data is not None and not depth_data.empty:
                return depth_data
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取五档行情失败 {code}: {e}")
            return pd.DataFrame()
    
    def get_lhb_data(self, code: str = None) -> Dict:
        """获取龙虎榜数据 - 增强版，包含6个月历史数据"""
        try:
            result = {
                'today_lhb': pd.DataFrame(),
                'history_lhb': pd.DataFrame(),
                'stock_in_lhb': False,
                'stock_lhb_reason': '',
                'lhb_analysis': {
                    'total_records': 0,
                    'recent_activity': False,
                    'data_available': False,
                    'analysis_summary': ''
                }
            }

            # 1. 尝试获取当日龙虎榜 (使用AData)
            try:
                lhb_list = adata.sentiment.hot.list_a_list_daily()
                if lhb_list is not None and not lhb_list.empty:
                    result['today_lhb'] = lhb_list

                    # 如果指定了股票代码，查看是否在龙虎榜中
                    if code:
                        normalized_code = self.normalize_stock_code(code)
                        if normalized_code in lhb_list['stock_code'].values:
                            result['stock_in_lhb'] = True
                            target_row = lhb_list[lhb_list['stock_code'] == normalized_code].iloc[0]
                            result['stock_lhb_reason'] = target_row.get('reason', '未知')
            except Exception as e:
                logger.warning(f"AData龙虎榜获取失败，尝试备用方案: {e}")

            # 2. 获取6个月历史龙虎榜数据 (使用AKShare)
            if code and self.akshare_available:
                history_data = self._get_6month_lhb_history(code)
                if history_data is not None and not history_data.empty:
                    result['history_lhb'] = history_data
                    result['lhb_analysis']['data_available'] = True
                    result['lhb_analysis']['total_records'] = len(history_data)

                    # 分析历史数据
                    analysis = self._analyze_lhb_history(history_data)
                    result['lhb_analysis'].update(analysis)

                    # 检查最近活跃性
                    from datetime import datetime, timedelta
                    recent_date = (datetime.now() - timedelta(days=5)).strftime('%Y-%m-%d')
                    # 确保日期格式一致进行比较
                    history_data_copy = history_data.copy()
                    history_data_copy['上榜日_str'] = pd.to_datetime(history_data_copy['上榜日']).dt.strftime('%Y-%m-%d')
                    recent_records = history_data_copy[history_data_copy['上榜日_str'] >= recent_date]
                    if not recent_records.empty:
                        result['lhb_analysis']['recent_activity'] = True
                        if not result['stock_in_lhb']:  # 如果当日数据没有，用历史数据补充
                            latest_record = recent_records.iloc[0]
                            result['stock_lhb_reason'] = latest_record.get('上榜原因', '未知')

            return result

        except Exception as e:
            logger.error(f"获取龙虎榜数据失败: {e}")
            return {
                'today_lhb': pd.DataFrame(),
                'history_lhb': pd.DataFrame(),
                'stock_in_lhb': False,
                'stock_lhb_reason': '',
                'lhb_analysis': {
                    'total_records': 0,
                    'recent_activity': False,
                    'data_available': False,
                    'analysis_summary': '数据获取失败'
                }
            }
    
    def get_capital_flow(self, code: str) -> pd.DataFrame:
        """获取资金流向数据 - 修复版，直接返回DataFrame以兼容导出器"""
        try:
            # 🔧 修复：直接返回DataFrame而不是字典，以兼容enhanced_data_exporter
            result_df = pd.DataFrame()

            # 1. 尝试使用AData获取基础数据
            try:
                normalized_code = self.normalize_stock_code(code)
                adata_df = adata.stock.market.get_capital_flow(stock_code=normalized_code)

                if adata_df is not None and not adata_df.empty:
                    adata_df['trade_date'] = pd.to_datetime(adata_df['trade_date'])
                    # 修复：按日期降序排列，最新数据在前
                    adata_df = adata_df.sort_values('trade_date', ascending=False)

                    # 🔧 标准化：AData资金流向数据单位标准化（万元）
                    # 确保所有资金流向数据都是万元单位
                    money_columns = ['主力净流入', '超大单净流入', '大单净流入', '中单净流入', '小单净流入']
                    for col in money_columns:
                        if col in adata_df.columns:
                            # AData API返回的数据已经是万元单位，保持不变
                            pass

                    result_df = adata_df
                    logger.info(f"AData获取资金流向数据成功: {len(adata_df)}条记录，最新日期: {adata_df['trade_date'].iloc[0]}")
                    return result_df
            except Exception as e:
                logger.warning(f"AData资金流向获取失败，尝试备用方案: {e}")

            # 2. 使用AKShare获取详细分级数据（备用方案）
            if self.akshare_available:
                try:
                    detailed_flow = self._get_detailed_capital_flow(code)
                    if detailed_flow is not None and not detailed_flow.empty:
                        # 🔧 标准化：AKShare资金流向数据单位标准化
                        # 确保数据单位统一为万元
                        return detailed_flow
                except Exception as e:
                    logger.warning(f"AKShare资金流向获取失败: {e}")

            # 3. 如果所有方法都失败，返回空DataFrame
            logger.warning(f"所有资金流向数据源都失败，返回空DataFrame")
            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取资金流向数据失败 {code}: {e}")
            return pd.DataFrame()
    
    def get_concept_info(self, code: str) -> pd.DataFrame:
        """获取概念板块信息 - 使用AData"""
        try:
            normalized_code = self.normalize_stock_code(code)
            
            df = adata.stock.info.get_concept_ths(stock_code=normalized_code)
            
            if df is not None and not df.empty:
                return df
            else:
                return pd.DataFrame()
                
        except Exception as e:
            logger.error(f"获取概念板块信息失败 {code}: {e}")
            return pd.DataFrame()
    
    def get_technical_indicators(self, code: str) -> Dict:
        """获取技术指标信息 - 增强版，包含完整技术指标计算"""
        result = {
            'is_new_high': False,
            'is_continuous_up': False,
            'continuous_days': 0,
            'macd': {},
            'kdj': {},
            'rsi': {},
            'bollinger': {},
            'volume_indicators': {},
            'technical_signals': [],
            'indicators_available': False,
            'analysis_summary': ''
        }

        try:
            # 1. 获取K线数据用于技术指标计算
            kline_data = self.get_daily_kline(code, count=120)

            if kline_data is not None and not kline_data.empty and len(kline_data) >= 30:
                # 计算技术指标
                indicators = self._calculate_all_technical_indicators(kline_data)
                result.update(indicators)
                result['indicators_available'] = True

                # 生成技术信号
                signals = self._analyze_technical_signals(indicators)
                result['technical_signals'] = signals

                # 生成分析摘要
                summary = self._generate_technical_summary(indicators, signals)
                result['analysis_summary'] = summary

                logger.info(f"股票{code}: 技术指标计算完成，包含{len(signals)}个信号")
            else:
                logger.warning(f"股票{code}: K线数据不足，无法计算技术指标")

            # 2. 获取市场排名信息（保留原有功能）
            if self.akshare_available:
                try:
                    normalized_code = self.normalize_stock_code(code)

                    # 检查是否创新高
                    try:
                        cxg_stocks = ak.stock_rank_cxg_ths(symbol="创月新高")
                        if cxg_stocks is not None and not cxg_stocks.empty:
                            if normalized_code in cxg_stocks['股票代码'].values:
                                result['is_new_high'] = True
                    except Exception as e:
                        logger.warning(f"获取创新高数据失败: {e}")

                    # 检查是否连续上涨
                    try:
                        lxsz_stocks = ak.stock_rank_lxsz_ths()
                        if lxsz_stocks is not None and not lxsz_stocks.empty:
                            if normalized_code in lxsz_stocks['股票代码'].values:
                                result['is_continuous_up'] = True
                                target_row = lxsz_stocks[lxsz_stocks['股票代码'] == normalized_code].iloc[0]
                                result['continuous_days'] = int(target_row.get('连涨天数', 0))
                    except Exception as e:
                        logger.warning(f"获取连续上涨数据失败: {e}")

                except Exception as e:
                    logger.warning(f"获取市场排名信息失败: {e}")

            return result

        except Exception as e:
            logger.error(f"获取技术指标失败 {code}: {e}")
            return result
    
    def get_comprehensive_analysis(self, code: str) -> Dict:
        """获取综合分析数据 - 整合所有数据源"""
        try:
            normalized_code = self.normalize_stock_code(code)
            
            result = {
                'basic_info': {},
                'realtime_quote': pd.DataFrame(),
                'kline_data': pd.DataFrame(),
                'market_depth': pd.DataFrame(),
                'lhb_data': {},
                'capital_flow': pd.DataFrame(),
                'concept_info': pd.DataFrame(),
                'technical_indicators': {},
                'minute_data': {},  # 添加分时数据字段
                'chip_distribution': {},  # 添加筹码分布字段
                'time_cycle': {},  # 添加时间周期字段
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # 获取各类数据
            result['basic_info'] = self.get_stock_basic_info(code)
            result['realtime_quote'] = self.get_realtime_quote([code])
            result['kline_data'] = self.get_daily_kline(code, 30)
            result['market_depth'] = self.get_market_depth(code)

            # 获取龙虎榜数据（带重试机制）
            logger.info(f"开始获取{code}龙虎榜数据...")
            result['lhb_data'] = self.get_lhb_data(code)

            result['capital_flow'] = self.get_capital_flow(code)
            result['concept_info'] = self.get_concept_info(code)
            result['technical_indicators'] = self.get_technical_indicators(code)

            # 获取分时数据 - 智能选择实时或历史数据（带重试机制）
            logger.info(f"开始获取{code}分时数据...")
            result['minute_data'] = self._get_smart_minute_data(code)

            # 获取筹码分布和时间周期数据
            result['chip_distribution'] = self._get_chip_distribution_data(result['kline_data'])
            result['time_cycle'] = self._get_time_cycle_data(result['kline_data'])
            
            return result
                
        except Exception as e:
            logger.error(f"获取综合分析数据失败 {code}: {e}")
            return {}

    def _get_smart_minute_data(self, code: str) -> Dict:
        """智能获取分时数据 - 始终获取7天历史数据，提供完整的游资炒作周期分析"""
        try:
            from datetime import datetime, time

            current_time = datetime.now()
            current_weekday = current_time.weekday()  # 0=Monday, 6=Sunday
            current_hour_minute = current_time.time()

            # 判断是否为交易时间（用于日志记录）
            is_trading_day = current_weekday < 5  # 周一到周五
            is_trading_hours = (
                (time(9, 30) <= current_hour_minute <= time(11, 30)) or  # 上午交易时间
                (time(13, 0) <= current_hour_minute <= time(15, 0))      # 下午交易时间
            )

            # 始终获取7天历史分时数据，提供完整的游资炒作周期分析
            if is_trading_day and is_trading_hours:
                logger.info(f"交易时间内，获取{code}最近7天分时数据（包含当日实时数据）")
            else:
                logger.info(f"非交易时间，获取{code}最近7天分时数据（包含当日完整数据）")

            result_7days = self.get_recent_7days_minute_data(code)

            if result_7days.get('data_available', False):
                logger.info(f"成功获取{code}最近7天分时数据，包含{result_7days.get('total_trading_days', 0)}个交易日")
                return result_7days
            else:
                # 如果7天数据获取失败，尝试获取当日数据作为备选
                logger.warning(f"7天分时数据获取失败，尝试获取当日数据作为备选")
                minute_result = self.get_historical_minute_data(code)

                if minute_result.get('data_available', False):
                    logger.info(f"成功获取{code}当日分时数据作为备选")
                    return minute_result
                else:
                    logger.error(f"所有分时数据获取方式都失败")
                    return {
                        'data_available': False,
                        'error': '所有分时数据获取方式都失败',
                        'summary': '分时数据不可用'
                    }

        except Exception as e:
            logger.error(f"智能分时数据获取失败 {code}: {e}")
            return {
                'data_available': False,
                'error': str(e),
                'summary': '分时数据获取失败'
            }

    def _get_chip_distribution_data(self, kline_data: pd.DataFrame) -> Dict:
        """获取筹码分布数据"""
        try:
            if kline_data.empty:
                return {
                    'available': False,
                    'summary': '无K线数据，无法计算筹码分布'
                }

            # 简单的筹码分布计算
            recent_data = kline_data.tail(20)  # 最近20天

            if '收盘' not in recent_data.columns or '成交量' not in recent_data.columns:
                return {
                    'available': False,
                    'summary': '数据字段不完整'
                }

            # 计算价格区间分布
            price_min = recent_data['收盘'].min()
            price_max = recent_data['收盘'].max()
            current_price = recent_data['收盘'].iloc[-1]

            # 计算筹码集中度
            price_std = recent_data['收盘'].std()
            price_mean = recent_data['收盘'].mean()
            concentration = (price_std / price_mean) * 100 if price_mean > 0 else 0

            return {
                'available': True,
                'price_range': f"{price_min:.2f} - {price_max:.2f}",
                'current_price': f"{current_price:.2f}",
                'concentration': f"{concentration:.2f}%",
                'summary': f"筹码集中度{concentration:.1f}%，当前价格{current_price:.2f}"
            }

        except Exception as e:
            logger.error(f"筹码分布计算失败: {e}")
            return {
                'available': False,
                'error': str(e),
                'summary': '筹码分布计算失败'
            }

    def _get_time_cycle_data(self, kline_data: pd.DataFrame) -> Dict:
        """获取时间周期数据"""
        try:
            if kline_data.empty:
                return {
                    'available': False,
                    'summary': '无K线数据，无法分析时间周期'
                }

            # 简单的时间周期分析
            data_length = len(kline_data)

            if '收盘' not in kline_data.columns:
                return {
                    'available': False,
                    'summary': '数据字段不完整'
                }

            # 计算趋势
            if data_length >= 5:
                recent_5 = kline_data['收盘'].tail(5).mean()
                earlier_5 = kline_data['收盘'].head(5).mean()
                trend = "上升" if recent_5 > earlier_5 else "下降"
            else:
                trend = "数据不足"

            # 计算波动周期
            if data_length >= 10:
                volatility = kline_data['收盘'].tail(10).std()
                cycle_strength = "高波动" if volatility > kline_data['收盘'].mean() * 0.05 else "低波动"
            else:
                cycle_strength = "数据不足"

            return {
                'available': True,
                'data_period': f"{data_length}天",
                'trend_direction': trend,
                'cycle_strength': cycle_strength,
                'summary': f"{data_length}天数据，{trend}趋势，{cycle_strength}"
            }

        except Exception as e:
            logger.error(f"时间周期分析失败: {e}")
            return {
                'available': False,
                'error': str(e),
                'summary': '时间周期分析失败'
            }
    
    def format_price(self, price) -> str:
        """格式化价格显示"""
        try:
            if pd.isna(price) or price is None:
                return "--"
            return f"{float(price):.2f}"
        except:
            return "--"
    
    def format_volume(self, volume) -> str:
        """格式化成交量显示"""
        try:
            if pd.isna(volume) or volume is None:
                return "--"
            
            vol = float(volume)
            if vol >= 100000000:  # 亿
                return f"{vol/100000000:.2f}亿"
            elif vol >= 10000:  # 万
                return f"{vol/10000:.2f}万"
            else:
                return f"{vol:.0f}"
        except:
            return "--"

    def _get_6month_lhb_history(self, code: str) -> pd.DataFrame:
        """获取指定股票6个月龙虎榜历史数据（带重试机制）"""
        try:
            from datetime import datetime, timedelta
            import akshare as ak
            import time

            # 计算6个月时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=180)  # 6个月约180天

            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = (end_date - timedelta(days=1)).strftime('%Y%m%d')

            logger.info(f"获取股票{code}龙虎榜历史数据: {start_date_str} 到 {end_date_str}")

            # 重试配置
            max_retries = 3
            retry_delay = 35  # 35秒间隔

            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"  🔄 第 {attempt + 1} 次尝试获取龙虎榜历史数据...")
                        time.sleep(retry_delay)  # 等待35秒后重试

                    # 获取龙虎榜数据
                    lhb_all_data = ak.stock_lhb_detail_em(start_date=start_date_str, end_date=end_date_str)

                    if lhb_all_data.empty:
                        logger.info(f"股票{code}: 6个月内无龙虎榜数据")
                        return pd.DataFrame()

                    # 筛选指定股票的数据
                    stock_lhb_data = lhb_all_data[lhb_all_data['代码'] == code].copy()

                    if stock_lhb_data.empty:
                        logger.info(f"股票{code}: 6个月内无龙虎榜记录")
                        return pd.DataFrame()

                    # 按日期排序（最新的在前）
                    stock_lhb_data = stock_lhb_data.sort_values('上榜日', ascending=False)

                    logger.info(f"  ✅ 成功获取股票{code}龙虎榜历史数据: {len(stock_lhb_data)}条记录")
                    return stock_lhb_data

                except Exception as e:
                    error_msg = str(e)
                    logger.error(f"  ❌ 第 {attempt + 1} 次尝试失败: {error_msg}")

                    # 如果是网络连接问题，减少重试次数
                    if "Connection" in error_msg or "Remote end closed" in error_msg or "Response ended prematurely" in error_msg:
                        if attempt >= 1:  # 网络问题最多重试2次
                            logger.error(f"  🌐 检测到网络问题，停止重试")
                            return pd.DataFrame()

                    if attempt < max_retries - 1:
                        continue
                    else:
                        logger.error(f"  ❌ 所有重试均失败，跳过龙虎榜历史数据获取")
                        return pd.DataFrame()

            return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取股票{code}龙虎榜历史数据失败: {e}")
            return pd.DataFrame()

    def _analyze_lhb_history(self, lhb_data: pd.DataFrame) -> Dict:
        """分析龙虎榜历史数据"""
        try:
            if lhb_data.empty:
                return {
                    'analysis_summary': '无历史数据',
                    'frequency': '无',
                    'recent_trend': '无',
                    'main_reasons': [],
                    'net_buy_trend': '无'
                }

            total_records = len(lhb_data)

            # 分析上榜频率
            if total_records >= 10:
                frequency = '高频'
            elif total_records >= 5:
                frequency = '中频'
            else:
                frequency = '低频'

            # 分析上榜原因
            reasons = lhb_data['上榜原因'].value_counts()
            main_reasons = reasons.head(3).index.tolist()

            # 分析资金流向趋势
            net_buy_trend = '无'
            if '龙虎榜净买额' in lhb_data.columns:
                net_amounts = lhb_data['龙虎榜净买额'].dropna()
                if not net_amounts.empty:
                    positive_count = (net_amounts > 0).sum()
                    total_count = len(net_amounts)
                    if positive_count / total_count > 0.6:
                        net_buy_trend = '净买入为主'
                    elif positive_count / total_count < 0.4:
                        net_buy_trend = '净卖出为主'
                    else:
                        net_buy_trend = '买卖均衡'

            # 分析最近趋势（最近3次）
            recent_trend = '无'
            if total_records >= 3:
                recent_data = lhb_data.head(3)
                recent_dates = pd.to_datetime(recent_data['上榜日'])
                date_diff = (recent_dates.max() - recent_dates.min()).days
                if date_diff <= 10:
                    recent_trend = '密集上榜'
                elif date_diff <= 30:
                    recent_trend = '活跃期'
                else:
                    recent_trend = '分散上榜'

            # 生成分析摘要
            analysis_summary = f"{frequency}上榜({total_records}次), {recent_trend}, {net_buy_trend}"

            return {
                'analysis_summary': analysis_summary,
                'frequency': frequency,
                'recent_trend': recent_trend,
                'main_reasons': main_reasons,
                'net_buy_trend': net_buy_trend
            }

        except Exception as e:
            logger.error(f"分析龙虎榜历史数据失败: {e}")
            return {
                'analysis_summary': '分析失败',
                'frequency': '未知',
                'recent_trend': '未知',
                'main_reasons': [],
                'net_buy_trend': '未知'
            }

    def _get_detailed_capital_flow(self, code: str) -> pd.DataFrame:
        """获取详细的资金流向数据（使用AKShare）"""
        try:
            import akshare as ak

            # 确定市场
            market = "sz" if code.startswith("00") or code.startswith("30") else "sh"

            logger.info(f"获取股票{code}详细资金流向数据，市场：{market}")

            # 获取个股资金流向数据
            fund_flow_df = ak.stock_individual_fund_flow(stock=code, market=market)

            if fund_flow_df is None or fund_flow_df.empty:
                logger.info(f"股票{code}: 无详细资金流向数据")
                return pd.DataFrame()

            # 数据预处理
            fund_flow_df = fund_flow_df.copy()

            # 确保日期列格式正确
            if '日期' in fund_flow_df.columns:
                fund_flow_df['日期'] = pd.to_datetime(fund_flow_df['日期'])
                fund_flow_df = fund_flow_df.sort_values('日期', ascending=False)

            logger.info(f"股票{code}: 获取到{len(fund_flow_df)}天的详细资金流向数据")
            return fund_flow_df

        except Exception as e:
            logger.error(f"获取股票{code}详细资金流向数据失败: {e}")
            return pd.DataFrame()

    def _analyze_capital_flow(self, flow_data: pd.DataFrame) -> Dict:
        """分析资金流向数据"""
        try:
            if flow_data.empty:
                return {
                    'summary': '无资金流向数据',
                    'main_net_inflow': 0,
                    'super_large_net_inflow': 0,
                    'large_net_inflow': 0,
                    'medium_net_inflow': 0,
                    'small_net_inflow': 0,
                    'latest_date': '未知',
                    'trend_analysis': '无',
                    'data_quality': 'poor'
                }

            # 获取最新数据
            latest_data = flow_data.iloc[0]

            # 提取关键数据
            main_flow = latest_data.get('主力净流入-净额', 0)
            super_large_flow = latest_data.get('超大单净流入-净额', 0)
            large_flow = latest_data.get('大单净流入-净额', 0)
            medium_flow = latest_data.get('中单净流入-净额', 0)
            small_flow = latest_data.get('小单净流入-净额', 0)
            latest_date = latest_data.get('日期', '未知')

            # 趋势分析（最近5天）
            trend_analysis = '无'
            if len(flow_data) >= 5:
                recent_5_days = flow_data.head(5)
                if '主力净流入-净额' in recent_5_days.columns:
                    positive_days = (recent_5_days['主力净流入-净额'] > 0).sum()
                    if positive_days >= 4:
                        trend_analysis = '强势流入'
                    elif positive_days >= 3:
                        trend_analysis = '偏向流入'
                    elif positive_days <= 1:
                        trend_analysis = '持续流出'
                    else:
                        trend_analysis = '震荡整理'

            # 数据质量评估
            data_quality = 'good' if len(flow_data) > 30 else 'fair' if len(flow_data) > 10 else 'poor'

            # 生成摘要
            main_flow_wan = main_flow / 10000  # 转换为万元
            if main_flow_wan > 1000:
                flow_level = '大额流入'
            elif main_flow_wan > 100:
                flow_level = '中等流入'
            elif main_flow_wan > -100:
                flow_level = '小幅波动'
            elif main_flow_wan > -1000:
                flow_level = '中等流出'
            else:
                flow_level = '大额流出'

            summary = f"{flow_level}({main_flow_wan:.0f}万), {trend_analysis}, 数据质量{data_quality}"

            return {
                'summary': summary,
                'main_net_inflow': main_flow,
                'super_large_net_inflow': super_large_flow,
                'large_net_inflow': large_flow,
                'medium_net_inflow': medium_flow,
                'small_net_inflow': small_flow,
                'latest_date': str(latest_date),
                'trend_analysis': trend_analysis,
                'data_quality': data_quality,
                'main_flow_wan': main_flow_wan
            }

        except Exception as e:
            logger.error(f"分析资金流向数据失败: {e}")
            return {
                'summary': '分析失败',
                'main_net_inflow': 0,
                'super_large_net_inflow': 0,
                'large_net_inflow': 0,
                'medium_net_inflow': 0,
                'small_net_inflow': 0,
                'latest_date': '未知',
                'trend_analysis': '未知',
                'data_quality': 'poor',
                'error': str(e)
            }

    def _calculate_all_technical_indicators(self, kline_data: pd.DataFrame) -> Dict:
        """计算所有技术指标"""
        try:
            # 确保数据列名正确
            if 'close' not in kline_data.columns:
                # 尝试映射常见的列名
                column_mapping = {
                    '收盘': 'close',
                    '最高': 'high',
                    '最低': 'low',
                    '开盘': 'open',
                    '成交量': 'volume'
                }

                for old_col, new_col in column_mapping.items():
                    if old_col in kline_data.columns:
                        kline_data = kline_data.rename(columns={old_col: new_col})

            indicators = {}

            # 1. 计算MACD
            try:
                macd_result = self._calculate_macd(kline_data)
                indicators['macd'] = macd_result if macd_result else {}
            except Exception as e:
                logger.warning(f"MACD计算失败: {e}")
                indicators['macd'] = {}

            # 2. 计算KDJ
            try:
                kdj_result = self._calculate_kdj(kline_data)
                indicators['kdj'] = kdj_result if kdj_result else {}
            except Exception as e:
                logger.warning(f"KDJ计算失败: {e}")
                indicators['kdj'] = {}

            # 3. 计算RSI
            try:
                rsi_result = self._calculate_rsi(kline_data)
                indicators['rsi'] = {'RSI14': rsi_result} if rsi_result is not None else {}
            except Exception as e:
                logger.warning(f"RSI计算失败: {e}")
                indicators['rsi'] = {}

            # 4. 计算布林带
            try:
                boll_result = self._calculate_bollinger_bands(kline_data)
                indicators['bollinger'] = boll_result if boll_result else {}
            except Exception as e:
                logger.warning(f"布林带计算失败: {e}")
                indicators['bollinger'] = {}

            # 5. 计算成交量指标
            try:
                volume_result = self._calculate_volume_indicators(kline_data)
                indicators['volume_indicators'] = volume_result if volume_result else {}
            except Exception as e:
                logger.warning(f"成交量指标计算失败: {e}")
                indicators['volume_indicators'] = {}

            return indicators

        except Exception as e:
            logger.error(f"技术指标计算失败: {e}")
            return {
                'macd': {},
                'kdj': {},
                'rsi': {},
                'bollinger': {},
                'volume_indicators': {}
            }

    def _calculate_macd(self, df, fast=12, slow=26, signal=9):
        """计算MACD指标"""
        try:
            if 'close' not in df.columns:
                return None

            close = df['close']

            # 计算EMA
            ema_fast = close.ewm(span=fast).mean()
            ema_slow = close.ewm(span=slow).mean()

            # 计算DIF (MACD线)
            dif = ema_fast - ema_slow

            # 计算DEA (信号线)
            dea = dif.ewm(span=signal).mean()

            # 计算MACD柱状图
            macd = (dif - dea) * 2

            return {
                'DIF': float(dif.iloc[-1]) if len(dif) > 0 else 0,
                'DEA': float(dea.iloc[-1]) if len(dea) > 0 else 0,
                'MACD': float(macd.iloc[-1]) if len(macd) > 0 else 0
            }
        except Exception as e:
            logger.error(f"MACD计算失败: {e}")
            return None

    def _calculate_kdj(self, df, n=9, m1=3, m2=3):
        """计算KDJ指标"""
        try:
            if not all(col in df.columns for col in ['high', 'low', 'close']):
                return None

            high = df['high']
            low = df['low']
            close = df['close']

            # 计算RSV
            lowest_low = low.rolling(window=n).min()
            highest_high = high.rolling(window=n).max()
            rsv = (close - lowest_low) / (highest_high - lowest_low) * 100

            # 计算K值
            k = rsv.ewm(alpha=1/m1).mean()

            # 计算D值
            d = k.ewm(alpha=1/m2).mean()

            # 计算J值
            j = 3 * k - 2 * d

            return {
                'K': float(k.iloc[-1]) if len(k) > 0 else 0,
                'D': float(d.iloc[-1]) if len(d) > 0 else 0,
                'J': float(j.iloc[-1]) if len(j) > 0 else 0
            }
        except Exception as e:
            logger.error(f"KDJ计算失败: {e}")
            return None

    def _calculate_rsi(self, df, period=14):
        """计算RSI指标"""
        try:
            if 'close' not in df.columns:
                return None

            close = df['close']
            delta = close.diff()

            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()

            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))

            return float(rsi.iloc[-1]) if len(rsi) > 0 else 0
        except Exception as e:
            logger.error(f"RSI计算失败: {e}")
            return None

    def _calculate_bollinger_bands(self, df, period=20, std_dev=2):
        """计算布林带指标"""
        try:
            if 'close' not in df.columns:
                return None

            close = df['close']

            # 计算中轨（移动平均线）
            middle = close.rolling(window=period).mean()

            # 计算标准差
            std = close.rolling(window=period).std()

            # 计算上轨和下轨
            upper = middle + (std * std_dev)
            lower = middle - (std * std_dev)

            # 计算布林带宽度
            width = ((upper - lower) / middle * 100)

            return {
                'UPPER': float(upper.iloc[-1]) if len(upper) > 0 else 0,
                'MIDDLE': float(middle.iloc[-1]) if len(middle) > 0 else 0,
                'LOWER': float(lower.iloc[-1]) if len(lower) > 0 else 0,
                'WIDTH': float(width.iloc[-1]) if len(width) > 0 else 0
            }
        except Exception as e:
            logger.error(f"布林带计算失败: {e}")
            return None

    def _calculate_volume_indicators(self, df):
        """计算成交量相关指标"""
        try:
            if 'volume' not in df.columns:
                return {}

            volume = df['volume']

            # 计算量比（当日成交量/过去5日平均成交量）
            avg_volume_5 = volume.rolling(window=5).mean()
            volume_ratio = volume / avg_volume_5

            # 计算成交量变化率
            volume_change = volume.pct_change() * 100

            return {
                'VOLUME_RATIO': float(volume_ratio.iloc[-1]) if len(volume_ratio) > 0 else 0,
                'VOLUME_CHANGE': float(volume_change.iloc[-1]) if len(volume_change) > 0 else 0
            }
        except Exception as e:
            logger.error(f"成交量指标计算失败: {e}")
            return {}

    def _analyze_technical_signals(self, indicators: Dict) -> List[str]:
        """分析技术指标信号"""
        try:
            signals = []

            # MACD信号
            macd_data = indicators.get('macd', {})
            if macd_data:
                dif = macd_data.get('DIF', 0)
                dea = macd_data.get('DEA', 0)
                macd = macd_data.get('MACD', 0)

                if dif > dea and macd > 0:
                    signals.append("MACD金叉向上")
                elif dif < dea and macd < 0:
                    signals.append("MACD死叉向下")

            # KDJ信号
            kdj_data = indicators.get('kdj', {})
            if kdj_data:
                k = kdj_data.get('K', 0)
                d = kdj_data.get('D', 0)

                if k > 80 and d > 80:
                    signals.append("KDJ超买信号")
                elif k < 20 and d < 20:
                    signals.append("KDJ超卖信号")

            # RSI信号
            rsi_data = indicators.get('rsi', {})
            if rsi_data:
                rsi = rsi_data.get('RSI14', 0)

                if rsi > 70:
                    signals.append("RSI超买信号")
                elif rsi < 30:
                    signals.append("RSI超卖信号")

            return signals

        except Exception as e:
            logger.error(f"技术信号分析失败: {e}")
            return []

    def _generate_technical_summary(self, indicators: Dict, signals: List[str]) -> str:
        """生成技术分析摘要"""
        try:
            summary_parts = []

            # MACD摘要
            macd_data = indicators.get('macd', {})
            if macd_data:
                dif = macd_data.get('DIF', 0)
                dea = macd_data.get('DEA', 0)
                if dif > dea:
                    summary_parts.append("MACD多头")
                else:
                    summary_parts.append("MACD空头")

            # KDJ摘要
            kdj_data = indicators.get('kdj', {})
            if kdj_data:
                k = kdj_data.get('K', 0)
                if k > 80:
                    summary_parts.append("KDJ超买")
                elif k < 20:
                    summary_parts.append("KDJ超卖")
                else:
                    summary_parts.append("KDJ中性")

            # RSI摘要
            rsi_data = indicators.get('rsi', {})
            if rsi_data:
                rsi = rsi_data.get('RSI14', 0)
                if rsi > 70:
                    summary_parts.append("RSI超买")
                elif rsi < 30:
                    summary_parts.append("RSI超卖")
                else:
                    summary_parts.append("RSI中性")

            # 信号摘要
            if signals:
                summary_parts.append(f"{len(signals)}个信号")

            return ", ".join(summary_parts) if summary_parts else "技术指标中性"

        except Exception as e:
            logger.error(f"技术摘要生成失败: {e}")
            return "技术分析失败"

    def get_historical_minute_data(self, code: str, target_date: str = None, period: str = '1') -> Dict:
        """获取历史分时数据 - 支持当日完整数据和指定日期数据"""
        try:
            from datetime import datetime

            # 如果没有指定日期，使用当日
            if target_date is None:
                target_date = datetime.now().strftime('%Y%m%d')

            result = {
                'minute_data': pd.DataFrame(),
                'data_available': False,
                'data_summary': '',
                'analysis': {},
                'target_date': target_date,
                'period': period
            }

            if not self.akshare_available:
                result['data_summary'] = 'AKShare不可用'
                return result

            logger.info(f"获取股票{code}在{target_date}的{period}分钟分时数据")

            # 使用AKShare获取历史分时数据（带重试机制）
            minute_data = None

            # 判断是否为可能的交易日，调整重试策略
            from datetime import datetime
            try:
                date_obj = datetime.strptime(target_date, '%Y%m%d')
                is_likely_trading_day = self._is_trading_day(date_obj)
            except:
                is_likely_trading_day = True  # 如果解析失败，默认认为是交易日

            # 根据是否为交易日调整重试次数
            if is_likely_trading_day:
                max_retries = 3
                retry_delay = 35  # 35秒间隔
            else:
                max_retries = 1  # 非交易日只尝试1次
                retry_delay = 5   # 短间隔

            for attempt in range(max_retries):
                try:
                    if attempt > 0:
                        logger.info(f"  🔄 第 {attempt + 1} 次尝试获取分时数据...")
                        import time
                        time.sleep(retry_delay)  # 等待后重试

                    import akshare as ak
                    minute_data = ak.stock_zh_a_hist_min_em(
                        symbol=code,
                        start_date=target_date,
                        end_date=target_date,
                        period=period,  # '1', '5', '15', '30', '60'
                        adjust=''
                    )

                    if minute_data is not None and not minute_data.empty:
                        logger.info(f"  ✅ 第 {attempt + 1} 次尝试成功: {code}, {len(minute_data)}条分时数据")
                        break
                    else:
                        logger.warning(f"  ⚠️ 第 {attempt + 1} 次尝试获取到空数据: {code}")
                        # 如果是非交易日且获取到空数据，直接跳出
                        if not is_likely_trading_day:
                            logger.info(f"  📅 {target_date} 可能为非交易日，跳过重试")
                            minute_data = None
                            break
                        if attempt < max_retries - 1:
                            continue
                        else:
                            minute_data = None

                except Exception as e:
                    logger.error(f"  ❌ 第 {attempt + 1} 次尝试失败: {code}, {e}")
                    # 如果是非交易日且出现错误，直接跳出
                    if not is_likely_trading_day:
                        logger.info(f"  📅 {target_date} 可能为非交易日，跳过重试")
                        minute_data = None
                        break
                    if attempt < max_retries - 1:
                        continue
                    else:
                        minute_data = None

            # 如果AKShare失败，尝试使用备用方案
            if (minute_data is None or minute_data.empty) and self.backup_fetcher is not None:
                try:
                    logger.info(f"🔄 尝试备用方案获取分时数据: {code}")
                    backup_data = self.backup_fetcher.get_minute_data_backup(
                        code=code,
                        period=period,
                        count=240,
                        target_date=target_date
                    )

                    if backup_data is not None and not backup_data.empty:
                        # 转换备用数据格式为AKShare兼容格式
                        minute_data = self._convert_backup_data_format(backup_data)
                        logger.info(f"✅ 备用方案获取分时数据成功: {code}, {len(minute_data)}条")
                    else:
                        logger.warning(f"⚠️ 备用方案也无法获取分时数据: {code}")

                except Exception as e:
                    logger.error(f"❌ 备用方案获取分时数据失败: {code}, {e}")

            # 如果所有方案都失败
            if minute_data is None or minute_data.empty:
                result['data_summary'] = f'{target_date}无分时数据（主方案和备用方案都失败）'
                logger.error(f"所有分时数据获取方式都失败")
                return result

            # 数据预处理
            minute_data = minute_data.copy()

            # 确保时间列格式正确
            if '时间' in minute_data.columns:
                minute_data['时间'] = pd.to_datetime(minute_data['时间'])
                minute_data = minute_data.sort_values('时间')

            # 数据分析
            analysis = self._analyze_minute_data(minute_data, code, target_date)

            result.update({
                'minute_data': minute_data,
                'data_available': True,
                'data_summary': analysis.get('summary', ''),
                'analysis': analysis
            })

            logger.info(f"股票{code}: 成功获取{target_date}的{len(minute_data)}条分时数据")
            return result

        except Exception as e:
            logger.error(f"获取股票{code}历史分时数据失败: {e}")
            return {
                'minute_data': pd.DataFrame(),
                'data_available': False,
                'data_summary': f'数据获取失败: {str(e)}',
                'analysis': {'error': str(e)},
                'target_date': target_date or 'unknown',
                'period': period
            }

    def _convert_backup_data_format(self, backup_data: pd.DataFrame) -> pd.DataFrame:
        """将备用数据格式转换为AKShare兼容格式"""
        try:
            if backup_data.empty:
                return pd.DataFrame()

            # 备用数据格式: ['open', 'high', 'low', 'close', 'volume', 'amount']
            # AKShare格式: ['时间', '开盘', '收盘', '最高', '最低', '成交量', '成交额']

            converted_data = pd.DataFrame()

            # 重置索引，将时间从索引转为列
            if backup_data.index.name == 'time' or isinstance(backup_data.index, pd.DatetimeIndex):
                backup_data = backup_data.reset_index()
                time_col = 'time' if 'time' in backup_data.columns else backup_data.columns[0]
            else:
                time_col = None

            # 映射字段名
            field_mapping = {
                'time': '时间',
                'open': '开盘',
                'close': '收盘',
                'high': '最高',
                'low': '最低',
                'volume': '成交量',
                'amount': '成交额'
            }

            for backup_col, akshare_col in field_mapping.items():
                if backup_col in backup_data.columns:
                    converted_data[akshare_col] = backup_data[backup_col]

            # 确保时间列格式正确
            if '时间' in converted_data.columns:
                converted_data['时间'] = pd.to_datetime(converted_data['时间'])
                converted_data = converted_data.sort_values('时间')

            logger.info(f"✅ 备用数据格式转换成功: {len(converted_data)}条数据")
            return converted_data

        except Exception as e:
            logger.error(f"❌ 备用数据格式转换失败: {e}")
            return pd.DataFrame()

    def _analyze_minute_data(self, minute_data: pd.DataFrame, code: str, target_date: str) -> Dict:
        """分析分时数据"""
        try:
            if minute_data.empty:
                return {
                    'summary': '无分时数据',
                    'total_records': 0,
                    'time_range': '无',
                    'price_range': '无',
                    'volume_total': 0,
                    'key_features': []
                }

            # 基础统计
            total_records = len(minute_data)
            time_start = minute_data.iloc[0]['时间'] if '时间' in minute_data.columns else '未知'
            time_end = minute_data.iloc[-1]['时间'] if '时间' in minute_data.columns else '未知'

            price_min = minute_data['收盘'].min() if '收盘' in minute_data.columns else 0
            price_max = minute_data['收盘'].max() if '收盘' in minute_data.columns else 0
            volume_total = minute_data['成交量'].sum() if '成交量' in minute_data.columns else 0

            # 关键特征识别
            key_features = []

            # 检查是否包含完整交易时段
            if '时间' in minute_data.columns:
                times = pd.to_datetime(minute_data['时间'])
                morning_data = times[(times.dt.hour >= 9) & (times.dt.hour < 12)]
                afternoon_data = times[(times.dt.hour >= 13) & (times.dt.hour < 15)]

                if len(morning_data) > 0:
                    key_features.append("包含上午时段")
                if len(afternoon_data) > 0:
                    key_features.append("包含下午时段")

                if len(morning_data) > 0 and len(afternoon_data) > 0:
                    key_features.append("全天数据完整")

            # 成交量分析
            if '成交量' in minute_data.columns and len(minute_data) > 1:
                volume_mean = minute_data['成交量'].mean()
                volume_std = minute_data['成交量'].std()

                # 识别成交量异常放大的时点
                volume_threshold = volume_mean + 2 * volume_std
                high_volume_count = (minute_data['成交量'] > volume_threshold).sum()

                if high_volume_count > 0:
                    key_features.append(f"{high_volume_count}个放量时点")

            # 价格波动分析
            if '收盘' in minute_data.columns and len(minute_data) > 1:
                price_change = (price_max - price_min) / price_min * 100 if price_min > 0 else 0

                if price_change > 5:
                    key_features.append("大幅波动")
                elif price_change > 2:
                    key_features.append("中等波动")
                else:
                    key_features.append("小幅波动")

            # 生成摘要
            summary = f"{total_records}条记录, {volume_total:,}总成交量"
            if key_features:
                summary += f", {', '.join(key_features[:3])}"  # 只显示前3个特征

            return {
                'summary': summary,
                'total_records': total_records,
                'time_range': f"{time_start} 到 {time_end}",
                'price_range': f"{price_min:.2f} - {price_max:.2f}",
                'volume_total': int(volume_total),
                'key_features': key_features,
                'price_change_pct': round((price_max - price_min) / price_min * 100, 2) if price_min > 0 else 0
            }

        except Exception as e:
            logger.error(f"分时数据分析失败: {e}")
            return {
                'summary': '分析失败',
                'total_records': len(minute_data) if not minute_data.empty else 0,
                'time_range': '未知',
                'price_range': '未知',
                'volume_total': 0,
                'key_features': [],
                'error': str(e)
            }

    def _is_trading_day(self, date_obj):
        """判断是否为交易日（排除周末和主要节假日）"""
        try:
            # 基本判断：周一到周五
            weekday = date_obj.weekday()  # 0=Monday, 6=Sunday
            if weekday >= 5:  # 周六或周日
                return False

            # 简单的节假日判断（主要节假日）
            month = date_obj.month
            day = date_obj.day

            # 元旦
            if month == 1 and day == 1:
                return False

            # 春节期间（简化判断，通常1月下旬到2月中旬）
            if (month == 1 and day >= 20) or (month == 2 and day <= 15):
                return False

            # 清明节期间（4月4-6日左右）
            if month == 4 and 3 <= day <= 6:
                return False

            # 劳动节期间（5月1-3日）
            if month == 5 and 1 <= day <= 3:
                return False

            # 国庆节期间（10月1-7日）
            if month == 10 and 1 <= day <= 7:
                return False

            return True

        except Exception as e:
            logger.warning(f"交易日判断失败: {e}")
            # 如果判断失败，默认认为是交易日，让API自己处理
            return True

    def get_recent_7days_minute_data(self, code: str, period: str = '1') -> Dict:
        """获取最近7天分时数据 - 专为游资炒作周期分析设计"""
        try:
            from datetime import datetime, timedelta

            result = {
                'trading_days_data': [],
                'data_available': False,
                'total_trading_days': 0,
                'date_range': '',
                'combined_analysis': {},
                'summary': ''
            }

            if not self.akshare_available:
                result['summary'] = 'AKShare不可用'
                return result

            logger.info(f"获取股票{code}最近7天的{period}分钟分时数据")

            # 获取最近的交易日数据（最多查找10天以确保获得足够的交易日）
            end_date = datetime.now()
            trading_days_data = []
            max_days_to_check = 10  # 最多查找10天
            target_trading_days = 5  # 目标获取5个交易日

            for i in range(max_days_to_check):
                target_date_obj = end_date - timedelta(days=i)
                target_date = target_date_obj.strftime('%Y%m%d')

                # 判断是否为交易日
                if not self._is_trading_day(target_date_obj):
                    logger.info(f"跳过非交易日: {target_date} ({target_date_obj.strftime('%A')})")
                    continue

                # 如果已经获取到足够的交易日数据，停止
                if len(trading_days_data) >= target_trading_days:
                    logger.info(f"已获取到{len(trading_days_data)}个交易日数据，停止获取")
                    break

                # 获取该日期的分时数据
                day_result = self.get_historical_minute_data(code, target_date, period)

                if day_result['data_available']:
                    trading_days_data.append({
                        'date': target_date,
                        'date_formatted': (end_date - timedelta(days=i)).strftime('%Y-%m-%d'),
                        'weekday': (end_date - timedelta(days=i)).strftime('%A'),
                        'minute_data': day_result['minute_data'],
                        'analysis': day_result['analysis'],
                        'is_today': i == 0
                    })
                    logger.info(f"股票{code}: 成功获取{target_date}的分时数据")
                else:
                    logger.info(f"股票{code}: {target_date}无分时数据（可能是非交易日）")

            if trading_days_data:
                # 按日期正序排列（最早的在前）
                trading_days_data.sort(key=lambda x: x['date'])

                # 综合分析
                combined_analysis = self._analyze_7days_minute_data(trading_days_data, code)

                result.update({
                    'trading_days_data': trading_days_data,
                    'data_available': True,
                    'total_trading_days': len(trading_days_data),
                    'date_range': f"{trading_days_data[0]['date_formatted']} 到 {trading_days_data[-1]['date_formatted']}",
                    'combined_analysis': combined_analysis,
                    'summary': combined_analysis.get('summary', '')
                })

                logger.info(f"股票{code}: 成功获取{len(trading_days_data)}个交易日的分时数据")
            else:
                result['summary'] = '最近7天无交易日数据'
                logger.warning(f"股票{code}: 最近7天无可用交易日数据")

            return result

        except Exception as e:
            logger.error(f"获取股票{code}最近7天分时数据失败: {e}")
            return {
                'trading_days_data': [],
                'data_available': False,
                'total_trading_days': 0,
                'date_range': '',
                'combined_analysis': {'error': str(e)},
                'summary': f'数据获取失败: {str(e)}'
            }

    def _analyze_7days_minute_data(self, trading_days_data: List[Dict], code: str) -> Dict:
        """分析7天分时数据，专注游资炒作周期识别"""
        try:
            if not trading_days_data:
                return {
                    'summary': '无7天数据可分析',
                    'trading_pattern': '无',
                    'volume_trend': '无',
                    'price_trend': '无',
                    'gaming_capital_signals': [],
                    'cycle_stage': '无法判断'
                }

            # 1. 基础统计
            total_days = len(trading_days_data)
            total_records = sum(len(day['minute_data']) for day in trading_days_data)

            # 2. 成交量趋势分析
            daily_volumes = []
            daily_prices = []

            for day_data in trading_days_data:
                minute_data = day_data['minute_data']
                if not minute_data.empty and '成交量' in minute_data.columns:
                    daily_volume = minute_data['成交量'].sum()
                    daily_volumes.append(daily_volume)

                    if '收盘' in minute_data.columns:
                        daily_close = minute_data['收盘'].iloc[-1]
                        daily_prices.append(daily_close)

            # 3. 游资信号识别
            gaming_signals = []

            # 检查连续放量
            if len(daily_volumes) >= 3:
                recent_avg = sum(daily_volumes[-3:]) / 3
                earlier_avg = sum(daily_volumes[:-3]) / max(1, len(daily_volumes) - 3) if len(daily_volumes) > 3 else recent_avg

                if recent_avg > earlier_avg * 1.5:
                    gaming_signals.append("近期连续放量")
                elif recent_avg < earlier_avg * 0.7:
                    gaming_signals.append("近期成交萎缩")

            # 检查价格趋势
            if len(daily_prices) >= 3:
                price_changes = []
                for i in range(1, len(daily_prices)):
                    change = (daily_prices[i] - daily_prices[i-1]) / daily_prices[i-1] * 100
                    price_changes.append(change)

                positive_days = sum(1 for change in price_changes if change > 0)
                if positive_days >= len(price_changes) * 0.7:
                    gaming_signals.append("价格持续上涨")
                elif positive_days <= len(price_changes) * 0.3:
                    gaming_signals.append("价格持续下跌")

            # 检查单日异常
            for day_data in trading_days_data:
                minute_data = day_data['minute_data']
                if not minute_data.empty:
                    # 检查单日振幅
                    if all(col in minute_data.columns for col in ['最高', '最低', '收盘']):
                        day_high = minute_data['最高'].max()
                        day_low = minute_data['最低'].min()
                        day_close = minute_data['收盘'].iloc[-1]

                        amplitude = (day_high - day_low) / day_low * 100
                        if amplitude > 8:  # 振幅超过8%
                            gaming_signals.append(f"{day_data['date_formatted']}大幅波动({amplitude:.1f}%)")

            # 4. 炒作周期判断
            cycle_stage = '无法判断'
            if gaming_signals:
                if any('连续放量' in signal for signal in gaming_signals):
                    if any('持续上涨' in signal for signal in gaming_signals):
                        cycle_stage = '加速期'
                    else:
                        cycle_stage = '启动期'
                elif any('成交萎缩' in signal for signal in gaming_signals):
                    if any('持续下跌' in signal for signal in gaming_signals):
                        cycle_stage = '衰退期'
                    else:
                        cycle_stage = '整理期'
                elif any('大幅波动' in signal for signal in gaming_signals):
                    cycle_stage = '高潮期'

            # 5. 成交量趋势
            volume_trend = '平稳'
            if len(daily_volumes) >= 2:
                if daily_volumes[-1] > daily_volumes[0] * 1.3:
                    volume_trend = '放量'
                elif daily_volumes[-1] < daily_volumes[0] * 0.7:
                    volume_trend = '缩量'

            # 6. 价格趋势
            price_trend = '震荡'
            if len(daily_prices) >= 2:
                total_change = (daily_prices[-1] - daily_prices[0]) / daily_prices[0] * 100
                if total_change > 5:
                    price_trend = '上涨'
                elif total_change < -5:
                    price_trend = '下跌'

            # 7. 生成摘要
            summary = f"{total_days}个交易日, {cycle_stage}, {volume_trend}, {price_trend}"
            if gaming_signals:
                summary += f", {len(gaming_signals)}个游资信号"

            return {
                'summary': summary,
                'trading_pattern': cycle_stage,
                'volume_trend': volume_trend,
                'price_trend': price_trend,
                'gaming_capital_signals': gaming_signals,
                'cycle_stage': cycle_stage,
                'total_days': total_days,
                'total_records': total_records,
                'daily_volumes': daily_volumes,
                'daily_prices': daily_prices
            }

        except Exception as e:
            logger.error(f"7天分时数据分析失败: {e}")
            return {
                'summary': '分析失败',
                'trading_pattern': '分析错误',
                'volume_trend': '未知',
                'price_trend': '未知',
                'gaming_capital_signals': [],
                'cycle_stage': '无法判断',
                'error': str(e)
            }

    def analyze_minute_data_patterns(self, minute_data: pd.DataFrame, target_date: str = None) -> Dict:
        """深度分析分时数据模式 - 专注游资操作手法识别"""
        try:
            if minute_data.empty:
                return {
                    'analysis_available': False,
                    'summary': '无分时数据可分析',
                    'patterns': {},
                    'gaming_signals': [],
                    'key_timepoints': [],
                    'recommendations': []
                }

            result = {
                'analysis_available': True,
                'summary': '',
                'patterns': {},
                'gaming_signals': [],
                'key_timepoints': [],
                'recommendations': [],
                'target_date': target_date or 'unknown'
            }

            # 1. 拉升形态识别
            pullup_patterns = self._identify_pullup_patterns(minute_data)
            result['patterns']['pullup'] = pullup_patterns

            # 2. 关键时点分析
            key_timepoints = self._analyze_key_timepoints(minute_data)
            result['key_timepoints'] = key_timepoints

            # 3. 异常成交识别
            volume_anomalies = self._identify_volume_anomalies(minute_data)
            result['patterns']['volume_anomalies'] = volume_anomalies

            # 4. 游资手法识别
            gaming_techniques = self._identify_gaming_techniques(minute_data)
            result['patterns']['gaming_techniques'] = gaming_techniques

            # 5. 综合游资信号
            gaming_signals = []
            gaming_signals.extend(pullup_patterns.get('signals', []))
            gaming_signals.extend(key_timepoints.get('signals', []))
            gaming_signals.extend(volume_anomalies.get('signals', []))
            gaming_signals.extend(gaming_techniques.get('signals', []))
            result['gaming_signals'] = gaming_signals

            # 6. 生成操作建议
            recommendations = self._generate_trading_recommendations(result)
            result['recommendations'] = recommendations

            # 7. 生成分析摘要
            summary = self._generate_pattern_summary(result)
            result['summary'] = summary

            logger.info(f"分时数据模式分析完成: {len(gaming_signals)}个游资信号")
            return result

        except Exception as e:
            logger.error(f"分时数据模式分析失败: {e}")
            return {
                'analysis_available': False,
                'summary': f'分析失败: {str(e)}',
                'patterns': {},
                'gaming_signals': [],
                'key_timepoints': [],
                'recommendations': [],
                'error': str(e)
            }

    def _identify_pullup_patterns(self, minute_data: pd.DataFrame) -> Dict:
        """识别拉升形态"""
        try:
            patterns = {
                'patterns_found': [],
                'signals': [],
                'details': {}
            }

            if not all(col in minute_data.columns for col in ['时间', '收盘', '成交量']):
                return patterns

            # 确保时间列是datetime类型
            if minute_data['时间'].dtype == 'object':
                minute_data['时间'] = pd.to_datetime(minute_data['时间'])

            # 计算价格变化率
            minute_data['price_change_pct'] = minute_data['收盘'].pct_change() * 100

            # 1. 识别90度拉板（短时间内大幅拉升）
            rapid_pullups = []
            for i in range(5, len(minute_data)):
                # 检查5分钟内的涨幅
                window_data = minute_data.iloc[i-4:i+1]
                total_change = (window_data['收盘'].iloc[-1] - window_data['收盘'].iloc[0]) / window_data['收盘'].iloc[0] * 100

                if total_change > 3:  # 5分钟内涨幅超过3%
                    rapid_pullups.append({
                        'time': window_data['时间'].iloc[-1].strftime('%H:%M'),
                        'change_pct': round(total_change, 2),
                        'volume': window_data['成交量'].sum()
                    })

            if rapid_pullups:
                patterns['patterns_found'].append('90度拉板')
                patterns['signals'].append(f'发现{len(rapid_pullups)}次快速拉升')
                patterns['details']['rapid_pullups'] = rapid_pullups

            # 2. 识别斜刺波（持续温和拉升）
            sustained_rises = 0
            for i in range(10, len(minute_data)):
                # 检查10分钟内的持续上涨
                window_data = minute_data.iloc[i-9:i+1]
                positive_changes = (window_data['price_change_pct'] > 0).sum()

                if positive_changes >= 7:  # 10分钟内有7分钟上涨
                    sustained_rises += 1

            if sustained_rises > 0:
                patterns['patterns_found'].append('斜刺波')
                patterns['signals'].append(f'发现{sustained_rises}次持续拉升')
                patterns['details']['sustained_rises'] = sustained_rises

            # 3. 识别阶梯式拉升（分步骤拉升）
            step_pullups = []
            current_step = None

            for i in range(1, len(minute_data)):
                change = minute_data['price_change_pct'].iloc[i]

                if change > 1:  # 单分钟涨幅超过1%
                    if current_step is None:
                        current_step = {
                            'start_time': minute_data['时间'].iloc[i].strftime('%H:%M'),
                            'steps': 1,
                            'total_change': change
                        }
                    else:
                        current_step['steps'] += 1
                        current_step['total_change'] += change
                elif current_step and current_step['steps'] >= 3:
                    # 结束当前阶梯
                    current_step['end_time'] = minute_data['时间'].iloc[i-1].strftime('%H:%M')
                    step_pullups.append(current_step)
                    current_step = None
                else:
                    current_step = None

            if step_pullups:
                patterns['patterns_found'].append('阶梯式拉升')
                patterns['signals'].append(f'发现{len(step_pullups)}次阶梯拉升')
                patterns['details']['step_pullups'] = step_pullups

            return patterns

        except Exception as e:
            logger.error(f"拉升形态识别失败: {e}")
            return {
                'patterns_found': [],
                'signals': [],
                'details': {},
                'error': str(e)
            }

    def _analyze_key_timepoints(self, minute_data: pd.DataFrame) -> Dict:
        """分析关键时点"""
        try:
            timepoints = {
                'opening_analysis': {},
                'closing_analysis': {},
                'hourly_analysis': {},
                'signals': []
            }

            if not all(col in minute_data.columns for col in ['时间', '收盘', '成交量']):
                return timepoints

            # 确保时间列是datetime类型
            if minute_data['时间'].dtype == 'object':
                minute_data['时间'] = pd.to_datetime(minute_data['时间'])

            minute_data['hour'] = minute_data['时间'].dt.hour
            minute_data['minute'] = minute_data['时间'].dt.minute

            # 1. 开盘分析（9:30-9:45）
            opening_data = minute_data[(minute_data['hour'] == 9) & (minute_data['minute'] >= 30) & (minute_data['minute'] <= 45)]
            if not opening_data.empty:
                opening_volume = opening_data['成交量'].sum()
                opening_change = (opening_data['收盘'].iloc[-1] - opening_data['收盘'].iloc[0]) / opening_data['收盘'].iloc[0] * 100

                timepoints['opening_analysis'] = {
                    'volume': int(opening_volume),
                    'price_change': round(opening_change, 2),
                    'pattern': '强势开盘' if opening_change > 2 else '弱势开盘' if opening_change < -2 else '平稳开盘'
                }

                if opening_volume > minute_data['成交量'].mean() * 3:
                    timepoints['signals'].append('开盘放量异常')
                if abs(opening_change) > 3:
                    timepoints['signals'].append(f'开盘价格异动({opening_change:.1f}%)')

            # 2. 收盘分析（14:45-15:00）
            closing_data = minute_data[(minute_data['hour'] == 14) & (minute_data['minute'] >= 45) |
                                     (minute_data['hour'] == 15) & (minute_data['minute'] == 0)]
            if not closing_data.empty:
                closing_volume = closing_data['成交量'].sum()
                closing_change = (closing_data['收盘'].iloc[-1] - closing_data['收盘'].iloc[0]) / closing_data['收盘'].iloc[0] * 100

                timepoints['closing_analysis'] = {
                    'volume': int(closing_volume),
                    'price_change': round(closing_change, 2),
                    'pattern': '尾盘拉升' if closing_change > 1 else '尾盘砸盘' if closing_change < -1 else '尾盘平稳'
                }

                if closing_volume > minute_data['成交量'].mean() * 2:
                    timepoints['signals'].append('尾盘放量')
                if closing_change > 2:
                    timepoints['signals'].append('尾盘拉升')
                elif closing_change < -2:
                    timepoints['signals'].append('尾盘砸盘')

            # 3. 整点分析
            hourly_volumes = {}
            for hour in [10, 11, 13, 14]:
                hour_data = minute_data[minute_data['hour'] == hour]
                if not hour_data.empty:
                    hourly_volumes[f'{hour}:00'] = int(hour_data['成交量'].sum())

            timepoints['hourly_analysis'] = hourly_volumes

            # 识别整点异常
            if hourly_volumes:
                avg_hourly_volume = sum(hourly_volumes.values()) / len(hourly_volumes)
                for time_point, volume in hourly_volumes.items():
                    if volume > avg_hourly_volume * 1.5:
                        timepoints['signals'].append(f'{time_point}整点放量')

            return timepoints

        except Exception as e:
            logger.error(f"关键时点分析失败: {e}")
            return {
                'opening_analysis': {},
                'closing_analysis': {},
                'hourly_analysis': {},
                'signals': [],
                'error': str(e)
            }

    def _identify_volume_anomalies(self, minute_data: pd.DataFrame) -> Dict:
        """识别成交量异常"""
        try:
            anomalies = {
                'volume_spikes': [],
                'volume_patterns': [],
                'signals': []
            }

            if '成交量' not in minute_data.columns:
                return anomalies

            volume_mean = minute_data['成交量'].mean()
            volume_std = minute_data['成交量'].std()

            # 1. 识别成交量异常放大
            spike_threshold = volume_mean + 2 * volume_std
            volume_spikes = []

            for i, row in minute_data.iterrows():
                if row['成交量'] > spike_threshold:
                    volume_spikes.append({
                        'time': row['时间'].strftime('%H:%M') if pd.notna(row['时间']) else 'unknown',
                        'volume': int(row['成交量']),
                        'multiple': round(row['成交量'] / volume_mean, 1)
                    })

            anomalies['volume_spikes'] = volume_spikes
            if len(volume_spikes) > 0:
                anomalies['signals'].append(f'发现{len(volume_spikes)}次成交量异常放大')

            # 2. 识别成交量模式
            # 连续放量
            consecutive_high_volume = 0
            max_consecutive = 0

            for volume in minute_data['成交量']:
                if volume > volume_mean * 1.2:
                    consecutive_high_volume += 1
                    max_consecutive = max(max_consecutive, consecutive_high_volume)
                else:
                    consecutive_high_volume = 0

            if max_consecutive >= 5:
                anomalies['volume_patterns'].append(f'连续{max_consecutive}分钟放量')
                anomalies['signals'].append('连续放量模式')

            # 成交量递增
            volume_trend = minute_data['成交量'].rolling(window=10).mean()
            if len(volume_trend) > 20:
                recent_trend = volume_trend.iloc[-10:].mean()
                earlier_trend = volume_trend.iloc[-20:-10].mean()

                if recent_trend > earlier_trend * 1.3:
                    anomalies['volume_patterns'].append('成交量递增趋势')
                    anomalies['signals'].append('成交量趋势性放大')

            return anomalies

        except Exception as e:
            logger.error(f"成交量异常识别失败: {e}")
            return {
                'volume_spikes': [],
                'volume_patterns': [],
                'signals': [],
                'error': str(e)
            }

    def _identify_gaming_techniques(self, minute_data: pd.DataFrame) -> Dict:
        """识别游资操作手法"""
        try:
            techniques = {
                'techniques_found': [],
                'signals': [],
                'details': {}
            }

            if not all(col in minute_data.columns for col in ['时间', '收盘', '成交量']):
                return techniques

            # 1. 试盘手法识别（小幅拉升后回落）
            probe_patterns = []
            for i in range(5, len(minute_data) - 5):
                # 检查拉升后回落模式
                before_price = minute_data['收盘'].iloc[i-5:i].mean()
                peak_price = minute_data['收盘'].iloc[i]
                after_price = minute_data['收盘'].iloc[i+1:i+6].mean()

                pullup = (peak_price - before_price) / before_price * 100
                pullback = (after_price - peak_price) / peak_price * 100

                if pullup > 1 and pullback < -0.5:  # 拉升超过1%后回落
                    probe_patterns.append({
                        'time': minute_data['时间'].iloc[i].strftime('%H:%M'),
                        'pullup_pct': round(pullup, 2),
                        'pullback_pct': round(pullback, 2)
                    })

            if probe_patterns:
                techniques['techniques_found'].append('试盘手法')
                techniques['signals'].append(f'发现{len(probe_patterns)}次试盘行为')
                techniques['details']['probe_patterns'] = probe_patterns

            # 2. 洗盘手法识别（震荡整理）
            consolidation_periods = 0
            for i in range(20, len(minute_data)):
                window_data = minute_data.iloc[i-19:i+1]
                price_std = window_data['收盘'].std()
                price_mean = window_data['收盘'].mean()

                if price_std / price_mean < 0.01:  # 价格波动很小
                    consolidation_periods += 1

            if consolidation_periods > 10:
                techniques['techniques_found'].append('洗盘整理')
                techniques['signals'].append('发现洗盘整理模式')
                techniques['details']['consolidation_periods'] = consolidation_periods

            # 3. 出货手法识别（高位放量下跌）
            distribution_signals = []
            if len(minute_data) > 30:
                recent_high = minute_data['收盘'].iloc[-30:].max()

                for i in range(len(minute_data) - 10, len(minute_data)):
                    if minute_data['收盘'].iloc[i] >= recent_high * 0.95:  # 接近高点
                        volume_ratio = minute_data['成交量'].iloc[i] / minute_data['成交量'].mean()
                        price_change = (minute_data['收盘'].iloc[i] - minute_data['收盘'].iloc[i-1]) / minute_data['收盘'].iloc[i-1] * 100

                        if volume_ratio > 2 and price_change < -1:  # 高位放量下跌
                            distribution_signals.append({
                                'time': minute_data['时间'].iloc[i].strftime('%H:%M'),
                                'volume_ratio': round(volume_ratio, 1),
                                'price_change': round(price_change, 2)
                            })

            if distribution_signals:
                techniques['techniques_found'].append('出货手法')
                techniques['signals'].append(f'发现{len(distribution_signals)}次出货信号')
                techniques['details']['distribution_signals'] = distribution_signals

            return techniques

        except Exception as e:
            logger.error(f"游资手法识别失败: {e}")
            return {
                'techniques_found': [],
                'signals': [],
                'details': {},
                'error': str(e)
            }

    def _generate_trading_recommendations(self, analysis_result: Dict) -> List[Dict]:
        """基于分析结果生成操作建议"""
        try:
            recommendations = []

            gaming_signals = analysis_result.get('gaming_signals', [])
            patterns = analysis_result.get('patterns', {})

            # 1. 基于拉升形态的建议
            pullup_patterns = patterns.get('pullup', {}).get('patterns_found', [])
            if '90度拉板' in pullup_patterns:
                recommendations.append({
                    'type': '买入信号',
                    'reason': '发现90度拉板形态',
                    'action': '可考虑适量跟进',
                    'risk': '注意追高风险，设置止损'
                })

            if '斜刺波' in pullup_patterns:
                recommendations.append({
                    'type': '持有信号',
                    'reason': '发现斜刺波持续拉升',
                    'action': '持股待涨，关注量价配合',
                    'risk': '注意拉升持续性'
                })

            # 2. 基于游资手法的建议
            gaming_techniques = patterns.get('gaming_techniques', {}).get('techniques_found', [])
            if '试盘手法' in gaming_techniques:
                recommendations.append({
                    'type': '观望信号',
                    'reason': '发现游资试盘行为',
                    'action': '等待明确方向，暂时观望',
                    'risk': '试盘后可能变盘'
                })

            if '出货手法' in gaming_techniques:
                recommendations.append({
                    'type': '卖出信号',
                    'reason': '发现游资出货迹象',
                    'action': '建议减仓或止盈',
                    'risk': '高位出货，警惕下跌'
                })

            # 3. 基于成交量异常的建议
            volume_anomalies = patterns.get('volume_anomalies', {})
            if '连续放量模式' in volume_anomalies.get('signals', []):
                recommendations.append({
                    'type': '关注信号',
                    'reason': '连续放量，资金关注度高',
                    'action': '密切关注后续走势',
                    'risk': '放量后需防范回调'
                })

            # 4. 综合建议
            if len(gaming_signals) >= 3:
                recommendations.append({
                    'type': '综合评估',
                    'reason': f'发现{len(gaming_signals)}个游资信号',
                    'action': '游资活跃，谨慎操作',
                    'risk': '游资操作增加波动性'
                })
            elif len(gaming_signals) == 0:
                recommendations.append({
                    'type': '综合评估',
                    'reason': '暂无明显游资信号',
                    'action': '按技术分析操作',
                    'risk': '缺乏主力资金推动'
                })

            return recommendations

        except Exception as e:
            logger.error(f"生成操作建议失败: {e}")
            return [{
                'type': '系统错误',
                'reason': '建议生成失败',
                'action': '请手动分析',
                'risk': '系统分析不可用'
            }]

    def _generate_pattern_summary(self, analysis_result: Dict) -> str:
        """生成分析摘要"""
        try:
            summary_parts = []

            patterns = analysis_result.get('patterns', {})
            gaming_signals = analysis_result.get('gaming_signals', [])

            # 拉升形态摘要
            pullup_patterns = patterns.get('pullup', {}).get('patterns_found', [])
            if pullup_patterns:
                summary_parts.append(f"拉升形态: {', '.join(pullup_patterns)}")

            # 游资手法摘要
            gaming_techniques = patterns.get('gaming_techniques', {}).get('techniques_found', [])
            if gaming_techniques:
                summary_parts.append(f"游资手法: {', '.join(gaming_techniques)}")

            # 成交量异常摘要
            volume_signals = patterns.get('volume_anomalies', {}).get('signals', [])
            if volume_signals:
                summary_parts.append(f"成交量: {len(volume_signals)}个异常")

            # 关键时点摘要
            timepoint_signals = analysis_result.get('key_timepoints', {}).get('signals', [])
            if timepoint_signals:
                summary_parts.append(f"关键时点: {len(timepoint_signals)}个信号")

            # 总体摘要
            if summary_parts:
                summary = f"{', '.join(summary_parts)}, 共{len(gaming_signals)}个游资信号"
            else:
                summary = "无明显游资操作特征"

            return summary

        except Exception as e:
            logger.error(f"生成分析摘要失败: {e}")
            return "摘要生成失败"

    def get_tick_data(self, code: str, trade_date: str = None) -> Dict:
        """获取逐笔成交数据 - 智能切换实时/历史数据"""
        try:
            from datetime import datetime, time

            current_time = datetime.now()
            current_weekday = current_time.weekday()  # 0=Monday, 6=Sunday
            current_hour_minute = current_time.time()

            # 判断是否为交易时间
            is_trading_day = current_weekday < 5  # 周一到周五
            is_trading_hours = (
                (time(9, 30) <= current_hour_minute <= time(11, 30)) or  # 上午交易时间
                (time(13, 0) <= current_hour_minute <= time(15, 0))      # 下午交易时间
            )

            # 确定目标日期
            if trade_date is None:
                if is_trading_day and is_trading_hours:
                    target_date = current_time.strftime('%Y%m%d')
                    data_type = "实时"
                    logger.info(f"交易时间内，获取{code}当日实时逐笔成交数据")
                else:
                    target_date = current_time.strftime('%Y%m%d')
                    data_type = "历史"
                    logger.info(f"非交易时间，获取{code}当日历史逐笔成交数据")
            else:
                target_date = trade_date
                data_type = "历史"
                logger.info(f"获取{code}指定日期{target_date}的逐笔成交数据")

            result = {
                'data_available': False,
                'tick_data': pd.DataFrame(),
                'target_date': target_date,
                'data_type': data_type,
                'total_records': 0,
                'analysis': {},
                'summary': ''
            }

            if not self.akshare_available:
                result['summary'] = 'AKShare不可用'
                return result

            # 获取逐笔成交数据
            tick_data = self._fetch_tick_data_from_source(code, target_date)

            if tick_data is not None and not tick_data.empty:
                # 数据处理和分析
                processed_data = self._process_tick_data(tick_data, code, target_date)

                result.update({
                    'data_available': True,
                    'tick_data': processed_data['tick_data'],
                    'total_records': len(processed_data['tick_data']),
                    'analysis': processed_data['analysis'],
                    'summary': processed_data['summary']
                })

                logger.info(f"股票{code}: 成功获取{target_date}的{len(processed_data['tick_data'])}条逐笔成交数据")
            else:
                result['summary'] = f'{target_date}无逐笔成交数据'
                logger.warning(f"股票{code}: {target_date}无逐笔成交数据")

            return result

        except Exception as e:
            logger.error(f"获取股票{code}逐笔成交数据失败: {e}")
            return {
                'data_available': False,
                'tick_data': pd.DataFrame(),
                'target_date': trade_date or datetime.now().strftime('%Y%m%d'),
                'data_type': '未知',
                'total_records': 0,
                'analysis': {'error': str(e)},
                'summary': f'逐笔成交数据获取失败: {str(e)}'
            }

    def _fetch_tick_data_from_source(self, code: str, target_date: str) -> pd.DataFrame:
        """从数据源获取逐笔成交数据 - 仅使用真实API，控制数据量在2000条以内"""
        try:
            import akshare as ak

            # 确定股票代码格式
            if code.startswith('00') or code.startswith('30'):
                symbol = f"sz{code}"  # 深圳
            else:
                symbol = f"sh{code}"  # 上海

            logger.info(f"从AKShare获取{symbol}的逐笔成交数据（控制在2000条以内）")

            # 使用腾讯财经逐笔成交数据接口
            try:
                tick_data = ak.stock_zh_a_tick_tx_js(symbol=symbol)

                if tick_data is not None and not tick_data.empty:
                    original_count = len(tick_data)
                    logger.info(f"AKShare获取逐笔成交数据成功: {original_count}条记录")

                    # 数据量控制：优先保留游资信号相关的逐笔数据
                    controlled_tick_data = self._control_tick_data_volume(tick_data, max_records=2000)

                    final_count = len(controlled_tick_data)
                    logger.info(f"逐笔成交数据量控制完成: {original_count}条 -> {final_count}条")

                    return controlled_tick_data
                else:
                    logger.warning(f"AKShare获取逐笔成交数据为空")
                    return pd.DataFrame()
            except Exception as e:
                logger.error(f"AKShare逐笔成交接口失败: {e}")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"从数据源获取逐笔成交数据失败: {e}")
            return pd.DataFrame()



    def _process_tick_data(self, tick_data: pd.DataFrame, code: str, target_date: str) -> Dict:
        """处理逐笔成交数据并进行分析 - 适配真实API数据格式"""
        try:
            if tick_data.empty:
                return {
                    'tick_data': pd.DataFrame(),
                    'analysis': {},
                    'summary': '无逐笔成交数据'
                }

            # 数据标准化处理 - 适配腾讯财经API格式
            processed_data = tick_data.copy()

            # 腾讯财经API返回的列名：['成交时间', '成交价格', '价格变动', '成交量', '成交金额', '性质']
            column_mapping = {
                '成交时间': '时间',
                '成交价格': '价格',
                '成交量': '成交量',
                '成交金额': '成交额',
                '性质': '买卖方向'
            }

            for old_col, new_col in column_mapping.items():
                if old_col in processed_data.columns:
                    processed_data = processed_data.rename(columns={old_col: new_col})

            # 处理买卖方向标识 - 腾讯财经格式转换
            if '买卖方向' in processed_data.columns:
                direction_mapping = {
                    '买盘': 'B',
                    '卖盘': 'S',
                    '中性盘': 'N',
                    '买': 'B',
                    '卖': 'S',
                    '中': 'N'
                }
                processed_data['买卖方向'] = processed_data['买卖方向'].map(direction_mapping).fillna('N')

            # 数据类型转换
            if '价格' in processed_data.columns:
                processed_data['价格'] = pd.to_numeric(processed_data['价格'], errors='coerce')
            if '成交量' in processed_data.columns:
                processed_data['成交量'] = pd.to_numeric(processed_data['成交量'], errors='coerce')
            if '成交额' in processed_data.columns:
                processed_data['成交额'] = pd.to_numeric(processed_data['成交额'], errors='coerce')

            # 确保成交额正确计算
            if '成交额' not in processed_data.columns and '价格' in processed_data.columns and '成交量' in processed_data.columns:
                processed_data['成交额'] = processed_data['价格'] * processed_data['成交量']

            # 添加序号
            processed_data['序号'] = range(1, len(processed_data) + 1)

            # 验证数据完整性
            required_columns = ['时间', '价格', '成交量', '成交额']
            missing_columns = [col for col in required_columns if col not in processed_data.columns]

            if missing_columns:
                logger.error(f"逐笔成交数据缺少必要列: {missing_columns}")
                return {
                    'tick_data': pd.DataFrame(),
                    'analysis': {'error': f'缺少必要列: {missing_columns}'},
                    'summary': f'数据格式错误: 缺少{missing_columns}'
                }

            # 进行深度分析
            analysis = self._analyze_tick_data(processed_data, code, target_date)

            # 生成摘要
            summary = self._generate_tick_summary(analysis, len(processed_data))

            logger.info(f"逐笔成交数据处理完成: {len(processed_data)}条记录")
            return {
                'tick_data': processed_data,
                'analysis': analysis,
                'summary': summary
            }

        except Exception as e:
            logger.error(f"处理逐笔成交数据失败: {e}")
            return {
                'tick_data': pd.DataFrame(),
                'analysis': {'error': str(e)},
                'summary': f'数据处理失败: {str(e)}'
            }

    def _analyze_tick_data(self, tick_data: pd.DataFrame, code: str, target_date: str) -> Dict:
        """分析逐笔成交数据 - 专注游资操作手法识别"""
        try:
            analysis = {
                'basic_stats': {},
                'large_orders': [],
                'trading_patterns': {},
                'time_distribution': {},
                'price_impact': {},
                'gaming_signals': []
            }

            if tick_data.empty:
                return analysis

            # 1. 基础统计
            total_records = len(tick_data)
            total_volume = tick_data['成交量'].sum() if '成交量' in tick_data.columns else 0
            total_amount = tick_data['成交额'].sum() if '成交额' in tick_data.columns else 0
            avg_price = tick_data['价格'].mean() if '价格' in tick_data.columns else 0

            analysis['basic_stats'] = {
                'total_records': total_records,
                'total_volume': int(total_volume),
                'total_amount': float(total_amount),
                'avg_price': round(avg_price, 2),
                'price_range': f"{tick_data['价格'].min():.2f} - {tick_data['价格'].max():.2f}" if '价格' in tick_data.columns else '未知'
            }

            # 2. 大单异动识别（>20万元）
            if '成交额' in tick_data.columns:
                large_orders = tick_data[tick_data['成交额'] > 200000].copy()
                if not large_orders.empty:
                    large_orders_list = []
                    for _, order in large_orders.iterrows():
                        large_orders_list.append({
                            '时间': order.get('时间', '未知'),
                            '价格': round(order.get('价格', 0), 2),
                            '成交量': int(order.get('成交量', 0)),
                            '成交额': round(order.get('成交额', 0), 2),
                            '买卖方向': order.get('买卖方向', 'N')
                        })
                    analysis['large_orders'] = large_orders_list
                    analysis['gaming_signals'].append(f'发现{len(large_orders_list)}笔大单异动(>20万)')

            # 3. 时间分布分析
            if '时间' in tick_data.columns:
                time_analysis = self._analyze_time_distribution(tick_data)
                analysis['time_distribution'] = time_analysis

                # 识别关键时点异常
                if time_analysis.get('peak_periods'):
                    analysis['gaming_signals'].append(f"关键时点成交活跃: {', '.join(time_analysis['peak_periods'])}")

            # 4. 买卖方向分析
            if '买卖方向' in tick_data.columns:
                direction_analysis = self._analyze_trading_direction(tick_data)
                analysis['trading_patterns'] = direction_analysis

                # 识别主动买入/卖出异常
                if direction_analysis.get('buy_ratio', 0) > 0.7:
                    analysis['gaming_signals'].append('主动买入占比异常高')
                elif direction_analysis.get('sell_ratio', 0) > 0.7:
                    analysis['gaming_signals'].append('主动卖出占比异常高')

            # 5. 价格冲击分析
            if '价格' in tick_data.columns and len(tick_data) > 1:
                price_impact = self._analyze_price_impact(tick_data)
                analysis['price_impact'] = price_impact

                if price_impact.get('max_single_impact', 0) > 2:
                    analysis['gaming_signals'].append(f"发现单笔大幅价格冲击({price_impact['max_single_impact']:.1f}%)")

            return analysis

        except Exception as e:
            logger.error(f"分析逐笔成交数据失败: {e}")
            return {
                'basic_stats': {},
                'large_orders': [],
                'trading_patterns': {},
                'time_distribution': {},
                'price_impact': {},
                'gaming_signals': [],
                'error': str(e)
            }

    def _analyze_time_distribution(self, tick_data: pd.DataFrame) -> Dict:
        """分析逐笔成交的时间分布"""
        try:
            time_analysis = {
                'hourly_distribution': {},
                'peak_periods': [],
                'quiet_periods': []
            }

            if '时间' not in tick_data.columns:
                return time_analysis

            # 提取小时信息
            tick_data['hour'] = pd.to_datetime(tick_data['时间'], format='%H:%M:%S').dt.hour

            # 按小时统计成交笔数
            hourly_counts = tick_data['hour'].value_counts().sort_index()
            time_analysis['hourly_distribution'] = hourly_counts.to_dict()

            # 识别高峰和低谷时段
            mean_count = hourly_counts.mean()
            std_count = hourly_counts.std()

            for hour, count in hourly_counts.items():
                if count > mean_count + std_count:
                    time_analysis['peak_periods'].append(f"{hour}:00-{hour+1}:00")
                elif count < mean_count - std_count:
                    time_analysis['quiet_periods'].append(f"{hour}:00-{hour+1}:00")

            return time_analysis

        except Exception as e:
            logger.error(f"时间分布分析失败: {e}")
            return {'error': str(e)}

    def _analyze_trading_direction(self, tick_data: pd.DataFrame) -> Dict:
        """分析买卖方向分布"""
        try:
            direction_analysis = {
                'buy_count': 0,
                'sell_count': 0,
                'neutral_count': 0,
                'buy_ratio': 0,
                'sell_ratio': 0,
                'neutral_ratio': 0
            }

            if '买卖方向' not in tick_data.columns:
                return direction_analysis

            # 统计买卖方向
            direction_counts = tick_data['买卖方向'].value_counts()

            buy_count = direction_counts.get('B', 0)
            sell_count = direction_counts.get('S', 0)
            neutral_count = direction_counts.get('N', 0)
            total_count = len(tick_data)

            direction_analysis.update({
                'buy_count': int(buy_count),
                'sell_count': int(sell_count),
                'neutral_count': int(neutral_count),
                'buy_ratio': round(buy_count / total_count, 3) if total_count > 0 else 0,
                'sell_ratio': round(sell_count / total_count, 3) if total_count > 0 else 0,
                'neutral_ratio': round(neutral_count / total_count, 3) if total_count > 0 else 0
            })

            return direction_analysis

        except Exception as e:
            logger.error(f"买卖方向分析失败: {e}")
            return {'error': str(e)}

    def _analyze_price_impact(self, tick_data: pd.DataFrame) -> Dict:
        """分析价格冲击"""
        try:
            price_impact = {
                'max_single_impact': 0,
                'avg_price_change': 0,
                'volatility': 0
            }

            if '价格' not in tick_data.columns or len(tick_data) < 2:
                return price_impact

            # 计算价格变化
            tick_data['price_change'] = tick_data['价格'].pct_change() * 100

            # 最大单笔价格冲击
            max_impact = abs(tick_data['price_change']).max()
            price_impact['max_single_impact'] = round(max_impact, 2) if not pd.isna(max_impact) else 0

            # 平均价格变化
            avg_change = tick_data['price_change'].mean()
            price_impact['avg_price_change'] = round(avg_change, 3) if not pd.isna(avg_change) else 0

            # 价格波动率
            volatility = tick_data['price_change'].std()
            price_impact['volatility'] = round(volatility, 3) if not pd.isna(volatility) else 0

            return price_impact

        except Exception as e:
            logger.error(f"价格冲击分析失败: {e}")
            return {'error': str(e)}

    def _generate_tick_summary(self, analysis: Dict, total_records: int) -> str:
        """生成逐笔成交数据摘要"""
        try:
            summary_parts = []

            # 基础信息
            summary_parts.append(f"{total_records}笔成交")

            # 大单信息
            large_orders_count = len(analysis.get('large_orders', []))
            if large_orders_count > 0:
                summary_parts.append(f"{large_orders_count}笔大单")

            # 买卖方向
            trading_patterns = analysis.get('trading_patterns', {})
            buy_ratio = trading_patterns.get('buy_ratio', 0)
            if buy_ratio > 0.6:
                summary_parts.append("主动买入为主")
            elif buy_ratio < 0.4:
                summary_parts.append("主动卖出为主")

            # 游资信号
            gaming_signals = analysis.get('gaming_signals', [])
            if gaming_signals:
                summary_parts.append(f"{len(gaming_signals)}个异常信号")

            return ', '.join(summary_parts) if summary_parts else '正常交易'

        except Exception as e:
            logger.error(f"生成逐笔成交摘要失败: {e}")
            return "摘要生成失败"

    def analyze_large_order_anomalies(self, tick_data: pd.DataFrame, code: str) -> Dict:
        """智能大单异动识别 - 基于游资炒作全链路的专业分析"""
        try:
            from datetime import datetime, time

            result = {
                'analysis_available': False,
                'large_orders': [],
                'anomaly_patterns': {},
                'gaming_signals': [],
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': ''
            }

            if tick_data.empty:
                result['summary'] = '无逐笔成交数据可分析'
                return result

            # 1. 动态阈值计算
            thresholds = self._calculate_dynamic_thresholds(tick_data, code)

            # 2. 大单识别和分类
            large_orders = self._identify_large_orders(tick_data, thresholds)

            # 3. 异动模式识别
            anomaly_patterns = self._identify_anomaly_patterns(large_orders, tick_data)

            # 4. 游资手法识别
            gaming_signals = self._identify_gaming_techniques_from_orders(large_orders, tick_data)

            # 5. 风险预警生成
            risk_warnings = self._generate_risk_warnings(large_orders, anomaly_patterns)

            # 6. 操作建议生成
            operation_suggestions = self._generate_operation_suggestions(gaming_signals, risk_warnings)

            # 7. 生成分析摘要
            summary = self._generate_large_order_summary(large_orders, gaming_signals, risk_warnings)

            result.update({
                'analysis_available': True,
                'large_orders': large_orders,
                'anomaly_patterns': anomaly_patterns,
                'gaming_signals': gaming_signals,
                'risk_warnings': risk_warnings,
                'operation_suggestions': operation_suggestions,
                'summary': summary,
                'thresholds': thresholds
            })

            logger.info(f"大单异动分析完成: {len(large_orders)}笔大单, {len(gaming_signals)}个游资信号")
            return result

        except Exception as e:
            logger.error(f"大单异动识别失败: {e}")
            return {
                'analysis_available': False,
                'large_orders': [],
                'anomaly_patterns': {},
                'gaming_signals': [],
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': f'大单异动分析失败: {str(e)}',
                'error': str(e)
            }

    def _calculate_dynamic_thresholds(self, tick_data: pd.DataFrame, code: str) -> Dict:
        """计算动态大单阈值 - 根据股票特性调整"""
        try:
            thresholds = {
                'large_order_amount': 200000,  # 默认20万元
                'super_large_order_amount': 1000000,  # 默认100万元
                'volume_threshold': 10000,  # 默认1万股
                'price_impact_threshold': 0.5  # 默认0.5%价格冲击
            }

            if '价格' not in tick_data.columns or '成交额' not in tick_data.columns:
                return thresholds

            # 根据股价调整阈值
            avg_price = tick_data['价格'].mean()

            if avg_price < 5:  # 低价股
                thresholds['large_order_amount'] = 100000  # 10万元
                thresholds['super_large_order_amount'] = 500000  # 50万元
                thresholds['volume_threshold'] = 20000  # 2万股
            elif avg_price > 50:  # 高价股
                thresholds['large_order_amount'] = 500000  # 50万元
                thresholds['super_large_order_amount'] = 2000000  # 200万元
                thresholds['volume_threshold'] = 5000  # 5千股
            elif avg_price > 100:  # 超高价股
                thresholds['large_order_amount'] = 1000000  # 100万元
                thresholds['super_large_order_amount'] = 5000000  # 500万元
                thresholds['volume_threshold'] = 2000  # 2千股

            # 根据成交额分布调整
            if '成交额' in tick_data.columns:
                avg_amount = tick_data['成交额'].mean()
                std_amount = tick_data['成交额'].std()

                # 使用统计学方法调整阈值
                statistical_threshold = avg_amount + 2 * std_amount
                if statistical_threshold > thresholds['large_order_amount']:
                    thresholds['large_order_amount'] = min(statistical_threshold, thresholds['large_order_amount'] * 2)

            logger.info(f"动态阈值计算完成: 大单{thresholds['large_order_amount']:,.0f}元, 超大单{thresholds['super_large_order_amount']:,.0f}元")
            return thresholds

        except Exception as e:
            logger.error(f"动态阈值计算失败: {e}")
            return {
                'large_order_amount': 200000,
                'super_large_order_amount': 1000000,
                'volume_threshold': 10000,
                'price_impact_threshold': 0.5
            }

    def _identify_large_orders(self, tick_data: pd.DataFrame, thresholds: Dict) -> List[Dict]:
        """识别和分类大单"""
        try:
            large_orders = []

            if '成交额' not in tick_data.columns:
                return large_orders

            # 筛选大单
            large_order_mask = tick_data['成交额'] >= thresholds['large_order_amount']
            large_order_data = tick_data[large_order_mask].copy()

            if large_order_data.empty:
                return large_orders

            # 计算价格变化（如果有足够数据）
            if len(tick_data) > 1:
                tick_data['price_change'] = tick_data['价格'].pct_change() * 100

            for idx, row in large_order_data.iterrows():
                # 确定大单类型
                amount = row['成交额']
                if amount >= thresholds['super_large_order_amount']:
                    order_type = '超大单'
                    risk_level = '高'
                else:
                    order_type = '大单'
                    risk_level = '中'

                # 计算价格冲击
                price_impact = 0
                if 'price_change' in tick_data.columns and idx in tick_data.index:
                    price_impact = abs(tick_data.loc[idx, 'price_change']) if pd.notna(tick_data.loc[idx, 'price_change']) else 0

                # 判断买卖方向影响
                direction = row.get('买卖方向', 'N')
                direction_desc = {
                    'B': '主动买入',
                    'S': '主动卖出',
                    'N': '中性成交'
                }.get(direction, '未知')

                # 时间分析
                trade_time = row.get('时间', '未知')
                time_period = self._classify_time_period(trade_time)

                large_order = {
                    '序号': len(large_orders) + 1,
                    '时间': trade_time,
                    '时段': time_period,
                    '价格': round(row.get('价格', 0), 2),
                    '成交量': int(row.get('成交量', 0)),
                    '成交额': round(amount, 2),
                    '成交额(万元)': round(amount / 10000, 2),
                    '买卖方向': direction,
                    '方向描述': direction_desc,
                    '大单类型': order_type,
                    '价格冲击(%)': round(price_impact, 3),
                    '风险等级': risk_level
                }

                large_orders.append(large_order)

            # 按时间排序
            large_orders.sort(key=lambda x: x['时间'])

            logger.info(f"识别大单完成: {len(large_orders)}笔大单")
            return large_orders

        except Exception as e:
            logger.error(f"大单识别失败: {e}")
            return []

    def _classify_time_period(self, trade_time: str) -> str:
        """分类交易时段"""
        try:
            if trade_time == '未知':
                return '未知时段'

            # 解析时间
            from datetime import datetime
            time_obj = datetime.strptime(trade_time, '%H:%M:%S').time()
            hour = time_obj.hour
            minute = time_obj.minute

            # 分类时段
            if hour == 9 and minute >= 30:
                return '开盘30分钟'
            elif hour == 10 and minute <= 30:
                return '开盘30分钟'
            elif 10 <= hour <= 11:
                return '上午交易'
            elif hour == 11 and minute <= 30:
                return '上午交易'
            elif 13 <= hour <= 14:
                return '下午交易'
            elif hour == 14 and minute >= 30:
                return '尾盘30分钟'
            elif hour == 15:
                return '尾盘30分钟'
            else:
                return '其他时段'

        except Exception as e:
            logger.error(f"时段分类失败: {e}")
            return '未知时段'

    def _identify_anomaly_patterns(self, large_orders: List[Dict], tick_data: pd.DataFrame) -> Dict:
        """识别异动模式"""
        try:
            patterns = {
                'consecutive_large_orders': [],
                'concentrated_time_orders': [],
                'reverse_direction_orders': [],
                'price_impact_orders': [],
                'pattern_summary': []
            }

            if not large_orders:
                return patterns

            # 1. 连续大单模式
            consecutive_count = 0
            consecutive_orders = []

            for i, order in enumerate(large_orders):
                if i > 0:
                    prev_time = large_orders[i-1]['时间']
                    curr_time = order['时间']

                    # 简单的时间差判断（这里可以更精确）
                    if self._is_time_close(prev_time, curr_time, minutes=5):
                        consecutive_count += 1
                        if consecutive_count == 1:
                            consecutive_orders = [large_orders[i-1], order]
                        else:
                            consecutive_orders.append(order)
                    else:
                        if consecutive_count >= 2:
                            patterns['consecutive_large_orders'].append({
                                'count': consecutive_count + 1,
                                'orders': consecutive_orders.copy(),
                                'total_amount': sum(o['成交额'] for o in consecutive_orders),
                                'time_span': f"{consecutive_orders[0]['时间']} - {consecutive_orders[-1]['时间']}"
                            })
                        consecutive_count = 0
                        consecutive_orders = []
                else:
                    consecutive_count = 0

            # 处理最后一组连续大单
            if consecutive_count >= 2:
                patterns['consecutive_large_orders'].append({
                    'count': consecutive_count + 1,
                    'orders': consecutive_orders.copy(),
                    'total_amount': sum(o['成交额'] for o in consecutive_orders),
                    'time_span': f"{consecutive_orders[0]['时间']} - {consecutive_orders[-1]['时间']}"
                })

            # 2. 集中时段大单
            time_groups = {}
            for order in large_orders:
                time_period = order['时段']
                if time_period not in time_groups:
                    time_groups[time_period] = []
                time_groups[time_period].append(order)

            for time_period, orders in time_groups.items():
                if len(orders) >= 3:  # 同一时段3笔以上大单
                    patterns['concentrated_time_orders'].append({
                        'time_period': time_period,
                        'count': len(orders),
                        'total_amount': sum(o['成交额'] for o in orders),
                        'orders': orders
                    })

            # 3. 反向大单（买卖方向快速切换）
            for i in range(1, len(large_orders)):
                prev_order = large_orders[i-1]
                curr_order = large_orders[i]

                if (prev_order['买卖方向'] == 'B' and curr_order['买卖方向'] == 'S') or \
                   (prev_order['买卖方向'] == 'S' and curr_order['买卖方向'] == 'B'):
                    if self._is_time_close(prev_order['时间'], curr_order['时间'], minutes=3):
                        patterns['reverse_direction_orders'].append({
                            'prev_order': prev_order,
                            'curr_order': curr_order,
                            'time_gap': f"{prev_order['时间']} -> {curr_order['时间']}"
                        })

            # 4. 高价格冲击大单
            for order in large_orders:
                if order['价格冲击(%)'] > 1.0:  # 价格冲击超过1%
                    patterns['price_impact_orders'].append(order)

            # 5. 生成模式摘要
            if patterns['consecutive_large_orders']:
                patterns['pattern_summary'].append(f"发现{len(patterns['consecutive_large_orders'])}组连续大单")
            if patterns['concentrated_time_orders']:
                patterns['pattern_summary'].append(f"发现{len(patterns['concentrated_time_orders'])}个集中时段异动")
            if patterns['reverse_direction_orders']:
                patterns['pattern_summary'].append(f"发现{len(patterns['reverse_direction_orders'])}次反向大单")
            if patterns['price_impact_orders']:
                patterns['pattern_summary'].append(f"发现{len(patterns['price_impact_orders'])}笔高冲击大单")

            return patterns

        except Exception as e:
            logger.error(f"异动模式识别失败: {e}")
            return {
                'consecutive_large_orders': [],
                'concentrated_time_orders': [],
                'reverse_direction_orders': [],
                'price_impact_orders': [],
                'pattern_summary': [],
                'error': str(e)
            }

    def _is_time_close(self, time1: str, time2: str, minutes: int = 5) -> bool:
        """判断两个时间是否接近"""
        try:
            from datetime import datetime, timedelta

            if time1 == '未知' or time2 == '未知':
                return False

            t1 = datetime.strptime(time1, '%H:%M:%S')
            t2 = datetime.strptime(time2, '%H:%M:%S')

            time_diff = abs((t2 - t1).total_seconds())
            return time_diff <= minutes * 60

        except Exception as e:
            logger.error(f"时间比较失败: {e}")
            return False

    def _identify_gaming_techniques_from_orders(self, large_orders: List[Dict], tick_data: pd.DataFrame) -> List[str]:
        """基于大单识别游资操作手法"""
        try:
            gaming_signals = []

            if not large_orders:
                return gaming_signals

            # 1. 试盘手法识别
            probe_signals = self._identify_probe_techniques(large_orders)
            gaming_signals.extend(probe_signals)

            # 2. 建仓手法识别
            accumulation_signals = self._identify_accumulation_techniques(large_orders)
            gaming_signals.extend(accumulation_signals)

            # 3. 拉升手法识别
            pullup_signals = self._identify_pullup_techniques(large_orders)
            gaming_signals.extend(pullup_signals)

            # 4. 出货手法识别
            distribution_signals = self._identify_distribution_techniques(large_orders)
            gaming_signals.extend(distribution_signals)

            return gaming_signals

        except Exception as e:
            logger.error(f"游资手法识别失败: {e}")
            return []

    def _identify_probe_techniques(self, large_orders: List[Dict]) -> List[str]:
        """识别试盘手法"""
        signals = []

        try:
            # 开盘试盘：开盘30分钟内的反向大单
            opening_orders = [o for o in large_orders if o['时段'] == '开盘30分钟']

            if len(opening_orders) >= 2:
                # 检查是否有买卖方向切换
                directions = [o['买卖方向'] for o in opening_orders]
                if 'B' in directions and 'S' in directions:
                    signals.append('开盘试盘：买卖大单交替出现')

            # 小额试盘：相对较小的大单（刚超过阈值）
            small_large_orders = [o for o in large_orders if o['大单类型'] == '大单' and o['成交额'] < 500000]
            if len(small_large_orders) >= 3:
                signals.append(f'小额试盘：{len(small_large_orders)}笔相对小额大单')

            return signals

        except Exception as e:
            logger.error(f"试盘手法识别失败: {e}")
            return []

    def _identify_accumulation_techniques(self, large_orders: List[Dict]) -> List[str]:
        """识别建仓手法"""
        signals = []

        try:
            # 分散建仓：多个时段的买入大单
            buy_orders = [o for o in large_orders if o['买卖方向'] == 'B']

            if len(buy_orders) >= 3:
                # 检查时段分布
                time_periods = set(o['时段'] for o in buy_orders)
                if len(time_periods) >= 2:
                    total_amount = sum(o['成交额'] for o in buy_orders)
                    signals.append(f'分散建仓：{len(buy_orders)}笔买入大单，总额{total_amount/10000:.1f}万元')

            # 午后建仓：下午交易时段的大量买入
            afternoon_buy = [o for o in large_orders if o['时段'] == '下午交易' and o['买卖方向'] == 'B']
            if len(afternoon_buy) >= 2:
                signals.append(f'午后建仓：下午{len(afternoon_buy)}笔买入大单')

            return signals

        except Exception as e:
            logger.error(f"建仓手法识别失败: {e}")
            return []

    def _identify_pullup_techniques(self, large_orders: List[Dict]) -> List[str]:
        """识别拉升手法"""
        signals = []

        try:
            # 连续拉升：短时间内多笔买入大单
            buy_orders = [o for o in large_orders if o['买卖方向'] == 'B']

            # 检查连续买入
            consecutive_buys = 0
            for i in range(1, len(buy_orders)):
                if self._is_time_close(buy_orders[i-1]['时间'], buy_orders[i]['时间'], minutes=10):
                    consecutive_buys += 1

            if consecutive_buys >= 2:
                signals.append(f'连续拉升：{consecutive_buys + 1}笔连续买入大单')

            # 尾盘拉升：尾盘30分钟的买入大单
            tail_buy = [o for o in large_orders if o['时段'] == '尾盘30分钟' and o['买卖方向'] == 'B']
            if len(tail_buy) >= 1:
                total_amount = sum(o['成交额'] for o in tail_buy)
                signals.append(f'尾盘拉升：{len(tail_buy)}笔买入大单，金额{total_amount/10000:.1f}万元')

            # 高冲击拉升：价格冲击较大的买入单
            high_impact_buys = [o for o in large_orders if o['买卖方向'] == 'B' and o['价格冲击(%)'] > 0.5]
            if len(high_impact_buys) >= 1:
                signals.append(f'强力拉升：{len(high_impact_buys)}笔高冲击买入大单')

            return signals

        except Exception as e:
            logger.error(f"拉升手法识别失败: {e}")
            return []

    def _identify_distribution_techniques(self, large_orders: List[Dict]) -> List[str]:
        """识别出货手法"""
        signals = []

        try:
            # 大量卖出：多笔卖出大单
            sell_orders = [o for o in large_orders if o['买卖方向'] == 'S']

            if len(sell_orders) >= 2:
                total_amount = sum(o['成交额'] for o in sell_orders)
                signals.append(f'大量出货：{len(sell_orders)}笔卖出大单，总额{total_amount/10000:.1f}万元')

            # 尾盘砸盘：尾盘的卖出大单
            tail_sell = [o for o in large_orders if o['时段'] == '尾盘30分钟' and o['买卖方向'] == 'S']
            if len(tail_sell) >= 1:
                signals.append(f'尾盘砸盘：{len(tail_sell)}笔卖出大单')

            # 超大单出货：超大单卖出
            super_sell = [o for o in large_orders if o['大单类型'] == '超大单' and o['买卖方向'] == 'S']
            if len(super_sell) >= 1:
                signals.append(f'超大单出货：{len(super_sell)}笔超大卖单')

            return signals

        except Exception as e:
            logger.error(f"出货手法识别失败: {e}")
            return []

    def _generate_risk_warnings(self, large_orders: List[Dict], anomaly_patterns: Dict) -> List[str]:
        """生成风险预警"""
        warnings = []

        try:
            # 1. 大单数量风险
            if len(large_orders) >= 10:
                warnings.append(f'高风险：发现{len(large_orders)}笔大单，市场异动剧烈')
            elif len(large_orders) >= 5:
                warnings.append(f'中风险：发现{len(large_orders)}笔大单，需密切关注')

            # 2. 超大单风险
            super_large_count = len([o for o in large_orders if o['大单类型'] == '超大单'])
            if super_large_count >= 3:
                warnings.append(f'高风险：{super_large_count}笔超大单，可能有重大消息')
            elif super_large_count >= 1:
                warnings.append(f'中风险：{super_large_count}笔超大单，关注资金动向')

            # 3. 反向大单风险
            reverse_orders = anomaly_patterns.get('reverse_direction_orders', [])
            if len(reverse_orders) >= 3:
                warnings.append(f'高风险：{len(reverse_orders)}次反向大单，市场分歧严重')
            elif len(reverse_orders) >= 1:
                warnings.append(f'中风险：{len(reverse_orders)}次反向大单，注意方向选择')

            # 4. 集中时段风险
            concentrated_periods = anomaly_patterns.get('concentrated_time_orders', [])
            if len(concentrated_periods) >= 2:
                warnings.append(f'中风险：{len(concentrated_periods)}个时段集中异动，可能有计划性操作')

            # 5. 价格冲击风险
            high_impact_orders = anomaly_patterns.get('price_impact_orders', [])
            if len(high_impact_orders) >= 3:
                warnings.append(f'高风险：{len(high_impact_orders)}笔高冲击大单，价格波动剧烈')

            # 6. 出货风险
            sell_orders = [o for o in large_orders if o['买卖方向'] == 'S']
            sell_ratio = len(sell_orders) / len(large_orders) if large_orders else 0
            if sell_ratio > 0.7:
                warnings.append(f'高风险：卖出大单占比{sell_ratio*100:.1f}%，可能面临抛压')
            elif sell_ratio > 0.5:
                warnings.append(f'中风险：卖出大单占比{sell_ratio*100:.1f}%，注意卖压')

            return warnings

        except Exception as e:
            logger.error(f"风险预警生成失败: {e}")
            return [f'风险预警生成失败: {str(e)}']

    def _generate_operation_suggestions(self, gaming_signals: List[str], risk_warnings: List[str]) -> List[Dict]:
        """生成操作建议"""
        suggestions = []

        try:
            # 根据游资信号生成建议
            buy_signals = [s for s in gaming_signals if any(keyword in s for keyword in ['建仓', '拉升', '买入'])]
            sell_signals = [s for s in gaming_signals if any(keyword in s for keyword in ['出货', '砸盘', '卖出'])]

            # 根据风险等级调整建议
            high_risk_count = len([w for w in risk_warnings if '高风险' in w])
            medium_risk_count = len([w for w in risk_warnings if '中风险' in w])

            if buy_signals and high_risk_count == 0:
                suggestions.append({
                    'type': '买入建议',
                    'confidence': '较高',
                    'reason': f'发现{len(buy_signals)}个买入信号，风险可控',
                    'action': '可考虑适量买入，设置止损',
                    'risk_level': '中等'
                })
            elif buy_signals and medium_risk_count <= 2:
                suggestions.append({
                    'type': '谨慎买入',
                    'confidence': '中等',
                    'reason': f'有买入信号但存在{medium_risk_count}个中等风险',
                    'action': '小仓位试探，严格止损',
                    'risk_level': '较高'
                })

            if sell_signals or high_risk_count >= 2:
                suggestions.append({
                    'type': '卖出建议',
                    'confidence': '较高',
                    'reason': f'发现{len(sell_signals)}个卖出信号或{high_risk_count}个高风险',
                    'action': '建议减仓或止盈，避免追高',
                    'risk_level': '高'
                })

            if not buy_signals and not sell_signals:
                suggestions.append({
                    'type': '观望建议',
                    'confidence': '中等',
                    'reason': '大单信号不明确，方向不清',
                    'action': '暂时观望，等待明确信号',
                    'risk_level': '中等'
                })

            # 特殊情况建议
            if high_risk_count >= 3:
                suggestions.append({
                    'type': '高风险警告',
                    'confidence': '高',
                    'reason': f'发现{high_risk_count}个高风险信号',
                    'action': '立即止损，避免重大损失',
                    'risk_level': '极高'
                })

            return suggestions

        except Exception as e:
            logger.error(f"操作建议生成失败: {e}")
            return [{
                'type': '系统错误',
                'confidence': '无',
                'reason': '建议生成失败',
                'action': '请手动分析',
                'risk_level': '未知'
            }]

    def _generate_large_order_summary(self, large_orders: List[Dict], gaming_signals: List[str], risk_warnings: List[str]) -> str:
        """生成大单异动分析摘要"""
        try:
            summary_parts = []

            # 大单基础信息
            if large_orders:
                total_amount = sum(o['成交额'] for o in large_orders)
                buy_count = len([o for o in large_orders if o['买卖方向'] == 'B'])
                sell_count = len([o for o in large_orders if o['买卖方向'] == 'S'])

                summary_parts.append(f"{len(large_orders)}笔大单")
                summary_parts.append(f"总额{total_amount/10000:.1f}万元")
                summary_parts.append(f"买{buy_count}卖{sell_count}")

            # 游资信号
            if gaming_signals:
                summary_parts.append(f"{len(gaming_signals)}个游资信号")

            # 风险等级
            high_risk_count = len([w for w in risk_warnings if '高风险' in w])
            if high_risk_count >= 2:
                summary_parts.append("高风险")
            elif risk_warnings:
                summary_parts.append("中等风险")
            else:
                summary_parts.append("低风险")

            return ', '.join(summary_parts) if summary_parts else '无大单异动'

        except Exception as e:
            logger.error(f"大单异动摘要生成失败: {e}")
            return "摘要生成失败"

    def _control_tick_data_volume(self, tick_data: pd.DataFrame, max_records: int = 2000) -> pd.DataFrame:
        """控制逐笔成交数据量，优先保留游资信号相关数据"""
        try:
            if len(tick_data) <= max_records:
                logger.info(f"逐笔数据量({len(tick_data)})未超限，无需控制")
                return tick_data

            logger.info(f"逐笔数据量({len(tick_data)})超限，开始智能筛选")

            # 数据预处理
            processed_data = tick_data.copy()

            # 标准化列名
            column_mapping = {
                '成交时间': '时间',
                '成交价格': '价格',
                '成交量': '成交量',
                '成交金额': '成交额',
                '性质': '买卖方向'
            }

            for old_col, new_col in column_mapping.items():
                if old_col in processed_data.columns:
                    processed_data = processed_data.rename(columns={old_col: new_col})

            # 处理买卖方向
            if '买卖方向' in processed_data.columns:
                direction_mapping = {'买盘': 'B', '卖盘': 'S', '中性盘': 'N'}
                processed_data['买卖方向'] = processed_data['买卖方向'].map(direction_mapping).fillna('N')

            # 数据类型转换
            if '价格' in processed_data.columns:
                processed_data['价格'] = pd.to_numeric(processed_data['价格'], errors='coerce')
            if '成交量' in processed_data.columns:
                processed_data['成交量'] = pd.to_numeric(processed_data['成交量'], errors='coerce')
            if '成交额' in processed_data.columns:
                processed_data['成交额'] = pd.to_numeric(processed_data['成交额'], errors='coerce')

            # 计算成交额（如果缺失）
            if '成交额' not in processed_data.columns and '价格' in processed_data.columns and '成交量' in processed_data.columns:
                processed_data['成交额'] = processed_data['价格'] * processed_data['成交量']

            # 智能筛选策略：优先保留游资信号相关数据
            selected_data = []

            # 1. 优先级1：大单数据（成交额>20万）- 全部保留
            if '成交额' in processed_data.columns:
                large_orders = processed_data[processed_data['成交额'] > 200000].copy()
                if not large_orders.empty:
                    selected_data.append(large_orders)
                    logger.info(f"保留大单数据: {len(large_orders)}条")

            # 2. 优先级2：关键时段数据 - 开盘30分钟和尾盘30分钟
            if '时间' in processed_data.columns:
                key_period_data = self._select_key_period_tick_data(processed_data)
                if not key_period_data.empty:
                    selected_data.append(key_period_data)
                    logger.info(f"保留关键时段数据: {len(key_period_data)}条")

            # 3. 优先级3：价格异动数据 - 价格变化较大的成交
            price_anomaly_data = self._select_price_anomaly_tick_data(processed_data)
            if not price_anomaly_data.empty:
                selected_data.append(price_anomaly_data)
                logger.info(f"保留价格异动数据: {len(price_anomaly_data)}条")

            # 4. 优先级4：成交量异动数据 - 成交量较大的成交
            volume_anomaly_data = self._select_volume_anomaly_tick_data(processed_data)
            if not volume_anomaly_data.empty:
                selected_data.append(volume_anomaly_data)
                logger.info(f"保留成交量异动数据: {len(volume_anomaly_data)}条")

            # 合并所有优先数据
            if selected_data:
                combined_data = pd.concat(selected_data, ignore_index=True)
                # 去重（基于时间和价格）
                combined_data = combined_data.drop_duplicates(subset=['时间', '价格'], keep='first')

                # 如果还是超过限制，按时间均匀采样
                if len(combined_data) > max_records:
                    step = len(combined_data) // max_records
                    combined_data = combined_data.iloc[::step].head(max_records)
                    logger.info(f"均匀采样至: {len(combined_data)}条")

                # 按时间排序
                if '时间' in combined_data.columns:
                    combined_data = combined_data.sort_values('时间')

                logger.info(f"逐笔数据智能筛选完成: {len(tick_data)}条 -> {len(combined_data)}条")
                return combined_data
            else:
                # 如果没有特殊数据，均匀采样
                step = len(processed_data) // max_records
                sampled_data = processed_data.iloc[::step].head(max_records)
                logger.info(f"均匀采样: {len(tick_data)}条 -> {len(sampled_data)}条")
                return sampled_data

        except Exception as e:
            logger.error(f"逐笔数据量控制失败: {e}")
            # 失败时简单截取前2000条
            return tick_data.head(max_records)

    def _select_key_period_tick_data(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """选择关键时段的逐笔成交数据"""
        try:
            if '时间' not in tick_data.columns:
                return pd.DataFrame()

            key_period_data = []

            for _, row in tick_data.iterrows():
                time_str = row['时间']
                try:
                    from datetime import datetime
                    time_obj = datetime.strptime(time_str, '%H:%M:%S').time()
                    hour = time_obj.hour
                    minute = time_obj.minute

                    # 开盘30分钟：9:30-10:00
                    if (hour == 9 and minute >= 30) or (hour == 10 and minute <= 0):
                        key_period_data.append(row)
                    # 尾盘30分钟：14:30-15:00
                    elif (hour == 14 and minute >= 30) or (hour == 15 and minute <= 0):
                        key_period_data.append(row)
                    # 整点前后5分钟
                    elif minute <= 5 or minute >= 55:
                        key_period_data.append(row)

                except Exception:
                    continue

            if key_period_data:
                return pd.DataFrame(key_period_data)
            else:
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"关键时段数据选择失败: {e}")
            return pd.DataFrame()

    def _select_price_anomaly_tick_data(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """选择价格异动的逐笔成交数据"""
        try:
            if '价格' not in tick_data.columns or len(tick_data) < 2:
                return pd.DataFrame()

            # 计算价格变化
            tick_data_copy = tick_data.copy()
            tick_data_copy['price_change'] = tick_data_copy['价格'].pct_change() * 100

            # 选择价格变化超过0.5%的记录
            price_anomaly = tick_data_copy[abs(tick_data_copy['price_change']) > 0.5]

            return price_anomaly.drop(columns=['price_change']) if not price_anomaly.empty else pd.DataFrame()

        except Exception as e:
            logger.error(f"价格异动数据选择失败: {e}")
            return pd.DataFrame()

    def _select_volume_anomaly_tick_data(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """选择成交量异动的逐笔成交数据"""
        try:
            if '成交量' not in tick_data.columns:
                return pd.DataFrame()

            # 计算成交量统计
            volume_stats = tick_data['成交量'].describe()

            # 选择成交量超过75%分位数2倍的记录
            volume_threshold = volume_stats['75%'] * 2
            volume_anomaly = tick_data[tick_data['成交量'] > volume_threshold]

            return volume_anomaly if not volume_anomaly.empty else pd.DataFrame()

        except Exception as e:
            logger.error(f"成交量异动数据选择失败: {e}")
            return pd.DataFrame()

    def analyze_intraday_price_patterns(self, tick_data: pd.DataFrame, code: str) -> Dict:
        """分析分时价格走势模式 - 识别游资经典拉升手法"""
        try:
            result = {
                'analysis_available': False,
                'price_patterns': [],
                'pullup_signals': [],
                'pattern_summary': {},
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': ''
            }

            if tick_data.empty or '价格' not in tick_data.columns or '时间' not in tick_data.columns:
                result['summary'] = '无有效价格数据可分析'
                return result

            logger.info(f"开始分析{code}的分时价格走势模式")

            # 数据预处理
            processed_data = self._prepare_price_analysis_data(tick_data)

            if processed_data.empty:
                result['summary'] = '价格数据预处理失败'
                return result

            # 1. 识别90度拉板模式
            ninety_degree_patterns = self._identify_ninety_degree_pullup(processed_data)

            # 2. 识别斜刺波模式
            diagonal_wave_patterns = self._identify_diagonal_wave_pullup(processed_data)

            # 3. 识别阶梯式拉升模式
            ladder_patterns = self._identify_ladder_pullup(processed_data)

            # 4. 识别V型反转模式
            v_reversal_patterns = self._identify_v_reversal_pattern(processed_data)

            # 5. 识别平台突破模式
            platform_breakout_patterns = self._identify_platform_breakout(processed_data)

            # 6. 识别砸盘模式
            crash_patterns = self._identify_crash_patterns(processed_data)

            # 合并所有模式
            all_patterns = []
            all_patterns.extend(ninety_degree_patterns)
            all_patterns.extend(diagonal_wave_patterns)
            all_patterns.extend(ladder_patterns)
            all_patterns.extend(v_reversal_patterns)
            all_patterns.extend(platform_breakout_patterns)
            all_patterns.extend(crash_patterns)

            # 生成拉升信号
            pullup_signals = self._generate_pullup_signals(all_patterns)

            # 生成风险预警
            risk_warnings = self._generate_pattern_risk_warnings(all_patterns)

            # 生成操作建议
            operation_suggestions = self._generate_pattern_operation_suggestions(all_patterns, pullup_signals)

            # 生成模式摘要
            pattern_summary = self._generate_pattern_summary(all_patterns)

            # 生成总体摘要
            summary = self._generate_price_pattern_summary(all_patterns, pullup_signals)

            result.update({
                'analysis_available': True,
                'price_patterns': all_patterns,
                'pullup_signals': pullup_signals,
                'pattern_summary': pattern_summary,
                'risk_warnings': risk_warnings,
                'operation_suggestions': operation_suggestions,
                'summary': summary
            })

            logger.info(f"分时价格走势分析完成: {len(all_patterns)}个模式, {len(pullup_signals)}个拉升信号")
            return result

        except Exception as e:
            logger.error(f"分时价格走势分析失败: {e}")
            return {
                'analysis_available': False,
                'price_patterns': [],
                'pullup_signals': [],
                'pattern_summary': {},
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': f'价格走势分析失败: {str(e)}',
                'error': str(e)
            }

    def _prepare_price_analysis_data(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """为价格分析准备数据"""
        try:
            if tick_data.empty:
                return pd.DataFrame()

            # 复制数据
            data = tick_data.copy()

            # 确保必要的列存在
            required_columns = ['时间', '价格']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                return pd.DataFrame()

            # 数据类型转换
            data['价格'] = pd.to_numeric(data['价格'], errors='coerce')

            # 移除无效数据
            data = data.dropna(subset=['价格'])

            if data.empty:
                return pd.DataFrame()

            # 按时间排序
            data = data.sort_values('时间')
            data = data.reset_index(drop=True)

            # 计算价格变化
            data['价格变化'] = data['价格'].diff()
            data['价格变化率'] = data['价格'].pct_change() * 100

            # 计算移动平均
            if len(data) >= 5:
                data['价格_MA5'] = data['价格'].rolling(window=5, min_periods=1).mean()
            else:
                data['价格_MA5'] = data['价格']

            # 添加时间索引
            data['序号'] = range(len(data))

            logger.info(f"价格分析数据预处理完成: {len(data)}条记录")
            return data

        except Exception as e:
            logger.error(f"价格分析数据预处理失败: {e}")
            return pd.DataFrame()

    def _identify_ninety_degree_pullup(self, data: pd.DataFrame) -> List[Dict]:
        """识别90度拉板模式 - 短时间内急速拉升"""
        try:
            patterns = []

            if len(data) < 10:  # 至少需要10个数据点
                return patterns

            # 滑动窗口检测急速拉升
            window_size = 10  # 10笔成交的窗口
            min_rise_rate = 2.0  # 最小涨幅2%
            max_time_span = 300  # 最大时间跨度5分钟

            for i in range(len(data) - window_size + 1):
                window_data = data.iloc[i:i + window_size]

                # 计算窗口内的价格变化
                start_price = window_data['价格'].iloc[0]
                end_price = window_data['价格'].iloc[-1]
                max_price = window_data['价格'].max()

                # 计算涨幅
                rise_rate = (end_price - start_price) / start_price * 100
                max_rise_rate = (max_price - start_price) / start_price * 100

                # 检查是否符合90度拉板特征
                if rise_rate >= min_rise_rate and max_rise_rate >= min_rise_rate:
                    # 检查拉升的连续性
                    consecutive_rises = 0
                    for j in range(1, len(window_data)):
                        if window_data['价格'].iloc[j] >= window_data['价格'].iloc[j-1]:
                            consecutive_rises += 1

                    # 连续上涨比例要求
                    rise_ratio = consecutive_rises / (len(window_data) - 1)

                    if rise_ratio >= 0.7:  # 70%以上连续上涨
                        pattern = {
                            'type': '90度拉板',
                            'start_time': window_data['时间'].iloc[0],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'end_price': round(end_price, 2),
                            'max_price': round(max_price, 2),
                            'rise_rate': round(rise_rate, 2),
                            'max_rise_rate': round(max_rise_rate, 2),
                            'consecutive_ratio': round(rise_ratio, 2),
                            'duration_records': len(window_data),
                            'strength': self._calculate_pattern_strength(rise_rate, rise_ratio),
                            'confidence': round(rise_ratio * 100, 1)
                        }

                        patterns.append(pattern)

                        # 避免重叠检测，跳过一些数据点
                        i += window_size // 2

            # 去重和排序
            patterns = self._deduplicate_patterns(patterns)
            patterns.sort(key=lambda x: x['start_time'])

            logger.info(f"识别90度拉板模式: {len(patterns)}个")
            return patterns

        except Exception as e:
            logger.error(f"90度拉板识别失败: {e}")
            return []

    def _calculate_pattern_strength(self, rise_rate: float, consistency: float) -> str:
        """计算模式强度"""
        try:
            score = rise_rate * consistency

            if score >= 3.0:
                return '强'
            elif score >= 1.5:
                return '中'
            else:
                return '弱'

        except Exception:
            return '未知'

    def _deduplicate_patterns(self, patterns: List[Dict]) -> List[Dict]:
        """去除重叠的模式"""
        try:
            if not patterns:
                return patterns

            # 按开始时间排序
            patterns.sort(key=lambda x: x['start_time'])

            deduplicated = []
            for pattern in patterns:
                # 检查是否与已有模式重叠
                is_duplicate = False
                for existing in deduplicated:
                    if (pattern['start_time'] <= existing['end_time'] and
                        pattern['end_time'] >= existing['start_time']):
                        # 如果重叠，保留强度更高的
                        if pattern.get('rise_rate', 0) > existing.get('rise_rate', 0):
                            deduplicated.remove(existing)
                            deduplicated.append(pattern)
                        is_duplicate = True
                        break

                if not is_duplicate:
                    deduplicated.append(pattern)

            return deduplicated

        except Exception as e:
            logger.error(f"模式去重失败: {e}")
            return patterns

    def _identify_diagonal_wave_pullup(self, data: pd.DataFrame) -> List[Dict]:
        """识别斜刺波模式 - 持续性斜向拉升"""
        try:
            patterns = []

            if len(data) < 20:  # 至少需要20个数据点
                return patterns

            # 滑动窗口检测斜向拉升
            window_size = 20  # 20笔成交的窗口
            min_rise_rate = 1.5  # 最小涨幅1.5%
            min_duration = 15  # 最小持续时间

            for i in range(len(data) - window_size + 1):
                window_data = data.iloc[i:i + window_size]

                # 计算线性回归斜率
                x = range(len(window_data))
                y = window_data['价格'].values

                # 简单线性回归
                n = len(x)
                sum_x = sum(x)
                sum_y = sum(y)
                sum_xy = sum(x[i] * y[i] for i in range(n))
                sum_x2 = sum(x[i] ** 2 for i in range(n))

                # 计算斜率和相关系数
                slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x ** 2)

                # 计算相关系数
                mean_x = sum_x / n
                mean_y = sum_y / n
                numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
                denominator_x = sum((x[i] - mean_x) ** 2 for i in range(n))
                denominator_y = sum((y[i] - mean_y) ** 2 for i in range(n))

                if denominator_x > 0 and denominator_y > 0:
                    correlation = numerator / (denominator_x * denominator_y) ** 0.5
                else:
                    correlation = 0

                # 计算总涨幅
                start_price = window_data['价格'].iloc[0]
                end_price = window_data['价格'].iloc[-1]
                rise_rate = (end_price - start_price) / start_price * 100

                # 检查是否符合斜刺波特征
                if (slope > 0 and correlation >= 0.7 and rise_rate >= min_rise_rate):
                    pattern = {
                        'type': '斜刺波',
                        'start_time': window_data['时间'].iloc[0],
                        'end_time': window_data['时间'].iloc[-1],
                        'start_price': round(start_price, 2),
                        'end_price': round(end_price, 2),
                        'rise_rate': round(rise_rate, 2),
                        'slope': round(slope, 4),
                        'correlation': round(correlation, 3),
                        'duration_records': len(window_data),
                        'strength': self._calculate_diagonal_strength(rise_rate, correlation),
                        'confidence': round(correlation * 100, 1)
                    }

                    patterns.append(pattern)

                    # 避免重叠检测
                    i += window_size // 3

            # 去重和排序
            patterns = self._deduplicate_patterns(patterns)
            patterns.sort(key=lambda x: x['start_time'])

            logger.info(f"识别斜刺波模式: {len(patterns)}个")
            return patterns

        except Exception as e:
            logger.error(f"斜刺波识别失败: {e}")
            return []

    def _calculate_diagonal_strength(self, rise_rate: float, correlation: float) -> str:
        """计算斜刺波强度"""
        try:
            score = rise_rate * correlation

            if score >= 2.5:
                return '强'
            elif score >= 1.2:
                return '中'
            else:
                return '弱'

        except Exception:
            return '未知'

    def _identify_ladder_pullup(self, data: pd.DataFrame) -> List[Dict]:
        """识别阶梯式拉升模式"""
        try:
            patterns = []

            if len(data) < 30:  # 至少需要30个数据点
                return patterns

            # 寻找阶梯式拉升：多个小幅拉升组成的大幅拉升
            window_size = 30
            min_total_rise = 2.0  # 总涨幅至少2%
            min_steps = 3  # 至少3个阶梯

            for i in range(len(data) - window_size + 1):
                window_data = data.iloc[i:i + window_size]

                # 寻找价格平台和突破
                platforms = self._find_price_platforms(window_data)

                if len(platforms) >= min_steps:
                    start_price = window_data['价格'].iloc[0]
                    end_price = window_data['价格'].iloc[-1]
                    total_rise = (end_price - start_price) / start_price * 100

                    if total_rise >= min_total_rise:
                        pattern = {
                            'type': '阶梯式拉升',
                            'start_time': window_data['时间'].iloc[0],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'end_price': round(end_price, 2),
                            'rise_rate': round(total_rise, 2),
                            'steps_count': len(platforms),
                            'platforms': platforms,
                            'duration_records': len(window_data),
                            'strength': self._calculate_ladder_strength(total_rise, len(platforms)),
                            'confidence': min(90, len(platforms) * 20)
                        }

                        patterns.append(pattern)

                        # 避免重叠检测
                        i += window_size // 2

            # 去重和排序
            patterns = self._deduplicate_patterns(patterns)
            patterns.sort(key=lambda x: x['start_time'])

            logger.info(f"识别阶梯式拉升模式: {len(patterns)}个")
            return patterns

        except Exception as e:
            logger.error(f"阶梯式拉升识别失败: {e}")
            return []

    def _find_price_platforms(self, data: pd.DataFrame) -> List[Dict]:
        """寻找价格平台"""
        try:
            platforms = []

            if len(data) < 5:
                return platforms

            # 寻找相对平稳的价格区间
            tolerance = 0.5  # 0.5%的价格波动容忍度
            min_platform_length = 3  # 最小平台长度

            current_platform = []

            for i in range(len(data)):
                price = data['价格'].iloc[i]

                if not current_platform:
                    current_platform = [i]
                else:
                    # 检查价格是否在平台范围内
                    platform_prices = [data['价格'].iloc[j] for j in current_platform]
                    avg_price = sum(platform_prices) / len(platform_prices)

                    if abs(price - avg_price) / avg_price * 100 <= tolerance:
                        current_platform.append(i)
                    else:
                        # 平台结束，记录平台
                        if len(current_platform) >= min_platform_length:
                            platform_start = current_platform[0]
                            platform_end = current_platform[-1]
                            platform_price = sum(data['价格'].iloc[j] for j in current_platform) / len(current_platform)

                            platforms.append({
                                'start_index': platform_start,
                                'end_index': platform_end,
                                'start_time': data['时间'].iloc[platform_start],
                                'end_time': data['时间'].iloc[platform_end],
                                'price': round(platform_price, 2),
                                'length': len(current_platform)
                            })

                        # 开始新平台
                        current_platform = [i]

            # 处理最后一个平台
            if len(current_platform) >= min_platform_length:
                platform_start = current_platform[0]
                platform_end = current_platform[-1]
                platform_price = sum(data['价格'].iloc[j] for j in current_platform) / len(current_platform)

                platforms.append({
                    'start_index': platform_start,
                    'end_index': platform_end,
                    'start_time': data['时间'].iloc[platform_start],
                    'end_time': data['时间'].iloc[platform_end],
                    'price': round(platform_price, 2),
                    'length': len(current_platform)
                })

            return platforms

        except Exception as e:
            logger.error(f"价格平台识别失败: {e}")
            return []

    def _calculate_ladder_strength(self, total_rise: float, steps: int) -> str:
        """计算阶梯式拉升强度"""
        try:
            score = total_rise * (steps / 3)  # 步数越多，强度越高

            if score >= 4.0:
                return '强'
            elif score >= 2.0:
                return '中'
            else:
                return '弱'

        except Exception:
            return '未知'

    def _identify_v_reversal_pattern(self, data: pd.DataFrame) -> List[Dict]:
        """识别V型反转模式"""
        try:
            patterns = []

            if len(data) < 15:
                return patterns

            # 寻找V型反转：先下跌后急速拉升
            window_size = 15
            min_fall_rate = 1.0  # 最小下跌1%
            min_rise_rate = 1.5  # 最小反弹1.5%

            for i in range(len(data) - window_size + 1):
                window_data = data.iloc[i:i + window_size]

                # 寻找最低点
                min_price_idx = window_data['价格'].idxmin()
                min_price_pos = window_data.index.get_loc(min_price_idx)

                # 确保最低点不在边缘
                if 2 <= min_price_pos <= len(window_data) - 3:
                    before_data = window_data.iloc[:min_price_pos + 1]
                    after_data = window_data.iloc[min_price_pos:]

                    # 计算下跌和上涨幅度
                    start_price = before_data['价格'].iloc[0]
                    min_price = before_data['价格'].iloc[-1]
                    end_price = after_data['价格'].iloc[-1]

                    fall_rate = (start_price - min_price) / start_price * 100
                    rise_rate = (end_price - min_price) / min_price * 100

                    if fall_rate >= min_fall_rate and rise_rate >= min_rise_rate:
                        pattern = {
                            'type': 'V型反转',
                            'start_time': window_data['时间'].iloc[0],
                            'bottom_time': window_data['时间'].iloc[min_price_pos],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'bottom_price': round(min_price, 2),
                            'end_price': round(end_price, 2),
                            'fall_rate': round(fall_rate, 2),
                            'rise_rate': round(rise_rate, 2),
                            'total_recovery': round((end_price - start_price) / start_price * 100, 2),
                            'duration_records': len(window_data),
                            'strength': self._calculate_v_strength(fall_rate, rise_rate),
                            'confidence': min(95, (fall_rate + rise_rate) * 10)
                        }

                        patterns.append(pattern)

                        # 避免重叠检测
                        i += window_size // 2

            # 去重和排序
            patterns = self._deduplicate_patterns(patterns)
            patterns.sort(key=lambda x: x['start_time'])

            logger.info(f"识别V型反转模式: {len(patterns)}个")
            return patterns

        except Exception as e:
            logger.error(f"V型反转识别失败: {e}")
            return []

    def _calculate_v_strength(self, fall_rate: float, rise_rate: float) -> str:
        """计算V型反转强度"""
        try:
            score = (fall_rate + rise_rate) / 2

            if score >= 3.0:
                return '强'
            elif score >= 1.5:
                return '中'
            else:
                return '弱'

        except Exception:
            return '未知'

    def _identify_platform_breakout(self, data: pd.DataFrame) -> List[Dict]:
        """识别平台突破模式"""
        try:
            patterns = []

            if len(data) < 20:
                return patterns

            # 寻找平台突破：横盘后突破拉升
            platforms = self._find_price_platforms(data)

            for platform in platforms:
                # 检查平台后是否有突破
                platform_end_idx = platform['end_index']

                if platform_end_idx < len(data) - 5:  # 确保平台后有足够数据
                    platform_price = platform['price']

                    # 检查平台后的价格走势
                    after_platform = data.iloc[platform_end_idx:platform_end_idx + 10]

                    if not after_platform.empty:
                        max_price_after = after_platform['价格'].max()
                        breakout_rate = (max_price_after - platform_price) / platform_price * 100

                        if breakout_rate >= 1.0:  # 突破幅度至少1%
                            pattern = {
                                'type': '平台突破',
                                'start_time': platform['start_time'],
                                'end_time': after_platform['时间'].iloc[-1],
                                'platform_start_time': platform['start_time'],
                                'platform_end_time': platform['end_time'],
                                'breakout_time': after_platform['时间'].iloc[-1],
                                'platform_price': platform_price,
                                'breakout_price': round(max_price_after, 2),
                                'platform_length': platform['length'],
                                'breakout_rate': round(breakout_rate, 2),
                                'strength': self._calculate_breakout_strength(breakout_rate, platform['length']),
                                'confidence': min(90, breakout_rate * 20)
                            }

                            patterns.append(pattern)

            logger.info(f"识别平台突破模式: {len(patterns)}个")
            return patterns

        except Exception as e:
            logger.error(f"平台突破识别失败: {e}")
            return []

    def _calculate_breakout_strength(self, breakout_rate: float, platform_length: int) -> str:
        """计算平台突破强度"""
        try:
            # 平台越长，突破越有效
            score = breakout_rate * (platform_length / 5)

            if score >= 3.0:
                return '强'
            elif score >= 1.5:
                return '中'
            else:
                return '弱'

        except Exception:
            return '未知'

    def _identify_crash_patterns(self, data: pd.DataFrame) -> List[Dict]:
        """识别砸盘模式"""
        try:
            patterns = []

            if len(data) < 10:
                return patterns

            # 寻找急速下跌模式
            window_size = 10
            min_fall_rate = 2.0  # 最小跌幅2%

            for i in range(len(data) - window_size + 1):
                window_data = data.iloc[i:i + window_size]

                start_price = window_data['价格'].iloc[0]
                end_price = window_data['价格'].iloc[-1]
                min_price = window_data['价格'].min()

                fall_rate = (start_price - end_price) / start_price * 100
                max_fall_rate = (start_price - min_price) / start_price * 100

                if fall_rate >= min_fall_rate and max_fall_rate >= min_fall_rate:
                    # 检查下跌的连续性
                    consecutive_falls = 0
                    for j in range(1, len(window_data)):
                        if window_data['价格'].iloc[j] <= window_data['价格'].iloc[j-1]:
                            consecutive_falls += 1

                    fall_ratio = consecutive_falls / (len(window_data) - 1)

                    if fall_ratio >= 0.6:  # 60%以上连续下跌
                        pattern = {
                            'type': '砸盘',
                            'start_time': window_data['时间'].iloc[0],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'end_price': round(end_price, 2),
                            'min_price': round(min_price, 2),
                            'fall_rate': round(fall_rate, 2),
                            'max_fall_rate': round(max_fall_rate, 2),
                            'consecutive_ratio': round(fall_ratio, 2),
                            'duration_records': len(window_data),
                            'strength': self._calculate_crash_strength(fall_rate, fall_ratio),
                            'confidence': round(fall_ratio * 100, 1)
                        }

                        patterns.append(pattern)

                        # 避免重叠检测
                        i += window_size // 2

            # 去重和排序
            patterns = self._deduplicate_patterns(patterns)
            patterns.sort(key=lambda x: x['start_time'])

            logger.info(f"识别砸盘模式: {len(patterns)}个")
            return patterns

        except Exception as e:
            logger.error(f"砸盘模式识别失败: {e}")
            return []

    def _calculate_crash_strength(self, fall_rate: float, consistency: float) -> str:
        """计算砸盘强度"""
        try:
            score = fall_rate * consistency

            if score >= 3.0:
                return '强'
            elif score >= 1.5:
                return '中'
            else:
                return '弱'

        except Exception:
            return '未知'

    def _generate_pullup_signals(self, patterns: List[Dict]) -> List[str]:
        """生成拉升信号"""
        try:
            signals = []

            # 统计各种模式
            pattern_counts = {}
            for pattern in patterns:
                pattern_type = pattern['type']
                pattern_counts[pattern_type] = pattern_counts.get(pattern_type, 0) + 1

            # 生成信号
            for pattern_type, count in pattern_counts.items():
                if pattern_type in ['90度拉板', '斜刺波', '阶梯式拉升', 'V型反转', '平台突破']:
                    if count >= 2:
                        signals.append(f'多次{pattern_type}({count}次)，拉升意图明显')
                    else:
                        signals.append(f'发现{pattern_type}，有拉升迹象')
                elif pattern_type == '砸盘':
                    if count >= 2:
                        signals.append(f'多次砸盘({count}次)，下跌压力较大')
                    else:
                        signals.append(f'发现砸盘，注意下跌风险')

            # 分析强度分布
            strong_patterns = [p for p in patterns if p.get('strength') == '强']
            if len(strong_patterns) >= 2:
                signals.append(f'发现{len(strong_patterns)}个强势拉升模式，游资操作明显')

            # 分析时间集中度
            if len(patterns) >= 3:
                signals.append(f'短时间内发现{len(patterns)}个价格模式，市场活跃度高')

            return signals

        except Exception as e:
            logger.error(f"拉升信号生成失败: {e}")
            return []

    def _generate_pattern_risk_warnings(self, patterns: List[Dict]) -> List[str]:
        """生成模式风险预警"""
        try:
            warnings = []

            # 砸盘风险
            crash_patterns = [p for p in patterns if p['type'] == '砸盘']
            if len(crash_patterns) >= 2:
                warnings.append(f'高风险：发现{len(crash_patterns)}次砸盘，下跌压力严重')
            elif len(crash_patterns) >= 1:
                warnings.append(f'中风险：发现砸盘模式，注意下跌风险')

            # 拉升后风险
            pullup_patterns = [p for p in patterns if p['type'] in ['90度拉板', '斜刺波']]
            if len(pullup_patterns) >= 3:
                warnings.append(f'中风险：多次急速拉升后，注意获利回吐压力')

            # V型反转风险
            v_patterns = [p for p in patterns if p['type'] == 'V型反转']
            if len(v_patterns) >= 1:
                warnings.append(f'中风险：V型反转后，价格波动可能加剧')

            return warnings

        except Exception as e:
            logger.error(f"模式风险预警生成失败: {e}")
            return []

    def _generate_pattern_operation_suggestions(self, patterns: List[Dict], signals: List[str]) -> List[Dict]:
        """生成模式操作建议"""
        try:
            suggestions = []

            # 统计拉升和砸盘模式
            pullup_count = len([p for p in patterns if p['type'] in ['90度拉板', '斜刺波', '阶梯式拉升', 'V型反转', '平台突破']])
            crash_count = len([p for p in patterns if p['type'] == '砸盘'])

            if pullup_count > crash_count and pullup_count >= 2:
                suggestions.append({
                    'type': '买入建议',
                    'confidence': '较高',
                    'reason': f'发现{pullup_count}个拉升模式，游资操作积极',
                    'action': '可考虑适量买入，关注拉升延续性',
                    'risk_level': '中等'
                })
            elif crash_count > pullup_count and crash_count >= 2:
                suggestions.append({
                    'type': '卖出建议',
                    'confidence': '较高',
                    'reason': f'发现{crash_count}个砸盘模式，下跌压力明显',
                    'action': '建议减仓或止损，避免进一步损失',
                    'risk_level': '较高'
                })
            else:
                suggestions.append({
                    'type': '观望建议',
                    'confidence': '中等',
                    'reason': '拉升和砸盘模式并存，方向不明确',
                    'action': '暂时观望，等待明确的方向信号',
                    'risk_level': '中等'
                })

            return suggestions

        except Exception as e:
            logger.error(f"模式操作建议生成失败: {e}")
            return []

    def _generate_pattern_summary(self, patterns: List[Dict]) -> Dict:
        """生成模式摘要"""
        try:
            summary = {
                'total_patterns': len(patterns),
                'pattern_types': {},
                'strength_distribution': {},
                'time_span': '',
                'dominant_pattern': ''
            }

            if not patterns:
                return summary

            # 统计模式类型
            for pattern in patterns:
                pattern_type = pattern['type']
                summary['pattern_types'][pattern_type] = summary['pattern_types'].get(pattern_type, 0) + 1

            # 统计强度分布
            for pattern in patterns:
                strength = pattern.get('strength', '未知')
                summary['strength_distribution'][strength] = summary['strength_distribution'].get(strength, 0) + 1

            # 计算时间跨度
            if patterns:
                start_times = [p.get('start_time', '') for p in patterns if p.get('start_time')]
                end_times = [p.get('end_time', '') for p in patterns if p.get('end_time')]

                if start_times and end_times:
                    summary['time_span'] = f"{min(start_times)} - {max(end_times)}"

            # 确定主导模式
            if summary['pattern_types']:
                dominant_pattern = max(summary['pattern_types'], key=summary['pattern_types'].get)
                summary['dominant_pattern'] = dominant_pattern

            return summary

        except Exception as e:
            logger.error(f"模式摘要生成失败: {e}")
            return {}

    def _generate_price_pattern_summary(self, patterns: List[Dict], signals: List[str]) -> str:
        """生成价格模式总体摘要"""
        try:
            if not patterns:
                return '无明显价格模式'

            summary_parts = []

            # 模式数量
            summary_parts.append(f"{len(patterns)}个价格模式")

            # 主要模式类型
            pattern_types = {}
            for pattern in patterns:
                pattern_type = pattern['type']
                pattern_types[pattern_type] = pattern_types.get(pattern_type, 0) + 1

            if pattern_types:
                main_pattern = max(pattern_types, key=pattern_types.get)
                summary_parts.append(f"主要为{main_pattern}")

            # 信号数量
            if signals:
                summary_parts.append(f"{len(signals)}个操作信号")

            # 强度评估
            strong_count = len([p for p in patterns if p.get('strength') == '强'])
            if strong_count > 0:
                summary_parts.append(f"{strong_count}个强势模式")

            return ', '.join(summary_parts)

        except Exception as e:
            logger.error(f"价格模式摘要生成失败: {e}")
            return "摘要生成失败"

    def analyze_key_time_volume_surge(self, tick_data: pd.DataFrame, code: str) -> Dict:
        """分析关键时点成交量放大 - 识别游资关键时点操作"""
        try:
            result = {
                'analysis_available': False,
                'key_time_analysis': {},
                'volume_surge_events': [],
                'gaming_time_signals': [],
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': ''
            }

            if tick_data.empty or '成交量' not in tick_data.columns or '时间' not in tick_data.columns:
                result['summary'] = '无有效成交量数据可分析'
                return result

            logger.info(f"开始分析{code}的关键时点成交量放大")

            # 数据预处理
            processed_data = self._prepare_volume_analysis_data(tick_data)

            if processed_data.empty:
                result['summary'] = '成交量数据预处理失败'
                return result

            # 1. 定义关键时点
            key_time_periods = self._define_key_time_periods()

            # 2. 分析各关键时点的成交量特征
            key_time_analysis = self._analyze_key_time_periods(processed_data, key_time_periods)

            # 3. 识别成交量异常放大事件
            volume_surge_events = self._identify_volume_surge_events(processed_data)

            # 4. 生成游资时点操作信号
            gaming_time_signals = self._generate_gaming_time_signals(key_time_analysis, volume_surge_events)

            # 5. 生成风险预警
            risk_warnings = self._generate_volume_risk_warnings(key_time_analysis, volume_surge_events)

            # 6. 生成操作建议
            operation_suggestions = self._generate_volume_operation_suggestions(gaming_time_signals, risk_warnings)

            # 7. 生成分析摘要
            summary = self._generate_volume_analysis_summary(key_time_analysis, volume_surge_events, gaming_time_signals)

            result.update({
                'analysis_available': True,
                'key_time_analysis': key_time_analysis,
                'volume_surge_events': volume_surge_events,
                'gaming_time_signals': gaming_time_signals,
                'risk_warnings': risk_warnings,
                'operation_suggestions': operation_suggestions,
                'summary': summary
            })

            logger.info(f"关键时点成交量分析完成: {len(volume_surge_events)}个放大事件, {len(gaming_time_signals)}个游资信号")
            return result

        except Exception as e:
            logger.error(f"关键时点成交量分析失败: {e}")
            return {
                'analysis_available': False,
                'key_time_analysis': {},
                'volume_surge_events': [],
                'gaming_time_signals': [],
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': f'关键时点成交量分析失败: {str(e)}',
                'error': str(e)
            }

    def _prepare_volume_analysis_data(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """为成交量分析准备数据"""
        try:
            if tick_data.empty:
                return pd.DataFrame()

            # 复制数据
            data = tick_data.copy()

            # 确保必要的列存在
            required_columns = ['时间', '成交量']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                return pd.DataFrame()

            # 数据类型转换
            data['成交量'] = pd.to_numeric(data['成交量'], errors='coerce')

            # 移除无效数据
            data = data.dropna(subset=['成交量'])

            if data.empty:
                return pd.DataFrame()

            # 按时间排序
            data = data.sort_values('时间')
            data = data.reset_index(drop=True)

            # 计算成交量统计指标
            data['成交量_MA5'] = data['成交量'].rolling(window=5, min_periods=1).mean()
            data['成交量_MA10'] = data['成交量'].rolling(window=10, min_periods=1).mean()

            # 计算成交量相对变化
            data['成交量变化率'] = data['成交量'].pct_change() * 100

            # 计算成交量相对于均值的倍数
            overall_mean = data['成交量'].mean()
            data['成交量倍数'] = data['成交量'] / overall_mean

            # 添加时间解析
            data['小时'] = data['时间'].apply(self._extract_hour_from_time)
            data['分钟'] = data['时间'].apply(self._extract_minute_from_time)

            logger.info(f"成交量分析数据预处理完成: {len(data)}条记录")
            return data

        except Exception as e:
            logger.error(f"成交量分析数据预处理失败: {e}")
            return pd.DataFrame()

    def _extract_hour_from_time(self, time_str: str) -> int:
        """从时间字符串提取小时"""
        try:
            from datetime import datetime
            time_obj = datetime.strptime(time_str, '%H:%M:%S')
            return time_obj.hour
        except:
            return -1

    def _extract_minute_from_time(self, time_str: str) -> int:
        """从时间字符串提取分钟"""
        try:
            from datetime import datetime
            time_obj = datetime.strptime(time_str, '%H:%M:%S')
            return time_obj.minute
        except:
            return -1

    def _define_key_time_periods(self) -> Dict:
        """定义关键时点"""
        try:
            key_periods = {
                '集合竞价': {
                    'start_hour': 9, 'start_minute': 25,
                    'end_hour': 9, 'end_minute': 30,
                    'description': '集合竞价时段',
                    'importance': '极高'
                },
                '开盘30分钟': {
                    'start_hour': 9, 'start_minute': 30,
                    'end_hour': 10, 'end_minute': 0,
                    'description': '开盘30分钟',
                    'importance': '极高'
                },
                '10点整': {
                    'start_hour': 9, 'start_minute': 55,
                    'end_hour': 10, 'end_minute': 5,
                    'description': '10点整前后',
                    'importance': '高'
                },
                '11点整': {
                    'start_hour': 10, 'start_minute': 55,
                    'end_hour': 11, 'end_minute': 5,
                    'description': '11点整前后',
                    'importance': '高'
                },
                '午盘收盘': {
                    'start_hour': 11, 'start_minute': 25,
                    'end_hour': 11, 'end_minute': 30,
                    'description': '午盘收盘前5分钟',
                    'importance': '高'
                },
                '午盘开盘': {
                    'start_hour': 13, 'start_minute': 0,
                    'end_hour': 13, 'end_minute': 10,
                    'description': '午盘开盘10分钟',
                    'importance': '高'
                },
                '14点整': {
                    'start_hour': 13, 'start_minute': 55,
                    'end_hour': 14, 'end_minute': 5,
                    'description': '14点整前后',
                    'importance': '高'
                },
                '尾盘30分钟': {
                    'start_hour': 14, 'start_minute': 30,
                    'end_hour': 15, 'end_minute': 0,
                    'description': '尾盘30分钟',
                    'importance': '极高'
                },
                '收盘集合竞价': {
                    'start_hour': 14, 'start_minute': 57,
                    'end_hour': 15, 'end_minute': 0,
                    'description': '收盘集合竞价',
                    'importance': '极高'
                }
            }

            return key_periods

        except Exception as e:
            logger.error(f"关键时点定义失败: {e}")
            return {}

    def _analyze_key_time_periods(self, data: pd.DataFrame, key_periods: Dict) -> Dict:
        """分析各关键时点的成交量特征"""
        try:
            analysis_result = {}

            # 计算全天成交量基准
            overall_stats = {
                'mean_volume': data['成交量'].mean(),
                'median_volume': data['成交量'].median(),
                'std_volume': data['成交量'].std(),
                'total_records': len(data)
            }

            for period_name, period_info in key_periods.items():
                try:
                    # 筛选时间段数据
                    period_data = self._filter_time_period_data(
                        data,
                        period_info['start_hour'], period_info['start_minute'],
                        period_info['end_hour'], period_info['end_minute']
                    )

                    if period_data.empty:
                        analysis_result[period_name] = {
                            'has_data': False,
                            'description': period_info['description'],
                            'importance': period_info['importance']
                        }
                        continue

                    # 计算时段统计
                    period_stats = {
                        'has_data': True,
                        'description': period_info['description'],
                        'importance': period_info['importance'],
                        'record_count': len(period_data),
                        'total_volume': period_data['成交量'].sum(),
                        'avg_volume': period_data['成交量'].mean(),
                        'max_volume': period_data['成交量'].max(),
                        'min_volume': period_data['成交量'].min(),
                        'volume_std': period_data['成交量'].std(),
                        'time_span': f"{period_data['时间'].iloc[0]} - {period_data['时间'].iloc[-1]}" if len(period_data) > 0 else ''
                    }

                    # 计算相对于全天的倍数
                    period_stats['avg_volume_ratio'] = period_stats['avg_volume'] / overall_stats['mean_volume']
                    period_stats['max_volume_ratio'] = period_stats['max_volume'] / overall_stats['mean_volume']

                    # 判断是否异常放大
                    if period_stats['avg_volume_ratio'] >= 2.0:
                        period_stats['volume_status'] = '显著放大'
                        period_stats['anomaly_level'] = '高'
                    elif period_stats['avg_volume_ratio'] >= 1.5:
                        period_stats['volume_status'] = '明显放大'
                        period_stats['anomaly_level'] = '中'
                    elif period_stats['avg_volume_ratio'] >= 1.2:
                        period_stats['volume_status'] = '轻微放大'
                        period_stats['anomaly_level'] = '低'
                    else:
                        period_stats['volume_status'] = '正常'
                        period_stats['anomaly_level'] = '无'

                    # 识别最大成交量时点
                    max_volume_idx = period_data['成交量'].idxmax()
                    period_stats['peak_volume_time'] = period_data.loc[max_volume_idx, '时间']
                    period_stats['peak_volume_value'] = period_data.loc[max_volume_idx, '成交量']

                    analysis_result[period_name] = period_stats

                except Exception as e:
                    logger.error(f"分析时段{period_name}失败: {e}")
                    analysis_result[period_name] = {
                        'has_data': False,
                        'error': str(e),
                        'description': period_info['description'],
                        'importance': period_info['importance']
                    }

            return analysis_result

        except Exception as e:
            logger.error(f"关键时点分析失败: {e}")
            return {}

    def _filter_time_period_data(self, data: pd.DataFrame, start_hour: int, start_minute: int,
                                end_hour: int, end_minute: int) -> pd.DataFrame:
        """筛选指定时间段的数据"""
        try:
            # 处理跨小时的情况
            if start_hour == end_hour:
                # 同一小时内
                mask = (
                    (data['小时'] == start_hour) &
                    (data['分钟'] >= start_minute) &
                    (data['分钟'] <= end_minute)
                )
            else:
                # 跨小时
                mask = (
                    ((data['小时'] == start_hour) & (data['分钟'] >= start_minute)) |
                    ((data['小时'] > start_hour) & (data['小时'] < end_hour)) |
                    ((data['小时'] == end_hour) & (data['分钟'] <= end_minute))
                )

            return data[mask].copy()

        except Exception as e:
            logger.error(f"时间段数据筛选失败: {e}")
            return pd.DataFrame()

    def _identify_volume_surge_events(self, data: pd.DataFrame) -> List[Dict]:
        """识别成交量异常放大事件"""
        try:
            surge_events = []

            if len(data) < 10:
                return surge_events

            # 计算成交量基准
            mean_volume = data['成交量'].mean()
            std_volume = data['成交量'].std()

            # 定义放大阈值
            surge_thresholds = {
                '极度放大': mean_volume + 3 * std_volume,
                '显著放大': mean_volume + 2 * std_volume,
                '明显放大': mean_volume + 1.5 * std_volume
            }

            # 滑动窗口检测放大事件
            window_size = 5  # 5笔成交的窗口

            for i in range(len(data) - window_size + 1):
                window_data = data.iloc[i:i + window_size]

                # 计算窗口内的成交量特征
                window_max_volume = window_data['成交量'].max()
                window_avg_volume = window_data['成交量'].mean()
                window_total_volume = window_data['成交量'].sum()

                # 判断放大级别
                surge_level = None
                surge_ratio = window_max_volume / mean_volume

                if window_max_volume >= surge_thresholds['极度放大']:
                    surge_level = '极度放大'
                elif window_max_volume >= surge_thresholds['显著放大']:
                    surge_level = '显著放大'
                elif window_max_volume >= surge_thresholds['明显放大']:
                    surge_level = '明显放大'

                if surge_level:
                    # 找到最大成交量的具体时点
                    max_volume_idx = window_data['成交量'].idxmax()
                    max_volume_time = window_data.loc[max_volume_idx, '时间']
                    max_volume_value = window_data.loc[max_volume_idx, '成交量']

                    # 判断时间段类型
                    time_period = self._classify_time_period_for_volume(max_volume_time)

                    surge_event = {
                        'time': max_volume_time,
                        'volume': int(max_volume_value),
                        'surge_level': surge_level,
                        'surge_ratio': round(surge_ratio, 2),
                        'time_period': time_period,
                        'window_avg_volume': round(window_avg_volume, 0),
                        'window_total_volume': int(window_total_volume),
                        'start_time': window_data['时间'].iloc[0],
                        'end_time': window_data['时间'].iloc[-1],
                        'duration_records': len(window_data)
                    }

                    surge_events.append(surge_event)

                    # 避免重叠检测
                    i += window_size // 2

            # 去重和排序
            surge_events = self._deduplicate_surge_events(surge_events)
            surge_events.sort(key=lambda x: x['time'])

            logger.info(f"识别成交量放大事件: {len(surge_events)}个")
            return surge_events

        except Exception as e:
            logger.error(f"成交量放大事件识别失败: {e}")
            return []

    def _classify_time_period_for_volume(self, time_str: str) -> str:
        """为成交量分析分类时间段"""
        try:
            from datetime import datetime
            time_obj = datetime.strptime(time_str, '%H:%M:%S')
            hour = time_obj.hour
            minute = time_obj.minute

            # 详细时间段分类
            if hour == 9 and 25 <= minute <= 30:
                return '集合竞价'
            elif hour == 9 and minute >= 30:
                return '开盘30分钟'
            elif hour == 10 and minute <= 0:
                return '开盘30分钟'
            elif 9 <= hour <= 10 and 55 <= minute <= 59 or hour == 10 and 0 <= minute <= 5:
                return '10点整'
            elif 10 <= hour <= 11 and 55 <= minute <= 59 or hour == 11 and 0 <= minute <= 5:
                return '11点整'
            elif hour == 11 and 25 <= minute <= 30:
                return '午盘收盘'
            elif hour == 13 and 0 <= minute <= 10:
                return '午盘开盘'
            elif 13 <= hour <= 14 and 55 <= minute <= 59 or hour == 14 and 0 <= minute <= 5:
                return '14点整'
            elif hour == 14 and minute >= 30:
                return '尾盘30分钟'
            elif hour == 15:
                return '收盘集合竞价'
            elif 10 <= hour <= 11:
                return '上午交易'
            elif 13 <= hour <= 14 and minute < 30:
                return '下午交易'
            else:
                return '其他时段'

        except Exception as e:
            logger.error(f"时间段分类失败: {e}")
            return '未知时段'

    def _deduplicate_surge_events(self, events: List[Dict]) -> List[Dict]:
        """去除重叠的放大事件"""
        try:
            if not events:
                return events

            # 按时间排序
            events.sort(key=lambda x: x['time'])

            deduplicated = []
            for event in events:
                # 检查是否与已有事件时间过于接近（5分钟内）
                is_duplicate = False
                for existing in deduplicated:
                    try:
                        from datetime import datetime
                        event_time = datetime.strptime(event['time'], '%H:%M:%S')
                        existing_time = datetime.strptime(existing['time'], '%H:%M:%S')

                        time_diff = abs((event_time - existing_time).total_seconds())
                        if time_diff <= 300:  # 5分钟内
                            # 保留成交量更大的事件
                            if event['volume'] > existing['volume']:
                                deduplicated.remove(existing)
                                deduplicated.append(event)
                            is_duplicate = True
                            break
                    except:
                        continue

                if not is_duplicate:
                    deduplicated.append(event)

            return deduplicated

        except Exception as e:
            logger.error(f"放大事件去重失败: {e}")
            return events

    def _generate_gaming_time_signals(self, key_time_analysis: Dict, surge_events: List[Dict]) -> List[str]:
        """生成游资时点操作信号"""
        try:
            signals = []

            # 1. 分析关键时点的成交量异常
            high_importance_periods = ['集合竞价', '开盘30分钟', '尾盘30分钟', '收盘集合竞价']

            for period_name in high_importance_periods:
                if period_name in key_time_analysis:
                    period_data = key_time_analysis[period_name]

                    if period_data.get('has_data', False):
                        volume_status = period_data.get('volume_status', '正常')
                        avg_ratio = period_data.get('avg_volume_ratio', 1.0)

                        if volume_status == '显著放大':
                            signals.append(f'{period_data["description"]}成交量显著放大({avg_ratio:.1f}倍)，游资重点关注')
                        elif volume_status == '明显放大':
                            signals.append(f'{period_data["description"]}成交量明显放大({avg_ratio:.1f}倍)，有游资操作迹象')

            # 2. 分析成交量放大事件的时间分布
            if surge_events:
                # 按时间段统计放大事件
                period_surge_count = {}
                for event in surge_events:
                    period = event['time_period']
                    period_surge_count[period] = period_surge_count.get(period, 0) + 1

                # 识别集中放大的时段
                for period, count in period_surge_count.items():
                    if count >= 3:
                        signals.append(f'{period}发现{count}次成交量放大，游资操作集中')
                    elif count >= 2:
                        signals.append(f'{period}发现{count}次成交量放大，有游资活动')

                # 分析极度放大事件
                extreme_surges = [e for e in surge_events if e['surge_level'] == '极度放大']
                if len(extreme_surges) >= 2:
                    signals.append(f'发现{len(extreme_surges)}次极度成交量放大，游资操作明显')
                elif len(extreme_surges) >= 1:
                    signals.append(f'发现极度成交量放大，可能有游资大单操作')

            # 3. 分析整点效应
            hour_effects = []
            for period_name in ['10点整', '11点整', '14点整']:
                if period_name in key_time_analysis:
                    period_data = key_time_analysis[period_name]
                    if period_data.get('has_data', False) and period_data.get('avg_volume_ratio', 1.0) >= 1.3:
                        hour_effects.append(period_name)

            if len(hour_effects) >= 2:
                signals.append(f'多个整点时段成交量放大，游资可能有计划性操作')

            return signals

        except Exception as e:
            logger.error(f"游资时点信号生成失败: {e}")
            return []

    def _generate_volume_risk_warnings(self, key_time_analysis: Dict, surge_events: List[Dict]) -> List[str]:
        """生成成交量风险预警"""
        try:
            warnings = []

            # 1. 尾盘异常风险
            if '尾盘30分钟' in key_time_analysis:
                tail_data = key_time_analysis['尾盘30分钟']
                if tail_data.get('has_data', False):
                    if tail_data.get('avg_volume_ratio', 1.0) >= 2.5:
                        warnings.append('高风险：尾盘成交量异常放大，可能有重大消息或操作')
                    elif tail_data.get('avg_volume_ratio', 1.0) >= 2.0:
                        warnings.append('中风险：尾盘成交量显著放大，注意异动')

            # 2. 集合竞价异常风险
            if '集合竞价' in key_time_analysis:
                auction_data = key_time_analysis['集合竞价']
                if auction_data.get('has_data', False):
                    if auction_data.get('avg_volume_ratio', 1.0) >= 3.0:
                        warnings.append('高风险：集合竞价成交量极度放大，可能有重大利好/利空')

            # 3. 连续放大风险
            if len(surge_events) >= 5:
                warnings.append(f'中风险：发现{len(surge_events)}次成交量放大，市场波动加剧')

            # 4. 极度放大风险
            extreme_count = len([e for e in surge_events if e['surge_level'] == '极度放大'])
            if extreme_count >= 3:
                warnings.append('高风险：多次极度成交量放大，价格可能剧烈波动')

            return warnings

        except Exception as e:
            logger.error(f"成交量风险预警生成失败: {e}")
            return []

    def _generate_volume_operation_suggestions(self, signals: List[str], warnings: List[str]) -> List[Dict]:
        """生成成交量操作建议"""
        try:
            suggestions = []

            # 根据信号和预警生成建议
            signal_count = len(signals)
            warning_count = len(warnings)
            high_risk_warnings = len([w for w in warnings if '高风险' in w])

            if signal_count >= 3 and high_risk_warnings == 0:
                suggestions.append({
                    'type': '关注建议',
                    'confidence': '较高',
                    'reason': f'发现{signal_count}个游资操作信号，成交量异常活跃',
                    'action': '密切关注后续走势，准备跟进操作',
                    'risk_level': '中等'
                })
            elif signal_count >= 2 and warning_count <= 1:
                suggestions.append({
                    'type': '谨慎关注',
                    'confidence': '中等',
                    'reason': f'发现{signal_count}个游资信号，但需注意风险',
                    'action': '观察成交量变化，等待更明确信号',
                    'risk_level': '中等'
                })
            elif high_risk_warnings >= 2:
                suggestions.append({
                    'type': '风险警告',
                    'confidence': '高',
                    'reason': f'发现{high_risk_warnings}个高风险预警',
                    'action': '暂停操作，等待市场稳定',
                    'risk_level': '高'
                })
            else:
                suggestions.append({
                    'type': '正常观察',
                    'confidence': '中等',
                    'reason': '成交量变化在正常范围内',
                    'action': '继续观察，无需特殊操作',
                    'risk_level': '低'
                })

            return suggestions

        except Exception as e:
            logger.error(f"成交量操作建议生成失败: {e}")
            return []

    def _generate_volume_analysis_summary(self, key_time_analysis: Dict, surge_events: List[Dict], signals: List[str]) -> str:
        """生成成交量分析摘要"""
        try:
            summary_parts = []

            # 关键时点统计
            active_periods = len([p for p in key_time_analysis.values() if p.get('has_data', False)])
            summary_parts.append(f"{active_periods}个关键时点有数据")

            # 放大事件统计
            if surge_events:
                summary_parts.append(f"{len(surge_events)}次成交量放大")

                # 统计放大级别
                extreme_count = len([e for e in surge_events if e['surge_level'] == '极度放大'])
                significant_count = len([e for e in surge_events if e['surge_level'] == '显著放大'])

                if extreme_count > 0:
                    summary_parts.append(f"{extreme_count}次极度放大")
                if significant_count > 0:
                    summary_parts.append(f"{significant_count}次显著放大")

            # 游资信号统计
            if signals:
                summary_parts.append(f"{len(signals)}个游资信号")

            # 异常时段识别
            abnormal_periods = []
            for period_name, period_data in key_time_analysis.items():
                if period_data.get('has_data', False) and period_data.get('volume_status', '正常') != '正常':
                    abnormal_periods.append(period_name)

            if abnormal_periods:
                summary_parts.append(f"{len(abnormal_periods)}个时段异常")

            return ', '.join(summary_parts) if summary_parts else '成交量正常'

        except Exception as e:
            logger.error(f"成交量分析摘要生成失败: {e}")
            return "摘要生成失败"

    def analyze_tail_market_operations(self, tick_data: pd.DataFrame, code: str) -> Dict:
        """分析尾盘拉升砸盘操作 - 识别游资尾盘手法"""
        try:
            result = {
                'analysis_available': False,
                'tail_operations': [],
                'operation_patterns': {},
                'gaming_signals': [],
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': ''
            }

            if tick_data.empty or '价格' not in tick_data.columns or '时间' not in tick_data.columns:
                result['summary'] = '无有效价格数据可分析'
                return result

            logger.info(f"开始分析{code}的尾盘拉升砸盘操作")

            # 数据预处理
            processed_data = self._prepare_tail_analysis_data(tick_data)

            if processed_data.empty:
                result['summary'] = '尾盘数据预处理失败'
                return result

            # 1. 筛选尾盘时段数据（14:30-15:00）
            tail_data = self._filter_tail_market_data(processed_data)

            if tail_data.empty:
                result['summary'] = '无尾盘时段数据'
                return result

            # 2. 识别尾盘拉升操作
            pullup_operations = self._identify_tail_pullup_operations(tail_data)

            # 3. 识别尾盘砸盘操作
            crash_operations = self._identify_tail_crash_operations(tail_data)

            # 4. 识别尾盘洗盘操作
            wash_operations = self._identify_tail_wash_operations(tail_data)

            # 5. 识别收盘前冲刺
            final_sprint_operations = self._identify_final_sprint_operations(tail_data)

            # 6. 识别尾盘护盘操作
            protection_operations = self._identify_tail_protection_operations(tail_data)

            # 合并所有操作
            all_operations = []
            all_operations.extend(pullup_operations)
            all_operations.extend(crash_operations)
            all_operations.extend(wash_operations)
            all_operations.extend(final_sprint_operations)
            all_operations.extend(protection_operations)

            # 7. 分析操作模式
            operation_patterns = self._analyze_tail_operation_patterns(all_operations, tail_data)

            # 8. 生成游资信号
            gaming_signals = self._generate_tail_gaming_signals(all_operations, operation_patterns)

            # 9. 生成风险预警
            risk_warnings = self._generate_tail_risk_warnings(all_operations, operation_patterns)

            # 10. 生成操作建议
            operation_suggestions = self._generate_tail_operation_suggestions(gaming_signals, risk_warnings)

            # 11. 生成分析摘要
            summary = self._generate_tail_analysis_summary(all_operations, gaming_signals)

            result.update({
                'analysis_available': True,
                'tail_operations': all_operations,
                'operation_patterns': operation_patterns,
                'gaming_signals': gaming_signals,
                'risk_warnings': risk_warnings,
                'operation_suggestions': operation_suggestions,
                'summary': summary
            })

            logger.info(f"尾盘操作分析完成: {len(all_operations)}个操作, {len(gaming_signals)}个游资信号")
            return result

        except Exception as e:
            logger.error(f"尾盘拉升砸盘分析失败: {e}")
            return {
                'analysis_available': False,
                'tail_operations': [],
                'operation_patterns': {},
                'gaming_signals': [],
                'risk_warnings': [],
                'operation_suggestions': [],
                'summary': f'尾盘操作分析失败: {str(e)}',
                'error': str(e)
            }

    def _prepare_tail_analysis_data(self, tick_data: pd.DataFrame) -> pd.DataFrame:
        """为尾盘分析准备数据"""
        try:
            if tick_data.empty:
                return pd.DataFrame()

            # 复制数据
            data = tick_data.copy()

            # 确保必要的列存在
            required_columns = ['时间', '价格']
            missing_columns = [col for col in required_columns if col not in data.columns]
            if missing_columns:
                logger.error(f"缺少必要的列: {missing_columns}")
                return pd.DataFrame()

            # 数据类型转换
            data['价格'] = pd.to_numeric(data['价格'], errors='coerce')
            if '成交量' in data.columns:
                data['成交量'] = pd.to_numeric(data['成交量'], errors='coerce')
            if '成交额' in data.columns:
                data['成交额'] = pd.to_numeric(data['成交额'], errors='coerce')

            # 移除无效数据
            data = data.dropna(subset=['价格'])

            if data.empty:
                return pd.DataFrame()

            # 按时间排序
            data = data.sort_values('时间')
            data = data.reset_index(drop=True)

            # 计算价格变化
            data['价格变化'] = data['价格'].diff()
            data['价格变化率'] = data['价格'].pct_change() * 100

            # 计算累计涨跌幅（相对于第一个价格）
            if len(data) > 0:
                first_price = data['价格'].iloc[0]
                data['累计涨跌幅'] = (data['价格'] - first_price) / first_price * 100

            # 添加时间解析
            data['小时'] = data['时间'].apply(self._extract_hour_from_time)
            data['分钟'] = data['时间'].apply(self._extract_minute_from_time)

            # 计算移动平均
            if len(data) >= 5:
                data['价格_MA5'] = data['价格'].rolling(window=5, min_periods=1).mean()
            else:
                data['价格_MA5'] = data['价格']

            logger.info(f"尾盘分析数据预处理完成: {len(data)}条记录")
            return data

        except Exception as e:
            logger.error(f"尾盘分析数据预处理失败: {e}")
            return pd.DataFrame()

    def _filter_tail_market_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """筛选尾盘时段数据（14:30-15:00）"""
        try:
            if data.empty:
                return pd.DataFrame()

            # 筛选14:30-15:00的数据
            tail_mask = (
                ((data['小时'] == 14) & (data['分钟'] >= 30)) |
                (data['小时'] == 15)
            )

            tail_data = data[tail_mask].copy()

            if not tail_data.empty:
                # 重新计算尾盘内的相对变化
                tail_start_price = tail_data['价格'].iloc[0]
                tail_data['尾盘涨跌幅'] = (tail_data['价格'] - tail_start_price) / tail_start_price * 100

                logger.info(f"筛选尾盘数据完成: {len(tail_data)}条记录")
            else:
                logger.warning("无尾盘时段数据")

            return tail_data

        except Exception as e:
            logger.error(f"尾盘数据筛选失败: {e}")
            return pd.DataFrame()

    def _identify_tail_pullup_operations(self, tail_data: pd.DataFrame) -> List[Dict]:
        """识别尾盘拉升操作"""
        try:
            operations = []

            if len(tail_data) < 5:
                return operations

            # 滑动窗口检测拉升
            window_size = 10  # 10笔成交的窗口
            min_rise_rate = 1.0  # 最小拉升1%

            for i in range(len(tail_data) - window_size + 1):
                window_data = tail_data.iloc[i:i + window_size]

                # 计算窗口内的价格变化
                start_price = window_data['价格'].iloc[0]
                end_price = window_data['价格'].iloc[-1]
                max_price = window_data['价格'].max()

                # 计算拉升幅度
                rise_rate = (end_price - start_price) / start_price * 100
                max_rise_rate = (max_price - start_price) / start_price * 100

                # 检查是否符合拉升特征
                if rise_rate >= min_rise_rate and max_rise_rate >= min_rise_rate:
                    # 检查拉升的连续性
                    consecutive_rises = 0
                    for j in range(1, len(window_data)):
                        if window_data['价格'].iloc[j] >= window_data['价格'].iloc[j-1]:
                            consecutive_rises += 1

                    rise_consistency = consecutive_rises / (len(window_data) - 1)

                    if rise_consistency >= 0.6:  # 60%以上连续上涨
                        # 判断拉升强度
                        if rise_rate >= 3.0:
                            strength = '强'
                        elif rise_rate >= 2.0:
                            strength = '中'
                        else:
                            strength = '弱'

                        operation = {
                            'type': '尾盘拉升',
                            'start_time': window_data['时间'].iloc[0],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'end_price': round(end_price, 2),
                            'max_price': round(max_price, 2),
                            'rise_rate': round(rise_rate, 2),
                            'max_rise_rate': round(max_rise_rate, 2),
                            'consistency': round(rise_consistency, 2),
                            'strength': strength,
                            'duration_records': len(window_data),
                            'confidence': round(rise_consistency * 100, 1)
                        }

                        operations.append(operation)

                        # 避免重叠检测
                        i += window_size // 2

            # 去重和排序
            operations = self._deduplicate_tail_operations(operations)
            operations.sort(key=lambda x: x['start_time'])

            logger.info(f"识别尾盘拉升操作: {len(operations)}个")
            return operations

        except Exception as e:
            logger.error(f"尾盘拉升识别失败: {e}")
            return []

    def _identify_tail_crash_operations(self, tail_data: pd.DataFrame) -> List[Dict]:
        """识别尾盘砸盘操作"""
        try:
            operations = []

            if len(tail_data) < 5:
                return operations

            # 滑动窗口检测砸盘
            window_size = 10  # 10笔成交的窗口
            min_fall_rate = 1.0  # 最小下跌1%

            for i in range(len(tail_data) - window_size + 1):
                window_data = tail_data.iloc[i:i + window_size]

                # 计算窗口内的价格变化
                start_price = window_data['价格'].iloc[0]
                end_price = window_data['价格'].iloc[-1]
                min_price = window_data['价格'].min()

                # 计算下跌幅度
                fall_rate = (start_price - end_price) / start_price * 100
                max_fall_rate = (start_price - min_price) / start_price * 100

                # 检查是否符合砸盘特征
                if fall_rate >= min_fall_rate and max_fall_rate >= min_fall_rate:
                    # 检查下跌的连续性
                    consecutive_falls = 0
                    for j in range(1, len(window_data)):
                        if window_data['价格'].iloc[j] <= window_data['价格'].iloc[j-1]:
                            consecutive_falls += 1

                    fall_consistency = consecutive_falls / (len(window_data) - 1)

                    if fall_consistency >= 0.6:  # 60%以上连续下跌
                        # 判断砸盘强度
                        if fall_rate >= 3.0:
                            strength = '强'
                        elif fall_rate >= 2.0:
                            strength = '中'
                        else:
                            strength = '弱'

                        operation = {
                            'type': '尾盘砸盘',
                            'start_time': window_data['时间'].iloc[0],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'end_price': round(end_price, 2),
                            'min_price': round(min_price, 2),
                            'fall_rate': round(fall_rate, 2),
                            'max_fall_rate': round(max_fall_rate, 2),
                            'consistency': round(fall_consistency, 2),
                            'strength': strength,
                            'duration_records': len(window_data),
                            'confidence': round(fall_consistency * 100, 1)
                        }

                        operations.append(operation)

                        # 避免重叠检测
                        i += window_size // 2

            # 去重和排序
            operations = self._deduplicate_tail_operations(operations)
            operations.sort(key=lambda x: x['start_time'])

            logger.info(f"识别尾盘砸盘操作: {len(operations)}个")
            return operations

        except Exception as e:
            logger.error(f"尾盘砸盘识别失败: {e}")
            return []

    def _identify_tail_wash_operations(self, tail_data: pd.DataFrame) -> List[Dict]:
        """识别尾盘洗盘操作（先砸后拉）"""
        try:
            operations = []

            if len(tail_data) < 20:
                return operations

            # 寻找先砸后拉的模式
            window_size = 20
            min_wash_amplitude = 1.5  # 最小洗盘幅度1.5%

            for i in range(len(tail_data) - window_size + 1):
                window_data = tail_data.iloc[i:i + window_size]

                # 寻找最低点
                min_price_idx = window_data['价格'].idxmin()
                min_price_pos = window_data.index.get_loc(min_price_idx)

                # 确保最低点不在边缘
                if 3 <= min_price_pos <= len(window_data) - 4:
                    before_data = window_data.iloc[:min_price_pos + 1]
                    after_data = window_data.iloc[min_price_pos:]

                    # 计算砸盘和拉升幅度
                    start_price = before_data['价格'].iloc[0]
                    min_price = before_data['价格'].iloc[-1]
                    end_price = after_data['价格'].iloc[-1]

                    fall_rate = (start_price - min_price) / start_price * 100
                    rise_rate = (end_price - min_price) / min_price * 100
                    total_amplitude = fall_rate + rise_rate

                    if total_amplitude >= min_wash_amplitude and fall_rate >= 0.8 and rise_rate >= 0.8:
                        operation = {
                            'type': '尾盘洗盘',
                            'start_time': window_data['时间'].iloc[0],
                            'wash_time': window_data['时间'].iloc[min_price_pos],
                            'end_time': window_data['时间'].iloc[-1],
                            'start_price': round(start_price, 2),
                            'min_price': round(min_price, 2),
                            'end_price': round(end_price, 2),
                            'fall_rate': round(fall_rate, 2),
                            'rise_rate': round(rise_rate, 2),
                            'wash_amplitude': round(total_amplitude, 2),
                            'strength': self._calculate_wash_strength(total_amplitude),
                            'duration_records': len(window_data),
                            'confidence': min(95, total_amplitude * 20)
                        }

                        operations.append(operation)

                        # 避免重叠检测
                        i += window_size // 2

            logger.info(f"识别尾盘洗盘操作: {len(operations)}个")
            return operations

        except Exception as e:
            logger.error(f"尾盘洗盘识别失败: {e}")
            return []

    def _identify_final_sprint_operations(self, tail_data: pd.DataFrame) -> List[Dict]:
        """识别收盘前冲刺操作（14:55-15:00）"""
        try:
            operations = []

            # 筛选收盘前5分钟数据
            final_data = tail_data[
                ((tail_data['小时'] == 14) & (tail_data['分钟'] >= 55)) |
                (tail_data['小时'] == 15)
            ].copy()

            if len(final_data) < 3:
                return operations

            # 计算收盘前冲刺
            start_price = final_data['价格'].iloc[0]
            end_price = final_data['价格'].iloc[-1]
            max_price = final_data['价格'].max()
            min_price = final_data['价格'].min()

            # 计算各种变化率
            final_change = (end_price - start_price) / start_price * 100
            max_rise = (max_price - start_price) / start_price * 100
            max_fall = (start_price - min_price) / start_price * 100

            # 判断冲刺类型
            if abs(final_change) >= 0.5 or max_rise >= 1.0 or max_fall >= 1.0:
                if final_change >= 0.5:
                    sprint_type = '收盘拉升'
                elif final_change <= -0.5:
                    sprint_type = '收盘砸盘'
                elif max_rise >= 1.0 and max_fall >= 1.0:
                    sprint_type = '收盘震荡'
                else:
                    sprint_type = '收盘异动'

                operation = {
                    'type': sprint_type,
                    'start_time': final_data['时间'].iloc[0],
                    'end_time': final_data['时间'].iloc[-1],
                    'start_price': round(start_price, 2),
                    'end_price': round(end_price, 2),
                    'max_price': round(max_price, 2),
                    'min_price': round(min_price, 2),
                    'final_change': round(final_change, 2),
                    'max_rise': round(max_rise, 2),
                    'max_fall': round(max_fall, 2),
                    'strength': self._calculate_sprint_strength(abs(final_change), max_rise, max_fall),
                    'duration_records': len(final_data),
                    'confidence': min(95, abs(final_change) * 30 + max_rise * 20)
                }

                operations.append(operation)

            logger.info(f"识别收盘前冲刺操作: {len(operations)}个")
            return operations

        except Exception as e:
            logger.error(f"收盘前冲刺识别失败: {e}")
            return []

    def _identify_tail_protection_operations(self, tail_data: pd.DataFrame) -> List[Dict]:
        """识别尾盘护盘操作"""
        try:
            operations = []

            if len(tail_data) < 10:
                return operations

            # 计算价格波动率
            price_std = tail_data['价格'].std()
            price_mean = tail_data['价格'].mean()
            volatility = price_std / price_mean * 100

            # 计算价格区间
            price_range = (tail_data['价格'].max() - tail_data['价格'].min()) / tail_data['价格'].mean() * 100

            # 判断是否为护盘（低波动率 + 小价格区间）
            if volatility <= 0.5 and price_range <= 1.0:
                # 进一步检查是否有护盘特征
                # 计算价格回归均值的次数
                mean_price = tail_data['价格'].mean()
                reversion_count = 0

                for i in range(1, len(tail_data)):
                    prev_price = tail_data['价格'].iloc[i-1]
                    curr_price = tail_data['价格'].iloc[i]

                    # 检查是否向均值回归
                    if abs(curr_price - mean_price) < abs(prev_price - mean_price):
                        reversion_count += 1

                reversion_ratio = reversion_count / (len(tail_data) - 1)

                if reversion_ratio >= 0.4:  # 40%以上的时间在向均值回归
                    operation = {
                        'type': '尾盘护盘',
                        'start_time': tail_data['时间'].iloc[0],
                        'end_time': tail_data['时间'].iloc[-1],
                        'avg_price': round(mean_price, 2),
                        'price_range': round(price_range, 2),
                        'volatility': round(volatility, 3),
                        'reversion_ratio': round(reversion_ratio, 2),
                        'strength': self._calculate_protection_strength(volatility, reversion_ratio),
                        'duration_records': len(tail_data),
                        'confidence': round(reversion_ratio * 100, 1)
                    }

                    operations.append(operation)

            logger.info(f"识别尾盘护盘操作: {len(operations)}个")
            return operations

        except Exception as e:
            logger.error(f"尾盘护盘识别失败: {e}")
            return []

    def _calculate_wash_strength(self, amplitude: float) -> str:
        """计算洗盘强度"""
        if amplitude >= 4.0:
            return '强'
        elif amplitude >= 2.5:
            return '中'
        else:
            return '弱'

    def _calculate_sprint_strength(self, final_change: float, max_rise: float, max_fall: float) -> str:
        """计算冲刺强度"""
        total_intensity = final_change + max_rise + max_fall
        if total_intensity >= 4.0:
            return '强'
        elif total_intensity >= 2.0:
            return '中'
        else:
            return '弱'

    def _calculate_protection_strength(self, volatility: float, reversion_ratio: float) -> str:
        """计算护盘强度"""
        protection_score = (1 - volatility) * reversion_ratio * 10
        if protection_score >= 3.0:
            return '强'
        elif protection_score >= 1.5:
            return '中'
        else:
            return '弱'

    def _deduplicate_tail_operations(self, operations: List[Dict]) -> List[Dict]:
        """去除重叠的尾盘操作"""
        try:
            if not operations:
                return operations

            # 按开始时间排序
            operations.sort(key=lambda x: x['start_time'])

            deduplicated = []
            for operation in operations:
                # 检查是否与已有操作重叠
                is_duplicate = False
                for existing in deduplicated:
                    if (operation['start_time'] <= existing['end_time'] and
                        operation['end_time'] >= existing['start_time']):
                        # 如果重叠，保留强度更高的
                        op_strength_score = self._get_strength_score(operation.get('strength', '弱'))
                        ex_strength_score = self._get_strength_score(existing.get('strength', '弱'))

                        if op_strength_score > ex_strength_score:
                            deduplicated.remove(existing)
                            deduplicated.append(operation)
                        is_duplicate = True
                        break

                if not is_duplicate:
                    deduplicated.append(operation)

            return deduplicated

        except Exception as e:
            logger.error(f"尾盘操作去重失败: {e}")
            return operations

    def _get_strength_score(self, strength: str) -> int:
        """获取强度分数"""
        strength_scores = {'强': 3, '中': 2, '弱': 1}
        return strength_scores.get(strength, 0)

    def _analyze_tail_operation_patterns(self, operations: List[Dict], tail_data: pd.DataFrame) -> Dict:
        """分析尾盘操作模式"""
        try:
            patterns = {
                'operation_count': len(operations),
                'operation_types': {},
                'strength_distribution': {},
                'time_distribution': {},
                'overall_trend': '',
                'dominant_pattern': '',
                'gaming_intensity': ''
            }

            if not operations:
                return patterns

            # 统计操作类型
            for op in operations:
                op_type = op['type']
                patterns['operation_types'][op_type] = patterns['operation_types'].get(op_type, 0) + 1

            # 统计强度分布
            for op in operations:
                strength = op.get('strength', '未知')
                patterns['strength_distribution'][strength] = patterns['strength_distribution'].get(strength, 0) + 1

            # 分析时间分布
            early_tail = 0  # 14:30-14:45
            mid_tail = 0    # 14:45-14:55
            final_tail = 0  # 14:55-15:00

            for op in operations:
                start_time = op['start_time']
                try:
                    from datetime import datetime
                    time_obj = datetime.strptime(start_time, '%H:%M:%S')
                    hour = time_obj.hour
                    minute = time_obj.minute

                    if hour == 14:
                        if 30 <= minute < 45:
                            early_tail += 1
                        elif 45 <= minute < 55:
                            mid_tail += 1
                        elif minute >= 55:
                            final_tail += 1
                    elif hour == 15:
                        final_tail += 1
                except:
                    continue

            patterns['time_distribution'] = {
                'early_tail': early_tail,
                'mid_tail': mid_tail,
                'final_tail': final_tail
            }

            # 分析整体趋势
            if not tail_data.empty:
                start_price = tail_data['价格'].iloc[0]
                end_price = tail_data['价格'].iloc[-1]
                overall_change = (end_price - start_price) / start_price * 100

                if overall_change >= 1.0:
                    patterns['overall_trend'] = '尾盘上涨'
                elif overall_change <= -1.0:
                    patterns['overall_trend'] = '尾盘下跌'
                else:
                    patterns['overall_trend'] = '尾盘震荡'

            # 确定主导模式
            if patterns['operation_types']:
                dominant_type = max(patterns['operation_types'], key=patterns['operation_types'].get)
                patterns['dominant_pattern'] = dominant_type

            # 评估游资操作强度
            strong_ops = patterns['strength_distribution'].get('强', 0)
            total_ops = len(operations)

            if total_ops >= 3 and strong_ops >= 2:
                patterns['gaming_intensity'] = '高'
            elif total_ops >= 2 and strong_ops >= 1:
                patterns['gaming_intensity'] = '中'
            elif total_ops >= 1:
                patterns['gaming_intensity'] = '低'
            else:
                patterns['gaming_intensity'] = '无'

            return patterns

        except Exception as e:
            logger.error(f"尾盘操作模式分析失败: {e}")
            return {}

    def _generate_tail_gaming_signals(self, operations: List[Dict], patterns: Dict) -> List[str]:
        """生成尾盘游资信号"""
        try:
            signals = []

            # 1. 基于操作类型生成信号
            operation_types = patterns.get('operation_types', {})

            for op_type, count in operation_types.items():
                if op_type == '尾盘拉升':
                    if count >= 2:
                        signals.append(f'多次尾盘拉升({count}次)，游资拉升意图明显')
                    else:
                        signals.append(f'发现尾盘拉升，游资可能有拉升计划')
                elif op_type == '尾盘砸盘':
                    if count >= 2:
                        signals.append(f'多次尾盘砸盘({count}次)，游资出货压力大')
                    else:
                        signals.append(f'发现尾盘砸盘，注意游资出货风险')
                elif op_type == '尾盘洗盘':
                    signals.append(f'发现尾盘洗盘，游资可能在清洗浮筹')
                elif op_type in ['收盘拉升', '收盘砸盘']:
                    signals.append(f'发现{op_type}，游资收盘前操作明显')
                elif op_type == '尾盘护盘':
                    signals.append(f'发现尾盘护盘，游资维稳意图明显')

            # 2. 基于操作强度生成信号
            strength_dist = patterns.get('strength_distribution', {})
            strong_count = strength_dist.get('强', 0)

            if strong_count >= 2:
                signals.append(f'发现{strong_count}个强势尾盘操作，游资操作力度大')

            # 3. 基于时间分布生成信号
            time_dist = patterns.get('time_distribution', {})
            final_tail_count = time_dist.get('final_tail', 0)

            if final_tail_count >= 1:
                signals.append(f'收盘前5分钟有{final_tail_count}次操作，游资收盘前活跃')

            # 4. 基于整体趋势生成信号
            overall_trend = patterns.get('overall_trend', '')
            gaming_intensity = patterns.get('gaming_intensity', '')

            if overall_trend == '尾盘上涨' and gaming_intensity in ['高', '中']:
                signals.append(f'尾盘整体上涨且游资操作强度{gaming_intensity}，看涨信号')
            elif overall_trend == '尾盘下跌' and gaming_intensity in ['高', '中']:
                signals.append(f'尾盘整体下跌且游资操作强度{gaming_intensity}，看跌信号')

            return signals

        except Exception as e:
            logger.error(f"尾盘游资信号生成失败: {e}")
            return []

    def _generate_tail_risk_warnings(self, operations: List[Dict], patterns: Dict) -> List[str]:
        """生成尾盘风险预警"""
        try:
            warnings = []

            # 1. 砸盘风险预警
            operation_types = patterns.get('operation_types', {})
            crash_count = operation_types.get('尾盘砸盘', 0) + operation_types.get('收盘砸盘', 0)

            if crash_count >= 2:
                warnings.append('高风险：多次尾盘砸盘，游资出货压力严重')
            elif crash_count >= 1:
                warnings.append('中风险：发现尾盘砸盘，注意下跌风险')

            # 2. 洗盘风险预警
            wash_count = operation_types.get('尾盘洗盘', 0)
            if wash_count >= 1:
                warnings.append('中风险：发现尾盘洗盘，短期可能震荡加剧')

            # 3. 收盘前异动风险
            time_dist = patterns.get('time_distribution', {})
            final_operations = time_dist.get('final_tail', 0)

            if final_operations >= 2:
                warnings.append('中风险：收盘前多次异动，可能有重大消息')

            # 4. 操作强度过高风险
            gaming_intensity = patterns.get('gaming_intensity', '')
            if gaming_intensity == '高':
                warnings.append('中风险：尾盘游资操作强度过高，价格波动可能加剧')

            # 5. 趋势反转风险
            overall_trend = patterns.get('overall_trend', '')
            pullup_count = operation_types.get('尾盘拉升', 0)

            if overall_trend == '尾盘上涨' and pullup_count >= 2:
                warnings.append('中风险：连续尾盘拉升后，注意获利回吐压力')

            return warnings

        except Exception as e:
            logger.error(f"尾盘风险预警生成失败: {e}")
            return []

    def _generate_tail_operation_suggestions(self, signals: List[str], warnings: List[str]) -> List[Dict]:
        """生成尾盘操作建议"""
        try:
            suggestions = []

            # 统计信号和预警类型
            signal_count = len(signals)
            warning_count = len(warnings)
            high_risk_warnings = len([w for w in warnings if '高风险' in w])

            # 分析信号内容
            bullish_signals = len([s for s in signals if any(keyword in s for keyword in ['拉升', '看涨', '上涨'])])
            bearish_signals = len([s for s in signals if any(keyword in s for keyword in ['砸盘', '看跌', '下跌', '出货'])])

            # 生成建议
            if high_risk_warnings >= 1:
                suggestions.append({
                    'type': '风险警告',
                    'confidence': '高',
                    'reason': f'发现{high_risk_warnings}个高风险预警',
                    'action': '建议减仓或止损，避免尾盘风险',
                    'risk_level': '高'
                })
            elif bullish_signals > bearish_signals and bullish_signals >= 2:
                suggestions.append({
                    'type': '买入建议',
                    'confidence': '较高',
                    'reason': f'发现{bullish_signals}个看涨信号，尾盘拉升明显',
                    'action': '可考虑适量买入，关注尾盘拉升延续性',
                    'risk_level': '中等'
                })
            elif bearish_signals > bullish_signals and bearish_signals >= 2:
                suggestions.append({
                    'type': '卖出建议',
                    'confidence': '较高',
                    'reason': f'发现{bearish_signals}个看跌信号，尾盘砸盘明显',
                    'action': '建议减仓，避免尾盘下跌风险',
                    'risk_level': '较高'
                })
            elif signal_count >= 2 and warning_count <= 1:
                suggestions.append({
                    'type': '关注建议',
                    'confidence': '中等',
                    'reason': f'发现{signal_count}个尾盘信号，游资操作活跃',
                    'action': '密切关注尾盘走势，准备相应操作',
                    'risk_level': '中等'
                })
            else:
                suggestions.append({
                    'type': '观望建议',
                    'confidence': '中等',
                    'reason': '尾盘操作信号不明确',
                    'action': '暂时观望，等待更明确的信号',
                    'risk_level': '低'
                })

            return suggestions

        except Exception as e:
            logger.error(f"尾盘操作建议生成失败: {e}")
            return []

    def _generate_tail_analysis_summary(self, operations: List[Dict], signals: List[str]) -> str:
        """生成尾盘分析摘要"""
        try:
            summary_parts = []

            # 操作数量
            if operations:
                summary_parts.append(f"{len(operations)}个尾盘操作")

                # 统计操作类型
                operation_types = {}
                for op in operations:
                    op_type = op['type']
                    operation_types[op_type] = operation_types.get(op_type, 0) + 1

                # 主要操作类型
                if operation_types:
                    main_type = max(operation_types, key=operation_types.get)
                    summary_parts.append(f"主要为{main_type}")

                # 强度统计
                strong_count = len([op for op in operations if op.get('strength') == '强'])
                if strong_count > 0:
                    summary_parts.append(f"{strong_count}个强势操作")
            else:
                summary_parts.append("无明显尾盘操作")

            # 信号数量
            if signals:
                summary_parts.append(f"{len(signals)}个游资信号")

            return ', '.join(summary_parts) if summary_parts else '尾盘正常'

        except Exception as e:
            logger.error(f"尾盘分析摘要生成失败: {e}")
            return "摘要生成失败"

    def analyze_historical_gaming_cycles(self, code: str) -> Dict:
        """分析历史炒作周期模式 - 基于6个月龙虎榜数据识别完整炒作周期"""
        try:
            result = {
                'analysis_available': False,
                'historical_cycles': [],
                'cycle_patterns': {},
                'gaming_characteristics': {},
                'time_intervals': [],
                'success_patterns': [],
                'summary': ''
            }

            logger.info(f"开始分析{code}的历史炒作周期模式")

            # 1. 获取6个月龙虎榜历史数据
            lhb_history = self._get_lhb_historical_data(code, months=6)

            if not lhb_history:
                result['summary'] = '无龙虎榜历史数据，该股票可能未被游资关注'
                return result

            # 2. 获取对应时期的价格数据
            price_history = self._get_price_data_for_lhb_periods(code, lhb_history)

            # 3. 识别完整的炒作周期
            gaming_cycles = self._identify_complete_gaming_cycles(lhb_history, price_history)

            # 4. 分析炒作周期模式
            cycle_patterns = self._analyze_cycle_patterns(gaming_cycles)

            # 5. 提取游资操作特征
            gaming_characteristics = self._extract_gaming_characteristics(gaming_cycles, lhb_history)

            # 6. 分析时间间隔规律
            time_intervals = self._analyze_time_intervals(gaming_cycles)

            # 7. 识别成功模式
            success_patterns = self._identify_success_patterns(gaming_cycles)

            # 8. 生成分析摘要
            summary = self._generate_cycle_analysis_summary(gaming_cycles, cycle_patterns)

            result.update({
                'analysis_available': True,
                'historical_cycles': gaming_cycles,
                'cycle_patterns': cycle_patterns,
                'gaming_characteristics': gaming_characteristics,
                'time_intervals': time_intervals,
                'success_patterns': success_patterns,
                'summary': summary
            })

            logger.info(f"历史炒作周期分析完成: 识别{len(gaming_cycles)}个完整周期")
            return result

        except Exception as e:
            logger.error(f"历史炒作周期分析失败: {e}")
            return {
                'analysis_available': False,
                'historical_cycles': [],
                'cycle_patterns': {},
                'gaming_characteristics': {},
                'time_intervals': [],
                'success_patterns': [],
                'summary': f'历史炒作周期分析失败: {str(e)}',
                'error': str(e)
            }

    def _get_lhb_historical_data(self, code: str, months: int = 6) -> List[Dict]:
        """获取6个月龙虎榜历史数据"""
        try:
            from datetime import datetime, timedelta
            import akshare as ak

            # 计算时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=months * 30)

            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')

            logger.info(f"获取{code}的龙虎榜历史数据: {start_date_str} - {end_date_str}")

            # 获取龙虎榜数据
            lhb_data = ak.stock_lhb_detail_em(start_date=start_date_str, end_date=end_date_str)

            if lhb_data.empty:
                return []

            # 筛选目标股票的数据
            stock_lhb = lhb_data[lhb_data['代码'] == code].copy()

            if stock_lhb.empty:
                return []

            # 转换为字典列表
            lhb_records = []
            for _, row in stock_lhb.iterrows():
                # 确保日期格式统一为字符串
                date_value = row['上榜日']
                if hasattr(date_value, 'strftime'):
                    date_str = date_value.strftime('%Y-%m-%d')
                else:
                    date_str = str(date_value)

                record = {
                    'date': date_str,
                    'reason': row.get('上榜原因', ''),
                    'close_price': row.get('收盘价', 0),
                    'change_rate': row.get('涨跌幅', 0),
                    'net_buy_amount': row.get('龙虎榜净买额', 0),
                    'buy_amount': row.get('龙虎榜买入额', 0),
                    'sell_amount': row.get('龙虎榜卖出额', 0),
                    'turnover_rate': row.get('换手率', 0)
                }
                lhb_records.append(record)

            # 按日期排序
            lhb_records.sort(key=lambda x: x['date'])

            logger.info(f"获取龙虎榜历史数据成功: {len(lhb_records)}条记录")
            return lhb_records

        except Exception as e:
            logger.error(f"获取龙虎榜历史数据失败: {e}")
            return []

    def _get_price_data_for_lhb_periods(self, code: str, lhb_history: List[Dict]) -> Dict:
        """获取龙虎榜期间的价格数据"""
        try:
            if not lhb_history:
                return {}

            import akshare as ak
            from datetime import datetime, timedelta

            # 获取所有龙虎榜日期
            lhb_dates = [record['date'] for record in lhb_history]

            # 扩展时间范围，获取前后几天的数据
            min_date = min(lhb_dates)
            max_date = max(lhb_dates)

            # 转换日期格式
            if isinstance(min_date, str):
                min_date = datetime.strptime(min_date, '%Y-%m-%d')
            if isinstance(max_date, str):
                max_date = datetime.strptime(max_date, '%Y-%m-%d')

            # 扩展前后10天
            start_date = min_date - timedelta(days=10)
            end_date = max_date + timedelta(days=10)

            start_date_str = start_date.strftime('%Y%m%d')
            end_date_str = end_date.strftime('%Y%m%d')

            logger.info(f"获取{code}的价格数据: {start_date_str} - {end_date_str}")

            # 获取历史价格数据
            price_data = ak.stock_zh_a_hist(symbol=code, start_date=start_date_str, end_date=end_date_str)

            if price_data.empty:
                return {}

            # 转换为字典格式，以日期为键
            price_dict = {}
            for _, row in price_data.iterrows():
                date_str = row['日期'].strftime('%Y-%m-%d')
                price_dict[date_str] = {
                    'open': row['开盘'],
                    'high': row['最高'],
                    'low': row['最低'],
                    'close': row['收盘'],
                    'volume': row['成交量'],
                    'amount': row['成交额'],
                    'change_rate': row.get('涨跌幅', 0)
                }

            logger.info(f"获取价格数据成功: {len(price_dict)}个交易日")
            return price_dict

        except Exception as e:
            logger.error(f"获取价格数据失败: {e}")
            return {}

    def _identify_complete_gaming_cycles(self, lhb_history: List[Dict], price_history: Dict) -> List[Dict]:
        """识别完整的炒作周期"""
        try:
            cycles = []

            if not lhb_history:
                return cycles

            # 按时间分组龙虎榜记录，识别连续的炒作期
            cycle_groups = self._group_lhb_by_time_proximity(lhb_history)

            for group in cycle_groups:
                cycle = self._analyze_single_cycle(group, price_history)
                if cycle:
                    cycles.append(cycle)

            logger.info(f"识别完整炒作周期: {len(cycles)}个")
            return cycles

        except Exception as e:
            logger.error(f"识别炒作周期失败: {e}")
            return []

    def _group_lhb_by_time_proximity(self, lhb_history: List[Dict]) -> List[List[Dict]]:
        """按时间邻近性分组龙虎榜记录"""
        try:
            if not lhb_history:
                return []

            from datetime import datetime, timedelta

            groups = []
            current_group = [lhb_history[0]]

            for i in range(1, len(lhb_history)):
                current_record = lhb_history[i]
                prev_record = lhb_history[i-1]

                # 转换日期
                current_date = datetime.strptime(current_record['date'], '%Y-%m-%d')
                prev_date = datetime.strptime(prev_record['date'], '%Y-%m-%d')

                # 如果间隔超过15天，认为是新的炒作周期
                if (current_date - prev_date).days > 15:
                    groups.append(current_group)
                    current_group = [current_record]
                else:
                    current_group.append(current_record)

            # 添加最后一组
            if current_group:
                groups.append(current_group)

            return groups

        except Exception as e:
            logger.error(f"龙虎榜分组失败: {e}")
            return []

    def _analyze_single_cycle(self, lhb_group: List[Dict], price_history: Dict) -> Dict:
        """分析单个炒作周期"""
        try:
            if not lhb_group:
                return {}

            from datetime import datetime, timedelta

            # 获取周期时间范围
            start_date = lhb_group[0]['date']
            end_date = lhb_group[-1]['date']

            # 扩展分析范围（前后各5天）
            start_dt = datetime.strptime(start_date, '%Y-%m-%d') - timedelta(days=5)
            end_dt = datetime.strptime(end_date, '%Y-%m-%d') + timedelta(days=5)

            # 获取周期内的价格数据
            cycle_prices = {}
            current_dt = start_dt
            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y-%m-%d')
                if date_str in price_history:
                    cycle_prices[date_str] = price_history[date_str]
                current_dt += timedelta(days=1)

            if not cycle_prices:
                return {}

            # 分析周期特征
            cycle_analysis = {
                'cycle_id': f"cycle_{start_date}_{end_date}",
                'start_date': start_date,
                'end_date': end_date,
                'duration_days': (datetime.strptime(end_date, '%Y-%m-%d') - datetime.strptime(start_date, '%Y-%m-%d')).days + 1,
                'lhb_count': len(lhb_group),
                'lhb_records': lhb_group,
                'price_data': cycle_prices
            }

            # 识别炒作阶段
            stages = self._identify_cycle_stages(cycle_analysis)
            cycle_analysis['stages'] = stages

            # 计算周期表现
            performance = self._calculate_cycle_performance(cycle_analysis)
            cycle_analysis['performance'] = performance

            # 分析操作特征
            characteristics = self._analyze_cycle_characteristics(cycle_analysis)
            cycle_analysis['characteristics'] = characteristics

            return cycle_analysis

        except Exception as e:
            logger.error(f"单个炒作周期分析失败: {e}")
            return {}

    def _identify_cycle_stages(self, cycle_analysis: Dict) -> Dict:
        """识别炒作周期的各个阶段"""
        try:
            price_data = cycle_analysis['price_data']
            lhb_records = cycle_analysis['lhb_records']

            if not price_data:
                return {}

            # 获取价格序列
            dates = sorted(price_data.keys())
            prices = [price_data[date]['close'] for date in dates]

            if len(prices) < 3:
                return {}

            # 基于游资炒作全链路识别阶段
            stages = {
                'startup_stage': None,      # 启动期：题材发酵+首板试盘
                'pullup_stage': None,       # 拉升期：合力加速+龙头确立
                'divergence_stage': None,   # 分歧期：淘汰赛+龙头渡劫
                'decline_stage': None       # 衰退期：退潮信号+监管介入
            }

            # 1. 识别启动期（第一次上龙虎榜前后）
            first_lhb_date = lhb_records[0]['date']
            startup_info = self._analyze_startup_stage(price_data, first_lhb_date)
            if startup_info:
                stages['startup_stage'] = startup_info

            # 2. 识别拉升期（连续上涨阶段）
            pullup_info = self._analyze_pullup_stage(price_data, dates, prices)
            if pullup_info:
                stages['pullup_stage'] = pullup_info

            # 3. 识别分歧期（高位震荡阶段）
            divergence_info = self._analyze_divergence_stage(price_data, dates, prices)
            if divergence_info:
                stages['divergence_stage'] = divergence_info

            # 4. 识别衰退期（下跌阶段）
            decline_info = self._analyze_decline_stage(price_data, dates, prices)
            if decline_info:
                stages['decline_stage'] = decline_info

            return stages

        except Exception as e:
            logger.error(f"炒作阶段识别失败: {e}")
            return {}

    def _analyze_startup_stage(self, price_data: Dict, first_lhb_date: str) -> Dict:
        """分析启动期特征"""
        try:
            from datetime import datetime, timedelta

            # 获取首次上榜前后5天的数据
            lhb_dt = datetime.strptime(first_lhb_date, '%Y-%m-%d')

            startup_data = {}
            for i in range(-5, 6):  # 前5天到后5天
                check_date = lhb_dt + timedelta(days=i)
                date_str = check_date.strftime('%Y-%m-%d')
                if date_str in price_data:
                    startup_data[date_str] = price_data[date_str]

            if len(startup_data) < 3:
                return {}

            dates = sorted(startup_data.keys())
            start_price = startup_data[dates[0]]['close']
            end_price = startup_data[dates[-1]]['close']

            # 计算启动期特征
            startup_info = {
                'start_date': dates[0],
                'end_date': dates[-1],
                'start_price': start_price,
                'end_price': end_price,
                'change_rate': (end_price - start_price) / start_price * 100,
                'max_price': max(startup_data[date]['high'] for date in dates),
                'min_price': min(startup_data[date]['low'] for date in dates),
                'avg_volume': sum(startup_data[date]['volume'] for date in dates) / len(dates),
                'lhb_trigger_date': first_lhb_date,
                'stage_type': '启动期'
            }

            return startup_info

        except Exception as e:
            logger.error(f"启动期分析失败: {e}")
            return {}

    def _analyze_pullup_stage(self, price_data: Dict, dates: List[str], prices: List[float]) -> Dict:
        """分析拉升期特征"""
        try:
            if len(prices) < 5:
                return {}

            # 寻找最长的连续上涨段
            max_rise_start = 0
            max_rise_end = 0
            max_rise_rate = 0

            for i in range(len(prices)):
                for j in range(i + 2, len(prices)):  # 至少3天
                    if prices[j] > prices[i]:
                        rise_rate = (prices[j] - prices[i]) / prices[i] * 100
                        if rise_rate > max_rise_rate:
                            max_rise_rate = rise_rate
                            max_rise_start = i
                            max_rise_end = j

            if max_rise_rate < 10:  # 拉升幅度至少10%
                return {}

            pullup_info = {
                'start_date': dates[max_rise_start],
                'end_date': dates[max_rise_end],
                'start_price': prices[max_rise_start],
                'end_price': prices[max_rise_end],
                'change_rate': max_rise_rate,
                'duration_days': max_rise_end - max_rise_start + 1,
                'avg_daily_rise': max_rise_rate / (max_rise_end - max_rise_start + 1),
                'stage_type': '拉升期'
            }

            return pullup_info

        except Exception as e:
            logger.error(f"拉升期分析失败: {e}")
            return {}

    def _analyze_divergence_stage(self, price_data: Dict, dates: List[str], prices: List[float]) -> Dict:
        """分析分歧期特征（高位震荡）"""
        try:
            if len(prices) < 5:
                return {}

            # 寻找高位震荡段（价格在高位区间内波动）
            max_price = max(prices)
            max_price_idx = prices.index(max_price)

            # 从最高点开始寻找震荡区间
            high_threshold = max_price * 0.9  # 90%以上认为是高位

            divergence_start = max_price_idx
            divergence_end = max_price_idx

            # 向后寻找震荡结束点
            for i in range(max_price_idx + 1, len(prices)):
                if prices[i] >= high_threshold:
                    divergence_end = i
                else:
                    break

            # 检查是否构成有效的分歧期（至少3天且有一定波动）
            if divergence_end - divergence_start < 2:
                return {}

            divergence_prices = prices[divergence_start:divergence_end + 1]
            volatility = (max(divergence_prices) - min(divergence_prices)) / max(divergence_prices) * 100

            if volatility < 5:  # 波动率至少5%
                return {}

            divergence_info = {
                'start_date': dates[divergence_start],
                'end_date': dates[divergence_end],
                'start_price': prices[divergence_start],
                'end_price': prices[divergence_end],
                'max_price': max(divergence_prices),
                'min_price': min(divergence_prices),
                'volatility': volatility,
                'duration_days': divergence_end - divergence_start + 1,
                'stage_type': '分歧期'
            }

            return divergence_info

        except Exception as e:
            logger.error(f"分歧期分析失败: {e}")
            return {}

    def _analyze_decline_stage(self, price_data: Dict, dates: List[str], prices: List[float]) -> Dict:
        """分析衰退期特征"""
        try:
            if len(prices) < 5:
                return {}

            # 寻找最长的下跌段
            max_decline_start = 0
            max_decline_end = 0
            max_decline_rate = 0

            for i in range(len(prices)):
                for j in range(i + 2, len(prices)):  # 至少3天
                    if prices[j] < prices[i]:
                        decline_rate = (prices[i] - prices[j]) / prices[i] * 100
                        if decline_rate > max_decline_rate:
                            max_decline_rate = decline_rate
                            max_decline_start = i
                            max_decline_end = j

            if max_decline_rate < 10:  # 下跌幅度至少10%
                return {}

            decline_info = {
                'start_date': dates[max_decline_start],
                'end_date': dates[max_decline_end],
                'start_price': prices[max_decline_start],
                'end_price': prices[max_decline_end],
                'decline_rate': max_decline_rate,
                'duration_days': max_decline_end - max_decline_start + 1,
                'avg_daily_decline': max_decline_rate / (max_decline_end - max_decline_start + 1),
                'stage_type': '衰退期'
            }

            return decline_info

        except Exception as e:
            logger.error(f"衰退期分析失败: {e}")
            return {}

    def _calculate_cycle_performance(self, cycle_analysis: Dict) -> Dict:
        """计算炒作周期表现"""
        try:
            price_data = cycle_analysis['price_data']

            if not price_data:
                return {}

            dates = sorted(price_data.keys())
            start_price = price_data[dates[0]]['close']
            end_price = price_data[dates[-1]]['close']
            max_price = max(price_data[date]['high'] for date in dates)
            min_price = min(price_data[date]['low'] for date in dates)

            performance = {
                'total_return': (end_price - start_price) / start_price * 100,
                'max_gain': (max_price - start_price) / start_price * 100,
                'max_drawdown': (max_price - min_price) / max_price * 100,
                'volatility': self._calculate_volatility([price_data[date]['close'] for date in dates]),
                'success_rate': 1 if end_price > start_price else 0,
                'risk_reward_ratio': 0
            }

            # 计算风险收益比
            if performance['max_drawdown'] > 0:
                performance['risk_reward_ratio'] = performance['max_gain'] / performance['max_drawdown']

            return performance

        except Exception as e:
            logger.error(f"周期表现计算失败: {e}")
            return {}

    def _calculate_volatility(self, prices: List[float]) -> float:
        """计算价格波动率"""
        try:
            if len(prices) < 2:
                return 0

            import math

            # 计算日收益率
            returns = []
            for i in range(1, len(prices)):
                daily_return = (prices[i] - prices[i-1]) / prices[i-1]
                returns.append(daily_return)

            # 计算标准差
            if not returns:
                return 0

            mean_return = sum(returns) / len(returns)
            variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
            volatility = math.sqrt(variance) * 100  # 转换为百分比

            return round(volatility, 2)

        except Exception as e:
            logger.error(f"波动率计算失败: {e}")
            return 0

    def _analyze_cycle_characteristics(self, cycle_analysis: Dict) -> Dict:
        """分析炒作周期特征"""
        try:
            lhb_records = cycle_analysis['lhb_records']
            performance = cycle_analysis.get('performance', {})

            characteristics = {
                'lhb_frequency': len(lhb_records),
                'avg_net_buy': sum(record.get('net_buy_amount', 0) for record in lhb_records) / len(lhb_records) if lhb_records else 0,
                'max_single_day_gain': 0,
                'operation_style': '',
                'capital_scale': '',
                'success_probability': performance.get('success_rate', 0)
            }

            # 分析操作风格
            if characteristics['lhb_frequency'] >= 3:
                characteristics['operation_style'] = '连续操作型'
            elif characteristics['lhb_frequency'] == 2:
                characteristics['operation_style'] = '试探型'
            else:
                characteristics['operation_style'] = '一击即走型'

            # 分析资金规模
            avg_net_buy = characteristics['avg_net_buy']
            if avg_net_buy >= 5000:  # 5000万以上
                characteristics['capital_scale'] = '大资金'
            elif avg_net_buy >= 1000:  # 1000-5000万
                characteristics['capital_scale'] = '中等资金'
            else:
                characteristics['capital_scale'] = '小资金'

            return characteristics

        except Exception as e:
            logger.error(f"周期特征分析失败: {e}")
            return {}

    def _analyze_cycle_patterns(self, gaming_cycles: List[Dict]) -> Dict:
        """分析炒作周期模式"""
        try:
            if not gaming_cycles:
                return {}

            patterns = {
                'total_cycles': len(gaming_cycles),
                'avg_duration': 0,
                'success_rate': 0,
                'common_characteristics': {},
                'best_performance_cycle': None,
                'worst_performance_cycle': None
            }

            # 计算平均持续时间
            durations = [cycle.get('duration_days', 0) for cycle in gaming_cycles]
            patterns['avg_duration'] = sum(durations) / len(durations) if durations else 0

            # 计算成功率
            successful_cycles = [cycle for cycle in gaming_cycles if cycle.get('performance', {}).get('total_return', 0) > 0]
            patterns['success_rate'] = len(successful_cycles) / len(gaming_cycles) * 100

            # 找出最佳和最差表现周期
            if gaming_cycles:
                best_cycle = max(gaming_cycles, key=lambda x: x.get('performance', {}).get('total_return', -999))
                worst_cycle = min(gaming_cycles, key=lambda x: x.get('performance', {}).get('total_return', 999))

                patterns['best_performance_cycle'] = {
                    'cycle_id': best_cycle.get('cycle_id', ''),
                    'start_date': best_cycle.get('start_date', ''),
                    'total_return': best_cycle.get('performance', {}).get('total_return', 0)
                }

                patterns['worst_performance_cycle'] = {
                    'cycle_id': worst_cycle.get('cycle_id', ''),
                    'start_date': worst_cycle.get('start_date', ''),
                    'total_return': worst_cycle.get('performance', {}).get('total_return', 0)
                }

            return patterns

        except Exception as e:
            logger.error(f"周期模式分析失败: {e}")
            return {}

    def _extract_gaming_characteristics(self, gaming_cycles: List[Dict], lhb_history: List[Dict]) -> Dict:
        """提取游资操作特征"""
        try:
            characteristics = {
                'preferred_reasons': {},
                'avg_capital_scale': 0,
                'operation_frequency': 0,
                'seasonal_pattern': {}
            }

            # 分析上榜原因偏好
            reason_counts = {}
            for record in lhb_history:
                reason = record.get('reason', '未知')
                reason_counts[reason] = reason_counts.get(reason, 0) + 1

            characteristics['preferred_reasons'] = reason_counts

            # 计算平均资金规模
            net_buy_amounts = [record.get('net_buy_amount', 0) for record in lhb_history]
            characteristics['avg_capital_scale'] = sum(net_buy_amounts) / len(net_buy_amounts) if net_buy_amounts else 0

            # 计算操作频率（每月平均上榜次数）
            if lhb_history:
                from datetime import datetime
                start_date = datetime.strptime(lhb_history[0]['date'], '%Y-%m-%d')
                end_date = datetime.strptime(lhb_history[-1]['date'], '%Y-%m-%d')
                months = (end_date - start_date).days / 30
                characteristics['operation_frequency'] = len(lhb_history) / max(months, 1)

            return characteristics

        except Exception as e:
            logger.error(f"游资特征提取失败: {e}")
            return {}

    def _analyze_time_intervals(self, gaming_cycles: List[Dict]) -> List[Dict]:
        """分析时间间隔规律"""
        try:
            intervals = []

            if len(gaming_cycles) < 2:
                return intervals

            from datetime import datetime

            for i in range(1, len(gaming_cycles)):
                prev_cycle = gaming_cycles[i-1]
                curr_cycle = gaming_cycles[i]

                prev_end = datetime.strptime(prev_cycle['end_date'], '%Y-%m-%d')
                curr_start = datetime.strptime(curr_cycle['start_date'], '%Y-%m-%d')

                interval_days = (curr_start - prev_end).days

                interval_info = {
                    'from_cycle': prev_cycle['cycle_id'],
                    'to_cycle': curr_cycle['cycle_id'],
                    'interval_days': interval_days,
                    'prev_performance': prev_cycle.get('performance', {}).get('total_return', 0),
                    'next_performance': curr_cycle.get('performance', {}).get('total_return', 0)
                }

                intervals.append(interval_info)

            return intervals

        except Exception as e:
            logger.error(f"时间间隔分析失败: {e}")
            return []

    def _identify_success_patterns(self, gaming_cycles: List[Dict]) -> List[Dict]:
        """识别成功模式"""
        try:
            success_patterns = []

            successful_cycles = [cycle for cycle in gaming_cycles if cycle.get('performance', {}).get('total_return', 0) > 10]

            for cycle in successful_cycles:
                pattern = {
                    'cycle_id': cycle.get('cycle_id', ''),
                    'success_factors': [],
                    'performance_metrics': cycle.get('performance', {}),
                    'key_characteristics': cycle.get('characteristics', {})
                }

                # 识别成功因素
                performance = cycle.get('performance', {})
                if performance.get('max_gain', 0) > 30:
                    pattern['success_factors'].append('高收益潜力')

                if performance.get('volatility', 0) < 10:
                    pattern['success_factors'].append('低波动风险')

                characteristics = cycle.get('characteristics', {})
                if characteristics.get('capital_scale') == '大资金':
                    pattern['success_factors'].append('大资金介入')

                success_patterns.append(pattern)

            return success_patterns

        except Exception as e:
            logger.error(f"成功模式识别失败: {e}")
            return []

    def _generate_cycle_analysis_summary(self, gaming_cycles: List[Dict], cycle_patterns: Dict) -> str:
        """生成炒作周期分析摘要"""
        try:
            if not gaming_cycles:
                return '该股票6个月内无明显游资炒作周期'

            summary_parts = []

            # 周期数量
            summary_parts.append(f"识别{len(gaming_cycles)}个炒作周期")

            # 成功率
            success_rate = cycle_patterns.get('success_rate', 0)
            summary_parts.append(f"成功率{success_rate:.1f}%")

            # 平均持续时间
            avg_duration = cycle_patterns.get('avg_duration', 0)
            summary_parts.append(f"平均持续{avg_duration:.1f}天")

            # 最佳表现
            best_cycle = cycle_patterns.get('best_performance_cycle')
            if best_cycle:
                best_return = best_cycle.get('total_return', 0)
                summary_parts.append(f"最高收益{best_return:.1f}%")

            return ', '.join(summary_parts)

        except Exception as e:
            logger.error(f"周期分析摘要生成失败: {e}")
            return "摘要生成失败"

# 全局实例
enhanced_stock_fetcher = EnhancedStockDataFetcher()
