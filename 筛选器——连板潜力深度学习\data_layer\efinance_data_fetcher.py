#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于efinance的数据获取器
使用efinance作为主要数据源，获取真实的历史股票数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import time
import os
import json
import warnings
warnings.filterwarnings('ignore')

# 尝试导入efinance
try:
    import efinance as ef
    EFINANCE_AVAILABLE = True
    logger = logging.getLogger(__name__)
    logger.info("✅ efinance库导入成功")
except ImportError:
    EFINANCE_AVAILABLE = False
    logger = logging.getLogger(__name__)
    logger.error("❌ efinance库未安装，请运行: pip install efinance")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class EfinanceDataFetcher:
    """基于efinance的数据获取器"""
    
    def __init__(self, cache_dir: str = "efinance_cache"):
        self.cache_dir = cache_dir
        self.efinance_available = EFINANCE_AVAILABLE
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        
        # 数据获取配置
        self.fetch_config = {
            'kline_period': 60,         # K线数据获取天数
            'minute_period': 5,         # 分时数据获取天数
            'retry_times': 3,           # 重试次数
            'retry_delay': 2,           # 重试延迟（秒）
            'cache_expire_hours': 24,   # 缓存过期时间（小时）
            'batch_size': 10,           # 批量获取大小
            'rate_limit_delay': 0.5     # API限流延迟
        }
        
        logger.info("📅 efinance数据获取器初始化完成")
    
    def get_stock_kline_data(self, stock_code: str, days: int = 30) -> pd.DataFrame:
        """获取股票K线数据"""
        try:
            if not self.efinance_available:
                logger.error("efinance不可用")
                return pd.DataFrame()
            
            logger.info(f"📊 获取{stock_code}的{days}天K线数据")
            
            # 使用efinance获取历史K线数据
            df = ef.stock.get_quote_history(stock_code)
            
            if df is None or df.empty:
                logger.warning(f"⚠️ {stock_code}没有K线数据")
                return pd.DataFrame()
            
            # 数据格式转换
            df_processed = self._process_kline_data(df)
            
            # 只取最近N天的数据
            if len(df_processed) > days:
                df_processed = df_processed.tail(days)
            
            logger.info(f"✅ 成功获取{stock_code}的{len(df_processed)}天K线数据")
            return df_processed
            
        except Exception as e:
            logger.error(f"获取{stock_code}K线数据失败: {e}")
            return pd.DataFrame()
    
    def _process_kline_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理K线数据格式"""
        try:
            # 重命名列名，统一格式
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '收盘': 'close', 
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '换手率': 'turnover_rate'
            }
            
            df_processed = df.rename(columns=column_mapping)
            
            # 确保日期格式正确
            if 'date' in df_processed.columns:
                df_processed['date'] = pd.to_datetime(df_processed['date'])
            
            # 确保数值列为数值类型
            numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount', 'turnover_rate']
            for col in numeric_columns:
                if col in df_processed.columns:
                    df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
            
            # 按日期排序
            df_processed = df_processed.sort_values('date')
            
            return df_processed
            
        except Exception as e:
            logger.error(f"处理K线数据格式失败: {e}")
            return df
    
    def get_stock_minute_data(self, stock_code: str, trade_date: datetime) -> pd.DataFrame:
        """获取股票分时数据"""
        try:
            if not self.efinance_available:
                logger.error("efinance不可用")
                return pd.DataFrame()
            
            logger.info(f"📊 获取{stock_code}在{trade_date.strftime('%Y-%m-%d')}的分时数据")
            
            # efinance获取分时数据（1分钟K线）
            df = ef.stock.get_quote_history(stock_code, klt=1)
            
            if df is None or df.empty:
                logger.warning(f"⚠️ {stock_code}没有分时数据")
                return pd.DataFrame()
            
            # 筛选指定日期的数据
            df['日期'] = pd.to_datetime(df['日期'])
            target_date = trade_date.strftime('%Y-%m-%d')
            df_filtered = df[df['日期'].dt.strftime('%Y-%m-%d') == target_date]
            
            if df_filtered.empty:
                logger.warning(f"⚠️ {stock_code}在{target_date}没有分时数据")
                return pd.DataFrame()
            
            # 数据格式转换
            df_processed = self._process_minute_data(df_filtered)
            
            logger.info(f"✅ 成功获取{stock_code}的{len(df_processed)}条分时数据")
            return df_processed
            
        except Exception as e:
            logger.error(f"获取{stock_code}分时数据失败: {e}")
            return pd.DataFrame()
    
    def _process_minute_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理分时数据格式"""
        try:
            # 重命名列名
            column_mapping = {
                '日期': 'datetime',
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high', 
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount'
            }
            
            df_processed = df.rename(columns=column_mapping)
            
            # 确保时间格式正确
            if 'datetime' in df_processed.columns:
                df_processed['datetime'] = pd.to_datetime(df_processed['datetime'])
            
            # 确保数值列为数值类型
            numeric_columns = ['open', 'close', 'high', 'low', 'volume', 'amount']
            for col in numeric_columns:
                if col in df_processed.columns:
                    df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
            
            # 按时间排序
            df_processed = df_processed.sort_values('datetime')
            
            return df_processed
            
        except Exception as e:
            logger.error(f"处理分时数据格式失败: {e}")
            return df
    
    def get_capital_flow_data(self, stock_code: str, days: int = 10) -> pd.DataFrame:
        """获取资金流向数据"""
        try:
            if not self.efinance_available:
                logger.error("efinance不可用")
                return pd.DataFrame()
            
            logger.info(f"💰 获取{stock_code}的{days}天资金流向数据")
            
            # 使用efinance获取资金流向数据
            df = ef.stock.get_history_bill(stock_code)
            
            if df is None or df.empty:
                logger.warning(f"⚠️ {stock_code}没有资金流向数据")
                return pd.DataFrame()
            
            # 数据格式转换
            df_processed = self._process_capital_data(df)
            
            # 只取最近N天的数据
            if len(df_processed) > days:
                df_processed = df_processed.tail(days)
            
            logger.info(f"✅ 成功获取{stock_code}的{len(df_processed)}天资金流向数据")
            return df_processed
            
        except Exception as e:
            logger.error(f"获取{stock_code}资金流向数据失败: {e}")
            return pd.DataFrame()
    
    def _process_capital_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """处理资金流向数据格式"""
        try:
            # 重命名列名
            column_mapping = {
                '日期': 'date',
                '主力净流入': 'main_net_inflow',
                '小单净流入': 'small_net_inflow',
                '中单净流入': 'medium_net_inflow',
                '大单净流入': 'large_net_inflow',
                '超大单净流入': 'super_large_net_inflow',
                '主力净流入占比': 'main_net_inflow_ratio',
                '收盘价': 'close_price',
                '涨跌幅': 'change_pct'
            }
            
            df_processed = df.rename(columns=column_mapping)
            
            # 确保日期格式正确
            if 'date' in df_processed.columns:
                df_processed['date'] = pd.to_datetime(df_processed['date'])
            
            # 确保数值列为数值类型
            numeric_columns = ['main_net_inflow', 'small_net_inflow', 'medium_net_inflow', 
                             'large_net_inflow', 'super_large_net_inflow', 'main_net_inflow_ratio',
                             'close_price', 'change_pct']
            for col in numeric_columns:
                if col in df_processed.columns:
                    df_processed[col] = pd.to_numeric(df_processed[col], errors='coerce')
            
            # 按日期排序
            df_processed = df_processed.sort_values('date')
            
            return df_processed
            
        except Exception as e:
            logger.error(f"处理资金流向数据格式失败: {e}")
            return df
    
    def get_dragon_tiger_data(self, date: str = None) -> pd.DataFrame:
        """获取龙虎榜数据"""
        try:
            if not self.efinance_available:
                logger.error("efinance不可用")
                return pd.DataFrame()
            
            logger.info(f"🐉 获取龙虎榜数据")
            
            if date:
                # 获取指定日期的龙虎榜数据
                df = ef.stock.get_daily_billboard(start_date=date, end_date=date)
            else:
                # 获取最新龙虎榜数据
                df = ef.stock.get_daily_billboard()
            
            if df is None or df.empty:
                logger.warning(f"⚠️ 没有龙虎榜数据")
                return pd.DataFrame()
            
            logger.info(f"✅ 成功获取{len(df)}条龙虎榜数据")
            return df
            
        except Exception as e:
            logger.error(f"获取龙虎榜数据失败: {e}")
            return pd.DataFrame()
    
    def batch_get_historical_data(self, stock_codes: List[str], end_date: datetime, 
                                lookback_days: int = 30) -> Dict[str, Dict[str, Any]]:
        """批量获取历史数据"""
        try:
            logger.info(f"📦 批量获取{len(stock_codes)}只股票的历史数据")
            
            results = {}
            
            for i, stock_code in enumerate(stock_codes, 1):
                try:
                    logger.info(f"处理第{i}/{len(stock_codes)}只股票: {stock_code}")
                    
                    # 获取K线数据
                    kline_data = self.get_stock_kline_data(stock_code, lookback_days)
                    
                    # 获取分时数据（最近一个交易日）
                    minute_date = end_date - timedelta(days=1)
                    minute_data = self.get_stock_minute_data(stock_code, minute_date)
                    
                    # 获取资金流向数据
                    capital_data = self.get_capital_flow_data(stock_code, 10)
                    
                    # 评估数据质量
                    data_quality = self._evaluate_data_quality(kline_data, minute_data, capital_data)
                    
                    results[stock_code] = {
                        'kline_data': kline_data,
                        'minute_data': minute_data,
                        'capital_data': capital_data,
                        'data_quality': data_quality
                    }
                    
                    # API限流
                    time.sleep(self.fetch_config['rate_limit_delay'])
                    
                except Exception as e:
                    logger.warning(f"处理股票{stock_code}失败: {e}")
                    results[stock_code] = {
                        'kline_data': pd.DataFrame(),
                        'minute_data': pd.DataFrame(),
                        'capital_data': pd.DataFrame(),
                        'data_quality': 'error'
                    }
                    continue
            
            logger.info(f"✅ 批量获取完成，成功处理{len(results)}只股票")
            return results
            
        except Exception as e:
            logger.error(f"批量获取历史数据失败: {e}")
            return {}
    
    def _evaluate_data_quality(self, kline_data: pd.DataFrame, minute_data: pd.DataFrame, 
                             capital_data: pd.DataFrame) -> str:
        """评估数据质量"""
        try:
            score = 0
            
            # K线数据质量评分
            if not kline_data.empty and len(kline_data) >= 20:
                score += 40
            elif not kline_data.empty and len(kline_data) >= 10:
                score += 20
            
            # 分时数据质量评分
            if not minute_data.empty and len(minute_data) >= 200:
                score += 30
            elif not minute_data.empty and len(minute_data) >= 100:
                score += 15
            
            # 资金流向数据质量评分
            if not capital_data.empty and len(capital_data) >= 5:
                score += 30
            elif not capital_data.empty:
                score += 15
            
            # 根据评分返回质量等级
            if score >= 80:
                return 'excellent'
            elif score >= 60:
                return 'good'
            elif score >= 40:
                return 'fair'
            elif score >= 20:
                return 'poor'
            else:
                return 'error'
                
        except Exception as e:
            logger.error(f"评估数据质量失败: {e}")
            return 'error'

def main():
    """测试efinance数据获取器"""
    print("🧪 测试efinance数据获取器...")
    
    fetcher = EfinanceDataFetcher()
    
    # 测试获取K线数据
    print("\n📊 测试K线数据获取...")
    kline_data = fetcher.get_stock_kline_data('000001', 30)
    if not kline_data.empty:
        print(f"✅ 成功获取K线数据: {len(kline_data)}条记录")
        print(kline_data.head())
    else:
        print("❌ K线数据获取失败")
    
    # 测试获取资金流向数据
    print("\n💰 测试资金流向数据获取...")
    capital_data = fetcher.get_capital_flow_data('000001', 10)
    if not capital_data.empty:
        print(f"✅ 成功获取资金流向数据: {len(capital_data)}条记录")
        print(capital_data.head())
    else:
        print("❌ 资金流向数据获取失败")

if __name__ == "__main__":
    main()
