#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线数据存储修复
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from training.training_pipeline import TrainingPipeline

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_kline_storage_in_training():
    """测试训练流程中的K线数据存储"""
    print("🧪 测试训练流程中的K线数据存储...")
    
    # 创建训练流程
    pipeline = TrainingPipeline()
    
    # 测试股票和日期
    test_stock = "603880"  # 训练日志中查询失败的股票
    test_date = datetime(2025, 1, 29)  # 涨停前一天
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 测试日期: {test_date.strftime('%Y-%m-%d')}")
    
    try:
        # 1. 检查数据库当前状态
        print("\n1️⃣ 检查数据库当前状态...")
        import sqlite3
        conn = sqlite3.connect('historical_data.db')
        
        before_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
        print(f"   {test_stock}当前K线数据: {before_count}条")
        
        conn.close()
        
        # 2. 调用训练流程中的K线数据获取方法
        print("\n2️⃣ 调用训练流程中的K线数据获取方法...")
        kline_data = pipeline._get_historical_kline_before_date(test_stock, test_date, 30)
        
        if not kline_data.empty:
            print(f"✅ 获取K线数据成功: {len(kline_data)}条")
            print(f"   数据时间范围: {kline_data['date'].min()} 到 {kline_data['date'].max()}")
            
            # 3. 检查数据库状态变化
            print("\n3️⃣ 检查数据库状态变化...")
            conn = sqlite3.connect('historical_data.db')
            
            after_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
            print(f"   {test_stock}现在K线数据: {after_count}条")
            print(f"   新增数据: {after_count - before_count}条")
            
            if after_count > before_count:
                print("✅ K线数据存储成功！")
                
                # 4. 测试查询功能
                print("\n4️⃣ 测试查询功能...")
                start_date = (test_date - timedelta(days=30)).strftime('%Y-%m-%d')
                end_date = test_date.strftime('%Y-%m-%d')
                
                stored_data = pipeline.data_storage.get_kline_data(
                    test_stock,
                    start_date=start_date,
                    end_date=end_date
                )
                
                print(f"✅ 查询K线数据成功: {len(stored_data)}条")
                
                if len(stored_data) > 0:
                    print("🎉 K线数据存储和查询都正常工作！")
                    return True
                else:
                    print("❌ 查询结果为空，可能查询逻辑有问题")
                    return False
            else:
                print("❌ K线数据没有存储到数据库")
                return False
            
            conn.close()
        else:
            print("❌ 获取K线数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_stocks():
    """测试多只股票的K线数据存储"""
    print("\n🧪 测试多只股票的K线数据存储...")
    
    pipeline = TrainingPipeline()
    
    # 测试多只股票（从训练日志中选择）
    test_stocks = [
        ("603880", datetime(2025, 1, 29)),
        ("002261", datetime(2025, 1, 28)),
        ("002207", datetime(2025, 1, 27))
    ]
    
    results = {}
    
    for stock_code, test_date in test_stocks:
        print(f"\n📊 测试股票: {stock_code}, 日期: {test_date.strftime('%Y-%m-%d')}")
        
        try:
            # 获取K线数据（会自动存储）
            kline_data = pipeline._get_historical_kline_before_date(stock_code, test_date, 30)
            
            if not kline_data.empty:
                # 测试查询
                start_date = (test_date - timedelta(days=30)).strftime('%Y-%m-%d')
                end_date = test_date.strftime('%Y-%m-%d')
                
                stored_data = pipeline.data_storage.get_kline_data(
                    stock_code,
                    start_date=start_date,
                    end_date=end_date
                )
                
                results[stock_code] = {
                    "获取": len(kline_data),
                    "查询": len(stored_data),
                    "状态": "成功" if len(stored_data) > 0 else "查询失败"
                }
                print(f"✅ 获取: {len(kline_data)}条, 查询: {len(stored_data)}条")
            else:
                results[stock_code] = {
                    "获取": 0,
                    "查询": 0,
                    "状态": "获取失败"
                }
                print(f"❌ 获取失败")
                
        except Exception as e:
            results[stock_code] = {
                "获取": 0,
                "查询": 0,
                "状态": f"异常: {str(e)}"
            }
            print(f"❌ 异常: {e}")
    
    # 打印结果汇总
    print("\n📋 测试结果汇总:")
    print("股票代码\t获取\t查询\t状态")
    print("-" * 40)
    for stock_code, result in results.items():
        print(f"{stock_code}\t{result['获取']}\t{result['查询']}\t{result['状态']}")
    
    success_count = sum(1 for result in results.values() if result['状态'] == '成功')
    return success_count, len(results)

def main():
    """主函数"""
    print("🔧 K线数据存储修复测试开始...")
    print("="*60)
    
    # 测试单只股票
    single_result = test_kline_storage_in_training()
    
    # 测试多只股票
    success_count, total_count = test_multiple_stocks()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if single_result:
        print("✅ 单只股票K线数据存储和查询正常")
    else:
        print("❌ 单只股票K线数据存储或查询异常")
    
    print(f"📊 多股票测试: {success_count}/{total_count} 成功")
    
    if single_result and success_count > 0:
        print("🎉 修复成功！现在可以重新运行完整训练")
        print("💡 预期结果:")
        print("   - K线数据会正常存储到数据库")
        print("   - 负样本创建会成功")
        print("   - 模型训练会正常进行")
    else:
        print("❌ 修复可能不完整，需要进一步调试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
