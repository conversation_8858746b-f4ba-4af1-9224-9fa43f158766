#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试市值配置修复
"""

def test_market_cap_config():
    """测试市值配置修复"""
    print("🔧 测试市值配置修复...")
    
    # 修复前的配置
    old_config = {
        'market_cap_min': 30e8,      # 3000亿
        'market_cap_max': 130e8,     # 13000亿
    }
    
    # 修复后的配置
    new_config = {
        'market_cap_min': 30,        # 30亿
        'market_cap_max': 130,       # 130亿
    }
    
    print(f"📊 配置对比:")
    print(f"  修复前:")
    print(f"    market_cap_min: {old_config['market_cap_min']} = {old_config['market_cap_min']/1e8:.0f}亿")
    print(f"    market_cap_max: {old_config['market_cap_max']} = {old_config['market_cap_max']/1e8:.0f}亿")
    print(f"  修复后:")
    print(f"    market_cap_min: {new_config['market_cap_min']}亿")
    print(f"    market_cap_max: {new_config['market_cap_max']}亿")
    
    # 模拟一些股票市值数据
    test_stocks = [
        {'name': '小盘股', 'market_cap': 25},    # 25亿
        {'name': '中盘股1', 'market_cap': 50},   # 50亿
        {'name': '中盘股2', 'market_cap': 100},  # 100亿
        {'name': '大盘股', 'market_cap': 150},   # 150亿
        {'name': '超大盘股', 'market_cap': 500}, # 500亿
    ]
    
    print(f"\n🧪 筛选测试:")
    print(f"  修复前筛选结果（{old_config['market_cap_min']/1e8:.0f}-{old_config['market_cap_max']/1e8:.0f}亿）:")
    for stock in test_stocks:
        market_cap = stock['market_cap']
        passed = old_config['market_cap_min']/1e8 <= market_cap <= old_config['market_cap_max']/1e8
        status = "✅通过" if passed else "❌排除"
        print(f"    {stock['name']}({market_cap}亿): {status}")
    
    print(f"  修复后筛选结果（{new_config['market_cap_min']}-{new_config['market_cap_max']}亿）:")
    for stock in test_stocks:
        market_cap = stock['market_cap']
        passed = new_config['market_cap_min'] <= market_cap <= new_config['market_cap_max']
        status = "✅通过" if passed else "❌排除"
        print(f"    {stock['name']}({market_cap}亿): {status}")
    
    # 统计通过数量
    old_passed = sum(1 for stock in test_stocks 
                    if old_config['market_cap_min']/1e8 <= stock['market_cap'] <= old_config['market_cap_max']/1e8)
    new_passed = sum(1 for stock in test_stocks 
                    if new_config['market_cap_min'] <= stock['market_cap'] <= new_config['market_cap_max'])
    
    print(f"\n📈 筛选结果统计:")
    print(f"  修复前通过: {old_passed}/{len(test_stocks)}只")
    print(f"  修复后通过: {new_passed}/{len(test_stocks)}只")
    
    if new_passed > old_passed:
        print(f"✅ 修复成功！现在有更多股票能通过基础筛选")
        return True
    else:
        print(f"❌ 修复可能有问题")
        return False

def test_actual_investigator():
    """测试实际的调查器配置"""
    print("\n🔍 测试实际调查器配置...")
    
    try:
        import sys
        import os
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from investigation.potential_stock_investigator import PotentialStockInvestigator
        
        investigator = PotentialStockInvestigator()
        
        print(f"📊 当前配置:")
        print(f"  market_cap_min: {investigator.config['market_cap_min']}")
        print(f"  market_cap_max: {investigator.config['market_cap_max']}")
        
        if investigator.config['market_cap_min'] == 30 and investigator.config['market_cap_max'] == 130:
            print("✅ 配置修复成功！")
            return True
        else:
            print("❌ 配置仍有问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 市值配置修复测试...")
    print("="*60)
    
    # 测试配置修复
    config_test = test_market_cap_config()
    
    # 测试实际调查器
    investigator_test = test_actual_investigator()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if config_test and investigator_test:
        print("✅ 市值配置修复成功")
        print("💡 现在基础筛选应该能正常工作")
        print("🚀 建议重新运行潜在股调查")
    else:
        print("❌ 修复可能不完整，需要进一步检查")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
