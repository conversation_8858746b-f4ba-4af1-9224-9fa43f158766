#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试涨停筛选功能
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_limit_up_analyzer():
    """测试涨停数据分析器"""
    print("🔍 测试涨停数据分析器...")
    
    try:
        from investigation.limit_up_analyzer import LimitUpAnalyzer
        
        # 创建分析器
        analyzer = LimitUpAnalyzer()
        
        # 测试加载统计数据
        print("\n📊 加载涨停统计数据...")
        stats = analyzer.load_annual_limit_up_stats(days_back=365)
        
        print(f"✅ 统计完成: {len(stats)}只股票有涨停记录")
        
        # 获取统计摘要
        summary = analyzer.get_statistics_summary()
        print(f"\n📈 统计摘要:")
        print(f"  总股票数: {summary.get('total_stocks', 0)}")
        print(f"  总涨停次数: {summary.get('total_limit_ups', 0)}")
        print(f"  最大涨停次数: {summary.get('max_limit_ups', 0)}")
        print(f"  平均涨停次数: {summary.get('avg_limit_ups', 0):.2f}")
        print(f"  涨停≥3次股票: {summary.get('stocks_with_3_plus', 0)}")
        print(f"  涨停≥5次股票: {summary.get('stocks_with_5_plus', 0)}")
        print(f"  涨停≥10次股票: {summary.get('stocks_with_10_plus', 0)}")
        
        # 测试获取符合条件的股票
        print(f"\n🎯 获取涨停≥3次的股票...")
        qualified_stocks = analyzer.get_stocks_with_min_limit_ups(min_count=3)
        
        print(f"✅ 符合条件的股票: {len(qualified_stocks)}只")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_potential_stock_investigator():
    """测试潜在股调查器的涨停筛选功能"""
    print("\n🔍 测试潜在股调查器的涨停筛选功能...")
    
    try:
        from investigation.potential_stock_investigator import PotentialStockInvestigator
        
        # 创建调查器
        investigator = PotentialStockInvestigator()
        
        print(f"📊 配置信息:")
        print(f"  最小涨停次数: {investigator.config['min_limit_up_count']}")
        print(f"  回溯天数: {investigator.config['limit_up_days_back']}")
        print(f"  市值范围: {investigator.config['market_cap_min']}-{investigator.config['market_cap_max']}亿")
        
        # 测试涨停筛选功能（使用模拟数据）
        print(f"\n🧪 测试涨停筛选功能...")
        
        # 创建模拟的基础筛选后数据
        mock_stocks = [
            {'代码': '000001', '名称': '平安银行', '市值': 50},
            {'代码': '000002', '名称': '万科A', '市值': 80},
            {'代码': '600000', '名称': '浦发银行', '市值': 60},
            {'代码': '600036', '名称': '招商银行', '市值': 90},
            {'代码': '000858', '名称': '五粮液', '市值': 120}
        ]
        
        # 应用涨停筛选
        filtered_stocks = investigator._apply_limit_up_filtering(mock_stocks)
        
        print(f"📈 筛选结果:")
        print(f"  输入股票: {len(mock_stocks)}只")
        print(f"  通过筛选: {len(filtered_stocks)}只")
        
        if filtered_stocks:
            print(f"\n📋 通过筛选的股票:")
            for i, stock in enumerate(filtered_stocks, 1):
                code = stock.get('标准化代码', '')
                name = stock.get('名称', '')
                count = stock.get('涨停次数', 0)
                print(f"   {i}. {code} {name}: {count}次涨停")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试完整的筛选流程"""
    print("\n🚀 测试完整的筛选流程...")
    
    try:
        from investigation.potential_stock_investigator import PotentialStockInvestigator
        
        # 创建调查器
        investigator = PotentialStockInvestigator()
        
        # 修改配置以减少处理时间
        investigator.config['max_stocks_to_process'] = 50  # 只处理50只股票进行测试
        
        print("⚠️ 注意：这将执行完整的调查流程，可能需要几分钟时间...")
        print("如果不想执行完整测试，请按Ctrl+C中断")
        
        # 等待用户确认
        input("按Enter键继续，或Ctrl+C取消...")
        
        # 执行调查
        result = investigator.investigate_potential_stocks()
        
        print(f"\n📊 调查结果:")
        print(f"  成功: {result.get('success', False)}")
        
        if result.get('success'):
            recommendations = result.get('recommendations', [])
            print(f"  推荐股票数量: {len(recommendations)}")
            
            if recommendations:
                print(f"\n🏆 推荐股票:")
                for i, stock in enumerate(recommendations[:5], 1):
                    code = stock.get('stock_code', '')
                    name = stock.get('stock_name', '')
                    score = stock.get('prediction_score', 0)
                    print(f"   {i}. {code} {name}: 预测分数{score:.3f}")
        else:
            error = result.get('error', 'Unknown error')
            print(f"  错误: {error}")
        
        return result.get('success', False)
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        return True
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 涨停筛选功能测试...")
    print("="*60)
    
    # 测试涨停数据分析器
    analyzer_ok = test_limit_up_analyzer()
    
    # 测试潜在股调查器
    investigator_ok = test_potential_stock_investigator()
    
    # 询问是否进行完整测试
    print("\n" + "="*60)
    print("📋 基础测试结论:")
    
    if analyzer_ok and investigator_ok:
        print("✅ 涨停数据分析器正常")
        print("✅ 潜在股调查器涨停筛选功能正常")
        
        # 询问是否进行完整测试
        try:
            choice = input("\n是否进行完整的调查流程测试？(y/N): ").strip().lower()
            if choice in ['y', 'yes']:
                integration_ok = test_integration()
                if integration_ok:
                    print("✅ 完整流程测试通过")
                else:
                    print("❌ 完整流程测试失败")
        except KeyboardInterrupt:
            print("\n⚠️ 跳过完整测试")
    else:
        print("❌ 基础功能测试失败")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
