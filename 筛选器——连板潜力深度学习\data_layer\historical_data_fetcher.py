#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据获取器
解决当前时间匹配问题，建立能够获取真实历史数据的系统，特别是涨停前1-5天的关键数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import akshare as ak
import time
import os
import json
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HistoricalDataFetcher:
    """历史数据获取器"""
    
    def __init__(self, cache_dir: str = "historical_data_cache"):
        self.cache_dir = cache_dir
        self.akshare_available = self._check_akshare_availability()
        
        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)
        
        # 数据获取配置
        self.fetch_config = {
            'kline_period': 60,         # K线数据获取天数
            'minute_period': 5,         # 分时数据获取天数
            'retry_times': 3,           # 重试次数
            'retry_delay': 2,           # 重试延迟（秒）
            'cache_expire_hours': 24,   # 缓存过期时间（小时）
            'batch_size': 10,           # 批量获取大小
            'rate_limit_delay': 1       # API限流延迟
        }
        
        # 交易日历缓存
        self.trading_calendar = None
        
        logger.info("📅 历史数据获取器初始化完成")
    
    def _check_akshare_availability(self) -> bool:
        """检查AKShare可用性"""
        try:
            import akshare as ak
            # 简单测试
            ak.stock_zh_a_spot_em()
            return True
        except Exception as e:
            logger.warning(f"AKShare不可用: {e}")
            return False
    
    def get_historical_kline_data(self, stock_code: str, target_date: datetime, 
                                 days_before: int = 30) -> Optional[pd.DataFrame]:
        """获取指定日期前的历史K线数据"""
        try:
            logger.info(f"📅 获取{stock_code}在{target_date.strftime('%Y-%m-%d')}前{days_before}天的K线数据")
            
            # 检查缓存
            cache_key = f"kline_{stock_code}_{target_date.strftime('%Y%m%d')}_{days_before}"
            cached_data = self._get_cached_data(cache_key)
            if cached_data is not None:
                logger.info(f"✅ 从缓存获取{stock_code}的K线数据")
                return cached_data
            
            if not self.akshare_available:
                logger.error("AKShare不可用，无法获取历史数据")
                return None
            
            # 计算开始日期（考虑交易日）
            start_date = self._get_trading_date_before(target_date, days_before + 10)  # 多获取一些以确保足够数据
            end_date = self._get_trading_date_before(target_date, 1)  # 目标日期前一天
            
            # 获取历史K线数据
            kline_data = self._fetch_kline_with_retry(
                stock_code, 
                start_date.strftime('%Y%m%d'), 
                end_date.strftime('%Y%m%d')
            )
            
            if kline_data is not None and not kline_data.empty:
                # 只保留需要的天数
                kline_data = kline_data.tail(days_before)
                
                # 缓存数据
                self._cache_data(cache_key, kline_data)
                
                logger.info(f"✅ 成功获取{stock_code}的{len(kline_data)}天K线数据")
                return kline_data
            else:
                logger.warning(f"⚠️ 未能获取{stock_code}的K线数据")
                return None
                
        except Exception as e:
            logger.error(f"获取{stock_code}历史K线数据失败: {e}")
            return None
    
    def get_historical_minute_data(self, stock_code: str, target_date: datetime) -> Optional[pd.DataFrame]:
        """获取指定日期的历史分时数据"""
        try:
            logger.info(f"📅 获取{stock_code}在{target_date.strftime('%Y-%m-%d')}的分时数据")
            
            # 检查缓存
            cache_key = f"minute_{stock_code}_{target_date.strftime('%Y%m%d')}"
            cached_data = self._get_cached_data(cache_key)
            if cached_data is not None:
                logger.info(f"✅ 从缓存获取{stock_code}的分时数据")
                return cached_data
            
            if not self.akshare_available:
                logger.error("AKShare不可用，无法获取分时数据")
                return None
            
            # 确保是交易日
            if not self._is_trading_day(target_date):
                logger.warning(f"⚠️ {target_date.strftime('%Y-%m-%d')}不是交易日")
                return None
            
            # 获取分时数据
            minute_data = self._fetch_minute_with_retry(stock_code, target_date.strftime('%Y%m%d'))
            
            if minute_data is not None and not minute_data.empty:
                # 缓存数据
                self._cache_data(cache_key, minute_data)
                
                logger.info(f"✅ 成功获取{stock_code}的{len(minute_data)}条分时数据")
                return minute_data
            else:
                logger.warning(f"⚠️ 未能获取{stock_code}的分时数据")
                return None
                
        except Exception as e:
            logger.error(f"获取{stock_code}历史分时数据失败: {e}")
            return None
    
    def get_historical_capital_flow(self, stock_code: str, target_date: datetime, 
                                  days_before: int = 10) -> Optional[pd.DataFrame]:
        """获取指定日期前的历史资金流向数据"""
        try:
            logger.info(f"📅 获取{stock_code}在{target_date.strftime('%Y-%m-%d')}前{days_before}天的资金流向数据")
            
            # 检查缓存
            cache_key = f"capital_{stock_code}_{target_date.strftime('%Y%m%d')}_{days_before}"
            cached_data = self._get_cached_data(cache_key)
            if cached_data is not None:
                logger.info(f"✅ 从缓存获取{stock_code}的资金流向数据")
                return cached_data
            
            if not self.akshare_available:
                logger.error("AKShare不可用，无法获取资金流向数据")
                return None
            
            # 获取资金流向数据
            capital_data = self._fetch_capital_flow_with_retry(stock_code)
            
            if capital_data is not None and not capital_data.empty:
                # 筛选目标日期前的数据
                capital_data['日期'] = pd.to_datetime(capital_data['日期'])
                filtered_data = capital_data[capital_data['日期'] < target_date].tail(days_before)
                
                if not filtered_data.empty:
                    # 缓存数据
                    self._cache_data(cache_key, filtered_data)
                    
                    logger.info(f"✅ 成功获取{stock_code}的{len(filtered_data)}天资金流向数据")
                    return filtered_data
            
            logger.warning(f"⚠️ 未能获取{stock_code}的资金流向数据")
            return None
                
        except Exception as e:
            logger.error(f"获取{stock_code}历史资金流向数据失败: {e}")
            return None
    
    def _fetch_kline_with_retry(self, stock_code: str, start_date: str, end_date: str) -> Optional[pd.DataFrame]:
        """带重试的K线数据获取"""
        for attempt in range(self.fetch_config['retry_times']):
            try:
                time.sleep(self.fetch_config['rate_limit_delay'])  # API限流
                
                # 使用AKShare获取历史K线数据
                kline_data = ak.stock_zh_a_hist(
                    symbol=stock_code,
                    period="daily",
                    start_date=start_date,
                    end_date=end_date,
                    adjust=""
                )
                
                if not kline_data.empty:
                    # 标准化列名
                    kline_data = self._standardize_kline_columns(kline_data)
                    return kline_data
                
            except Exception as e:
                logger.warning(f"第{attempt + 1}次获取K线数据失败: {e}")
                if attempt < self.fetch_config['retry_times'] - 1:
                    time.sleep(self.fetch_config['retry_delay'])
        
        return None
    
    def _fetch_minute_with_retry(self, stock_code: str, date: str) -> Optional[pd.DataFrame]:
        """带重试的分时数据获取"""
        for attempt in range(self.fetch_config['retry_times']):
            try:
                time.sleep(self.fetch_config['rate_limit_delay'])  # API限流
                
                # 使用AKShare获取分时数据
                minute_data = ak.stock_zh_a_hist_min_em(
                    symbol=stock_code,
                    start_date=f"{date} 09:30:00",
                    end_date=f"{date} 15:00:00",
                    period="1",
                    adjust=""
                )
                
                if not minute_data.empty:
                    # 标准化列名
                    minute_data = self._standardize_minute_columns(minute_data)
                    return minute_data
                
            except Exception as e:
                logger.warning(f"第{attempt + 1}次获取分时数据失败: {e}")
                if attempt < self.fetch_config['retry_times'] - 1:
                    time.sleep(self.fetch_config['retry_delay'])
        
        return None
    
    def _fetch_capital_flow_with_retry(self, stock_code: str) -> Optional[pd.DataFrame]:
        """带重试的资金流向数据获取"""
        for attempt in range(self.fetch_config['retry_times']):
            try:
                time.sleep(self.fetch_config['rate_limit_delay'])  # API限流
                
                # 使用AKShare获取资金流向数据
                market = "sh" if stock_code.startswith('6') else "sz"
                capital_data = ak.stock_individual_fund_flow(stock=stock_code, market=market)
                
                if not capital_data.empty:
                    return capital_data
                
            except Exception as e:
                logger.warning(f"第{attempt + 1}次获取资金流向数据失败: {e}")
                if attempt < self.fetch_config['retry_times'] - 1:
                    time.sleep(self.fetch_config['retry_delay'])
        
        return None
    
    def _standardize_kline_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化K线数据列名"""
        column_mapping = {
            '日期': 'date',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount',
            '振幅': 'amplitude',
            '涨跌幅': 'change_pct',
            '涨跌额': 'change_amount',
            '换手率': 'turnover_rate'
        }
        
        for chinese_name, english_name in column_mapping.items():
            if chinese_name in df.columns:
                df[english_name] = df[chinese_name]
        
        # 确保日期格式正确
        if 'date' in df.columns:
            df['date'] = pd.to_datetime(df['date'])
        
        return df
    
    def _standardize_minute_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """标准化分时数据列名"""
        column_mapping = {
            '时间': 'datetime',
            '开盘': 'open',
            '收盘': 'close',
            '最高': 'high',
            '最低': 'low',
            '成交量': 'volume',
            '成交额': 'amount'
        }
        
        for chinese_name, english_name in column_mapping.items():
            if chinese_name in df.columns:
                df[english_name] = df[chinese_name]
        
        # 提取时间
        if 'datetime' in df.columns:
            df['time'] = pd.to_datetime(df['datetime']).dt.strftime('%H:%M')

        return df

    def _get_trading_calendar(self) -> pd.DataFrame:
        """获取交易日历"""
        try:
            if self.trading_calendar is not None:
                return self.trading_calendar

            # 检查缓存
            calendar_cache_file = os.path.join(self.cache_dir, "trading_calendar.csv")

            if os.path.exists(calendar_cache_file):
                # 检查缓存是否过期（7天）
                cache_time = os.path.getmtime(calendar_cache_file)
                if time.time() - cache_time < 7 * 24 * 3600:
                    self.trading_calendar = pd.read_csv(calendar_cache_file)
                    self.trading_calendar['calendar_date'] = pd.to_datetime(self.trading_calendar['calendar_date'])
                    logger.info("✅ 从缓存加载交易日历")
                    return self.trading_calendar

            # 获取交易日历
            if self.akshare_available:
                try:
                    # 获取最近2年的交易日历
                    start_year = datetime.now().year - 1
                    end_year = datetime.now().year + 1

                    calendar_data = ak.stock_zh_a_trade_date()

                    if not calendar_data.empty:
                        calendar_data.columns = ['calendar_date']
                        calendar_data['calendar_date'] = pd.to_datetime(calendar_data['calendar_date'])

                        # 筛选时间范围
                        calendar_data = calendar_data[
                            (calendar_data['calendar_date'].dt.year >= start_year) &
                            (calendar_data['calendar_date'].dt.year <= end_year)
                        ]

                        self.trading_calendar = calendar_data

                        # 缓存交易日历
                        calendar_data.to_csv(calendar_cache_file, index=False)
                        logger.info(f"✅ 获取交易日历成功: {len(calendar_data)}个交易日")
                        return self.trading_calendar

                except Exception as e:
                    logger.warning(f"获取交易日历失败: {e}")

            # 如果无法获取，生成简单的工作日日历
            logger.warning("使用简化的工作日日历")
            dates = pd.date_range(start='2023-01-01', end='2025-12-31', freq='B')  # 工作日
            self.trading_calendar = pd.DataFrame({'calendar_date': dates})

            return self.trading_calendar

        except Exception as e:
            logger.error(f"获取交易日历失败: {e}")
            # 返回空的DataFrame
            return pd.DataFrame({'calendar_date': []})

    def _is_trading_day(self, date: datetime) -> bool:
        """判断是否为交易日"""
        try:
            calendar = self._get_trading_calendar()
            if calendar.empty:
                # 简单判断：周一到周五
                return date.weekday() < 5

            date_only = date.date()
            trading_dates = calendar['calendar_date'].dt.date
            return date_only in trading_dates.values

        except Exception as e:
            logger.error(f"判断交易日失败: {e}")
            return date.weekday() < 5  # 默认工作日

    def _get_trading_date_before(self, target_date: datetime, days_before: int) -> datetime:
        """获取指定日期前N个交易日的日期"""
        try:
            calendar = self._get_trading_calendar()
            if calendar.empty:
                # 简单计算：假设每周5个交易日
                business_days = pd.bdate_range(end=target_date, periods=days_before + 10, freq='B')
                return business_days[0]

            # 筛选目标日期之前的交易日
            target_date_only = target_date.date()
            before_dates = calendar[calendar['calendar_date'].dt.date < target_date_only]

            if len(before_dates) >= days_before:
                return before_dates.iloc[-days_before]['calendar_date']
            else:
                # 如果交易日不够，返回最早的交易日
                return before_dates.iloc[0]['calendar_date'] if not before_dates.empty else target_date - timedelta(days=days_before * 2)

        except Exception as e:
            logger.error(f"计算交易日失败: {e}")
            return target_date - timedelta(days=days_before * 2)

    def _get_cached_data(self, cache_key: str) -> Optional[pd.DataFrame]:
        """获取缓存数据"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")

            if os.path.exists(cache_file):
                # 检查缓存是否过期
                cache_time = os.path.getmtime(cache_file)
                expire_time = self.fetch_config['cache_expire_hours'] * 3600

                if time.time() - cache_time < expire_time:
                    data = pd.read_csv(cache_file)

                    # 恢复日期时间格式
                    if 'date' in data.columns:
                        data['date'] = pd.to_datetime(data['date'])
                    if 'datetime' in data.columns:
                        data['datetime'] = pd.to_datetime(data['datetime'])
                    if '日期' in data.columns:
                        data['日期'] = pd.to_datetime(data['日期'])

                    return data

            return None

        except Exception as e:
            logger.warning(f"读取缓存失败: {e}")
            return None

    def _cache_data(self, cache_key: str, data: pd.DataFrame):
        """缓存数据"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{cache_key}.csv")
            data.to_csv(cache_file, index=False)

        except Exception as e:
            logger.warning(f"缓存数据失败: {e}")

    def get_limit_up_stocks_before_date(self, target_date: datetime, days_before: int = 5) -> List[Dict[str, Any]]:
        """获取指定日期前N天内的涨停股票"""
        try:
            logger.info(f"📅 获取{target_date.strftime('%Y-%m-%d')}前{days_before}天内的涨停股票")

            # 优先使用本地数据
            limit_up_stocks = self._get_local_limit_up_stocks(target_date, days_before)

            if limit_up_stocks:
                logger.info(f"✅ 从本地数据找到{len(limit_up_stocks)}只涨停股票")
                return limit_up_stocks

            # 如果本地数据不可用，尝试API获取
            logger.info("本地数据不可用，尝试API获取...")
            limit_up_stocks = []

            # 遍历前N天
            for i in range(1, days_before + 1):
                check_date = target_date - timedelta(days=i)

                # 跳过非交易日
                if not self._is_trading_day(check_date):
                    continue

                # 获取当日涨停股票
                daily_limit_ups = self._get_daily_limit_up_stocks(check_date)

                if daily_limit_ups:
                    for stock in daily_limit_ups:
                        stock['limit_up_date'] = check_date.strftime('%Y-%m-%d')
                        stock['days_before_target'] = i
                        limit_up_stocks.append(stock)

                # API限流
                time.sleep(self.fetch_config['rate_limit_delay'])

            logger.info(f"✅ 找到{len(limit_up_stocks)}只涨停股票")
            return limit_up_stocks

        except Exception as e:
            logger.error(f"获取涨停股票失败: {e}")
            return []

    def _get_local_limit_up_stocks(self, target_date: datetime, days_before: int) -> List[Dict[str, Any]]:
        """从本地文件获取涨停股票数据"""
        try:
            # 本地数据路径
            local_data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(__file__))), "limit_up_data")

            if not os.path.exists(local_data_path):
                logger.warning(f"本地涨停数据路径不存在: {local_data_path}")
                return []

            limit_up_stocks = []

            # 遍历前N天
            for i in range(1, days_before + 1):
                check_date = target_date - timedelta(days=i)

                # 跳过非交易日
                if not self._is_trading_day(check_date):
                    continue

                # 构建文件路径
                date_str = check_date.strftime('%Y%m%d')
                file_path = os.path.join(local_data_path, f"{date_str}.json")

                if os.path.exists(file_path):
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            data = json.load(f)

                        stocks = data.get('stocks', [])
                        for stock in stocks:
                            # 转换数据格式
                            converted_stock = {
                                'stock_code': stock.get('code', ''),
                                'stock_name': stock.get('name', ''),
                                'close_price': stock.get('price', 0),
                                'change_pct': stock.get('change_pct', 0),
                                'turnover_rate': 0,  # 本地数据中没有，设为0
                                'volume': 0,  # 本地数据中没有，设为0
                                'amount': 0,  # 本地数据中没有，设为0
                                'consecutive_days': stock.get('consecutive_days', 1),
                                'reason': stock.get('limit_up_reason', ''),
                                'limit_up_date': check_date.strftime('%Y-%m-%d'),
                                'days_before_target': i
                            }
                            limit_up_stocks.append(converted_stock)

                        logger.info(f"✅ 读取{date_str}涨停数据: {len(stocks)}只股票")

                    except Exception as e:
                        logger.warning(f"读取{file_path}失败: {e}")
                        continue
                else:
                    logger.warning(f"文件不存在: {file_path}")

            return limit_up_stocks

        except Exception as e:
            logger.error(f"从本地获取涨停股票数据失败: {e}")
            return []

    def _get_daily_limit_up_stocks(self, date: datetime) -> List[Dict[str, Any]]:
        """获取指定日期的涨停股票"""
        try:
            if not self.akshare_available:
                return []

            # 获取涨停股票数据
            limit_up_data = ak.stock_zh_a_st_em(symbol="涨停")

            if limit_up_data.empty:
                return []

            # 转换为字典列表
            stocks = []
            for _, row in limit_up_data.iterrows():
                stock_info = {
                    'stock_code': row.get('代码', ''),
                    'stock_name': row.get('名称', ''),
                    'close_price': float(row.get('最新价', 0)),
                    'change_pct': float(row.get('涨跌幅', 0)),
                    'turnover_rate': float(row.get('换手率', 0)),
                    'volume': float(row.get('成交量', 0)),
                    'amount': float(row.get('成交额', 0))
                }
                stocks.append(stock_info)

            return stocks

        except Exception as e:
            logger.warning(f"获取{date.strftime('%Y-%m-%d')}涨停股票失败: {e}")
            return []

    def batch_get_historical_data(self, stock_list: List[str], target_date: datetime,
                                days_before: int = 30) -> Dict[str, Dict[str, Any]]:
        """批量获取历史数据"""
        try:
            logger.info(f"📅 批量获取{len(stock_list)}只股票的历史数据")

            results = {}

            for i, stock_code in enumerate(stock_list):
                try:
                    logger.info(f"处理第{i+1}/{len(stock_list)}只股票: {stock_code}")

                    # 获取K线数据
                    kline_data = self.get_historical_kline_data(stock_code, target_date, days_before)

                    # 获取分时数据（目标日期前一天）
                    minute_date = self._get_trading_date_before(target_date, 1)
                    minute_data = self.get_historical_minute_data(stock_code, minute_date)

                    # 获取资金流向数据
                    capital_data = self.get_historical_capital_flow(stock_code, target_date, 10)

                    results[stock_code] = {
                        'kline_data': kline_data,
                        'minute_data': minute_data,
                        'capital_data': capital_data,
                        'data_quality': self._assess_data_quality(kline_data, minute_data, capital_data)
                    }

                    # 批量处理间隔
                    if (i + 1) % self.fetch_config['batch_size'] == 0:
                        logger.info(f"已处理{i+1}只股票，暂停{self.fetch_config['rate_limit_delay']}秒...")
                        time.sleep(self.fetch_config['rate_limit_delay'])

                except Exception as e:
                    logger.error(f"获取{stock_code}数据失败: {e}")
                    results[stock_code] = {
                        'kline_data': None,
                        'minute_data': None,
                        'capital_data': None,
                        'data_quality': 'error'
                    }

            logger.info(f"✅ 批量数据获取完成，成功率: {len([r for r in results.values() if r['data_quality'] != 'error'])}/{len(stock_list)}")
            return results

        except Exception as e:
            logger.error(f"批量获取历史数据失败: {e}")
            return {}

    def _assess_data_quality(self, kline_data: Optional[pd.DataFrame],
                           minute_data: Optional[pd.DataFrame],
                           capital_data: Optional[pd.DataFrame]) -> str:
        """评估数据质量"""
        try:
            quality_score = 0

            if kline_data is not None and not kline_data.empty:
                quality_score += 40

            if minute_data is not None and not minute_data.empty:
                quality_score += 30

            if capital_data is not None and not capital_data.empty:
                quality_score += 30

            if quality_score >= 80:
                return 'excellent'
            elif quality_score >= 60:
                return 'good'
            elif quality_score >= 40:
                return 'fair'
            else:
                return 'poor'

        except Exception as e:
            logger.error(f"评估数据质量失败: {e}")
            return 'unknown'

def main():
    """测试历史数据获取器"""
    print("📅 测试历史数据获取器...")

    fetcher = HistoricalDataFetcher()

    # 测试获取K线数据
    test_stock = "000001"
    target_date = datetime(2024, 7, 10)  # 测试日期

    print(f"\n🔍 测试获取{test_stock}的历史K线数据...")
    kline_data = fetcher.get_historical_kline_data(test_stock, target_date, 20)

    if kline_data is not None:
        print(f"✅ 成功获取K线数据: {len(kline_data)}天")
        print(f"数据范围: {kline_data['date'].min()} 到 {kline_data['date'].max()}")
    else:
        print("❌ 获取K线数据失败")

    # 测试获取分时数据
    print(f"\n🔍 测试获取{test_stock}的分时数据...")
    minute_data = fetcher.get_historical_minute_data(test_stock, target_date)

    if minute_data is not None:
        print(f"✅ 成功获取分时数据: {len(minute_data)}条")
    else:
        print("❌ 获取分时数据失败")

    # 测试获取资金流向数据
    print(f"\n🔍 测试获取{test_stock}的资金流向数据...")
    capital_data = fetcher.get_historical_capital_flow(test_stock, target_date, 10)

    if capital_data is not None:
        print(f"✅ 成功获取资金流向数据: {len(capital_data)}天")
    else:
        print("❌ 获取资金流向数据失败")

if __name__ == "__main__":
    main()
