#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
历史数据源测试脚本
测试各个数据源对于几个月前历史数据的获取能力
包括：历史分时数据、历史K线数据、历史资金流向、历史筹码分布
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('test_historical_data.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)

class HistoricalDataSourceTester:
    """历史数据源测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.test_stock_code = "000001"  # 平安银行作为测试股票
        self.test_stock_name = "平安银行"
        
        # 测试时间点：3个月前、6个月前
        self.test_dates = [
            datetime.now() - timedelta(days=90),   # 3个月前
            datetime.now() - timedelta(days=180),  # 6个月前
        ]
        
        logger.info("🧪 历史数据源测试器初始化完成")
        logger.info(f"📊 测试股票: {self.test_stock_name}({self.test_stock_code})")
        logger.info(f"📅 测试时间点: {[d.strftime('%Y-%m-%d') for d in self.test_dates]}")
    
    def test_akshare_data_sources(self):
        """测试AKShare数据源"""
        logger.info("🔍 开始测试AKShare数据源...")
        
        try:
            import akshare as ak
            akshare_available = True
        except ImportError:
            logger.error("❌ AKShare未安装")
            return {"available": False, "error": "AKShare未安装"}
        
        results = {
            "available": True,
            "kline_data": {},
            "minute_data": {},
            "capital_flow": {},
            "chip_distribution": {},
            "errors": []
        }
        
        for test_date in self.test_dates:
            date_str = test_date.strftime('%Y-%m-%d')
            logger.info(f"📅 测试日期: {date_str}")
            
            # 测试K线数据
            try:
                start_date = (test_date - timedelta(days=30)).strftime('%Y%m%d')
                end_date = test_date.strftime('%Y%m%d')
                
                kline_data = ak.stock_zh_a_hist(
                    symbol=self.test_stock_code,
                    period="daily",
                    start_date=start_date,
                    end_date=end_date,
                    adjust=""
                )
                
                results["kline_data"][date_str] = {
                    "success": True,
                    "data_count": len(kline_data),
                    "date_range": f"{start_date} - {end_date}",
                    "columns": list(kline_data.columns) if not kline_data.empty else [],
                    "sample_data": kline_data.head(2).to_dict() if not kline_data.empty else {}
                }
                logger.info(f"✅ K线数据获取成功: {len(kline_data)}条记录")
                
            except Exception as e:
                error_msg = f"K线数据获取失败: {str(e)}"
                results["kline_data"][date_str] = {"success": False, "error": error_msg}
                results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
            
            # 测试分时数据
            try:
                minute_data = ak.stock_zh_a_minute(
                    symbol=self.test_stock_code,
                    period='1',
                    adjust=''
                )
                
                results["minute_data"][date_str] = {
                    "success": True,
                    "data_count": len(minute_data),
                    "columns": list(minute_data.columns) if not minute_data.empty else [],
                    "latest_date": str(minute_data.index[-1]) if not minute_data.empty else "无数据",
                    "note": "AKShare分时数据通常只提供最近几天的数据"
                }
                logger.info(f"✅ 分时数据获取成功: {len(minute_data)}条记录")
                
            except Exception as e:
                error_msg = f"分时数据获取失败: {str(e)}"
                results["minute_data"][date_str] = {"success": False, "error": error_msg}
                results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
            
            # 测试资金流向数据
            try:
                capital_flow = ak.stock_individual_fund_flow(
                    stock=self.test_stock_code,
                    market="sz"
                )
                
                results["capital_flow"][date_str] = {
                    "success": True,
                    "data_count": len(capital_flow),
                    "columns": list(capital_flow.columns) if not capital_flow.empty else [],
                    "date_range": f"{capital_flow.index[0]} - {capital_flow.index[-1]}" if not capital_flow.empty else "无数据"
                }
                logger.info(f"✅ 资金流向数据获取成功: {len(capital_flow)}条记录")
                
            except Exception as e:
                error_msg = f"资金流向数据获取失败: {str(e)}"
                results["capital_flow"][date_str] = {"success": False, "error": error_msg}
                results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
            
            # 测试筹码分布数据（AKShare可能不直接提供）
            try:
                # 尝试获取股东户数等相关数据
                holder_data = ak.stock_zh_a_gdhs(symbol=self.test_stock_code)
                
                results["chip_distribution"][date_str] = {
                    "success": True,
                    "data_type": "股东户数数据",
                    "data_count": len(holder_data),
                    "columns": list(holder_data.columns) if not holder_data.empty else [],
                    "note": "AKShare不直接提供筹码分布，这里是股东户数数据"
                }
                logger.info(f"✅ 股东户数数据获取成功: {len(holder_data)}条记录")
                
            except Exception as e:
                error_msg = f"筹码分布相关数据获取失败: {str(e)}"
                results["chip_distribution"][date_str] = {"success": False, "error": error_msg}
                results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
            
            # 添加延迟避免API限流
            time.sleep(2)
        
        self.test_results["akshare"] = results
        return results
    
    def test_efinance_data_sources(self):
        """测试efinance数据源"""
        logger.info("🔍 开始测试efinance数据源...")
        
        try:
            import efinance as ef
            efinance_available = True
        except ImportError:
            logger.error("❌ efinance未安装，请运行: pip install efinance")
            return {"available": False, "error": "efinance未安装"}
        
        results = {
            "available": True,
            "kline_data": {},
            "minute_data": {},
            "capital_flow": {},
            "chip_distribution": {},
            "errors": []
        }
        
        for test_date in self.test_dates:
            date_str = test_date.strftime('%Y-%m-%d')
            logger.info(f"📅 测试日期: {date_str}")
            
            # 测试K线数据
            try:
                start_date = (test_date - timedelta(days=30)).strftime('%Y%m%d')
                end_date = test_date.strftime('%Y%m%d')
                
                kline_data = ef.stock.get_quote_history(
                    stock_codes=self.test_stock_code,
                    beg=start_date,
                    end=end_date
                )
                
                results["kline_data"][date_str] = {
                    "success": True,
                    "data_count": len(kline_data),
                    "date_range": f"{start_date} - {end_date}",
                    "columns": list(kline_data.columns) if not kline_data.empty else [],
                    "sample_data": kline_data.head(2).to_dict() if not kline_data.empty else {}
                }
                logger.info(f"✅ K线数据获取成功: {len(kline_data)}条记录")
                
            except Exception as e:
                error_msg = f"K线数据获取失败: {str(e)}"
                results["kline_data"][date_str] = {"success": False, "error": error_msg}
                results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
            
            # 测试分时数据
            try:
                minute_data = ef.stock.get_quote_history(
                    stock_codes=self.test_stock_code,
                    klt=1,  # 1分钟K线
                    beg=test_date.strftime('%Y%m%d'),
                    end=test_date.strftime('%Y%m%d')
                )
                
                results["minute_data"][date_str] = {
                    "success": True,
                    "data_count": len(minute_data),
                    "columns": list(minute_data.columns) if not minute_data.empty else [],
                    "sample_data": minute_data.head(2).to_dict() if not minute_data.empty else {}
                }
                logger.info(f"✅ 分时数据获取成功: {len(minute_data)}条记录")
                
            except Exception as e:
                error_msg = f"分时数据获取失败: {str(e)}"
                results["minute_data"][date_str] = {"success": False, "error": error_msg}
                results["errors"].append(error_msg)
                logger.error(f"❌ {error_msg}")
            
            # 添加延迟避免API限流
            time.sleep(2)
        
        self.test_results["efinance"] = results
        return results

    def test_tushare_data_sources(self):
        """测试Tushare数据源"""
        logger.info("🔍 开始测试Tushare数据源...")

        try:
            import tushare as ts
            # 注意：Tushare需要token，这里只是测试导入
            tushare_available = True
        except ImportError:
            logger.error("❌ Tushare未安装，请运行: pip install tushare")
            return {"available": False, "error": "Tushare未安装"}

        results = {
            "available": True,
            "note": "Tushare需要token才能使用，这里只测试导入",
            "kline_data": {},
            "minute_data": {},
            "capital_flow": {},
            "errors": []
        }

        # 由于Tushare需要token，这里只记录可用性
        logger.info("⚠️ Tushare需要申请token才能获取数据")

        self.test_results["tushare"] = results
        return results

    def test_yfinance_data_sources(self):
        """测试yfinance数据源（主要用于港股美股，但也可以测试A股）"""
        logger.info("🔍 开始测试yfinance数据源...")

        try:
            import yfinance as yf
            yfinance_available = True
        except ImportError:
            logger.error("❌ yfinance未安装，请运行: pip install yfinance")
            return {"available": False, "error": "yfinance未安装"}

        results = {
            "available": True,
            "kline_data": {},
            "errors": []
        }

        # A股在yfinance中的格式通常是 股票代码.SS 或 股票代码.SZ
        test_symbols = [f"{self.test_stock_code}.SZ", f"{self.test_stock_code}.SS"]

        for test_date in self.test_dates:
            date_str = test_date.strftime('%Y-%m-%d')
            logger.info(f"📅 测试日期: {date_str}")

            for symbol in test_symbols:
                try:
                    start_date = test_date - timedelta(days=30)
                    end_date = test_date

                    ticker = yf.Ticker(symbol)
                    kline_data = ticker.history(start=start_date, end=end_date)

                    if not kline_data.empty:
                        results["kline_data"][f"{date_str}_{symbol}"] = {
                            "success": True,
                            "symbol": symbol,
                            "data_count": len(kline_data),
                            "date_range": f"{start_date.strftime('%Y-%m-%d')} - {end_date.strftime('%Y-%m-%d')}",
                            "columns": list(kline_data.columns),
                            "sample_data": kline_data.head(2).to_dict()
                        }
                        logger.info(f"✅ {symbol} K线数据获取成功: {len(kline_data)}条记录")
                        break  # 成功获取就跳出循环

                except Exception as e:
                    error_msg = f"{symbol} K线数据获取失败: {str(e)}"
                    results["errors"].append(error_msg)
                    logger.error(f"❌ {error_msg}")

            time.sleep(1)

        self.test_results["yfinance"] = results
        return results

    def generate_test_report(self):
        """生成测试报告"""
        logger.info("📋 生成测试报告...")

        report = {
            "test_time": datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            "test_stock": f"{self.test_stock_name}({self.test_stock_code})",
            "test_dates": [d.strftime('%Y-%m-%d') for d in self.test_dates],
            "summary": {},
            "detailed_results": self.test_results,
            "recommendations": []
        }

        # 生成摘要
        for source_name, source_results in self.test_results.items():
            if source_results.get("available", False):
                summary = {
                    "available": True,
                    "data_types": {},
                    "error_count": len(source_results.get("errors", []))
                }

                # 统计各类数据的可用性
                for data_type in ["kline_data", "minute_data", "capital_flow", "chip_distribution"]:
                    if data_type in source_results:
                        success_count = sum(1 for result in source_results[data_type].values()
                                          if isinstance(result, dict) and result.get("success", False))
                        total_count = len(source_results[data_type])
                        summary["data_types"][data_type] = f"{success_count}/{total_count}"

                report["summary"][source_name] = summary
            else:
                report["summary"][source_name] = {"available": False, "reason": source_results.get("error", "未知错误")}

        # 生成建议
        recommendations = []

        # 检查K线数据可用性
        kline_sources = []
        for source_name, source_results in self.test_results.items():
            if source_results.get("available") and "kline_data" in source_results:
                success_count = sum(1 for result in source_results["kline_data"].values()
                                  if isinstance(result, dict) and result.get("success", False))
                if success_count > 0:
                    kline_sources.append(source_name)

        if kline_sources:
            recommendations.append(f"✅ K线历史数据推荐使用: {', '.join(kline_sources)}")
        else:
            recommendations.append("❌ 警告：没有找到可靠的K线历史数据源")

        # 检查分时数据可用性
        minute_sources = []
        for source_name, source_results in self.test_results.items():
            if source_results.get("available") and "minute_data" in source_results:
                success_count = sum(1 for result in source_results["minute_data"].values()
                                  if isinstance(result, dict) and result.get("success", False))
                if success_count > 0:
                    minute_sources.append(source_name)

        if minute_sources:
            recommendations.append(f"✅ 分时历史数据推荐使用: {', '.join(minute_sources)}")
        else:
            recommendations.append("⚠️ 警告：分时历史数据获取困难，可能需要其他方案")

        # 检查资金流向数据
        capital_sources = []
        for source_name, source_results in self.test_results.items():
            if source_results.get("available") and "capital_flow" in source_results:
                success_count = sum(1 for result in source_results["capital_flow"].values()
                                  if isinstance(result, dict) and result.get("success", False))
                if success_count > 0:
                    capital_sources.append(source_name)

        if capital_sources:
            recommendations.append(f"✅ 资金流向数据推荐使用: {', '.join(capital_sources)}")
        else:
            recommendations.append("⚠️ 警告：资金流向历史数据获取困难")

        # 检查筹码分布数据
        chip_sources = []
        for source_name, source_results in self.test_results.items():
            if source_results.get("available") and "chip_distribution" in source_results:
                success_count = sum(1 for result in source_results["chip_distribution"].values()
                                  if isinstance(result, dict) and result.get("success", False))
                if success_count > 0:
                    chip_sources.append(source_name)

        if chip_sources:
            recommendations.append(f"✅ 筹码分布数据推荐使用: {', '.join(chip_sources)}")
        else:
            recommendations.append("❌ 警告：筹码分布历史数据获取困难，可能需要替代方案")

        report["recommendations"] = recommendations

        # 保存报告
        import json
        with open('historical_data_test_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)

        logger.info("📄 测试报告已保存到: historical_data_test_report.json")
        return report

    def print_summary(self):
        """打印测试摘要"""
        print("\n" + "="*60)
        print("📊 历史数据源测试摘要")
        print("="*60)

        print(f"🎯 测试股票: {self.test_stock_name}({self.test_stock_code})")
        print(f"📅 测试时间: {[d.strftime('%Y-%m-%d') for d in self.test_dates]}")
        print()

        for source_name, source_results in self.test_results.items():
            print(f"📡 {source_name.upper()} 数据源:")

            if not source_results.get("available", False):
                print(f"   ❌ 不可用: {source_results.get('error', '未知错误')}")
                continue

            print(f"   ✅ 可用")

            # 显示各类数据的测试结果
            for data_type in ["kline_data", "minute_data", "capital_flow", "chip_distribution"]:
                if data_type in source_results:
                    success_count = sum(1 for result in source_results[data_type].values()
                                      if isinstance(result, dict) and result.get("success", False))
                    total_count = len(source_results[data_type])

                    data_type_name = {
                        "kline_data": "K线数据",
                        "minute_data": "分时数据",
                        "capital_flow": "资金流向",
                        "chip_distribution": "筹码分布"
                    }.get(data_type, data_type)

                    if success_count > 0:
                        print(f"   ✅ {data_type_name}: {success_count}/{total_count} 成功")
                    else:
                        print(f"   ❌ {data_type_name}: {success_count}/{total_count} 成功")

            if source_results.get("errors"):
                print(f"   ⚠️ 错误数量: {len(source_results['errors'])}")

            print()

        print("="*60)

def main():
    """主函数"""
    print("🧪 历史数据源测试开始...")
    print("="*60)

    # 创建测试器
    tester = HistoricalDataSourceTester()

    # 测试各个数据源
    print("\n🔍 开始测试各个数据源...")

    # 测试AKShare
    try:
        akshare_results = tester.test_akshare_data_sources()
        print(f"✅ AKShare测试完成")
    except Exception as e:
        logger.error(f"❌ AKShare测试失败: {e}")

    # 测试efinance
    try:
        efinance_results = tester.test_efinance_data_sources()
        print(f"✅ efinance测试完成")
    except Exception as e:
        logger.error(f"❌ efinance测试失败: {e}")

    # 测试Tushare
    try:
        tushare_results = tester.test_tushare_data_sources()
        print(f"✅ Tushare测试完成")
    except Exception as e:
        logger.error(f"❌ Tushare测试失败: {e}")

    # 测试yfinance
    try:
        yfinance_results = tester.test_yfinance_data_sources()
        print(f"✅ yfinance测试完成")
    except Exception as e:
        logger.error(f"❌ yfinance测试失败: {e}")

    # 生成报告
    print(f"\n📋 生成测试报告...")
    report = tester.generate_test_report()

    # 打印摘要
    tester.print_summary()

    # 打印建议
    print("💡 使用建议:")
    for recommendation in report["recommendations"]:
        print(f"   {recommendation}")

    print(f"\n📄 详细报告已保存到: historical_data_test_report.json")
    print(f"📄 日志文件: test_historical_data.log")
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
