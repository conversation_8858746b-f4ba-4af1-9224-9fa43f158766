#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
演示通知系统样式
展示当前通知样式和改进后的Windows系统通知样式
"""

import sys
import os
from datetime import datetime
import time
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def demo_current_notification_style():
    """演示当前的通知样式"""
    print("=" * 60)
    print("🔍 当前通知系统样式演示")
    print("=" * 60)
    
    try:
        from notification_system import notification_system
        
        # 关闭声音，只看弹窗样式
        notification_system.set_sound_enabled(False)
        notification_system.set_popup_enabled(True)
        
        print("📢 当前通知样式说明:")
        print("   🔸 使用 tkinter.messagebox 弹窗")
        print("   🔸 简单的消息框样式")
        print("   🔸 阻塞式弹窗（需要点击确定）")
        print("   🔸 基础的图标显示")
        
        print(f"\n📊 演示不同类型的通知:")
        
        # 演示止损警报
        print(f"\n1️⃣ 止损警报样式:")
        print("   标题: 🚨 止损警报")
        print("   图标: ERROR (红色X)")
        print("   内容: 股票信息 + 亏损幅度 + 止损原因")
        
        # 演示止盈提醒
        print(f"\n2️⃣ 止盈提醒样式:")
        print("   标题: 🎉 止盈提醒") 
        print("   图标: INFO (蓝色i)")
        print("   内容: 股票信息 + 盈利幅度 + 建议策略")
        
        # 演示关键决策
        print(f"\n3️⃣ 关键决策样式:")
        print("   标题: 📊 关键决策")
        print("   图标: WARNING (黄色!)")
        print("   内容: 第X天决策 + 决策建议 + 原因")
        
        # 实际发送一个演示通知
        print(f"\n🚀 发送演示通知（3秒后自动发送）...")
        time.sleep(3)
        
        success = notification_system.send_stop_loss_alert(
            stock_code="DEMO001",
            stock_name="演示股票",
            current_price=10.50,
            loss_pct=-8.5,
            reason="跌破主力成本区"
        )
        
        if success:
            print("   ✅ 演示通知已发送（请查看弹窗）")
        else:
            print("   ❌ 演示通知发送失败")
        
        print(f"\n⚠️ 当前样式的问题:")
        print("   🔸 不是真正的Windows系统通知")
        print("   🔸 样式比较简陋")
        print("   🔸 阻塞式，影响程序运行")
        print("   🔸 无法自定义样式和布局")
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")

def demo_windows_toast_notification():
    """演示Windows Toast通知样式"""
    print(f"\n" + "=" * 60)
    print("🚀 Windows Toast通知样式演示")
    print("=" * 60)
    
    try:
        # 尝试导入Windows Toast通知库
        try:
            from win10toast import ToastNotifier
            toast_available = True
            print("✅ Windows Toast通知库可用")
        except ImportError:
            toast_available = False
            print("❌ Windows Toast通知库不可用")
            print("   💡 需要安装: pip install win10toast")
        
        print(f"\n📊 Windows Toast通知特性:")
        print("   🔸 真正的Windows系统通知")
        print("   🔸 出现在系统通知区域")
        print("   🔸 非阻塞式，不影响程序运行")
        print("   🔸 可以自定义图标和声音")
        print("   🔸 支持点击回调")
        print("   🔸 自动消失（可设置时间）")
        
        if toast_available:
            print(f"\n🚀 发送Windows Toast演示通知...")
            
            toaster = ToastNotifier()
            
            # 演示不同类型的通知
            notifications = [
                {
                    'title': '🚨 止损警报',
                    'msg': '沧州大化(600230)\n当前价格: 13.20\n亏损: -8.5%\n建议立即止损！',
                    'duration': 10,
                    'icon_path': None
                },
                {
                    'title': '🎉 止盈提醒', 
                    'msg': '沧州大化(600230)\n当前价格: 16.80\n盈利: +15.2%\n考虑获利了结！',
                    'duration': 10,
                    'icon_path': None
                },
                {
                    'title': '🎯 第3天关键决策',
                    'msg': '沧州大化(600230)\n洗盘概率: 85%\n决策: 坚定持有\n原因: 洗盘确认',
                    'duration': 10,
                    'icon_path': None
                }
            ]
            
            for i, notif in enumerate(notifications, 1):
                print(f"   {i}. 发送: {notif['title']}")
                try:
                    toaster.show_toast(
                        title=notif['title'],
                        msg=notif['msg'],
                        duration=notif['duration'],
                        icon_path=notif['icon_path'],
                        threaded=True
                    )
                    time.sleep(2)  # 间隔发送
                except Exception as e:
                    print(f"      ❌ 发送失败: {e}")
            
            print(f"\n✅ Windows Toast通知演示完成")
            print("   💡 请查看Windows系统右下角的通知")
        
        else:
            print(f"\n💡 Windows Toast通知样式预览:")
            print("   ┌─────────────────────────────────┐")
            print("   │ 🚨 止损警报                    │")
            print("   │ ─────────────────────────────── │")
            print("   │ 沧州大化(600230)               │")
            print("   │ 当前价格: 13.20                │")
            print("   │ 亏损: -8.5%                    │")
            print("   │ 建议立即止损！                  │")
            print("   └─────────────────────────────────┘")
            print("   ↑ 出现在Windows系统通知区域")
        
    except Exception as e:
        print(f"❌ Windows Toast演示失败: {e}")

def demo_custom_notification_window():
    """演示自定义通知窗口样式"""
    print(f"\n" + "=" * 60)
    print("🎨 自定义通知窗口样式演示")
    print("=" * 60)
    
    try:
        import tkinter as tk
        from tkinter import ttk
        import threading
        
        print("📊 自定义通知窗口特性:")
        print("   🔸 美观的自定义界面")
        print("   🔸 股票专业样式")
        print("   🔸 颜色编码（红色止损、绿色止盈）")
        print("   🔸 自动消失或手动关闭")
        print("   🔸 非阻塞式")
        
        def create_custom_notification(title, message, notification_type="info", duration=10):
            """创建自定义通知窗口"""
            def show_notification():
                # 创建通知窗口
                notification_window = tk.Toplevel()
                notification_window.title("股票监控通知")
                notification_window.geometry("400x200")
                notification_window.resizable(False, False)
                
                # 设置窗口属性
                notification_window.attributes('-topmost', True)
                notification_window.attributes('-toolwindow', True)
                
                # 根据通知类型设置颜色
                if notification_type == "stop_loss":
                    bg_color = "#ffebee"  # 浅红色
                    title_color = "#d32f2f"  # 红色
                    border_color = "#f44336"
                elif notification_type == "take_profit":
                    bg_color = "#e8f5e8"  # 浅绿色
                    title_color = "#388e3c"  # 绿色
                    border_color = "#4caf50"
                elif notification_type == "critical":
                    bg_color = "#fff3e0"  # 浅橙色
                    title_color = "#f57c00"  # 橙色
                    border_color = "#ff9800"
                else:
                    bg_color = "#e3f2fd"  # 浅蓝色
                    title_color = "#1976d2"  # 蓝色
                    border_color = "#2196f3"
                
                notification_window.configure(bg=bg_color)
                
                # 标题框架
                title_frame = tk.Frame(notification_window, bg=title_color, height=40)
                title_frame.pack(fill=tk.X, padx=2, pady=2)
                title_frame.pack_propagate(False)
                
                title_label = tk.Label(
                    title_frame, 
                    text=title, 
                    bg=title_color, 
                    fg="white", 
                    font=("微软雅黑", 12, "bold")
                )
                title_label.pack(expand=True)
                
                # 消息框架
                message_frame = tk.Frame(notification_window, bg=bg_color)
                message_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
                
                message_label = tk.Label(
                    message_frame,
                    text=message,
                    bg=bg_color,
                    fg="#333333",
                    font=("微软雅黑", 10),
                    justify=tk.LEFT,
                    wraplength=350
                )
                message_label.pack(expand=True)
                
                # 按钮框架
                button_frame = tk.Frame(notification_window, bg=bg_color)
                button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
                
                close_button = tk.Button(
                    button_frame,
                    text="确定",
                    command=notification_window.destroy,
                    bg=border_color,
                    fg="white",
                    font=("微软雅黑", 9),
                    relief=tk.FLAT,
                    padx=20
                )
                close_button.pack(side=tk.RIGHT)
                
                # 定位到屏幕右下角
                notification_window.update_idletasks()
                screen_width = notification_window.winfo_screenwidth()
                screen_height = notification_window.winfo_screenheight()
                x = screen_width - 420
                y = screen_height - 250
                notification_window.geometry(f"+{x}+{y}")
                
                # 自动关闭
                if duration > 0:
                    notification_window.after(duration * 1000, notification_window.destroy)
                
                notification_window.mainloop()
            
            # 在新线程中显示
            thread = threading.Thread(target=show_notification, daemon=True)
            thread.start()
        
        print(f"\n🚀 发送自定义通知演示（3秒后）...")
        time.sleep(3)
        
        # 演示不同类型的自定义通知
        notifications = [
            {
                'title': '🚨 止损警报',
                'message': '股票: 沧州大化 (600230)\n当前价格: 13.20\n亏损幅度: -8.5%\n止损原因: 跌破主力成本区\n\n⚠️ 建议立即卖出止损！',
                'type': 'stop_loss'
            },
            {
                'title': '🎉 止盈提醒',
                'message': '股票: 沧州大化 (600230)\n当前价格: 16.80\n盈利幅度: +15.2%\n建议策略: 分批止盈\n\n💰 考虑获利了结！',
                'type': 'take_profit'
            },
            {
                'title': '🎯 第3天关键决策',
                'message': '股票: 沧州大化 (600230)\n当前价格: 13.50\n持仓盈亏: -6.4%\n决策建议: 坚定持有\n决策原因: 第3天洗盘确认\n\n🔔 请及时关注并执行！',
                'type': 'critical'
            }
        ]
        
        for i, notif in enumerate(notifications):
            print(f"   {i+1}. 显示: {notif['title']}")
            create_custom_notification(
                notif['title'], 
                notif['message'], 
                notif['type'], 
                duration=8
            )
            time.sleep(3)  # 间隔显示
        
        print(f"\n✅ 自定义通知窗口演示完成")
        print("   💡 请查看屏幕右下角的通知窗口")
        
    except Exception as e:
        print(f"❌ 自定义通知演示失败: {e}")

def main():
    """主演示函数"""
    print("🚀 通知系统样式演示")
    print(f"⏰ 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        # 演示当前通知样式
        demo_current_notification_style()
        
        # 演示Windows Toast通知
        demo_windows_toast_notification()
        
        # 演示自定义通知窗口
        demo_custom_notification_window()
        
        print(f"\n" + "=" * 60)
        print("📊 通知样式对比总结")
        print("=" * 60)
        
        print("1️⃣ 当前样式 (tkinter.messagebox):")
        print("   ✅ 简单易用")
        print("   ❌ 样式简陋")
        print("   ❌ 阻塞式")
        print("   ❌ 不够专业")
        
        print("\n2️⃣ Windows Toast通知:")
        print("   ✅ 系统原生样式")
        print("   ✅ 非阻塞式")
        print("   ✅ 专业外观")
        print("   ❌ 需要额外库")
        
        print("\n3️⃣ 自定义通知窗口:")
        print("   ✅ 完全自定义")
        print("   ✅ 股票专业样式")
        print("   ✅ 颜色编码")
        print("   ✅ 非阻塞式")
        print("   ✅ 功能丰富")
        
        print(f"\n💡 建议:")
        print("   🎯 推荐使用自定义通知窗口样式")
        print("   🎯 专业、美观、功能完整")
        print("   🎯 更符合股票软件的风格")
        
    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")
    
    print("\n✅ 通知样式演示完成")

if __name__ == "__main__":
    main()
