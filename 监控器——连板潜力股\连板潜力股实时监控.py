#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板潜力股实时监控 - 全时段涨停捕捉系统
基于《全时段涨停潜力股实时监控指南》开发

核心功能：
1. 平开股监控体系（-1%至+1%开盘）：量能突击、盘口异动、形态突破
2. 高开股监控体系（+2%至+6%开盘）：量能持续、抛压测试、跳空守护
3. 实时盘口监控：买盘厚度分析、挂单动态分析、撤单行为监测
4. 全时段监控策略：9:30-14:00不同时段专属策略
5. 买入信号判断：满足条件时自动发送Windows通知
6. 动态仓位计算：基于信号强度的智能仓位管理

作者：AI Assistant
版本：1.0.0
创建时间：2025-07-19
"""

import tkinter as tk
from tkinter import ttk, messagebox
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
from typing import Dict, List, Optional, Any
import time
import json
import os
from pathlib import Path

# 导入现有应用的数据获取模块
try:
    from enhanced_data_fetcher import EnhancedStockDataFetcher
    enhanced_stock_fetcher = EnhancedStockDataFetcher()
    print("✅ 成功导入增强数据获取器")

    # 检查备用数据源状态
    try:
        if hasattr(enhanced_stock_fetcher, 'multi_source_fetcher') and enhanced_stock_fetcher.multi_source_fetcher:
            print("✅ 多数据源备用获取器已就绪")
            # 显示可用的备用数据源
            available_sources = []
            if hasattr(enhanced_stock_fetcher.multi_source_fetcher, 'sources_available'):
                for source, available in enhanced_stock_fetcher.multi_source_fetcher.sources_available.items():
                    if available:
                        available_sources.append(source)
            if available_sources:
                print(f"📊 可用备用数据源: {', '.join(available_sources)}")
        else:
            print("⚠️ 备用数据源不可用，仅使用AKShare")
    except Exception as e:
        print(f"⚠️ 备用数据源检查失败: {e}")

except ImportError as e:
    print(f"⚠️ 无法导入增强数据获取器: {e}")
    print("⚠️ 尝试使用备用数据获取方法...")

# 导入已买入股票管理器
try:
    from bought_stocks_manager import bought_stocks_manager
    print("✅ 成功导入已买入股票管理器")
except ImportError as e:
    print(f"⚠️ 无法导入已买入股票管理器: {e}")
    bought_stocks_manager = None

# 导入卖出监控算法
try:
    from sell_monitoring_algorithms import sell_monitoring_algorithms
    print("✅ 成功导入卖出监控算法")
except ImportError as e:
    print(f"⚠️ 无法导入卖出监控算法: {e}")
    sell_monitoring_algorithms = None

    # 备用数据获取器
    try:
        import akshare as ak
        import requests
        AKSHARE_AVAILABLE = True
        print("✅ AKShare数据源可用")
        
        class BackupDataFetcher:
            """备用数据获取器"""
            
            def __init__(self):
                self.session = requests.Session()
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })
            
            def get_stock_basic_info(self, code):
                """获取股票基本信息"""
                try:
                    # 方法1：使用代码对照表（最快最稳定）
                    try:
                        df = ak.stock_info_a_code_name()
                        if not df.empty:
                            df['code'] = df['code'].astype(str).str.zfill(6)
                            stock_row = df[df['code'] == code]
                            if not stock_row.empty:
                                stock_name = stock_row['name'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法1代码对照表获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法1代码对照表失败 {code}: {e}")

                    # 方法2：使用个股信息接口
                    try:
                        stock_info = ak.stock_individual_info_em(symbol=code)
                        if not stock_info.empty:
                            name_row = stock_info[stock_info['item'] == '股票简称']
                            if not name_row.empty:
                                stock_name = name_row['value'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法2个股信息获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法2个股信息失败 {code}: {e}")

                    # 方法3：从实时行情中获取股票名称（备用）
                    try:
                        df = ak.stock_zh_a_spot_em()
                        if not df.empty:
                            df['代码'] = df['代码'].astype(str).str.zfill(6)
                            stock_row = df[df['代码'] == code]
                            if not stock_row.empty:
                                stock_name = stock_row['名称'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法3实时行情获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法3实时行情失败 {code}: {e}")

                    # 方法4：从本地映射表获取
                    try:
                        from stock_name_mapping import get_stock_name_from_mapping
                        mapped_name = get_stock_name_from_mapping(code)
                        if mapped_name:
                            print(f"   ✅ 方法4本地映射获取: {code} -> {mapped_name}")
                            return {'name': mapped_name}
                    except Exception as e:
                        print(f"   ⚠️ 方法4本地映射失败 {code}: {e}")

                    # 如果所有方法都失败，返回默认名称
                    print(f"   ⚠️ 所有方法都无法获取股票名称 {code}，使用默认名称")
                    return {'name': f'股票{code}'}

                except Exception as e:
                    print(f"   ❌ 获取股票基本信息异常 {code}: {e}")
                    return {'name': f'股票{code}'}
            
            def get_realtime_quote(self, codes):
                """获取实时行情"""
                try:
                    # 使用AKShare获取实时行情
                    df = ak.stock_zh_a_spot_em()
                    
                    if df.empty:
                        return pd.DataFrame()
                    
                    # 筛选指定股票
                    df['代码'] = df['代码'].astype(str).str.zfill(6)
                    filtered_df = df[df['代码'].isin(codes)]
                    
                    # 标准化列名以匹配主应用格式
                    if not filtered_df.empty:
                        filtered_df = filtered_df.rename(columns={
                            '代码': 'stock_code',
                            '名称': 'stock_name', 
                            '最新价': 'current_price',
                            '涨跌额': 'change',
                            '涨跌幅': 'change_pct',
                            '今开': 'open',
                            '最高': 'high',
                            '最低': 'low',
                            '昨收': 'prev_close',
                            '成交量': 'volume',
                            '成交额': 'amount',
                            '总市值': 'market_cap'
                        })
                    
                    return filtered_df
                    
                except Exception as e:
                    print(f"⚠️ 获取实时行情失败: {e}")
                    return pd.DataFrame()
            
            def get_market_depth(self, code):
                """获取五档行情"""
                try:
                    # 使用新浪财经API获取五档数据
                    url = f"http://hq.sinajs.cn/list={self._get_market_prefix(code)}{code}"
                    response = self.session.get(url, timeout=10)
                    
                    if response.status_code != 200:
                        return pd.DataFrame()
                    
                    data = response.text
                    if 'var hq_str_' not in data:
                        return pd.DataFrame()
                    
                    # 解析数据
                    content = data.split('"')[1]
                    items = content.split(',')
                    
                    if len(items) < 33:
                        return pd.DataFrame()
                    
                    # 构建五档数据
                    depth_data = {
                        'b1': float(items[6]) if items[6] else 0,
                        'bv1': int(items[10]) if items[10] else 0,
                        'b2': float(items[7]) if items[7] else 0,
                        'bv2': int(items[11]) if items[11] else 0,
                        'b3': float(items[8]) if items[8] else 0,
                        'bv3': int(items[12]) if items[12] else 0,
                        'b4': float(items[9]) if items[9] else 0,
                        'bv4': int(items[13]) if items[13] else 0,
                        'b5': float(items[14]) if len(items) > 14 and items[14] else 0,
                        'bv5': int(items[15]) if len(items) > 15 and items[15] else 0,
                        's1': float(items[20]) if items[20] else 0,
                        'sv1': int(items[21]) if items[21] else 0,
                        's2': float(items[22]) if items[22] else 0,
                        'sv2': int(items[23]) if items[23] else 0,
                        's3': float(items[24]) if items[24] else 0,
                        'sv3': int(items[25]) if items[25] else 0,
                        's4': float(items[26]) if items[26] else 0,
                        'sv4': int(items[27]) if items[27] else 0,
                        's5': float(items[28]) if items[28] else 0,
                        'sv5': int(items[29]) if items[29] else 0,
                    }
                    
                    return pd.DataFrame([depth_data])
                    
                except Exception as e:
                    print(f"⚠️ 获取五档数据失败 {code}: {e}")
                    return pd.DataFrame()
            
            def get_daily_kline(self, code, days=2):
                """获取日K线数据"""
                try:
                    df = ak.stock_zh_a_hist(symbol=code, adjust="qfq")
                    if not df.empty:
                        return df.tail(days)
                    return pd.DataFrame()
                except Exception as e:
                    print(f"⚠️ 获取日K线数据失败 {code}: {e}")
                    return pd.DataFrame()
            
            def _get_market_prefix(self, code):
                """获取市场前缀"""
                if code.startswith(('600', '601', '603', '688')):
                    return 'sh'
                elif code.startswith(('000', '001', '002', '300')):
                    return 'sz'
                else:
                    return 'sh'
        
        enhanced_stock_fetcher = BackupDataFetcher()
        print("✅ 备用数据获取器初始化成功")
        
    except ImportError:
        enhanced_stock_fetcher = None
        print("❌ 所有数据获取器都不可用")

# Windows通知功能
try:
    import win10toast
    toaster = win10toast.ToastNotifier()
    NOTIFICATION_AVAILABLE = True
    print("✅ Windows通知功能可用")
except ImportError:
    NOTIFICATION_AVAILABLE = False
    print("⚠️ Windows通知不可用，请安装: pip install win10toast")


class PriceTrajectoryTracker:
    """价格轨迹跟踪器 - 记忆股票历史状态"""

    def __init__(self, max_history=30):
        self.price_history = []      # 价格历史
        self.gain_history = []       # 涨幅历史
        self.volume_history = []     # 量比历史
        self.time_history = []       # 时间历史
        self.max_history = max_history  # 最大历史记录数（约7.5分钟数据）

    def add_data_point(self, timestamp, price, gain, volume):
        """添加新的数据点"""
        self.price_history.append(price)
        self.gain_history.append(gain)
        self.volume_history.append(volume)
        self.time_history.append(timestamp)

        # 保持历史记录在合理范围内
        if len(self.price_history) > self.max_history:
            self.price_history.pop(0)
            self.gain_history.pop(0)
            self.volume_history.pop(0)
            self.time_history.pop(0)

    def get_stock_state(self):
        """分析股票当前状态"""
        if len(self.gain_history) < 5:
            return "数据不足", {}

        current_gain = self.gain_history[-1]

        # 判断各种状态
        if self.is_v_shaped_reversal():
            low_point = min(self.gain_history[-15:]) if len(self.gain_history) >= 15 else min(self.gain_history)
            return "V字反转", {
                'low_point': low_point,
                'current_gain': current_gain,
                'reversal_strength': current_gain - low_point
            }

        elif self.is_sideways_trading():
            duration = self.get_sideways_duration()
            avg_gain = sum(self.gain_history[-10:]) / len(self.gain_history[-10:])
            return "横盘状态", {
                'avg_gain': avg_gain,
                'duration_points': duration,
                'current_gain': current_gain
            }

        elif self.is_continuous_uptrend():
            start_gain = self.gain_history[-10] if len(self.gain_history) >= 10 else self.gain_history[0]
            return "持续上涨", {
                'start_gain': start_gain,
                'current_gain': current_gain,
                'total_rise': current_gain - start_gain
            }

        elif self.is_pullback_from_high():
            high_point = max(self.gain_history[-15:]) if len(self.gain_history) >= 15 else max(self.gain_history)
            return "冲高回落", {
                'high_point': high_point,
                'current_gain': current_gain,
                'pullback_amount': high_point - current_gain
            }

        else:
            return "普通状态", {'current_gain': current_gain}

    def is_v_shaped_reversal(self):
        """判断是否V字反转"""
        if len(self.gain_history) < 10:
            return False

        # 检查最近15个点（如果有的话）
        check_length = min(15, len(self.gain_history))
        recent_gains = self.gain_history[-check_length:]

        # 找到最低点
        min_gain = min(recent_gains)
        min_index = recent_gains.index(min_gain)

        # V字特征：
        # 1. 最低点不在最后几个点（已经开始反转）
        # 2. 最低点是负数或很小的正数
        # 3. 从最低点反转幅度足够大
        if (min_index < check_length - 3 and  # 最低点不在最后3个点
            min_gain < 1.0 and  # 最低点涨幅小于1%
            self.gain_history[-1] > min_gain + 1.5):  # 反转幅度超过1.5%
            return True

        return False

    def is_sideways_trading(self, threshold=0.8):
        """判断是否在横盘"""
        if len(self.gain_history) < 8:
            return False

        # 检查最近8个点的波动范围
        recent_gains = self.gain_history[-8:]
        max_gain = max(recent_gains)
        min_gain = min(recent_gains)

        # 横盘特征：波动范围小于阈值
        return (max_gain - min_gain) <= threshold

    def get_sideways_duration(self):
        """获取横盘持续时间（数据点数）"""
        if not self.is_sideways_trading():
            return 0

        # 从后往前找，直到找到不符合横盘条件的点
        duration = 0
        for i in range(len(self.gain_history) - 1, 0, -1):
            if i < 8:
                break

            check_gains = self.gain_history[i-7:i+1]  # 检查8个点
            if max(check_gains) - min(check_gains) <= 0.8:
                duration += 1
            else:
                break

        return duration

    def is_continuous_uptrend(self):
        """判断是否持续上涨"""
        if len(self.gain_history) < 8:
            return False

        recent_gains = self.gain_history[-8:]

        # 检查上涨趋势：大部分时间在上涨
        uptrend_count = 0
        for i in range(1, len(recent_gains)):
            if recent_gains[i] >= recent_gains[i-1] - 0.3:  # 允许小幅回调
                uptrend_count += 1

        # 至少70%的时间在上涨，且总体涨幅为正
        return (uptrend_count >= 5 and
                recent_gains[-1] > recent_gains[0] + 0.5)

    def is_pullback_from_high(self):
        """判断是否冲高回落"""
        if len(self.gain_history) < 10:
            return False

        # 检查最近15个点中是否有明显高点
        check_length = min(15, len(self.gain_history))
        recent_gains = self.gain_history[-check_length:]

        max_gain = max(recent_gains)
        current_gain = self.gain_history[-1]

        # 冲高回落特征：曾经冲到高点，现在明显回落
        return (max_gain > current_gain + 1.2 and  # 从高点回落超过1.2%
                max_gain > 3.0)  # 高点至少达到3%

    def get_state_description(self):
        """获取状态的详细描述"""
        state, details = self.get_stock_state()

        if state == "数据不足":
            return "数据不足: 等待更多数据点"
        elif state == "V字反转":
            return f"V字反转: 从{details['low_point']:.1f}%反转到{details['current_gain']:.1f}% (反转{details['reversal_strength']:.1f}%)"
        elif state == "横盘状态":
            return f"横盘状态: {details['avg_gain']:.1f}%附近横盘{details['duration_points']}个点"
        elif state == "持续上涨":
            return f"持续上涨: 从{details['start_gain']:.1f}%涨到{details['current_gain']:.1f}% (累计{details['total_rise']:.1f}%)"
        elif state == "冲高回落":
            return f"冲高回落: 从{details['high_point']:.1f}%回落到{details['current_gain']:.1f}% (回落{details['pullback_amount']:.1f}%)"
        else:
            current_gain = details.get('current_gain', 0)
            return f"普通状态: 当前{current_gain:.1f}%"


class ContinuousConfirmationSystem:
    """连续确认系统 - 基于股票状态的动态确认"""

    def __init__(self, required_confirmations=2):
        self.required_confirmations = required_confirmations
        self.confirmation_queue = []

    def add_signal(self, timestamp, signal_data, algorithm_info):
        """添加信号到确认队列（基于算法类型调整确认要求）"""
        confirmation_entry = {
            'timestamp': timestamp,
            'buy_signal': signal_data.get('buy_signal', False),
            'signal_strength': signal_data.get('signal_strength', 0),
            'algorithm_type': algorithm_info.get('algorithm_type', ''),
            'algorithm_confidence': algorithm_info.get('confidence', 0),
            'current_gain': signal_data.get('current_gain', 0),
            'volume_ratio': signal_data.get('volume_ratio', 0),
            'reason': signal_data.get('reason', ''),
            'stock_state': algorithm_info.get('stock_state', '普通状态')
        }

        self.confirmation_queue.append(confirmation_entry)

        # 根据算法类型调整确认要求
        algorithm_type = algorithm_info.get('algorithm_type', '')
        stock_state = algorithm_info.get('stock_state', '普通状态')

        # 动态调整确认次数
        if algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
            # V字反转：降低确认要求，机会稍纵即逝
            required_confirmations = 1
        elif stock_state == "横盘状态":
            # 横盘突破：中等确认要求
            required_confirmations = 2
        elif algorithm_type in ['trend_following', 'momentum_continuation']:
            # 趋势跟随：标准确认要求
            required_confirmations = 2
        else:
            # 其他情况：标准确认要求
            required_confirmations = 2

        # 保持队列长度
        if len(self.confirmation_queue) > required_confirmations:
            self.confirmation_queue.pop(0)

        # 检查是否满足连续确认
        if len(self.confirmation_queue) >= required_confirmations:
            return self.check_continuous_confirmation()

        return False, f"等待更多确认 ({len(self.confirmation_queue)}/{required_confirmations})"

    def check_continuous_confirmation(self):
        """检查连续确认条件"""
        if len(self.confirmation_queue) < 1:
            return False, "确认队列为空"

        try:
            # 1. 所有信号必须为买入信号
            buy_signals = [entry['buy_signal'] for entry in self.confirmation_queue]
            if not all(buy_signals):
                return False, f"存在非买入信号: {buy_signals}"

            # 2. 信号强度必须稳定或递增（根据算法类型调整要求）
            strengths = [entry['signal_strength'] for entry in self.confirmation_queue]
            min_strength = min(strengths)

            # 根据算法类型调整信号强度要求
            latest_entry = self.confirmation_queue[-1]
            algorithm_type = latest_entry['algorithm_type']
            stock_state = latest_entry.get('stock_state', '普通状态')

            if algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
                # V字反转：最低信号强度要求（机会稍纵即逝）
                min_required_strength = 0.55
            elif stock_state == "横盘状态":
                # 横盘突破：最低信号强度要求（突破确认重要）
                min_required_strength = 0.55
            elif algorithm_type in ['trend_following', 'momentum_continuation']:
                # 趋势跟随：降低信号强度要求
                min_required_strength = 0.60
            else:
                # 其他情况：标准信号强度要求
                min_required_strength = 0.65

            if min_strength < min_required_strength:
                return False, f"信号强度不足: 最低{min_strength:.2f} < {min_required_strength:.2f}"

            # 3. 算法置信度必须稳定（根据算法类型调整）
            confidences = [entry['algorithm_confidence'] for entry in self.confirmation_queue]
            min_confidence = min(confidences)

            if algorithm_type in ['v_reversal_momentum', 'v_reversal_early']:
                # V字反转：保持高置信度要求
                min_required_confidence = 0.85
            else:
                # 其他情况：标准置信度要求
                min_required_confidence = 0.8

            if min_confidence < min_required_confidence:
                return False, f"算法置信度不足: 最低{min_confidence:.2f} < {min_required_confidence:.2f}"

            # 4. 时间连续性检查（可选）
            timestamps = [entry['timestamp'] for entry in self.confirmation_queue]
            # 这里可以添加时间间隔检查逻辑

            return True, f"连续确认成功: 强度{min_strength:.2f}, 置信度{min_confidence:.2f}"

        except Exception as e:
            return False, f"确认检查失败: {e}"

    def reset(self):
        """重置确认队列"""
        self.confirmation_queue.clear()


class LimitUpPotentialMonitor:
    """连板潜力股实时监控 - 全时段涨停捕捉系统"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚀 连板潜力股实时监控 - 全时段涨停捕捉系统")
        self.root.geometry("1400x900")
        self.root.configure(bg='#f0f0f0')
        
        # 设置全局字体
        self.setup_fonts()
        
        # 监控配置
        self.config = {
            'scan_interval': 30,                    # 扫描间隔（秒）
            'key_time_scan_interval': 15,           # 关键时间窗口扫描间隔（秒）
            'flat_open_threshold': 1.0,             # 平开股阈值（±1%）
            'high_open_min': 2.0,                   # 高开股最小阈值（+2%）
            'high_open_max': 6.0,                   # 高开股最大阈值（+6%）
            'volume_spike_threshold': 3.0,          # 买一量比突增阈值（300%）
            'depth_change_threshold': 0.5,          # 买五厚度变化率阈值（50%）
            'volume_ratio_threshold': 3.0,          # 量比持续阈值（3.0）
            'cancel_rate_threshold': 0.4,           # 撤单率阈值（40%）
            'notification_duration': 30,            # 通知持续时间（秒）
        }
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.monitored_stocks = {}  # {code: {name, data, signals, decision}}
        self.realtime_data_cache = {}  # 实时数据缓存
        
        # 数据分层管理
        self.historical_base_data = {}  # 历史基础数据（一次性加载）
        self.realtime_data_cache = {}   # 实时数据缓存（每30秒更新）
        self.continuity_cache = {}      # 持续性监控缓存（撤单行为等）

        # 数据管理系统
        self.data_cache_dir = Path("historical_data_cache")
        self.data_cache_dir.mkdir(exist_ok=True)

        # 实时数据保存系统
        self.realtime_data_dir = Path("realtime_data")
        self.realtime_data_dir.mkdir(exist_ok=True)

        # 当日数据保存目录（按日期分文件夹）
        today_str = datetime.now().strftime('%Y%m%d')
        self.today_data_dir = self.realtime_data_dir / today_str
        self.today_data_dir.mkdir(exist_ok=True)

        # 早盘关键数据存储（9:30-10:00）
        self.morning_key_data = {}  # {code: {max_volume_ratio, min_price, avg_line_status, etc.}}

        # 实时数据序列存储
        self.realtime_data_series = {}  # {code: []}

        # 🚀 新增：价格轨迹记忆系统
        self.price_trackers = {}  # {code: PriceTrajectoryTracker}

        # 🚀 新增：连续确认系统
        self.confirmation_systems = {}  # {code: ContinuousConfirmationSystem}

        # 🚀 新增：涨停股票通知记录（避免重复通知）
        self.limit_up_notified = set()  # 已通知的涨停股票代码

        # 时段状态管理
        self.current_time_period = None
        self.time_period_strategies = self.initialize_time_period_strategies()

        # 创建GUI界面
        self.create_gui()

        # 加载配置
        self.load_config()

        # 自动检查和下载历史数据
        self.auto_check_and_download_historical_data()

        # 🚀 新增：初始化专业系统
        self.initialize_professional_systems()

        print("✅ 连板潜力股实时监控初始化完成")

    def initialize_professional_systems(self):
        """初始化专业系统"""
        try:
            # 初始化连续确认系统
            self.confirmation_system = ContinuousConfirmationSystem(required_confirmations=2)

            # 每日重置涨停通知记录
            self.reset_daily_limit_up_notifications()

            print("✅ 专业系统初始化完成")
        except Exception as e:
            print(f"⚠️ 专业系统初始化失败: {e}")

    def reset_daily_limit_up_notifications(self):
        """每日重置涨停股票通知记录"""
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')
            if not hasattr(self, 'last_reset_date') or self.last_reset_date != current_date:
                self.limit_up_notified.clear()
                self.last_reset_date = current_date
                print(f"✅ 涨停通知记录已重置 ({current_date})")
        except Exception as e:
            print(f"⚠️ 重置涨停通知记录失败: {e}")

    def setup_fonts(self):
        """设置全局字体"""
        try:
            # 设置默认字体为微软雅黑
            default_font = ('Microsoft YaHei', 9)
            self.root.option_add('*Font', default_font)

            # 设置ttk样式
            style = ttk.Style()

            # 配置各种控件的字体
            style.configure('TLabel', font=('Microsoft YaHei', 9))
            style.configure('TButton', font=('Microsoft YaHei', 9))
            style.configure('TEntry', font=('Microsoft YaHei', 9))
            style.configure('TCombobox', font=('Microsoft YaHei', 9))
            style.configure('Treeview', font=('Microsoft YaHei', 9))
            style.configure('Treeview.Heading', font=('Microsoft YaHei', 9, 'bold'))

            # 设置表格行高，适应中文字体
            style.configure('Treeview', rowheight=30)

            # 优化表格外观
            style.configure('Treeview',
                          background='white',
                          foreground='black',
                          fieldbackground='white',
                          borderwidth=1,
                          relief='solid')

            # 优化表格标题外观
            style.configure('Treeview.Heading',
                          background='#e1e1e1',
                          foreground='black',
                          borderwidth=1,
                          relief='raised')

            # 优化按钮外观
            style.configure('TButton',
                          padding=(10, 5),
                          relief='raised',
                          borderwidth=1)

            print("✅ 字体设置完成")

        except Exception as e:
            print(f"⚠️ 字体设置失败: {e}")

    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)

        # 标题
        title_label = ttk.Label(main_frame, text="🚀 连板潜力股实时监控",
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))

        # 控制按钮区域
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))

        # 添加股票按钮
        self.add_stock_btn = ttk.Button(control_frame, text="➕ 添加股票",
                                       command=self.add_stocks_dialog)
        self.add_stock_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 删除股票按钮
        self.remove_stock_btn = ttk.Button(control_frame, text="➖ 删除股票",
                                          command=self.remove_selected_stock)
        self.remove_stock_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 🚀 新增：清空全部按钮
        self.clear_all_btn = ttk.Button(control_frame, text="🗑️ 清空全部",
                                       command=self.clear_all_stocks)
        self.clear_all_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 开始/停止监控按钮
        self.monitor_btn = ttk.Button(control_frame, text="🚀 开始监控",
                                     command=self.toggle_monitoring)
        self.monitor_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 设置按钮
        self.settings_btn = ttk.Button(control_frame, text="⚙️ 设置",
                                      command=self.show_settings)
        self.settings_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 立即分析按钮
        self.analyze_now_btn = ttk.Button(control_frame, text="🔍 立即分析",
                                         command=self.analyze_now)
        self.analyze_now_btn.pack(side=tk.LEFT, padx=(0, 10))



        # 重新初始化按钮
        self.reinit_btn = ttk.Button(control_frame, text="🔄 重新初始化",
                                    command=self.reinitialize_all_stocks)
        self.reinit_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 监控状态标签
        self.status_label = ttk.Label(control_frame, text="⏸️ 监控已停止",
                                     font=('Microsoft YaHei', 10, 'bold'))
        self.status_label.pack(side=tk.RIGHT)

        # 创建股票列表表格（连板监控专门显示）
        self.create_stock_table(main_frame)

        # 底部状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.info_label = ttk.Label(status_frame, text="💡 请添加股票代码开始连板监控")
        self.info_label.pack(side=tk.LEFT)

        self.time_label = ttk.Label(status_frame, text="")
        self.time_label.pack(side=tk.RIGHT)

        # 更新时间显示
        self.update_time_display()

    def create_stock_table(self, parent):
        """创建股票列表表格（连板监控专门显示）"""
        # 表格框架
        table_frame = ttk.Frame(parent)
        table_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))
        table_frame.columnconfigure(0, weight=1)
        table_frame.rowconfigure(0, weight=1)

        # 🚀 修复：定义新的界面列结构
        columns = ('股票代码', '股票名称', '实时涨跌', '开盘类型', '量能突击', '盘口异动', '形态突破',
                  '综合信号', '买入建议', '当前价格', '更新时间')

        # 创建表格
        self.tree = ttk.Treeview(table_frame, columns=columns, show='headings', height=15)
        self.tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 🚀 修复：调整列标题和宽度
        column_widths = {
            '股票代码': 80, '股票名称': 100, '实时涨跌': 80, '开盘类型': 80,
            '量能突击': 80, '盘口异动': 80, '形态突破': 80, '综合信号': 80,
            '买入建议': 100, '当前价格': 80, '更新时间': 120
        }

        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100))

        # 添加滚动条
        scrollbar = ttk.Scrollbar(table_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)

        # 绑定右键菜单
        self.tree.bind("<Button-3>", self.show_context_menu)  # Windows右键
        self.tree.bind("<Control-Button-1>", self.show_context_menu)  # Mac右键

        # 创建右键菜单
        self.context_menu = tk.Menu(self.root, tearoff=0)
        self.context_menu.add_command(label="📈 已买入", command=self.mark_as_bought)
        self.context_menu.add_command(label="📊 查看详情", command=self.show_stock_details)
        self.context_menu.add_separator()
        self.context_menu.add_command(label="🗑️ 删除股票", command=self.remove_selected_stock)

    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"当前时间: {current_time}")
        self.root.after(1000, self.update_time_display)

    # ==================== 基础功能方法 ====================

    def add_stocks_dialog(self):
        """添加股票对话框"""
        print("➕ 添加股票功能待实现")
        messagebox.showinfo("提示", "添加股票功能待实现")

    def remove_selected_stock(self):
        """删除选中的股票"""
        print("➖ 删除股票功能待实现")
        messagebox.showinfo("提示", "删除股票功能待实现")

    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """开始监控"""
        if not self.monitored_stocks:
            messagebox.showwarning("警告", "请先添加股票到监控列表")
            return

        self.monitoring = True
        self.monitor_btn.config(text="⏸️ 停止监控")
        self.status_label.config(text="🚀 监控运行中")
        self.info_label.config(text=f"💡 正在监控 {len(self.monitored_stocks)} 只股票")

        # 更新已买入股票的持有天数
        if bought_stocks_manager:
            try:
                bought_stocks_manager.update_hold_days()
                holding_stocks = bought_stocks_manager.get_holding_stocks()
                if holding_stocks:
                    print(f"📈 当前持仓股票: {len(holding_stocks)} 只")
                    for code, info in holding_stocks.items():
                        print(f"   💰 {code} {info['name']} - 持有{info['hold_days']}天")

                        # 🚀 修复：启动时更新已买入股票的显示状态
                        if code in self.monitored_stocks:
                            self.update_stock_bought_status(code)

                    # 🚀 启动时将已买入股票置顶
                    self._move_bought_stocks_to_top()
            except Exception as e:
                print(f"⚠️ 更新持有天数失败: {e}")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()

        print("🚀 开始连板潜力股监控")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.monitor_btn.config(text="🚀 开始监控")
        self.status_label.config(text="⏸️ 监控已停止")
        self.info_label.config(text="💡 监控已停止")
        print("⏸️ 停止连板潜力股监控")

    def monitor_loop(self):
        """监控主循环"""
        print("🔍 连板潜力股监控循环启动")
        # 这里将实现完整的监控逻辑
        pass

    def show_settings(self):
        """显示设置"""
        print("⚙️ 设置功能待实现")
        messagebox.showinfo("提示", "设置功能待实现")

    def show_context_menu(self, event):
        """显示右键菜单"""
        try:
            # 获取点击的项目
            item = self.tree.identify_row(event.y)
            if item:
                # 选中该项目
                self.tree.selection_set(item)

                # 获取股票信息
                code = item
                if code in self.monitored_stocks:
                    stock_name = self.monitored_stocks[code]['name']

                    # 检查是否已买入
                    if bought_stocks_manager and bought_stocks_manager.is_bought_stock(code):
                        # 已买入股票，显示不同的菜单
                        self.context_menu.delete(0, tk.END)
                        self.context_menu.add_command(label=f"📈 {stock_name} (已买入)", state='disabled')
                        self.context_menu.add_separator()
                        self.context_menu.add_command(label="💰 标记已卖出", command=self.mark_as_sold)
                        self.context_menu.add_command(label="📊 查看详情", command=self.show_stock_details)
                        self.context_menu.add_separator()
                        self.context_menu.add_command(label="🗑️ 删除股票", command=self.remove_selected_stock)
                    else:
                        # 普通股票菜单
                        self.context_menu.delete(0, tk.END)
                        self.context_menu.add_command(label=f"📊 {stock_name}", state='disabled')
                        self.context_menu.add_separator()
                        self.context_menu.add_command(label="📈 标记已买入", command=self.mark_as_bought)
                        self.context_menu.add_command(label="📊 查看详情", command=self.show_stock_details)
                        self.context_menu.add_separator()
                        self.context_menu.add_command(label="🗑️ 删除股票", command=self.remove_selected_stock)

                    # 显示菜单
                    self.context_menu.post(event.x_root, event.y_root)

        except Exception as e:
            print(f"⚠️ 显示右键菜单失败: {e}")

    def mark_as_bought(self):
        """标记股票为已买入"""
        try:
            selected_items = self.tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择股票")
                return

            if not bought_stocks_manager:
                messagebox.showerror("错误", "已买入股票管理器不可用")
                return

            code = selected_items[0]
            if code not in self.monitored_stocks:
                messagebox.showerror("错误", "股票信息不存在")
                return

            stock_name = self.monitored_stocks[code]['name']

            # 检查是否已经买入
            if bought_stocks_manager.is_bought_stock(code):
                messagebox.showinfo("提示", f"{stock_name} 已在已买入列表中")
                return

            # 获取当前价格（可选）
            current_price = None
            try:
                # 从实时数据中获取当前价格
                if code in self.realtime_data_cache:
                    current_price = self.realtime_data_cache[code].get('current_price')
            except:
                pass

            # 添加到已买入列表
            if bought_stocks_manager.add_bought_stock(code, stock_name, current_price):
                messagebox.showinfo("成功", f"✅ 已将 {stock_name} 标记为已买入")
                print(f"📈 标记已买入: {code} {stock_name}")

                # 🚀 修复：立即更新显示状态和置顶
                self.update_stock_bought_status(code)
                # 🚀 刷新整个界面以确保置顶生效
                self._move_bought_stocks_to_top()
            else:
                messagebox.showerror("错误", f"标记 {stock_name} 为已买入失败")

        except Exception as e:
            print(f"⚠️ 标记已买入失败: {e}")
            messagebox.showerror("错误", f"标记已买入失败: {e}")

    def mark_as_sold(self):
        """标记股票为已卖出"""
        try:
            selected_items = self.tree.selection()
            if not selected_items:
                messagebox.showwarning("警告", "请先选择股票")
                return

            if not bought_stocks_manager:
                messagebox.showerror("错误", "已买入股票管理器不可用")
                return

            code = selected_items[0]
            if code not in self.monitored_stocks:
                messagebox.showerror("错误", "股票信息不存在")
                return

            stock_name = self.monitored_stocks[code]['name']

            # 检查是否在已买入列表中
            if not bought_stocks_manager.is_bought_stock(code):
                messagebox.showinfo("提示", f"{stock_name} 不在已买入列表中")
                return

            # 创建卖出对话框
            self.show_sell_dialog(code, stock_name)

        except Exception as e:
            print(f"⚠️ 标记已卖出失败: {e}")
            messagebox.showerror("错误", f"标记已卖出失败: {e}")

    def show_sell_dialog(self, code, stock_name):
        """显示卖出对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title(f"卖出 {stock_name}")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 股票信息
        info_frame = ttk.LabelFrame(dialog, text="股票信息", padding="10")
        info_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(info_frame, text=f"股票代码: {code}").pack(anchor=tk.W)
        ttk.Label(info_frame, text=f"股票名称: {stock_name}").pack(anchor=tk.W)

        # 获取买入信息
        stock_info = bought_stocks_manager.get_stock_info(code)
        if stock_info:
            ttk.Label(info_frame, text=f"买入日期: {stock_info['buy_date']}").pack(anchor=tk.W)
            if stock_info['buy_price']:
                ttk.Label(info_frame, text=f"买入价格: {stock_info['buy_price']:.2f}").pack(anchor=tk.W)
            ttk.Label(info_frame, text=f"持有天数: {stock_info['hold_days']}").pack(anchor=tk.W)

        # 卖出价格输入
        price_frame = ttk.LabelFrame(dialog, text="卖出信息", padding="10")
        price_frame.pack(fill=tk.X, padx=10, pady=5)

        ttk.Label(price_frame, text="卖出价格:").pack(anchor=tk.W)
        price_var = tk.StringVar()
        price_entry = ttk.Entry(price_frame, textvariable=price_var, width=20)
        price_entry.pack(anchor=tk.W, pady=2)

        # 获取当前价格作为默认值
        try:
            if code in self.realtime_data_cache:
                current_price = self.realtime_data_cache[code].get('current_price')
                if current_price:
                    price_var.set(str(current_price))
        except:
            pass

        # 备注输入
        ttk.Label(price_frame, text="备注:").pack(anchor=tk.W, pady=(10, 0))
        notes_var = tk.StringVar()
        notes_entry = ttk.Entry(price_frame, textvariable=notes_var, width=40)
        notes_entry.pack(anchor=tk.W, pady=2)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def confirm_sell():
            try:
                sell_price = None
                if price_var.get().strip():
                    sell_price = float(price_var.get())

                notes = notes_var.get().strip()

                if bought_stocks_manager.mark_as_sold(code, sell_price, notes):
                    messagebox.showinfo("成功", f"✅ 已将 {stock_name} 标记为已卖出")
                    print(f"💰 标记已卖出: {code} {stock_name}")

                    # 更新显示
                    self.update_stock_bought_status(code)
                    dialog.destroy()
                else:
                    messagebox.showerror("错误", f"标记 {stock_name} 为已卖出失败")

            except ValueError:
                messagebox.showerror("错误", "请输入有效的卖出价格")
            except Exception as e:
                messagebox.showerror("错误", f"卖出操作失败: {e}")

        ttk.Button(button_frame, text="确认卖出", command=confirm_sell).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

    def show_stock_details(self):
        """显示股票详情"""
        selected_items = self.tree.selection()
        if not selected_items:
            messagebox.showwarning("警告", "请先选择股票")
            return

        code = selected_items[0]
        if code not in self.monitored_stocks:
            messagebox.showerror("错误", "股票信息不存在")
            return

        stock_name = self.monitored_stocks[code]['name']

        # 创建详情对话框
        dialog = tk.Toplevel(self.root)
        dialog.title(f"股票详情 - {stock_name}")
        dialog.geometry("600x400")
        dialog.transient(self.root)

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # 创建文本框显示详情
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        text_widget = tk.Text(text_frame, wrap=tk.WORD)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 显示股票信息
        details = f"股票代码: {code}\n"
        details += f"股票名称: {stock_name}\n"
        details += f"添加时间: {self.monitored_stocks[code].get('added_time', '未知')}\n\n"

        # 显示已买入信息
        if bought_stocks_manager and bought_stocks_manager.is_bought_stock(code):
            stock_info = bought_stocks_manager.get_stock_info(code)
            details += "=== 已买入信息 ===\n"
            details += f"买入日期: {stock_info['buy_date']}\n"
            details += f"买入时间: {stock_info['buy_time']}\n"
            if stock_info['buy_price']:
                details += f"买入价格: {stock_info['buy_price']:.2f}\n"
            details += f"持有天数: {stock_info['hold_days']}\n"
            details += f"状态: {'持有中' if stock_info['status'] == 'holding' else '已卖出'}\n"

            if stock_info['status'] == 'sold':
                details += f"卖出日期: {stock_info['sell_date']}\n"
                if stock_info['sell_price']:
                    details += f"卖出价格: {stock_info['sell_price']:.2f}\n"
                if stock_info.get('profit_loss_pct'):
                    details += f"盈亏: {stock_info['profit_loss_pct']:+.2f}%\n"
            details += "\n"

        # 显示实时数据
        if code in self.realtime_data_cache:
            realtime_data = self.realtime_data_cache[code]
            details += "=== 实时数据 ===\n"
            details += f"当前价格: {realtime_data.get('current_price', 0):.2f}\n"
            details += f"涨跌幅: {realtime_data.get('change_pct', 0):+.2f}%\n"
            details += f"成交量: {realtime_data.get('volume', 0)}\n"
            details += f"更新时间: {realtime_data.get('update_time', '未知')}\n"

        text_widget.insert(tk.END, details)
        text_widget.config(state=tk.DISABLED)

        # 关闭按钮
        ttk.Button(dialog, text="关闭", command=dialog.destroy).pack(pady=10)

    def update_stock_bought_status(self, code):
        """更新股票的已买入状态显示"""
        try:
            if not self.tree.exists(code):
                return

            # 🚀 修复：添加已买入股票的蓝色标识和置顶
            if bought_stocks_manager and bought_stocks_manager.is_bought_stock(code):
                stock_name = self.monitored_stocks[code]['name']

                # 获取当前行数据
                current_values = list(self.tree.item(code, 'values'))
                if current_values:
                    # 在股票名称前添加蓝色标识
                    current_values[1] = f"💙 {stock_name} (已买入)"

                    # 更新行数据
                    self.tree.item(code, values=current_values)

                print(f"📈 {code} {stock_name} 已标记为已买入状态（蓝色显示）")

                # 🚀 置顶显示：将已买入股票移动到顶部
                self._move_bought_stocks_to_top()

        except Exception as e:
            print(f"⚠️ 更新已买入状态显示失败 {code}: {e}")

    def _move_bought_stocks_to_top(self):
        """将已买入股票移动到表格顶部"""
        try:
            if not bought_stocks_manager:
                return

            # 获取所有已买入股票
            bought_stocks = bought_stocks_manager.get_all_bought_stocks()

            # 按买入时间排序（最新买入的在最上面）
            sorted_bought = sorted(
                bought_stocks.items(),
                key=lambda x: x[1].get('buy_datetime', ''),
                reverse=True
            )

            # 将已买入股票移动到顶部
            for i, (code, stock_info) in enumerate(sorted_bought):
                if self.tree.exists(code):
                    self.tree.move(code, '', i)

            print(f"📌 已将 {len(sorted_bought)} 只已买入股票置顶显示")

        except Exception as e:
            print(f"⚠️ 置顶已买入股票失败: {e}")

    def _record_decision_to_memory(self, stock_code: str, sell_decision: Dict, hold_days: int) -> bool:
        """
        记录决策到记忆系统

        Args:
            stock_code: 股票代码
            sell_decision: 卖出决策结果
            hold_days: 持有天数

        Returns:
            bool: 记录是否成功
        """
        try:
            # 导入记忆系统
            from bought_stocks_memory_system import bought_stocks_memory

            # 记录决策
            bought_stocks_memory.record_decision(
                stock_code=stock_code,
                decision=sell_decision.get('final_decision', '未知'),
                action=sell_decision.get('action', 'watch'),
                confidence=sell_decision.get('confidence', '未知'),
                reason=sell_decision.get('reason', ''),
                hold_days=hold_days
            )

            return True

        except ImportError:
            # 记忆系统不可用
            return False
        except Exception as e:
            print(f"⚠️ 记录决策到记忆系统失败: {e}")
            return False

    def _get_memory_display_info(self, stock_code: str, hold_days: int) -> str:
        """
        获取记忆系统显示信息

        Args:
            stock_code: 股票代码
            hold_days: 持有天数

        Returns:
            str: 显示信息
        """
        try:
            # 导入记忆系统
            from bought_stocks_memory_system import bought_stocks_memory

            info_lines = []

            # 获取多日表现
            multi_day_perf = bought_stocks_memory.get_multi_day_performance(stock_code)
            if 'error' not in multi_day_perf:
                trend = multi_day_perf.get('trend_analysis', {})
                if trend.get('is_continuous_decline', False):
                    info_lines.append("   📉 记忆分析: 连续下跌趋势")
                elif trend.get('is_continuous_rise', False):
                    info_lines.append("   📈 记忆分析: 连续上涨趋势")

                # 显示历史表现
                daily_analysis = multi_day_perf.get('daily_analysis', [])
                if len(daily_analysis) >= 2:
                    recent_performance = []
                    for day_data in daily_analysis[-3:]:  # 最近3天
                        day_num = day_data['hold_days']
                        change = day_data['change_pct']
                        recent_performance.append(f"第{day_num}天{change:+.1f}%")

                    if recent_performance:
                        info_lines.append(f"   📊 历史表现: {' → '.join(recent_performance)}")

            # 获取洗盘概率趋势
            wash_trend = bought_stocks_memory.get_wash_probability_trend(stock_code)
            if 'error' not in wash_trend:
                if wash_trend.get('is_wash_strengthening', False):
                    info_lines.append("   🌊 洗盘趋势: 信心增强")
                elif wash_trend.get('is_wash_weakening', False):
                    info_lines.append("   🌊 洗盘趋势: 信心下降")

            # 特定天数分析
            if hold_days == 2:
                day2_analysis = bought_stocks_memory.analyze_specific_day_performance(stock_code, 2)
                if 'error' not in day2_analysis:
                    day2_data = day2_analysis.get('day2_analysis', {})
                    if day2_data.get('requires_wash_analysis', False):
                        info_lines.append("   ⚠️ 第2天分析: 需要洗盘确认")

            elif hold_days == 3:
                day3_analysis = bought_stocks_memory.analyze_specific_day_performance(stock_code, 3)
                if 'error' not in day3_analysis:
                    day3_data = day3_analysis.get('day3_analysis', {})
                    if day3_data.get('is_continuous_decline', False):
                        info_lines.append("   🚨 第3天分析: 连续下跌警告")
                    elif day3_data.get('is_critical_day', False):
                        info_lines.append("   🎯 第3天分析: 关键决策日")

            return '\n'.join(info_lines) if info_lines else ""

        except ImportError:
            return ""
        except Exception as e:
            return f"   ⚠️ 记忆系统错误: {e}"

    def _get_main_behavior_display_info(self, sell_decision: Dict) -> str:
        """
        获取主力行为分析显示信息

        Args:
            sell_decision: 卖出决策结果

        Returns:
            str: 主力行为显示信息
        """
        try:
            main_behavior = sell_decision.get('main_behavior_analysis', {})
            if 'error' in main_behavior or not main_behavior:
                return ""

            info_lines = []

            # 主力意图
            main_intention = main_behavior.get('main_intention', '方向不明')
            info_lines.append(f"   🎯 主力意图: {main_intention}")

            # 行为评分
            behavior_score = main_behavior.get('behavior_score', 0)
            score_desc = "强势" if behavior_score >= 0.8 else "中性" if behavior_score >= 0.6 else "弱势"
            info_lines.append(f"   📊 行为评分: {behavior_score:.2f} ({score_desc})")

            # 价格位置
            price_position = main_behavior.get('price_position', 'unknown')
            position_desc = {
                'above_cost': '高于成本区',
                'below_cost': '低于成本区',
                'near_cost': '接近成本区',
                'unknown': '位置不明'
            }.get(price_position, '位置不明')
            info_lines.append(f"   💰 价格位置: {position_desc}")

            # 风险等级
            risk_level = main_behavior.get('risk_level', 'medium')
            risk_desc = {
                'low': '低风险',
                'medium': '中等风险',
                'high': '高风险',
                'critical': '极高风险'
            }.get(risk_level, '中等风险')
            risk_icon = "🟢" if risk_level == 'low' else "🟡" if risk_level == 'medium' else "🟠" if risk_level == 'high' else "🔴"
            info_lines.append(f"   {risk_icon} 风险等级: {risk_desc}")

            # 出逃信号
            escape_signals = main_behavior.get('escape_signals', [])
            if escape_signals:
                info_lines.append(f"   🚨 出逃信号: {', '.join(escape_signals[:3])}")
                if len(escape_signals) > 3:
                    info_lines.append(f"   📝 更多信号: +{len(escape_signals) - 3}个")

            # 洗盘信号
            wash_signals = main_behavior.get('wash_signals', [])
            if wash_signals:
                info_lines.append(f"   🌊 洗盘信号: {', '.join(wash_signals[:3])}")
                if len(wash_signals) > 3:
                    info_lines.append(f"   📝 更多信号: +{len(wash_signals) - 3}个")

            # 增强数据
            enhanced_data = main_behavior.get('enhanced_data', {})

            # 板块表现
            sector_data = enhanced_data.get('sector_performance', {})
            if sector_data and 'error' not in sector_data:
                sector_trend = sector_data.get('sector_trend', 'neutral')
                trend_desc = {'bullish': '强势', 'neutral': '中性', 'bearish': '弱势'}.get(sector_trend, '中性')
                avg_change = sector_data.get('avg_change', 0)
                info_lines.append(f"   🏢 板块表现: {trend_desc} ({avg_change:+.1f}%)")

            # 北向资金
            northbound_data = enhanced_data.get('northbound_capital', {})
            if northbound_data and 'error' not in northbound_data:
                trend = northbound_data.get('trend', 'neutral')
                net_inflow = northbound_data.get('net_inflow', 0)
                trend_desc = {
                    'strong_inflow': '大幅流入',
                    'inflow': '净流入',
                    'neutral': '平衡',
                    'outflow': '净流出',
                    'strong_outflow': '大幅流出'
                }.get(trend, '平衡')
                info_lines.append(f"   💸 北向资金: {trend_desc} ({net_inflow:+.1f}亿)")

            return '\n'.join(info_lines) if info_lines else ""

        except Exception as e:
            return f"   ⚠️ 主力行为分析错误: {e}"

    def send_sell_alert(self, code: str, stock_name: str, sell_decision: Dict):
        """发送卖出提醒"""
        try:
            message = f"🚨 卖出提醒\n"
            message += f"股票: {code} {stock_name}\n"
            message += f"建议: {sell_decision.get('final_decision', '立即卖出')}\n"
            message += f"当前价格: {sell_decision.get('current_price', 0):.2f}\n"
            message += f"持仓盈亏: {sell_decision.get('holding_profit_pct', 0):+.2f}%\n"
            message += f"洗盘概率: {sell_decision.get('wash_analysis', {}).get('wash_probability', 0):.1%}"

            print(f"📢 {message}")

            # 发送Windows通知
            try:
                from plyer import notification
                notification.notify(
                    title="连板潜力股监控 - 卖出提醒",
                    message=f"{code} {stock_name}\n{sell_decision.get('final_decision', '立即卖出')}",
                    timeout=30
                )
            except:
                pass

        except Exception as e:
            print(f"⚠️ 发送卖出提醒失败: {e}")

    def send_reduce_alert(self, code: str, stock_name: str, sell_decision: Dict):
        """发送减仓提醒"""
        try:
            message = f"⚠️ 减仓提醒\n"
            message += f"股票: {code} {stock_name}\n"
            message += f"建议: {sell_decision.get('final_decision', '减仓观察')}\n"
            message += f"当前价格: {sell_decision.get('current_price', 0):.2f}\n"
            message += f"持仓盈亏: {sell_decision.get('holding_profit_pct', 0):+.2f}%"

            print(f"📢 {message}")

        except Exception as e:
            print(f"⚠️ 发送减仓提醒失败: {e}")



    def analyze_now(self):
        """
        立即分析所有股票（基于涨停股隔夜挂单分析）

        专门用于分析涨停股的隔夜挂单情况，预测连板概率
        """
        try:
            if not self.monitored_stocks:
                messagebox.showinfo("提示", "请先添加股票进行分析")
                return

            print("🔍 开始涨停股隔夜挂单分析...")
            print("=" * 80)
            print(f"📊 分析股票数量: {len(self.monitored_stocks)}")
            print("🎯 分析目标: 隔夜挂单结构 → 连板概率预测")
            print()

            # 检查当前时间是否适合隔夜挂单分析
            current_time = datetime.now().time()
            is_overnight_period = self.is_overnight_analysis_period(current_time)

            if is_overnight_period:
                print("⏰ 当前处于隔夜挂单分析最佳时段")
            else:
                print("⏰ 当前非隔夜挂单分析时段，将进行实时挂单分析")

            print()

            # 执行隔夜挂单分析
            self.perform_overnight_analysis_round(is_overnight_period)

            messagebox.showinfo("分析完成", f"已完成 {len(self.monitored_stocks)} 只股票的隔夜挂单分析\n请查看控制台输出详情")

        except Exception as e:
            print(f"❌ 隔夜挂单分析失败: {e}")
            messagebox.showerror("错误", f"隔夜挂单分析失败: {e}")

    def perform_single_analysis_round(self):
        """执行一轮完整的分析"""
        try:
            print(f"📊 分析股票数量: {len(self.monitored_stocks)}")

            analysis_results = []

            for code, stock_info in self.monitored_stocks.items():
                print(f"\n🔍 分析 {code} {stock_info['name']}...")
                print("-" * 60)

                try:
                    # 1. 检查历史数据
                    if not self.historical_base_data.get(code):
                        print(f"   📥 加载历史数据...")
                        if not self.load_historical_data_from_cache(code):
                            print(f"   📥 下载历史数据...")
                            if not self.download_single_stock_historical_data(code):
                                print(f"   ❌ 历史数据获取失败，跳过分析")
                                continue

                    # 2. 获取实时数据
                    print(f"   📡 获取实时数据...")
                    stock_data = self.fetch_realtime_stock_data(code)

                    if not stock_data:
                        print(f"   ❌ 实时数据获取失败，跳过分析")
                        continue

                    print(f"      当前价格: {stock_data.get('current_price', 'N/A')}")
                    print(f"      涨跌幅: {stock_data.get('change_pct', 'N/A'):.2f}%")
                    print(f"      买一量: {stock_data.get('bid_volumes', [0])[0]:,.0f}手")
                    print(f"      卖一量: {stock_data.get('ask_volumes', [0])[0]:,.0f}手")

                    # 3. 更新缓存
                    self.update_realtime_data_cache(code, stock_data)

                    # 4. 执行核心算法分析
                    print(f"   🧮 执行算法分析...")

                    # 开盘类型分类
                    open_type_result = self.classify_open_type(stock_data)
                    print(f"      开盘类型: {open_type_result.get('type_name', '未知')}")

                    # 买盘厚度分析
                    bid_depth_result = self.analyze_bid_depth(code, stock_data)
                    print(f"      买盘厚度: {bid_depth_result.get('reason', '未知')}")

                    # 挂单模式分析
                    order_pattern_result = self.analyze_order_pattern(code, stock_data)
                    print(f"      挂单模式: {order_pattern_result.get('pattern_description', '未知')}")

                    # 撤单行为分析
                    cancel_behavior_result = self.analyze_cancel_behavior(code, stock_data)
                    print(f"      撤单行为: {cancel_behavior_result.get('behavior_description', '未知')}")

                    # 5. 信号检测
                    print(f"   🎯 信号检测...")
                    signal_result = self.detect_limit_up_signals(code, stock_data)

                    print(f"      算法类型: {signal_result.get('algorithm_type', '未知')}")
                    print(f"      信号强度: {signal_result.get('signal_strength', 0):.1%}")
                    print(f"      买入信号: {'✅ 是' if signal_result.get('buy_signal', False) else '❌ 否'}")
                    print(f"      操作建议: {signal_result.get('recommendation', '未知')}")
                    print(f"      建议仓位: {signal_result.get('position_size', 0):.1%}")

                    # 6. 更新界面显示
                    self.update_stock_display(code, stock_data, signal_result)

                    # 记录分析结果
                    analysis_results.append({
                        'code': code,
                        'name': stock_info['name'],
                        'signal_strength': signal_result.get('signal_strength', 0),
                        'buy_signal': signal_result.get('buy_signal', False),
                        'recommendation': signal_result.get('recommendation', '未知')
                    })

                    print(f"   ✅ {code} 分析完成")

                except Exception as e:
                    print(f"   ❌ {code} 分析失败: {e}")
                    continue

            # 汇总分析结果
            print("\n" + "=" * 80)
            print("📊 分析结果汇总:")

            buy_signals = [r for r in analysis_results if r['buy_signal']]
            high_potential = [r for r in analysis_results if r['signal_strength'] > 0.3]

            print(f"   📈 总分析股票: {len(analysis_results)} 只")
            print(f"   🚀 买入信号: {len(buy_signals)} 只")
            print(f"   ⭐ 高潜力股票: {len(high_potential)} 只")

            if buy_signals:
                print("\n🚀 买入信号股票:")
                for stock in buy_signals:
                    print(f"   - {stock['code']} {stock['name']}: {stock['signal_strength']:.1%} ({stock['recommendation']})")

            if high_potential:
                print("\n⭐ 高潜力股票:")
                for stock in sorted(high_potential, key=lambda x: x['signal_strength'], reverse=True):
                    print(f"   - {stock['code']} {stock['name']}: {stock['signal_strength']:.1%} ({stock['recommendation']})")

            print("\n🎉 立即分析完成！")

        except Exception as e:
            print(f"❌ 分析执行失败: {e}")
            import traceback
            traceback.print_exc()

    # ==================== 隔夜挂单分析系统 ====================

    def is_overnight_analysis_period(self, current_time):
        """
        判断是否为隔夜挂单分析最佳时段

        Args:
            current_time: 当前时间

        Returns:
            bool: 是否为最佳分析时段
        """
        try:
            # 隔夜挂单分析最佳时段：9:15-9:25
            morning_start = datetime.strptime("09:15:00", "%H:%M:%S").time()
            morning_end = datetime.strptime("09:25:00", "%H:%M:%S").time()

            return morning_start <= current_time <= morning_end

        except Exception:
            return False

    def perform_overnight_analysis_round(self, is_overnight_period):
        """
        执行隔夜挂单分析轮次

        Args:
            is_overnight_period: 是否为隔夜分析时段
        """
        try:
            # 分析结果收集
            limit_up_candidates = []  # 连板候选
            breakout_risks = []       # 破板风险
            analysis_results = []     # 详细分析结果

            for code, stock_info in self.monitored_stocks.items():
                try:
                    stock_name = stock_info['name']
                    print(f"🔍 分析 {code} {stock_name}...")
                    print("-" * 60)

                    # 获取实时挂单数据
                    print("   📡 获取挂单数据...")
                    stock_data = self.fetch_realtime_stock_data(code)
                    if not stock_data:
                        print(f"   ❌ {code} 挂单数据获取失败")
                        continue

                    # 执行隔夜挂单分析
                    print("   🧮 执行隔夜挂单分析...")
                    overnight_analysis = self.analyze_overnight_orders(code, stock_data, stock_name)

                    # 显示分析结果
                    self.display_overnight_analysis_result(code, stock_name, overnight_analysis)

                    # 收集结果
                    analysis_results.append({
                        'code': code,
                        'name': stock_name,
                        'analysis': overnight_analysis
                    })

                    # 根据连板概率分类
                    limit_up_probability = overnight_analysis.get('limit_up_probability', 0)

                    if limit_up_probability >= 75:
                        limit_up_candidates.append({
                            'code': code,
                            'name': stock_name,
                            'probability': limit_up_probability,
                            'strategy': overnight_analysis.get('strategy', ''),
                            'buy_power': overnight_analysis.get('buy_power_score', 0)
                        })
                    elif overnight_analysis.get('breakout_risk', 0) >= 60:
                        breakout_risks.append({
                            'code': code,
                            'name': stock_name,
                            'risk_score': overnight_analysis.get('breakout_risk', 0),
                            'risk_factors': overnight_analysis.get('risk_factors', [])
                        })

                    print(f"   ✅ {code} 分析完成")
                    print()

                except Exception as e:
                    print(f"   ❌ {code} 分析异常: {e}")
                    continue

            # 显示汇总结果
            self.display_overnight_analysis_summary(
                len(self.monitored_stocks),
                limit_up_candidates,
                breakout_risks,
                is_overnight_period
            )

            # 生成操作建议
            self.generate_overnight_trading_suggestions(limit_up_candidates, breakout_risks, is_overnight_period)

        except Exception as e:
            print(f"❌ 隔夜挂单分析轮次失败: {e}")
            import traceback
            traceback.print_exc()

    def analyze_overnight_orders(self, code, stock_data, stock_name):
        """
        隔夜挂单分析核心算法（基于报告的五大信号体系）

        Args:
            code: 股票代码
            stock_data: 股票数据
            stock_name: 股票名称

        Returns:
            dict: 隔夜挂单分析结果
        """
        try:
            # 获取基础数据
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])
            volume = stock_data.get('volume', 0)

            # 计算涨跌幅
            change_pct = ((current_price - prev_close) / prev_close * 100) if prev_close > 0 else 0

            # 获取流通股本（简化处理）
            circulating_shares = self.get_circulating_shares(code)

            # 1. 买盘结构深度分析
            buy_structure = self.analyze_buy_structure(bid_volumes, circulating_shares, volume)

            # 2. 卖盘真空分析
            sell_vacuum = self.analyze_sell_vacuum(ask_volumes, circulating_shares, volume)

            # 3. 量价关系模型
            volume_price_model = self.analyze_volume_price_relationship(bid_volumes, ask_volumes, volume, change_pct)

            # 4. 连板五大信号检测
            limit_up_signals = self.detect_limit_up_five_signals(bid_volumes, ask_volumes, volume, circulating_shares)

            # 5. 破板五大信号检测
            breakout_signals = self.detect_breakout_five_signals(bid_volumes, ask_volumes, volume, change_pct)

            # 6. 主力行为解码
            main_behavior = self.decode_main_behavior(bid_volumes, ask_volumes)

            # 7. 连板概率计算（基于报告公式）
            limit_up_probability = self.calculate_limit_up_probability(
                buy_structure, sell_vacuum, limit_up_signals, main_behavior
            )

            # 8. 破板风险评估
            breakout_risk = self.calculate_breakout_risk(breakout_signals, sell_vacuum, main_behavior)

            # 9. 生成策略建议
            strategy = self.generate_trading_strategy(limit_up_probability, breakout_risk, change_pct)

            return {
                'limit_up_probability': limit_up_probability,
                'breakout_risk': breakout_risk,
                'strategy': strategy,
                'buy_structure': buy_structure,
                'sell_vacuum': sell_vacuum,
                'volume_price_model': volume_price_model,
                'limit_up_signals': limit_up_signals,
                'breakout_signals': breakout_signals,
                'main_behavior': main_behavior,
                'buy_power_score': buy_structure.get('total_score', 0),
                'change_pct': change_pct,
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"⚠️ {code} 隔夜挂单分析失败: {e}")
            return {
                'limit_up_probability': 0,
                'breakout_risk': 100,
                'strategy': '分析失败',
                'error': str(e)
            }

    def get_circulating_shares(self, code):
        """
        获取流通股本（简化处理）

        Args:
            code: 股票代码

        Returns:
            float: 流通股本（万股）
        """
        try:
            # 从历史数据中获取，如果没有则使用默认值
            historical_data = self.historical_base_data.get(code, {})
            circulating_shares = historical_data.get('circulating_shares', 50000)  # 默认5亿股
            return circulating_shares
        except Exception:
            return 50000  # 默认5亿股

    def analyze_buy_structure(self, bid_volumes, circulating_shares, volume):
        """
        买盘结构深度分析（基于报告的买盘结构分析）

        Args:
            bid_volumes: 买盘量
            circulating_shares: 流通股本
            volume: 成交量

        Returns:
            dict: 买盘结构分析结果
        """
        try:
            if not bid_volumes or circulating_shares <= 0:
                return {'total_score': 0, 'analysis': '数据不足'}

            # 买一封单比
            buy1_ratio = (bid_volumes[0] / circulating_shares) if len(bid_volumes) > 0 else 0
            buy1_score = 1.0 if buy1_ratio > 0.03 else (0.6 if buy1_ratio > 0.01 else 0.2)

            # 买五总厚度
            buy5_total = sum(bid_volumes[:5]) if len(bid_volumes) >= 5 else sum(bid_volumes)
            buy5_ratio = buy5_total / circulating_shares
            buy5_score = 1.0 if buy5_ratio > 0.05 else (0.7 if buy5_ratio > 0.02 else 0.3)

            # 机构单密度（简化：大于500手的单子占比）
            big_orders = sum(1 for vol in bid_volumes if vol >= 500)
            institution_ratio = big_orders / len(bid_volumes) if bid_volumes else 0
            institution_score = 1.0 if institution_ratio > 0.4 else (0.6 if institution_ratio > 0.2 else 0.2)

            # 散户单比例（小于50手的单子占比）
            small_orders = sum(1 for vol in bid_volumes if vol < 50)
            retail_ratio = small_orders / len(bid_volumes) if bid_volumes else 0
            retail_score = 0.2 if retail_ratio > 0.7 else (0.6 if retail_ratio > 0.4 else 1.0)

            # 综合评分
            total_score = (buy1_score * 0.3 + buy5_score * 0.3 + institution_score * 0.25 + retail_score * 0.15)

            return {
                'total_score': total_score,
                'buy1_ratio': buy1_ratio,
                'buy5_ratio': buy5_ratio,
                'institution_ratio': institution_ratio,
                'retail_ratio': retail_ratio,
                'analysis': f'买一封单比{buy1_ratio:.2%}, 买五厚度{buy5_ratio:.2%}, 机构单{institution_ratio:.1%}'
            }

        except Exception as e:
            return {'total_score': 0, 'analysis': f'分析失败: {e}'}

    def analyze_sell_vacuum(self, ask_volumes, circulating_shares, volume):
        """
        卖盘真空分析（基于报告的卖盘真空检测）

        Args:
            ask_volumes: 卖盘量
            circulating_shares: 流通股本
            volume: 成交量

        Returns:
            dict: 卖盘真空分析结果
        """
        try:
            if not ask_volumes or circulating_shares <= 0:
                return {'vacuum_score': 1.0, 'analysis': '完全真空'}

            # 卖盘总量
            total_ask = sum(ask_volumes)
            ask_ratio = total_ask / circulating_shares

            # 真空程度评分
            if ask_ratio < 0.001:  # <0.1%
                vacuum_score = 1.0
                analysis = '完全真空'
            elif ask_ratio < 0.005:  # <0.5%
                vacuum_score = 0.8
                analysis = '高度真空'
            elif ask_ratio < 0.01:  # <1%
                vacuum_score = 0.6
                analysis = '中度真空'
            else:
                vacuum_score = 0.2
                analysis = '卖压较重'

            return {
                'vacuum_score': vacuum_score,
                'ask_ratio': ask_ratio,
                'total_ask': total_ask,
                'analysis': analysis
            }

        except Exception as e:
            return {'vacuum_score': 0, 'analysis': f'分析失败: {e}'}

    def analyze_volume_price_relationship(self, bid_volumes, ask_volumes, volume, change_pct):
        """
        量价关系模型（基于报告的开盘动能预测模型）

        Args:
            bid_volumes: 买盘量
            ask_volumes: 卖盘量
            volume: 成交量
            change_pct: 涨跌幅

        Returns:
            dict: 量价关系分析结果
        """
        try:
            # 买盘强度系数
            buy1_vol = bid_volumes[0] if bid_volumes else 0
            buy5_vol = sum(bid_volumes[:5]) if len(bid_volumes) >= 5 else sum(bid_volumes)

            buy_power = min(1.0, buy5_vol / (volume * 0.2)) if volume > 0 else 0

            # 真空系数
            sell_vol = sum(ask_volumes) if ask_volumes else 0
            vacuum_factor = 1.5 if sell_vol < volume * 0.01 else 1.0

            # 开盘溢价率预测
            open_gain = buy_power * vacuum_factor * 0.1

            # 大单加持修正
            big_order_ratio = sum(1 for vol in bid_volumes if vol >= 500) / len(bid_volumes) if bid_volumes else 0
            if big_order_ratio > 0.4:
                open_gain += 0.03

            # 限制最大预测涨幅
            predicted_gain = min(0.099, open_gain)

            return {
                'buy_power': buy_power,
                'vacuum_factor': vacuum_factor,
                'predicted_gain': predicted_gain,
                'big_order_ratio': big_order_ratio,
                'current_gain': change_pct / 100,
                'analysis': f'预测涨幅{predicted_gain:.1%}, 当前{change_pct:.1f}%'
            }

        except Exception as e:
            return {'predicted_gain': 0, 'analysis': f'分析失败: {e}'}

    def detect_limit_up_five_signals(self, bid_volumes, ask_volumes, volume, circulating_shares):
        """
        连板五大信号检测（基于报告的连板信号体系）

        Args:
            bid_volumes: 买盘量
            ask_volumes: 卖盘量
            volume: 成交量
            circulating_shares: 流通股本

        Returns:
            dict: 连板信号检测结果
        """
        try:
            signals = {}

            # 1. 泰山压顶式：买一封单＞昨日成交量20%
            buy1_vol = bid_volumes[0] if bid_volumes else 0
            signals['taishan'] = buy1_vol > volume * 0.2 if volume > 0 else False

            # 2. 铁桶防御式：买二至买五厚度＞买一200%
            if len(bid_volumes) >= 5:
                buy2to5 = sum(bid_volumes[1:5])
                signals['iron_defense'] = buy2to5 > buy1_vol * 2
            else:
                signals['iron_defense'] = False

            # 3. 机构暗码：买三档出现特殊手数（简化检测）
            if len(bid_volumes) >= 3:
                buy3_vol = bid_volumes[2]
                special_codes = [4444, 3333, 6666, 8888, 1111, 2222, 5555, 7777, 9999]
                signals['institution_code'] = any(abs(buy3_vol - code) < 10 for code in special_codes)
            else:
                signals['institution_code'] = False

            # 4. 游资接力：买一由多个500手等额大单组成（简化检测）
            signals['hot_money'] = buy1_vol >= 500 and buy1_vol % 500 == 0

            # 5. 真空加强：卖盘总量＜流通盘0.01%
            total_ask = sum(ask_volumes) if ask_volumes else 0
            signals['vacuum_enhanced'] = total_ask < circulating_shares * 0.0001

            # 计算信号强度
            signal_count = sum(signals.values())
            signal_strength = signal_count / 5.0

            return {
                'signals': signals,
                'signal_count': signal_count,
                'signal_strength': signal_strength,
                'analysis': f'连板信号{signal_count}/5个, 强度{signal_strength:.1%}'
            }

        except Exception as e:
            return {'signal_count': 0, 'signal_strength': 0, 'analysis': f'检测失败: {e}'}

    def detect_breakout_five_signals(self, bid_volumes, ask_volumes, volume, change_pct):
        """
        破板五大信号检测（基于报告的破板风险识别）

        Args:
            bid_volumes: 买盘量
            ask_volumes: 卖盘量
            volume: 成交量
            change_pct: 涨跌幅

        Returns:
            dict: 破板信号检测结果
        """
        try:
            signals = {}

            # 1. 纸老虎式：买一封单快速减少（简化：买一量相对较小）
            buy1_vol = bid_volumes[0] if bid_volumes else 0
            signals['paper_tiger'] = buy1_vol < volume * 0.05 if volume > 0 else True

            # 2. 暗度陈仓：买一薄+卖盘突增
            total_ask = sum(ask_volumes) if ask_volumes else 0
            signals['stealth_selling'] = buy1_vol < 1000 and total_ask > buy1_vol * 2

            # 3. 散户盛宴：买盘多为50手以下小单（简化检测）
            small_orders = sum(1 for vol in bid_volumes if vol < 50) if bid_volumes else 0
            retail_ratio = small_orders / len(bid_volumes) if bid_volumes else 1
            signals['retail_feast'] = retail_ratio > 0.7

            # 4. 诱多陷阱：大单频繁撤挂（简化：检测大单但量不足）
            big_orders = sum(1 for vol in bid_volumes if vol >= 1000) if bid_volumes else 0
            total_big_vol = sum(vol for vol in bid_volumes if vol >= 1000) if bid_volumes else 0
            signals['bull_trap'] = big_orders > 0 and total_big_vol < volume * 0.1

            # 5. 板块拖累：涨幅相对较弱（简化：涨幅<5%）
            signals['sector_drag'] = change_pct < 5.0

            # 计算风险强度
            risk_count = sum(signals.values())
            risk_strength = risk_count / 5.0

            return {
                'signals': signals,
                'risk_count': risk_count,
                'risk_strength': risk_strength,
                'analysis': f'破板风险{risk_count}/5个, 强度{risk_strength:.1%}'
            }

        except Exception as e:
            return {'risk_count': 0, 'risk_strength': 0, 'analysis': f'检测失败: {e}'}

    def decode_main_behavior(self, bid_volumes, ask_volumes):
        """
        主力行为解码（基于报告的主力行为解码表）

        Args:
            bid_volumes: 买盘量
            ask_volumes: 卖盘量

        Returns:
            dict: 主力行为解码结果
        """
        try:
            if not bid_volumes:
                return {'pattern': 'unknown', 'intention': '数据不足', 'confidence': 0}

            buy1_vol = bid_volumes[0]
            buy2to5_vol = sum(bid_volumes[1:5]) if len(bid_volumes) >= 5 else 0
            total_ask = sum(ask_volumes) if ask_volumes else 0

            # 泰山压顶：买一巨单封死
            if buy1_vol > 10000:  # 1万手以上
                return {'pattern': 'taishan', 'intention': '强势连板', 'confidence': 0.9}

            # 铁桶阵：买二至买五加厚
            elif buy2to5_vol > buy1_vol * 2:
                return {'pattern': 'iron_bucket', 'intention': '防御性拉升', 'confidence': 0.8}

            # 钓鱼线：买一反复撤挂（简化：买一量适中但卖盘很厚）
            elif 1000 <= buy1_vol <= 5000 and total_ask > buy1_vol * 3:
                return {'pattern': 'fishing_line', 'intention': '诱多出货', 'confidence': 0.7}

            # 蚂蚁搬家：分散小单堆积
            elif all(vol < 500 for vol in bid_volumes[:3]):
                return {'pattern': 'ant_moving', 'intention': '散户跟风', 'confidence': 0.6}

            # 龙虎斗：大单互吃
            elif buy1_vol > 5000 and total_ask > 5000:
                return {'pattern': 'dragon_tiger', 'intention': '游资接力', 'confidence': 0.75}

            else:
                return {'pattern': 'normal', 'intention': '常规交易', 'confidence': 0.5}

        except Exception as e:
            return {'pattern': 'error', 'intention': f'解码失败: {e}', 'confidence': 0}

    def calculate_limit_up_probability(self, buy_structure, sell_vacuum, limit_up_signals, main_behavior):
        """
        连板概率计算（基于报告的量化预测模型）

        Args:
            buy_structure: 买盘结构分析
            sell_vacuum: 卖盘真空分析
            limit_up_signals: 连板信号
            main_behavior: 主力行为

        Returns:
            float: 连板概率（0-100）
        """
        try:
            # 基于报告公式：P = 0.4×买盘结构 + 0.3×真空系数 + 0.2×信号强度 + 0.1×主力意图
            buy_score = buy_structure.get('total_score', 0) * 0.4
            vacuum_score = sell_vacuum.get('vacuum_score', 0) * 0.3
            signal_score = limit_up_signals.get('signal_strength', 0) * 0.2
            main_score = main_behavior.get('confidence', 0) * 0.1

            probability = (buy_score + vacuum_score + signal_score + main_score) * 100

            # 限制在合理范围内
            return max(0, min(100, probability))

        except Exception:
            return 0

    def calculate_breakout_risk(self, breakout_signals, sell_vacuum, main_behavior):
        """
        破板风险评估

        Args:
            breakout_signals: 破板信号
            sell_vacuum: 卖盘真空分析
            main_behavior: 主力行为

        Returns:
            float: 破板风险（0-100）
        """
        try:
            # 风险评估：破板信号强度 + 卖压 + 主力意图
            signal_risk = breakout_signals.get('risk_strength', 0) * 0.5
            vacuum_risk = (1 - sell_vacuum.get('vacuum_score', 0)) * 0.3
            main_risk = (1 - main_behavior.get('confidence', 0)) * 0.2

            risk = (signal_risk + vacuum_risk + main_risk) * 100

            return max(0, min(100, risk))

        except Exception:
            return 50  # 默认中等风险

    def generate_trading_strategy(self, limit_up_probability, breakout_risk, change_pct):
        """
        生成交易策略（基于报告的决策阈值）

        Args:
            limit_up_probability: 连板概率
            breakout_risk: 破板风险
            change_pct: 当前涨跌幅

        Returns:
            str: 交易策略建议
        """
        try:
            # 基于报告的决策阈值
            if limit_up_probability >= 75:
                return "🚀 竞价买入"
            elif limit_up_probability >= 60:
                return "📈 开盘确认"
            elif breakout_risk >= 70:
                return "❌ 放弃"
            elif change_pct > 9.5:
                return "⚠️ 已涨停，观察封单"
            else:
                return "👀 继续观察"

        except Exception:
            return "❓ 策略生成失败"

    def display_overnight_analysis_result(self, code, stock_name, overnight_analysis):
        """
        显示隔夜挂单分析结果

        Args:
            code: 股票代码
            stock_name: 股票名称
            overnight_analysis: 分析结果
        """
        try:
            print(f"      当前涨跌幅: {overnight_analysis.get('change_pct', 0):.2f}%")
            print(f"      连板概率: {overnight_analysis.get('limit_up_probability', 0):.1f}%")
            print(f"      破板风险: {overnight_analysis.get('breakout_risk', 0):.1f}%")
            print(f"      交易策略: {overnight_analysis.get('strategy', '未知')}")

            # 详细分析
            buy_structure = overnight_analysis.get('buy_structure', {})
            print(f"      买盘结构: {buy_structure.get('analysis', '未知')}")

            sell_vacuum = overnight_analysis.get('sell_vacuum', {})
            print(f"      卖盘真空: {sell_vacuum.get('analysis', '未知')}")

            limit_up_signals = overnight_analysis.get('limit_up_signals', {})
            print(f"      连板信号: {limit_up_signals.get('analysis', '未知')}")

            main_behavior = overnight_analysis.get('main_behavior', {})
            print(f"      主力行为: {main_behavior.get('pattern', '未知')} - {main_behavior.get('intention', '未知')}")

        except Exception as e:
            print(f"      ❌ 结果显示失败: {e}")

    def display_overnight_analysis_summary(self, total_stocks, limit_up_candidates, breakout_risks, is_overnight_period):
        """
        显示隔夜挂单分析汇总

        Args:
            total_stocks: 总股票数
            limit_up_candidates: 连板候选
            breakout_risks: 破板风险
            is_overnight_period: 是否为隔夜分析时段
        """
        try:
            print("\n" + "=" * 80)
            print("📊 隔夜挂单分析汇总:")
            print(f"   📈 总分析股票: {total_stocks} 只")
            print(f"   🚀 连板候选: {len(limit_up_candidates)} 只")
            print(f"   ⚠️ 破板风险: {len(breakout_risks)} 只")
            print(f"   ⏰ 分析时段: {'隔夜挂单最佳时段' if is_overnight_period else '实时挂单分析'}")

            if limit_up_candidates:
                print("\n🚀 连板候选股票（按概率排序）:")
                sorted_candidates = sorted(limit_up_candidates, key=lambda x: x['probability'], reverse=True)
                for candidate in sorted_candidates:
                    print(f"   • {candidate['code']} {candidate['name']}: {candidate['probability']:.1f}% - {candidate['strategy']}")

            if breakout_risks:
                print("\n⚠️ 破板风险股票:")
                sorted_risks = sorted(breakout_risks, key=lambda x: x['risk_score'], reverse=True)
                for risk in sorted_risks:
                    print(f"   • {risk['code']} {risk['name']}: 风险{risk['risk_score']:.1f}%")

        except Exception as e:
            print(f"❌ 汇总显示失败: {e}")

    def generate_overnight_trading_suggestions(self, limit_up_candidates, breakout_risks, is_overnight_period):
        """
        生成隔夜交易建议

        Args:
            limit_up_candidates: 连板候选
            breakout_risks: 破板风险
            is_overnight_period: 是否为隔夜分析时段
        """
        try:
            print("\n" + "🎯 交易建议:")

            if is_overnight_period:
                print("   ⏰ 当前为隔夜挂单最佳分析时段（9:15-9:25）")

                if limit_up_candidates:
                    high_prob_candidates = [c for c in limit_up_candidates if c['probability'] >= 85]
                    medium_prob_candidates = [c for c in limit_up_candidates if 75 <= c['probability'] < 85]

                    if high_prob_candidates:
                        print("   🚀 强烈推荐（连板概率≥85%）:")
                        for candidate in high_prob_candidates:
                            print(f"      • {candidate['code']} {candidate['name']}: 可动用杠杆资金")

                    if medium_prob_candidates:
                        print("   📈 建议关注（连板概率75-85%）:")
                        for candidate in medium_prob_candidates:
                            print(f"      • {candidate['code']} {candidate['name']}: 竞价买入")
                else:
                    print("   📝 当前无高概率连板候选，建议观望")

            else:
                print("   ⏰ 当前非隔夜挂单最佳时段，建议:")
                print("      • 关注已识别的连板候选股")
                print("      • 等待9:15-9:25时段进行精确分析")
                print("      • 避免盲目追高")

            if breakout_risks:
                print("   ⚠️ 风险提醒:")
                for risk in breakout_risks:
                    print(f"      • {risk['code']} {risk['name']}: 破板风险较高，建议回避")

            print("\n💡 操作提醒:")
            print("   • 重点观察9:19-9:20的关键变化")
            print("   • 结合同板块个股挂单情况综合判断")
            print("   • 机构单暗码比单纯数量更重要")
            print("   • 设置好止损位，控制风险")

        except Exception as e:
            print(f"❌ 交易建议生成失败: {e}")

    def reinitialize_all_stocks(self):
        """重新初始化所有股票的历史数据"""
        print("🔄 重新初始化功能待实现")
        messagebox.showinfo("提示", "重新初始化功能待实现")

    def load_config(self):
        """加载配置"""
        try:
            config_file = Path("config_limit_up_monitor.json")
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    saved_config = json.load(f)
                    self.config.update(saved_config.get('config', {}))

                    # 加载监控股票
                    saved_stocks = saved_config.get('monitored_stocks', {})

                    print(f"📁 发现配置文件，包含 {len(saved_stocks)} 只股票")

                    for code, stock_info in saved_stocks.items():
                        try:
                            # 重新获取股票名称（如果配置中的名称是"未知"或以"股票"开头）
                            saved_name = stock_info.get('name', '未知')
                            if saved_name == '未知' or saved_name.startswith('股票'):
                                print(f"   🔄 重新获取股票名称: {code}")
                                try:
                                    new_stock_info = enhanced_stock_fetcher.get_stock_basic_info(code)
                                    stock_name = new_stock_info.get('name', saved_name)
                                except:
                                    stock_name = saved_name
                            else:
                                stock_name = saved_name

                            # 恢复股票到监控列表
                            self.monitored_stocks[code] = {
                                'name': stock_name,
                                'data': {},
                                'signals': {},
                                'decision': {},
                                'last_update': None,
                                'initialized': False,  # 重新启动后需要重新初始化
                                'added_time': stock_info.get('added_time', datetime.now().isoformat()),
                                'alert_history': []
                            }

                            # 添加到表格显示
                            self.tree.insert('', 'end', iid=code, values=(
                                code, stock_name, '未知', '❌', '❌', '❌',
                                '0', '需重新初始化', '-', '已加载'
                            ))

                            print(f"   📊 加载股票: {code} {stock_name}")

                        except Exception as e:
                            print(f"   ⚠️ 加载股票 {code} 失败: {e}")
                            continue

                print(f"✅ 配置加载成功，监控股票: {len(self.monitored_stocks)} 只")

                # 显示加载提示
                if self.monitored_stocks:
                    self.info_label.config(text=f"💡 已加载 {len(self.monitored_stocks)} 只股票，请重新初始化历史数据")

            else:
                print("📝 配置文件不存在，使用默认配置")

        except Exception as e:
            print(f"⚠️ 配置加载失败: {e}")

    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'config': self.config,
                'monitored_stocks': {},
                'save_time': datetime.now().isoformat()
            }

            # 保存监控股票信息（不包含实时数据）
            for code, stock_info in self.monitored_stocks.items():
                config_data['monitored_stocks'][code] = {
                    'name': stock_info['name'],
                    'initialized': stock_info.get('initialized', False),
                    'added_time': stock_info.get('added_time', datetime.now().isoformat())
                }

            # 保存到配置文件
            config_file = Path("config_limit_up_monitor.json")
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print(f"✅ 配置已保存，监控股票: {len(self.monitored_stocks)} 只")

        except Exception as e:
            print(f"⚠️ 配置保存失败: {e}")

    # ==================== 开盘类型分类算法 ====================

    def classify_open_type(self, stock_data):
        """
        🚀 修复：简化的开盘类型分类算法（固定显示）

        基于开盘涨跌幅进行简单分类，不再动态变化

        Args:
            stock_data: 股票实时数据

        Returns:
            dict: 开盘类型分类结果
        """
        try:
            # 获取价格数据
            current_price = stock_data.get('current_price', 0)
            open_price = stock_data.get('open', 0)
            prev_close = stock_data.get('prev_close', 0)

            if prev_close <= 0:
                return {
                    'type': 'unknown',
                    'type_name': '未知',
                    'open_change_pct': 0,
                    'current_change_pct': 0,
                    'description': '缺少前收盘价数据'
                }

            # 计算涨跌幅
            if open_price > 0:
                open_change_pct = (open_price - prev_close) / prev_close * 100
            else:
                # 如果没有开盘价，使用当前价格
                open_change_pct = (current_price - prev_close) / prev_close * 100

            current_change_pct = (current_price - prev_close) / prev_close * 100

            # 🚀 修复：基于开盘涨跌幅的固定分类
            if open_change_pct >= 2.0:
                type_name = '高开'
                type_code = 'high_open'
            elif open_change_pct <= -2.0:
                type_name = '低开'
                type_code = 'low_open'
            else:
                type_name = '平开'
                type_code = 'flat_open'

            return {
                'type': type_code,
                'type_name': type_name,
                'open_change_pct': open_change_pct,
                'current_change_pct': current_change_pct,
                'description': f'开盘{open_change_pct:+.2f}%，当前{current_change_pct:+.2f}%'
            }

        except Exception as e:
            print(f"⚠️ 开盘类型分类失败: {e}")
            return {
                'type': 'error',
                'type_name': '错误',
                'open_change_pct': 0,
                'current_change_pct': 0,
                'dynamic_type': 'error',
                'is_converted': False,
                'conversion_info': '',
                'open_price': 0,
                'current_price': 0,
                'prev_close': 0,
                'description': f'分类失败: {e}'
            }

    def _classify_by_change_pct(self, change_pct, context=""):
        """
        根据涨跌幅进行分类的辅助方法

        Args:
            change_pct: 涨跌幅百分比
            context: 上下文描述（如"开盘"、"当前"）

        Returns:
            dict: 分类结果
        """
        if abs(change_pct) <= self.config['flat_open_threshold']:
            # 平开股：-1%至+1%
            return {
                'type': 'flat_open',
                'type_name': '平开股',
                'description': f"{context}涨跌幅{change_pct:.2f}%，属于平开股范围"
            }
        elif (self.config['high_open_min'] <= change_pct <= self.config['high_open_max']):
            # 高开股：+2%至+6%
            return {
                'type': 'high_open',
                'type_name': '高开股',
                'description': f"{context}涨幅{change_pct:.2f}%，属于高开股范围"
            }
        elif change_pct < -self.config['flat_open_threshold']:
            # 低开股：<-1%
            return {
                'type': 'low_open',
                'type_name': '低开股',
                'description': f"{context}跌幅{change_pct:.2f}%，属于低开股"
            }
        elif change_pct > self.config['high_open_max']:
            # 超高开股：>6%
            return {
                'type': 'ultra_high_open',
                'type_name': '超高开股',
                'description': f"{context}涨幅{change_pct:.2f}%，超出高开股范围"
            }
        else:
            # 其他情况
            return {
                'type': 'other',
                'type_name': '其他',
                'description': f"{context}涨跌幅{change_pct:.2f}%，不在标准范围内"
            }

    def _is_positive_conversion(self, initial_type, current_type):
        """
        判断是否为正向转换（有利于连板监控的转换）

        Args:
            initial_type: 初始分类
            current_type: 当前分类

        Returns:
            bool: 是否为正向转换
        """
        # 定义正向转换规则
        positive_conversions = {
            'low_open': ['flat_open', 'high_open'],      # 低开转平开/高开
            'flat_open': ['high_open'],                  # 平开转高开
            'other': ['flat_open', 'high_open']          # 其他转平开/高开
        }

        return current_type in positive_conversions.get(initial_type, [])

    # ==================== 开盘下跌反转监控算法 ====================

    def analyze_opening_drop_reversal(self, code, stock_data):
        """
        开盘下跌反转分析（基于专业报告的五大信号体系）

        专门处理开盘下跌后的反转连板机会

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 反转分析结果
        """
        try:
            # 1. 下跌深度分析
            drop_analysis = self.analyze_drop_depth(stock_data)

            if not drop_analysis['is_suitable']:
                return {
                    'reversal_signal': False,
                    'signal_strength': 0.0,
                    'recommendation': '⏳ 跌幅过深，不适合反转监控',
                    'drop_analysis': drop_analysis
                }

            # 2. 五大反转信号检测
            reversal_signals = {
                'volume_reversal': self.check_volume_reversal_signal(code, stock_data),
                'order_anomaly': self.check_order_anomaly_signal(code, stock_data),
                'pattern_breakthrough': self.check_pattern_breakthrough_signal(code, stock_data),
                'time_energy': self.check_time_energy_signal(code, stock_data),
                'capital_flow': self.check_capital_flow_signal(code, stock_data)
            }

            # 3. 计算反转信号强度
            signal_count = sum([signal['detected'] for signal in reversal_signals.values()])
            signal_strength = signal_count / 5.0

            # 4. 反转决策判断
            reversal_decision = self.judge_reversal_decision(drop_analysis, reversal_signals, signal_strength)

            # 5. 动态仓位计算
            position_size = self.calculate_reversal_position_size(
                drop_analysis['drop_depth'],
                reversal_decision.get('recovery_speed', 0)
            )

            return {
                'reversal_signal': reversal_decision['buy_signal'],
                'signal_strength': signal_strength,
                'recommendation': reversal_decision['recommendation'],
                'position_size': position_size,
                'drop_analysis': drop_analysis,
                'reversal_signals': reversal_signals,
                'reversal_decision': reversal_decision,
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"⚠️ 开盘下跌反转分析失败 {code}: {e}")
            return {
                'reversal_signal': False,
                'signal_strength': 0.0,
                'recommendation': '❌ 分析失败',
                'error': str(e)
            }

    def analyze_drop_depth(self, stock_data):
        """
        下跌深度分析（基于报告的跌幅分类）

        Args:
            stock_data: 股票实时数据

        Returns:
            dict: 下跌分析结果
        """
        try:
            current_price = stock_data.get('current_price', 0)
            open_price = stock_data.get('open', 0)
            prev_close = stock_data.get('prev_close', 0)

            if prev_close <= 0 or open_price <= 0:
                return {'is_suitable': False, 'reason': '缺少价格数据'}

            # 计算跌幅
            current_change_pct = (current_price - prev_close) / prev_close * 100
            drop_from_open = (current_price - open_price) / open_price * 100

            # 只处理下跌情况
            if current_change_pct >= 0:
                return {'is_suitable': False, 'reason': '当前未下跌'}

            drop_depth = abs(current_change_pct)

            # 根据报告分类
            if drop_depth <= 2.0:
                # 浅跌：洗盘模式
                mode = 'wash_trading'
                mode_name = '洗盘模式'
                reversal_probability = 0.7  # 65-75%
                monitor_window = 30  # 30分钟反包
                is_suitable = True
            elif drop_depth <= 4.0:
                # 中跌：自救模式
                mode = 'self_rescue'
                mode_name = '自救模式'
                reversal_probability = 0.45  # 40-50%
                monitor_window = 60  # 60分钟反包
                is_suitable = True
            elif drop_depth <= 5.0:
                # 深跌：资金分歧
                mode = 'capital_divergence'
                mode_name = '资金分歧'
                reversal_probability = 0.25  # <30%
                monitor_window = 90  # 90分钟观察
                is_suitable = True  # 仍可监控，但降低期望
            else:
                # 超深跌：出货嫌疑
                mode = 'distribution_suspected'
                mode_name = '出货嫌疑'
                reversal_probability = 0.1  # <20%
                monitor_window = 0
                is_suitable = False

            return {
                'is_suitable': is_suitable,
                'mode': mode,
                'mode_name': mode_name,
                'drop_depth': drop_depth,
                'drop_from_open': drop_from_open,
                'reversal_probability': reversal_probability,
                'monitor_window': monitor_window,
                'current_change_pct': current_change_pct,
                'description': f'{mode_name}：跌幅{drop_depth:.2f}%，反转概率{reversal_probability*100:.0f}%'
            }

        except Exception as e:
            return {'is_suitable': False, 'reason': f'分析失败: {e}'}

    def check_volume_reversal_signal(self, code, stock_data):
        """
        量能反转信号检测（基于报告：反弹量能>下跌量能200%）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 量能反转信号结果
        """
        try:
            # 获取当前量比
            current_volume_ratio = stock_data.get('volume_ratio', 0)

            # 获取历史量能数据
            if code not in self.continuity_cache:
                return {'detected': False, 'reason': '缺少历史量能数据'}

            cache = self.continuity_cache[code]
            volume_history = cache.get('volume_ratio_history', [])

            if len(volume_history) < 10:
                return {'detected': False, 'reason': '历史数据不足'}

            # 识别下跌时段的量能（最近10分钟）
            recent_volumes = volume_history[-10:]
            drop_period_avg_volume = sum(recent_volumes) / len(recent_volumes)

            # 当前量能vs下跌时段量能
            volume_reversal_ratio = current_volume_ratio / max(0.1, drop_period_avg_volume)

            # 判断标准：
            # 1. 下跌时段量比<0.8（洗盘确认）
            # 2. 当前量能>下跌量能200%
            wash_confirmed = drop_period_avg_volume < 0.8
            volume_surge = volume_reversal_ratio > 2.0

            detected = wash_confirmed and volume_surge

            return {
                'detected': detected,
                'volume_reversal_ratio': volume_reversal_ratio,
                'drop_period_avg_volume': drop_period_avg_volume,
                'current_volume_ratio': current_volume_ratio,
                'wash_confirmed': wash_confirmed,
                'volume_surge': volume_surge,
                'signal_strength': min(1.0, volume_reversal_ratio / 3.0) if detected else 0.0,
                'description': f'量能反转比{volume_reversal_ratio:.2f}，下跌期量比{drop_period_avg_volume:.2f}'
            }

        except Exception as e:
            return {'detected': False, 'reason': f'量能反转检测失败: {e}'}

    def check_order_anomaly_signal(self, code, stock_data):
        """
        盘口异动信号检测（基于报告：鲸吞买单、撤压诱空、铁板防御）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 盘口异动信号结果
        """
        try:
            # 获取五档数据
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if not bid_volumes or not ask_volumes:
                return {'detected': False, 'reason': '缺少五档数据'}

            # 1. 鲸吞买单检测：买一量>1000手且>卖一量300%
            whale_buy = (bid_volumes[0] > 100000 and  # >1000手
                        bid_volumes[0] > ask_volumes[0] * 3)

            # 2. 铁板防御检测：买三至买五总量>买一量150%
            iron_defense = sum(bid_volumes[2:5]) > bid_volumes[0] * 1.5

            # 3. 买卖压力比检测
            total_bid = sum(bid_volumes)
            total_ask = sum(ask_volumes)
            pressure_ratio = total_bid / max(1, total_ask)
            pressure_advantage = pressure_ratio > 1.5

            # 综合判断
            anomaly_count = sum([whale_buy, iron_defense, pressure_advantage])
            detected = anomaly_count >= 2

            return {
                'detected': detected,
                'whale_buy': whale_buy,
                'iron_defense': iron_defense,
                'pressure_advantage': pressure_advantage,
                'pressure_ratio': pressure_ratio,
                'anomaly_count': anomaly_count,
                'signal_strength': anomaly_count / 3.0,
                'description': f'异动信号{anomaly_count}/3，买卖压力比{pressure_ratio:.2f}'
            }

        except Exception as e:
            return {'detected': False, 'reason': f'盘口异动检测失败: {e}'}

    def check_time_energy_signal(self, code, stock_data):
        """
        时间能量信号检测（基于报告：反攻强度>3.0）

        反攻强度 = (反弹幅度 × 反弹速度) / 下跌幅度

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 时间能量信号结果
        """
        try:
            # 获取价格历史
            if code not in self.continuity_cache:
                return {'detected': False, 'reason': '缺少价格历史数据'}

            cache = self.continuity_cache[code]
            price_history = cache.get('price_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(price_history) < 5:
                return {'detected': False, 'reason': '价格历史数据不足'}

            current_price = stock_data.get('current_price', 0)

            # 找到最低点
            min_price = min(price_history[-10:])  # 最近10个数据点的最低价
            min_index = price_history[-10:].index(min_price)

            # 计算反弹幅度
            rebound_amplitude = (current_price - min_price) / min_price * 100

            # 计算反弹速度（每分钟涨幅）
            if min_index < len(timestamp_history[-10:]) - 1:
                time_elapsed = (timestamp_history[-1] - timestamp_history[-10:][min_index]) / 60  # 分钟
                rebound_speed = rebound_amplitude / max(0.1, time_elapsed)
            else:
                rebound_speed = 0

            # 计算下跌幅度（从开盘到最低点）
            open_price = stock_data.get('open', current_price)
            drop_amplitude = abs((min_price - open_price) / open_price * 100)

            # 计算反攻强度
            if drop_amplitude > 0:
                attack_intensity = (rebound_amplitude * rebound_speed) / drop_amplitude
            else:
                attack_intensity = 0

            # 判断标准
            strong_signal = attack_intensity > 3.0
            medium_signal = 1.5 <= attack_intensity <= 3.0
            detected = strong_signal

            return {
                'detected': detected,
                'attack_intensity': attack_intensity,
                'rebound_amplitude': rebound_amplitude,
                'rebound_speed': rebound_speed,
                'drop_amplitude': drop_amplitude,
                'strong_signal': strong_signal,
                'medium_signal': medium_signal,
                'signal_strength': min(1.0, attack_intensity / 4.0),
                'description': f'反攻强度{attack_intensity:.2f}，反弹{rebound_amplitude:.2f}%'
            }

        except Exception as e:
            return {'detected': False, 'reason': f'时间能量检测失败: {e}'}

    def check_capital_flow_signal(self, code, stock_data):
        """
        资金回流信号检测（基于报告：主力净流入率>0.03）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 资金回流信号结果
        """
        try:
            # 获取资金流向数据
            big_in = stock_data.get('big_order_in', 0)
            big_out = stock_data.get('big_order_out', 0)
            small_out = stock_data.get('small_order_out', 0)
            total_volume = stock_data.get('volume', 1)

            # 获取流通市值（简化计算）
            current_price = stock_data.get('current_price', 0)
            # 假设流通股本（实际应从基础数据获取）
            float_shares = self.historical_base_data.get(code, {}).get('float_shares', 100000000)  # 1亿股默认
            float_mv = current_price * float_shares

            # 计算主力净流入率
            main_net_flow = big_in - big_out
            main_net_ratio = main_net_flow / max(1, float_mv) * 100

            # 计算散户抛压率
            retail_sell_ratio = small_out / max(1, total_volume)

            # 判断标准（基于报告）
            main_inflow_sufficient = main_net_ratio > 0.03  # 主力净流入率>3%
            retail_pressure_low = retail_sell_ratio < 0.3   # 散户抛压<30%

            detected = main_inflow_sufficient and retail_pressure_low

            return {
                'detected': detected,
                'main_net_ratio': main_net_ratio,
                'retail_sell_ratio': retail_sell_ratio,
                'main_inflow_sufficient': main_inflow_sufficient,
                'retail_pressure_low': retail_pressure_low,
                'main_net_flow': main_net_flow,
                'signal_strength': min(1.0, main_net_ratio / 0.05) if detected else 0.0,
                'description': f'主力净流入率{main_net_ratio:.3f}%，散户抛压{retail_sell_ratio:.2f}'
            }

        except Exception as e:
            return {'detected': False, 'reason': f'资金回流检测失败: {e}'}

    def judge_reversal_decision(self, drop_analysis, reversal_signals, signal_strength):
        """
        反转决策判断（基于报告的决策树）

        Args:
            drop_analysis: 下跌分析结果
            reversal_signals: 五大反转信号
            signal_strength: 信号强度

        Returns:
            dict: 反转决策结果
        """
        try:
            # 获取信号状态
            volume_reversal = reversal_signals['volume_reversal']['detected']
            order_anomaly = reversal_signals['order_anomaly']['detected']
            pattern_breakthrough = reversal_signals['pattern_breakthrough']['detected']
            time_energy = reversal_signals['time_energy']['detected']
            capital_flow = reversal_signals['capital_flow']['detected']

            # 获取反攻强度
            attack_intensity = reversal_signals['time_energy'].get('attack_intensity', 0)

            # 决策树逻辑（基于报告）
            if drop_analysis['drop_depth'] > 5.0:
                # 跌幅>5%，放弃
                return {
                    'buy_signal': False,
                    'recommendation': '❌ 跌幅过深，放弃',
                    'confidence': 0.0,
                    'reason': '跌幅超过5%'
                }

            if not volume_reversal:
                # 无量能反转，继续观察
                return {
                    'buy_signal': False,
                    'recommendation': '👀 等待量能反转',
                    'confidence': 0.2,
                    'reason': '量能反转信号未出现'
                }

            if not order_anomaly:
                # 无盘口异动，等待形态突破
                if pattern_breakthrough:
                    return {
                        'buy_signal': False,
                        'recommendation': '📊 等待盘口配合',
                        'confidence': 0.4,
                        'reason': '形态突破但盘口未配合'
                    }
                else:
                    return {
                        'buy_signal': False,
                        'recommendation': '⏳ 等待形态突破',
                        'confidence': 0.3,
                        'reason': '盘口和形态信号均未出现'
                    }

            # 有量能反转和盘口异动，检查反攻强度
            if attack_intensity > 3.0:
                # 反攻强度>3，立即买入
                return {
                    'buy_signal': True,
                    'recommendation': '🚀 立即买入',
                    'confidence': 0.9,
                    'position_ratio': 0.6,  # 重仓
                    'reason': f'满足所有条件，反攻强度{attack_intensity:.2f}'
                }

            # 反攻强度不足，检查资金回流
            if capital_flow:
                # 有资金回流，半仓买入
                return {
                    'buy_signal': True,
                    'recommendation': '📈 半仓买入',
                    'confidence': 0.7,
                    'position_ratio': 0.4,  # 中仓
                    'reason': '资金回流确认，谨慎买入'
                }
            else:
                # 无资金回流，放弃
                return {
                    'buy_signal': False,
                    'recommendation': '❌ 资金未回流，放弃',
                    'confidence': 0.1,
                    'reason': '缺少资金回流支撑'
                }

        except Exception as e:
            return {
                'buy_signal': False,
                'recommendation': '❌ 决策失败',
                'confidence': 0.0,
                'reason': f'决策判断失败: {e}'
            }

    def calculate_reversal_position_size(self, drop_depth, recovery_speed):
        """
        反转仓位计算（基于报告的仓位控制公式）

        Args:
            drop_depth: 最大跌幅（绝对值）
            recovery_speed: 每分钟回升幅度

        Returns:
            float: 建议仓位比例
        """
        try:
            # 基于报告的公式
            risk_factor = min(1.0, drop_depth * 0.5)  # 每跌1%风险系数+0.5
            speed_bonus = recovery_speed * 20  # 每分钟涨0.1%奖励2%

            position_size = max(0.2, min(0.7, 0.5 - risk_factor + speed_bonus))

            return position_size

        except Exception as e:
            print(f"⚠️ 仓位计算失败: {e}")
            return 0.2  # 默认轻仓

    # ==================== 数据保存系统 ====================

    def save_realtime_data(self, code, stock_data):
        """
        保存实时数据到文件

        Args:
            code: 股票代码
            stock_data: 实时股票数据
        """
        try:
            current_time = datetime.now()
            timestamp = current_time.strftime('%H:%M:%S')

            # 准备保存的数据
            save_data = {
                'timestamp': timestamp,
                'datetime': current_time.isoformat(),
                'code': code,
                'current_price': stock_data.get('current_price', 0),
                'open_price': stock_data.get('open', 0),
                'high_price': stock_data.get('high', 0),
                'low_price': stock_data.get('low', 0),
                'volume': stock_data.get('volume', 0),
                'volume_ratio': stock_data.get('volume_ratio', 0),
                'change_pct': stock_data.get('change_pct', 0),
                'bid_volumes': stock_data.get('bid_volumes', []),
                'ask_volumes': stock_data.get('ask_volumes', []),
                'bid_prices': stock_data.get('bid_prices', []),
                'ask_prices': stock_data.get('ask_prices', [])
            }

            # 添加到内存序列
            if code not in self.realtime_data_series:
                self.realtime_data_series[code] = []

            self.realtime_data_series[code].append(save_data)

            # 保存到文件（每只股票单独文件）
            data_file = self.today_data_dir / f"{code}_realtime.json"

            # 如果文件存在，追加数据；否则创建新文件
            if data_file.exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    existing_data = json.load(f)
            else:
                existing_data = []

            existing_data.append(save_data)

            # 写回文件
            with open(data_file, 'w', encoding='utf-8') as f:
                json.dump(existing_data, f, ensure_ascii=False, indent=2, default=str)

            # 更新早盘关键数据（9:30-10:00）
            if self.is_morning_key_period():
                self.update_morning_key_data(code, stock_data, current_time)

        except Exception as e:
            print(f"⚠️ 保存实时数据失败 {code}: {e}")

    def load_today_realtime_data(self, code):
        """
        加载当日已保存的实时数据

        Args:
            code: 股票代码

        Returns:
            list: 实时数据列表
        """
        try:
            data_file = self.today_data_dir / f"{code}_realtime.json"

            if data_file.exists():
                with open(data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)

                # 加载到内存
                self.realtime_data_series[code] = data

                print(f"✅ 加载 {code} 当日实时数据: {len(data)} 条记录")
                return data
            else:
                print(f"📝 {code} 当日暂无实时数据")
                return []

        except Exception as e:
            print(f"⚠️ 加载当日实时数据失败 {code}: {e}")
            return []

    def is_morning_key_period(self):
        """检查是否在早盘关键时段（9:30-10:00）"""
        try:
            now = datetime.now()
            current_time = now.time()

            morning_start = datetime.strptime("09:30", "%H:%M").time()
            morning_end = datetime.strptime("10:00", "%H:%M").time()

            return morning_start <= current_time <= morning_end

        except Exception:
            return False

    def update_morning_key_data(self, code, stock_data, current_time):
        """
        更新早盘关键数据（9:30-10:00）

        Args:
            code: 股票代码
            stock_data: 股票数据
            current_time: 当前时间
        """
        try:
            if code not in self.morning_key_data:
                self.morning_key_data[code] = {
                    'max_volume_ratio': 0,
                    'max_volume_time': None,
                    'min_price': float('inf'),
                    'min_price_time': None,
                    'max_price': 0,
                    'max_price_time': None,
                    'avg_line_conquered': False,
                    'avg_line_conquered_time': None,
                    'max_bid5_depth': 0,
                    'max_bid5_time': None,
                    'morning_high': 0,
                    'morning_low': float('inf'),
                    'volume_peaks': []  # 量能峰值记录
                }

            key_data = self.morning_key_data[code]
            current_price = stock_data.get('current_price', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])

            # 更新最大量比
            if volume_ratio > key_data['max_volume_ratio']:
                key_data['max_volume_ratio'] = volume_ratio
                key_data['max_volume_time'] = current_time.strftime('%H:%M:%S')

            # 更新最低价
            if current_price < key_data['min_price'] and current_price > 0:
                key_data['min_price'] = current_price
                key_data['min_price_time'] = current_time.strftime('%H:%M:%S')

            # 更新最高价
            if current_price > key_data['max_price']:
                key_data['max_price'] = current_price
                key_data['max_price_time'] = current_time.strftime('%H:%M:%S')
                key_data['morning_high'] = current_price

            # 更新早盘低点
            if current_price < key_data['morning_low'] and current_price > 0:
                key_data['morning_low'] = current_price

            # 更新买五厚度
            if bid_volumes:
                bid5_depth = sum(bid_volumes)
                if bid5_depth > key_data['max_bid5_depth']:
                    key_data['max_bid5_depth'] = bid5_depth
                    key_data['max_bid5_time'] = current_time.strftime('%H:%M:%S')

            # 记录量能峰值（量比>3.0的时点）
            if volume_ratio > 3.0:
                key_data['volume_peaks'].append({
                    'time': current_time.strftime('%H:%M:%S'),
                    'volume_ratio': volume_ratio,
                    'price': current_price
                })

            # 检查均价线征服（简化判断：价格持续高于开盘价）
            open_price = stock_data.get('open', 0)
            if not key_data['avg_line_conquered'] and current_price > open_price * 1.01:
                # 简化判断：高于开盘价1%认为征服均价线
                key_data['avg_line_conquered'] = True
                key_data['avg_line_conquered_time'] = current_time.strftime('%H:%M:%S')

        except Exception as e:
            print(f"⚠️ 更新早盘关键数据失败 {code}: {e}")

    def get_morning_key_data(self, code):
        """
        获取早盘关键数据

        Args:
            code: 股票代码

        Returns:
            dict: 早盘关键数据
        """
        return self.morning_key_data.get(code, {})

    # ==================== 时段差异化策略系统 ====================

    def initialize_time_period_strategies(self):
        """
        初始化时段差异化策略（支持9:15开始监控）

        Returns:
            dict: 时段策略配置
        """
        return {
            # 🚀 新增：集合竞价时段（9:15-9:25）
            'auction_period': {
                'name': '集合竞价监控',
                'time_range': ('09:15', '09:25'),
                'scan_interval': 15,  # 15秒间隔，关键时段
                'priority': 'critical',
                'key_signals': ['overnight_order', 'auction_volume', 'regulation_risk'],
                'thresholds': {
                    'buy_order_ratio': 0.2,  # 买单比例>20%
                    'regulation_risk_max': 0.7,  # 监管风险<70%
                    'auction_volume_min': 1.0  # 集合竞价量比>1.0
                }
            },
            'opening_volume': {
                'name': '开盘量能监控',
                'time_range': ('09:30', '09:40'),
                'scan_interval': 15,
                'priority': 'high',
                'key_signals': ['volume_spike', 'opening_gap'],
                'thresholds': {
                    'volume_ratio_min': 2.0,
                    'gap_protection': 0.5
                }
            },
            'avg_price_conquest': {
                'name': '均价征服监控',
                'time_range': ('09:40', '10:30'),
                'scan_interval': 30,
                'priority': 'high',
                'key_signals': ['avg_line_break', 'volume_sustain'],
                'thresholds': {
                    'avg_line_hold_time': 10,  # 分钟
                    'volume_ratio_sustain': 1.5
                }
            },
            'intraday_surge': {
                'name': '盘中突击监控',
                'time_range': ('10:30', '11:30'),
                'scan_interval': 60,
                'priority': 'medium',
                'key_signals': ['breakout_surge', 'platform_break'],
                'thresholds': {
                    'surge_speed': 0.1,  # 每分钟涨幅
                    'platform_hold_time': 20
                }
            },
            'lunch_break': {
                'name': '午休观察',
                'time_range': ('11:30', '13:00'),
                'scan_interval': 300,  # 5分钟
                'priority': 'low',
                'key_signals': [],
                'thresholds': {}
            },
            'afternoon_charge': {
                'name': '午盘冲锋监控',
                'time_range': ('13:00', '14:00'),
                'scan_interval': 30,
                'priority': 'high',
                'key_signals': ['morning_high_break', 'volume_recovery'],
                'thresholds': {
                    'morning_high_break': True,
                    'volume_recovery_ratio': 1.2
                }
            },
            'late_session': {
                'name': '尾盘监控',
                'time_range': ('14:00', '14:45'),
                'scan_interval': 60,
                'priority': 'medium',
                'key_signals': ['late_surge', 'volume_confirm'],
                'thresholds': {
                    'late_surge_min': 0.07,  # 7%以上
                    'volume_ratio_min': 3.0
                }
            },
            'final_sprint': {
                'name': '最后冲刺',
                'time_range': ('14:45', '15:00'),
                'scan_interval': 15,
                'priority': 'critical',
                'key_signals': ['final_push', 'limit_up_attempt'],
                'thresholds': {
                    'final_push_volume': 5.0,
                    'limit_approach': 0.09  # 接近涨停
                }
            }
        }

    def get_current_time_period(self):
        """
        获取当前时段策略

        Returns:
            dict: 当前时段策略配置
        """
        try:
            now = datetime.now()
            current_time = now.time()

            for period_name, strategy in self.time_period_strategies.items():
                start_time = datetime.strptime(strategy['time_range'][0], "%H:%M").time()
                end_time = datetime.strptime(strategy['time_range'][1], "%H:%M").time()

                if start_time <= current_time <= end_time:
                    strategy['period_name'] = period_name
                    self.current_time_period = period_name
                    return strategy

            # 如果不在任何时段内，返回默认策略
            return {
                'name': '非交易时间',
                'period_name': 'non_trading',
                'scan_interval': 300,
                'priority': 'none',
                'key_signals': [],
                'thresholds': {}
            }

        except Exception as e:
            print(f"⚠️ 获取当前时段失败: {e}")
            return self.time_period_strategies['intraday_surge']  # 默认策略

    def apply_time_period_strategy(self, code, stock_data):
        """
        应用时段差异化策略

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 时段策略分析结果
        """
        try:
            current_strategy = self.get_current_time_period()
            period_name = current_strategy.get('period_name', 'unknown')

            # 根据时段选择对应的分析方法
            if period_name == 'auction_period':
                # 🚀 新增：集合竞价时段分析
                return self.analyze_auction_period(code, stock_data, current_strategy)
            elif period_name == 'opening_volume':
                return self.analyze_opening_volume_period(code, stock_data, current_strategy)
            elif period_name == 'avg_price_conquest':
                return self.analyze_avg_price_conquest_period(code, stock_data, current_strategy)
            elif period_name == 'intraday_surge':
                return self.analyze_intraday_surge_period(code, stock_data, current_strategy)
            elif period_name == 'afternoon_charge':
                return self.analyze_afternoon_charge_period(code, stock_data, current_strategy)
            elif period_name == 'late_session':
                return self.analyze_late_session_period(code, stock_data, current_strategy)
            elif period_name == 'final_sprint':
                return self.analyze_final_sprint_period(code, stock_data, current_strategy)
            else:
                return self.analyze_default_period(code, stock_data, current_strategy)

        except Exception as e:
            print(f"⚠️ 时段策略应用失败 {code}: {e}")
            return {
                'period_signal': False,
                'period_name': 'error',
                'signal_strength': 0.0,
                'details': {'error': str(e)}
            }

    def analyze_current_period(self, code, stock_data):
        """
        分析当前时段并返回时段策略

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 时段分析结果
        """
        try:
            current_time = datetime.now()
            hour = current_time.hour
            minute = current_time.minute

            # 判断当前时段
            if hour == 9 and 15 <= minute <= 25:
                period_name = "集合竞价监控"
                strategy = "隔夜挂单分析"
            elif hour == 9 and 30 <= minute <= 45:
                period_name = "早盘博弈"
                strategy = "开盘形态确认"
            elif hour == 9 and 45 <= minute <= 59:
                period_name = "上午盘整"
                strategy = "量能持续监控"
            elif hour == 10:
                period_name = "上午盘整"
                strategy = "量能持续监控"
            elif hour == 11 and minute <= 30:
                period_name = "上午盘整"
                strategy = "量能持续监控"
            elif hour == 13 and minute <= 30:
                period_name = "午盘回流"
                strategy = "资金回流确认"
            elif hour == 13 and 30 < minute <= 59:
                period_name = "下午冲刺"
                strategy = "突破信号捕捉"
            elif hour == 14:
                period_name = "下午冲刺"
                strategy = "突破信号捕捉"
            elif hour == 14 and 45 <= minute <= 59:
                period_name = "尾盘决战"
                strategy = "最后冲刺监控"
            else:
                period_name = "盘中突击监控"
                strategy = "全时段监控"

            return {
                'period_name': period_name,
                'strategy': strategy,
                'current_time': current_time.strftime('%H:%M:%S'),
                'market_phase': self._get_market_phase(hour, minute)
            }

        except Exception as e:
            return {
                'period_name': '未知时段',
                'strategy': '观察',
                'error': str(e)
            }

    def _get_market_phase(self, hour, minute):
        """获取市场阶段"""
        if hour == 9 and 15 <= minute <= 25:
            return "集合竞价"
        elif hour == 9 and 30 <= minute <= 59:
            return "早盘"
        elif hour == 10 or (hour == 11 and minute <= 30):
            return "上午盘"
        elif hour == 13:
            return "午盘"
        elif hour == 14 and minute <= 45:
            return "下午盘"
        elif hour == 14 and 45 <= minute <= 59:
            return "尾盘"
        else:
            return "盘外"

    def analyze_auction_period(self, code, stock_data, strategy):
        """
        集合竞价时段分析（9:15-9:25）

        重点：隔夜挂单分析、监管风险检测、主力意图预判

        Args:
            code: 股票代码
            stock_data: 股票数据
            strategy: 时段策略配置

        Returns:
            dict: 集合竞价分析结果
        """
        try:
            # 🚀 集合竞价时段特殊处理

            # 1. 监管风险优先检测（报告要求）
            if sell_monitoring_algorithms:
                regulation_analysis = sell_monitoring_algorithms.analyze_regulation_risk(stock_data)
                regulation_risk = regulation_analysis.get('risk_score', 0)

                # 监管风险过高，立即标记卖出
                if regulation_risk > 0.7:
                    return {
                        'period_signal': True,
                        'period_name': '集合竞价',
                        'signal_type': 'sell_immediately',
                        'signal_strength': 0.1,  # 极低，表示立即卖出
                        'recommendation': '9:25挂跌停卖出',
                        'reason': f"监管风险过高: {regulation_analysis.get('risk_factors', [])}",
                        'details': {
                            'regulation_analysis': regulation_analysis,
                            'action': 'immediate_sell'
                        }
                    }

            # 2. 隔夜挂单结构分析（模拟，实际需要Level-2数据）
            volume_ratio = stock_data.get('volume_ratio', 0)
            change_pct = stock_data.get('change_pct', 0)

            # 模拟买盘结构分析
            buy_structure_score = 0.5
            if volume_ratio > 1.5:  # 集合竞价量能较大
                buy_structure_score += 0.2
            if change_pct > 2:  # 高开
                buy_structure_score += 0.2
            elif change_pct < -2:  # 低开
                buy_structure_score -= 0.3

            # 3. 主力意图预判
            main_intention = 'unknown'
            if buy_structure_score > 0.7:
                main_intention = 'bullish'  # 看多
                signal_strength = 0.8
                recommendation = '持有待涨'
            elif buy_structure_score < 0.3:
                main_intention = 'bearish'  # 看空
                signal_strength = 0.2
                recommendation = '9:25竞价卖出'
            else:
                main_intention = 'neutral'  # 中性
                signal_strength = 0.5
                recommendation = '开盘后确认'

            return {
                'period_signal': True,
                'period_name': '集合竞价',
                'signal_type': 'auction_analysis',
                'signal_strength': signal_strength,
                'recommendation': recommendation,
                'reason': f'主力意图: {main_intention}',
                'details': {
                    'buy_structure_score': buy_structure_score,
                    'main_intention': main_intention,
                    'volume_ratio': volume_ratio,
                    'change_pct': change_pct,
                    'regulation_check': 'passed' if regulation_risk <= 0.7 else 'failed'
                }
            }

        except Exception as e:
            print(f"⚠️ 集合竞价分析失败 {code}: {e}")
            return {
                'period_signal': False,
                'period_name': '集合竞价',
                'signal_strength': 0.5,
                'details': {'error': str(e)}
            }

    def analyze_opening_volume_period(self, code, stock_data, strategy):
        """
        开盘量能监控期分析（9:30-9:40）

        重点：量能突击、开盘缺口保护
        """
        try:
            thresholds = strategy.get('thresholds', {})
            volume_ratio = stock_data.get('volume_ratio', 0)
            current_price = stock_data.get('current_price', 0)
            open_price = stock_data.get('open', 0)
            prev_close = stock_data.get('prev_close', 0)

            # 1. 量能突击检测
            volume_spike = volume_ratio > thresholds.get('volume_ratio_min', 2.0)

            # 2. 开盘缺口保护检测
            if prev_close > 0 and open_price > 0:
                open_gap = (open_price - prev_close) / prev_close
                gap_protection = current_price > (prev_close + open_gap * thresholds.get('gap_protection', 0.5))
            else:
                gap_protection = False

            # 3. 早盘强势特征
            morning_key = self.get_morning_key_data(code)
            is_new_high = current_price >= morning_key.get('morning_high', 0)

            # 综合判断
            signal_count = sum([volume_spike, gap_protection, is_new_high])
            period_signal = signal_count >= 2

            return {
                'period_signal': period_signal,
                'period_name': '开盘量能监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'volume_spike': volume_spike,
                    'gap_protection': gap_protection,
                    'is_new_high': is_new_high,
                    'volume_ratio': volume_ratio,
                    'signal_count': signal_count
                }
            }

        except Exception as e:
            return {'period_signal': False, 'period_name': '开盘量能监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_avg_price_conquest_period(self, code, stock_data, strategy):
        """
        均价征服监控期分析（9:40-10:30）

        重点：均价线征服、量能持续
        """
        try:
            thresholds = strategy.get('thresholds', {})
            morning_key = self.get_morning_key_data(code)

            # 1. 均价线征服检测
            avg_line_conquered = morning_key.get('avg_line_conquered', False)

            # 2. 量能持续检测
            volume_ratio = stock_data.get('volume_ratio', 0)
            volume_sustain = volume_ratio > thresholds.get('volume_ratio_sustain', 1.5)

            # 3. 价格稳定性检测（基于历史数据）
            price_stable = self.check_price_stability(code, stock_data)

            # 综合判断
            signal_count = sum([avg_line_conquered, volume_sustain, price_stable])
            period_signal = signal_count >= 2

            return {
                'period_signal': period_signal,
                'period_name': '均价征服监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'avg_line_conquered': avg_line_conquered,
                    'volume_sustain': volume_sustain,
                    'price_stable': price_stable,
                    'volume_ratio': volume_ratio,
                    'signal_count': signal_count
                }
            }

        except Exception as e:
            return {'period_signal': False, 'period_name': '均价征服监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_intraday_surge_period(self, code, stock_data, strategy):
        """
        盘中突击监控期分析（10:30-11:30）

        重点：平台突破、涨速监控
        """
        try:
            thresholds = strategy.get('thresholds', {})

            # 1. 涨速检测
            surge_speed = self.calculate_surge_speed(code, stock_data)
            speed_sufficient = surge_speed > thresholds.get('surge_speed', 0.1)

            # 2. 平台突破检测
            platform_break = self.check_platform_breakthrough(code, stock_data)

            # 3. 量能配合检测
            volume_ratio = stock_data.get('volume_ratio', 0)
            volume_support = volume_ratio > 1.2

            # 综合判断
            signal_count = sum([speed_sufficient, platform_break, volume_support])
            period_signal = signal_count >= 2

            return {
                'period_signal': period_signal,
                'period_name': '盘中突击监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'speed_sufficient': speed_sufficient,
                    'platform_break': platform_break,
                    'volume_support': volume_support,
                    'surge_speed': surge_speed,
                    'volume_ratio': volume_ratio,
                    'signal_count': signal_count
                }
            }

        except Exception as e:
            return {'period_signal': False, 'period_name': '盘中突击监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_afternoon_charge_period(self, code, stock_data, strategy):
        """
        午盘冲锋监控期分析（13:00-14:00）

        重点：突破早盘高点、量能恢复
        """
        try:
            thresholds = strategy.get('thresholds', {})
            morning_key = self.get_morning_key_data(code)
            current_price = stock_data.get('current_price', 0)

            # 1. 早盘高点突破检测
            morning_high = morning_key.get('morning_high', 0)
            high_break = current_price > morning_high * 1.005  # 突破0.5%

            # 2. 量能恢复检测
            volume_ratio = stock_data.get('volume_ratio', 0)
            max_morning_volume = morning_key.get('max_volume_ratio', 0)
            volume_recovery = volume_ratio > max_morning_volume * thresholds.get('volume_recovery_ratio', 0.8)

            # 3. 买盘强度检测
            bid_strength = self.check_bid_strength(code, stock_data)

            # 综合判断
            signal_count = sum([high_break, volume_recovery, bid_strength])
            period_signal = signal_count >= 2

            return {
                'period_signal': period_signal,
                'period_name': '午盘冲锋监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'high_break': high_break,
                    'volume_recovery': volume_recovery,
                    'bid_strength': bid_strength,
                    'morning_high': morning_high,
                    'current_price': current_price,
                    'volume_ratio': volume_ratio,
                    'signal_count': signal_count
                }
            }

        except Exception as e:
            return {'period_signal': False, 'period_name': '午盘冲锋监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_late_session_period(self, code, stock_data, strategy):
        """
        尾盘监控期分析（14:00-14:45）

        重点：尾盘拉升、量能确认
        """
        try:
            thresholds = strategy.get('thresholds', {})
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)

            # 1. 尾盘涨幅检测
            if prev_close > 0:
                current_gain = (current_price - prev_close) / prev_close
                late_surge = current_gain > thresholds.get('late_surge_min', 0.07)
            else:
                late_surge = False

            # 2. 量能确认检测
            volume_ratio = stock_data.get('volume_ratio', 0)
            volume_confirm = volume_ratio > thresholds.get('volume_ratio_min', 3.0)

            # 3. 尾盘特征检测（换手率控制）
            turnover_healthy = self.check_turnover_health(code, stock_data)

            # 综合判断
            signal_count = sum([late_surge, volume_confirm, turnover_healthy])
            period_signal = signal_count >= 2

            return {
                'period_signal': period_signal,
                'period_name': '尾盘监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'late_surge': late_surge,
                    'volume_confirm': volume_confirm,
                    'turnover_healthy': turnover_healthy,
                    'current_gain': current_gain if prev_close > 0 else 0,
                    'volume_ratio': volume_ratio,
                    'signal_count': signal_count
                }
            }

        except Exception as e:
            return {'period_signal': False, 'period_name': '尾盘监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_final_sprint_period(self, code, stock_data, strategy):
        """
        最后冲刺期分析（14:45-15:00）

        重点：最后推升、涨停尝试
        """
        try:
            thresholds = strategy.get('thresholds', {})
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)

            # 1. 最后推升检测
            volume_ratio = stock_data.get('volume_ratio', 0)
            final_push = volume_ratio > thresholds.get('final_push_volume', 5.0)

            # 2. 涨停接近检测
            if prev_close > 0:
                current_gain = (current_price - prev_close) / prev_close
                limit_approach = current_gain > thresholds.get('limit_approach', 0.09)
            else:
                limit_approach = False

            # 3. 封单强度检测
            seal_strength = self.check_limit_up_seal_strength(code, stock_data)

            # 综合判断
            signal_count = sum([final_push, limit_approach, seal_strength])
            period_signal = signal_count >= 2

            return {
                'period_signal': period_signal,
                'period_name': '最后冲刺',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'final_push': final_push,
                    'limit_approach': limit_approach,
                    'seal_strength': seal_strength,
                    'current_gain': current_gain if prev_close > 0 else 0,
                    'volume_ratio': volume_ratio,
                    'signal_count': signal_count
                }
            }

        except Exception as e:
            return {'period_signal': False, 'period_name': '最后冲刺', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_default_period(self, code, stock_data, strategy):
        """
        默认时段分析
        """
        return {
            'period_signal': False,
            'period_name': strategy.get('name', '默认时段'),
            'signal_strength': 0.0,
            'details': {'message': '非关键时段，保持观察'}
        }

    # ==================== 智能算法切换系统 ====================

    def select_intelligent_algorithm_with_state(self, code, stock_data):
        """
        基于股票状态的智能算法选择（新增）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 算法选择结果（包含股票状态信息）
        """
        try:
            # 获取价格轨迹跟踪器
            price_tracker = self.price_trackers.get(code)
            if not price_tracker:
                # 如果没有跟踪器，使用原有算法
                return self.select_intelligent_algorithm(code, stock_data)

            # 获取股票状态
            stock_state, state_details = price_tracker.get_stock_state()

            # 🚀 使用主循环中计算的正确涨跌幅
            current_gain = stock_data.get('correct_change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            market_duration = self.get_market_duration_minutes()

            # 基于股票状态的算法选择

            # 注意：涨停股票已在主循环中处理，这里不应该出现涨停股票
            # 如果出现，说明数据有问题，记录日志
            if current_gain >= 9.5:
                print(f"   ⚠️ {code} 状态算法中检测到疑似涨停: 涨幅{current_gain:.2f}%，但未被主循环拦截")
                # 不使用涨停封板算法，而是观察状态
                algorithm_info = {
                    'algorithm_type': 'observation',
                    'algorithm_name': '观察算法',
                    'confidence': 0.0,
                    'reason': f'疑似涨停{current_gain:.2f}%，需要观察',
                    'stock_state': stock_state
                }

            # 2. V字反转 - 最佳买入机会
            elif stock_state == "V字反转":
                reversal_strength = state_details.get('reversal_strength', 0)
                if reversal_strength >= 2.0:  # 反转幅度足够大
                    algorithm_info = {
                        'algorithm_type': 'v_reversal_momentum',
                        'algorithm_name': 'V字反转动量算法',
                        'confidence': 0.95,
                        'reason': f'V字反转确认，反转幅度{reversal_strength:.1f}%',
                        'stock_state': stock_state
                    }
                else:
                    algorithm_info = {
                        'algorithm_type': 'v_reversal_early',
                        'algorithm_name': 'V字反转早期算法',
                        'confidence': 0.85,
                        'reason': f'V字反转初期，反转幅度{reversal_strength:.1f}%',
                        'stock_state': stock_state
                    }

            # 3. 横盘状态 - 等待突破
            elif stock_state == "横盘状态":
                avg_gain = state_details.get('avg_gain', 0)
                duration = state_details.get('duration_points', 0)

                if avg_gain >= 5.0:
                    algorithm_info = {
                        'algorithm_type': 'high_level_breakout',
                        'algorithm_name': '高位横盘突破算法',
                        'confidence': 0.9,
                        'reason': f'高位{avg_gain:.1f}%横盘{duration}点，监控突破',
                        'stock_state': stock_state
                    }
                elif avg_gain >= 3.0:
                    algorithm_info = {
                        'algorithm_type': 'mid_level_accumulation',
                        'algorithm_name': '中位横盘蓄势算法',
                        'confidence': 0.85,
                        'reason': f'中位{avg_gain:.1f}%横盘{duration}点，蓄势待发',
                        'stock_state': stock_state
                    }
                else:
                    algorithm_info = {
                        'algorithm_type': 'low_level_consolidation',
                        'algorithm_name': '低位横盘整理算法',
                        'confidence': 0.8,
                        'reason': f'低位{avg_gain:.1f}%横盘{duration}点，整理中',
                        'stock_state': stock_state
                    }

            # 4. 持续上涨 - 趋势跟随
            elif stock_state == "持续上涨":
                total_rise = state_details.get('total_rise', 0)
                if current_gain >= 6.0:
                    algorithm_info = {
                        'algorithm_type': 'momentum_continuation',
                        'algorithm_name': '高位动量延续算法',
                        'confidence': 0.85,
                        'reason': f'持续上涨{total_rise:.1f}%，高位动量',
                        'stock_state': stock_state
                    }
                else:
                    algorithm_info = {
                        'algorithm_type': 'trend_following',
                        'algorithm_name': '趋势跟随算法',
                        'confidence': 0.9,
                        'reason': f'持续上涨{total_rise:.1f}%，趋势良好',
                        'stock_state': stock_state
                    }

            # 5. 冲高回落 - 谨慎观望
            elif stock_state == "冲高回落":
                pullback_amount = state_details.get('pullback_amount', 0)
                high_point = state_details.get('high_point', 0)

                if pullback_amount <= 1.5 and current_gain >= 3.0:
                    algorithm_info = {
                        'algorithm_type': 'pullback_recovery',
                        'algorithm_name': '回调修复算法',
                        'confidence': 0.7,
                        'reason': f'从{high_point:.1f}%小幅回调{pullback_amount:.1f}%，等待修复',
                        'stock_state': stock_state
                    }
                else:
                    algorithm_info = {
                        'algorithm_type': 'pullback_observation',
                        'algorithm_name': '回调观望算法',
                        'confidence': 0.6,
                        'reason': f'从{high_point:.1f}%大幅回调{pullback_amount:.1f}%，观望为主',
                        'stock_state': stock_state
                    }

            # 6. 普通状态 - 使用原有算法
            else:
                # 调用原有算法并添加状态信息
                algorithm_info = self.select_intelligent_algorithm(code, stock_data)
                algorithm_info['stock_state'] = stock_state

            return algorithm_info

        except Exception as e:
            print(f"⚠️ 基于状态的智能算法选择失败 {code}: {e}")
            # 回退到原有算法
            return self.select_intelligent_algorithm(code, stock_data)

    def select_intelligent_algorithm(self, code, stock_data):
        """
        智能算法选择（基于报告的算法切换逻辑）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 算法选择结果
        """
        try:
            # 获取基础分类
            open_type_result = self.classify_open_type(stock_data)

            # 🚀 优先使用主循环中计算的正确涨跌幅
            current_gain = stock_data.get('correct_change_pct', open_type_result.get('current_change_pct', 0))
            open_gain = open_type_result.get('open_change_pct', 0)

            # 获取时间信息
            current_time = datetime.now()
            market_duration = self.get_market_duration_minutes()

            # 智能算法选择逻辑
            algorithm_info = self.determine_algorithm_type(
                open_gain, current_gain, market_duration, code, stock_data
            )

            return algorithm_info

        except Exception as e:
            print(f"⚠️ 智能算法选择失败 {code}: {e}")
            return {
                'algorithm_type': 'error',
                'algorithm_name': '错误算法',
                'confidence': 0.0,
                'reason': f'算法选择失败: {e}'
            }

    def determine_algorithm_type(self, open_gain, current_gain, duration, code, stock_data):
        """
        确定算法类型（基于报告的复杂逻辑）

        Args:
            open_gain: 开盘涨跌幅
            current_gain: 当前涨跌幅
            duration: 开盘持续时间（分钟）
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 算法类型信息
        """
        try:
            # 注意：涨停股票已在主循环中硬性判断，这里不应该出现涨停股票
            if current_gain >= 9.5:
                print(f"   ⚠️ {code} 原有算法检测到疑似涨停: 涨幅{current_gain:.2f}%，但未被主循环拦截")
                return {
                    'algorithm_type': 'observation',
                    'algorithm_name': '观察算法',
                    'confidence': 0.0,
                    'reason': f'疑似涨停{current_gain:.2f}%，需要观察'
                }

            # 2. 🚀 优化：开盘下跌反转场景（支持动态切换）
            if current_gain < 0:
                drop_depth = abs(current_gain)
                if drop_depth <= 5.0:  # 跌幅≤5%，可能反转
                    return {
                        'algorithm_type': 'drop_reversal',
                        'algorithm_name': '开盘下跌反转算法',
                        'confidence': 0.8,
                        'reason': f'跌幅{drop_depth:.2f}%，启动反转监控'
                    }
                else:
                    return {
                        'algorithm_type': 'abandon',
                        'algorithm_name': '放弃算法',
                        'confidence': 0.0,
                        'reason': f'跌幅{drop_depth:.2f}%过深，放弃监控'
                    }

            # 🚀 新增：低开反转成功后的动态切换逻辑
            if open_gain < -1.0 and current_gain > 0:  # 低开但现在转正
                recovery_range = current_gain - open_gain
                if recovery_range > 3.0:  # 反转幅度>3%，切换到高位算法
                    if current_gain >= 5.0:
                        return {
                            'algorithm_type': 'high_position_breakthrough',
                            'algorithm_name': '高位横盘突破算法',
                            'confidence': 0.9,
                            'reason': f'低开反转成功，当前{current_gain:.2f}%，切换高位算法'
                        }
                    elif current_gain >= 2.0:
                        return {
                            'algorithm_type': 'mid_position_accumulation',
                            'algorithm_name': '中位蓄势算法',
                            'confidence': 0.85,
                            'reason': f'低开反转成功，当前{current_gain:.2f}%，切换中位算法'
                        }

                # 反转幅度较小，继续使用低开算法
                return {
                    'algorithm_type': 'drop_reversal',
                    'algorithm_name': '开盘下跌反转算法',
                    'confidence': 0.8,
                    'reason': f'低开{open_gain:.2f}%反转中，当前{current_gain:.2f}%'
                }

            # 3. 高开回落再反转场景
            drop_from_open = open_gain - current_gain
            if open_gain > 2.0 and drop_from_open > 1.0:  # 高开后回落
                if drop_from_open <= 3.0:  # 回落≤3%
                    return {
                        'algorithm_type': 'high_open_correction',
                        'algorithm_name': '高开修正算法',
                        'confidence': 0.85,
                        'reason': f'高开{open_gain:.2f}%后回落{drop_from_open:.2f}%，启动修正算法'
                    }
                else:  # 回落>3%
                    return {
                        'algorithm_type': 'vshape_recovery',
                        'algorithm_name': 'V型反转算法',
                        'confidence': 0.7,
                        'reason': f'高开后深度回落{drop_from_open:.2f}%，启动V型反转'
                    }

            # 4. 中位蓄势场景（新增）
            if 1.0 <= current_gain <= 5.0 and duration >= 15:
                platform_duration = self.estimate_platform_duration(code, stock_data)
                volatility = self.calculate_recent_volatility(code, stock_data)
                if platform_duration >= 15 and volatility < 2.0:  # 横盘≥15分钟且振幅<2%
                    return {
                        'algorithm_type': 'mid_level_accumulation',
                        'algorithm_name': '中位蓄势算法',
                        'confidence': 0.85,
                        'reason': f'中位{current_gain:.2f}%蓄势{platform_duration}分钟，振幅{volatility:.2f}%'
                    }

            # 5. 高位横盘突破场景
            if current_gain >= 6.0 and duration >= 30:
                platform_duration = self.estimate_platform_duration(code, stock_data)
                if platform_duration >= 20:  # 横盘≥20分钟
                    return {
                        'algorithm_type': 'high_platform_breakout',
                        'algorithm_name': '高位横盘突破算法',
                        'confidence': 0.9,
                        'reason': f'高位{current_gain:.2f}%横盘{platform_duration}分钟'
                    }

            # 6. 平开上涨场景（增强版）
            if abs(open_gain) <= 1.0 and current_gain > 2.0 and duration >= 30:
                return {
                    'algorithm_type': 'enhanced_flat_open',
                    'algorithm_name': '增强版平开算法',
                    'confidence': 0.8,
                    'reason': f'平开后涨至{current_gain:.2f}%并维持{duration}分钟'
                }

            # 7. 标准平开场景
            if abs(open_gain) <= 1.0:
                return {
                    'algorithm_type': 'standard_flat_open',
                    'algorithm_name': '标准平开算法',
                    'confidence': 0.7,
                    'reason': f'标准平开股，当前涨幅{current_gain:.2f}%'
                }

            # 8. 标准高开场景
            if 2.0 <= open_gain <= 6.0:
                return {
                    'algorithm_type': 'standard_high_open',
                    'algorithm_name': '标准高开算法',
                    'confidence': 0.75,
                    'reason': f'标准高开股{open_gain:.2f}%，当前{current_gain:.2f}%'
                }

            # 9. 其他场景
            return {
                'algorithm_type': 'observation',
                'algorithm_name': '观察算法',
                'confidence': 0.3,
                'reason': f'开盘{open_gain:.2f}%，当前{current_gain:.2f}%，暂不符合主要场景'
            }

        except Exception as e:
            return {
                'algorithm_type': 'error',
                'algorithm_name': '错误算法',
                'confidence': 0.0,
                'reason': f'算法确定失败: {e}'
            }

    def get_market_duration_minutes(self):
        """
        获取开盘持续时间（分钟）

        Returns:
            int: 开盘持续分钟数
        """
        try:
            now = datetime.now()
            current_time = now.time()

            # 上午时段
            morning_start = datetime.strptime("09:30", "%H:%M").time()
            morning_end = datetime.strptime("11:30", "%H:%M").time()

            # 下午时段
            afternoon_start = datetime.strptime("13:00", "%H:%M").time()
            afternoon_end = datetime.strptime("15:00", "%H:%M").time()

            if morning_start <= current_time <= morning_end:
                # 上午时段
                start_datetime = datetime.combine(now.date(), morning_start)
                current_datetime = datetime.combine(now.date(), current_time)
                duration = (current_datetime - start_datetime).total_seconds() / 60
                return int(duration)
            elif afternoon_start <= current_time <= afternoon_end:
                # 下午时段（加上上午的120分钟）
                start_datetime = datetime.combine(now.date(), afternoon_start)
                current_datetime = datetime.combine(now.date(), current_time)
                afternoon_duration = (current_datetime - start_datetime).total_seconds() / 60
                return int(120 + afternoon_duration)  # 上午120分钟 + 下午时间
            else:
                return 0

        except Exception as e:
            print(f"⚠️ 计算开盘时间失败: {e}")
            return 0

    def get_open_type_emoji(self, open_type):
        """获取开盘类型对应的表情符号"""
        emoji_map = {
            'flat_open': '📊',      # 平开股
            'high_open': '🚀',      # 高开股
            'low_open': '📉',       # 低开股
            'ultra_high_open': '🔥', # 超高开股
            'other': '❓',          # 其他
            'unknown': '❌',        # 未知
            'error': '⚠️'          # 错误
        }
        return emoji_map.get(open_type, '❓')

    def is_suitable_for_limit_up_monitoring(self, open_type):
        """
        判断是否适合连板监控

        Args:
            open_type: 开盘类型

        Returns:
            bool: 是否适合监控
        """
        # 平开股和高开股适合连板监控
        suitable_types = ['flat_open', 'high_open']
        return open_type in suitable_types

    def get_monitoring_strategy(self, open_type):
        """
        根据开盘类型获取监控策略

        Args:
            open_type: 开盘类型

        Returns:
            dict: 监控策略配置
        """
        strategies = {
            'flat_open': {
                'name': '平开股监控策略',
                'focus_signals': ['volume_spike', 'depth_anomaly', 'pattern_break'],
                'signal_weights': {'volume_spike': 0.4, 'depth_anomaly': 0.3, 'pattern_break': 0.3},
                'buy_threshold': 3,  # 需要满足3个信号
                'description': '量能突击+盘口异动+形态突破'
            },
            'high_open': {
                'name': '高开股监控策略',
                'focus_signals': ['volume_consistent', 'pressure_test', 'gap_protection'],
                'signal_weights': {'volume_consistent': 0.4, 'pressure_test': 0.3, 'gap_protection': 0.3},
                'buy_threshold': 3,  # 需要满足3个信号
                'description': '量能持续+抛压测试+跳空守护'
            },
            'low_open': {
                'name': '低开股观望策略',
                'focus_signals': [],
                'signal_weights': {},
                'buy_threshold': 0,
                'description': '低开股不适合连板监控，建议观望'
            },
            'ultra_high_open': {
                'name': '超高开股风险策略',
                'focus_signals': [],
                'signal_weights': {},
                'buy_threshold': 0,
                'description': '超高开股风险较大，建议谨慎'
            },
            'other': {
                'name': '通用观望策略',
                'focus_signals': [],
                'signal_weights': {},
                'buy_threshold': 0,
                'description': '不在标准范围内，建议观望'
            }
        }

        return strategies.get(open_type, strategies['other'])

    # ==================== 平开股监控算法 ====================

    def analyze_flat_open_stock(self, code, stock_data):
        """
        平开股监控分析（-1%至+1%开盘）

        核心买入信号组合（需同时满足）：
        1. 量能突击：买一量比突增>300%
        2. 盘口异动：买五厚度变化率>50%/30秒
        3. 形态突破：分时W底完成

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 分析结果
        """
        try:
            signals = {
                'volume_spike': False,      # 量能突击
                'depth_anomaly': False,     # 盘口异动
                'pattern_break': False,     # 形态突破
                'buy_signal': False,        # 综合买入信号
                'signal_count': 0,          # 满足信号数量
                'signal_strength': 0.0,     # 信号强度
                'details': {}               # 详细信息
            }

            # 信号1：量能突击检测
            volume_result = self.check_volume_spike_flat_open(code, stock_data)
            signals['volume_spike'] = volume_result['detected']
            signals['details']['volume_spike'] = volume_result

            # 信号2：盘口异动检测
            depth_result = self.check_depth_anomaly_flat_open(code, stock_data)
            signals['depth_anomaly'] = depth_result['detected']
            signals['details']['depth_anomaly'] = depth_result

            # 信号3：形态突破检测
            pattern_result = self.check_pattern_breakthrough_flat_open(code, stock_data)
            signals['pattern_break'] = pattern_result['detected']
            signals['details']['pattern_break'] = pattern_result

            # 计算信号统计
            signal_list = [signals['volume_spike'], signals['depth_anomaly'], signals['pattern_break']]
            signals['signal_count'] = sum(signal_list)
            signals['signal_strength'] = signals['signal_count'] / 3.0

            # 综合买入信号判断：三个条件同时满足
            signals['buy_signal'] = (signals['volume_spike'] and
                                   signals['depth_anomaly'] and
                                   signals['pattern_break'])

            # 生成建议
            if signals['buy_signal']:
                recommendation = "🚀 立即买入"
                confidence = 0.9
            elif signals['signal_count'] >= 2:
                recommendation = "👀 密切关注"
                confidence = 0.7
            elif signals['signal_count'] >= 1:
                recommendation = "📝 继续观察"
                confidence = 0.5
            else:
                recommendation = "⏳ 暂不关注"
                confidence = 0.2

            signals['recommendation'] = recommendation
            signals['confidence'] = confidence

            return signals

        except Exception as e:
            print(f"⚠️ 平开股分析失败 {code}: {e}")
            return {
                'volume_spike': False,
                'depth_anomaly': False,
                'pattern_break': False,
                'buy_signal': False,
                'signal_count': 0,
                'signal_strength': 0.0,
                'recommendation': "❌ 分析失败",
                'confidence': 0.0,
                'details': {'error': str(e)}
            }

    def check_volume_spike_flat_open(self, code, stock_data):
        """
        检测量能突击（买一量比突增>300%）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 检测结果
        """
        try:
            # 获取当前买一挂单量
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            current_bid1_volume = bid_volumes[0] if bid_volumes else 0

            if current_bid1_volume <= 0:
                return {
                    'detected': False,
                    'reason': '无买一挂单数据',
                    'current_bid1': 0,
                    'historical_avg': 0,
                    'spike_ratio': 0
                }

            # 获取历史买一挂单量平均值（从缓存中获取）
            historical_bid1_avg = self.get_historical_bid1_average(code)

            if historical_bid1_avg <= 0:
                return {
                    'detected': False,
                    'reason': '缺少历史买一数据',
                    'current_bid1': current_bid1_volume,
                    'historical_avg': 0,
                    'spike_ratio': 0
                }

            # 计算突增比例
            spike_ratio = current_bid1_volume / historical_bid1_avg

            # 判断是否突增>300%（即比例>4.0）
            threshold = self.config['volume_spike_threshold'] + 1.0  # 3.0 + 1.0 = 4.0
            detected = spike_ratio > threshold

            return {
                'detected': detected,
                'reason': f'买一量比突增{spike_ratio:.1f}倍' if detected else f'买一量比{spike_ratio:.1f}倍未达标',
                'current_bid1': current_bid1_volume,
                'historical_avg': historical_bid1_avg,
                'spike_ratio': spike_ratio,
                'threshold': threshold
            }

        except Exception as e:
            print(f"⚠️ 量能突击检测失败 {code}: {e}")
            return {
                'detected': False,
                'reason': f'检测失败: {e}',
                'current_bid1': 0,
                'historical_avg': 0,
                'spike_ratio': 0
            }

    def check_depth_anomaly_flat_open(self, code, stock_data):
        """
        检测盘口异动（买五厚度变化率>50%/30秒）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 检测结果
        """
        try:
            # 获取当前买五总厚度
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            current_bid5_total = sum(bid_volumes) if bid_volumes else 0

            if current_bid5_total <= 0:
                return {
                    'detected': False,
                    'reason': '无买五挂单数据',
                    'current_bid5_total': 0,
                    'previous_bid5_total': 0,
                    'change_rate': 0
                }

            # 获取30秒前的买五总厚度（从持续性缓存中获取）
            previous_bid5_total = self.get_previous_bid5_total(code, seconds_ago=30)

            if previous_bid5_total <= 0:
                # 如果没有30秒前的数据，使用历史平均值作为基准
                previous_bid5_total = self.get_historical_bid5_average(code)

                if previous_bid5_total <= 0:
                    return {
                        'detected': False,
                        'reason': '缺少历史买五数据',
                        'current_bid5_total': current_bid5_total,
                        'previous_bid5_total': 0,
                        'change_rate': 0
                    }

            # 计算变化率
            change_rate = (current_bid5_total - previous_bid5_total) / previous_bid5_total

            # 判断是否变化率>50%
            threshold = self.config['depth_change_threshold']  # 0.5 = 50%
            detected = change_rate > threshold

            return {
                'detected': detected,
                'reason': f'买五厚度变化率{change_rate:.1%}' if detected else f'买五厚度变化率{change_rate:.1%}未达标',
                'current_bid5_total': current_bid5_total,
                'previous_bid5_total': previous_bid5_total,
                'change_rate': change_rate,
                'threshold': threshold
            }

        except Exception as e:
            print(f"⚠️ 盘口异动检测失败 {code}: {e}")
            return {
                'detected': False,
                'reason': f'检测失败: {e}',
                'current_bid5_total': 0,
                'previous_bid5_total': 0,
                'change_rate': 0
            }

    def check_pattern_breakthrough_flat_open(self, code, stock_data):
        """
        检测形态突破（分时W底完成）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 检测结果
        """
        try:
            # 获取当前价格
            current_price = stock_data.get('current_price', 0)

            if current_price <= 0:
                return {
                    'detected': False,
                    'reason': '无当前价格数据',
                    'current_price': 0,
                    'neckline_price': 0,
                    'pattern_low': 0
                }

            # 获取分时数据进行W底形态分析
            minute_data = self.get_recent_minute_data(code)

            if minute_data is None or len(minute_data) < 10:
                return {
                    'detected': False,
                    'reason': '分时数据不足',
                    'current_price': current_price,
                    'neckline_price': 0,
                    'pattern_low': 0
                }

            # W底形态识别
            w_pattern_result = self.identify_w_bottom_pattern(minute_data)

            if not w_pattern_result['has_pattern']:
                return {
                    'detected': False,
                    'reason': 'W底形态未形成',
                    'current_price': current_price,
                    'neckline_price': w_pattern_result.get('neckline_price', 0),
                    'pattern_low': w_pattern_result.get('pattern_low', 0)
                }

            # 检查是否突破颈线
            neckline_price = w_pattern_result['neckline_price']
            pattern_low = w_pattern_result['pattern_low']

            # 突破条件：当前价格 > 颈线价格 且 最低点 > 形态低点
            breakthrough = (current_price > neckline_price and
                          w_pattern_result.get('min_low', 0) > pattern_low)

            return {
                'detected': breakthrough,
                'reason': f'W底突破颈线{neckline_price:.2f}' if breakthrough else f'未突破颈线{neckline_price:.2f}',
                'current_price': current_price,
                'neckline_price': neckline_price,
                'pattern_low': pattern_low,
                'w_pattern_details': w_pattern_result
            }

        except Exception as e:
            print(f"⚠️ 形态突破检测失败 {code}: {e}")
            return {
                'detected': False,
                'reason': f'检测失败: {e}',
                'current_price': 0,
                'neckline_price': 0,
                'pattern_low': 0
            }

    # ==================== 低开股监控算法 ====================

    def analyze_low_open_stock(self, code, stock_data):
        """
        🚀 新增：低开股监控分析（-2%以下开盘）

        专门处理开盘下跌但后续上涨的股票

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 分析结果
        """
        try:
            signals = {
                'volume_reversal': False,    # 量能反转
                'price_recovery': False,     # 价格回升
                'pattern_reversal': False,   # 形态反转
                'buy_signal': False,         # 综合买入信号
                'signal_count': 0,           # 满足信号数量
                'signal_strength': 0.0,      # 信号强度
                'details': {}                # 详细信息
            }

            # 获取价格数据
            current_price = stock_data.get('current_price', 0)
            open_price = stock_data.get('open', 0)
            prev_close = stock_data.get('prev_close', 0)

            if prev_close <= 0:
                return signals

            # 计算涨跌幅
            open_change_pct = (open_price - prev_close) / prev_close * 100 if open_price > 0 else 0
            current_change_pct = (current_price - prev_close) / prev_close * 100

            # 信号1：量能反转检测（降低阈值，更容易触发）
            volume_ratio = stock_data.get('volume_ratio', 0)
            volume_reversal = volume_ratio > 1.0  # 🚀 降低阈值：1.5 -> 1.0
            signals['volume_reversal'] = volume_reversal
            signals['details']['volume_reversal'] = {
                'detected': volume_reversal,
                'volume_ratio': volume_ratio,
                'reason': f'量比{volume_ratio:.2f}' + ('，量能反转' if volume_reversal else '，量能不足')
            }

            # 信号2：价格回升检测（降低阈值，更容易触发）
            recovery_range = current_change_pct - open_change_pct
            price_recovery = recovery_range > 0.5 and current_change_pct > open_change_pct  # 🚀 降低阈值：1.0 -> 0.5
            signals['price_recovery'] = price_recovery
            signals['details']['price_recovery'] = {
                'detected': price_recovery,
                'recovery_range': recovery_range,
                'open_change': open_change_pct,
                'current_change': current_change_pct,
                'reason': f'回升{recovery_range:.2f}%' + ('，价格反转' if price_recovery else '，回升不足')
            }

            # 信号3：形态反转检测（价格突破开盘价或接近前收盘）
            breakthrough_open = current_price > open_price if open_price > 0 else False
            near_prev_close = abs(current_change_pct) < 1.0  # 接近前收盘价±1%
            pattern_reversal = breakthrough_open or near_prev_close
            signals['pattern_reversal'] = pattern_reversal
            signals['details']['pattern_reversal'] = {
                'detected': pattern_reversal,
                'breakthrough_open': breakthrough_open,
                'near_prev_close': near_prev_close,
                'reason': '形态反转' if pattern_reversal else '形态未反转'
            }

            # 计算信号统计
            signal_list = [signals['volume_reversal'], signals['price_recovery'], signals['pattern_reversal']]
            signals['signal_count'] = sum(signal_list)
            signals['signal_strength'] = signals['signal_count'] / 3.0

            # 🚀 关键：低开股的买入信号判断（更宽松的条件）
            # 只要有1个信号满足就可以考虑买入（更激进的策略）
            signals['buy_signal'] = signals['signal_count'] >= 1

            # 生成建议（更积极的推荐）
            if signals['signal_count'] >= 2:
                recommendation = "🚀 强烈低开反转"
                confidence = 0.9
            elif signals['signal_count'] == 1:
                recommendation = "📈 低开反转机会"
                confidence = 0.7
            else:
                recommendation = "📊 观察反转信号"
                confidence = 0.4

            signals['recommendation'] = recommendation
            signals['confidence'] = confidence
            signals['reason'] = f"低开{open_change_pct:.2f}%，当前{current_change_pct:.2f}%，{signals['signal_count']}/3信号满足"

            return signals

        except Exception as e:
            print(f"⚠️ 低开股分析失败 {code}: {e}")
            return {
                'volume_reversal': False,
                'price_recovery': False,
                'pattern_reversal': False,
                'buy_signal': False,
                'signal_count': 0,
                'signal_strength': 0.0,
                'recommendation': "❌ 分析失败",
                'confidence': 0.0,
                'details': {'error': str(e)}
            }

    # ==================== 高开股监控算法 ====================

    def analyze_high_open_stock(self, code, stock_data):
        """
        高开股监控分析（+2%至+6%开盘）

        核心买入信号组合（需同时满足）：
        1. 量能持续：量比维持>3.0持续
        2. 抛压测试：卖五撤单率>40%/30秒
        3. 跳空守护：不补缺口

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 分析结果
        """
        try:
            signals = {
                'volume_consistent': False,     # 量能持续
                'pressure_test': False,         # 抛压测试
                'gap_protection': False,        # 跳空守护
                'buy_signal': False,            # 综合买入信号
                'signal_count': 0,              # 满足信号数量
                'signal_strength': 0.0,         # 信号强度
                'details': {}                   # 详细信息
            }

            # 信号1：量能持续检测
            volume_result = self.check_volume_consistency_high_open(code, stock_data)
            signals['volume_consistent'] = volume_result['detected']
            signals['details']['volume_consistent'] = volume_result

            # 信号2：抛压测试检测
            pressure_result = self.check_pressure_test_high_open(code, stock_data)
            signals['pressure_test'] = pressure_result['detected']
            signals['details']['pressure_test'] = pressure_result

            # 信号3：跳空守护检测
            gap_result = self.check_gap_protection_high_open(code, stock_data)
            signals['gap_protection'] = gap_result['detected']
            signals['details']['gap_protection'] = gap_result

            # 计算信号统计
            signal_list = [signals['volume_consistent'], signals['pressure_test'], signals['gap_protection']]
            signals['signal_count'] = sum(signal_list)
            signals['signal_strength'] = signals['signal_count'] / 3.0

            # 综合买入信号判断：三个条件同时满足
            signals['buy_signal'] = (signals['volume_consistent'] and
                                   signals['pressure_test'] and
                                   signals['gap_protection'])

            # 生成建议
            if signals['buy_signal']:
                recommendation = "🚀 立即买入"
                confidence = 0.9
            elif signals['signal_count'] >= 2:
                recommendation = "👀 密切关注"
                confidence = 0.7
            elif signals['signal_count'] >= 1:
                recommendation = "📝 继续观察"
                confidence = 0.5
            else:
                recommendation = "⏳ 暂不关注"
                confidence = 0.2

            signals['recommendation'] = recommendation
            signals['confidence'] = confidence

            return signals

        except Exception as e:
            print(f"⚠️ 高开股分析失败 {code}: {e}")
            return {
                'volume_consistent': False,
                'pressure_test': False,
                'gap_protection': False,
                'buy_signal': False,
                'signal_count': 0,
                'signal_strength': 0.0,
                'recommendation': "❌ 分析失败",
                'confidence': 0.0,
                'details': {'error': str(e)}
            }

    def check_volume_consistency_high_open(self, code, stock_data):
        """
        检测量能持续（量比维持>3.0持续）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 检测结果
        """
        try:
            # 获取当前成交量
            current_volume = stock_data.get('volume', 0)

            if current_volume <= 0:
                return {
                    'detected': False,
                    'reason': '无成交量数据',
                    'current_volume_ratio': 0,
                    'historical_ratios': [],
                    'consistency_duration': 0
                }

            # 计算当前量比
            historical_avg_volume = self.get_historical_volume_average(code)

            if historical_avg_volume <= 0:
                return {
                    'detected': False,
                    'reason': '缺少历史成交量数据',
                    'current_volume_ratio': 0,
                    'historical_ratios': [],
                    'consistency_duration': 0
                }

            current_volume_ratio = current_volume / historical_avg_volume

            # 获取最近的量比历史记录
            historical_ratios = self.get_recent_volume_ratios(code, minutes=10)

            # 检查量比是否持续>3.0
            threshold = self.config['volume_ratio_threshold']  # 3.0
            consistent_ratios = [ratio for ratio in historical_ratios if ratio > threshold]

            # 计算持续时间（分钟）
            consistency_duration = len(consistent_ratios)

            # 判断条件：当前量比>3.0 且 最近5分钟内至少有3分钟量比>3.0
            current_above_threshold = current_volume_ratio > threshold
            historical_consistency = consistency_duration >= 3

            detected = current_above_threshold and historical_consistency

            return {
                'detected': detected,
                'reason': f'量比{current_volume_ratio:.1f}持续{consistency_duration}分钟' if detected else f'量比{current_volume_ratio:.1f}持续性不足',
                'current_volume_ratio': current_volume_ratio,
                'historical_ratios': historical_ratios,
                'consistency_duration': consistency_duration,
                'threshold': threshold
            }

        except Exception as e:
            print(f"⚠️ 量能持续检测失败 {code}: {e}")
            return {
                'detected': False,
                'reason': f'检测失败: {e}',
                'current_volume_ratio': 0,
                'historical_ratios': [],
                'consistency_duration': 0
            }

    def check_pressure_test_high_open(self, code, stock_data):
        """
        检测抛压测试（卖五撤单率>40%/30秒）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 检测结果
        """
        try:
            # 获取当前卖五总挂单量
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            current_ask5_total = sum(ask_volumes) if ask_volumes else 0

            if current_ask5_total <= 0:
                return {
                    'detected': False,
                    'reason': '无卖五挂单数据',
                    'current_ask5_total': 0,
                    'initial_ask5_total': 0,
                    'cancel_rate': 0
                }

            # 获取30秒前的卖五总挂单量
            initial_ask5_total = self.get_previous_ask5_total(code, seconds_ago=30)

            if initial_ask5_total <= 0:
                # 如果没有30秒前的数据，使用历史平均值作为基准
                initial_ask5_total = self.get_historical_ask5_average(code)

                if initial_ask5_total <= 0:
                    return {
                        'detected': False,
                        'reason': '缺少历史卖五数据',
                        'current_ask5_total': current_ask5_total,
                        'initial_ask5_total': 0,
                        'cancel_rate': 0
                    }

            # 计算撤单率
            if initial_ask5_total > current_ask5_total:
                cancel_rate = (initial_ask5_total - current_ask5_total) / initial_ask5_total
            else:
                cancel_rate = 0  # 如果卖盘增加，撤单率为0

            # 判断撤单率是否>40%
            threshold = self.config['cancel_rate_threshold']  # 0.4 = 40%
            detected = cancel_rate > threshold

            return {
                'detected': detected,
                'reason': f'卖五撤单率{cancel_rate:.1%}' if detected else f'卖五撤单率{cancel_rate:.1%}未达标',
                'current_ask5_total': current_ask5_total,
                'initial_ask5_total': initial_ask5_total,
                'cancel_rate': cancel_rate,
                'threshold': threshold
            }

        except Exception as e:
            print(f"⚠️ 抛压测试检测失败 {code}: {e}")
            return {
                'detected': False,
                'reason': f'检测失败: {e}',
                'current_ask5_total': 0,
                'initial_ask5_total': 0,
                'cancel_rate': 0
            }

    def check_gap_protection_high_open(self, code, stock_data):
        """
        检测跳空守护（不补缺口）

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 检测结果
        """
        try:
            # 获取价格数据
            current_price = stock_data.get('current_price', 0)
            open_price = stock_data.get('open', 0)
            prev_close = stock_data.get('prev_close', 0)

            if prev_close <= 0 or open_price <= 0 or current_price <= 0:
                return {
                    'detected': False,
                    'reason': '缺少价格数据',
                    'current_price': current_price,
                    'open_price': open_price,
                    'prev_close': prev_close,
                    'gap_fill_ratio': 0
                }

            # 计算缺口大小
            gap_size = open_price - prev_close

            if gap_size <= 0:
                return {
                    'detected': False,
                    'reason': '无向上跳空缺口',
                    'current_price': current_price,
                    'open_price': open_price,
                    'prev_close': prev_close,
                    'gap_fill_ratio': 0
                }

            # 计算缺口回补比例
            if current_price < open_price:
                # 当前价格低于开盘价，计算回补比例
                filled_gap = open_price - current_price
                gap_fill_ratio = filled_gap / gap_size
            else:
                # 当前价格高于或等于开盘价，没有回补
                gap_fill_ratio = 0

            # 判断缺口保护：回补比例<50%
            max_fill_ratio = 0.5  # 50%
            detected = gap_fill_ratio < max_fill_ratio

            return {
                'detected': detected,
                'reason': f'缺口回补{gap_fill_ratio:.1%}，守护良好' if detected else f'缺口回补{gap_fill_ratio:.1%}，守护不足',
                'current_price': current_price,
                'open_price': open_price,
                'prev_close': prev_close,
                'gap_fill_ratio': gap_fill_ratio,
                'gap_size': gap_size,
                'max_fill_ratio': max_fill_ratio
            }

        except Exception as e:
            print(f"⚠️ 跳空守护检测失败 {code}: {e}")
            return {
                'detected': False,
                'reason': f'检测失败: {e}',
                'current_price': 0,
                'open_price': 0,
                'prev_close': 0,
                'gap_fill_ratio': 0
            }

    # ==================== 涨停封板算法已移除 ====================
    # 涨停判断现在统一在主循环中进行硬性判断（基于9.9%涨幅）

    # ==================== 实时盘口监控系统 ====================

    def analyze_realtime_depth(self, code, stock_data):
        """
        实时盘口监控分析

        包括：
        1. 买盘厚度分析
        2. 挂单动态分析（蚂蚁上树、铁板防御、弹簧压缩模式）
        3. 撤单行为监测

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 盘口分析结果
        """
        try:
            analysis_result = {
                'bid_depth_analysis': {},       # 买盘厚度分析
                'order_pattern_analysis': {},   # 挂单动态分析
                'cancel_behavior_analysis': {}, # 撤单行为监测
                'overall_signal': False,        # 综合信号
                'signal_strength': 0.0          # 信号强度
            }

            # 1. 买盘厚度分析
            analysis_result['bid_depth_analysis'] = self.analyze_bid_depth(code, stock_data)

            # 2. 挂单动态分析
            analysis_result['order_pattern_analysis'] = self.analyze_order_pattern(code, stock_data)

            # 3. 撤单行为监测
            analysis_result['cancel_behavior_analysis'] = self.analyze_cancel_behavior(code, stock_data)

            # 计算综合信号强度
            signal_scores = []

            if analysis_result['bid_depth_analysis'].get('strong_bid', False):
                signal_scores.append(0.4)

            if analysis_result['order_pattern_analysis'].get('positive_pattern', False):
                signal_scores.append(0.3)

            if analysis_result['cancel_behavior_analysis'].get('bullish_behavior', False):
                signal_scores.append(0.3)

            analysis_result['signal_strength'] = sum(signal_scores)
            analysis_result['overall_signal'] = analysis_result['signal_strength'] >= 0.6

            return analysis_result

        except Exception as e:
            print(f"⚠️ 实时盘口分析失败 {code}: {e}")
            return {
                'bid_depth_analysis': {},
                'order_pattern_analysis': {},
                'cancel_behavior_analysis': {},
                'overall_signal': False,
                'signal_strength': 0.0,
                'error': str(e)
            }

    def analyze_bid_depth(self, code, stock_data):
        """
        买盘厚度分析

        检查：
        1. 买一厚度 > 昨日同期200%
        2. 买五总厚度 > 卖五150%

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 买盘厚度分析结果
        """
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if not bid_volumes or not ask_volumes:
                return {
                    'strong_bid': False,
                    'reason': '缺少五档数据',
                    'bid1_strength': False,
                    'bid5_vs_ask5': False
                }

            # 检查1：买一厚度 > 昨日同期200%
            current_bid1 = bid_volumes[0]
            yesterday_bid1_avg = self.get_yesterday_same_time_bid1(code)

            bid1_strength = False
            bid1_ratio = 0
            if yesterday_bid1_avg > 0:
                bid1_ratio = current_bid1 / yesterday_bid1_avg
                bid1_strength = bid1_ratio > 2.0
            else:
                # 如果没有昨日数据，使用历史平均买一作为基准
                historical_bid1_avg = self.get_historical_bid1_average(code)
                if historical_bid1_avg > 0:
                    bid1_ratio = current_bid1 / historical_bid1_avg
                    bid1_strength = bid1_ratio > 3.0  # 提高阈值到300%

            # 检查2：买五总厚度 > 卖五150%
            bid5_total = sum(bid_volumes)
            ask5_total = sum(ask_volumes)

            bid5_vs_ask5 = False
            bid_ask_ratio = 0
            if ask5_total > 0:
                bid_ask_ratio = bid5_total / ask5_total
                bid5_vs_ask5 = bid_ask_ratio > 1.5

            # 综合判断
            strong_bid = bid1_strength and bid5_vs_ask5

            return {
                'strong_bid': strong_bid,
                'reason': f'买一强度{bid1_strength}({bid1_ratio:.1f}倍), 买卖比{bid5_vs_ask5}({bid_ask_ratio:.1f}倍)',
                'bid1_strength': bid1_strength,
                'bid5_vs_ask5': bid5_vs_ask5,
                'current_bid1': current_bid1,
                'yesterday_bid1_avg': yesterday_bid1_avg,
                'bid1_ratio': bid1_ratio,
                'bid5_total': bid5_total,
                'ask5_total': ask5_total,
                'bid_ask_ratio': bid_ask_ratio
            }

        except Exception as e:
            print(f"⚠️ 买盘厚度分析失败 {code}: {e}")
            return {
                'strong_bid': False,
                'reason': f'分析失败: {e}',
                'bid1_strength': False,
                'bid5_vs_ask5': False
            }

    def analyze_order_pattern(self, code, stock_data):
        """
        挂单动态分析（识别特殊模式）

        模式识别：
        1. 蚂蚁上树：买一至买五每档递增10%+
        2. 铁板防御：买三至买五突增大单
        3. 弹簧压缩：买盘持续增厚但价格不动

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 挂单模式分析结果
        """
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)

            if not bid_volumes or len(bid_volumes) < 5:
                return {
                    'positive_pattern': False,
                    'pattern_type': 'none',
                    'pattern_description': '缺少五档数据',
                    'ant_climbing': False,
                    'iron_defense': False,
                    'spring_compression': False
                }

            patterns = {
                'ant_climbing': False,      # 蚂蚁上树
                'iron_defense': False,      # 铁板防御
                'spring_compression': False # 弹簧压缩
            }

            # 模式1：蚂蚁上树检测
            patterns['ant_climbing'] = self.detect_ant_climbing_pattern(bid_volumes)

            # 模式2：铁板防御检测
            patterns['iron_defense'] = self.detect_iron_defense_pattern(code, bid_volumes)

            # 模式3：弹簧压缩检测
            patterns['spring_compression'] = self.detect_spring_compression_pattern(code, stock_data)

            # 确定主要模式
            positive_pattern = any(patterns.values())

            if patterns['ant_climbing']:
                pattern_type = 'ant_climbing'
                pattern_description = '蚂蚁上树：买盘逐档递增，主力稳步建仓'
            elif patterns['iron_defense']:
                pattern_type = 'iron_defense'
                pattern_description = '铁板防御：买三至买五突增，护盘信号强烈'
            elif patterns['spring_compression']:
                pattern_type = 'spring_compression'
                pattern_description = '弹簧压缩：买盘持续增厚，爆发前兆明显'
            else:
                pattern_type = 'none'
                pattern_description = '无明显特殊模式'

            return {
                'positive_pattern': positive_pattern,
                'pattern_type': pattern_type,
                'pattern_description': pattern_description,
                'ant_climbing': patterns['ant_climbing'],
                'iron_defense': patterns['iron_defense'],
                'spring_compression': patterns['spring_compression']
            }

        except Exception as e:
            print(f"⚠️ 挂单模式分析失败 {code}: {e}")
            return {
                'positive_pattern': False,
                'pattern_type': 'error',
                'pattern_description': f'分析失败: {e}',
                'ant_climbing': False,
                'iron_defense': False,
                'spring_compression': False
            }

    def detect_ant_climbing_pattern(self, bid_volumes):
        """检测蚂蚁上树模式：买一至买五每档递增10%+"""
        try:
            if len(bid_volumes) < 5 or bid_volumes[0] <= 0:
                return False

            # 检查每档是否递增10%+
            for i in range(1, 5):
                if bid_volumes[i] <= 0:
                    return False

                growth_rate = (bid_volumes[i] - bid_volumes[i-1]) / bid_volumes[i-1]
                if growth_rate < 0.1:  # 递增幅度<10%
                    return False

            return True

        except Exception:
            return False

    def detect_iron_defense_pattern(self, code, bid_volumes):
        """检测铁板防御模式：买三至买五突增大单"""
        try:
            if len(bid_volumes) < 5:
                return False

            # 获取历史买三至买五的平均值
            historical_bid345_avg = self.get_historical_bid345_average(code)

            if historical_bid345_avg <= 0:
                return False

            # 计算当前买三至买五总量
            current_bid345_total = sum(bid_volumes[2:5])

            # 判断是否突增（>历史平均值200%）
            surge_ratio = current_bid345_total / historical_bid345_avg

            return surge_ratio > 2.0

        except Exception:
            return False

    def detect_spring_compression_pattern(self, code, stock_data):
        """检测弹簧压缩模式：买盘持续增厚但价格不动"""
        try:
            # 获取当前买盘总厚度
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            current_bid_total = sum(bid_volumes)

            # 获取价格变化
            current_price = stock_data.get('current_price', 0)

            # 获取最近5分钟的买盘厚度和价格历史
            recent_bid_history = self.get_recent_bid_total_history(code, minutes=5)
            recent_price_history = self.get_recent_price_history(code, minutes=5)

            if len(recent_bid_history) < 3 or len(recent_price_history) < 3:
                return False

            # 检查买盘是否持续增厚
            bid_increasing = all(
                recent_bid_history[i] >= recent_bid_history[i-1]
                for i in range(1, len(recent_bid_history))
            )

            # 检查价格是否相对稳定（波动<1%）
            price_stability = (max(recent_price_history) - min(recent_price_history)) / min(recent_price_history) < 0.01

            return bid_increasing and price_stability

        except Exception:
            return False

    def analyze_cancel_behavior(self, code, stock_data):
        """
        撤单行为监测

        计算主力吸筹指数：
        主力吸筹指数 = (卖盘撤单量 - 买盘撤单量) / 总挂单量

        判断标准：
        - >0.3：强烈吸筹
        - <-0.2：出货嫌疑

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 撤单行为分析结果
        """
        try:
            # 获取当前五档数据
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if not bid_volumes or not ask_volumes:
                return {
                    'bullish_behavior': False,
                    'absorption_index': 0.0,
                    'behavior_type': 'unknown',
                    'behavior_description': '缺少五档数据',
                    'bid_cancel_volume': 0,
                    'ask_cancel_volume': 0
                }

            # 获取30秒前的五档数据
            previous_bid_volumes = self.get_previous_bid_volumes(code, seconds_ago=30)
            previous_ask_volumes = self.get_previous_ask_volumes(code, seconds_ago=30)

            if not previous_bid_volumes or not previous_ask_volumes:
                return {
                    'bullish_behavior': False,
                    'absorption_index': 0.0,
                    'behavior_type': 'insufficient_data',
                    'behavior_description': '缺少历史对比数据',
                    'bid_cancel_volume': 0,
                    'ask_cancel_volume': 0
                }

            # 计算撤单量
            bid_cancel_volume = self.calculate_cancel_volume(previous_bid_volumes, bid_volumes)
            ask_cancel_volume = self.calculate_cancel_volume(previous_ask_volumes, ask_volumes)

            # 计算总挂单量
            total_volume = sum(bid_volumes) + sum(ask_volumes)

            if total_volume <= 0:
                return {
                    'bullish_behavior': False,
                    'absorption_index': 0.0,
                    'behavior_type': 'no_volume',
                    'behavior_description': '无挂单量',
                    'bid_cancel_volume': bid_cancel_volume,
                    'ask_cancel_volume': ask_cancel_volume
                }

            # 计算主力吸筹指数
            absorption_index = (ask_cancel_volume - bid_cancel_volume) / total_volume

            # 判断行为类型
            if absorption_index > 0.3:
                behavior_type = 'strong_absorption'
                behavior_description = f'强烈吸筹(指数{absorption_index:.2f})'
                bullish_behavior = True
            elif absorption_index > 0.1:
                behavior_type = 'moderate_absorption'
                behavior_description = f'温和吸筹(指数{absorption_index:.2f})'
                bullish_behavior = True
            elif absorption_index < -0.2:
                behavior_type = 'distribution'
                behavior_description = f'出货嫌疑(指数{absorption_index:.2f})'
                bullish_behavior = False
            else:
                behavior_type = 'neutral'
                behavior_description = f'中性行为(指数{absorption_index:.2f})'
                bullish_behavior = False

            return {
                'bullish_behavior': bullish_behavior,
                'absorption_index': absorption_index,
                'behavior_type': behavior_type,
                'behavior_description': behavior_description,
                'bid_cancel_volume': bid_cancel_volume,
                'ask_cancel_volume': ask_cancel_volume
            }

        except Exception as e:
            print(f"⚠️ 撤单行为分析失败 {code}: {e}")
            return {
                'bullish_behavior': False,
                'absorption_index': 0.0,
                'behavior_type': 'error',
                'behavior_description': f'分析失败: {e}',
                'bid_cancel_volume': 0,
                'ask_cancel_volume': 0
            }

    def calculate_cancel_volume(self, previous_volumes, current_volumes):
        """
        计算撤单量

        Args:
            previous_volumes: 之前的挂单量列表
            current_volumes: 当前的挂单量列表

        Returns:
            int: 撤单量（正数表示撤单，负数表示增单）
        """
        try:
            if len(previous_volumes) != len(current_volumes):
                return 0

            # 计算每档的撤单量，然后求和
            cancel_volume = 0
            for i in range(len(previous_volumes)):
                # 如果之前有挂单，现在减少了，就是撤单
                if previous_volumes[i] > current_volumes[i]:
                    cancel_volume += (previous_volumes[i] - current_volumes[i])

            return cancel_volume

        except Exception:
            return 0

    def get_previous_bid_volumes(self, code, seconds_ago=30):
        """获取N秒前的买盘五档数据"""
        try:
            cache = self.continuity_cache.get(code, {})
            bid_history = cache.get('bid_volumes_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(bid_history) != len(timestamp_history) or len(bid_history) < 2:
                return None

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的数据点
            closest_index = -1
            min_time_diff = float('inf')

            for i, timestamp in enumerate(timestamp_history):
                time_diff = abs(timestamp - target_time)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_index = i

            if closest_index >= 0 and min_time_diff <= 60:  # 允许60秒误差
                return bid_history[closest_index]

            return None

        except Exception as e:
            print(f"⚠️ 获取{seconds_ago}秒前买盘数据失败 {code}: {e}")
            return None

    def get_previous_ask_volumes(self, code, seconds_ago=30):
        """获取N秒前的卖盘五档数据"""
        try:
            cache = self.continuity_cache.get(code, {})
            ask_history = cache.get('ask_volumes_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(ask_history) != len(timestamp_history) or len(ask_history) < 2:
                return None

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的数据点
            closest_index = -1
            min_time_diff = float('inf')

            for i, timestamp in enumerate(timestamp_history):
                time_diff = abs(timestamp - target_time)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_index = i

            if closest_index >= 0 and min_time_diff <= 60:  # 允许60秒误差
                return ask_history[closest_index]

            return None

        except Exception as e:
            print(f"⚠️ 获取{seconds_ago}秒前卖盘数据失败 {code}: {e}")
            return None

    # ==================== 历史基准数据获取方法 ====================

    def get_yesterday_same_time_bid1(self, code):
        """获取昨日同期买一挂单量平均值"""
        try:
            current_time = datetime.now().time()

            # 从历史数据缓存中获取
            historical_data = self.historical_base_data.get(code, {})

            # 如果有技术指标数据，尝试从中获取
            tech_data = historical_data.get('technical_indicators', {})
            if tech_data and 'yesterday_same_time_bid1' in tech_data:
                return tech_data['yesterday_same_time_bid1']

            # 使用历史平均买一作为替代
            return self.get_historical_bid1_average(code)

        except Exception as e:
            print(f"⚠️ 获取昨日同期买一数据失败 {code}: {e}")
            return 0

    def get_historical_bid1_average(self, code):
        """获取历史买一挂单量平均值"""
        try:
            # 从持续性缓存中获取历史买一数据
            cache = self.continuity_cache.get(code, {})
            bid_history = cache.get('bid_volumes_history', [])

            if len(bid_history) >= 5:
                # 计算最近5次的买一平均值
                recent_bid1_list = [bid_volumes[0] for bid_volumes in bid_history[-5:] if bid_volumes]
                if recent_bid1_list:
                    return sum(recent_bid1_list) / len(recent_bid1_list)

            # 如果没有足够的历史数据，使用默认基准
            historical_data = self.historical_base_data.get(code, {})
            avg_volume_5d = historical_data.get('avg_volume_5d', 0)

            # 估算买一挂单量（通常为5日均量的1-3%）
            if avg_volume_5d > 0:
                estimated_bid1 = avg_volume_5d * 0.02  # 2%作为估算
                return estimated_bid1

            return 0

        except Exception as e:
            print(f"⚠️ 获取历史买一平均值失败 {code}: {e}")
            return 0

    def get_previous_bid5_total(self, code, seconds_ago=30):
        """获取N秒前的买五总厚度"""
        try:
            cache = self.continuity_cache.get(code, {})
            bid_history = cache.get('bid_volumes_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(bid_history) < 2 or len(timestamp_history) < 2:
                return 0

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的数据点
            closest_index = -1
            min_time_diff = float('inf')

            for i, timestamp in enumerate(timestamp_history):
                time_diff = abs(timestamp - target_time)
                if time_diff < min_time_diff:
                    min_time_diff = time_diff
                    closest_index = i

            if closest_index >= 0 and min_time_diff <= 60:  # 允许60秒误差
                bid_volumes = bid_history[closest_index]
                return sum(bid_volumes) if bid_volumes else 0

            return 0

        except Exception as e:
            print(f"⚠️ 获取{seconds_ago}秒前买五数据失败 {code}: {e}")
            return 0

    def get_historical_bid5_average(self, code):
        """获取历史买五总厚度平均值"""
        try:
            cache = self.continuity_cache.get(code, {})
            bid_history = cache.get('bid_volumes_history', [])

            if len(bid_history) >= 3:
                # 计算最近几次的买五总厚度平均值
                recent_bid5_totals = [sum(bid_volumes) for bid_volumes in bid_history[-3:] if bid_volumes]
                if recent_bid5_totals:
                    return sum(recent_bid5_totals) / len(recent_bid5_totals)

            # 使用估算值
            historical_data = self.historical_base_data.get(code, {})
            avg_volume_5d = historical_data.get('avg_volume_5d', 0)

            if avg_volume_5d > 0:
                # 估算买五总厚度（通常为5日均量的5-10%）
                estimated_bid5 = avg_volume_5d * 0.08  # 8%作为估算
                return estimated_bid5

            return 0

        except Exception as e:
            print(f"⚠️ 获取历史买五平均值失败 {code}: {e}")
            return 0

    def get_historical_bid345_average(self, code):
        """获取历史买三至买五的平均值"""
        try:
            cache = self.continuity_cache.get(code, {})
            bid_history = cache.get('bid_volumes_history', [])

            if len(bid_history) >= 3:
                # 计算最近几次的买三至买五平均值
                recent_bid345_totals = []
                for bid_volumes in bid_history[-3:]:
                    if bid_volumes and len(bid_volumes) >= 5:
                        bid345_total = sum(bid_volumes[2:5])  # 买三、买四、买五
                        recent_bid345_totals.append(bid345_total)

                if recent_bid345_totals:
                    return sum(recent_bid345_totals) / len(recent_bid345_totals)

            # 使用估算值
            historical_data = self.historical_base_data.get(code, {})
            avg_volume_5d = historical_data.get('avg_volume_5d', 0)

            if avg_volume_5d > 0:
                # 估算买三至买五总量（通常为5日均量的6-12%）
                estimated_bid345 = avg_volume_5d * 0.09  # 9%作为估算
                return estimated_bid345

            return 0

        except Exception as e:
            print(f"⚠️ 获取历史买三至买五平均值失败 {code}: {e}")
            return 0

    def get_recent_bid_total_history(self, code, minutes=5):
        """获取最近N分钟的买盘总厚度历史"""
        try:
            cache = self.continuity_cache.get(code, {})
            bid_history = cache.get('bid_volumes_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(bid_history) != len(timestamp_history):
                return []

            current_time = datetime.now().timestamp()
            cutoff_time = current_time - (minutes * 60)

            recent_bid_totals = []
            for i, timestamp in enumerate(timestamp_history):
                if timestamp >= cutoff_time:
                    bid_volumes = bid_history[i]
                    if bid_volumes:
                        bid_total = sum(bid_volumes)
                        recent_bid_totals.append(bid_total)

            return recent_bid_totals

        except Exception as e:
            print(f"⚠️ 获取最近买盘历史失败 {code}: {e}")
            return []

    def get_recent_price_history(self, code, minutes=5):
        """获取最近N分钟的价格历史"""
        try:
            cache = self.continuity_cache.get(code, {})
            price_history = cache.get('price_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(price_history) != len(timestamp_history):
                return []

            current_time = datetime.now().timestamp()
            cutoff_time = current_time - (minutes * 60)

            recent_prices = []
            for i, timestamp in enumerate(timestamp_history):
                if timestamp >= cutoff_time:
                    price = price_history[i]
                    if price > 0:
                        recent_prices.append(price)

            return recent_prices

        except Exception as e:
            print(f"⚠️ 获取最近价格历史失败 {code}: {e}")
            return []

    def get_recent_minute_data(self, code):
        """获取最近的分时数据用于形态分析"""
        try:
            # 尝试从enhanced_data_fetcher获取分时数据
            try:
                if hasattr(enhanced_stock_fetcher, 'get_minute_data'):
                    minute_data = enhanced_stock_fetcher.get_minute_data(code)
                    if minute_data is not None and not minute_data.empty:
                        return minute_data
            except Exception as e:
                print(f"   ⚠️ 从数据源获取分时数据失败: {e}")

            # 如果无法获取分时数据，使用价格历史模拟
            cache = self.continuity_cache.get(code, {})
            price_history = cache.get('price_history', [])
            timestamp_history = cache.get('timestamp_history', [])

            if len(price_history) >= 10 and len(timestamp_history) >= 10:
                # 构造简单的分时数据结构
                minute_data = pd.DataFrame({
                    'time': [datetime.fromtimestamp(ts) for ts in timestamp_history[-20:]],
                    'price': price_history[-20:],
                    'close': price_history[-20:]  # 使用价格作为收盘价
                })
                return minute_data

            # 如果没有足够的历史数据，返回None（形态检测将跳过）
            return None

        except Exception as e:
            print(f"⚠️ 获取分时数据失败 {code}: {e}")
            return None

    def identify_w_bottom_pattern(self, minute_data):
        """
        识别W底形态

        W底特征：
        1. 两个低点，中间有一个高点
        2. 两个低点价格相近（差异<2%）
        3. 中间高点形成颈线
        4. 右侧低点后有反弹

        Args:
            minute_data: 分时数据DataFrame

        Returns:
            dict: W底形态识别结果
        """
        try:
            if minute_data is None or len(minute_data) < 10:
                return {'has_pattern': False, 'reason': '数据不足'}

            # 获取价格序列
            if 'close' in minute_data.columns:
                prices = minute_data['close'].values
            elif 'price' in minute_data.columns:
                prices = minute_data['price'].values
            else:
                return {'has_pattern': False, 'reason': '无价格数据'}

            # 寻找局部极值点
            lows = []
            highs = []

            for i in range(2, len(prices) - 2):
                # 局部低点：比前后两个点都低
                if (prices[i] < prices[i-1] and prices[i] < prices[i-2] and
                    prices[i] < prices[i+1] and prices[i] < prices[i+2]):
                    lows.append((i, prices[i]))

                # 局部高点：比前后两个点都高
                if (prices[i] > prices[i-1] and prices[i] > prices[i-2] and
                    prices[i] > prices[i+1] and prices[i] > prices[i+2]):
                    highs.append((i, prices[i]))

            # 需要至少2个低点和1个高点
            if len(lows) < 2 or len(highs) < 1:
                return {'has_pattern': False, 'reason': '极值点不足'}

            # 寻找W底模式：低点-高点-低点
            for i in range(len(lows) - 1):
                left_low_idx, left_low_price = lows[i]

                for j in range(i + 1, len(lows)):
                    right_low_idx, right_low_price = lows[j]

                    # 检查两个低点之间是否有高点
                    middle_highs = [h for h in highs if left_low_idx < h[0] < right_low_idx]

                    if not middle_highs:
                        continue

                    # 选择最高的中间高点作为颈线
                    neckline_idx, neckline_price = max(middle_highs, key=lambda x: x[1])

                    # 检查W底条件
                    # 1. 两个低点价格相近（差异<3%）
                    price_diff_pct = abs(left_low_price - right_low_price) / min(left_low_price, right_low_price)
                    if price_diff_pct > 0.03:
                        continue

                    # 2. 颈线高于两个低点
                    if neckline_price <= max(left_low_price, right_low_price):
                        continue

                    # 3. 右侧低点后有反弹迹象
                    if right_low_idx < len(prices) - 3:
                        recent_prices = prices[right_low_idx:right_low_idx+3]
                        if max(recent_prices) <= right_low_price * 1.005:  # 至少反弹0.5%
                            continue

                    # 找到有效的W底形态
                    pattern_low = min(left_low_price, right_low_price)

                    return {
                        'has_pattern': True,
                        'neckline_price': neckline_price,
                        'pattern_low': pattern_low,
                        'left_low': {'index': left_low_idx, 'price': left_low_price},
                        'right_low': {'index': right_low_idx, 'price': right_low_price},
                        'neckline': {'index': neckline_idx, 'price': neckline_price},
                        'min_low': min(prices[right_low_idx:]),  # 右侧最低点
                        'price_diff_pct': price_diff_pct
                    }

            return {'has_pattern': False, 'reason': '未找到有效W底形态'}

        except Exception as e:
            print(f"⚠️ W底形态识别失败: {e}")
            return {'has_pattern': False, 'reason': f'识别失败: {e}'}

    # ==================== 全时段监控策略 ====================

    def get_time_based_strategy(self, current_time=None):
        """
        获取基于时间的监控策略

        时段专属策略：
        - 9:30-9:40：开盘量能监控
        - 9:40-10:30：均价征服监控
        - 10:30-11:30：盘中突击监控
        - 13:00-14:00：午盘冲锋监控

        Args:
            current_time: 当前时间，默认为系统当前时间

        Returns:
            dict: 时段策略配置
        """
        if current_time is None:
            current_time = datetime.now().time()

        strategies = {
            "9:30-9:40": {
                "name": "开盘量能监控",
                "focus": "开盘量能",
                "key_indicators": ["volume_ratio", "first_trade_size", "bid5_thickness"],
                "thresholds": {
                    "volume_ratio": 5.0,        # 量比>5
                    "bid5_thickness_ratio": 2.0  # 买五厚度>昨日2倍
                },
                "scan_interval": 15,  # 15秒扫描
                "description": "关注开盘量能爆发和买盘厚度"
            },
            "9:40-10:30": {
                "name": "均价征服监控",
                "focus": "均价征服",
                "key_indicators": ["avg_price_conquest", "price_stability", "bid1_continuous"],
                "thresholds": {
                    "above_avg_duration": 10,    # 站稳均价线>10分钟
                    "bid1_growth": 1.5          # 买一量持续增加
                },
                "scan_interval": 30,  # 30秒扫描
                "description": "关注价格站稳均价线和买一量持续增加"
            },
            "10:30-11:30": {
                "name": "盘中突击监控",
                "focus": "盘中突击",
                "key_indicators": ["price_acceleration", "volume_surge", "large_orders"],
                "thresholds": {
                    "price_speed": 0.005,        # 涨速>0.5%/30秒
                    "large_order_count": 3       # 连续3笔千手单
                },
                "scan_interval": 15,  # 15秒扫描
                "description": "关注价格加速上涨和大单连续出现"
            },
            "13:00-14:00": {
                "name": "午盘冲锋监控",
                "focus": "午盘冲锋",
                "key_indicators": ["morning_high_break", "pressure_ratio", "volume_recovery"],
                "thresholds": {
                    "morning_high_break": True,  # 突破早盘高点
                    "pressure_ratio": 0.7        # 卖压<买压30%
                },
                "scan_interval": 20,  # 20秒扫描
                "description": "关注突破早盘高点和卖压减弱"
            }
        }

        # 查找当前时间对应的策略
        for time_range, strategy in strategies.items():
            start_str, end_str = time_range.split("-")
            start_time = datetime.strptime(start_str, "%H:%M").time()
            end_time = datetime.strptime(end_str, "%H:%M").time()

            if start_time <= current_time <= end_time:
                strategy['time_range'] = time_range
                strategy['is_active'] = True
                return strategy

        # 如果不在特定时段，返回常规策略
        return {
            "name": "常规监控",
            "focus": "常规监控",
            "key_indicators": ["basic_signals"],
            "thresholds": {},
            "scan_interval": 30,
            "time_range": "其他时段",
            "is_active": False,
            "description": "常规监控模式"
        }

    def apply_time_based_analysis(self, code, stock_data, strategy):
        """
        应用基于时间的专属分析

        Args:
            code: 股票代码
            stock_data: 股票实时数据
            strategy: 时段策略配置

        Returns:
            dict: 时段专属分析结果
        """
        try:
            strategy_name = strategy.get('name', '未知策略')
            focus = strategy.get('focus', '常规监控')

            if focus == "开盘量能":
                return self.analyze_opening_volume_strategy(code, stock_data, strategy)
            elif focus == "均价征服":
                return self.analyze_avg_price_conquest_strategy(code, stock_data, strategy)
            elif focus == "盘中突击":
                return self.analyze_intraday_surge_strategy(code, stock_data, strategy)
            elif focus == "午盘冲锋":
                return self.analyze_afternoon_charge_strategy(code, stock_data, strategy)
            else:
                return self.analyze_general_strategy(code, stock_data, strategy)

        except Exception as e:
            print(f"⚠️ 时段专属分析失败 {code}: {e}")
            return {
                'strategy_signal': False,
                'strategy_name': strategy.get('name', '未知'),
                'signal_strength': 0.0,
                'details': {'error': str(e)}
            }

    def analyze_opening_volume_strategy(self, code, stock_data, strategy):
        """开盘量能策略分析（9:30-9:40）"""
        try:
            thresholds = strategy.get('thresholds', {})

            # 获取量比
            volume_ratio = self.calculate_current_volume_ratio(code, stock_data)

            # 获取首笔成交量
            first_trade_size = self.get_first_trade_size(code)

            # 获取买五厚度比例
            bid5_thickness_ratio = self.calculate_bid5_thickness_ratio(code, stock_data)

            # 判断信号
            volume_signal = volume_ratio > thresholds.get('volume_ratio', 5.0)
            thickness_signal = bid5_thickness_ratio > thresholds.get('bid5_thickness_ratio', 2.0)
            first_trade_signal = first_trade_size > 10000  # 首笔>万手

            signal_count = sum([volume_signal, thickness_signal, first_trade_signal])
            strategy_signal = signal_count >= 2  # 至少满足2个条件

            return {
                'strategy_signal': strategy_signal,
                'strategy_name': '开盘量能监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'volume_ratio': volume_ratio,
                    'volume_signal': volume_signal,
                    'bid5_thickness_ratio': bid5_thickness_ratio,
                    'thickness_signal': thickness_signal,
                    'first_trade_size': first_trade_size,
                    'first_trade_signal': first_trade_signal
                }
            }

        except Exception as e:
            return {'strategy_signal': False, 'strategy_name': '开盘量能监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_avg_price_conquest_strategy(self, code, stock_data, strategy):
        """均价征服策略分析（9:40-10:30）"""
        try:
            thresholds = strategy.get('thresholds', {})

            # 检查是否站稳均价线
            above_avg_duration = self.get_above_avg_price_duration(code)

            # 检查买一量是否持续增加
            bid1_growth_trend = self.check_bid1_growth_trend(code)

            # 检查价格稳定性
            price_stability = self.check_price_stability(code, minutes=10)

            # 判断信号
            avg_conquest_signal = above_avg_duration > thresholds.get('above_avg_duration', 10)
            bid1_signal = bid1_growth_trend > thresholds.get('bid1_growth', 1.5)
            stability_signal = price_stability

            signal_count = sum([avg_conquest_signal, bid1_signal, stability_signal])
            strategy_signal = signal_count >= 2

            return {
                'strategy_signal': strategy_signal,
                'strategy_name': '均价征服监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'above_avg_duration': above_avg_duration,
                    'avg_conquest_signal': avg_conquest_signal,
                    'bid1_growth_trend': bid1_growth_trend,
                    'bid1_signal': bid1_signal,
                    'price_stability': price_stability,
                    'stability_signal': stability_signal
                }
            }

        except Exception as e:
            return {'strategy_signal': False, 'strategy_name': '均价征服监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_intraday_surge_strategy(self, code, stock_data, strategy):
        """盘中突击策略分析（10:30-11:30）"""
        try:
            thresholds = strategy.get('thresholds', {})

            # 计算涨速
            price_speed = self.calculate_price_acceleration(code, seconds=30)

            # 检查大单连续性
            large_order_count = self.count_recent_large_orders(code, minutes=5)

            # 检查量能突增
            volume_surge = self.check_volume_surge(code, stock_data)

            # 判断信号
            speed_signal = price_speed > thresholds.get('price_speed', 0.005)
            large_order_signal = large_order_count >= thresholds.get('large_order_count', 3)
            volume_signal = volume_surge

            signal_count = sum([speed_signal, large_order_signal, volume_signal])
            strategy_signal = signal_count >= 2

            return {
                'strategy_signal': strategy_signal,
                'strategy_name': '盘中突击监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'price_speed': price_speed,
                    'speed_signal': speed_signal,
                    'large_order_count': large_order_count,
                    'large_order_signal': large_order_signal,
                    'volume_surge': volume_surge,
                    'volume_signal': volume_signal
                }
            }

        except Exception as e:
            return {'strategy_signal': False, 'strategy_name': '盘中突击监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_afternoon_charge_strategy(self, code, stock_data, strategy):
        """午盘冲锋策略分析（13:00-14:00）"""
        try:
            thresholds = strategy.get('thresholds', {})

            # 检查是否突破早盘高点
            morning_high_break = self.check_morning_high_breakthrough(code, stock_data)

            # 计算买卖压力比
            pressure_ratio = self.calculate_buy_sell_pressure_ratio(code, stock_data)

            # 检查量能恢复
            volume_recovery = self.check_afternoon_volume_recovery(code, stock_data)

            # 判断信号
            breakthrough_signal = morning_high_break
            pressure_signal = pressure_ratio < thresholds.get('pressure_ratio', 0.7)
            recovery_signal = volume_recovery

            signal_count = sum([breakthrough_signal, pressure_signal, recovery_signal])
            strategy_signal = signal_count >= 2

            return {
                'strategy_signal': strategy_signal,
                'strategy_name': '午盘冲锋监控',
                'signal_strength': signal_count / 3.0,
                'details': {
                    'morning_high_break': morning_high_break,
                    'breakthrough_signal': breakthrough_signal,
                    'pressure_ratio': pressure_ratio,
                    'pressure_signal': pressure_signal,
                    'volume_recovery': volume_recovery,
                    'recovery_signal': recovery_signal
                }
            }

        except Exception as e:
            return {'strategy_signal': False, 'strategy_name': '午盘冲锋监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    def analyze_general_strategy(self, code, stock_data, strategy):
        """常规策略分析（其他时段）"""
        try:
            # 简化的常规分析
            return {
                'strategy_signal': False,
                'strategy_name': '常规监控',
                'signal_strength': 0.0,
                'details': {
                    'message': '非关键时段，使用常规监控'
                }
            }

        except Exception as e:
            return {'strategy_signal': False, 'strategy_name': '常规监控', 'signal_strength': 0.0, 'details': {'error': str(e)}}

    # ==================== 买入信号判断和通知系统 ====================

    def detect_limit_up_signals(self, code, stock_data):
        """
        连板潜力股信号检测主函数

        整合所有分析结果，生成最终的买入信号判断

        Args:
            code: 股票代码
            stock_data: 股票实时数据

        Returns:
            dict: 综合信号分析结果
        """
        try:
            # 1. 开盘类型分类
            open_type_result = self.classify_open_type(stock_data)
            open_type = open_type_result['type']

            # 2. 根据开盘类型选择对应算法（支持转换型）
            base_type = open_type.replace('_converted', '')  # 移除转换标识获取基础类型

            if base_type == 'flat_open':
                # 平开股分析（包括转换平开股）
                core_signals = self.analyze_flat_open_stock(code, stock_data)
                if open_type.endswith('_converted'):
                    algorithm_type = '转换平开股算法'
                    # 转换型平开股给予额外信号强度加成
                    core_signals['signal_strength'] = min(1.0, core_signals.get('signal_strength', 0) + 0.1)
                else:
                    algorithm_type = '平开股算法'

            elif base_type == 'high_open':
                # 高开股分析（包括转换高开股）
                core_signals = self.analyze_high_open_stock(code, stock_data)
                if open_type.endswith('_converted'):
                    algorithm_type = '转换高开股算法'
                    # 转换型高开股给予额外信号强度加成
                    core_signals['signal_strength'] = min(1.0, core_signals.get('signal_strength', 0) + 0.15)
                else:
                    algorithm_type = '高开股算法'

            elif base_type == 'low_open':
                # 🚀 新增：低开股分析
                core_signals = self.analyze_low_open_stock(code, stock_data)
                algorithm_type = '低开反转算法'

            elif open_type_result.get('is_converted', False) and open_type_result.get('dynamic_type') in ['flat_open', 'high_open']:
                # 特殊处理：其他类型转换为平开/高开的情况
                dynamic_type = open_type_result['dynamic_type']
                if dynamic_type == 'flat_open':
                    core_signals = self.analyze_flat_open_stock(code, stock_data)
                    algorithm_type = '逆转平开股算法'
                    # 逆转型给予更大的信号强度加成
                    core_signals['signal_strength'] = min(1.0, core_signals.get('signal_strength', 0) + 0.2)
                else:  # high_open
                    core_signals = self.analyze_high_open_stock(code, stock_data)
                    algorithm_type = '逆转高开股算法'
                    # 逆转型给予更大的信号强度加成
                    core_signals['signal_strength'] = min(1.0, core_signals.get('signal_strength', 0) + 0.25)
            elif open_type_result['current_change_pct'] < 0:
                # 特殊处理：开盘下跌反转监控
                reversal_analysis = self.analyze_opening_drop_reversal(code, stock_data)
                if reversal_analysis.get('reversal_signal', False):
                    # 检测到反转信号，使用反转算法
                    core_signals = {
                        'buy_signal': True,
                        'signal_strength': reversal_analysis['signal_strength'],
                        'recommendation': reversal_analysis['recommendation'],
                        'reversal_analysis': reversal_analysis
                    }
                    algorithm_type = '开盘下跌反转算法'
                else:
                    # 下跌但无反转信号，继续观察
                    return {
                        'buy_signal': False,
                        'signal_strength': reversal_analysis['signal_strength'],
                        'recommendation': reversal_analysis['recommendation'],
                        'algorithm_type': '下跌观察算法',
                        'open_type_result': open_type_result,
                        'reversal_analysis': reversal_analysis,
                        'reason': '开盘下跌，等待反转信号'
                    }
            else:
                # 其他类型，不适合连板监控
                return {
                    'buy_signal': False,
                    'signal_strength': 0.0,
                    'recommendation': '⏳ 不适合连板监控',
                    'algorithm_type': '观望算法',
                    'open_type_result': open_type_result,
                    'reason': f'{open_type_result["type_name"]}不在连板监控范围内'
                }

            # 3. 实时盘口监控
            depth_analysis = self.analyze_realtime_depth(code, stock_data)

            # 4. 全时段策略分析
            current_strategy = self.get_time_based_strategy()
            strategy_analysis = self.apply_time_based_analysis(code, stock_data, current_strategy)

            # 5. 综合信号强度计算
            signal_components = {
                'core_signals': core_signals.get('signal_strength', 0.0) * 0.5,      # 核心信号50%
                'depth_analysis': depth_analysis.get('signal_strength', 0.0) * 0.3,  # 盘口分析30%
                'strategy_analysis': strategy_analysis.get('signal_strength', 0.0) * 0.2  # 时段策略20%
            }

            total_signal_strength = sum(signal_components.values())

            # 6. 买入信号判断
            buy_signal = self.judge_buy_signal(core_signals, depth_analysis, strategy_analysis, total_signal_strength)

            # 7. 生成最终建议
            recommendation = self.generate_final_recommendation(buy_signal, total_signal_strength, open_type)

            # 8. 动态仓位计算
            position_size = self.calculate_dynamic_position(total_signal_strength, open_type_result['current_change_pct'])

            # 9. 如果满足买入条件，发送通知
            if buy_signal:
                self.send_buy_signal_notification(code, stock_data, recommendation, total_signal_strength)

            return {
                'buy_signal': buy_signal,
                'signal_strength': total_signal_strength,
                'recommendation': recommendation,
                'position_size': position_size,
                'algorithm_type': algorithm_type,
                'open_type_result': open_type_result,
                'core_signals': core_signals,
                'depth_analysis': depth_analysis,
                'strategy_analysis': strategy_analysis,
                'current_strategy': current_strategy,
                'signal_components': signal_components,
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            print(f"⚠️ 连板信号检测失败 {code}: {e}")
            return {
                'buy_signal': False,
                'signal_strength': 0.0,
                'recommendation': '❌ 分析失败',
                'algorithm_type': '错误',
                'error': str(e)
            }

    def judge_buy_signal(self, core_signals, depth_analysis, strategy_analysis, total_strength):
        """
        买入信号综合判断

        判断逻辑：
        1. 核心信号必须为True（平开股或高开股的三大信号同时满足）
        2. 总信号强度 >= 0.7
        3. 盘口分析或时段策略至少一个为正面

        Args:
            core_signals: 核心信号分析结果
            depth_analysis: 盘口分析结果
            strategy_analysis: 策略分析结果
            total_strength: 总信号强度

        Returns:
            bool: 是否发出买入信号
        """
        try:
            # 条件1：核心信号必须满足
            core_buy_signal = core_signals.get('buy_signal', False)

            # 条件2：总信号强度足够
            strength_sufficient = total_strength >= 0.7

            # 条件3：盘口或策略至少一个正面
            depth_positive = depth_analysis.get('overall_signal', False)
            strategy_positive = strategy_analysis.get('strategy_signal', False)
            supporting_positive = depth_positive or strategy_positive

            # 综合判断
            buy_signal = core_buy_signal and strength_sufficient and supporting_positive

            return buy_signal

        except Exception as e:
            print(f"⚠️ 买入信号判断失败: {e}")
            return False

    def generate_final_recommendation(self, buy_signal, signal_strength, open_type):
        """
        生成最终操作建议

        Args:
            buy_signal: 买入信号
            signal_strength: 信号强度
            open_type: 开盘类型

        Returns:
            str: 操作建议
        """
        try:
            if buy_signal:
                if signal_strength >= 0.9:
                    return "🚀 强烈买入"
                elif signal_strength >= 0.8:
                    return "🚀 立即买入"
                else:
                    return "🚀 建议买入"
            else:
                if signal_strength >= 0.6:
                    return "👀 密切关注"
                elif signal_strength >= 0.4:
                    return "📝 继续观察"
                elif signal_strength >= 0.2:
                    return "⏳ 暂时观望"
                else:
                    return "❌ 暂不关注"

        except Exception:
            return "❓ 建议未知"

    def calculate_dynamic_position(self, signal_strength, current_gain):
        """
        动态仓位计算公式

        基础仓位 = 30%
        信号强度修正 = 信号强度 * 60%
        涨幅修正 = max(0, (当前涨幅 - 5%) * 3%)

        最终仓位 = min(90%, 基础仓位 + 信号强度修正 - 涨幅修正)

        Args:
            signal_strength: 信号强度 (0-1)
            current_gain: 当前涨幅 (百分比)

        Returns:
            float: 建议仓位比例 (0-1)
        """
        try:
            base_position = 0.3  # 基础仓位30%

            # 信号强度修正
            signal_bonus = signal_strength * 0.6

            # 涨幅修正（超过5%开始减仓）
            gain_penalty = max(0, (current_gain - 5.0) * 0.03)

            # 计算最终仓位
            final_position = base_position + signal_bonus - gain_penalty

            # 限制在0-90%范围内
            final_position = max(0.0, min(0.9, final_position))

            return final_position

        except Exception:
            return 0.3  # 默认30%仓位

    def send_buy_signal_notification(self, code, stock_data, recommendation, signal_strength):
        """
        发送买入信号通知

        Args:
            code: 股票代码
            stock_data: 股票数据
            recommendation: 操作建议
            signal_strength: 信号强度
        """
        try:
            if not NOTIFICATION_AVAILABLE:
                print(f"⚠️ 通知功能不可用，无法发送 {code} 买入信号")
                return

            # 获取股票基本信息
            stock_name = stock_data.get('stock_name', f'股票{code}')
            current_price = stock_data.get('current_price', 0)
            change_pct = stock_data.get('change_pct', 0)

            # 构建通知内容
            title = "🚀 连板潜力股买入信号"

            message = f"""
{code} {stock_name}
当前价格: {current_price:.2f}元
涨跌幅: {change_pct:+.2f}%
信号强度: {signal_strength:.1%}
操作建议: {recommendation}
时间: {datetime.now().strftime('%H:%M:%S')}
            """.strip()

            # 发送通知
            toaster.show_toast(
                title,
                message,
                duration=self.config.get('notification_duration', 30),
                icon_path=None,
                threaded=True
            )

            print(f"✅ 已发送买入信号通知: {code} {stock_name}")

        except Exception as e:
            print(f"⚠️ 发送通知失败 {code}: {e}")

    def send_general_notification(self, title, message, duration=10):
        """
        发送通用通知

        Args:
            title: 通知标题
            message: 通知内容
            duration: 持续时间（秒）
        """
        try:
            if NOTIFICATION_AVAILABLE:
                toaster.show_toast(
                    title,
                    message,
                    duration=duration,
                    icon_path=None,
                    threaded=True
                )
                print(f"✅ 已发送通知: {title}")
            else:
                print(f"⚠️ 通知功能不可用: {title} - {message}")

        except Exception as e:
            print(f"⚠️ 发送通知失败: {e}")

    # ==================== 历史数据管理系统 ====================

    def auto_check_and_download_historical_data(self):
        """应用启动时自动检查和下载历史数据"""
        try:
            if not self.monitored_stocks:
                print("📝 暂无监控股票，跳过历史数据检查")
                return

            print(f"🔍 开始检查 {len(self.monitored_stocks)} 只股票的历史数据完整性...")

            need_download = []
            for code, stock_info in self.monitored_stocks.items():
                if not self.check_historical_data_completeness(code):
                    need_download.append(code)
                    stock_info['initialized'] = False
                else:
                    # 🚀 关键修复：历史数据完整时，加载到内存中
                    if self.load_historical_data_from_cache(code):
                        stock_info['initialized'] = True
                        print(f"   ✅ {code} {stock_info['name']} 历史数据完整")
                    else:
                        print(f"   ⚠️ {code} {stock_info['name']} 历史数据加载失败")
                        need_download.append(code)
                        stock_info['initialized'] = False

            if need_download:
                print(f"📥 需要下载历史数据的股票: {len(need_download)} 只")
                self.download_historical_data_batch(need_download)
            else:
                print("✅ 所有股票历史数据完整，无需下载")

        except Exception as e:
            print(f"❌ 历史数据检查失败: {e}")

    def check_historical_data_completeness(self, code):
        """检查单只股票的历史数据完整性"""
        try:
            # 检查缓存文件是否存在
            cache_file = self.data_cache_dir / f"{code}_historical_data.json"
            if not cache_file.exists():
                return False

            # 检查缓存文件是否过期（超过1天）
            file_time = datetime.fromtimestamp(cache_file.stat().st_mtime)
            if datetime.now() - file_time > timedelta(days=1):
                print(f"   ⚠️ {code} 历史数据缓存过期")
                return False

            # 检查缓存内容完整性
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)

                required_fields = ['kline_30d', 'prev_close', 'avg_volume_5d', 'ma20', 'cache_time']
                if not all(field in cache_data for field in required_fields):
                    print(f"   ⚠️ {code} 历史数据缓存不完整")
                    return False

                return True

            except json.JSONDecodeError as e:
                print(f"   ⚠️ {code} 历史数据缓存文件损坏: {e}")
                # 删除损坏的缓存文件
                try:
                    cache_file.unlink()
                    print(f"   🗑️ 已删除损坏的缓存文件: {code}")
                except:
                    pass
                return False

        except Exception as e:
            print(f"   ❌ {code} 历史数据检查异常: {e}")
            return False

    def download_historical_data_batch(self, stock_codes):
        """批量下载历史数据（增强版：支持完整性检查）"""
        try:
            success_count = 0
            total_count = len(stock_codes)

            print(f"📊 开始批量检查/下载 {total_count} 只股票的历史数据...")

            for i, code in enumerate(stock_codes, 1):
                try:
                    stock_name = self.monitored_stocks[code]['name']
                    print(f"📥 [{i}/{total_count}] 检查 {code} {stock_name} 历史数据...")

                    # 1. 首先检查数据完整性
                    if self.check_historical_data_integrity(code):
                        print(f"   ✅ {code} 历史数据完整，加载到内存")
                        # 🚀 关键修复：将完整的历史数据加载到内存中
                        if self.load_historical_data_from_cache(code):
                            self.monitored_stocks[code]['initialized'] = True
                            success_count += 1
                        else:
                            print(f"   ⚠️ {code} 历史数据加载失败，重新下载")
                            if self.download_single_stock_historical_data(code):
                                self.monitored_stocks[code]['initialized'] = True
                                success_count += 1
                        continue

                    # 2. 数据不完整，重新下载
                    print(f"   🔄 {code} 历史数据不完整，重新下载...")
                    if self.download_single_stock_historical_data(code):
                        self.monitored_stocks[code]['initialized'] = True
                        success_count += 1
                        print(f"   ✅ {code} 历史数据下载完成")
                    else:
                        print(f"   ❌ {code} 历史数据下载失败")

                    # 避免API限流
                    time.sleep(1)

                except Exception as e:
                    print(f"   ❌ {code} 处理异常: {e}")
                    continue

            print(f"📥 历史数据处理完成: 成功 {success_count}/{total_count} 只股票")

            # 保存配置（更新初始化状态）
            self.save_config()

        except Exception as e:
            print(f"❌ 批量处理历史数据失败: {e}")

    def check_historical_data_integrity(self, code):
        """
        检查历史数据完整性

        Args:
            code: 股票代码

        Returns:
            bool: 数据是否完整
        """
        try:
            cache_file = self.data_cache_dir / f"{code}_historical.json"

            # 1. 检查文件是否存在
            if not cache_file.exists():
                return False

            # 2. 检查文件是否为空或损坏
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            except (json.JSONDecodeError, IOError):
                print(f"   ⚠️ {code} 历史数据文件损坏")
                return False

            # 3. 检查数据结构完整性
            required_keys = ['k_line_data', 'volume_data', 'price_stats']
            if not all(key in data for key in required_keys):
                print(f"   ⚠️ {code} 历史数据结构不完整")
                return False

            # 4. 检查K线数据数量（至少需要30天数据）
            k_line_data = data.get('k_line_data', [])
            if len(k_line_data) < 20:  # 至少20个交易日
                print(f"   ⚠️ {code} K线数据不足: {len(k_line_data)}条")
                return False

            # 5. 检查数据时效性（是否包含最近的数据）
            if k_line_data:
                latest_date = k_line_data[-1].get('date', '')
                if latest_date:
                    try:
                        latest_datetime = datetime.strptime(latest_date, '%Y-%m-%d')
                        days_ago = (datetime.now() - latest_datetime).days
                        if days_ago > 7:  # 数据超过7天认为过期
                            print(f"   ⚠️ {code} 历史数据过期: {days_ago}天前")
                            return False
                    except ValueError:
                        print(f"   ⚠️ {code} 日期格式错误: {latest_date}")
                        return False

            # 6. 检查基础统计数据
            volume_data = data.get('volume_data', {})
            if not volume_data.get('avg_volume_5d') or not volume_data.get('avg_volume_10d'):
                print(f"   ⚠️ {code} 成交量统计数据缺失")
                return False

            return True

        except Exception as e:
            print(f"   ⚠️ {code} 数据完整性检查失败: {e}")
            return False

    def download_single_stock_historical_data(self, code):
        """下载单只股票的历史数据"""
        try:
            historical_data = {}

            # 1. 获取30天K线数据
            print(f"   🔄 获取 {code} K线数据...")
            kline_data = enhanced_stock_fetcher.get_daily_kline(code, 30)

            if not kline_data.empty and len(kline_data) >= 2:
                historical_data['kline_30d'] = kline_data.to_dict('records')

                # 提取关键数据
                close_col = '收盘' if '收盘' in kline_data.columns else 'close'
                volume_col = '成交量' if '成交量' in kline_data.columns else 'volume'

                historical_data['prev_close'] = float(kline_data.iloc[-2][close_col])
                historical_data['prev_high'] = float(kline_data.iloc[-2].get('最高', kline_data.iloc[-2].get('high', 0)))
                historical_data['prev_low'] = float(kline_data.iloc[-2].get('最低', kline_data.iloc[-2].get('low', 0)))
                historical_data['prev_open'] = float(kline_data.iloc[-2].get('开盘', kline_data.iloc[-2].get('open', 0)))

                # 计算成交量基准
                if volume_col in kline_data.columns:
                    historical_data['avg_volume_5d'] = float(kline_data[volume_col].tail(5).mean())
                    historical_data['avg_volume_10d'] = float(kline_data[volume_col].tail(10).mean())
                    historical_data['avg_volume_30d'] = float(kline_data[volume_col].tail(30).mean())

                # 计算价格基准
                if close_col in kline_data.columns:
                    historical_data['ma5'] = float(kline_data[close_col].tail(5).mean())
                    historical_data['ma10'] = float(kline_data[close_col].tail(10).mean())
                    historical_data['ma20'] = float(kline_data[close_col].tail(20).mean())

            else:
                print(f"   ⚠️ {code} K线数据不足")
                return False

            # 2. 获取技术指标数据（如果需要）
            print(f"   🔄 获取 {code} 技术指标...")
            try:
                # 检查方法是否存在
                if hasattr(enhanced_stock_fetcher, 'get_technical_analysis'):
                    tech_data = enhanced_stock_fetcher.get_technical_analysis(code)
                    if tech_data and tech_data.get('indicators_available', False):
                        historical_data['technical_indicators'] = tech_data
                else:
                    print(f"   ℹ️ {code} 技术指标功能不可用，跳过")
            except Exception as e:
                print(f"   ⚠️ {code} 技术指标获取失败: {e}")

            # 3. 保存到缓存文件
            historical_data['cache_time'] = datetime.now().isoformat()
            historical_data['stock_code'] = code
            historical_data['stock_name'] = self.monitored_stocks[code]['name']

            # 处理DataFrame和Timestamp对象的JSON序列化
            historical_data = self.prepare_data_for_json(historical_data)

            cache_file = self.data_cache_dir / f"{code}_historical_data.json"
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(historical_data, f, ensure_ascii=False, indent=2, default=str)

            # 4. 加载到内存缓存
            self.historical_base_data[code] = historical_data

            return True

        except Exception as e:
            print(f"   ❌ {code} 历史数据下载失败: {e}")
            return False

    def load_historical_data_from_cache(self, code):
        """从缓存文件加载历史数据到内存"""
        try:
            cache_file = self.data_cache_dir / f"{code}_historical_data.json"
            if cache_file.exists():
                try:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        historical_data = json.load(f)
                    self.historical_base_data[code] = historical_data
                    print(f"   ✅ {code} 历史数据缓存加载成功")
                    return True
                except json.JSONDecodeError as e:
                    print(f"   ⚠️ {code} 缓存文件损坏，删除重建: {e}")
                    cache_file.unlink()
                    return False
            return False
        except Exception as e:
            print(f"❌ {code} 历史数据缓存加载失败: {e}")
            return False

    def prepare_data_for_json(self, data):
        """准备数据用于JSON序列化，处理DataFrame和Timestamp对象"""
        try:
            if isinstance(data, dict):
                result = {}
                for key, value in data.items():
                    result[key] = self.prepare_data_for_json(value)
                return result
            elif isinstance(data, list):
                return [self.prepare_data_for_json(item) for item in data]
            elif isinstance(data, pd.DataFrame):
                # 将DataFrame转换为字典列表
                return data.to_dict('records')
            elif isinstance(data, (pd.Timestamp, datetime)):
                # 将时间戳转换为ISO格式字符串
                return data.isoformat() if hasattr(data, 'isoformat') else str(data)
            elif isinstance(data, (pd.Series, pd.Index)):
                # 将Series转换为列表
                return data.tolist()
            elif pd.isna(data):
                # 处理NaN值
                return None
            else:
                return data
        except Exception as e:
            print(f"⚠️ 数据序列化处理失败: {e}")
            return str(data)

    # ==================== 数据获取和缓存系统 ====================

    def initialize_stock_monitoring(self, code):
        """
        初始化股票监控（优先使用缓存的历史数据）

        Args:
            code: 股票代码

        Returns:
            bool: 初始化是否成功
        """
        print(f"📊 初始化 {code} 的监控数据...")

        try:
            # 1. 优先从缓存加载历史数据
            if not self.load_historical_data_from_cache(code):
                print(f"   🔄 缓存不可用，重新下载 {code} 历史数据...")
                if not self.download_single_stock_historical_data(code):
                    print(f"   ❌ {code} 历史数据获取失败")
                    return False

            # 2. 初始化持续性监控缓存
            self.continuity_cache[code] = {
                'bid_volumes_history': [],      # 买盘量历史
                'ask_volumes_history': [],      # 卖盘量历史
                'price_history': [],            # 价格历史
                'volume_ratio_history': [],     # 量比历史
                'timestamp_history': [],        # 时间戳历史
                'last_update': None
            }

            print(f"✅ {code} 监控数据初始化完成")
            return True

        except Exception as e:
            print(f"❌ {code} 监控数据初始化失败: {e}")
            return False

    def update_realtime_data_cache(self, code, stock_data):
        """
        更新实时数据缓存

        Args:
            code: 股票代码
            stock_data: 实时股票数据
        """
        try:
            current_time = datetime.now()

            # 更新实时数据缓存
            self.realtime_data_cache[code] = {
                'stock_data': stock_data,
                'update_time': current_time,
                'timestamp': current_time.timestamp()
            }

            # 更新持续性监控缓存
            if code in self.continuity_cache:
                cache = self.continuity_cache[code]

                # 添加当前数据到历史记录
                cache['bid_volumes_history'].append(stock_data.get('bid_volumes', [0] * 5))
                cache['ask_volumes_history'].append(stock_data.get('ask_volumes', [0] * 5))
                cache['price_history'].append(stock_data.get('current_price', 0))
                cache['timestamp_history'].append(current_time.timestamp())

                # 计算并添加量比
                volume_ratio = self.calculate_current_volume_ratio(code, stock_data)
                cache['volume_ratio_history'].append(volume_ratio)

                # 保持历史记录长度（最多保留30分钟的数据，每30秒一次，共60条）
                max_history_length = 60
                for key in ['bid_volumes_history', 'ask_volumes_history', 'price_history',
                           'volume_ratio_history', 'timestamp_history']:
                    if len(cache[key]) > max_history_length:
                        cache[key] = cache[key][-max_history_length:]

                cache['last_update'] = current_time

        except Exception as e:
            print(f"⚠️ 更新实时数据缓存失败 {code}: {e}")

    # ==================== 数据获取辅助方法 ====================

    def get_historical_bid1_average(self, code):
        """获取历史买一挂单量平均值"""
        try:
            # 这里应该从历史数据中计算，暂时使用简化方法
            base_data = self.historical_base_data.get(code, {})
            avg_volume = base_data.get('avg_volume_5d', 0)
            # 假设买一挂单量约为日均成交量的0.1%
            return max(1000, avg_volume * 0.001)
        except:
            return 1000

    def get_previous_bid5_total(self, code, seconds_ago=30):
        """获取指定秒数前的买五总厚度"""
        try:
            if code not in self.continuity_cache:
                return 0

            cache = self.continuity_cache[code]
            history = cache.get('bid_volumes_history', [])
            timestamps = cache.get('timestamp_history', [])

            if len(history) < 2 or len(timestamps) < 2:
                return 0

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的记录
            closest_index = 0
            min_diff = abs(timestamps[0] - target_time)

            for i, ts in enumerate(timestamps):
                diff = abs(ts - target_time)
                if diff < min_diff:
                    min_diff = diff
                    closest_index = i

            bid_volumes = history[closest_index]
            return sum(bid_volumes) if bid_volumes else 0

        except:
            return 0

    def get_historical_bid5_average(self, code):
        """获取历史买五总厚度平均值"""
        try:
            # 简化计算，基于历史成交量估算
            base_data = self.historical_base_data.get(code, {})
            avg_volume = base_data.get('avg_volume_5d', 0)
            return max(5000, avg_volume * 0.005)
        except:
            return 5000

    def get_recent_minute_data(self, code):
        """获取最近的分时数据"""
        try:
            if code not in self.continuity_cache:
                return None

            cache = self.continuity_cache[code]
            price_history = cache.get('price_history', [])
            timestamps = cache.get('timestamp_history', [])

            if len(price_history) < 10:
                return None

            # 构建简化的分时数据
            minute_data = []
            for i, (price, ts) in enumerate(zip(price_history, timestamps)):
                minute_data.append({
                    'time': datetime.fromtimestamp(ts),
                    'price': price,
                    'index': i
                })

            return minute_data

        except:
            return None

    def identify_w_bottom_pattern(self, minute_data):
        """识别W底形态"""
        try:
            if not minute_data or len(minute_data) < 10:
                return {'has_pattern': False, 'neckline_price': 0, 'pattern_low': 0}

            prices = [item['price'] for item in minute_data]

            # 简化的W底识别：找到两个低点和一个高点
            min_price = min(prices)
            max_price = max(prices)

            # 如果价格波动太小，认为没有形态
            if (max_price - min_price) / min_price < 0.02:  # 波动<2%
                return {'has_pattern': False, 'neckline_price': 0, 'pattern_low': min_price}

            # 简化判断：如果当前价格在中位数以上，认为可能突破
            median_price = sorted(prices)[len(prices) // 2]

            return {
                'has_pattern': True,
                'neckline_price': median_price,
                'pattern_low': min_price,
                'min_low': min_price
            }

        except:
            return {'has_pattern': False, 'neckline_price': 0, 'pattern_low': 0}

    def get_historical_volume_average(self, code):
        """获取历史成交量平均值"""
        try:
            base_data = self.historical_base_data.get(code, {})
            return base_data.get('avg_volume_5d', 0)
        except:
            return 0

    def get_recent_volume_ratios(self, code, minutes=10):
        """获取最近的量比历史记录"""
        try:
            if code not in self.continuity_cache:
                return []

            cache = self.continuity_cache[code]
            ratios = cache.get('volume_ratio_history', [])

            # 返回最近的记录（每30秒一次，10分钟约20条记录）
            return ratios[-20:] if len(ratios) > 20 else ratios

        except:
            return []

    def get_previous_ask5_total(self, code, seconds_ago=30):
        """获取指定秒数前的卖五总厚度"""
        try:
            if code not in self.continuity_cache:
                return 0

            cache = self.continuity_cache[code]
            history = cache.get('ask_volumes_history', [])
            timestamps = cache.get('timestamp_history', [])

            if len(history) < 2 or len(timestamps) < 2:
                return 0

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的记录
            closest_index = 0
            min_diff = abs(timestamps[0] - target_time)

            for i, ts in enumerate(timestamps):
                diff = abs(ts - target_time)
                if diff < min_diff:
                    min_diff = diff
                    closest_index = i

            ask_volumes = history[closest_index]
            return sum(ask_volumes) if ask_volumes else 0

        except:
            return 0

    def get_historical_ask5_average(self, code):
        """获取历史卖五总厚度平均值"""
        try:
            # 简化计算，基于历史成交量估算
            base_data = self.historical_base_data.get(code, {})
            avg_volume = base_data.get('avg_volume_5d', 0)
            return max(5000, avg_volume * 0.005)
        except:
            return 5000

    def calculate_current_volume_ratio(self, code, stock_data):
        """计算当前量比"""
        try:
            current_volume = stock_data.get('volume', 0)
            if current_volume <= 0:
                return 0

            historical_avg = self.get_historical_volume_average(code)
            if historical_avg <= 0:
                return 0

            return current_volume / historical_avg
        except:
            return 0

    def get_previous_bid_volumes(self, code, seconds_ago=30):
        """获取指定秒数前的买盘五档数据"""
        try:
            if code not in self.continuity_cache:
                return [0] * 5

            cache = self.continuity_cache[code]
            history = cache.get('bid_volumes_history', [])
            timestamps = cache.get('timestamp_history', [])

            if len(history) < 2:
                return [0] * 5

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的记录
            closest_index = 0
            min_diff = abs(timestamps[0] - target_time)

            for i, ts in enumerate(timestamps):
                diff = abs(ts - target_time)
                if diff < min_diff:
                    min_diff = diff
                    closest_index = i

            return history[closest_index] if history[closest_index] else [0] * 5

        except:
            return [0] * 5

    def get_previous_ask_volumes(self, code, seconds_ago=30):
        """获取指定秒数前的卖盘五档数据"""
        try:
            if code not in self.continuity_cache:
                return [0] * 5

            cache = self.continuity_cache[code]
            history = cache.get('ask_volumes_history', [])
            timestamps = cache.get('timestamp_history', [])

            if len(history) < 2:
                return [0] * 5

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds_ago

            # 找到最接近目标时间的记录
            closest_index = 0
            min_diff = abs(timestamps[0] - target_time)

            for i, ts in enumerate(timestamps):
                diff = abs(ts - target_time)
                if diff < min_diff:
                    min_diff = diff
                    closest_index = i

            return history[closest_index] if history[closest_index] else [0] * 5

        except:
            return [0] * 5

    # ==================== 时段策略辅助方法 ====================

    def get_yesterday_same_time_bid1(self, code):
        """获取昨日同期买一挂单量（简化实现）"""
        try:
            # 简化实现，使用历史平均值
            return self.get_historical_bid1_average(code)
        except:
            return 1000

    def get_historical_bid345_average(self, code):
        """获取历史买三至买五平均值"""
        try:
            base_data = self.historical_base_data.get(code, {})
            avg_volume = base_data.get('avg_volume_5d', 0)
            return max(3000, avg_volume * 0.003)
        except:
            return 3000

    def get_recent_bid_total_history(self, code, minutes=5):
        """获取最近的买盘总厚度历史"""
        try:
            if code not in self.continuity_cache:
                return []

            cache = self.continuity_cache[code]
            history = cache.get('bid_volumes_history', [])

            # 计算每个时间点的买盘总厚度
            bid_totals = []
            for bid_volumes in history:
                if bid_volumes:
                    bid_totals.append(sum(bid_volumes))
                else:
                    bid_totals.append(0)

            # 返回最近的记录（每30秒一次，5分钟约10条记录）
            return bid_totals[-10:] if len(bid_totals) > 10 else bid_totals

        except:
            return []

    def get_recent_price_history(self, code, minutes=5):
        """获取最近的价格历史"""
        try:
            if code not in self.continuity_cache:
                return []

            cache = self.continuity_cache[code]
            price_history = cache.get('price_history', [])

            # 返回最近的记录（每30秒一次，5分钟约10条记录）
            return price_history[-10:] if len(price_history) > 10 else price_history

        except:
            return []

    # ==================== 更多时段策略辅助方法 ====================

    def get_first_trade_size(self, code):
        """获取首笔成交量（简化实现）"""
        try:
            # 简化实现，返回一个基于历史数据的估算值
            base_data = self.historical_base_data.get(code, {})
            avg_volume = base_data.get('avg_volume_5d', 0)
            return max(1000, avg_volume * 0.01)  # 假设首笔约为日均量的1%
        except:
            return 1000

    def calculate_bid5_thickness_ratio(self, code, stock_data):
        """计算买五厚度比例"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            current_bid5_total = sum(bid_volumes)

            yesterday_avg = self.get_yesterday_same_time_bid1(code) * 5  # 简化估算

            if yesterday_avg <= 0:
                return 0

            return current_bid5_total / yesterday_avg
        except:
            return 0

    def get_above_avg_price_duration(self, code):
        """获取站稳均价线持续时间（分钟）"""
        try:
            if code not in self.continuity_cache:
                return 0

            cache = self.continuity_cache[code]
            price_history = cache.get('price_history', [])

            if len(price_history) < 2:
                return 0

            # 简化实现：计算最近价格的平均值作为均价线
            if len(price_history) >= 10:
                avg_price = sum(price_history[-10:]) / 10
                current_price = price_history[-1]

                # 如果当前价格高于均价，返回一个估算的持续时间
                if current_price > avg_price:
                    return len([p for p in price_history[-10:] if p > avg_price])

            return 0
        except:
            return 0

    def check_bid1_growth_trend(self, code):
        """检查买一量增长趋势"""
        try:
            if code not in self.continuity_cache:
                return 0

            cache = self.continuity_cache[code]
            bid_history = cache.get('bid_volumes_history', [])

            if len(bid_history) < 3:
                return 0

            # 计算最近3次的买一量趋势
            recent_bid1 = [bids[0] if bids else 0 for bids in bid_history[-3:]]

            if len(recent_bid1) >= 3 and recent_bid1[0] > 0:
                growth_rate = (recent_bid1[-1] - recent_bid1[0]) / recent_bid1[0]
                return growth_rate

            return 0
        except:
            return 0

    def check_price_stability(self, code, minutes=10):
        """检查价格稳定性"""
        try:
            if code not in self.continuity_cache:
                return False

            cache = self.continuity_cache[code]
            price_history = cache.get('price_history', [])

            if len(price_history) < 5:
                return False

            recent_prices = price_history[-10:] if len(price_history) >= 10 else price_history

            if len(recent_prices) < 2:
                return False

            # 计算价格波动率
            max_price = max(recent_prices)
            min_price = min(recent_prices)

            if min_price <= 0:
                return False

            volatility = (max_price - min_price) / min_price

            # 波动率<2%认为稳定
            return volatility < 0.02
        except:
            return False

    def calculate_price_acceleration(self, code, seconds=30):
        """计算价格加速度（涨速）"""
        try:
            if code not in self.continuity_cache:
                return 0

            cache = self.continuity_cache[code]
            price_history = cache.get('price_history', [])
            timestamps = cache.get('timestamp_history', [])

            if len(price_history) < 2 or len(timestamps) < 2:
                return 0

            current_time = datetime.now().timestamp()
            target_time = current_time - seconds

            # 找到最接近目标时间的价格
            closest_index = 0
            min_diff = abs(timestamps[0] - target_time)

            for i, ts in enumerate(timestamps):
                diff = abs(ts - target_time)
                if diff < min_diff:
                    min_diff = diff
                    closest_index = i

            if closest_index < len(price_history) and price_history[closest_index] > 0:
                current_price = price_history[-1]
                previous_price = price_history[closest_index]

                # 计算涨速（百分比/秒）
                price_change_rate = (current_price - previous_price) / previous_price
                return price_change_rate

            return 0
        except:
            return 0

    def count_recent_large_orders(self, code, minutes=5):
        """统计最近大单数量（简化实现）"""
        try:
            # 简化实现，基于成交量变化估算
            if code not in self.continuity_cache:
                return 0

            cache = self.continuity_cache[code]
            volume_ratios = cache.get('volume_ratio_history', [])

            # 统计量比>2.0的次数作为大单指标
            large_order_count = sum(1 for ratio in volume_ratios[-10:] if ratio > 2.0)

            return large_order_count
        except:
            return 0

    def check_volume_surge(self, code, stock_data):
        """检查量能突增"""
        try:
            volume_ratio = self.calculate_current_volume_ratio(code, stock_data)
            return volume_ratio > 3.0
        except:
            return False

    def check_morning_high_breakthrough(self, code, stock_data):
        """检查是否突破早盘高点"""
        try:
            current_price = stock_data.get('current_price', 0)

            # 获取早盘高点（简化实现：使用历史数据中的前高）
            base_data = self.historical_base_data.get(code, {})
            prev_high = base_data.get('prev_high', 0)

            if current_price <= 0 or prev_high <= 0:
                return False

            # 如果当前价格超过前高的1%，认为突破
            return current_price > prev_high * 1.01
        except:
            return False

    def calculate_buy_sell_pressure_ratio(self, code, stock_data):
        """计算买卖压力比"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            bid_total = sum(bid_volumes)
            ask_total = sum(ask_volumes)

            if bid_total <= 0:
                return 1.0  # 如果没有买盘，返回1.0（中性）

            return ask_total / bid_total
        except:
            return 1.0

    def check_afternoon_volume_recovery(self, code, stock_data):
        """检查午盘量能恢复"""
        try:
            current_volume_ratio = self.calculate_current_volume_ratio(code, stock_data)

            # 简化判断：量比>1.5认为量能恢复
            return current_volume_ratio > 1.5
        except:
            return False

    # ==================== 基础功能实现 ====================

    def add_stocks_dialog(self):
        """添加股票对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加股票")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))

        # 输入框
        ttk.Label(dialog, text="请输入股票代码（每行一个）:").pack(pady=10)

        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)

        text_widget = tk.Text(text_frame, height=10, width=40)
        scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_widget.configure(yscrollcommand=scrollbar.set)

        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def add_stocks():
            codes_text = text_widget.get("1.0", tk.END).strip()
            if not codes_text:
                messagebox.showwarning("警告", "请输入股票代码")
                return

            codes = [code.strip() for code in codes_text.split('\n') if code.strip()]

            if not codes:
                messagebox.showwarning("警告", "请输入有效的股票代码")
                return

            success_count = 0
            for code in codes:
                # 验证股票代码格式
                if not self.validate_stock_code(code):
                    print(f"⚠️ 股票代码格式错误: {code}")
                    continue

                if code in self.monitored_stocks:
                    print(f"⚠️ 股票 {code} 已在监控列表中")
                    continue

                # 获取股票基本信息
                try:
                    print(f"   🔄 正在获取股票名称: {code}")
                    stock_info = enhanced_stock_fetcher.get_stock_basic_info(code)
                    fetched_name = stock_info.get('name', f'股票{code}')

                    # 检查是否获取到真实名称
                    if fetched_name and not fetched_name.startswith('股票') and fetched_name != '未知':
                        stock_name = fetched_name
                        print(f"   ✅ 成功获取股票名称: {code} -> {stock_name}")
                    else:
                        stock_name = f'股票{code}'
                        print(f"   ⚠️ 未获取到真实股票名称，使用默认: {code} -> {stock_name}")

                    # 添加到监控列表
                    self.monitored_stocks[code] = {
                        'name': stock_name,
                        'data': {},
                        'signals': {},
                        'decision': {},
                        'last_update': None,
                        'initialized': False,
                        'added_time': datetime.now().isoformat(),
                        'alert_history': []
                    }

                    # 🚀 立即获取前收盘价（用于涨停判断）
                    self.ensure_previous_close_data(code)

                    # 添加到表格显示
                    self.tree.insert('', 'end', iid=code, values=(
                        code, stock_name, '未知', '❌', '❌', '❌',
                        '0', '需初始化', '-', '刚添加'
                    ))

                    success_count += 1
                    print(f"✅ 添加股票: {code} {stock_name}")

                except Exception as e:
                    print(f"❌ 添加股票失败 {code}: {e}")
                    continue

            if success_count > 0:
                # 保存配置
                self.save_config()

                messagebox.showinfo("成功", f"成功添加 {success_count} 只股票")
                self.info_label.config(text=f"💡 已添加 {success_count} 只股票，请初始化历史数据")
                dialog.destroy()
            else:
                messagebox.showerror("错误", "没有成功添加任何股票")

        ttk.Button(button_frame, text="添加", command=add_stocks).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)

    def validate_stock_code(self, code):
        """验证股票代码格式"""
        try:
            # 去除空格并转换为字符串
            code = str(code).strip()

            # 检查长度
            if len(code) != 6:
                return False

            # 检查是否全为数字
            if not code.isdigit():
                return False

            # 检查是否为有效的股票代码前缀
            valid_prefixes = ['000', '001', '002', '003', '300', '600', '601', '603', '605', '688']

            for prefix in valid_prefixes:
                if code.startswith(prefix):
                    return True

            return False

        except:
            return False

    def remove_selected_stock(self):
        """删除选中的股票"""
        selected_items = self.tree.selection()

        if not selected_items:
            messagebox.showwarning("警告", "请先选择要删除的股票")
            return

        # 确认删除
        if not messagebox.askyesno("确认", f"确定要删除选中的 {len(selected_items)} 只股票吗？"):
            return

        # 删除股票
        for item in selected_items:
            code = item
            if code in self.monitored_stocks:
                stock_name = self.monitored_stocks[code]['name']
                del self.monitored_stocks[code]
                print(f"🗑️ 删除股票: {code} {stock_name}")

            # 从表格中删除
            self.tree.delete(item)

        # 保存配置
        self.save_config()

        messagebox.showinfo("成功", f"成功删除 {len(selected_items)} 只股票")
        self.info_label.config(text=f"💡 当前监控 {len(self.monitored_stocks)} 只股票")

    def clear_all_stocks(self):
        """清空所有股票"""
        try:
            if not self.monitored_stocks:
                messagebox.showinfo("提示", "当前没有股票需要清空")
                return

            stock_count = len(self.monitored_stocks)

            # 确认清空
            if messagebox.askyesno("确认清空", f"确定要清空所有 {stock_count} 只股票吗？\n此操作不可撤销！"):
                # 清空监控列表
                self.monitored_stocks.clear()

                # 清空树形控件
                for item in self.tree.get_children():
                    self.tree.delete(item)

                # 保存配置
                self.save_config()

                print(f"✅ 已清空所有 {stock_count} 只股票")
                self.info_label.config(text="💡 当前监控 0 只股票")

                messagebox.showinfo("完成", f"已成功清空所有 {stock_count} 只股票")

        except Exception as e:
            print(f"❌ 清空股票失败: {e}")
            messagebox.showerror("错误", f"清空股票失败: {e}")

    def reinitialize_all_stocks(self):
        """重新初始化所有股票的历史数据"""
        try:
            if not self.monitored_stocks:
                messagebox.showinfo("提示", "没有需要初始化的股票")
                return

            print(f"🔄 开始重新初始化 {len(self.monitored_stocks)} 只股票的历史数据...")

            success_count = 0
            for code in list(self.monitored_stocks.keys()):
                try:
                    print(f"🔄 重新初始化 {code} {self.monitored_stocks[code]['name']}...")

                    if self.initialize_stock_monitoring(code):
                        self.monitored_stocks[code]['initialized'] = True
                        success_count += 1

                        # 更新表格显示
                        if self.tree.exists(code):
                            current_values = list(self.tree.item(code, 'values'))
                            current_values[7] = '已初始化'  # 更新买入建议列显示状态
                            self.tree.item(code, values=current_values)
                    else:
                        print(f"❌ {code} 初始化失败")

                except Exception as e:
                    print(f"❌ {code} 初始化异常: {e}")
                    continue

            # 保存配置
            self.save_config()

            print(f"✅ 重新初始化完成：成功 {success_count}/{len(self.monitored_stocks)} 只股票")
            messagebox.showinfo("完成", f"重新初始化完成：\n成功 {success_count} 只股票\n失败 {len(self.monitored_stocks) - success_count} 只股票")

            # 更新状态提示
            self.info_label.config(text=f"💡 {success_count} 只股票已就绪，可以开始监控")

        except Exception as e:
            error_msg = f"重新初始化失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)



    def monitor_loop(self):
        """监控主循环（全面升级版）"""
        print("🔍 连板潜力股监控循环启动")

        # 启动时加载当日已有数据
        self.load_today_data_for_all_stocks()

        while self.monitoring:
            try:
                # 检查是否在交易时间
                if not self.is_trading_time():
                    print("⏰ 非交易时间，暂停监控")
                    time.sleep(60)  # 非交易时间每分钟检查一次
                    continue

                # 获取当前时段策略
                current_time_strategy = self.get_current_time_period()
                scan_interval = current_time_strategy.get('scan_interval', 30)
                period_name = current_time_strategy.get('name', '未知时段')

                print(f"🔍 开始扫描 {len(self.monitored_stocks)} 只股票")
                print(f"⏰ 当前时段: {period_name} | 扫描间隔: {scan_interval}秒")

                # 🚀 每日重置涨停通知记录
                self.reset_daily_limit_up_notifications()

                # 🚀 智能股票筛选：9:15-9:30时段只获取已买入股票数据
                try:
                    # 🚀 根据时段决定获取哪些股票的数据
                    active_codes = self._get_stocks_to_monitor_by_period(period_name)

                    if not active_codes:
                        if period_name == '集合竞价':
                            print("⚠️ 集合竞价时段：没有已买入股票需要监控")
                        else:
                            print("⚠️ 没有已初始化的股票需要监控")
                        time.sleep(scan_interval)
                        continue

                    # 显示监控股票信息
                    if period_name == '集合竞价':
                        print(f"📊 集合竞价时段：只获取 {len(active_codes)} 只已买入股票的数据...")
                    else:
                        print(f"📊 批量获取 {len(active_codes)} 只股票的实时数据...")

                    # 一次性获取所有股票数据
                    batch_quote_df = enhanced_stock_fetcher.get_realtime_quote(active_codes)

                    if batch_quote_df.empty:
                        print("⚠️ 批量获取实时数据失败，跳过本轮监控")
                        print("💡 提示：系统已自动尝试所有可用数据源（AKShare + 备用数据源）")
                        print(f"⏳ 等待 {scan_interval} 秒后重试...")
                        time.sleep(scan_interval)
                        continue

                    # 检查数据源类型并显示状态
                    data_sources = batch_quote_df['data_source'].unique() if 'data_source' in batch_quote_df.columns else ['unknown']
                    print(f"✅ 批量获取成功，共获取 {len(batch_quote_df)} 只股票数据")
                    print(f"📊 数据源: {', '.join(data_sources)}")

                except Exception as e:
                    print(f"❌ 批量获取数据失败: {e}")
                    print("💡 提示：请检查网络连接或稍后重试")
                    print(f"⏳ 等待 {scan_interval} 秒后重试...")
                    time.sleep(scan_interval)
                    continue

                # 遍历所有监控股票，使用批量获取的数据
                processed_count = 0
                for code, stock_info in self.monitored_stocks.items():
                    if not self.monitoring:  # 检查是否停止监控
                        print("⚠️ 监控已停止，退出循环")
                        break

                    try:
                        # 检查是否已初始化
                        if not stock_info.get('initialized', False):
                            continue

                        # 从批量数据中获取对应股票的数据
                        stock_data_rows = batch_quote_df[batch_quote_df['stock_code'] == code]
                        if stock_data_rows.empty:
                            print(f"⚠️ {code} 在批量数据中未找到，跳过")
                            continue

                        # 转换为字典格式
                        stock_data = stock_data_rows.iloc[0].to_dict()
                        processed_count += 1

                        # 🚀 修复：补充历史数据（用于开盘类型分类）
                        if code in self.historical_base_data:
                            historical_data = self.historical_base_data[code]
                            stock_data['prev_close'] = historical_data.get('prev_close', 0)
                            stock_data['prev_high'] = historical_data.get('prev_high', 0)
                            stock_data['prev_low'] = historical_data.get('prev_low', 0)
                            stock_data['prev_open'] = historical_data.get('prev_open', 0)

                        # 🔍 显示数据源信息（仅在使用备用数据源时）
                        data_source = stock_data.get('data_source', 'unknown')
                        if data_source != 'akshare':
                            print(f"   📊 {code} 使用备用数据源: {data_source}")

                        # 更新实时数据缓存
                        self.realtime_data_cache[code] = stock_data

                        # 数据已经从批量获取中获得，继续处理

                        # 🔄 算法切换机制：只有两种算法类型
                        if bought_stocks_manager and bought_stocks_manager.is_bought_stock(code):
                            # 已买入股票：使用买入后的专门算法（卖出监控算法）
                            self.process_bought_stock(code, stock_data)
                        else:
                            # 普通股票：使用原有选股算法
                            self.process_regular_stock(code, stock_data)

                    except Exception as e:
                        print(f"⚠️ 处理股票 {code} 时出错: {e}")
                        continue

                print(f"✅ 本轮监控完成，处理了 {processed_count} 只股票")

                # 等待下一轮扫描
                print(f"⏳ 等待 {scan_interval} 秒后开始下一轮监控...")
                time.sleep(scan_interval)

            except Exception as e:
                print(f"❌ 监控循环异常: {e}")
                print("💡 提示：请检查网络连接或稍后重试")
                print(f"⏳ 等待 {scan_interval} 秒后重试...")
                time.sleep(scan_interval)
                continue

        print("⏸️ 连板潜力股监控循环结束")

    def _get_stocks_to_monitor_by_period(self, period_name: str) -> List[str]:
        """
        根据时段决定需要监控的股票

        Args:
            period_name: 时段名称

        Returns:
            List[str]: 需要监控的股票代码列表
        """
        try:
            # 🚀 集合竞价时段（9:15-9:25）：只监控已买入股票
            if period_name == '集合竞价监控':
                bought_codes = []

                # 检查已买入股票管理器是否可用
                if bought_stocks_manager:
                    # 获取所有已买入股票
                    bought_stocks = bought_stocks_manager.get_all_bought_stocks()

                    for code, stock_info in bought_stocks.items():
                        # 确保股票在监控列表中且已初始化
                        if (code in self.monitored_stocks and
                            self.monitored_stocks[code].get('initialized', False)):
                            bought_codes.append(code)

                print(f"🔍 集合竞价时段筛选结果：{len(bought_codes)} 只已买入股票")
                if bought_codes:
                    bought_names = [self.monitored_stocks[code]['name'] for code in bought_codes]
                    print(f"   已买入股票: {', '.join(bought_names)}")

                return bought_codes

            # 🚀 其他时段：监控所有已初始化的股票
            else:
                all_codes = [code for code, info in self.monitored_stocks.items()
                           if info.get('initialized', False)]

                return all_codes

        except Exception as e:
            print(f"⚠️ 时段股票筛选失败: {e}")
            # 异常时返回所有已初始化的股票
            return [code for code, info in self.monitored_stocks.items()
                   if info.get('initialized', False)]



    def process_bought_stock(self, code: str, stock_data: Dict):
        """
        处理已买入股票（卖出监控算法）

        Args:
            code: 股票代码
            stock_data: 股票数据
        """
        try:
            # 🚀 关键修复：计算并设置量比到stock_data中
            volume_ratio = self.calculate_current_volume_ratio(code, stock_data)
            stock_data['volume_ratio'] = volume_ratio

            if not sell_monitoring_algorithms:
                print(f"⚠️ 卖出监控算法不可用，跳过 {code}")
                return

            # 获取已买入股票信息
            stock_info = bought_stocks_manager.get_stock_info(code)
            if not stock_info:
                print(f"⚠️ 未找到已买入股票信息: {code}")
                return

            stock_name = stock_info['name']
            hold_days = stock_info['hold_days']
            buy_price = stock_info.get('buy_price')
            buy_date = stock_info.get('buy_date', '')

            # 添加买入价格到股票数据中
            if buy_price:
                stock_data['buy_price'] = buy_price

            # 🚀 修复：检查是否为买入当天（更严格的判断）
            from datetime import datetime
            current_date = datetime.now().strftime('%Y-%m-%d')

            # 确保buy_date格式正确
            if isinstance(buy_date, str) and len(buy_date) >= 10:
                buy_date_str = buy_date[:10]  # 取前10位 YYYY-MM-DD
            else:
                buy_date_str = str(buy_date)

            print(f"🔍 买入日期检查: buy_date='{buy_date_str}', current_date='{current_date}'")

            if buy_date_str == current_date:
                # 买入当天：暂停分析
                current_price = stock_data.get('current_price', 0)
                change_pct = stock_data.get('change_pct', 0)

                print(f"💰 {code} {stock_name} (买入当天) - 暂停分析")
                print(f"   📊 买入价格: {buy_price:.2f}")
                print(f"   📊 当前价格: {current_price:.2f}")
                print(f"   💡 买入当天不进行分析，明天开始监控")
                print(f"   ⏰ 买入时间: {buy_date}")

                # 🚀 记录买入当天的价格轨迹到记忆系统
                if bought_stocks_memory:
                    profit_loss = ((current_price - buy_price) / buy_price * 100) if buy_price > 0 else 0
                    bought_stocks_memory.record_daily_performance(
                        code, stock_name, current_price, change_pct, profit_loss
                    )
                    print(f"   📝 已记录买入当天数据到记忆系统")

                # 保存实时数据但不进行分析
                self.save_realtime_data(code, stock_data)
                return

            print(f"💰 分析已买入股票: {code} {stock_name} (持有{hold_days}天)")

            # 🚀 执行增强版卖出监控算法（集成通知系统）
            sell_decision = sell_monitoring_algorithms.make_enhanced_decision_with_notifications(
                stock_data, hold_days, stock_info.get('buy_date')
            )

            # 显示分析结果
            current_price = stock_data.get('current_price', 0)
            change_pct = stock_data.get('change_pct', 0)
            holding_profit_pct = sell_decision.get('holding_profit_pct', 0)

            print(f"   📊 当前价格: {current_price:.2f} ({change_pct:+.2f}%)")
            print(f"   💰 持仓盈亏: {holding_profit_pct:+.2f}%")
            print(f"   🎯 决策建议: {sell_decision.get('final_decision', '未知')}")
            print(f"   🔍 置信度: {sell_decision.get('confidence', '未知')}")

            # 显示洗盘分析
            wash_analysis = sell_decision.get('wash_analysis', {})
            wash_prob = wash_analysis.get('wash_probability', 0)
            print(f"   🌊 洗盘概率: {wash_prob:.1%}")

            # 🚀 显示记忆系统分析
            memory_info = self._get_memory_display_info(code, hold_days)
            if memory_info:
                print(memory_info)

            # 🚀 显示主力行为分析
            main_behavior_info = self._get_main_behavior_display_info(sell_decision)
            if main_behavior_info:
                print(main_behavior_info)

            # 显示时段策略
            time_strategy = sell_decision.get('time_strategy', {})
            current_period = time_strategy.get('current_period', {})
            period_name = current_period.get('name', '未知时段')
            print(f"   ⏰ 当前时段: {period_name}")

            # 根据决策执行操作
            action = sell_decision.get('action', 'watch')
            if action == 'sell':
                print(f"   🚨 建议立即卖出 {code} {stock_name}")
                # 这里可以添加自动卖出逻辑或发送通知
                self.send_sell_alert(code, stock_name, sell_decision)
            elif action == 'reduce':
                print(f"   ⚠️ 建议减仓 {code} {stock_name}")
                self.send_reduce_alert(code, stock_name, sell_decision)
            elif action == 'hold':
                print(f"   ✅ 继续持有 {code} {stock_name}")

            # 更新股票监控信息
            if code in self.monitored_stocks:
                self.monitored_stocks[code]['sell_decision'] = sell_decision
                self.monitored_stocks[code]['last_analysis_time'] = datetime.now().strftime('%H:%M:%S')

        except Exception as e:
            print(f"⚠️ 处理已买入股票失败 {code}: {e}")

    def process_regular_stock(self, code: str, stock_data: Dict):
        """
        处理普通股票（原有选股算法）

        Args:
            code: 股票代码
            stock_data: 股票数据
        """
        try:
            # 🚀 硬性涨停检查（优先级最高）- 直接使用AKShare的准确涨跌幅
            current_gain = stock_data.get('change_pct', 0)  # AKShare提供的准确涨跌幅
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)

            # 🚀 将AKShare的涨跌幅作为标准数据传递给后续算法
            stock_data['correct_change_pct'] = current_gain

            # 🔍 调试信息：检查数据获取
            print(f"   🔍 {code} 涨跌幅: {current_gain:.2f}% (选股模式，当前价{current_price:.2f}, 昨收{prev_close:.2f})")

            if current_gain >= 9.9:
                # 涨停股票：只提醒一次，不发买入信号
                if code not in self.limit_up_notified:
                    self.limit_up_notified.add(code)
                    print(f"   🚨 {code} 已涨停 {current_gain:.2f}%，无法买入")

                # 更新表格显示（涨停状态）
                limit_up_result = {
                    'buy_signal': False,
                    'signal_strength': 0.0,
                    'recommendation': '🚫 已涨停',
                    'algorithm_type': '涨停监控',
                    'open_type_result': {'type': 'limit_up', 'type_name': '涨停'},
                    'core_signals': {}
                }
                self.update_stock_display(code, stock_data, limit_up_result)
                return

            # 🚀 执行原有的选股算法逻辑
            signal_result = self.execute_stock_selection_algorithms(code, stock_data)

            # 🚀 修复：更新界面显示（包含开盘类型）
            if signal_result:
                self.update_stock_display(code, stock_data, signal_result)

        except Exception as e:
            print(f"⚠️ 处理普通股票失败 {code}: {e}")

    def process_analysis_stock(self, code: str, stock_data: Dict):
        """
        处理分析模式股票（使用分析算法但不执行买卖决策）

        Args:
            code: 股票代码
            stock_data: 股票数据
        """
        try:
            stock_name = stock_data.get('stock_name', '未知')
            current_price = stock_data.get('current_price', 0)
            change_pct = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            print(f"💰 分析股票: {code} {stock_name}")
            print(f"   📊 当前价格: {current_price:.2f} ({change_pct:+.2f}%)")
            print(f"   📊 量比: {volume_ratio:.2f}")

            # 🚀 执行强化版分析算法（不执行买卖决策）
            if sell_monitoring_algorithms:
                # 使用卖出监控算法进行分析（但不执行卖出决策）
                analysis_result = sell_monitoring_algorithms.analyze_main_force_behavior(stock_data, hold_days=1)

                if 'error' not in analysis_result:
                    # 显示主力行为分析结果
                    main_intention = analysis_result.get('main_intention', '方向不明')
                    behavior_score = analysis_result.get('behavior_score', 0)
                    risk_level = analysis_result.get('risk_level', 'medium')
                    escape_signals = analysis_result.get('escape_signals', [])
                    wash_signals = analysis_result.get('wash_signals', [])

                    print(f"   🎯 主力意图: {main_intention}")
                    print(f"   📊 行为评分: {behavior_score:.2f}")
                    print(f"   ⚠️ 风险等级: {risk_level}")

                    if escape_signals:
                        print(f"   🚨 出逃信号: {', '.join(escape_signals[:3])}")

                    if wash_signals:
                        print(f"   🌊 洗盘信号: {', '.join(wash_signals[:3])}")

                    # 🚀 获取增强数据
                    enhanced_data = analysis_result.get('enhanced_data', {})

                    # 显示板块表现
                    sector_data = enhanced_data.get('sector_performance', {})
                    if sector_data and 'error' not in sector_data:
                        sector_trend = sector_data.get('sector_trend', 'neutral')
                        avg_change = sector_data.get('avg_change', 0)
                        trend_desc = {'bullish': '强势', 'neutral': '中性', 'bearish': '弱势'}.get(sector_trend, '中性')
                        print(f"   🏢 板块表现: {trend_desc} ({avg_change:+.1f}%)")

                    # 显示北向资金
                    northbound_data = enhanced_data.get('northbound_capital', {})
                    if northbound_data and 'error' not in northbound_data:
                        trend = northbound_data.get('trend', 'neutral')
                        net_inflow = northbound_data.get('net_inflow', 0)
                        trend_desc = {
                            'strong_inflow': '大幅流入',
                            'inflow': '净流入',
                            'neutral': '平衡',
                            'outflow': '净流出',
                            'strong_outflow': '大幅流出'
                        }.get(trend, '平衡')
                        print(f"   💸 北向资金: {trend_desc} ({net_inflow:+.1f}亿)")

                    # 🚀 分析建议（仅供参考，不执行买卖）
                    if behavior_score >= 0.8 and len(wash_signals) >= 3:
                        print(f"   💡 分析建议: 股票表现强势，洗盘信号明显")
                    elif len(escape_signals) >= 3:
                        print(f"   💡 分析建议: 主力出逃信号较多，需要谨慎")
                    elif main_intention in ['洗盘吸筹', '蓄势待发']:
                        print(f"   💡 分析建议: 主力意图积极，可关注后续表现")
                    else:
                        print(f"   💡 分析建议: 方向不明，建议观察")

                else:
                    print(f"   ⚠️ 主力行为分析失败: {analysis_result['error']}")

            # 🚀 计算洗盘概率
            if sell_monitoring_algorithms:
                wash_analysis = sell_monitoring_algorithms.calculate_wash_probability(stock_data)
                wash_prob = wash_analysis.get('wash_probability', 0)
                print(f"   🌊 洗盘概率: {wash_prob:.1%}")

            # 🚀 保存实时数据
            self.save_realtime_data(code, stock_data)

        except Exception as e:
            print(f"⚠️ 处理分析股票失败 {code}: {e}")

    def execute_stock_selection_algorithms(self, code: str, stock_data: Dict):
        """
        执行原有的选股算法逻辑

        Args:
            code: 股票代码
            stock_data: 股票数据
        """
        try:
            # 🚀 新功能：保存实时数据
            self.save_realtime_data(code, stock_data)

            # 🚀 新功能：更新实时数据缓存
            self.update_realtime_data_cache(code, stock_data)

            # 🚀 新功能：初始化价格轨迹跟踪器
            if code not in self.price_trackers:
                self.price_trackers[code] = PriceTrajectoryTracker(max_history=30)

            # 🚀 新功能：初始化连续确认系统
            if code not in self.confirmation_systems:
                self.confirmation_systems[code] = ContinuousConfirmationSystem(required_confirmations=2)

            # 🚀 关键修复：计算并设置量比到stock_data中
            volume_ratio = self.calculate_current_volume_ratio(code, stock_data)
            stock_data['volume_ratio'] = volume_ratio

            # 🚀 新功能：添加数据点到价格轨迹跟踪器
            current_time = datetime.now().strftime('%H:%M:%S')
            current_price = stock_data.get('current_price', 0)
            current_gain = stock_data.get('change_pct', 0)

            self.price_trackers[code].add_data_point(
                current_time, current_price, current_gain, volume_ratio
            )

            # 🚀 新功能：智能算法选择（基于股票状态）
            algorithm_info = self.select_intelligent_algorithm_with_state(code, stock_data)

            algorithm_type = algorithm_info.get('algorithm_type', 'standard_flat_open')
            algorithm_name = algorithm_info.get('algorithm_name', '标准算法')
            confidence = algorithm_info.get('confidence', 0.0)
            reason = algorithm_info.get('reason', '未知原因')

            print(f"   🎯 选择算法: {algorithm_name}")
            print(f"   📊 置信度: {confidence:.2f}")
            print(f"   💡 选择原因: {reason}")

            # 🚀 时段分析
            period_analysis = self.analyze_current_period(code, stock_data)
            period_name = period_analysis.get('period_name', '未知时段')
            period_strategy = period_analysis.get('strategy', '观察')

            print(f"   ⏰ 当前时段: {period_name}")
            print(f"   📋 时段策略: {period_strategy}")

            # 🚀 执行智能信号检测
            if algorithm_type not in ['observation', 'abandon', 'error']:
                signal_result = self.execute_intelligent_signal_detection(
                    code, stock_data, algorithm_info, period_analysis
                )

                # 🎯 显示完整的算法分析结果
                self._display_complete_algorithm_analysis(code, stock_data, signal_result, algorithm_info, period_analysis)

                # 🚀 更新股票显示
                display_result = {
                    'buy_signal': signal_result.get('buy_signal', False),
                    'signal_strength': signal_result.get('signal_strength', 0.0),
                    'recommendation': signal_result.get('recommendation', '观察'),
                    'algorithm_type': algorithm_name,
                    'algorithm_info': algorithm_info,
                    'period_analysis': period_analysis,
                    'core_signals': signal_result.get('core_signals', {}),
                    'open_type_result': signal_result.get('open_type_result', {}),
                    'signal_result': signal_result
                }

                self.update_stock_display(code, stock_data, display_result)

            else:
                # 观察、放弃或错误状态
                print(f"   ⏳ 算法状态: {algorithm_type}")
                print(f"   💡 状态说明: {reason}")

                display_result = {
                    'buy_signal': False,
                    'signal_strength': 0.0,
                    'recommendation': reason,
                    'algorithm_type': algorithm_name,
                    'algorithm_info': algorithm_info,
                    'period_analysis': period_analysis,
                    'core_signals': {},
                    'open_type_result': {'type': algorithm_type, 'type_name': algorithm_name}
                }

                self.update_stock_display(code, stock_data, display_result)

        except Exception as e:
            print(f"⚠️ 执行选股算法失败 {code}: {e}")
            import traceback
            print(f"   详细错误: {traceback.format_exc()}")

            # 错误时也要更新显示
            error_result = {
                'buy_signal': False,
                'signal_strength': 0.0,
                'recommendation': f'❌ 算法执行失败: {e}',
                'algorithm_type': '错误',
                'core_signals': {}
            }
            self.update_stock_display(code, stock_data, error_result)

    def _display_complete_algorithm_analysis(self, code: str, stock_data: Dict,
                                           signal_result: Dict, algorithm_info: Dict,
                                           period_analysis: Dict) -> None:
        """
        显示完整的算法分析结果

        Args:
            code: 股票代码
            stock_data: 股票数据
            signal_result: 信号检测结果
            algorithm_info: 算法信息
            period_analysis: 时段分析
        """
        try:
            stock_name = stock_data.get('stock_name', '未知')
            current_price = stock_data.get('current_price', 0)
            change_pct = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            # 🎯 基础信息显示
            print(f"🎯 {code} {stock_name} 完整分析:")
            print(f"   📊 当前价格: {current_price:.2f} ({change_pct:+.2f}%)")
            print(f"   📊 量比: {volume_ratio:.2f}")

            # 🎯 算法选择信息
            algorithm_name = algorithm_info.get('algorithm_name', '未知算法')
            confidence = algorithm_info.get('confidence', 0.0)
            reason = algorithm_info.get('reason', '未知原因')
            stock_state = algorithm_info.get('stock_state', '普通状态')

            print(f"   🎯 选择算法: {algorithm_name}")
            print(f"   📊 算法置信度: {confidence:.2f}")
            print(f"   💡 选择原因: {reason}")
            if stock_state != '普通状态':
                print(f"   📈 股票状态: {stock_state}")

            # 🎯 时段策略信息
            period_name = period_analysis.get('period_name', '未知时段')
            strategy = period_analysis.get('strategy', '观察')
            market_phase = period_analysis.get('market_phase', '未知阶段')

            print(f"   ⏰ 当前时段: {period_name}")
            print(f"   📋 时段策略: {strategy}")
            print(f"   🏛️ 市场阶段: {market_phase}")

            # 🎯 核心信号分析
            core_signals = signal_result.get('core_signals', {})
            if core_signals:
                self._display_core_signals_analysis(core_signals)

            # 🎯 买入信号判断
            buy_signal = signal_result.get('buy_signal', False)
            signal_strength = signal_result.get('signal_strength', 0.0)
            recommendation = signal_result.get('recommendation', '观察')

            print(f"   🚀 买入信号: {'✅ 是' if buy_signal else '❌ 否'}")
            print(f"   📊 信号强度: {signal_strength:.2f}")
            print(f"   💡 操作建议: {recommendation}")

            # 🎯 开盘类型分析
            open_type_result = signal_result.get('open_type_result', {})
            if open_type_result:
                open_type = open_type_result.get('type_name', '未知类型')
                print(f"   📈 开盘类型: {open_type}")

            # 🎯 风险提示
            if signal_strength < 0.3:
                print(f"   ⚠️ 风险提示: 信号强度较低，建议谨慎")
            elif change_pct > 7:
                print(f"   ⚠️ 风险提示: 涨幅较大，注意追高风险")
            elif volume_ratio < 0.8:
                print(f"   ⚠️ 风险提示: 量能不足，缺乏支撑")

            print(f"   " + "="*50)

        except Exception as e:
            print(f"   ⚠️ 完整分析显示失败: {e}")

    def _display_core_signals_analysis(self, core_signals: Dict) -> None:
        """
        显示核心信号分析详情

        Args:
            core_signals: 核心信号数据
        """
        try:
            # 统计信号满足情况
            signal_count = 0
            total_signals = 0
            satisfied_signals = []
            unsatisfied_signals = []

            for signal_name, signal_value in core_signals.items():
                if isinstance(signal_value, bool):
                    total_signals += 1
                    if signal_value:
                        signal_count += 1
                        satisfied_signals.append(signal_name)
                    else:
                        unsatisfied_signals.append(signal_name)

            if total_signals > 0:
                print(f"   🎯 信号统计: {signal_count}/{total_signals} 个信号满足")

                # 显示满足的信号
                if satisfied_signals:
                    signal_names = self._translate_signal_names(satisfied_signals)
                    print(f"   ✅ 满足信号: {', '.join(signal_names)}")

                # 显示未满足的信号
                if unsatisfied_signals:
                    signal_names = self._translate_signal_names(unsatisfied_signals)
                    print(f"   ❌ 缺失信号: {', '.join(signal_names)}")

            # 显示信号详细数据
            details = core_signals.get('details', {})
            if details:
                self._display_signal_details(details)

        except Exception as e:
            print(f"   ⚠️ 核心信号分析显示失败: {e}")

    def _translate_signal_names(self, signal_names: list) -> list:
        """
        翻译信号名称为中文

        Args:
            signal_names: 英文信号名称列表

        Returns:
            list: 中文信号名称列表
        """
        translation_map = {
            'volume_spike': '量能突击',
            'depth_anomaly': '盘口异动',
            'pattern_break': '形态突破',
            'volume_consistent': '量能持续',
            'pressure_test': '抛压测试',
            'gap_protection': '跳空守护',
            'reversal_signal': '反转信号',
            'breakout_signal': '突破信号',
            'accumulation_signal': '蓄势信号',
            'momentum_signal': '动量信号',
            'wash_signal': '洗盘信号'
        }

        return [translation_map.get(name, name) for name in signal_names]

    def _display_signal_details(self, details: Dict) -> None:
        """
        显示信号详细数据

        Args:
            details: 信号详细数据
        """
        try:
            for signal_type, signal_data in details.items():
                if isinstance(signal_data, dict) and signal_data.get('detected', False):
                    signal_name = self._translate_signal_names([signal_type])[0]

                    # 显示量能突击详情
                    if signal_type == 'volume_spike':
                        spike_ratio = signal_data.get('spike_ratio', 0)
                        print(f"   📊 {signal_name}: 突增{spike_ratio:.1f}倍")

                    # 显示盘口异动详情
                    elif signal_type == 'depth_anomaly':
                        change_rate = signal_data.get('change_rate', 0)
                        print(f"   📊 {signal_name}: 厚度变化{change_rate:.1%}")

                    # 显示抛压测试详情
                    elif signal_type == 'pressure_test':
                        cancel_rate = signal_data.get('cancel_rate', 0)
                        print(f"   📊 {signal_name}: 撤单率{cancel_rate:.1%}")

                    # 显示其他信号详情
                    else:
                        strength = signal_data.get('strength', 0)
                        if strength > 0:
                            print(f"   📊 {signal_name}: 强度{strength:.2f}")

        except Exception as e:
            print(f"   ⚠️ 信号详情显示失败: {e}")

    def load_today_data_for_all_stocks(self):
        """为所有监控股票加载当日数据和前收盘价"""
        try:
            print("📊 加载当日已有数据和前收盘价...")
            for code in self.monitored_stocks.keys():
                # 加载当日实时数据
                self.load_today_realtime_data(code)

                # 🚀 获取前收盘价（用于涨停判断）
                self.ensure_previous_close_data(code)

            print("✅ 当日数据和前收盘价加载完成")
        except Exception as e:
            print(f"⚠️ 加载当日数据失败: {e}")

    def ensure_previous_close_data(self, code):
        """确保获取到前收盘价数据"""
        try:
            if code not in self.historical_base_data:
                print(f"   📊 获取 {code} 前收盘价...")
                # 获取历史数据
                hist_df = enhanced_stock_fetcher.get_historical_data(code, period='5d')
                if hist_df is not None and len(hist_df) >= 2:
                    # 获取前一个交易日的收盘价
                    prev_close = hist_df.iloc[-2]['close']  # 倒数第二天的收盘价
                    self.historical_base_data[code] = {
                        'prev_close': prev_close,
                        'prev_high': hist_df.iloc[-2]['high'],
                        'prev_low': hist_df.iloc[-2]['low'],
                        'prev_volume': hist_df.iloc[-2]['volume']
                    }
                    print(f"   ✅ {code} 前收盘价: {prev_close:.2f}")
                else:
                    print(f"   ⚠️ {code} 无法获取历史数据")
        except Exception as e:
            print(f"   ⚠️ {code} 获取前收盘价失败: {e}")

    def execute_state_aware_signal_detection(self, code, stock_data, algorithm_info, period_analysis):
        """
        执行基于状态的智能信号检测（新增）

        Args:
            code: 股票代码
            stock_data: 股票数据
            algorithm_info: 算法信息（包含股票状态）
            period_analysis: 时段分析

        Returns:
            dict: 信号检测结果
        """
        try:
            algorithm_type = algorithm_info.get('algorithm_type', 'standard_flat_open')
            stock_state = algorithm_info.get('stock_state', '普通状态')

            # 🚀 修复：为了确保稳定性，所有算法都使用智能信号检测
            return self.execute_intelligent_signal_detection(code, stock_data, algorithm_info, period_analysis)

            # 实时盘口监控
            depth_analysis = self.analyze_realtime_depth(code, stock_data)

            # 综合信号强度计算
            signal_components = {
                'core_signals': core_signals.get('signal_strength', 0.0) * 0.4,      # 核心信号40%
                'depth_analysis': depth_analysis.get('signal_strength', 0.0) * 0.3,  # 盘口分析30%
                'period_analysis': period_analysis.get('signal_strength', 0.0) * 0.2, # 时段策略20%
                'algorithm_confidence': algorithm_info.get('confidence', 0.0) * 0.1   # 算法置信度10%
            }

            total_signal_strength = sum(signal_components.values())

            # 基于状态的买入信号判断
            buy_signal = self.judge_state_aware_buy_signal(
                core_signals, depth_analysis, period_analysis, algorithm_info, total_signal_strength
            )

            # 生成推荐和仓位
            recommendation = self.generate_intelligent_recommendation(
                buy_signal, total_signal_strength, algorithm_info, period_analysis
            )
            position_size = self.calculate_intelligent_position_size(
                total_signal_strength, algorithm_info, period_analysis
            )

            # 🚀 添加开盘类型分析（用于界面显示）
            open_type_result = self.classify_open_type(stock_data)

            return {
                'buy_signal': buy_signal,
                'signal_strength': total_signal_strength,
                'recommendation': recommendation,
                'position_size': position_size,
                'algorithm_type': algorithm_info.get('algorithm_name', '未知算法'),
                'algorithm_info': algorithm_info,
                'period_analysis': period_analysis,
                'core_signals': core_signals,
                'depth_analysis': depth_analysis,
                'signal_components': signal_components,
                'stock_state': stock_state,
                'reason': core_signals.get('reason', '状态感知分析'),
                'open_type_result': open_type_result  # 🚀 添加开盘类型结果
            }

        except Exception as e:
            print(f"⚠️ 基于状态的信号检测失败 {code}: {e}")

            # 🚀 修复：即使异常也要返回开盘类型结果
            try:
                open_type_result = self.classify_open_type(stock_data)
            except:
                open_type_result = {'type': 'unknown', 'type_name': '未知'}

            return {
                'buy_signal': False,
                'signal_strength': 0.0,
                'recommendation': '❌ 分析失败',
                'algorithm_type': '错误',
                'error': str(e),
                'open_type_result': open_type_result  # 🚀 确保开盘类型结果存在
            }

    def execute_intelligent_signal_detection(self, code, stock_data, algorithm_info, period_analysis):
        """
        执行智能信号检测

        Args:
            code: 股票代码
            stock_data: 股票数据
            algorithm_info: 算法信息
            period_analysis: 时段分析

        Returns:
            dict: 信号检测结果
        """
        try:
            algorithm_type = algorithm_info.get('algorithm_type', 'standard_flat_open')

            # 根据算法类型执行对应分析
            if algorithm_type == 'drop_reversal':
                core_signals = self.analyze_opening_drop_reversal(code, stock_data)
            elif algorithm_type == 'high_open_correction':
                core_signals = self.analyze_high_open_correction(code, stock_data)
            elif algorithm_type == 'vshape_recovery':
                core_signals = self.analyze_vshape_recovery(code, stock_data)
            elif algorithm_type == 'mid_level_accumulation':
                core_signals = self.analyze_mid_level_accumulation(code, stock_data)
            elif algorithm_type == 'high_platform_breakout':
                core_signals = self.analyze_high_platform_breakout(code, stock_data)
            elif algorithm_type == 'enhanced_flat_open':
                core_signals = self.analyze_enhanced_flat_open(code, stock_data)
            elif algorithm_type == 'standard_flat_open':
                core_signals = self.analyze_flat_open_stock(code, stock_data)
            elif algorithm_type == 'standard_high_open':
                core_signals = self.analyze_high_open_stock(code, stock_data)
            else:
                # 观察或错误状态
                return {
                    'buy_signal': False,
                    'signal_strength': 0.0,
                    'recommendation': '⏳ 观察中',
                    'algorithm_type': algorithm_info.get('algorithm_name', '观察算法'),
                    'algorithm_info': algorithm_info,
                    'period_analysis': period_analysis,
                    'reason': algorithm_info.get('reason', '暂不符合买入条件')
                }

            # 实时盘口监控
            depth_analysis = self.analyze_realtime_depth(code, stock_data)

            # 综合信号强度计算
            signal_components = {
                'core_signals': core_signals.get('signal_strength', 0.0) * 0.4,      # 核心信号40%
                'depth_analysis': depth_analysis.get('signal_strength', 0.0) * 0.3,  # 盘口分析30%
                'period_analysis': period_analysis.get('signal_strength', 0.0) * 0.2, # 时段策略20%
                'algorithm_confidence': algorithm_info.get('confidence', 0.0) * 0.1   # 算法置信度10%
            }

            total_signal_strength = sum(signal_components.values())

            # 买入信号判断
            buy_signal = self.judge_intelligent_buy_signal(
                core_signals, depth_analysis, period_analysis, algorithm_info, total_signal_strength
            )

            # 生成最终建议
            recommendation = self.generate_intelligent_recommendation(
                buy_signal, total_signal_strength, algorithm_info, period_analysis
            )

            # 动态仓位计算
            position_size = self.calculate_intelligent_position_size(
                total_signal_strength, algorithm_info, period_analysis
            )

            # 如果满足买入条件，发送通知
            if buy_signal:
                self.send_intelligent_buy_notification(
                    code, stock_data, recommendation, total_signal_strength, algorithm_info
                )

            # 🚀 修复：添加开盘类型结果
            open_type_result = self.classify_open_type(stock_data)

            return {
                'buy_signal': buy_signal,
                'signal_strength': total_signal_strength,
                'recommendation': recommendation,
                'position_size': position_size,
                'algorithm_type': algorithm_info.get('algorithm_name', '未知算法'),
                'algorithm_info': algorithm_info,
                'period_analysis': period_analysis,
                'core_signals': core_signals,
                'depth_analysis': depth_analysis,
                'signal_components': signal_components,
                'analysis_time': datetime.now().strftime('%H:%M:%S'),
                'open_type_result': open_type_result  # 🚀 关键：添加开盘类型结果
            }

        except Exception as e:
            print(f"⚠️ 智能信号检测失败 {code}: {e}")

            # 🚀 修复：即使异常也要返回开盘类型结果
            try:
                open_type_result = self.classify_open_type(stock_data)
            except:
                open_type_result = {'type': 'unknown', 'type_name': '未知'}

            return {
                'buy_signal': False,
                'signal_strength': 0.0,
                'recommendation': '❌ 分析失败',
                'algorithm_type': '错误',
                'error': str(e),
                'open_type_result': open_type_result  # 🚀 确保开盘类型结果存在
            }

    def print_monitoring_result(self, code, signal_result, algorithm_info, period_analysis):
        """打印监控结果"""
        try:
            algorithm_name = algorithm_info.get('algorithm_name', '未知算法')
            period_name = period_analysis.get('period_name', '未知时段')
            recommendation = signal_result.get('recommendation', '未知')
            signal_strength = signal_result.get('signal_strength', 0)

            print(f"   📊 {code} | {algorithm_name} | {period_name}")
            print(f"      💡 {recommendation} | 信号强度: {signal_strength:.2f}")

        except Exception as e:
            print(f"   ⚠️ {code} 结果输出失败: {e}")

    # ==================== 基于状态的算法分析方法 ====================

    def analyze_v_reversal_with_state(self, code, stock_data, algorithm_info):
        """V字反转状态感知分析（按指南调整）"""
        try:
            current_gain = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])
            algorithm_type = algorithm_info.get('algorithm_type', '')

            volume_strong = volume_ratio > 2.0
            bid_strong = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False
            reversal_confirmed = current_gain > 1.0

            if algorithm_type == 'v_reversal_momentum':
                # 动量确认阶段
                if volume_strong and bid_strong and reversal_confirmed:
                    buy_signal = True
                    signal_strength = 0.95
                    reason = f"V字反转动量(完美): 量比{volume_ratio:.1f}✓, 买盘强度✓, 反转确认✓"
                elif volume_strong and reversal_confirmed:
                    buy_signal = True
                    signal_strength = 0.85
                    reason = f"V字反转动量(良好): 量比{volume_ratio:.1f}✓, 反转确认✓"
                elif volume_ratio > 1.8 and reversal_confirmed:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"V字反转动量(可接受): 量比{volume_ratio:.1f}(需>1.8), 反转确认✓"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"V字反转动量(不足): 量比{volume_ratio:.1f}(需>2.0), 反转{reversal_confirmed}"
            else:
                # 早期阶段
                if volume_strong and reversal_confirmed:
                    buy_signal = True
                    signal_strength = 0.85
                    reason = f"V字反转早期(优秀): 量比{volume_ratio:.1f}✓, 反转确认✓"
                elif volume_ratio > 1.5 and reversal_confirmed:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"V字反转早期(良好): 量比{volume_ratio:.1f}(需>1.5), 反转确认✓"
                elif reversal_confirmed:
                    buy_signal = True
                    signal_strength = 0.65
                    reason = f"V字反转早期(基础): 反转确认✓, 量比{volume_ratio:.1f}不足"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"V字反转早期(不足): 量比{volume_ratio:.1f}, 反转确认{reversal_confirmed}"

            return {
                'buy_signal': buy_signal,
                'signal_strength': signal_strength,
                'reason': reason,
                'current_gain': current_gain,
                'volume_ratio': volume_ratio
            }

        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'reason': f'V字反转分析失败: {e}'}

    def analyze_sideways_breakout_with_state(self, code, stock_data, algorithm_info):
        """横盘突破状态感知分析（按指南调整）"""
        try:
            current_gain = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])
            algorithm_type = algorithm_info.get('algorithm_type', '')

            price_breakout = current_gain > 0
            bid_strong = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False

            if algorithm_type == 'high_level_breakout':
                # 高位横盘突破（按指南要求）
                if volume_ratio > 2.5 and price_breakout and bid_strong:
                    buy_signal = True
                    signal_strength = 0.9
                    reason = f"高位横盘突破(完美): 量比{volume_ratio:.1f}✓, 突破✓, 买盘强✓"
                elif volume_ratio > 2.0 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.8
                    reason = f"高位横盘突破(良好): 量比{volume_ratio:.1f}✓, 突破✓"
                elif volume_ratio > 1.8 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"高位横盘突破(可接受): 量比{volume_ratio:.1f}(需>1.8), 突破✓"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"高位横盘突破(不足): 量比{volume_ratio:.1f}(需>2.0), 突破{price_breakout}"

            elif algorithm_type in ['mid_level_accumulation', 'mid_position_accumulation']:
                # 🚀 修复：中位蓄势突破（支持两种算法类型）
                if volume_ratio > 2.0 and price_breakout and bid_strong:
                    buy_signal = True
                    signal_strength = 0.85
                    reason = f"中位蓄势突破(完美): 量比{volume_ratio:.1f}✓, 突破✓, 买盘强✓"
                elif volume_ratio > 1.8 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.8
                    reason = f"中位蓄势突破(良好): 量比{volume_ratio:.1f}✓, 突破✓"
                elif volume_ratio > 1.5 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"中位蓄势突破(可接受): 量比{volume_ratio:.1f}(需>1.5), 突破✓"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"中位蓄势突破(不足): 量比{volume_ratio:.1f}(需>1.8), 突破{price_breakout}"

            else:  # low_level_consolidation
                # 低位整理突破（按指南调整）
                if volume_ratio > 2.5 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.8
                    reason = f"低位整理突破(优秀): 量比{volume_ratio:.1f}✓, 突破✓"
                elif volume_ratio > 2.0 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"低位整理突破(良好): 量比{volume_ratio:.1f}✓, 突破✓"
                elif volume_ratio > 1.5 and price_breakout:
                    buy_signal = True
                    signal_strength = 0.7
                    reason = f"低位整理突破(可接受): 量比{volume_ratio:.1f}(需>1.5), 突破✓"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"低位整理突破(不足): 量比{volume_ratio:.1f}(需>2.0), 突破{price_breakout}"

            return {
                'buy_signal': buy_signal,
                'signal_strength': signal_strength,
                'reason': reason,
                'current_gain': current_gain,
                'volume_ratio': volume_ratio
            }

        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'reason': f'横盘突破分析失败: {e}'}

    def analyze_trend_following_with_state(self, code, stock_data, algorithm_info):
        """趋势跟随状态感知分析（按指南调整）"""
        try:
            current_gain = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])
            algorithm_type = algorithm_info.get('algorithm_type', '')

            bid_ok = sum(bid_volumes) > sum(ask_volumes) * 1.5 if bid_volumes and ask_volumes else False

            if algorithm_type == 'momentum_continuation':
                # 高位动量延续
                if current_gain > 6.0:
                    return {
                        'buy_signal': False,
                        'signal_strength': 0.0,
                        'reason': f"高位动量但涨幅{current_gain:.1f}%过高，避免追涨"
                    }

                if volume_ratio > 3.0 and bid_ok:
                    buy_signal = True
                    signal_strength = 0.8
                    reason = f"高位动量延续(优秀): 量比{volume_ratio:.1f}✓, 买盘强✓"
                elif volume_ratio > 2.5 and bid_ok:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"高位动量延续(良好): 量比{volume_ratio:.1f}✓, 买盘强✓"
                elif volume_ratio > 2.0:
                    buy_signal = True
                    signal_strength = 0.7
                    reason = f"高位动量延续(可接受): 量比{volume_ratio:.1f}✓"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"高位动量延续(不足): 量比{volume_ratio:.1f}(需>2.5)"
            else:
                # 趋势跟随（按指南调整量比要求）
                if volume_ratio > 3.5 and bid_ok:
                    buy_signal = True
                    signal_strength = 0.85
                    reason = f"趋势跟随(完美): 量比{volume_ratio:.1f}✓, 买盘强✓"
                elif volume_ratio > 3.0 and bid_ok:
                    buy_signal = True
                    signal_strength = 0.8
                    reason = f"趋势跟随(优秀): 量比{volume_ratio:.1f}✓, 买盘强✓"
                elif volume_ratio > 2.5:
                    buy_signal = True
                    signal_strength = 0.75
                    reason = f"趋势跟随(良好): 量比{volume_ratio:.1f}✓"
                else:
                    buy_signal = False
                    signal_strength = 0.0
                    reason = f"趋势跟随(不足): 量比{volume_ratio:.1f}(需>3.0)"

            return {
                'buy_signal': buy_signal,
                'signal_strength': signal_strength,
                'reason': reason,
                'current_gain': current_gain,
                'volume_ratio': volume_ratio
            }

        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'reason': f'趋势跟随分析失败: {e}'}

    def analyze_pullback_with_state(self, code, stock_data, algorithm_info):
        """回调修复状态感知分析"""
        try:
            current_gain = stock_data.get('change_pct', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])
            algorithm_type = algorithm_info.get('algorithm_type', '')

            if algorithm_type == 'pullback_observation':
                # 观望为主，不发买入信号
                return {
                    'buy_signal': False,
                    'signal_strength': 0.0,
                    'reason': f"冲高回落观望，不发买入信号"
                }
            else:
                # 回调修复需要非常强的确认
                volume_strong = volume_ratio > 3.0
                bid_very_strong = sum(bid_volumes) > sum(ask_volumes) * 2.5 if bid_volumes and ask_volumes else False

                buy_signal = volume_strong and bid_very_strong
                signal_strength = 0.7 if buy_signal else 0.0
                reason = f"回调修复: 量比{volume_ratio:.1f}(需>3.0), 强买盘{bid_very_strong}"

                return {
                    'buy_signal': buy_signal,
                    'signal_strength': signal_strength,
                    'reason': reason,
                    'current_gain': current_gain,
                    'volume_ratio': volume_ratio
                }

        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'reason': f'回调分析失败: {e}'}

    def judge_state_aware_buy_signal(self, core_signals, depth_analysis, period_analysis, algorithm_info, total_strength):
        """基于状态的买入信号判断"""
        try:
            # 核心原则：完全信任算法内部的买入判断
            algorithm_buy_signal = core_signals.get('buy_signal', False)

            # 如果算法内部判断不买入，直接返回False
            if not algorithm_buy_signal:
                return False

            # 算法内部判断买入时，进行最后的安全检查

            # 1. 算法置信度检查
            algorithm_confidence = algorithm_info.get('confidence', 0.0)
            if algorithm_confidence < 0.8:
                print(f"   ⚠️ 算法置信度不足: {algorithm_confidence:.2f} < 0.8")
                return False

            # 2. 时段支持检查
            period_support = period_analysis.get('period_signal', False)
            if not period_support:
                print(f"   ⚠️ 时段不支持买入")
                return False

            # 3. 盘口深度检查
            depth_support = depth_analysis.get('buy_signal', False)
            if not depth_support:
                print(f"   ⚠️ 盘口深度不支持")
                return False

            # 4. 最终信号强度检查（降低门槛）
            if total_strength < 0.6:
                print(f"   ⚠️ 总信号强度不足: {total_strength:.2f} < 0.6")
                return False

            print(f"   ✅ 状态感知买入信号确认: 算法={algorithm_buy_signal}, 置信度={algorithm_confidence:.2f}, 强度={total_strength:.2f}")
            return True

        except Exception as e:
            print(f"⚠️ 状态感知买入判断失败: {e}")
            return False

    # ==================== 增强版算法实现 ====================

    def analyze_enhanced_flat_open(self, code, stock_data):
        """
        增强版平开算法（基于报告：平开→上涨→平台整理→突破）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 增强版平开分析结果
        """
        try:
            # 获取早盘关键数据
            morning_key = self.get_morning_key_data(code)
            current_price = stock_data.get('current_price', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            # 1. 量能对比（当前量能 vs 早盘最大量能）
            am_peak_vol = morning_key.get('max_volume_ratio', 0)
            volume_sustain = volume_ratio > am_peak_vol * 0.8 if am_peak_vol > 0 else False

            # 2. 平台强度系数计算
            platform_strength = self.calculate_platform_strength(code, stock_data)

            # 3. 突破确认检测
            morning_high = morning_key.get('morning_high', 0)
            breakthrough = current_price > morning_high * 1.005 if morning_high > 0 else False

            # 综合判断
            signals = {
                'volume_sustain': volume_sustain,
                'platform_strength': platform_strength > 0.6,
                'breakthrough': breakthrough,
                'buy_signal': False,
                'signal_strength': 0.0
            }

            signal_count = sum([signals['volume_sustain'], signals['platform_strength'], signals['breakthrough']])
            signals['signal_strength'] = signal_count / 3.0
            signals['buy_signal'] = signal_count >= 2

            return signals

        except Exception as e:
            print(f"⚠️ 增强版平开算法失败 {code}: {e}")
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def analyze_high_open_correction(self, code, stock_data):
        """
        高开修正算法（基于报告：高开回落企稳再攻）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 高开修正分析结果
        """
        try:
            current_price = stock_data.get('current_price', 0)
            open_price = stock_data.get('open', 0)
            prev_close = stock_data.get('prev_close', 0)

            # 1. 缺口保卫检测
            if prev_close > 0 and open_price > 0:
                open_gap = (open_price - prev_close) / prev_close
                gap_guard = current_price > (prev_close + open_gap * 0.5)
            else:
                gap_guard = False

            # 2. 主力吸筹检测（简化版）
            main_accumulate = self.check_main_accumulation(code, stock_data)

            # 3. 量能健康检测
            volume_healthy = self.check_volume_health_during_correction(code, stock_data)

            # 综合判断
            signals = {
                'gap_guard': gap_guard,
                'main_accumulate': main_accumulate,
                'volume_healthy': volume_healthy,
                'buy_signal': False,
                'signal_strength': 0.0
            }

            signal_count = sum([signals['gap_guard'], signals['main_accumulate'], signals['volume_healthy']])
            signals['signal_strength'] = signal_count / 3.0
            signals['buy_signal'] = signal_count >= 2

            return signals

        except Exception as e:
            print(f"⚠️ 高开修正算法失败 {code}: {e}")
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def analyze_vshape_recovery(self, code, stock_data):
        """
        V型反转算法（基于报告：深度回落后V型反转）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: V型反转分析结果
        """
        try:
            # 使用已有的开盘下跌反转算法
            return self.analyze_opening_drop_reversal(code, stock_data)

        except Exception as e:
            print(f"⚠️ V型反转算法失败 {code}: {e}")
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def analyze_high_platform_breakout(self, code, stock_data):
        """
        高位横盘突破算法（基于报告：6%-7%卡位突破）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 高位横盘突破分析结果
        """
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            # 1. 量能结构分析
            volume_score = self.analyze_volume_structure_for_breakout(code, stock_data)

            # 2. 盘口特征分析
            order_score = self.analyze_order_features_for_breakout(code, stock_data)

            # 3. 时间能量分析
            time_score = self.analyze_time_energy_for_breakout(code, stock_data)

            # 突破概率计算
            breakout_probability = 0.4 * volume_score + 0.35 * order_score + 0.25 * time_score

            # 综合判断
            signals = {
                'volume_score': volume_score,
                'order_score': order_score,
                'time_score': time_score,
                'breakout_probability': breakout_probability,
                'buy_signal': breakout_probability >= 0.75,
                'signal_strength': breakout_probability
            }

            return signals

        except Exception as e:
            print(f"⚠️ 高位横盘突破算法失败 {code}: {e}")
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def analyze_mid_level_accumulation(self, code, stock_data):
        """
        中位蓄势算法（基于报告：1%-5%平稳波动的方向预判）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 中位蓄势分析结果
        """
        try:
            # 1. 方向预判指标计算
            trend_prediction = self.calculate_trend_prediction(code, stock_data)

            # 2. 试盘线检测
            probe_line_detected = self.detect_probe_line(code, stock_data)

            # 3. 三大演变路径分析
            evolution_analysis = self.analyze_evolution_paths(code, stock_data)

            # 4. 买入条件检测
            buy_conditions = self.check_mid_level_buy_conditions(code, stock_data, trend_prediction)

            # 5. 卖出预警检测
            sell_warnings = self.check_mid_level_sell_warnings(code, stock_data)

            # 综合判断
            signals = {
                'trend_prediction': trend_prediction,
                'probe_line_detected': probe_line_detected,
                'evolution_analysis': evolution_analysis,
                'buy_conditions': buy_conditions,
                'sell_warnings': sell_warnings,
                'buy_signal': False,
                'signal_strength': 0.0
            }

            # 买入信号判断
            if (trend_prediction['score'] >= 0.7 and
                probe_line_detected and
                buy_conditions['all_satisfied'] and
                not sell_warnings['any_triggered']):

                signals['buy_signal'] = True
                signals['signal_strength'] = min(1.0, trend_prediction['score'] + 0.2)
            elif trend_prediction['score'] >= 0.4:
                signals['signal_strength'] = trend_prediction['score']
            else:
                signals['signal_strength'] = max(0.1, trend_prediction['score'])

            return signals

        except Exception as e:
            print(f"⚠️ 中位蓄势算法失败 {code}: {e}")
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def calculate_trend_prediction(self, code, stock_data):
        """
        方向预判指标计算（基于报告的预判算法）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 趋势预判结果
        """
        try:
            # 1. 量能趋势分析
            vol_trend = self.calculate_volume_trend(code, stock_data)

            # 2. 买五厚度变化率
            depth_change = self.calculate_depth_change_rate(code, stock_data)

            # 3. 横盘时长
            duration = self.estimate_platform_duration(code, stock_data)

            # 4. 按报告公式计算得分
            score = 0

            # 量能系数（0-0.4）
            if vol_trend > 0:  # 量能递增
                score += min(0.4, vol_trend * 0.1)

            # 盘口系数（0-0.3）
            if depth_change > 0.5:  # 买五增厚50%+
                score += 0.3
            elif depth_change > 0.2:
                score += 0.15

            # 时间系数（0-0.3）
            if duration < 30:  # 横盘时间短
                score += 0.3  # 能量充足
            elif duration < 60:
                score += 0.15
            else:
                score -= 0.2  # 能量衰竭

            # 确保得分在合理范围内
            score = max(0.0, min(1.0, score))

            # 判断方向倾向
            if score >= 0.7:
                direction = 'upward_breakout'
                direction_name = '向上突破'
                probability = 'high'
            elif score >= 0.4:
                direction = 'sideways_continue'
                direction_name = '继续横盘'
                probability = 'medium'
            else:
                direction = 'downward_break'
                direction_name = '向下破位'
                probability = 'low'

            return {
                'score': score,
                'direction': direction,
                'direction_name': direction_name,
                'probability': probability,
                'vol_trend': vol_trend,
                'depth_change': depth_change,
                'duration': duration,
                'components': {
                    'volume_score': min(0.4, vol_trend * 0.1) if vol_trend > 0 else 0,
                    'depth_score': 0.3 if depth_change > 0.5 else (0.15 if depth_change > 0.2 else 0),
                    'time_score': 0.3 if duration < 30 else (0.15 if duration < 60 else -0.2)
                }
            }

        except Exception as e:
            print(f"⚠️ 趋势预判计算失败 {code}: {e}")
            return {
                'score': 0.0,
                'direction': 'unknown',
                'direction_name': '未知',
                'probability': 'none',
                'error': str(e)
            }

    def detect_probe_line(self, code, stock_data):
        """
        试盘线检测（基于报告：突然拉升2%后回落至平台）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            bool: 是否检测到试盘线
        """
        try:
            if code not in self.realtime_data_series:
                return False

            data_series = self.realtime_data_series[code]
            if len(data_series) < 10:
                return False

            # 获取最近10个数据点的价格
            recent_prices = [item.get('current_price', 0) for item in data_series[-10:]]
            if not recent_prices or min(recent_prices) <= 0:
                return False

            current_price = recent_prices[-1]

            # 寻找试盘线模式：价格突然拉升2%+然后回落
            for i in range(len(recent_prices) - 3):
                base_price = recent_prices[i]
                peak_price = max(recent_prices[i+1:i+4])  # 接下来3个点的最高价

                if base_price > 0:
                    # 检查是否有2%+的拉升
                    surge_pct = (peak_price - base_price) / base_price
                    if surge_pct >= 0.02:  # 拉升2%+
                        # 检查是否回落到接近基础价格
                        if current_price <= base_price * 1.005:  # 回落到基础价格附近
                            return True

            return False

        except Exception as e:
            print(f"⚠️ 试盘线检测失败 {code}: {e}")
            return False

    def analyze_evolution_paths(self, code, stock_data):
        """
        三大演变路径分析（基于报告：向上突破/继续横盘/向下破位）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 演变路径分析结果
        """
        try:
            current_price = stock_data.get('current_price', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            # 获取平台高低点
            platform_high, platform_low = self.get_platform_high_low(code, stock_data)

            # 1. 向上突破路径分析
            upward_signals = {
                'price_breakout': current_price > platform_high * 1.005,  # 突破平台高点
                'volume_surge': volume_ratio > 1.5,  # 放量突破
                'bid_strength': self.check_bid_strength(code, stock_data)  # 买盘强度
            }
            upward_probability = sum(upward_signals.values()) / 3.0

            # 2. 继续横盘路径分析
            sideways_signals = {
                'volume_shrink': 0.8 <= volume_ratio <= 1.2,  # 量能温和
                'price_stable': platform_low <= current_price <= platform_high,  # 价格在平台内
                'time_factor': self.estimate_platform_duration(code, stock_data) < 60  # 时间未过长
            }
            sideways_probability = sum(sideways_signals.values()) / 3.0

            # 3. 向下破位路径分析
            downward_signals = {
                'price_breakdown': current_price < platform_low * 0.995,  # 跌破平台低点
                'volume_panic': volume_ratio > 2.0,  # 恐慌性放量
                'bid_weakness': not self.check_bid_strength(code, stock_data)  # 买盘疲弱
            }
            downward_probability = sum(downward_signals.values()) / 3.0

            # 确定最可能的路径
            probabilities = {
                'upward': upward_probability,
                'sideways': sideways_probability,
                'downward': downward_probability
            }

            most_likely_path = max(probabilities, key=probabilities.get)

            return {
                'upward_breakout': {
                    'probability': upward_probability,
                    'signals': upward_signals
                },
                'sideways_continue': {
                    'probability': sideways_probability,
                    'signals': sideways_signals
                },
                'downward_break': {
                    'probability': downward_probability,
                    'signals': downward_signals
                },
                'most_likely_path': most_likely_path,
                'platform_high': platform_high,
                'platform_low': platform_low
            }

        except Exception as e:
            print(f"⚠️ 演变路径分析失败 {code}: {e}")
            return {
                'most_likely_path': 'unknown',
                'error': str(e)
            }

    def check_mid_level_buy_conditions(self, code, stock_data, trend_prediction):
        """
        中位蓄势买入条件检测（基于报告的买入条件）

        Args:
            code: 股票代码
            stock_data: 股票数据
            trend_prediction: 趋势预判结果

        Returns:
            dict: 买入条件检测结果
        """
        try:
            # 1. 平台形成时间<45分钟
            duration = self.estimate_platform_duration(code, stock_data)
            time_condition = duration < 45

            # 2. 买五厚度增加>40%
            depth_change = self.calculate_depth_change_rate(code, stock_data)
            depth_condition = depth_change > 0.4

            # 3. 出现试盘线
            probe_condition = self.detect_probe_line(code, stock_data)

            # 4. 方向预判得分≥0.7
            score_condition = trend_prediction.get('score', 0) >= 0.7

            conditions = {
                'time_condition': time_condition,
                'depth_condition': depth_condition,
                'probe_condition': probe_condition,
                'score_condition': score_condition,
                'all_satisfied': all([time_condition, depth_condition, probe_condition, score_condition])
            }

            return conditions

        except Exception as e:
            print(f"⚠️ 买入条件检测失败 {code}: {e}")
            return {'all_satisfied': False, 'error': str(e)}

    def check_mid_level_sell_warnings(self, code, stock_data):
        """
        中位蓄势卖出预警检测（基于报告的预警条件）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 卖出预警检测结果
        """
        try:
            volume_ratio = stock_data.get('volume_ratio', 0)
            bid_volumes = stock_data.get('bid_volumes', [])

            # 1. 横盘时间>60分钟且量比<0.8
            duration = self.estimate_platform_duration(code, stock_data)
            time_volume_warning = duration > 60 and volume_ratio < 0.8

            # 2. 买一厚度减少50%+
            bid_weakness_warning = False
            if bid_volumes and len(bid_volumes) > 0:
                current_bid1 = bid_volumes[0]
                # 简化判断：如果买一量很小，认为厚度减少
                bid_weakness_warning = current_bid1 < 10000  # <1万股认为很薄

            # 3. 跌破平台低点且3分钟无法收复
            platform_high, platform_low = self.get_platform_high_low(code, stock_data)
            current_price = stock_data.get('current_price', 0)
            breakdown_warning = current_price < platform_low * 0.995

            warnings = {
                'time_volume_warning': time_volume_warning,
                'bid_weakness_warning': bid_weakness_warning,
                'breakdown_warning': breakdown_warning,
                'any_triggered': any([time_volume_warning, bid_weakness_warning, breakdown_warning])
            }

            return warnings

        except Exception as e:
            print(f"⚠️ 卖出预警检测失败 {code}: {e}")
            return {'any_triggered': True, 'error': str(e)}

    # ==================== 中位蓄势辅助方法 ====================

    def calculate_volume_trend(self, code, stock_data):
        """
        计算量能趋势（用于中位蓄势预判）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            float: 量能趋势值（正数表示递增，负数表示递减）
        """
        try:
            if code not in self.realtime_data_series:
                return 0.0

            data_series = self.realtime_data_series[code]
            if len(data_series) < 5:
                return 0.0

            # 获取最近5个量比数据
            recent_volumes = []
            for item in data_series[-5:]:
                vol_ratio = item.get('volume_ratio', 0)
                if vol_ratio > 0:
                    recent_volumes.append(vol_ratio)

            if len(recent_volumes) < 3:
                return 0.0

            # 计算量能趋势（简单线性回归斜率）
            n = len(recent_volumes)
            x_sum = sum(range(n))
            y_sum = sum(recent_volumes)
            xy_sum = sum(i * vol for i, vol in enumerate(recent_volumes))
            x2_sum = sum(i * i for i in range(n))

            # 斜率计算
            if n * x2_sum - x_sum * x_sum != 0:
                slope = (n * xy_sum - x_sum * y_sum) / (n * x2_sum - x_sum * x_sum)
                return slope
            else:
                return 0.0

        except Exception as e:
            print(f"⚠️ 量能趋势计算失败 {code}: {e}")
            return 0.0

    def calculate_depth_change_rate(self, code, stock_data):
        """
        计算买五厚度变化率

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            float: 厚度变化率
        """
        try:
            if code not in self.realtime_data_series:
                return 0.0

            data_series = self.realtime_data_series[code]
            if len(data_series) < 2:
                return 0.0

            # 获取当前和之前的买五厚度
            current_bid_volumes = stock_data.get('bid_volumes', [])
            prev_bid_volumes = data_series[-2].get('bid_volumes', []) if len(data_series) >= 2 else []

            if not current_bid_volumes or not prev_bid_volumes:
                return 0.0

            current_depth = sum(current_bid_volumes)
            prev_depth = sum(prev_bid_volumes)

            if prev_depth > 0:
                change_rate = (current_depth - prev_depth) / prev_depth
                return change_rate
            else:
                return 0.0

        except Exception as e:
            print(f"⚠️ 厚度变化率计算失败 {code}: {e}")
            return 0.0

    def get_platform_high_low(self, code, stock_data):
        """
        获取平台高低点

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            tuple: (平台高点, 平台低点)
        """
        try:
            if code not in self.realtime_data_series:
                current_price = stock_data.get('current_price', 0)
                return current_price, current_price

            data_series = self.realtime_data_series[code]
            if len(data_series) < 5:
                current_price = stock_data.get('current_price', 0)
                return current_price, current_price

            # 获取最近的价格数据（用于确定平台范围）
            recent_prices = [item.get('current_price', 0) for item in data_series[-20:]]
            recent_prices = [p for p in recent_prices if p > 0]

            if not recent_prices:
                current_price = stock_data.get('current_price', 0)
                return current_price, current_price

            platform_high = max(recent_prices)
            platform_low = min(recent_prices)

            return platform_high, platform_low

        except Exception as e:
            print(f"⚠️ 平台高低点获取失败 {code}: {e}")
            current_price = stock_data.get('current_price', 0)
            return current_price, current_price

    def calculate_recent_volatility(self, code, stock_data):
        """
        计算最近的价格波动率（用于识别平稳波动）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            float: 波动率百分比
        """
        try:
            if code not in self.realtime_data_series:
                return 0.0

            data_series = self.realtime_data_series[code]
            if len(data_series) < 5:
                return 0.0

            # 获取最近10个价格数据
            recent_prices = [item.get('current_price', 0) for item in data_series[-10:]]
            recent_prices = [p for p in recent_prices if p > 0]

            if len(recent_prices) < 3:
                return 0.0

            # 计算振幅
            price_high = max(recent_prices)
            price_low = min(recent_prices)

            if price_low > 0:
                volatility = (price_high - price_low) / price_low * 100
                return volatility
            else:
                return 0.0

        except Exception as e:
            print(f"⚠️ 波动率计算失败 {code}: {e}")
            return 0.0

    # ==================== 缺失的关键辅助方法 ====================

    def check_bid_strength(self, code, stock_data):
        """
        检测买盘强度

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            bool: 买盘是否强劲
        """
        try:
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])

            if not bid_volumes or not ask_volumes:
                return False

            # 买盘总厚度 vs 卖盘总厚度
            total_bid = sum(bid_volumes)
            total_ask = sum(ask_volumes)

            # 买盘强度判断：买盘厚度 > 卖盘厚度 * 1.5
            return total_bid > total_ask * 1.5

        except Exception as e:
            print(f"⚠️ 买盘强度检测失败 {code}: {e}")
            return False

    def check_turnover_health(self, code, stock_data):
        """
        检测换手率健康度（用于尾盘监控）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            bool: 换手率是否健康
        """
        try:
            # 简化实现：基于成交量判断
            volume = stock_data.get('volume', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            # 健康的换手率：量比适中，不过度放量
            return 1.0 <= volume_ratio <= 3.0

        except Exception as e:
            print(f"⚠️ 换手率健康检测失败 {code}: {e}")
            return False

    def check_limit_up_seal_strength(self, code, stock_data):
        """
        检测涨停封单强度

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            bool: 封单是否强劲
        """
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            bid_volumes = stock_data.get('bid_volumes', [])

            if prev_close <= 0:
                return False

            # 计算当前涨幅
            current_gain = (current_price - prev_close) / prev_close

            # 接近涨停（>9%）且买一厚度足够
            near_limit_up = current_gain > 0.09
            strong_bid = bid_volumes and bid_volumes[0] > 50000  # 买一>5万股

            return near_limit_up and strong_bid

        except Exception as e:
            print(f"⚠️ 涨停封单强度检测失败 {code}: {e}")
            return False

    def check_pattern_breakthrough_signal(self, code, stock_data):
        """
        检测形态突破信号（用于反转算法）

        Args:
            code: 股票代码
            stock_data: 股票数据

        Returns:
            dict: 形态突破信号结果
        """
        try:
            # 简化实现：基于价格突破判断
            current_price = stock_data.get('current_price', 0)
            morning_key = self.get_morning_key_data(code)
            morning_high = morning_key.get('morning_high', 0)

            # V型反转特征：突破早盘高点
            v_shape_break = current_price > morning_high * 1.002 if morning_high > 0 else False

            # 圆弧底特征：价格稳步上升
            arc_bottom = self.check_price_stability(code, stock_data)

            # 头肩底特征：多次试探后突破
            head_shoulder = self.detect_probe_line(code, stock_data)

            detected = any([v_shape_break, arc_bottom, head_shoulder])

            return {
                'detected': detected,
                'v_shape_break': v_shape_break,
                'arc_bottom': arc_bottom,
                'head_shoulder': head_shoulder,
                'signal_strength': sum([v_shape_break, arc_bottom, head_shoulder]) / 3.0,
                'description': '形态突破信号检测'
            }

        except Exception as e:
            print(f"⚠️ 形态突破信号检测失败 {code}: {e}")
            return {
                'detected': False,
                'signal_strength': 0.0,
                'description': f'检测失败: {e}'
            }

    # ==================== 辅助分析方法 ====================

    def calculate_platform_strength(self, code, stock_data):
        """计算平台强度系数"""
        try:
            # 简化实现：基于价格稳定性和时间持续性
            if code not in self.realtime_data_series:
                return 0.0

            data_series = self.realtime_data_series[code]
            if len(data_series) < 10:
                return 0.0

            # 获取最近10个数据点的价格
            recent_prices = [item.get('current_price', 0) for item in data_series[-10:]]
            if not recent_prices or min(recent_prices) <= 0:
                return 0.0

            # 计算价格振幅
            price_range = (max(recent_prices) - min(recent_prices)) / min(recent_prices)

            # 平台强度 = 1 - 振幅（振幅越小，平台越强）
            platform_strength = max(0.0, 1.0 - price_range * 10)

            return min(1.0, platform_strength)

        except Exception as e:
            print(f"⚠️ 计算平台强度失败 {code}: {e}")
            return 0.0

    def check_main_accumulation(self, code, stock_data):
        """检测主力吸筹（简化版）"""
        try:
            # 基于买卖盘厚度变化判断
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])

            if not bid_volumes or not ask_volumes:
                return False

            # 买盘厚度 vs 卖盘厚度
            total_bid = sum(bid_volumes)
            total_ask = sum(ask_volumes)

            # 主力吸筹特征：买盘明显厚于卖盘
            return total_bid > total_ask * 1.5

        except Exception:
            return False

    def check_volume_health_during_correction(self, code, stock_data):
        """检测回调期量能健康度"""
        try:
            volume_ratio = stock_data.get('volume_ratio', 0)

            # 健康的回调：量能不放大（量比<1.5）
            return volume_ratio < 1.5

        except Exception:
            return False

    def analyze_volume_structure_for_breakout(self, code, stock_data):
        """分析突破时的量能结构"""
        try:
            volume_ratio = stock_data.get('volume_ratio', 0)
            morning_key = self.get_morning_key_data(code)
            am_vol = morning_key.get('max_volume_ratio', 0)

            if am_vol > 0:
                health_index = volume_ratio / am_vol
                if health_index > 0.8:
                    return 0.9
                elif health_index > 0.5:
                    return 0.6
                else:
                    return 0.3
            else:
                return 0.5  # 默认中等

        except Exception:
            return 0.0

    def analyze_order_features_for_breakout(self, code, stock_data):
        """分析突破时的盘口特征"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [])
            ask_volumes = stock_data.get('ask_volumes', [])

            if not bid_volumes or not ask_volumes:
                return 0.0

            # 买盘厚度优势
            total_bid = sum(bid_volumes)
            total_ask = sum(ask_volumes)

            if total_ask > 0:
                bid_advantage = total_bid / total_ask
                if bid_advantage > 2.0:
                    return 0.9
                elif bid_advantage > 1.5:
                    return 0.7
                elif bid_advantage > 1.0:
                    return 0.5
                else:
                    return 0.2
            else:
                return 0.8  # 卖盘很薄，有利突破

        except Exception:
            return 0.0

    def analyze_time_energy_for_breakout(self, code, stock_data):
        """分析突破时的时间能量"""
        try:
            # 简化实现：基于横盘时长
            duration = self.estimate_platform_duration(code, stock_data)

            if duration < 30:
                return 0.8  # 能量充足
            elif duration < 60:
                return 0.5  # 能量中性
            else:
                return 0.2  # 能量衰竭

        except Exception:
            return 0.0

    def estimate_platform_duration(self, code, stock_data):
        """估算平台持续时间（分钟）"""
        try:
            if code not in self.realtime_data_series:
                return 0

            data_series = self.realtime_data_series[code]
            if len(data_series) < 5:
                return 0

            # 简化实现：返回数据点数量 * 0.5（假设每30秒一个数据点）
            return len(data_series) * 0.5

        except Exception:
            return 0

    # ==================== 智能判断和通知方法 ====================

    def judge_intelligent_buy_signal(self, core_signals, depth_analysis, period_analysis, algorithm_info, total_strength):
        """
        智能买入信号判断（修复版：严格按照各算法的内部逻辑）

        核心原则：
        1. 完全信任各算法的内部买入判断
        2. 只有算法内部判断为True才发送通知
        3. 不再使用宽松的OR逻辑
        """
        try:
            # 核心原则：完全信任算法内部的买入判断
            algorithm_buy_signal = core_signals.get('buy_signal', False)

            # 如果算法内部判断不买入，直接返回False
            if not algorithm_buy_signal:
                return False

            # 算法内部判断买入时，进行最后的安全检查

            # 1. 算法置信度检查（必须≥0.8，提高标准）
            algorithm_confidence = algorithm_info.get('confidence', 0.0)
            if algorithm_confidence < 0.8:
                print(f"   ⚠️ 算法置信度不足: {algorithm_confidence:.2f} < 0.8")
                return False

            # 2. 时段支持检查（必须有时段支持）
            period_support = period_analysis.get('period_signal', False)
            if not period_support:
                print(f"   ⚠️ 当前时段不支持买入信号")
                return False

            # 3. 深度分析风险检查
            risk_factors = depth_analysis.get('risk_factors', [])
            high_risk_count = len([r for r in risk_factors if r.get('level', '') == 'high'])
            if high_risk_count > 0:
                print(f"   ⚠️ 检测到{high_risk_count}个高风险因素")
                return False

            # 4. 最终信号强度检查（必须≥0.7，修复：降低门槛）
            if total_strength < 0.7:
                print(f"   ⚠️ 总信号强度不足: {total_strength:.2f} < 0.7")
                return False

            print(f"   ✅ 买入信号确认: 算法={algorithm_buy_signal}, 置信度={algorithm_confidence:.2f}, 强度={total_strength:.2f}")
            return True

        except Exception as e:
            print(f"⚠️ 智能买入判断失败: {e}")
            return False

    def generate_intelligent_recommendation(self, buy_signal, signal_strength, algorithm_info, period_analysis):
        """生成智能推荐"""
        try:
            algorithm_name = algorithm_info.get('algorithm_name', '未知算法')
            period_name = period_analysis.get('period_name', '未知时段')

            if buy_signal:
                if signal_strength >= 0.8:
                    return f"🚀 强烈买入 | {algorithm_name} | {period_name}"
                else:
                    return f"📈 建议买入 | {algorithm_name} | {period_name}"
            else:
                if signal_strength >= 0.5:
                    return f"👀 密切关注 | {algorithm_name} | {period_name}"
                elif signal_strength >= 0.3:
                    return f"📝 继续观察 | {algorithm_name} | {period_name}"
                else:
                    return f"⏳ 暂不关注 | {algorithm_name} | {period_name}"

        except Exception as e:
            return f"❌ 推荐生成失败: {e}"

    def calculate_intelligent_position_size(self, signal_strength, algorithm_info, period_analysis):
        """计算智能仓位大小（包含中位蓄势专用公式）"""
        try:
            algorithm_type = algorithm_info.get('algorithm_type', '')

            # 中位蓄势专用仓位公式（基于报告）
            if algorithm_type == 'mid_level_accumulation':
                return self.calculate_mid_level_position_size(signal_strength, algorithm_info, period_analysis)

            # 其他算法的通用仓位计算
            # 基础仓位
            base_position = signal_strength * 0.6

            # 算法置信度调整
            confidence_bonus = algorithm_info.get('confidence', 0.0) * 0.2

            # 时段优先级调整
            period_priority = period_analysis.get('signal_strength', 0.0) * 0.1

            # 综合仓位
            total_position = base_position + confidence_bonus + period_priority

            # 限制在合理范围内
            return max(0.1, min(0.7, total_position))

        except Exception as e:
            print(f"⚠️ 智能仓位计算失败: {e}")
            return 0.3  # 默认仓位

    def calculate_mid_level_position_size(self, signal_strength, algorithm_info, period_analysis):
        """
        中位蓄势专用仓位计算（基于报告的仓位管理公式）

        Args:
            signal_strength: 信号强度
            algorithm_info: 算法信息
            period_analysis: 时段分析

        Returns:
            float: 建议仓位比例
        """
        try:
            # 获取当前涨幅和预判得分
            # 这里需要从算法信息中获取，简化处理
            current_gain = 0.03  # 假设中位蓄势的平均涨幅3%
            predict_score = signal_strength

            # 基于报告的仓位公式
            base = 0.4 * (predict_score / 0.7)  # 基础仓位系数
            gain_bonus = min(0.2, current_gain * 0.05)  # 每涨1%加5%仓位

            position_size = min(0.6, base + gain_bonus)

            # 确保最小仓位
            return max(0.2, position_size)

        except Exception as e:
            print(f"⚠️ 中位蓄势仓位计算失败: {e}")
            return 0.3

    def send_intelligent_buy_notification(self, code, stock_data, recommendation, signal_strength, algorithm_info):
        """发送智能买入通知"""
        try:
            if not NOTIFICATION_AVAILABLE:
                return

            stock_name = self.monitored_stocks.get(code, {}).get('name', code)
            current_price = stock_data.get('current_price', 0)
            algorithm_name = algorithm_info.get('algorithm_name', '未知算法')

            title = f"🚀 连板潜力股买入信号"
            message = f"""
股票: {stock_name} ({code})
价格: {current_price:.2f}元
算法: {algorithm_name}
信号强度: {signal_strength:.2f}
建议: {recommendation}
时间: {datetime.now().strftime('%H:%M:%S')}
            """.strip()

            toaster.show_toast(
                title=title,
                msg=message,
                duration=self.config.get('notification_duration', 30),
                icon_path=None,
                threaded=True
            )

            print(f"🔔 发送买入通知: {stock_name} - {recommendation}")

        except Exception as e:
            print(f"⚠️ 发送智能通知失败: {e}")

    # ==================== 缺失的辅助方法 ====================

    def check_price_stability(self, code, stock_data):
        """检查价格稳定性"""
        try:
            if code not in self.realtime_data_series:
                return False

            data_series = self.realtime_data_series[code]
            if len(data_series) < 5:
                return False

            # 获取最近5个价格
            recent_prices = [item.get('current_price', 0) for item in data_series[-5:]]
            if not recent_prices or min(recent_prices) <= 0:
                return False

            # 计算价格稳定性（标准差）
            avg_price = sum(recent_prices) / len(recent_prices)
            variance = sum((p - avg_price) ** 2 for p in recent_prices) / len(recent_prices)
            std_dev = variance ** 0.5

            # 稳定性判断：标准差 < 平均价格的2%
            return std_dev < avg_price * 0.02

        except Exception:
            return False

    def calculate_surge_speed(self, code, stock_data):
        """计算涨速（每分钟涨幅）"""
        try:
            if code not in self.realtime_data_series:
                return 0.0

            data_series = self.realtime_data_series[code]
            if len(data_series) < 2:
                return 0.0

            # 获取最近两个数据点
            current_price = data_series[-1].get('current_price', 0)
            prev_price = data_series[-2].get('current_price', 0)

            if prev_price <= 0:
                return 0.0

            # 计算涨幅（假设数据间隔30秒，换算为每分钟）
            price_change = (current_price - prev_price) / prev_price
            surge_speed = price_change * 2  # 30秒 -> 1分钟

            return surge_speed

        except Exception:
            return 0.0

    def check_platform_breakthrough(self, code, stock_data):
        """检查平台突破"""
        try:
            morning_key = self.get_morning_key_data(code)
            current_price = stock_data.get('current_price', 0)
            morning_high = morning_key.get('morning_high', 0)

            # 简化判断：突破早盘高点
            return current_price > morning_high * 1.002 if morning_high > 0 else False

        except Exception:
            return False

    def is_trading_time(self):
        """检查是否在交易时间（支持9:15开始监控）"""
        try:
            # 测试模式：总是允许监控
            if hasattr(self, 'test_mode') and self.test_mode:
                return True

            now = datetime.now()
            current_time = now.time()
            weekday = now.weekday()

            # 周末不交易
            if weekday >= 5:  # 5=周六, 6=周日
                return False

            # 🚀 扩展交易时间段（支持9:15开始监控）
            # 集合竞价时间：9:15-9:25
            auction_start = datetime.strptime("09:15", "%H:%M").time()
            auction_end = datetime.strptime("09:25", "%H:%M").time()

            # 连续竞价时间：9:30-11:30, 13:00-15:00
            morning_start = datetime.strptime("09:30", "%H:%M").time()
            morning_end = datetime.strptime("11:30", "%H:%M").time()
            afternoon_start = datetime.strptime("13:00", "%H:%M").time()
            afternoon_end = datetime.strptime("15:00", "%H:%M").time()

            # 检查是否在监控时间内
            in_auction = auction_start <= current_time <= auction_end      # 集合竞价
            in_morning = morning_start <= current_time <= morning_end      # 上午交易
            in_afternoon = afternoon_start <= current_time <= afternoon_end  # 下午交易

            return in_auction or in_morning or in_afternoon

        except Exception as e:
            print(f"⚠️ 交易时间检查失败: {e}")
            return True  # 异常时默认认为是交易时间

    def is_limit_up_stock(self, change_pct):
        """
        硬性判断股票是否涨停（基于实时涨跌幅）

        Args:
            change_pct: 实时涨跌幅

        Returns:
            bool: 是否涨停
        """
        return change_pct >= 9.9  # 涨幅≥9.9%视为涨停

    def fetch_realtime_stock_data(self, code):
        """获取实时股票数据"""
        try:
            # 获取实时行情
            quote_df = enhanced_stock_fetcher.get_realtime_quote([code])

            if quote_df.empty:
                return None

            quote_data = quote_df.iloc[0].to_dict()

            # 获取五档行情
            depth_df = enhanced_stock_fetcher.get_market_depth(code)

            if not depth_df.empty:
                depth_data = depth_df.iloc[0].to_dict()

                # 整合买卖五档数据 - 支持多种字段名格式
                quote_data['bid_volumes'] = [
                    depth_data.get('bv1', depth_data.get('买一量', depth_data.get('bid1_volume', 0))),
                    depth_data.get('bv2', depth_data.get('买二量', depth_data.get('bid2_volume', 0))),
                    depth_data.get('bv3', depth_data.get('买三量', depth_data.get('bid3_volume', 0))),
                    depth_data.get('bv4', depth_data.get('买四量', depth_data.get('bid4_volume', 0))),
                    depth_data.get('bv5', depth_data.get('买五量', depth_data.get('bid5_volume', 0)))
                ]
                quote_data['ask_volumes'] = [
                    depth_data.get('sv1', depth_data.get('卖一量', depth_data.get('ask1_volume', 0))),
                    depth_data.get('sv2', depth_data.get('卖二量', depth_data.get('ask2_volume', 0))),
                    depth_data.get('sv3', depth_data.get('卖三量', depth_data.get('ask3_volume', 0))),
                    depth_data.get('sv4', depth_data.get('卖四量', depth_data.get('ask4_volume', 0))),
                    depth_data.get('sv5', depth_data.get('卖五量', depth_data.get('ask5_volume', 0)))
                ]
                quote_data['bid_prices'] = [
                    depth_data.get('b1', depth_data.get('买一价', depth_data.get('bid1_price', 0))),
                    depth_data.get('b2', depth_data.get('买二价', depth_data.get('bid2_price', 0))),
                    depth_data.get('b3', depth_data.get('买三价', depth_data.get('bid3_price', 0))),
                    depth_data.get('b4', depth_data.get('买四价', depth_data.get('bid4_price', 0))),
                    depth_data.get('b5', depth_data.get('买五价', depth_data.get('bid5_price', 0)))
                ]
                quote_data['ask_prices'] = [
                    depth_data.get('s1', depth_data.get('卖一价', depth_data.get('ask1_price', 0))),
                    depth_data.get('s2', depth_data.get('卖二价', depth_data.get('ask2_price', 0))),
                    depth_data.get('s3', depth_data.get('卖三价', depth_data.get('ask3_price', 0))),
                    depth_data.get('s4', depth_data.get('卖四价', depth_data.get('ask4_price', 0))),
                    depth_data.get('s5', depth_data.get('卖五价', depth_data.get('ask5_price', 0)))
                ]

                # 确保数据类型正确
                quote_data['bid_volumes'] = [float(v) if v else 0 for v in quote_data['bid_volumes']]
                quote_data['ask_volumes'] = [float(v) if v else 0 for v in quote_data['ask_volumes']]
                quote_data['bid_prices'] = [float(p) if p else 0 for p in quote_data['bid_prices']]
                quote_data['ask_prices'] = [float(p) if p else 0 for p in quote_data['ask_prices']]

            else:
                # 如果没有五档数据，使用默认值
                quote_data['bid_volumes'] = [0] * 5
                quote_data['ask_volumes'] = [0] * 5
                quote_data['bid_prices'] = [0] * 5
                quote_data['ask_prices'] = [0] * 5

            # 补充历史数据（用于开盘类型分类）
            if code in self.historical_base_data:
                historical_data = self.historical_base_data[code]
                quote_data['prev_close'] = historical_data.get('prev_close', 0)
                quote_data['prev_high'] = historical_data.get('prev_high', 0)
                quote_data['prev_low'] = historical_data.get('prev_low', 0)
                quote_data['prev_open'] = historical_data.get('prev_open', 0)

                # 补充技术指标基准
                quote_data['ma5'] = historical_data.get('ma5', 0)
                quote_data['ma10'] = historical_data.get('ma10', 0)
                quote_data['ma20'] = historical_data.get('ma20', 0)

                # 补充成交量基准
                quote_data['avg_volume_5d'] = historical_data.get('avg_volume_5d', 0)
                quote_data['avg_volume_10d'] = historical_data.get('avg_volume_10d', 0)
                quote_data['avg_volume_30d'] = historical_data.get('avg_volume_30d', 0)

            return quote_data

        except Exception as e:
            print(f"⚠️ 获取实时数据失败 {code}: {e}")
            return None

    def update_stock_display(self, code, stock_data, signal_result):
        """更新股票在表格中的显示"""
        try:
            if not self.tree.exists(code):
                return

            # 获取开盘类型
            open_type_result = signal_result.get('open_type_result', {})
            open_type_name = open_type_result.get('type_name', '未知')
            open_type_emoji = self.get_open_type_emoji(open_type_result.get('type', 'unknown'))

            # 获取核心信号状态
            core_signals = signal_result.get('core_signals', {})

            # 根据开盘类型显示不同的信号
            if open_type_result.get('type') == 'limit_up':
                # 涨停股：显示涨停状态
                volume_signal = '🚫'
                depth_signal = '🚫'
                pattern_signal = '🚫'
            elif open_type_result.get('type') == 'flat_open':
                # 平开股：量能突击、盘口异动、形态突破
                volume_signal = '✅' if core_signals.get('volume_spike', False) else '❌'
                depth_signal = '✅' if core_signals.get('depth_anomaly', False) else '❌'
                pattern_signal = '✅' if core_signals.get('pattern_break', False) else '❌'
            elif open_type_result.get('type') == 'high_open':
                # 高开股：量能持续、抛压测试、跳空守护
                volume_signal = '✅' if core_signals.get('volume_consistent', False) else '❌'
                depth_signal = '✅' if core_signals.get('pressure_test', False) else '❌'
                pattern_signal = '✅' if core_signals.get('gap_protection', False) else '❌'
            elif open_type_result.get('type') == 'low_open':
                # 🚀 新增：低开股：量能反转、价格回升、形态反转
                volume_signal = '✅' if core_signals.get('volume_reversal', False) else '❌'
                depth_signal = '✅' if core_signals.get('price_recovery', False) else '❌'
                pattern_signal = '✅' if core_signals.get('pattern_reversal', False) else '❌'
            else:
                # 其他类型
                volume_signal = '❌'
                depth_signal = '❌'
                pattern_signal = '❌'

            # 综合信号强度
            signal_strength = signal_result.get('signal_strength', 0)
            signal_display = f"{signal_strength:.1%}"

            # 买入建议
            recommendation = signal_result.get('recommendation', '未知')

            # 当前价格
            current_price = stock_data.get('current_price', 0)
            price_display = f"{current_price:.2f}" if current_price > 0 else '-'

            # 更新时间
            update_time = datetime.now().strftime('%H:%M:%S')

            # 🚀 修复：添加实时涨跌显示
            change_pct = stock_data.get('change_pct', 0)
            if change_pct > 0:
                change_display = f"+{change_pct:.2f}%"
            elif change_pct < 0:
                change_display = f"{change_pct:.2f}%"
            else:
                change_display = "0.00%"

            # 更新表格行
            self.tree.item(code, values=(
                code,                                    # 股票代码
                self.monitored_stocks[code]['name'],     # 股票名称
                change_display,                          # 🚀 实时涨跌
                f"{open_type_emoji}{open_type_name}",    # 开盘类型
                volume_signal,                           # 量能突击/持续
                depth_signal,                            # 盘口异动/抛压测试
                pattern_signal,                          # 形态突破/跳空守护
                signal_display,                          # 综合信号
                recommendation,                          # 买入建议
                price_display,                           # 当前价格
                update_time                              # 更新时间
            ))

            # 如果有买入信号，高亮显示
            if signal_result.get('buy_signal', False):
                self.tree.set(code, '买入建议', f"🚀 {recommendation}")
                # 可以添加行颜色变化等视觉效果

        except Exception as e:
            print(f"⚠️ 更新股票显示失败 {code}: {e}")

    def show_settings(self):
        """显示设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("监控设置")
        dialog.geometry("500x600")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 50))

        # 创建设置界面
        notebook = ttk.Notebook(dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 基础设置页
        basic_frame = ttk.Frame(notebook)
        notebook.add(basic_frame, text="基础设置")

        # 扫描间隔设置
        ttk.Label(basic_frame, text="扫描间隔设置:").pack(anchor=tk.W, pady=(10, 5))

        scan_frame = ttk.Frame(basic_frame)
        scan_frame.pack(fill=tk.X, pady=5)

        ttk.Label(scan_frame, text="常规扫描间隔(秒):").pack(side=tk.LEFT)
        scan_var = tk.StringVar(value=str(self.config['scan_interval']))
        ttk.Entry(scan_frame, textvariable=scan_var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # 阈值设置页
        threshold_frame = ttk.Frame(notebook)
        notebook.add(threshold_frame, text="阈值设置")

        # 平开股阈值
        ttk.Label(threshold_frame, text="平开股阈值设置:").pack(anchor=tk.W, pady=(10, 5))

        flat_frame = ttk.LabelFrame(threshold_frame, text="平开股参数")
        flat_frame.pack(fill=tk.X, pady=5, padx=5)

        # 量能突击阈值
        volume_spike_frame = ttk.Frame(flat_frame)
        volume_spike_frame.pack(fill=tk.X, pady=2)
        ttk.Label(volume_spike_frame, text="买一量比突增阈值:").pack(side=tk.LEFT)
        volume_spike_var = tk.StringVar(value=str(self.config['volume_spike_threshold']))
        ttk.Entry(volume_spike_frame, textvariable=volume_spike_var, width=10).pack(side=tk.LEFT, padx=(5, 0))
        ttk.Label(volume_spike_frame, text="倍").pack(side=tk.LEFT)

        # 保存按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)

        def save_settings():
            try:
                # 保存设置
                self.config['scan_interval'] = int(scan_var.get())
                self.config['volume_spike_threshold'] = float(volume_spike_var.get())

                # 保存到文件
                self.save_config()

                messagebox.showinfo("成功", "设置已保存")
                dialog.destroy()

            except ValueError:
                messagebox.showerror("错误", "请输入有效的数值")
            except Exception as e:
                messagebox.showerror("错误", f"保存设置失败: {e}")

        ttk.Button(button_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=(0, 10))
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT)


def main():
    """主程序入口"""
    try:
        print("🚀 启动连板潜力股实时监控...")
        app = LimitUpPotentialMonitor()

        print("🚀 连板潜力股实时监控启动成功")
        print("🎯 全时段涨停捕捉系统")
        print("="*50)

        app.root.mainloop()

    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
