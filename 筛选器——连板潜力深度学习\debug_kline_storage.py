#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试K线数据存储问题
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_layer.efinance_data_fetcher import EfinanceDataFetcher
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_single_stock_kline():
    """调试单只股票的K线数据获取和存储"""
    print("🔍 调试单只股票K线数据流程...")
    
    # 创建数据获取器和存储器
    fetcher = EfinanceDataFetcher()
    storage = HistoricalDataStorage()
    
    # 测试股票（从训练日志中选择一个有问题的股票）
    test_stock = "600191"  # 华能国际
    
    print(f"📊 测试股票: {test_stock}")
    
    try:
        # 1. 获取K线数据
        print("\n1️⃣ 获取K线数据...")
        kline_data = fetcher.get_stock_kline_data(test_stock, days=30)
        
        if not kline_data.empty:
            print(f"✅ K线数据获取成功: {len(kline_data)}条记录")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   数据类型:\n{kline_data.dtypes}")
            print(f"   样本数据:\n{kline_data.head(3)}")
            
            # 检查关键列是否存在
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume', 'amount']
            missing_columns = [col for col in required_columns if col not in kline_data.columns]
            if missing_columns:
                print(f"❌ 缺少必要列: {missing_columns}")
                return False
            
            # 2. 存储K线数据
            print("\n2️⃣ 存储K线数据...")
            stored_count = storage.store_kline_data(test_stock, kline_data)
            print(f"✅ K线数据存储完成: {stored_count}条记录")
            
            if stored_count > 0:
                # 3. 验证存储的数据
                print("\n3️⃣ 验证存储的数据...")
                stored_data = storage.get_kline_data(test_stock)
                print(f"✅ 从数据库读取: {len(stored_data)}条记录")
                
                if not stored_data.empty:
                    print(f"   存储的数据样本:\n{stored_data.head(3)}")
                    return True
                else:
                    print("❌ 数据库中没有找到存储的数据")
                    return False
            else:
                print("❌ K线数据存储失败")
                return False
        else:
            print("❌ K线数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 调试过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_multiple_stocks():
    """调试多只股票的K线数据"""
    print("\n🔍 调试多只股票K线数据...")
    
    # 从训练日志中选择几只有问题的股票
    test_stocks = ["600191", "002261", "002207", "002940"]
    
    fetcher = EfinanceDataFetcher()
    storage = HistoricalDataStorage()
    
    results = {}
    
    for stock_code in test_stocks:
        print(f"\n📊 测试股票: {stock_code}")
        
        try:
            # 获取K线数据
            kline_data = fetcher.get_stock_kline_data(stock_code, days=30)
            
            if not kline_data.empty:
                print(f"✅ 获取成功: {len(kline_data)}条")
                
                # 存储数据
                stored_count = storage.store_kline_data(stock_code, kline_data)
                print(f"✅ 存储成功: {stored_count}条")
                
                # 验证数据
                stored_data = storage.get_kline_data(stock_code)
                print(f"✅ 验证成功: {len(stored_data)}条")
                
                results[stock_code] = {
                    "获取": len(kline_data),
                    "存储": stored_count,
                    "验证": len(stored_data),
                    "状态": "成功" if len(stored_data) > 0 else "失败"
                }
            else:
                print(f"❌ 获取失败")
                results[stock_code] = {
                    "获取": 0,
                    "存储": 0,
                    "验证": 0,
                    "状态": "获取失败"
                }
                
        except Exception as e:
            print(f"❌ 处理{stock_code}失败: {e}")
            results[stock_code] = {
                "获取": 0,
                "存储": 0,
                "验证": 0,
                "状态": f"异常: {str(e)}"
            }
    
    # 打印结果汇总
    print("\n📋 测试结果汇总:")
    print("股票代码\t获取\t存储\t验证\t状态")
    print("-" * 50)
    for stock_code, result in results.items():
        print(f"{stock_code}\t{result['获取']}\t{result['存储']}\t{result['验证']}\t{result['状态']}")
    
    return results

def check_database_status():
    """检查数据库当前状态"""
    print("\n🔍 检查数据库当前状态...")
    
    import sqlite3
    conn = sqlite3.connect('historical_data.db')
    
    try:
        # 检查K线数据表
        count = conn.execute('SELECT COUNT(*) FROM stock_kline_data').fetchone()[0]
        print(f"📊 K线数据总数: {count}")
        
        if count > 0:
            # 按股票分组统计
            stock_counts = pd.read_sql(
                'SELECT stock_code, COUNT(*) as count FROM stock_kline_data GROUP BY stock_code ORDER BY count DESC', 
                conn
            )
            print(f"📊 股票分布:\n{stock_counts}")
            
            # 检查日期范围
            date_range = pd.read_sql(
                'SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_kline_data', 
                conn
            )
            print(f"📊 日期范围: {date_range.iloc[0]['min_date']} 到 {date_range.iloc[0]['max_date']}")
        
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
    
    finally:
        conn.close()

def main():
    """主函数"""
    print("🔧 K线数据存储调试开始...")
    print("="*60)
    
    # 检查数据库当前状态
    check_database_status()
    
    # 调试单只股票
    single_result = debug_single_stock_kline()
    
    # 调试多只股票
    multiple_results = debug_multiple_stocks()
    
    # 再次检查数据库状态
    print("\n" + "="*60)
    print("🔍 调试后数据库状态:")
    check_database_status()
    
    print("\n" + "="*60)
    print("📋 调试结论:")
    
    if single_result:
        print("✅ 单只股票K线数据流程正常")
    else:
        print("❌ 单只股票K线数据流程异常")
    
    success_count = sum(1 for result in multiple_results.values() if result['状态'] == '成功')
    total_count = len(multiple_results)
    print(f"📊 多只股票测试: {success_count}/{total_count} 成功")
    
    if success_count == total_count:
        print("✅ K线数据存储功能正常，问题可能在训练流程的其他环节")
    else:
        print("❌ K线数据存储存在问题，需要进一步修复")
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
