#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
潜在股监控器
基于6大黄金启动信号的实时股票监控系统
实现买盘托单突变、卖压枯竭等关键信号的实时检测
"""

import tkinter as tk
from tkinter import ttk, messagebox, simpledialog
import pandas as pd
import numpy as np
import threading
import time
from datetime import datetime, timedelta
import json
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 导入现有应用的数据获取模块
try:
    from enhanced_data_fetcher import EnhancedStockDataFetcher
    enhanced_stock_fetcher = EnhancedStockDataFetcher()
    print("✅ 成功导入增强数据获取器")
except ImportError as e:
    print(f"⚠️ 无法导入增强数据获取器: {e}")
    print("⚠️ 尝试使用备用数据获取方法...")

    # 备用数据获取器
    try:
        import akshare as ak
        import requests
        AKSHARE_AVAILABLE = True
        print("✅ AKShare数据源可用")

        class BackupDataFetcher:
            """备用数据获取器"""

            def __init__(self):
                self.session = requests.Session()
                self.session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                })

            def get_stock_basic_info(self, code):
                """获取股票基本信息"""
                try:
                    # 方法1：使用代码对照表（最快最稳定）
                    try:
                        df = ak.stock_info_a_code_name()
                        if not df.empty:
                            df['code'] = df['code'].astype(str).str.zfill(6)
                            stock_row = df[df['code'] == code]
                            if not stock_row.empty:
                                stock_name = stock_row['name'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法1代码对照表获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法1代码对照表失败 {code}: {e}")

                    # 方法2：使用个股信息接口
                    try:
                        stock_info = ak.stock_individual_info_em(symbol=code)
                        if not stock_info.empty:
                            name_row = stock_info[stock_info['item'] == '股票简称']
                            if not name_row.empty:
                                stock_name = name_row['value'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法2个股信息获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法2个股信息失败 {code}: {e}")

                    # 方法3：从实时行情中获取股票名称（备用）
                    try:
                        df = ak.stock_zh_a_spot_em()
                        if not df.empty:
                            df['代码'] = df['代码'].astype(str).str.zfill(6)
                            stock_row = df[df['代码'] == code]
                            if not stock_row.empty:
                                stock_name = stock_row['名称'].iloc[0]
                                if stock_name and stock_name.strip():
                                    print(f"   ✅ 方法3实时行情获取: {code} -> {stock_name.strip()}")
                                    return {'name': stock_name.strip()}
                    except Exception as e:
                        print(f"   ⚠️ 方法3实时行情失败 {code}: {e}")

                    # 方法4：从本地映射表获取
                    try:
                        from stock_name_mapping import get_stock_name_from_mapping
                        mapped_name = get_stock_name_from_mapping(code)
                        if mapped_name:
                            print(f"   ✅ 方法4本地映射获取: {code} -> {mapped_name}")
                            return {'name': mapped_name}
                    except Exception as e:
                        print(f"   ⚠️ 方法4本地映射失败 {code}: {e}")

                    # 如果所有方法都失败，返回默认名称
                    print(f"   ⚠️ 所有方法都无法获取股票名称 {code}，使用默认名称")
                    return {'name': f'股票{code}'}

                except Exception as e:
                    print(f"   ❌ 获取股票基本信息异常 {code}: {e}")
                    return {'name': f'股票{code}'}

            def get_realtime_quote(self, codes):
                """获取实时行情"""
                try:
                    # 使用AKShare获取实时行情
                    df = ak.stock_zh_a_spot_em()

                    if df.empty:
                        return pd.DataFrame()

                    # 筛选指定股票
                    df['代码'] = df['代码'].astype(str).str.zfill(6)
                    filtered_df = df[df['代码'].isin(codes)]

                    # 标准化列名以匹配主应用格式
                    if not filtered_df.empty:
                        filtered_df = filtered_df.rename(columns={
                            '代码': 'stock_code',
                            '名称': 'stock_name',
                            '最新价': 'current_price',
                            '涨跌额': 'change',
                            '涨跌幅': 'change_pct',
                            '今开': 'open',
                            '最高': 'high',
                            '最低': 'low',
                            '昨收': 'prev_close',
                            '成交量': 'volume',
                            '成交额': 'amount',
                            '总市值': 'market_cap'
                        })

                    return filtered_df

                except Exception as e:
                    print(f"⚠️ 获取实时行情失败: {e}")
                    return pd.DataFrame()

            def get_market_depth(self, code):
                """获取五档行情"""
                try:
                    # 使用新浪财经API获取五档数据
                    url = f"http://hq.sinajs.cn/list={self._get_market_prefix(code)}{code}"
                    response = self.session.get(url, timeout=10)

                    if response.status_code != 200:
                        return pd.DataFrame()

                    data = response.text
                    if 'var hq_str_' not in data:
                        return pd.DataFrame()

                    # 解析数据
                    content = data.split('"')[1]
                    items = content.split(',')

                    if len(items) < 33:
                        return pd.DataFrame()

                    # 构建五档数据
                    depth_data = {
                        '买1': float(items[6]) if items[6] else 0,
                        '买量1': int(items[10]) if items[10] else 0,
                        '买2': float(items[7]) if items[7] else 0,
                        '买量2': int(items[11]) if items[11] else 0,
                        '买3': float(items[8]) if items[8] else 0,
                        '买量3': int(items[12]) if items[12] else 0,
                        '买4': float(items[9]) if items[9] else 0,
                        '买量4': int(items[13]) if items[13] else 0,
                        '买5': float(items[14]) if len(items) > 14 and items[14] else 0,
                        '买量5': int(items[15]) if len(items) > 15 and items[15] else 0,
                        '卖1': float(items[20]) if items[20] else 0,
                        '卖量1': int(items[21]) if items[21] else 0,
                        '卖2': float(items[22]) if items[22] else 0,
                        '卖量2': int(items[23]) if items[23] else 0,
                        '卖3': float(items[24]) if items[24] else 0,
                        '卖量3': int(items[25]) if items[25] else 0,
                        '卖4': float(items[26]) if items[26] else 0,
                        '卖量4': int(items[27]) if items[27] else 0,
                        '卖5': float(items[28]) if items[28] else 0,
                        '卖量5': int(items[29]) if items[29] else 0,
                    }

                    return pd.DataFrame([depth_data])

                except Exception as e:
                    print(f"⚠️ 获取五档数据失败 {code}: {e}")
                    return pd.DataFrame()

            def get_daily_kline(self, code, days=2):
                """获取日K线数据"""
                try:
                    df = ak.stock_zh_a_hist(symbol=code, adjust="qfq")
                    if not df.empty:
                        return df.tail(days)
                    return pd.DataFrame()
                except Exception as e:
                    print(f"⚠️ 获取日K线数据失败 {code}: {e}")
                    return pd.DataFrame()

            def _get_market_prefix(self, code):
                """获取市场前缀"""
                if code.startswith(('600', '601', '603', '688')):
                    return 'sh'
                elif code.startswith(('000', '001', '002', '300')):
                    return 'sz'
                else:
                    return 'sh'

        enhanced_stock_fetcher = BackupDataFetcher()
        print("✅ 备用数据获取器初始化成功")

    except ImportError:
        enhanced_stock_fetcher = None
        print("❌ 所有数据获取器都不可用")

# Windows通知模块
try:
    from win10toast import ToastNotifier
    toaster = ToastNotifier()
    NOTIFICATION_AVAILABLE = True
    print("✅ Windows通知功能可用")
except ImportError:
    try:
        from plyer import notification
        NOTIFICATION_AVAILABLE = True
        print("✅ 跨平台通知功能可用")
    except ImportError:
        NOTIFICATION_AVAILABLE = False
        print("⚠️ 通知功能不可用")

class PotentialStockMonitor:
    """潜在股监控器主类"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🚨 潜在股监控器 - 6大黄金启动信号实时监控")
        self.root.geometry("1200x800")
        self.root.configure(bg='#f0f0f0')

        # 设置全局字体
        self.setup_fonts()
        
        # 监控配置
        self.config = {
            'scan_interval': 30,           # 扫描间隔（秒）
            'bid_surge_threshold': 5.0,    # 买一托单突增倍数
            'ask_pressure_threshold': 0.3, # 卖压系数阈值
            'volume_pulse_threshold': 2.5, # 量能突击倍数
            'price_strength_threshold': 0.003, # 价格韧性阈值
            'notification_duration': 30,   # 通知持续时间（秒）
        }
        
        # 监控状态
        self.monitoring = False
        self.monitor_thread = None
        self.monitored_stocks = {}  # {code: {name, data, signals}}

        # 数据分层管理
        self.historical_base_data = {}  # 历史基础数据（一次性加载）
        self.realtime_data_cache = {}   # 实时数据缓存（每30秒更新）
        self.historical_cache = {}      # 临时历史数据缓存（兼容性保留）
        
        # 创建GUI界面
        self.create_gui()
        
        # 加载配置
        self.load_config()
        
        print("✅ 潜在股监控器初始化完成")

    def initialize_stock_monitoring(self, code):
        """初始化股票监控（加载历史基础数据）"""
        print(f"📊 初始化 {code} 的历史基础数据...")

        try:
            base_data = {}

            # 1. 加载历史K线数据（用于计算基准）
            print(f"   🔄 加载 {code} 历史K线数据...")
            kline_history = enhanced_stock_fetcher.get_daily_kline(code, 30)

            if not kline_history.empty and len(kline_history) >= 2:
                base_data['kline_history_30d'] = kline_history
                base_data['prev_close'] = float(kline_history.iloc[-2].get('收盘', kline_history.iloc[-2].get('close', 0)))
                base_data['prev_high'] = float(kline_history.iloc[-2].get('最高', kline_history.iloc[-2].get('high', 0)))
                base_data['prev_low'] = float(kline_history.iloc[-2].get('最低', kline_history.iloc[-2].get('low', 0)))
                base_data['prev_open'] = float(kline_history.iloc[-2].get('开盘', kline_history.iloc[-2].get('open', 0)))
                print(f"   ✅ K线数据加载完成，前日收盘价: {base_data['prev_close']}")
            else:
                print(f"   ⚠️ K线数据不足，使用默认值")
                base_data['prev_close'] = 0
                base_data['prev_high'] = 0
                base_data['prev_low'] = 0
                base_data['prev_open'] = 0

            # 2. 计算历史成交量基准
            print(f"   🔄 计算 {code} 成交量基准...")
            if not kline_history.empty and len(kline_history) >= 10:
                recent_volumes = []
                for _, row in kline_history.tail(10).iterrows():
                    volume = int(row.get('成交量', row.get('volume', 0)))
                    if volume > 0:
                        recent_volumes.append(volume)

                if recent_volumes:
                    base_data['avg_volume_5d'] = sum(recent_volumes[-5:]) / len(recent_volumes[-5:])
                    base_data['avg_volume_10d'] = sum(recent_volumes) / len(recent_volumes)
                    print(f"   ✅ 成交量基准计算完成，5日均量: {base_data['avg_volume_5d']:.0f}")
                else:
                    base_data['avg_volume_5d'] = 0
                    base_data['avg_volume_10d'] = 0
            else:
                base_data['avg_volume_5d'] = 0
                base_data['avg_volume_10d'] = 0

            # 3. 加载筹码分布历史数据（如果主应用支持）
            print(f"   🔄 尝试加载 {code} 筹码分布数据...")
            try:
                if hasattr(enhanced_stock_fetcher, 'get_chip_distribution'):
                    chip_data = enhanced_stock_fetcher.get_chip_distribution(code)
                    base_data['chip_distribution_60d'] = chip_data
                    print(f"   ✅ 筹码分布数据加载完成")
                else:
                    # 基于历史K线数据构建简化筹码分布
                    base_data['chip_distribution_60d'] = self.build_simple_chip_distribution(kline_history)
                    print(f"   ✅ 简化筹码分布构建完成")
            except Exception as e:
                print(f"   ⚠️ 筹码分布数据加载失败: {e}")
                base_data['chip_distribution_60d'] = None

            # 4. 计算价格区间统计
            if not kline_history.empty:
                highs = [float(row.get('最高', row.get('high', 0))) for _, row in kline_history.iterrows()]
                lows = [float(row.get('最低', row.get('low', 0))) for _, row in kline_history.iterrows()]
                closes = [float(row.get('收盘', row.get('close', 0))) for _, row in kline_history.iterrows()]

                base_data['price_range'] = {
                    'max_high_30d': max(highs) if highs else 0,
                    'min_low_30d': min(lows) if lows else 0,
                    'avg_close_30d': sum(closes) / len(closes) if closes else 0
                }
            else:
                base_data['price_range'] = {
                    'max_high_30d': 0,
                    'min_low_30d': 0,
                    'avg_close_30d': 0
                }

            # 5. 保存加载时间戳
            base_data['loaded_date'] = datetime.now().date()
            base_data['loaded_time'] = datetime.now()

            # 保存到历史基础数据缓存
            self.historical_base_data[code] = base_data

            print(f"✅ {code} 历史基础数据初始化完成")
            return True

        except Exception as e:
            print(f"❌ {code} 历史数据初始化失败: {e}")
            return False

    def build_simple_chip_distribution(self, kline_history):
        """基于历史K线数据构建简化筹码分布"""
        try:
            if kline_history.empty:
                return None

            chip_distribution = {}
            current_time = datetime.now()

            # 基于成交量和价格构建筹码分布
            for i, (_, row) in enumerate(kline_history.iterrows()):
                close_price = float(row.get('收盘', row.get('close', 0)))
                volume = int(row.get('成交量', row.get('volume', 0)))
                trade_date = row.get('日期', row.get('date', current_time))

                if close_price > 0 and volume > 0:
                    # 计算时间衰减因子（越近的数据权重越大）
                    if isinstance(trade_date, str):
                        trade_date = datetime.strptime(trade_date, '%Y-%m-%d')
                    elif not isinstance(trade_date, datetime):
                        trade_date = current_time

                    days_ago = (current_time - trade_date).days
                    decay_factor = 0.98 ** days_ago  # 每天衰减2%

                    # 按价格区间统计筹码
                    price_level = round(close_price, 2)
                    weighted_volume = volume * decay_factor

                    if price_level in chip_distribution:
                        chip_distribution[price_level] += weighted_volume
                    else:
                        chip_distribution[price_level] = weighted_volume

            return chip_distribution

        except Exception as e:
            print(f"⚠️ 构建简化筹码分布失败: {e}")
            return None

    def get_realtime_data_with_history_context(self, code):
        """获取实时数据（结合历史基础数据）"""
        try:
            # 1. 获取历史基础数据
            historical = self.historical_base_data.get(code, {})

            if not historical:
                print(f"⚠️ {code} 缺少历史基础数据，尝试重新初始化...")
                if not self.initialize_stock_monitoring(code):
                    return None
                historical = self.historical_base_data.get(code, {})

            # 2. 获取实时数据（每30秒必须更新）
            realtime_quote = enhanced_stock_fetcher.get_realtime_quote([code])
            if realtime_quote.empty:
                print(f"⚠️ {code} 实时行情数据为空")
                return None

            quote_data = realtime_quote.iloc[0]

            # 3. 获取实时五档数据
            depth_data = enhanced_stock_fetcher.get_market_depth(code)
            parsed_depth = self.parse_market_depth(depth_data) if not depth_data.empty else {
                'bid_prices': [0] * 5, 'bid_volumes': [0] * 5,
                'ask_prices': [0] * 5, 'ask_volumes': [0] * 5
            }

            # 4. 构建完整的实时数据
            realtime_data = {
                # 基础实时数据
                'latest_price': float(quote_data.get('current_price', quote_data.get('最新价', 0))),
                'change_pct': float(quote_data.get('change_pct', quote_data.get('涨跌幅', 0))),
                'volume': int(quote_data.get('volume', quote_data.get('成交量', 0))),
                'turnover': float(quote_data.get('amount', quote_data.get('成交额', 0))),
                'high': float(quote_data.get('high', quote_data.get('最高', 0))),
                'low': float(quote_data.get('low', quote_data.get('最低', 0))),
                'open': float(quote_data.get('open', quote_data.get('今开', 0))),
                'market_cap': float(quote_data.get('market_cap', quote_data.get('总市值', 0))),

                # 五档数据
                'bid_prices': parsed_depth['bid_prices'],
                'bid_volumes': parsed_depth['bid_volumes'],
                'ask_prices': parsed_depth['ask_prices'],
                'ask_volumes': parsed_depth['ask_volumes'],

                # 时间戳
                'timestamp': datetime.now()
            }

            # 5. 结合历史基础数据进行分析
            analysis = self.analyze_with_historical_context(realtime_data, historical)

            # 6. 合并数据
            complete_data = {**realtime_data, **analysis}

            # 7. 缓存实时数据
            self.realtime_data_cache[code] = complete_data

            return complete_data

        except Exception as e:
            print(f"❌ 获取 {code} 实时数据失败: {e}")
            return None

    def analyze_with_historical_context(self, realtime_data, historical_data):
        """基于历史数据分析实时数据"""
        try:
            analysis = {}

            current_price = realtime_data['latest_price']
            current_volume = realtime_data['volume']

            # 1. 基于历史数据的相对分析
            prev_close = historical_data.get('prev_close', 0)
            prev_high = historical_data.get('prev_high', 0)
            avg_volume_5d = historical_data.get('avg_volume_5d', 1)

            if prev_close > 0:
                analysis['price_vs_prev_close'] = current_price / prev_close
            else:
                analysis['price_vs_prev_close'] = 1.0

            if prev_high > 0:
                analysis['price_vs_prev_high'] = current_price / prev_high
                analysis['above_prev_high'] = current_price > prev_high
            else:
                analysis['price_vs_prev_high'] = 1.0
                analysis['above_prev_high'] = False

            if avg_volume_5d > 0:
                analysis['volume_ratio_5d'] = current_volume / avg_volume_5d
            else:
                analysis['volume_ratio_5d'] = 1.0

            # 2. 筹码分布分析
            chip_distribution = historical_data.get('chip_distribution_60d')
            if chip_distribution and current_price > 0:
                analysis['profit_ratio'] = self.calculate_current_profit_ratio(current_price, chip_distribution)
                analysis['chip_concentration'] = self.calculate_chip_concentration_from_distribution(chip_distribution)
            else:
                analysis['profit_ratio'] = 0.5  # 默认50%获利盘
                analysis['chip_concentration'] = 0.5  # 默认中等集中度

            # 3. 价格区间分析
            price_range = historical_data.get('price_range', {})
            max_high_30d = price_range.get('max_high_30d', current_price)
            min_low_30d = price_range.get('min_low_30d', current_price)

            if max_high_30d > min_low_30d:
                analysis['price_position_30d'] = (current_price - min_low_30d) / (max_high_30d - min_low_30d)
            else:
                analysis['price_position_30d'] = 0.5

            return analysis

        except Exception as e:
            print(f"⚠️ 历史数据分析失败: {e}")
            return {}

    def calculate_current_profit_ratio(self, current_price, chip_distribution):
        """计算当前获利盘比例"""
        try:
            if not chip_distribution or current_price <= 0:
                return 0.5

            total_chips = sum(chip_distribution.values())
            profit_chips = sum(volume for price, volume in chip_distribution.items() if price < current_price)

            if total_chips > 0:
                return profit_chips / total_chips
            else:
                return 0.5

        except Exception as e:
            return 0.5

    def calculate_chip_concentration_from_distribution(self, chip_distribution):
        """从筹码分布计算集中度"""
        try:
            if not chip_distribution:
                return 0.5

            total_chips = sum(chip_distribution.values())
            if total_chips == 0:
                return 0.5

            # 计算筹码分布的方差，方差越小集中度越高
            prices = list(chip_distribution.keys())
            volumes = list(chip_distribution.values())

            # 加权平均价格
            weighted_avg_price = sum(p * v for p, v in zip(prices, volumes)) / total_chips

            # 计算方差
            variance = sum(v * (p - weighted_avg_price) ** 2 for p, v in zip(prices, volumes)) / total_chips

            # 转换为集中度（方差越小，集中度越高）
            if variance > 0:
                concentration = 1 / (1 + variance)  # 归一化到0-1之间
            else:
                concentration = 1.0

            return min(concentration, 1.0)

        except Exception as e:
            return 0.5

    def should_refresh_historical_data(self, code):
        """判断是否需要刷新历史数据"""
        try:
            historical = self.historical_base_data.get(code, {})
            loaded_date = historical.get('loaded_date')

            # 如果是新的交易日，刷新历史数据
            if not loaded_date or loaded_date < datetime.now().date():
                return True

            return False

        except Exception as e:
            return True

    def daily_data_refresh(self):
        """每日数据刷新（在开盘前执行）"""
        try:
            current_time = datetime.now().time()

            # 在9:00-9:25之间执行每日数据刷新
            if (datetime.strptime("09:00", "%H:%M").time() <= current_time <=
                datetime.strptime("09:25", "%H:%M").time()):

                print("🔄 执行每日历史数据刷新...")

                for code in self.monitored_stocks.keys():
                    if self.should_refresh_historical_data(code):
                        print(f"🔄 刷新 {code} 的历史基础数据...")
                        self.initialize_stock_monitoring(code)

                print("✅ 每日历史数据刷新完成")

        except Exception as e:
            print(f"⚠️ 每日数据刷新失败: {e}")

    def get_historical_data_summary(self, code):
        """获取历史数据摘要（用于调试）"""
        try:
            historical = self.historical_base_data.get(code, {})

            if not historical:
                return "❌ 无历史数据"

            summary = []
            summary.append(f"📊 {code} 历史数据摘要:")
            summary.append(f"   加载时间: {historical.get('loaded_time', 'N/A')}")
            summary.append(f"   前日收盘: {historical.get('prev_close', 0):.2f}")
            summary.append(f"   前日最高: {historical.get('prev_high', 0):.2f}")
            summary.append(f"   5日均量: {historical.get('avg_volume_5d', 0):.0f}")

            chip_data = historical.get('chip_distribution_60d')
            if chip_data:
                summary.append(f"   筹码数据: {len(chip_data)} 个价格区间")
            else:
                summary.append(f"   筹码数据: 无")

            return "\n".join(summary)

        except Exception as e:
            return f"❌ 获取历史数据摘要失败: {e}"

    def view_historical_data(self):
        """查看历史数据"""
        try:
            if not self.monitored_stocks:
                messagebox.showinfo("提示", "请先添加股票到监控列表")
                return

            # 创建历史数据查看窗口
            history_window = tk.Toplevel(self.root)
            history_window.title("📋 历史基础数据查看")
            history_window.geometry("800x600")
            history_window.transient(self.root)

            # 创建文本显示区域
            text_frame = ttk.Frame(history_window)
            text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            text_widget = tk.Text(text_frame, font=('Microsoft YaHei', 10), wrap=tk.WORD)
            scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
            text_widget.configure(yscrollcommand=scrollbar.set)

            text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 显示所有股票的历史数据摘要
            content = []
            content.append("📊 潜在股监控器 - 历史基础数据报告")
            content.append("=" * 60)
            content.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append(f"监控股票数量: {len(self.monitored_stocks)}")
            content.append("")

            for code in self.monitored_stocks.keys():
                summary = self.get_historical_data_summary(code)
                content.append(summary)
                content.append("-" * 40)

            # 显示缓存统计
            content.append("\n📈 数据缓存统计:")
            content.append(f"历史基础数据缓存: {len(self.historical_base_data)} 只股票")
            content.append(f"实时数据缓存: {len(self.realtime_data_cache)} 只股票")

            # 显示系统状态
            content.append("\n🔧 系统状态:")
            content.append(f"监控状态: {'运行中' if self.monitoring else '已停止'}")
            content.append(f"扫描间隔: {self.config['scan_interval']} 秒")
            content.append(f"数据获取器: {type(enhanced_stock_fetcher).__name__}")

            text_widget.insert(tk.END, "\n".join(content))
            text_widget.config(state=tk.DISABLED)

            # 添加刷新按钮
            button_frame = ttk.Frame(history_window)
            button_frame.pack(pady=10)

            def refresh_data():
                """刷新历史数据"""
                try:
                    text_widget.config(state=tk.NORMAL)
                    text_widget.delete(1.0, tk.END)

                    # 重新生成内容
                    new_content = []
                    new_content.append("📊 潜在股监控器 - 历史基础数据报告")
                    new_content.append("=" * 60)
                    new_content.append(f"刷新时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
                    new_content.append(f"监控股票数量: {len(self.monitored_stocks)}")
                    new_content.append("")

                    for code in self.monitored_stocks.keys():
                        summary = self.get_historical_data_summary(code)
                        new_content.append(summary)
                        new_content.append("-" * 40)

                    text_widget.insert(tk.END, "\n".join(new_content))
                    text_widget.config(state=tk.DISABLED)

                except Exception as e:
                    messagebox.showerror("错误", f"刷新数据失败: {e}")

            ttk.Button(button_frame, text="🔄 刷新数据", command=refresh_data).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="❌ 关闭", command=history_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"查看历史数据失败: {e}")

    def setup_fonts(self):
        """设置全局字体"""
        try:
            # 设置默认字体为微软雅黑
            default_font = ('Microsoft YaHei', 9)
            self.root.option_add('*Font', default_font)

            # 设置ttk样式
            style = ttk.Style()

            # 配置各种控件的字体
            style.configure('TLabel', font=('Microsoft YaHei', 9))
            style.configure('TButton', font=('Microsoft YaHei', 9))
            style.configure('TEntry', font=('Microsoft YaHei', 9))
            style.configure('TCombobox', font=('Microsoft YaHei', 9))
            style.configure('Treeview', font=('Microsoft YaHei', 9))
            style.configure('Treeview.Heading', font=('Microsoft YaHei', 9, 'bold'))

            # 设置表格行高，适应中文字体
            style.configure('Treeview', rowheight=28)

            # 优化表格外观
            style.configure('Treeview',
                          background='white',
                          foreground='black',
                          fieldbackground='white',
                          borderwidth=1,
                          relief='solid')

            # 优化表格标题外观
            style.configure('Treeview.Heading',
                          background='#e1e1e1',
                          foreground='black',
                          borderwidth=1,
                          relief='raised')

            # 优化按钮外观
            style.configure('TButton',
                          padding=(10, 5),
                          relief='raised',
                          borderwidth=1)

            print("✅ 字体设置完成")

        except Exception as e:
            print(f"⚠️ 字体设置失败: {e}")

    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="🚨 潜在股监控器",
                               font=('Microsoft YaHei', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 控制按钮框架
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        
        # 添加股票按钮
        self.add_stock_btn = ttk.Button(control_frame, text="📈 添加股票", 
                                       command=self.add_stocks_dialog)
        self.add_stock_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 开始/停止监控按钮
        self.monitor_btn = ttk.Button(control_frame, text="🚀 开始监控", 
                                     command=self.toggle_monitoring)
        self.monitor_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 设置按钮
        self.settings_btn = ttk.Button(control_frame, text="⚙️ 设置", 
                                      command=self.show_settings)
        self.settings_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # 清空列表按钮
        self.clear_btn = ttk.Button(control_frame, text="🗑️ 清空列表",
                                   command=self.clear_stocks)
        self.clear_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 测试通知按钮
        self.test_notification_btn = ttk.Button(control_frame, text="🔔 测试通知",
                                               command=self.test_notification)
        self.test_notification_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 测试数据获取按钮
        self.test_data_btn = ttk.Button(control_frame, text="📊 测试数据",
                                       command=self.test_data_fetch)
        self.test_data_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 查看历史数据按钮
        self.view_history_btn = ttk.Button(control_frame, text="📋 历史数据",
                                          command=self.view_historical_data)
        self.view_history_btn.pack(side=tk.LEFT, padx=(0, 10))

        # 监控状态标签
        self.status_label = ttk.Label(control_frame, text="⏸️ 监控已停止",
                                     font=('Microsoft YaHei', 10, 'bold'))
        self.status_label.pack(side=tk.RIGHT)
        
        # 股票列表表格
        columns = ('代码', '名称', '最新价', '涨跌幅', '买一变化率', '卖压系数',
                  '量能突击', '价格重心', '信号状态', '最后更新')

        self.tree = ttk.Treeview(main_frame, columns=columns, show='headings', height=20)
        self.tree.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S))

        # 设置列标题和宽度
        column_widths = {
            '代码': 80, '名称': 100, '最新价': 80, '涨跌幅': 80,
            '买一变化率': 100, '卖压系数': 100, '量能突击': 100, '价格重心': 100,
            '信号状态': 150, '最后更新': 120
        }
        
        for col in columns:
            self.tree.heading(col, text=col)
            self.tree.column(col, width=column_widths.get(col, 100))
        
        # 添加滚动条
        scrollbar = ttk.Scrollbar(main_frame, orient=tk.VERTICAL, command=self.tree.yview)
        scrollbar.grid(row=2, column=3, sticky=(tk.N, tk.S))
        self.tree.configure(yscrollcommand=scrollbar.set)

        # 底部状态栏
        status_frame = ttk.Frame(main_frame)
        status_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))

        self.info_label = ttk.Label(status_frame, text="💡 请添加股票代码开始监控")
        self.info_label.pack(side=tk.LEFT)

        self.time_label = ttk.Label(status_frame, text="")
        self.time_label.pack(side=tk.RIGHT)

        # 更新时间显示
        self.update_time_display()
    
    def update_time_display(self):
        """更新时间显示"""
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        self.time_label.config(text=f"当前时间: {current_time}")
        self.root.after(1000, self.update_time_display)
    
    def add_stocks_dialog(self):
        """添加股票对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("添加股票代码")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()
        
        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 50, self.root.winfo_rooty() + 50))
        
        # 说明标签
        info_label = ttk.Label(dialog, text="请输入股票代码（一行一个）：",
                              font=('Microsoft YaHei', 10))
        info_label.pack(pady=10)
        
        # 文本输入框
        text_frame = ttk.Frame(dialog)
        text_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        text_widget = tk.Text(text_frame, height=10, width=40,
                              font=('Microsoft YaHei', 10))
        text_widget.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        
        # 滚动条
        text_scrollbar = ttk.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_widget.yview)
        text_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        text_widget.configure(yscrollcommand=text_scrollbar.set)
        
        # 按钮框架
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)
        
        def add_stocks():
            """添加股票"""
            content = text_widget.get("1.0", tk.END).strip()
            if not content:
                messagebox.showwarning("警告", "请输入股票代码")
                return
            
            codes = [line.strip() for line in content.split('\n') if line.strip()]
            if not codes:
                messagebox.showwarning("警告", "请输入有效的股票代码")
                return
            
            # 添加股票到监控列表
            added_count = 0
            for code in codes:
                if self.add_stock_to_monitor(code):
                    added_count += 1
            
            if added_count > 0:
                messagebox.showinfo("成功", f"成功添加 {added_count} 只股票到监控列表")
                self.save_config()
                dialog.destroy()
            else:
                messagebox.showerror("错误", "未能添加任何股票，请检查代码格式")
        
        ttk.Button(button_frame, text="确定", command=add_stocks).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)
    
    def add_stock_to_monitor(self, code):
        """添加股票到监控列表"""
        try:
            # 标准化股票代码
            code = str(code).strip().upper()
            if len(code) != 6 or not code.isdigit():
                print(f"⚠️ 股票代码格式错误: {code}")
                return False
            
            if code in self.monitored_stocks:
                print(f"⚠️ 股票 {code} 已在监控列表中")
                return False
            
            # 获取股票基本信息
            print(f"   🔄 正在获取股票名称: {code}")
            stock_name = f'股票{code}'  # 默认名称

            if enhanced_stock_fetcher:
                try:
                    basic_info = enhanced_stock_fetcher.get_stock_basic_info(code)
                    fetched_name = basic_info.get('name', f'股票{code}')

                    # 检查是否获取到真实名称
                    if fetched_name and not fetched_name.startswith('股票') and fetched_name != '未知':
                        stock_name = fetched_name
                        print(f"   ✅ 成功获取股票名称: {code} -> {stock_name}")
                    else:
                        print(f"   ⚠️ 未获取到真实股票名称，使用默认: {code} -> {stock_name}")

                except Exception as e:
                    print(f"   ❌ 获取股票名称失败 {code}: {e}")
                    stock_name = f'股票{code}'
            else:
                print(f"   ⚠️ 数据获取器不可用，使用默认名称: {code} -> {stock_name}")
            
            # 初始化历史基础数据
            print(f"🔄 正在初始化 {code} {stock_name}...")
            if self.initialize_stock_monitoring(code):

                # 添加到监控列表
                self.monitored_stocks[code] = {
                    'name': stock_name,
                    'data': {},
                    'signals': {},
                    'last_update': None,
                    'alert_history': [],
                    'initialized': True
                }

                # 添加到表格
                self.tree.insert('', 'end', iid=code, values=(
                    code, stock_name, '-', '-', '-', '-', '-', '-', '已初始化', '-'
                ))

                print(f"✅ 添加股票到监控: {code} {stock_name}")
                return True

            else:
                print(f"❌ {code} {stock_name} 初始化失败，跳过添加")
                return False

        except Exception as e:
            print(f"❌ 添加股票失败 {code}: {e}")
            return False

    def toggle_monitoring(self):
        """切换监控状态"""
        if self.monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()

    def start_monitoring(self):
        """开始监控"""
        if not self.monitored_stocks:
            messagebox.showwarning("警告", "请先添加股票到监控列表")
            return

        if not self.is_trading_time():
            messagebox.showwarning("警告", "当前不在交易时间内")
            return

        self.monitoring = True
        self.monitor_btn.config(text="⏸️ 停止监控")
        self.status_label.config(text="🚀 监控运行中")
        self.info_label.config(text=f"💡 正在监控 {len(self.monitored_stocks)} 只股票")

        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()

        print("🚀 开始股票监控")

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        self.monitor_btn.config(text="🚀 开始监控")
        self.status_label.config(text="⏸️ 监控已停止")
        self.info_label.config(text="💡 监控已停止")

        print("⏸️ 停止股票监控")

    def is_trading_time(self):
        """检查是否在交易时间内"""
        now = datetime.now()

        # 检查是否为工作日（周一到周五）
        if now.weekday() >= 5:  # 周六、周日
            return False

        # 检查时间是否在交易时间内
        current_time = now.time()
        morning_start = datetime.strptime("09:30", "%H:%M").time()
        morning_end = datetime.strptime("11:30", "%H:%M").time()
        afternoon_start = datetime.strptime("13:00", "%H:%M").time()
        afternoon_end = datetime.strptime("15:00", "%H:%M").time()

        return (morning_start <= current_time <= morning_end) or \
               (afternoon_start <= current_time <= afternoon_end)

    def monitor_loop(self):
        """监控主循环"""
        while self.monitoring:
            try:
                if not self.is_trading_time():
                    print("⏰ 非交易时间，暂停监控")
                    time.sleep(60)  # 非交易时间每分钟检查一次
                    continue

                # 检查是否需要每日数据刷新
                self.daily_data_refresh()

                print(f"🔍 开始扫描 {len(self.monitored_stocks)} 只股票...")

                # 分析每只股票（使用新的数据获取方法）
                for code in self.monitored_stocks.keys():
                    if not self.monitoring:  # 检查是否需要停止
                        break

                    try:
                        # 使用新的实时数据获取方法（结合历史基础数据）
                        stock_data = self.get_realtime_data_with_history_context(code)

                        if stock_data:
                            # 检测6大黄金启动信号
                            signals = self.detect_golden_signals(code, stock_data)

                            # 更新监控数据
                            self.monitored_stocks[code]['data'] = stock_data
                            self.monitored_stocks[code]['signals'] = signals
                            self.monitored_stocks[code]['last_update'] = datetime.now()

                            # 更新GUI显示
                            self.root.after(0, self.update_stock_display, code)

                            # 信号确认机制
                            confirmed_signals = self.confirm_signals(code, signals, stock_data)

                            # 检查是否触发报警（基于确认后的信号）
                            if self.check_alert_conditions(confirmed_signals):
                                self.root.after(0, self.send_alert, code, confirmed_signals)
                        else:
                            print(f"⚠️ {code} 实时数据获取失败")

                    except Exception as e:
                        print(f"⚠️ 分析股票 {code} 失败: {e}")
                        continue

                print(f"✅ 扫描完成，等待 {self.config['scan_interval']} 秒...")
                time.sleep(self.config['scan_interval'])

            except Exception as e:
                print(f"❌ 监控循环异常: {e}")
                time.sleep(10)  # 出错后等待10秒再继续

    def get_batch_realtime_data(self, codes):
        """批量获取实时数据（增强版）"""
        realtime_data = {}

        try:
            if not enhanced_stock_fetcher:
                print("❌ 数据获取器不可用")
                return realtime_data

            # 批量获取实时行情
            df = enhanced_stock_fetcher.get_realtime_quote(codes)

            if df.empty:
                print("⚠️ 未获取到实时行情数据")
                print(f"   请求的股票代码: {codes}")
                return realtime_data

            print(f"📊 获取到实时行情数据: {len(df)} 条记录")

            # 处理每只股票的数据
            for _, row in df.iterrows():
                # 兼容不同的列名格式
                code = str(row.get('stock_code', row.get('代码', row.get('code', '')))).zfill(6)
                if code in codes:
                    realtime_data[code] = {
                        'latest_price': float(row.get('current_price', row.get('最新价', row.get('latest_price', 0)))),
                        'change_pct': float(row.get('change_pct', row.get('涨跌幅', 0))),
                        'volume': int(row.get('volume', row.get('成交量', 0))),
                        'turnover': float(row.get('amount', row.get('成交额', row.get('turnover', 0)))),
                        'high': float(row.get('high', row.get('最高', 0))),
                        'low': float(row.get('low', row.get('最低', 0))),
                        'open': float(row.get('open', row.get('今开', 0))),
                        'market_cap': float(row.get('market_cap', row.get('总市值', 0))),
                        'turnover_rate': float(row.get('turnover_rate', row.get('换手率', 0))),
                        'timestamp': datetime.now()
                    }

            # 获取增强版五档行情数据
            for code in codes:
                try:
                    # 获取基础五档数据
                    depth_df = enhanced_stock_fetcher.get_enhanced_market_depth(code)
                    if not depth_df.empty and code in realtime_data:
                        depth_data = self.parse_market_depth(depth_df)
                        realtime_data[code].update(depth_data)

                        # 获取分钟级成交量数据
                        minute_data = self.get_minute_volume_data(code)
                        if minute_data:
                            realtime_data[code]['minute_volume_data'] = minute_data

                        # 获取分笔成交数据（采样）
                        tick_data = self.get_sampled_tick_data(code)
                        if tick_data:
                            realtime_data[code]['tick_data'] = tick_data

                except Exception as e:
                    print(f"⚠️ 获取 {code} 增强数据失败: {e}")
                    continue

            print(f"✅ 获取到 {len(realtime_data)} 只股票的增强实时数据")
            return realtime_data

        except Exception as e:
            print(f"❌ 批量获取实时数据失败: {e}")
            return realtime_data

    def get_minute_volume_data(self, code):
        """获取分钟级成交量数据"""
        try:
            minute_df = enhanced_stock_fetcher.get_minute_kline(code, '1')

            if not minute_df.empty:
                # 提取最近10分钟的成交量数据
                recent_data = minute_df.tail(10)
                minute_volumes = []

                for _, row in recent_data.iterrows():
                    volume = int(row.get('成交量', row.get('volume', 0)))
                    timestamp = row.get('时间', row.get('datetime', datetime.now()))
                    minute_volumes.append({
                        'timestamp': timestamp,
                        'volume': volume
                    })

                return minute_volumes

            return None

        except Exception as e:
            return None

    def get_sampled_tick_data(self, code):
        """获取采样的分笔成交数据"""
        try:
            tick_df = enhanced_stock_fetcher.get_tick_data(code)

            if not tick_df.empty:
                # 采样最近20笔成交
                recent_ticks = tick_df.tail(20)
                tick_data = []

                for _, row in recent_ticks.iterrows():
                    tick_info = {
                        'time': row.get('时间', row.get('time', '')),
                        'price': float(row.get('价格', row.get('price', 0))),
                        'volume': int(row.get('成交量', row.get('volume', 0))),
                        'direction': row.get('方向', row.get('direction', ''))
                    }
                    tick_data.append(tick_info)

                return tick_data

            return None

        except Exception as e:
            return None

    def parse_market_depth(self, depth_df):
        """解析五档行情数据"""
        try:
            depth_data = {
                'bid_prices': [0] * 5,  # 买一到买五价格
                'bid_volumes': [0] * 5, # 买一到买五数量
                'ask_prices': [0] * 5,  # 卖一到卖五价格
                'ask_volumes': [0] * 5, # 卖一到卖五数量
            }

            if depth_df.empty:
                return depth_data

            row = depth_df.iloc[0]

            # 主应用的五档数据格式：b1,bv1,b2,bv2... (买盘) s1,sv1,s2,sv2... (卖盘)
            for i in range(5):
                # 买盘数据 (b1, bv1, b2, bv2, ...)
                bid_price_key = f'b{i+1}'
                bid_volume_key = f'bv{i+1}'

                if bid_price_key in row and row[bid_price_key] is not None:
                    depth_data['bid_prices'][i] = float(row[bid_price_key])
                if bid_volume_key in row and row[bid_volume_key] is not None:
                    depth_data['bid_volumes'][i] = int(row[bid_volume_key])

                # 卖盘数据 (s1, sv1, s2, sv2, ...)
                ask_price_key = f's{i+1}'
                ask_volume_key = f'sv{i+1}'

                if ask_price_key in row and row[ask_price_key] is not None:
                    depth_data['ask_prices'][i] = float(row[ask_price_key])
                if ask_volume_key in row and row[ask_volume_key] is not None:
                    depth_data['ask_volumes'][i] = int(row[ask_volume_key])

            # 备用解析：尝试其他格式
            if all(p == 0 for p in depth_data['bid_prices']):
                # 尝试中文格式
                for i in range(5):
                    bid_price_key = f'买{i+1}'
                    bid_volume_key = f'买量{i+1}'
                    ask_price_key = f'卖{i+1}'
                    ask_volume_key = f'卖量{i+1}'

                    if bid_price_key in row and row[bid_price_key] is not None:
                        depth_data['bid_prices'][i] = float(row[bid_price_key])
                    if bid_volume_key in row and row[bid_volume_key] is not None:
                        depth_data['bid_volumes'][i] = int(row[bid_volume_key])
                    if ask_price_key in row and row[ask_price_key] is not None:
                        depth_data['ask_prices'][i] = float(row[ask_price_key])
                    if ask_volume_key in row and row[ask_volume_key] is not None:
                        depth_data['ask_volumes'][i] = int(row[ask_volume_key])

            return depth_data

        except Exception as e:
            print(f"⚠️ 解析五档数据失败: {e}")
            return {
                'bid_prices': [0] * 5,
                'bid_volumes': [0] * 5,
                'ask_prices': [0] * 5,
                'ask_volumes': [0] * 5,
            }

    def detect_golden_signals(self, code, stock_data):
        """检测6大黄金启动信号"""
        signals = {
            'bid_surge': False,      # 买盘托单突变
            'ask_exhaustion': False, # 卖压枯竭
            'volume_pulse': False,   # 量能脉冲
            'price_strength': False, # 价格韧性
            'time_window': False,    # 时间窗口共振
            'chip_control': False,   # 筹码分布
            'alert_score': 0,        # 综合报警评分
            'details': {}            # 详细信息
        }

        try:
            # 信号1: 买盘托单突变检测
            signals['bid_surge'], bid_details = self.check_bid_surge(code, stock_data)
            signals['details']['bid_surge'] = bid_details

            # 信号2: 卖压枯竭检测
            signals['ask_exhaustion'], ask_details = self.check_ask_exhaustion(code, stock_data)
            signals['details']['ask_exhaustion'] = ask_details

            # 信号3: 量能脉冲检测
            signals['volume_pulse'], volume_details = self.check_volume_pulse(code, stock_data)
            signals['details']['volume_pulse'] = volume_details

            # 信号4: 价格韧性检测
            signals['price_strength'], price_details = self.check_price_strength(code, stock_data)
            signals['details']['price_strength'] = price_details

            # 信号5: 时间窗口共振检测
            signals['time_window'], time_details = self.check_time_window(code, stock_data)
            signals['details']['time_window'] = time_details

            # 信号6: 筹码分布检测
            signals['chip_control'], chip_details = self.check_chip_control(code, stock_data)
            signals['details']['chip_control'] = chip_details

            # 计算核心监控指标
            core_indicators = self.calculate_core_indicators(code, stock_data)
            signals['core_indicators'] = core_indicators

            # 计算综合报警评分
            signals['alert_score'] = self.calculate_alert_score(signals)

            return signals

        except Exception as e:
            print(f"⚠️ 检测股票 {code} 信号失败: {e}")
            return signals

    def check_bid_surge(self, code, stock_data):
        """检测买盘托单突变（完善版）"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if not bid_volumes or bid_volumes[0] == 0:
                return False, {'reason': '无买一数据'}

            current_bid1 = bid_volumes[0]

            # 获取股票市值用于动态阈值调整
            market_cap = stock_data.get('market_cap', 0)
            dynamic_threshold = self.get_dynamic_bid_threshold(market_cap)

            # 获取历史买一数据进行对比
            historical_bid1 = self.get_historical_bid1(code)

            if historical_bid1 == 0:
                # 首次获取数据，记录当前值
                self.update_historical_bid1(code, current_bid1)
                return False, {'reason': '首次获取数据', 'current_bid1': current_bid1}

            # 检测1：买一突增5倍以上
            surge_ratio = current_bid1 / historical_bid1 if historical_bid1 > 0 else 0
            bid1_surge = surge_ratio >= self.config['bid_surge_threshold']

            # 检测2：买一托单是否达到动态阈值
            bid1_threshold_met = current_bid1 >= dynamic_threshold

            # 检测3：买三至买五挂单厚度 > 卖一至卖五总量（买盘蓄力）
            bid_345_total = sum(bid_volumes[2:5]) if len(bid_volumes) >= 5 else 0  # 买三到买五
            ask_15_total = sum(ask_volumes[0:5]) if len(ask_volumes) >= 5 else 0   # 卖一到卖五

            bid_power_accumulation = bid_345_total > ask_15_total and bid_345_total > 0

            # 检测4：买盘整体厚度分析
            total_bid_volume = sum(bid_volumes)
            total_ask_volume = sum(ask_volumes)
            bid_ask_ratio = total_bid_volume / total_ask_volume if total_ask_volume > 0 else 999

            # 综合判断：需要满足多个条件
            conditions_met = 0
            if bid1_surge:
                conditions_met += 1
            if bid1_threshold_met:
                conditions_met += 1
            if bid_power_accumulation:
                conditions_met += 1
            if bid_ask_ratio > 2.0:  # 买盘总量是卖盘的2倍以上
                conditions_met += 1

            # 至少满足2个条件才认为是托单突变
            is_surge = conditions_met >= 2

            # 更新历史数据
            self.update_historical_bid1(code, current_bid1)

            details = {
                'current_bid1': current_bid1,
                'historical_bid1': historical_bid1,
                'surge_ratio': surge_ratio,
                'dynamic_threshold': dynamic_threshold,
                'bid1_surge': bid1_surge,
                'bid1_threshold_met': bid1_threshold_met,
                'bid_345_total': bid_345_total,
                'ask_15_total': ask_15_total,
                'bid_power_accumulation': bid_power_accumulation,
                'bid_ask_ratio': bid_ask_ratio,
                'conditions_met': conditions_met,
                'market_cap': market_cap,
                'triggered': is_surge
            }

            return is_surge, details

        except Exception as e:
            return False, {'error': str(e)}

    def get_dynamic_bid_threshold(self, market_cap):
        """根据市值动态调整买一托单阈值"""
        try:
            # 市值单位：亿元
            if market_cap == 0:
                return 500  # 默认阈值

            if market_cap < 50:  # 小盘股（<50亿）
                return 300
            elif market_cap < 200:  # 中盘股（50-200亿）
                return 600
            elif market_cap < 500:  # 大盘股（200-500亿）
                return 1000
            else:  # 超大盘股（>500亿）
                return 1500

        except Exception as e:
            print(f"⚠️ 动态阈值计算失败: {e}")
            return 500  # 默认阈值

    def check_ask_exhaustion(self, code, stock_data):
        """检测卖压枯竭信号（完善版）"""
        try:
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)

            if not ask_volumes:
                return False, {'reason': '无卖盘数据'}

            # 检测1：卖一量锐减至<50手
            ask1_volume = ask_volumes[0]
            ask1_low = ask1_volume < 50

            # 检测2：卖一至卖五总量<100手
            ask_total = sum(ask_volumes)
            ask_total_low = ask_total < 100

            # 检测3：卖压系数 = (卖一量+卖二量)/买一量 < 0.3
            bid1_volume = bid_volumes[0] if bid_volumes else 0
            ask12_volume = ask1_volume + (ask_volumes[1] if len(ask_volumes) > 1 else 0)
            ask_pressure_ratio = ask12_volume / bid1_volume if bid1_volume > 0 else 999
            ask_pressure_low = ask_pressure_ratio < self.config['ask_pressure_threshold']

            # 检测4：卖五总撤单率>60%监控（模拟实现）
            cancel_rate = self.estimate_ask_cancel_rate(code, ask_volumes)
            high_cancel_rate = cancel_rate > 0.6

            # 检测5：卖盘压单厚度分析
            # 检查卖二到卖五是否也很薄
            ask_25_total = sum(ask_volumes[1:5]) if len(ask_volumes) >= 5 else 0
            ask_depth_thin = ask_25_total < 200  # 卖二到卖五总量<200手

            # 检测6：卖盘递减模式（正常应该是递增的）
            ask_decreasing = self.check_ask_decreasing_pattern(ask_volumes)

            # 综合评分系统
            exhaustion_score = 0
            conditions = {
                'ask1_low': ask1_low,
                'ask_total_low': ask_total_low,
                'ask_pressure_low': ask_pressure_low,
                'high_cancel_rate': high_cancel_rate,
                'ask_depth_thin': ask_depth_thin,
                'ask_decreasing': ask_decreasing
            }

            # 权重评分
            weights = {
                'ask1_low': 25,           # 卖一量<50手（关键）
                'ask_total_low': 20,      # 总量<100手
                'ask_pressure_low': 20,   # 卖压系数<0.3
                'high_cancel_rate': 15,   # 撤单率>60%
                'ask_depth_thin': 10,     # 卖盘深度薄
                'ask_decreasing': 10      # 递减模式
            }

            for condition, triggered in conditions.items():
                if triggered:
                    exhaustion_score += weights[condition]

            # 卖压枯竭判断：评分≥60分
            is_exhausted = exhaustion_score >= 60

            details = {
                'ask1_volume': ask1_volume,
                'ask_total': ask_total,
                'ask_pressure_ratio': ask_pressure_ratio,
                'cancel_rate': cancel_rate,
                'ask_25_total': ask_25_total,
                'exhaustion_score': exhaustion_score,
                'conditions': conditions,
                'triggered': is_exhausted
            }

            return is_exhausted, details

        except Exception as e:
            return False, {'error': str(e)}

    def estimate_ask_cancel_rate(self, code, current_ask_volumes):
        """估算卖五总撤单率（增强版）"""
        try:
            # 获取历史卖盘数据序列
            ask_history = self.get_ask_volume_history(code)

            # 记录当前卖盘数据
            current_time = datetime.now()
            ask_record = {
                'timestamp': current_time,
                'ask_volumes': current_ask_volumes.copy()
            }

            ask_history.append(ask_record)

            # 保持最近5分钟的数据（假设30秒扫描一次，5分钟=10个记录）
            if len(ask_history) > 10:
                ask_history.pop(0)

            # 更新历史数据
            self.update_ask_volume_history(code, ask_history)

            if len(ask_history) < 3:
                return 0.0

            # 计算撤单率：分析最近3次数据的变化模式
            cancel_events = 0
            total_observations = 0

            for i in range(1, len(ask_history)):
                prev_record = ask_history[i-1]
                curr_record = ask_history[i]

                prev_volumes = prev_record['ask_volumes']
                curr_volumes = curr_record['ask_volumes']

                # 检查每个档位的撤单情况
                for j in range(min(len(prev_volumes), len(curr_volumes))):
                    if prev_volumes[j] > 0:
                        total_observations += 1

                        # 如果当前量比前一次减少超过50%，认为是撤单
                        if curr_volumes[j] < prev_volumes[j] * 0.5:
                            cancel_events += 1

            # 计算撤单率
            if total_observations > 0:
                cancel_rate = cancel_events / total_observations
                return min(cancel_rate, 1.0)

            return 0.0

        except Exception as e:
            return 0.0

    def get_ask_volume_history(self, code):
        """获取卖盘量历史记录"""
        return self.historical_cache.get(code, {}).get('ask_volume_history', [])

    def update_ask_volume_history(self, code, history):
        """更新卖盘量历史记录"""
        if code not in self.historical_cache:
            self.historical_cache[code] = {}
        self.historical_cache[code]['ask_volume_history'] = history

    def monitor_order_flow(self, code, stock_data):
        """监控订单流变化"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            tick_data = stock_data.get('tick_data', [])

            # 分析订单流特征
            order_flow_analysis = {
                'bid_flow_strength': self.calculate_bid_flow_strength(bid_volumes, tick_data),
                'ask_flow_weakness': self.calculate_ask_flow_weakness(ask_volumes, tick_data),
                'order_imbalance': self.calculate_order_imbalance(bid_volumes, ask_volumes),
                'tick_direction_bias': self.analyze_tick_direction_bias(tick_data)
            }

            return order_flow_analysis

        except Exception as e:
            return {}

    def calculate_bid_flow_strength(self, bid_volumes, tick_data):
        """计算买盘流强度"""
        try:
            if not bid_volumes or not tick_data:
                return 0

            # 买盘总量
            total_bid = sum(bid_volumes)

            # 主动买入成交比例
            buy_ticks = [t for t in tick_data if t.get('direction', '') in ['买盘', 'B', '1']]
            buy_ratio = len(buy_ticks) / len(tick_data) if tick_data else 0

            # 综合买盘流强度
            flow_strength = (total_bid / 10000) * buy_ratio  # 标准化

            return min(flow_strength, 10.0)  # 限制最大值

        except Exception as e:
            return 0

    def calculate_ask_flow_weakness(self, ask_volumes, tick_data):
        """计算卖盘流弱度"""
        try:
            if not ask_volumes or not tick_data:
                return 0

            # 卖盘总量
            total_ask = sum(ask_volumes)

            # 主动卖出成交比例
            sell_ticks = [t for t in tick_data if t.get('direction', '') in ['卖盘', 'S', '2']]
            sell_ratio = len(sell_ticks) / len(tick_data) if tick_data else 0

            # 卖盘流弱度（卖盘少且主动卖出少表示弱度高）
            if total_ask > 0:
                flow_weakness = (1 / (total_ask / 1000)) * (1 - sell_ratio)
            else:
                flow_weakness = 10.0  # 无卖盘时弱度最高

            return min(flow_weakness, 10.0)

        except Exception as e:
            return 0

    def calculate_order_imbalance(self, bid_volumes, ask_volumes):
        """计算订单不平衡度"""
        try:
            total_bid = sum(bid_volumes) if bid_volumes else 0
            total_ask = sum(ask_volumes) if ask_volumes else 0

            if total_bid + total_ask == 0:
                return 0

            # 订单不平衡度：正值表示买盘占优，负值表示卖盘占优
            imbalance = (total_bid - total_ask) / (total_bid + total_ask)

            return imbalance

        except Exception as e:
            return 0

    def analyze_tick_direction_bias(self, tick_data):
        """分析分笔成交方向偏向"""
        try:
            if not tick_data:
                return 0

            buy_count = 0
            sell_count = 0

            for tick in tick_data:
                direction = tick.get('direction', '')
                if direction in ['买盘', 'B', '1']:
                    buy_count += 1
                elif direction in ['卖盘', 'S', '2']:
                    sell_count += 1

            total_directional = buy_count + sell_count

            if total_directional == 0:
                return 0

            # 方向偏向：正值表示买盘偏向，负值表示卖盘偏向
            bias = (buy_count - sell_count) / total_directional

            return bias

        except Exception as e:
            return 0

    def check_ask_decreasing_pattern(self, ask_volumes):
        """检查卖盘是否呈递减模式（异常模式）"""
        try:
            if len(ask_volumes) < 3:
                return False

            # 正常情况下，卖盘应该是递增的（卖一<卖二<卖三...）
            # 如果出现递减，说明上方压单在减少
            decreasing_count = 0

            for i in range(len(ask_volumes) - 1):
                if ask_volumes[i] > ask_volumes[i + 1] and ask_volumes[i + 1] > 0:
                    decreasing_count += 1

            # 如果有2个以上递减，认为是异常模式
            return decreasing_count >= 2

        except Exception as e:
            return False

    def check_volume_pulse(self, code, stock_data):
        """检测量能脉冲（完善版）"""
        try:
            current_volume = stock_data.get('volume', 0)
            change_pct = stock_data.get('change_pct', 0)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if current_volume == 0:
                return False, {'reason': '无成交量数据'}

            # 检测1：下跌0.5%状态前置条件
            in_decline = change_pct <= -0.5

            # 检测2：当前分钟成交量 / 前5分钟均量 > 2.5倍
            minute_avg_volume = self.get_minute_avg_volume(code)
            minute_volume_ratio = current_volume / minute_avg_volume if minute_avg_volume > 0 else 0
            minute_volume_surge = minute_volume_ratio >= self.config['volume_pulse_threshold']

            # 检测3：单笔成交量>日均笔量3倍（模拟实现）
            daily_avg_tick_volume = self.get_daily_avg_tick_volume(code)
            estimated_tick_volume = current_volume / 240 if current_volume > 0 else 0  # 假设每分钟240笔
            tick_volume_surge = estimated_tick_volume > daily_avg_tick_volume * 3 if daily_avg_tick_volume > 0 else False

            # 检测4：连续买单吃光卖一档位（通过卖一量变化模拟）
            ask1_eaten = self.check_ask1_eaten_pattern(code, ask_volumes)

            # 检测5：量能突击强度分析
            volume_intensity = self.calculate_volume_intensity(code, current_volume)
            high_intensity = volume_intensity > 3.0

            # 检测6：成交量与价格背离检测
            volume_price_divergence = self.check_volume_price_divergence(code, stock_data)

            # 综合评分系统
            pulse_score = 0
            conditions = {
                'in_decline': in_decline,
                'minute_volume_surge': minute_volume_surge,
                'tick_volume_surge': tick_volume_surge,
                'ask1_eaten': ask1_eaten,
                'high_intensity': high_intensity,
                'volume_price_divergence': volume_price_divergence
            }

            # 权重评分
            weights = {
                'in_decline': 20,              # 下跌0.5%前置条件
                'minute_volume_surge': 25,     # 分钟量能突击
                'tick_volume_surge': 20,       # 单笔量能突击
                'ask1_eaten': 15,              # 连续吃光卖一
                'high_intensity': 10,          # 量能强度
                'volume_price_divergence': 10  # 量价背离
            }

            for condition, triggered in conditions.items():
                if triggered:
                    pulse_score += weights[condition]

            # 量能脉冲判断：评分≥50分
            is_pulse = pulse_score >= 50

            # 更新历史数据
            self.update_historical_volume(code, current_volume)

            details = {
                'current_volume': current_volume,
                'change_pct': change_pct,
                'minute_volume_ratio': minute_volume_ratio,
                'estimated_tick_volume': estimated_tick_volume,
                'daily_avg_tick_volume': daily_avg_tick_volume,
                'volume_intensity': volume_intensity,
                'pulse_score': pulse_score,
                'conditions': conditions,
                'triggered': is_pulse
            }

            return is_pulse, details

        except Exception as e:
            return False, {'error': str(e)}

    def get_minute_avg_volume(self, code):
        """获取前5分钟平均成交量"""
        try:
            minute_volumes = self.historical_cache.get(code, {}).get('minute_volumes', [])
            if len(minute_volumes) >= 5:
                return sum(minute_volumes[-5:]) / 5
            elif len(minute_volumes) > 0:
                return sum(minute_volumes) / len(minute_volumes)
            else:
                return 0
        except:
            return 0

    def get_daily_avg_tick_volume(self, code):
        """获取日均笔量（估算）"""
        try:
            # 基于历史成交量估算日均笔量
            avg_volume = self.get_historical_avg_volume(code)
            if avg_volume > 0:
                # 假设每日240分钟交易，每分钟平均240笔
                estimated_daily_ticks = 240 * 240
                return avg_volume / estimated_daily_ticks if estimated_daily_ticks > 0 else 0
            return 0
        except:
            return 0

    def check_ask1_eaten_pattern(self, code, current_ask_volumes):
        """检查连续买单吃光卖一档位模式"""
        try:
            if not current_ask_volumes or current_ask_volumes[0] == 0:
                return False

            # 获取历史卖一数据
            ask1_history = self.historical_cache.get(code, {}).get('ask1_history', [])

            # 记录当前卖一量
            current_ask1 = current_ask_volumes[0]
            ask1_history.append(current_ask1)

            # 保持最近10个数据点
            if len(ask1_history) > 10:
                ask1_history.pop(0)

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['ask1_history'] = ask1_history

            # 检查是否有连续3次卖一量急剧减少（被吃光）
            if len(ask1_history) >= 4:
                eaten_count = 0
                for i in range(len(ask1_history) - 3, len(ask1_history)):
                    if i > 0 and ask1_history[i] < ask1_history[i-1] * 0.5:  # 减少50%以上
                        eaten_count += 1

                return eaten_count >= 3

            return False

        except Exception as e:
            return False

    def calculate_volume_intensity(self, code, current_volume):
        """计算量能强度"""
        try:
            # 获取历史量能数据
            volumes = self.historical_cache.get(code, {}).get('volumes', [])

            if len(volumes) < 5:
                return 1.0

            # 计算当前量能相对于历史的强度
            recent_avg = sum(volumes[-5:]) / 5
            max_volume = max(volumes)

            if recent_avg > 0:
                intensity = current_volume / recent_avg
                # 考虑历史最大量的影响
                if max_volume > 0:
                    max_ratio = current_volume / max_volume
                    intensity = intensity * (1 + max_ratio)

                return intensity

            return 1.0

        except Exception as e:
            return 1.0

    def check_volume_price_divergence(self, code, stock_data):
        """检查量价背离（量增价跌）"""
        try:
            current_volume = stock_data.get('volume', 0)
            change_pct = stock_data.get('change_pct', 0)

            # 获取历史数据
            volumes = self.historical_cache.get(code, {}).get('volumes', [])

            if len(volumes) < 3:
                return False

            # 计算量能变化
            recent_avg_volume = sum(volumes[-3:]) / 3
            volume_increase = current_volume > recent_avg_volume * 1.5

            # 价格下跌但量能放大 = 量价背离
            price_decline = change_pct < -0.2

            return volume_increase and price_decline

        except Exception as e:
            return False

    def check_price_strength(self, code, stock_data):
        """检测价格韧性增强（完善版）"""
        try:
            current_price = stock_data.get('latest_price', 0)
            bid_prices = stock_data.get('bid_prices', [0] * 5)
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_prices = stock_data.get('ask_prices', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if current_price == 0 or not bid_prices or not ask_prices:
                return False, {'reason': '价格数据不完整'}

            # 检测1：精确的买盘均价计算
            bid_avg_price = self.calculate_precise_bid_avg_price(bid_prices, bid_volumes)

            if bid_avg_price == 0:
                return False, {'reason': '买盘均价计算失败'}

            # 检测2：当前价 > 买盘均价
            price_above_bid_avg = current_price > bid_avg_price

            # 检测3：距离卖一价 < 0.3%
            ask1_price = ask_prices[0] if ask_prices[0] > 0 else current_price
            price_to_ask1_ratio = (ask1_price - current_price) / current_price if current_price > 0 else 1
            close_to_ask1 = price_to_ask1_ratio < self.config['price_strength_threshold']

            # 检测4：实时价格重心计算
            price_gravity = self.calculate_price_gravity(current_price, bid_prices, bid_volumes, ask_prices, ask_volumes)
            price_above_gravity = current_price > price_gravity

            # 检测5：价格相对强度分析
            relative_strength = self.calculate_relative_strength(code, current_price)
            strong_relative = relative_strength > 1.02  # 相对强度>2%

            # 检测6：价格抗跌能力检测
            anti_fall_ability = self.check_anti_fall_ability(code, stock_data)

            # 检测7：买卖盘价格差分析
            bid_ask_spread = self.analyze_bid_ask_spread(bid_prices, ask_prices)
            narrow_spread = bid_ask_spread < 0.01  # 价差<1%

            # 综合评分系统
            strength_score = 0
            conditions = {
                'price_above_bid_avg': price_above_bid_avg,
                'close_to_ask1': close_to_ask1,
                'price_above_gravity': price_above_gravity,
                'strong_relative': strong_relative,
                'anti_fall_ability': anti_fall_ability,
                'narrow_spread': narrow_spread
            }

            # 权重评分
            weights = {
                'price_above_bid_avg': 25,    # 当前价>买盘均价（核心）
                'close_to_ask1': 25,          # 距卖一<0.3%（核心）
                'price_above_gravity': 20,    # 价格重心分析
                'strong_relative': 15,        # 相对强度
                'anti_fall_ability': 10,      # 抗跌能力
                'narrow_spread': 5            # 价差分析
            }

            for condition, triggered in conditions.items():
                if triggered:
                    strength_score += weights[condition]

            # 价格韧性判断：评分≥60分
            is_strong = strength_score >= 60

            details = {
                'current_price': current_price,
                'bid_avg_price': bid_avg_price,
                'ask1_price': ask1_price,
                'price_to_ask1_ratio': price_to_ask1_ratio,
                'price_gravity': price_gravity,
                'relative_strength': relative_strength,
                'bid_ask_spread': bid_ask_spread,
                'strength_score': strength_score,
                'conditions': conditions,
                'triggered': is_strong
            }

            return is_strong, details

        except Exception as e:
            return False, {'error': str(e)}

    def calculate_precise_bid_avg_price(self, bid_prices, bid_volumes):
        """精确计算买盘均价"""
        try:
            total_value = 0
            total_volume = 0

            for i in range(min(len(bid_prices), len(bid_volumes))):
                if bid_prices[i] > 0 and bid_volumes[i] > 0:
                    total_value += bid_prices[i] * bid_volumes[i]
                    total_volume += bid_volumes[i]

            return total_value / total_volume if total_volume > 0 else 0

        except Exception as e:
            return 0

    def calculate_price_gravity(self, current_price, bid_prices, bid_volumes, ask_prices, ask_volumes):
        """计算实时价格重心"""
        try:
            # 价格重心 = (买一价×买一量 + 卖一价×卖一量) / (买一量+卖一量)
            if not bid_prices or not ask_prices or not bid_volumes or not ask_volumes:
                return current_price

            bid1_price = bid_prices[0] if bid_prices[0] > 0 else current_price
            ask1_price = ask_prices[0] if ask_prices[0] > 0 else current_price
            bid1_volume = bid_volumes[0] if bid_volumes else 0
            ask1_volume = ask_volumes[0] if ask_volumes else 0

            total_volume = bid1_volume + ask1_volume

            if total_volume > 0:
                gravity = (bid1_price * bid1_volume + ask1_price * ask1_volume) / total_volume
                return gravity
            else:
                return current_price

        except Exception as e:
            return current_price

    def calculate_relative_strength(self, code, current_price):
        """计算价格相对强度"""
        try:
            # 获取历史价格数据
            price_history = self.historical_cache.get(code, {}).get('price_history', [])

            # 记录当前价格
            price_history.append(current_price)
            if len(price_history) > 10:
                price_history.pop(0)

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['price_history'] = price_history

            if len(price_history) < 5:
                return 1.0

            # 计算相对强度：当前价 / 5期均价
            avg_price = sum(price_history[-5:]) / 5
            relative_strength = current_price / avg_price if avg_price > 0 else 1.0

            return relative_strength

        except Exception as e:
            return 1.0

    def check_anti_fall_ability(self, code, stock_data):
        """检查价格抗跌能力"""
        try:
            change_pct = stock_data.get('change_pct', 0)

            # 获取历史涨跌幅数据
            change_history = self.historical_cache.get(code, {}).get('change_history', [])

            # 记录当前涨跌幅
            change_history.append(change_pct)
            if len(change_history) > 10:
                change_history.pop(0)

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['change_history'] = change_history

            if len(change_history) < 5:
                return False

            # 检查抗跌能力：在大盘下跌时相对抗跌
            recent_changes = change_history[-5:]
            avg_change = sum(recent_changes) / len(recent_changes)

            # 如果平均跌幅小于-1%但当前跌幅小于平均值，说明有抗跌能力
            if avg_change < -1.0 and change_pct > avg_change:
                return True

            # 如果当前微跌或上涨，也认为有抗跌能力
            return change_pct >= -0.5

        except Exception as e:
            return False

    def analyze_bid_ask_spread(self, bid_prices, ask_prices):
        """分析买卖盘价差"""
        try:
            if not bid_prices or not ask_prices or bid_prices[0] == 0 or ask_prices[0] == 0:
                return 0.05  # 默认5%价差

            bid1 = bid_prices[0]
            ask1 = ask_prices[0]

            spread = (ask1 - bid1) / bid1 if bid1 > 0 else 0.05
            return spread

        except Exception as e:
            return 0.05

    def check_time_window(self, code, stock_data):
        """检测时间窗口共振（增强版）"""
        try:
            now = datetime.now()
            current_time = now.time()

            # 关键时间窗口定义
            time_windows = {
                "10:30": {
                    "description": "早盘洗盘结束点",
                    "signal_type": "volume_breakout",
                    "tolerance": 300  # 5分钟容差
                },
                "13:15": {
                    "description": "午盘偷袭启动点",
                    "signal_type": "bid_surge",
                    "tolerance": 300
                },
                "14:45": {
                    "description": "尾盘抢筹窗口",
                    "signal_type": "ask_sweep",
                    "tolerance": 300
                }
            }

            active_window = None
            window_details = {}

            # 检查是否在关键时间窗口内
            for time_str, window_config in time_windows.items():
                key_time = datetime.strptime(time_str, "%H:%M").time()
                time_diff = abs((datetime.combine(datetime.today(), current_time) -
                               datetime.combine(datetime.today(), key_time)).total_seconds())

                if time_diff <= window_config["tolerance"]:
                    active_window = time_str
                    window_details = {
                        'current_time': current_time.strftime("%H:%M:%S"),
                        'key_time': time_str,
                        'description': window_config["description"],
                        'signal_type': window_config["signal_type"],
                        'time_diff_seconds': time_diff,
                        'in_window': True
                    }
                    break

            if not active_window:
                return False, {
                    'current_time': current_time.strftime("%H:%M:%S"),
                    'in_window': False,
                    'reason': '不在关键时间窗口'
                }

            # 根据时间窗口验证特定信号
            signal_verified = False
            signal_details = {}

            if active_window == "10:30":
                # 早盘洗盘结束：检测放量突破分时均线
                signal_verified, signal_details = self.check_volume_breakout_signal(code, stock_data)

            elif active_window == "13:15":
                # 午盘偷袭启动：检测买盘托单突然增厚200%
                signal_verified, signal_details = self.check_bid_surge_200_signal(code, stock_data)

            elif active_window == "14:45":
                # 尾盘抢筹：检测卖盘压单被连续扫货
                signal_verified, signal_details = self.check_ask_sweep_signal(code, stock_data)

            # 综合判断
            window_resonance = signal_verified

            details = {
                **window_details,
                'signal_verified': signal_verified,
                'signal_details': signal_details,
                'triggered': window_resonance
            }

            return window_resonance, details

        except Exception as e:
            return False, {'error': str(e)}

    def check_volume_breakout_signal(self, code, stock_data):
        """检测放量突破分时均线信号（10:30窗口）"""
        try:
            current_volume = stock_data.get('volume', 0)
            current_price = stock_data.get('latest_price', 0)

            # 获取分时均线（简化为5分钟均价）
            price_history = self.historical_cache.get(code, {}).get('price_history', [])

            if len(price_history) < 5:
                return False, {'reason': '价格历史数据不足'}

            # 计算5分钟均价
            avg_price_5min = sum(price_history[-5:]) / 5

            # 检测1：价格突破分时均线
            price_breakout = current_price > avg_price_5min * 1.01  # 突破1%以上

            # 检测2：放量确认
            minute_volumes = self.historical_cache.get(code, {}).get('minute_volumes', [])
            if len(minute_volumes) >= 5:
                avg_volume_5min = sum(minute_volumes[-5:]) / 5
                volume_surge = current_volume > avg_volume_5min * 1.5  # 放量50%以上
            else:
                volume_surge = False

            # 综合判断
            breakout_confirmed = price_breakout and volume_surge

            details = {
                'current_price': current_price,
                'avg_price_5min': avg_price_5min,
                'price_breakout': price_breakout,
                'volume_surge': volume_surge,
                'triggered': breakout_confirmed
            }

            return breakout_confirmed, details

        except Exception as e:
            return False, {'error': str(e)}

    def check_bid_surge_200_signal(self, code, stock_data):
        """检测买盘托单突然增厚200%信号（13:15窗口）"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)

            if not bid_volumes or bid_volumes[0] == 0:
                return False, {'reason': '无买盘数据'}

            current_bid1 = bid_volumes[0]
            historical_bid1 = self.get_historical_bid1(code)

            if historical_bid1 == 0:
                return False, {'reason': '无历史买一数据'}

            # 检测买一托单增厚200%（3倍）
            surge_ratio = current_bid1 / historical_bid1
            bid_surge_200 = surge_ratio >= 3.0

            # 检测买盘整体增厚
            total_bid = sum(bid_volumes)
            historical_total_bid = self.historical_cache.get(code, {}).get('historical_total_bid', 0)

            if historical_total_bid > 0:
                total_surge_ratio = total_bid / historical_total_bid
                total_surge = total_surge_ratio >= 2.0  # 总买盘增厚100%
            else:
                total_surge = False

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['historical_total_bid'] = total_bid

            # 综合判断
            surge_confirmed = bid_surge_200 or total_surge

            details = {
                'current_bid1': current_bid1,
                'historical_bid1': historical_bid1,
                'surge_ratio': surge_ratio,
                'total_surge_ratio': total_surge_ratio if historical_total_bid > 0 else 0,
                'bid_surge_200': bid_surge_200,
                'total_surge': total_surge,
                'triggered': surge_confirmed
            }

            return surge_confirmed, details

        except Exception as e:
            return False, {'error': str(e)}

    def check_ask_sweep_signal(self, code, stock_data):
        """检测卖盘压单被连续扫货信号（14:45窗口）"""
        try:
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            current_volume = stock_data.get('volume', 0)

            if not ask_volumes:
                return False, {'reason': '无卖盘数据'}

            # 检测1：卖盘压单减少
            ask_total = sum(ask_volumes)
            historical_ask_total = self.historical_cache.get(code, {}).get('historical_ask_total', 0)

            if historical_ask_total > 0:
                ask_reduction_ratio = (historical_ask_total - ask_total) / historical_ask_total
                ask_swept = ask_reduction_ratio > 0.3  # 卖盘减少30%以上
            else:
                ask_swept = False

            # 检测2：成交量放大（扫货特征）
            minute_volumes = self.historical_cache.get(code, {}).get('minute_volumes', [])
            if len(minute_volumes) >= 3:
                recent_avg_volume = sum(minute_volumes[-3:]) / 3
                volume_surge = current_volume > recent_avg_volume * 1.8  # 成交量放大80%
            else:
                volume_surge = False

            # 检测3：连续扫货模式（卖一持续减少）
            ask1_history = self.historical_cache.get(code, {}).get('ask1_history', [])
            continuous_sweep = False

            if len(ask1_history) >= 3:
                # 检查最近3次卖一是否持续减少
                decreasing_count = 0
                for i in range(len(ask1_history) - 2, len(ask1_history)):
                    if i > 0 and ask1_history[i] < ask1_history[i-1]:
                        decreasing_count += 1

                continuous_sweep = decreasing_count >= 2

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['historical_ask_total'] = ask_total

            # 综合判断
            sweep_confirmed = ask_swept and (volume_surge or continuous_sweep)

            details = {
                'ask_total': ask_total,
                'historical_ask_total': historical_ask_total,
                'ask_reduction_ratio': ask_reduction_ratio if historical_ask_total > 0 else 0,
                'ask_swept': ask_swept,
                'volume_surge': volume_surge,
                'continuous_sweep': continuous_sweep,
                'triggered': sweep_confirmed
            }

            return sweep_confirmed, details

        except Exception as e:
            return False, {'error': str(e)}

    def check_chip_control(self, code, stock_data):
        """检测筹码分布（高度控盘）完善版"""
        try:
            current_price = stock_data.get('latest_price', 0)

            if current_price == 0:
                return False, {'reason': '无当前价格'}

            # 获取关键价格数据
            price_data = self.get_key_price_data(code)

            if not price_data:
                return False, {'reason': '无法获取关键价格数据'}

            prev_close = price_data.get('prev_close', 0)
            prev_high = price_data.get('prev_high', 0)

            # 检测1：股价>前日最高价的前置条件
            above_prev_high = current_price > prev_high if prev_high > 0 else False

            # 检测2：精确的获利盘比例计算
            profit_ratio = self.calculate_precise_profit_ratio(code, current_price, price_data)

            # 检测3：获利盘<15%的高度控盘判断
            low_profit_ratio = profit_ratio < 0.15

            # 检测4：筹码集中度分析
            chip_concentration = self.analyze_chip_concentration(code, current_price, price_data)
            high_concentration = chip_concentration > 0.7  # 筹码集中度>70%

            # 检测5：主力控盘度估算
            control_degree = self.estimate_control_degree(code, stock_data, price_data)
            high_control = control_degree > 0.6  # 控盘度>60%

            # 检测6：浮筹分析
            floating_chips = self.analyze_floating_chips(code, current_price, price_data)
            low_floating = floating_chips < 0.2  # 浮筹<20%

            # 综合评分系统
            control_score = 0
            conditions = {
                'above_prev_high': above_prev_high,
                'low_profit_ratio': low_profit_ratio,
                'high_concentration': high_concentration,
                'high_control': high_control,
                'low_floating': low_floating
            }

            # 权重评分
            weights = {
                'above_prev_high': 20,        # 突破前日最高价
                'low_profit_ratio': 30,       # 获利盘<15%（核心）
                'high_concentration': 20,     # 筹码集中度高
                'high_control': 15,           # 主力控盘度高
                'low_floating': 15            # 浮筹少
            }

            for condition, triggered in conditions.items():
                if triggered:
                    control_score += weights[condition]

            # 筹码控盘判断：评分≥60分
            is_controlled = control_score >= 60

            details = {
                'current_price': current_price,
                'prev_close': prev_close,
                'prev_high': prev_high,
                'profit_ratio': profit_ratio,
                'chip_concentration': chip_concentration,
                'control_degree': control_degree,
                'floating_chips': floating_chips,
                'control_score': control_score,
                'conditions': conditions,
                'triggered': is_controlled
            }

            return is_controlled, details

        except Exception as e:
            return False, {'error': str(e)}

    def get_key_price_data(self, code):
        """获取关键价格数据"""
        try:
            if enhanced_stock_fetcher:
                # 获取最近3日K线数据
                kline_df = enhanced_stock_fetcher.get_daily_kline(code, 3)

                if not kline_df.empty and len(kline_df) >= 2:
                    # 前日数据
                    prev_data = kline_df.iloc[-2]

                    price_data = {
                        'prev_close': float(prev_data.get('收盘', prev_data.get('close', 0))),
                        'prev_high': float(prev_data.get('最高', prev_data.get('high', 0))),
                        'prev_low': float(prev_data.get('最低', prev_data.get('low', 0))),
                        'prev_open': float(prev_data.get('开盘', prev_data.get('open', 0))),
                        'prev_volume': int(prev_data.get('成交量', prev_data.get('volume', 0)))
                    }

                    # 如果有更多历史数据，添加更多信息
                    if len(kline_df) >= 3:
                        # 前前日数据
                        prev2_data = kline_df.iloc[-3]
                        price_data.update({
                            'prev2_close': float(prev2_data.get('收盘', prev2_data.get('close', 0))),
                            'prev2_high': float(prev2_data.get('最高', prev2_data.get('high', 0)))
                        })

                    return price_data

            return {}

        except Exception as e:
            print(f"⚠️ 获取关键价格数据失败 {code}: {e}")
            return {}

    def calculate_precise_profit_ratio(self, code, current_price, price_data):
        """精确计算获利盘比例"""
        try:
            prev_close = price_data.get('prev_close', 0)
            prev_low = price_data.get('prev_low', 0)
            prev2_close = price_data.get('prev2_close', 0)

            if prev_close == 0:
                return 0

            # 方法1：基于前日收盘价的简单计算
            simple_profit = max(0, (current_price - prev_close) / prev_close)

            # 方法2：基于近期价格区间的获利盘估算
            if prev_low > 0 and prev2_close > 0:
                # 假设筹码主要分布在前2日的价格区间内
                price_range_low = min(prev_close, prev2_close, prev_low)
                price_range_high = max(prev_close, prev2_close)

                if price_range_high > price_range_low:
                    # 当前价格在区间中的位置
                    position_ratio = (current_price - price_range_low) / (price_range_high - price_range_low)
                    # 获利盘比例 = 当前价格下方的筹码比例
                    estimated_profit = min(position_ratio, 1.0)
                else:
                    estimated_profit = simple_profit
            else:
                estimated_profit = simple_profit

            # 取两种方法的平均值
            final_profit_ratio = (simple_profit + estimated_profit) / 2

            return min(final_profit_ratio, 1.0)  # 限制在0-1之间

        except Exception as e:
            return 0

    def analyze_chip_concentration(self, code, current_price, price_data):
        """分析筹码集中度"""
        try:
            prev_volume = price_data.get('prev_volume', 0)

            # 获取历史成交量数据
            volumes = self.historical_cache.get(code, {}).get('volumes', [])

            if len(volumes) < 5 or prev_volume == 0:
                return 0.5  # 默认中等集中度

            # 计算成交量变异系数（集中度指标）
            avg_volume = sum(volumes) / len(volumes)
            volume_variance = sum((v - avg_volume) ** 2 for v in volumes) / len(volumes)
            volume_std = volume_variance ** 0.5

            # 变异系数越小，集中度越高
            if avg_volume > 0:
                cv = volume_std / avg_volume
                concentration = max(0, 1 - cv)  # 转换为集中度
            else:
                concentration = 0.5

            return min(concentration, 1.0)

        except Exception as e:
            return 0.5

    def estimate_control_degree(self, code, stock_data, price_data):
        """估算主力控盘度"""
        try:
            current_volume = stock_data.get('volume', 0)
            turnover_rate = stock_data.get('turnover_rate', 0)

            # 控盘度指标1：换手率（换手率越低，控盘度越高）
            if turnover_rate > 0:
                turnover_control = max(0, 1 - turnover_rate / 10)  # 假设10%换手率为基准
            else:
                turnover_control = 0.5

            # 控盘度指标2：成交量稳定性
            volumes = self.historical_cache.get(code, {}).get('volumes', [])
            if len(volumes) >= 5:
                volume_stability = self.calculate_volume_stability(volumes)
            else:
                volume_stability = 0.5

            # 控盘度指标3：价格稳定性
            price_history = self.historical_cache.get(code, {}).get('price_history', [])
            if len(price_history) >= 5:
                price_stability = self.calculate_price_stability(price_history)
            else:
                price_stability = 0.5

            # 综合控盘度
            control_degree = (turnover_control * 0.4 + volume_stability * 0.3 + price_stability * 0.3)

            return min(control_degree, 1.0)

        except Exception as e:
            return 0.5

    def analyze_floating_chips(self, code, current_price, price_data):
        """分析浮筹比例"""
        try:
            prev_high = price_data.get('prev_high', 0)
            prev_low = price_data.get('prev_low', 0)

            if prev_high == 0 or prev_low == 0:
                return 0.3  # 默认浮筹比例

            # 浮筹估算：基于价格波动幅度
            price_range = prev_high - prev_low
            price_volatility = price_range / prev_low if prev_low > 0 else 0

            # 波动越大，浮筹越多
            floating_ratio = min(price_volatility * 2, 1.0)  # 简化计算

            return floating_ratio

        except Exception as e:
            return 0.3

    def calculate_volume_stability(self, volumes):
        """计算成交量稳定性"""
        try:
            if len(volumes) < 2:
                return 0.5

            avg_volume = sum(volumes) / len(volumes)
            variance = sum((v - avg_volume) ** 2 for v in volumes) / len(volumes)
            std_dev = variance ** 0.5

            # 变异系数越小，稳定性越高
            if avg_volume > 0:
                cv = std_dev / avg_volume
                stability = max(0, 1 - cv)
            else:
                stability = 0.5

            return min(stability, 1.0)

        except Exception as e:
            return 0.5

    def calculate_price_stability(self, prices):
        """计算价格稳定性"""
        try:
            if len(prices) < 2:
                return 0.5

            avg_price = sum(prices) / len(prices)
            variance = sum((p - avg_price) ** 2 for p in prices) / len(prices)
            std_dev = variance ** 0.5

            # 变异系数越小，稳定性越高
            if avg_price > 0:
                cv = std_dev / avg_price
                stability = max(0, 1 - cv * 10)  # 价格稳定性权重调整
            else:
                stability = 0.5

            return min(stability, 1.0)

        except Exception as e:
            return 0.5

    def calculate_core_indicators(self, code, stock_data):
        """计算核心监控指标"""
        try:
            bid_volumes = stock_data.get('bid_volumes', [0] * 5)
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)
            bid_prices = stock_data.get('bid_prices', [0] * 5)
            ask_prices = stock_data.get('ask_prices', [0] * 5)
            current_volume = stock_data.get('volume', 0)

            # 指标1：买一挂单量变化率
            bid1_change_rate = self.calculate_bid1_change_rate(code, bid_volumes)

            # 指标2：卖压系数精确计算
            ask_pressure_coefficient = self.calculate_precise_ask_pressure(bid_volumes, ask_volumes)

            # 指标3：价格重心实时计算
            price_gravity_realtime = self.calculate_realtime_price_gravity(bid_prices, bid_volumes, ask_prices, ask_volumes)

            # 指标4：量能突击指标计算
            volume_attack_indicator = self.calculate_volume_attack_indicator(code, current_volume)

            # 指标5：买卖盘力量对比
            bid_ask_power_ratio = self.calculate_bid_ask_power_ratio(bid_prices, bid_volumes, ask_prices, ask_volumes)

            # 指标6：市场情绪指标
            market_sentiment = self.calculate_market_sentiment(code, stock_data)

            indicators = {
                'bid1_change_rate': bid1_change_rate,
                'ask_pressure_coefficient': ask_pressure_coefficient,
                'price_gravity_realtime': price_gravity_realtime,
                'volume_attack_indicator': volume_attack_indicator,
                'bid_ask_power_ratio': bid_ask_power_ratio,
                'market_sentiment': market_sentiment
            }

            return indicators

        except Exception as e:
            print(f"⚠️ 计算核心指标失败 {code}: {e}")
            return {}

    def calculate_bid1_change_rate(self, code, bid_volumes):
        """计算买一挂单量变化率：Δ(当前买一量/5分钟前买一量)"""
        try:
            if not bid_volumes or bid_volumes[0] == 0:
                return 0

            current_bid1 = bid_volumes[0]

            # 获取5分钟前的买一量
            bid1_history = self.historical_cache.get(code, {}).get('bid1_5min_history', [])

            # 记录当前买一量
            bid1_history.append(current_bid1)

            # 保持5分钟的历史数据（假设30秒扫描一次，5分钟=10个数据点）
            if len(bid1_history) > 10:
                bid1_history.pop(0)

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['bid1_5min_history'] = bid1_history

            # 计算变化率
            if len(bid1_history) >= 10:  # 有5分钟前的数据
                bid1_5min_ago = bid1_history[0]
                if bid1_5min_ago > 0:
                    change_rate = (current_bid1 - bid1_5min_ago) / bid1_5min_ago
                    return change_rate

            return 0

        except Exception as e:
            return 0

    def calculate_precise_ask_pressure(self, bid_volumes, ask_volumes):
        """精确计算卖压系数：(卖一量+卖二量)/买一量"""
        try:
            if not bid_volumes or not ask_volumes or bid_volumes[0] == 0:
                return 999  # 无买盘时卖压极大

            bid1_volume = bid_volumes[0]
            ask1_volume = ask_volumes[0] if ask_volumes else 0
            ask2_volume = ask_volumes[1] if len(ask_volumes) > 1 else 0

            ask_pressure = (ask1_volume + ask2_volume) / bid1_volume

            return ask_pressure

        except Exception as e:
            return 999

    def calculate_realtime_price_gravity(self, bid_prices, bid_volumes, ask_prices, ask_volumes):
        """实时计算价格重心：(买一价×买一量 + 卖一价×卖一量)/(买一量+卖一量)"""
        try:
            if (not bid_prices or not ask_prices or not bid_volumes or not ask_volumes or
                bid_prices[0] == 0 or ask_prices[0] == 0):
                return 0

            bid1_price = bid_prices[0]
            ask1_price = ask_prices[0]
            bid1_volume = bid_volumes[0]
            ask1_volume = ask_volumes[0]

            total_volume = bid1_volume + ask1_volume

            if total_volume > 0:
                price_gravity = (bid1_price * bid1_volume + ask1_price * ask1_volume) / total_volume
                return price_gravity

            return 0

        except Exception as e:
            return 0

    def calculate_volume_attack_indicator(self, code, current_volume):
        """计算量能突击指标：当前分钟成交量 / 前5分钟均量"""
        try:
            if current_volume == 0:
                return 0

            # 获取前5分钟成交量数据
            minute_volumes = self.historical_cache.get(code, {}).get('minute_volumes', [])

            if len(minute_volumes) >= 5:
                avg_5min_volume = sum(minute_volumes[-5:]) / 5
                if avg_5min_volume > 0:
                    attack_indicator = current_volume / avg_5min_volume
                    return attack_indicator

            return 1.0  # 默认无突击

        except Exception as e:
            return 1.0

    def calculate_bid_ask_power_ratio(self, bid_prices, bid_volumes, ask_prices, ask_volumes):
        """计算买卖盘力量对比"""
        try:
            if not bid_prices or not ask_prices or not bid_volumes or not ask_volumes:
                return 1.0

            # 计算买盘总力量（价格×数量的加权）
            bid_power = 0
            for i in range(min(len(bid_prices), len(bid_volumes))):
                if bid_prices[i] > 0 and bid_volumes[i] > 0:
                    bid_power += bid_prices[i] * bid_volumes[i]

            # 计算卖盘总力量
            ask_power = 0
            for i in range(min(len(ask_prices), len(ask_volumes))):
                if ask_prices[i] > 0 and ask_volumes[i] > 0:
                    ask_power += ask_prices[i] * ask_volumes[i]

            # 买卖盘力量比
            if ask_power > 0:
                power_ratio = bid_power / ask_power
                return power_ratio

            return 999  # 无卖盘时买盘力量极大

        except Exception as e:
            return 1.0

    def calculate_market_sentiment(self, code, stock_data):
        """计算市场情绪指标"""
        try:
            change_pct = stock_data.get('change_pct', 0)
            volume = stock_data.get('volume', 0)

            # 情绪指标1：价格动量
            price_momentum = change_pct / 10  # 标准化到-1到1之间

            # 情绪指标2：成交活跃度
            volumes = self.historical_cache.get(code, {}).get('volumes', [])
            if len(volumes) >= 3:
                avg_volume = sum(volumes[-3:]) / 3
                volume_activity = volume / avg_volume if avg_volume > 0 else 1
                volume_activity = min(volume_activity, 3.0)  # 限制最大值
            else:
                volume_activity = 1.0

            # 情绪指标3：价格稳定性（波动越小情绪越稳定）
            price_history = self.historical_cache.get(code, {}).get('price_history', [])
            if len(price_history) >= 5:
                price_stability = self.calculate_price_stability(price_history)
            else:
                price_stability = 0.5

            # 综合情绪指标
            sentiment = (price_momentum + volume_activity + price_stability) / 3

            return sentiment

        except Exception as e:
            return 0.5

    def confirm_signals(self, code, signals, stock_data):
        """信号确认机制"""
        try:
            confirmed_signals = signals.copy()

            # 确认机制1：2分钟确认窗口
            window_confirmed = self.check_2min_confirmation_window(code, signals)

            # 确认机制2：黄金法则 - 启动前卖一<50手
            golden_rule_confirmed = self.check_golden_rule(stock_data)

            # 确认机制3：量价齐升确认 - 首笔拉升>日均量
            volume_price_confirmed = self.check_volume_price_rise(code, stock_data)

            # 确认机制4：假信号过滤
            fake_signal_filtered = self.filter_fake_signals(code, stock_data)

            # 综合确认评分
            confirmation_score = 0
            confirmation_details = {
                'window_confirmed': window_confirmed,
                'golden_rule_confirmed': golden_rule_confirmed,
                'volume_price_confirmed': volume_price_confirmed,
                'fake_signal_filtered': fake_signal_filtered
            }

            # 确认权重
            confirmation_weights = {
                'window_confirmed': 30,
                'golden_rule_confirmed': 25,
                'volume_price_confirmed': 25,
                'fake_signal_filtered': 20
            }

            for check, confirmed in confirmation_details.items():
                if confirmed:
                    confirmation_score += confirmation_weights[check]

            # 只有确认评分≥60分的信号才被认为是可靠的
            signal_confirmed = confirmation_score >= 60

            # 更新确认后的信号
            confirmed_signals['signal_confirmed'] = signal_confirmed
            confirmed_signals['confirmation_score'] = confirmation_score
            confirmed_signals['confirmation_details'] = confirmation_details

            # 如果信号未确认，降低警报评分
            if not signal_confirmed:
                original_score = confirmed_signals.get('alert_score', 0)
                confirmed_signals['alert_score'] = original_score * 0.5  # 降低50%

            return confirmed_signals

        except Exception as e:
            print(f"⚠️ 信号确认失败 {code}: {e}")
            return signals

    def check_2min_confirmation_window(self, code, signals):
        """检查2分钟确认窗口机制"""
        try:
            # 获取信号历史记录
            signal_history = self.historical_cache.get(code, {}).get('signal_history', [])

            # 记录当前信号
            current_time = datetime.now()
            current_signal = {
                'time': current_time,
                'alert_score': signals.get('alert_score', 0),
                'signals': {k: v for k, v in signals.items() if k in ['bid_surge', 'ask_exhaustion', 'volume_pulse', 'price_strength', 'time_window', 'chip_control']}
            }

            signal_history.append(current_signal)

            # 保持最近10分钟的信号历史
            cutoff_time = current_time - timedelta(minutes=10)
            signal_history = [s for s in signal_history if s['time'] > cutoff_time]

            # 更新历史数据
            if code not in self.historical_cache:
                self.historical_cache[code] = {}
            self.historical_cache[code]['signal_history'] = signal_history

            # 检查2分钟内是否有持续信号
            two_min_ago = current_time - timedelta(minutes=2)
            recent_signals = [s for s in signal_history if s['time'] > two_min_ago]

            if len(recent_signals) >= 2:  # 至少有2个信号记录
                # 检查信号的一致性
                consistent_signals = 0
                for signal_key in ['bid_surge', 'ask_exhaustion', 'volume_pulse']:
                    if all(s['signals'].get(signal_key, False) for s in recent_signals):
                        consistent_signals += 1

                # 如果有2个以上一致信号，认为确认
                return consistent_signals >= 2

            return False

        except Exception as e:
            return False

    def check_golden_rule(self, stock_data):
        """检查黄金法则：启动前卖一<50手"""
        try:
            ask_volumes = stock_data.get('ask_volumes', [0] * 5)

            if not ask_volumes:
                return False

            ask1_volume = ask_volumes[0]

            # 黄金法则：卖一量必须<50手
            golden_rule_met = ask1_volume < 50

            return golden_rule_met

        except Exception as e:
            return False

    def check_volume_price_rise(self, code, stock_data):
        """检查量价齐升确认：首笔拉升>日均量"""
        try:
            current_volume = stock_data.get('volume', 0)
            change_pct = stock_data.get('change_pct', 0)

            # 获取日均成交量
            volumes = self.historical_cache.get(code, {}).get('volumes', [])

            if len(volumes) < 3:
                return False

            daily_avg_volume = sum(volumes) / len(volumes)

            # 检查条件：
            # 1. 价格上涨（涨幅>0.5%）
            price_rising = change_pct > 0.5

            # 2. 成交量放大（>日均量）
            volume_surge = current_volume > daily_avg_volume

            # 量价齐升确认
            volume_price_rise = price_rising and volume_surge

            return volume_price_rise

        except Exception as e:
            return False

    def filter_fake_signals(self, code, stock_data):
        """假信号过滤机制"""
        try:
            change_pct = stock_data.get('change_pct', 0)

            # 过滤条件1：排除大盘急跌时段
            market_decline_filter = self.check_market_decline_filter()

            # 过滤条件2：避免消息刺激股
            news_stimulus_filter = self.check_news_stimulus_filter(code, stock_data)

            # 过滤条件3：技术面确认
            technical_filter = self.check_technical_filter(code, stock_data)

            # 过滤条件4：成交量真实性
            volume_authenticity_filter = self.check_volume_authenticity(code, stock_data)

            # 综合过滤结果
            filter_passed = (market_decline_filter and
                           news_stimulus_filter and
                           technical_filter and
                           volume_authenticity_filter)

            return filter_passed

        except Exception as e:
            return True  # 过滤失败时默认通过

    def check_market_decline_filter(self):
        """检查大盘急跌过滤"""
        try:
            # 这里应该获取大盘指数数据
            # 简化实现：假设大盘正常
            return True

        except Exception as e:
            return True

    def check_news_stimulus_filter(self, code, stock_data):
        """检查消息刺激过滤"""
        try:
            change_pct = stock_data.get('change_pct', 0)
            current_volume = stock_data.get('volume', 0)

            # 获取历史数据
            volumes = self.historical_cache.get(code, {}).get('volumes', [])

            if len(volumes) < 3:
                return True

            avg_volume = sum(volumes[-3:]) / 3

            # 如果涨幅过大且成交量异常放大，可能是消息刺激
            if change_pct > 5.0 and current_volume > avg_volume * 5:
                return False  # 可能是消息刺激，过滤掉

            return True

        except Exception as e:
            return True

    def check_technical_filter(self, code, stock_data):
        """检查技术面过滤"""
        try:
            # 获取价格历史
            price_history = self.historical_cache.get(code, {}).get('price_history', [])

            if len(price_history) < 5:
                return True

            current_price = stock_data.get('latest_price', 0)

            # 检查是否在合理的技术位置
            recent_high = max(price_history[-5:])
            recent_low = min(price_history[-5:])

            # 如果当前价格在合理区间内，通过过滤
            if recent_low <= current_price <= recent_high * 1.1:
                return True

            return False

        except Exception as e:
            return True

    def check_volume_authenticity(self, code, stock_data):
        """检查成交量真实性"""
        try:
            current_volume = stock_data.get('volume', 0)

            # 获取成交量历史
            volumes = self.historical_cache.get(code, {}).get('volumes', [])

            if len(volumes) < 5:
                return True

            # 检查成交量是否过于异常
            avg_volume = sum(volumes) / len(volumes)
            max_volume = max(volumes)

            # 如果当前成交量超过历史最大值的3倍，可能不真实
            if current_volume > max_volume * 3:
                return False

            return True

        except Exception as e:
            return True

        except Exception as e:
            return 0.5

    def calculate_alert_score(self, signals):
        """计算综合报警评分"""
        try:
            score = 0

            # 各信号权重
            weights = {
                'bid_surge': 30,      # 买盘托单突变（最重要）
                'ask_exhaustion': 25, # 卖压枯竭
                'volume_pulse': 20,   # 量能脉冲
                'price_strength': 15, # 价格韧性
                'time_window': 10,    # 时间窗口
                'chip_control': 5     # 筹码分布
            }

            # 计算加权得分
            for signal_name, weight in weights.items():
                if signals.get(signal_name, False):
                    score += weight

            return score

        except Exception as e:
            print(f"⚠️ 计算报警评分失败: {e}")
            return 0

    def check_alert_conditions(self, signals):
        """检查是否触发报警条件（优化版）"""
        try:
            # 获取核心监控指标
            core_indicators = signals.get('core_indicators', {})

            # 报告要求的精确阈值：买一突增>500% + 卖压系数<0.3 + 量能突击>2.5
            bid1_change_rate = core_indicators.get('bid1_change_rate', 0)
            ask_pressure_coefficient = core_indicators.get('ask_pressure_coefficient', 999)
            volume_attack_indicator = core_indicators.get('volume_attack_indicator', 0)

            # 红色警报条件（报告要求的精确组合）
            red_alert_conditions = {
                'bid1_surge_500': bid1_change_rate > 5.0,  # 买一突增>500%
                'ask_pressure_low': ask_pressure_coefficient < 0.3,  # 卖压系数<0.3
                'volume_attack_high': volume_attack_indicator > 2.5   # 量能突击>2.5
            }

            # 红色警报：三个条件同时满足
            red_alert = all(red_alert_conditions.values())

            # 黄色警报条件（次级警报）
            yellow_alert_conditions = {
                'bid1_surge_300': bid1_change_rate > 3.0,  # 买一突增>300%
                'ask_pressure_medium': ask_pressure_coefficient < 0.5,  # 卖压系数<0.5
                'volume_attack_medium': volume_attack_indicator > 1.8   # 量能突击>1.8
            }

            # 黄色警报：至少两个条件满足
            yellow_alert_count = sum(yellow_alert_conditions.values())
            yellow_alert = yellow_alert_count >= 2

            # 综合评分警报
            alert_score = signals.get('alert_score', 0)
            score_alert = alert_score >= 60

            # 时间窗口加权
            time_window_active = signals.get('time_window', False)
            time_weight = 1.5 if time_window_active else 1.0

            # 最终警报判断
            final_alert = red_alert or (yellow_alert and time_weight > 1.0) or score_alert

            # 记录警报详情
            alert_details = {
                'red_alert': red_alert,
                'yellow_alert': yellow_alert,
                'score_alert': score_alert,
                'time_window_active': time_window_active,
                'red_alert_conditions': red_alert_conditions,
                'yellow_alert_conditions': yellow_alert_conditions,
                'alert_score': alert_score,
                'final_alert': final_alert
            }

            # 更新信号中的警报详情
            signals['alert_details'] = alert_details

            return final_alert

        except Exception as e:
            print(f"⚠️ 检查报警条件失败: {e}")
            return False

    # 历史数据管理方法
    def get_historical_bid1(self, code):
        """获取历史买一数据"""
        return self.historical_cache.get(code, {}).get('bid1', 0)

    def update_historical_bid1(self, code, bid1):
        """更新历史买一数据"""
        if code not in self.historical_cache:
            self.historical_cache[code] = {}
        self.historical_cache[code]['bid1'] = bid1

    def get_historical_avg_volume(self, code):
        """获取历史平均成交量"""
        return self.historical_cache.get(code, {}).get('avg_volume', 0)

    def update_historical_volume(self, code, volume):
        """更新历史成交量数据"""
        if code not in self.historical_cache:
            self.historical_cache[code] = {}

        # 更新日级成交量（保留最近5个数据点）
        volumes = self.historical_cache[code].get('volumes', [])
        volumes.append(volume)
        if len(volumes) > 5:
            volumes.pop(0)

        self.historical_cache[code]['volumes'] = volumes
        self.historical_cache[code]['avg_volume'] = sum(volumes) / len(volumes)

        # 更新分钟级成交量（保留最近10个数据点）
        minute_volumes = self.historical_cache[code].get('minute_volumes', [])
        minute_volumes.append(volume)
        if len(minute_volumes) > 10:
            minute_volumes.pop(0)

        self.historical_cache[code]['minute_volumes'] = minute_volumes

    def get_previous_close_price(self, code):
        """获取前日收盘价"""
        try:
            if enhanced_stock_fetcher:
                # 获取日K线数据
                kline_df = enhanced_stock_fetcher.get_daily_kline(code, 2)
                if not kline_df.empty and len(kline_df) >= 2:
                    # 返回前一日收盘价
                    return float(kline_df.iloc[-2].get('收盘', kline_df.iloc[-2].get('close', 0)))

            return 0
        except Exception as e:
            print(f"⚠️ 获取 {code} 前日收盘价失败: {e}")
            return 0

    def get_historical_ask_volumes(self, code):
        """获取历史卖盘数据"""
        return self.historical_cache.get(code, {}).get('ask_volumes', [])

    def update_historical_ask_volumes(self, code, ask_volumes):
        """更新历史卖盘数据"""
        if code not in self.historical_cache:
            self.historical_cache[code] = {}
        self.historical_cache[code]['ask_volumes'] = ask_volumes.copy()

    def update_stock_display(self, code):
        """更新股票显示"""
        try:
            if code not in self.monitored_stocks:
                return

            stock_info = self.monitored_stocks[code]
            data = stock_info.get('data', {})
            signals = stock_info.get('signals', {})

            # 准备显示数据
            latest_price = data.get('latest_price', 0)
            change_pct = data.get('change_pct', 0)

            # 核心监控指标
            core_indicators = signals.get('core_indicators', {})

            # 买一变化率
            bid1_change_rate = core_indicators.get('bid1_change_rate', 0)
            bid1_change_display = f"{bid1_change_rate:+.1%}" if bid1_change_rate != 0 else "-"

            # 卖压系数
            ask_pressure = core_indicators.get('ask_pressure_coefficient', 0)
            ask_pressure_display = f"{ask_pressure:.2f}" if ask_pressure < 999 else "∞"

            # 量能突击
            volume_attack = core_indicators.get('volume_attack_indicator', 0)
            volume_attack_display = f"{volume_attack:.2f}x" if volume_attack > 0 else "-"

            # 价格重心
            price_gravity = core_indicators.get('price_gravity_realtime', 0)
            price_gravity_display = f"{price_gravity:.2f}" if price_gravity > 0 else "-"

            # 信号状态
            alert_score = signals.get('alert_score', 0)
            triggered_signals = []
            for signal_name in ['bid_surge', 'ask_exhaustion', 'volume_pulse', 'price_strength', 'time_window', 'chip_control']:
                if signals.get(signal_name, False):
                    signal_names = {
                        'bid_surge': '托单突增',
                        'ask_exhaustion': '卖压枯竭',
                        'volume_pulse': '量能脉冲',
                        'price_strength': '价格韧性',
                        'time_window': '时间窗口',
                        'chip_control': '筹码控盘'
                    }
                    triggered_signals.append(signal_names[signal_name])

            signal_status = f"评分:{alert_score} " + ",".join(triggered_signals) if triggered_signals else f"评分:{alert_score}"

            # 最后更新时间
            last_update = stock_info.get('last_update')
            update_time = last_update.strftime("%H:%M:%S") if last_update else "-"

            # 更新表格行
            values = (
                code,
                stock_info['name'],
                f"{latest_price:.2f}" if latest_price > 0 else "-",
                f"{change_pct:+.2f}%" if change_pct != 0 else "-",
                bid1_change_display,
                ask_pressure_display,
                volume_attack_display,
                price_gravity_display,
                signal_status,
                update_time
            )

            # 更新表格项
            if self.tree.exists(code):
                self.tree.item(code, values=values)

                # 根据信号状态设置行颜色
                if alert_score >= 60:
                    self.tree.set(code, '信号状态', f"🚨 {signal_status}")
                elif alert_score >= 30:
                    self.tree.set(code, '信号状态', f"⚠️ {signal_status}")
                else:
                    self.tree.set(code, '信号状态', signal_status)

        except Exception as e:
            print(f"⚠️ 更新股票显示失败 {code}: {e}")

    def send_alert(self, code, signals):
        """发送报警通知（优化版）"""
        try:
            if not NOTIFICATION_AVAILABLE:
                print(f"🚨 报警触发: {code} - 通知功能不可用")
                return

            stock_info = self.monitored_stocks.get(code, {})
            stock_name = stock_info.get('name', f'股票{code}')
            alert_details = signals.get('alert_details', {})
            core_indicators = signals.get('core_indicators', {})

            # 确定警报级别
            red_alert = alert_details.get('red_alert', False)
            yellow_alert = alert_details.get('yellow_alert', False)
            alert_score = signals.get('alert_score', 0)

            # 根据警报级别设置通知样式
            if red_alert:
                alert_level = "🔴 红色警报"
                title = "🚨 潜在股监控器 - 红色警报！"
                urgency = "CRITICAL"
            elif yellow_alert:
                alert_level = "🟡 黄色警报"
                title = "⚠️ 潜在股监控器 - 黄色警报"
                urgency = "HIGH"
            else:
                alert_level = "🔵 蓝色提醒"
                title = "💡 潜在股监控器 - 信号提醒"
                urgency = "NORMAL"

            # 构建详细消息
            message = f"{code} {stock_name}\n"
            message += f"{alert_level} | 评分: {alert_score}分\n"

            # 添加核心指标信息
            bid1_change = core_indicators.get('bid1_change_rate', 0)
            ask_pressure = core_indicators.get('ask_pressure_coefficient', 0)
            volume_attack = core_indicators.get('volume_attack_indicator', 0)

            message += f"买一变化: {bid1_change:+.1%}\n"
            message += f"卖压系数: {ask_pressure:.2f}\n"
            message += f"量能突击: {volume_attack:.2f}x"

            # 添加时间窗口信息
            if signals.get('time_window', False):
                time_details = signals.get('details', {}).get('time_window', {})
                window_desc = time_details.get('description', '关键时间窗口')
                message += f"\n⏰ {window_desc}"

            # 发送Windows通知
            try:
                if 'win10toast' in sys.modules:
                    toaster.show_toast(
                        title,
                        message,
                        duration=self.config['notification_duration'],
                        icon_path=None,
                        threaded=True
                    )
                else:
                    notification.notify(
                        title=title,
                        message=message,
                        timeout=self.config['notification_duration']
                    )

                print(f"🚨 发送{alert_level}通知: {code} {stock_name}")
                print(f"   📊 买一变化:{bid1_change:+.1%} | 卖压:{ask_pressure:.2f} | 量能:{volume_attack:.2f}x")

                # 记录报警历史
                alert_record = {
                    'time': datetime.now(),
                    'level': alert_level,
                    'score': alert_score,
                    'red_alert': red_alert,
                    'yellow_alert': yellow_alert,
                    'core_indicators': core_indicators.copy(),
                    'alert_details': alert_details.copy()
                }
                stock_info.setdefault('alert_history', []).append(alert_record)

                # 限制历史记录数量
                if len(stock_info['alert_history']) > 10:
                    stock_info['alert_history'].pop(0)

            except Exception as e:
                print(f"⚠️ 发送通知失败: {e}")

        except Exception as e:
            print(f"❌ 报警处理失败 {code}: {e}")

    def show_settings(self):
        """显示设置对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("监控设置")
        dialog.geometry("500x400")
        dialog.transient(self.root)
        dialog.grab_set()

        # 居中显示
        dialog.geometry("+%d+%d" % (self.root.winfo_rootx() + 100, self.root.winfo_rooty() + 100))

        # 创建设置界面
        notebook = ttk.Notebook(dialog)
        notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 监控参数页面
        monitor_frame = ttk.Frame(notebook)
        notebook.add(monitor_frame, text="监控参数")

        # 扫描间隔
        ttk.Label(monitor_frame, text="扫描间隔(秒):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=5)
        scan_interval_var = tk.StringVar(value=str(self.config['scan_interval']))
        ttk.Entry(monitor_frame, textvariable=scan_interval_var, width=10).grid(row=0, column=1, padx=5, pady=5)

        # 买一托单突增阈值
        ttk.Label(monitor_frame, text="买一托单突增倍数:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=5)
        bid_surge_var = tk.StringVar(value=str(self.config['bid_surge_threshold']))
        ttk.Entry(monitor_frame, textvariable=bid_surge_var, width=10).grid(row=1, column=1, padx=5, pady=5)

        # 卖压系数阈值
        ttk.Label(monitor_frame, text="卖压系数阈值:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=5)
        ask_pressure_var = tk.StringVar(value=str(self.config['ask_pressure_threshold']))
        ttk.Entry(monitor_frame, textvariable=ask_pressure_var, width=10).grid(row=2, column=1, padx=5, pady=5)

        # 量能突击阈值
        ttk.Label(monitor_frame, text="量能突击倍数:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=5)
        volume_pulse_var = tk.StringVar(value=str(self.config['volume_pulse_threshold']))
        ttk.Entry(monitor_frame, textvariable=volume_pulse_var, width=10).grid(row=3, column=1, padx=5, pady=5)

        # 通知持续时间
        ttk.Label(monitor_frame, text="通知持续时间(秒):").grid(row=4, column=0, sticky=tk.W, padx=5, pady=5)
        notification_var = tk.StringVar(value=str(self.config['notification_duration']))
        ttk.Entry(monitor_frame, textvariable=notification_var, width=10).grid(row=4, column=1, padx=5, pady=5)

        # 按钮
        button_frame = ttk.Frame(dialog)
        button_frame.pack(pady=10)

        def save_settings():
            try:
                self.config['scan_interval'] = int(scan_interval_var.get())
                self.config['bid_surge_threshold'] = float(bid_surge_var.get())
                self.config['ask_pressure_threshold'] = float(ask_pressure_var.get())
                self.config['volume_pulse_threshold'] = float(volume_pulse_var.get())
                self.config['notification_duration'] = int(notification_var.get())

                self.save_config()
                messagebox.showinfo("成功", "设置已保存")
                dialog.destroy()
            except ValueError:
                messagebox.showerror("错误", "请输入有效的数值")

        ttk.Button(button_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="取消", command=dialog.destroy).pack(side=tk.LEFT, padx=5)

    def clear_stocks(self):
        """清空股票列表"""
        if messagebox.askyesno("确认", "确定要清空所有监控股票吗？"):
            # 停止监控
            if self.monitoring:
                self.stop_monitoring()

            # 清空数据
            self.monitored_stocks.clear()
            self.historical_cache.clear()

            # 清空表格
            for item in self.tree.get_children():
                self.tree.delete(item)

            # 保存配置
            self.save_config()

            self.info_label.config(text="💡 请添加股票代码开始监控")
            print("🗑️ 已清空所有监控股票")

    def test_notification(self):
        """测试通知功能"""
        try:
            if not NOTIFICATION_AVAILABLE:
                messagebox.showwarning("警告", "通知功能不可用\n请安装: pip install win10toast 或 pip install plyer")
                return

            # 创建测试信号数据
            test_signals = {
                'alert_score': 85,
                'alert_details': {
                    'red_alert': True,
                    'yellow_alert': True,
                    'score_alert': True
                },
                'core_indicators': {
                    'bid1_change_rate': 6.8,  # 680%变化率
                    'ask_pressure_coefficient': 0.15,  # 卖压系数
                    'volume_attack_indicator': 3.2  # 量能突击
                },
                'time_window': True,
                'details': {
                    'time_window': {
                        'description': '午盘偷袭启动点',
                        'key_time': '13:15'
                    }
                }
            }

            # 发送测试通知
            self.send_test_alert("000001", "平安银行", test_signals)

            # 显示成功消息
            messagebox.showinfo("测试完成", "测试通知已发送！\n请查看系统右下角的通知")

        except Exception as e:
            messagebox.showerror("错误", f"测试通知失败: {e}")

    def send_test_alert(self, code, stock_name, signals):
        """发送测试报警通知"""
        try:
            alert_details = signals.get('alert_details', {})
            core_indicators = signals.get('core_indicators', {})

            # 确定警报级别
            red_alert = alert_details.get('red_alert', False)
            yellow_alert = alert_details.get('yellow_alert', False)
            alert_score = signals.get('alert_score', 0)

            # 根据警报级别设置通知样式
            if red_alert:
                alert_level = "🔴 红色警报"
                title = "🚨 潜在股监控器 - 红色警报！【测试】"
                urgency = "CRITICAL"
            elif yellow_alert:
                alert_level = "🟡 黄色警报"
                title = "⚠️ 潜在股监控器 - 黄色警报【测试】"
                urgency = "HIGH"
            else:
                alert_level = "🔵 蓝色提醒"
                title = "💡 潜在股监控器 - 信号提醒【测试】"
                urgency = "NORMAL"

            # 构建详细消息
            message = f"{code} {stock_name}\n"
            message += f"{alert_level} | 评分: {alert_score}分\n"

            # 添加核心指标信息
            bid1_change = core_indicators.get('bid1_change_rate', 0)
            ask_pressure = core_indicators.get('ask_pressure_coefficient', 0)
            volume_attack = core_indicators.get('volume_attack_indicator', 0)

            message += f"买一变化: {bid1_change:+.1%}\n"
            message += f"卖压系数: {ask_pressure:.2f}\n"
            message += f"量能突击: {volume_attack:.2f}x"

            # 添加时间窗口信息
            if signals.get('time_window', False):
                time_details = signals.get('details', {}).get('time_window', {})
                window_desc = time_details.get('description', '关键时间窗口')
                message += f"\n⏰ {window_desc}"

            message += "\n\n📝 这是一个测试通知"

            # 发送Windows通知
            try:
                if 'win10toast' in sys.modules:
                    toaster.show_toast(
                        title,
                        message,
                        duration=self.config['notification_duration'],
                        icon_path=None,
                        threaded=True
                    )
                else:
                    notification.notify(
                        title=title,
                        message=message,
                        timeout=self.config['notification_duration']
                    )

                print(f"🔔 发送测试通知: {alert_level}")
                print(f"   📊 买一变化:{bid1_change:+.1%} | 卖压:{ask_pressure:.2f} | 量能:{volume_attack:.2f}x")

            except Exception as e:
                print(f"⚠️ 发送测试通知失败: {e}")
                raise e

        except Exception as e:
            print(f"❌ 测试通知处理失败: {e}")
            raise e

    def test_data_fetch(self):
        """测试数据获取功能"""
        try:
            print("\n" + "="*50)
            print("🧪 开始测试数据获取功能")
            print("="*50)

            # 测试股票代码（使用活跃的大盘股）
            default_test_codes = ['000001', '000002', '600000', '600036', '000858']  # 平安银行、万科A、浦发银行、招商银行、五粮液

            if self.monitored_stocks:
                # 如果有监控的股票，使用第一只进行测试，但也包含默认测试股票
                user_code = list(self.monitored_stocks.keys())[0]
                test_codes = [user_code] + default_test_codes[:2]  # 用户股票+2只默认股票
                print(f"📊 使用监控股票进行测试: {user_code}")
                print(f"📊 同时测试默认股票: {default_test_codes[:2]}")
            else:
                test_codes = default_test_codes[:3]
                print(f"📊 使用默认活跃股票进行测试: {test_codes}")

            # 测试1：实时行情获取
            print("\n1️⃣ 测试实时行情获取...")
            try:
                df = enhanced_stock_fetcher.get_realtime_quote(test_codes)
                if not df.empty:
                    print(f"   ✅ 成功获取 {len(df)} 条实时行情数据")
                    print(f"   📋 数据列: {list(df.columns)}")
                    if len(df) > 0:
                        sample = df.iloc[0]
                        # 兼容不同的列名格式
                        code = sample.get('stock_code', sample.get('代码', 'N/A'))
                        price = sample.get('current_price', sample.get('最新价', 'N/A'))
                        name = sample.get('stock_name', sample.get('名称', 'N/A'))
                        print(f"   📊 样本数据: 代码={code}, 名称={name}, 最新价={price}")

                        # 显示所有非空字段
                        non_null_fields = {k: v for k, v in sample.items() if pd.notna(v) and v != '' and v != 0}
                        if non_null_fields:
                            print(f"   📋 非空字段: {list(non_null_fields.keys())}")
                        else:
                            print(f"   ⚠️ 所有字段都为空或零值（可能是非交易时间）")
                else:
                    print("   ❌ 未获取到实时行情数据")
            except Exception as e:
                print(f"   ❌ 实时行情获取失败: {e}")

            # 测试2：五档行情获取
            print("\n2️⃣ 测试五档行情获取...")
            test_code = test_codes[0]
            try:
                depth_df = enhanced_stock_fetcher.get_market_depth(test_code)
                if not depth_df.empty:
                    print(f"   ✅ 成功获取五档数据")
                    print(f"   📋 数据列: {list(depth_df.columns)}")

                    # 解析五档数据
                    depth_data = self.parse_market_depth(depth_df)
                    print(f"   📊 买一价格: {depth_data['bid_prices'][0]}")
                    print(f"   📊 买一数量: {depth_data['bid_volumes'][0]}")
                    print(f"   📊 卖一价格: {depth_data['ask_prices'][0]}")
                    print(f"   📊 卖一数量: {depth_data['ask_volumes'][0]}")
                else:
                    print("   ❌ 未获取到五档数据")
            except Exception as e:
                print(f"   ❌ 五档数据获取失败: {e}")

            # 测试3：数据源可用性
            print("\n3️⃣ 测试数据源可用性...")
            try:
                import akshare as ak
                akshare_available = True
            except ImportError:
                akshare_available = False

            print(f"   AKShare可用: {'✅' if akshare_available else '❌'}")
            print(f"   数据获取器可用: {'✅' if enhanced_stock_fetcher else '❌'}")
            print(f"   数据获取器类型: {type(enhanced_stock_fetcher).__name__}")

            # 测试4：网络连接
            print("\n4️⃣ 测试网络连接...")
            try:
                import requests
                response = requests.get("http://hq.sinajs.cn/list=sh000001", timeout=5)
                if response.status_code == 200:
                    print("   ✅ 新浪财经API连接正常")
                else:
                    print(f"   ❌ 新浪财经API连接异常: {response.status_code}")
            except Exception as e:
                print(f"   ❌ 网络连接测试失败: {e}")

            print("\n" + "="*50)
            print("🧪 数据获取测试完成")
            print("="*50)

            # 显示测试结果
            messagebox.showinfo("测试完成", "数据获取测试已完成！\n请查看控制台输出了解详细结果")

        except Exception as e:
            error_msg = f"数据获取测试失败: {e}"
            print(f"❌ {error_msg}")
            messagebox.showerror("测试失败", error_msg)

    def save_config(self):
        """保存配置到文件"""
        try:
            config_data = {
                'config': self.config,
                'monitored_stocks': {
                    code: {
                        'name': info['name'],
                        # 不保存实时数据，只保存基本信息
                    }
                    for code, info in self.monitored_stocks.items()
                }
            }

            with open('潜在股监控器_配置.json', 'w', encoding='utf-8') as f:
                json.dump(config_data, f, ensure_ascii=False, indent=2)

            print("✅ 配置已保存")

        except Exception as e:
            print(f"⚠️ 保存配置失败: {e}")

    def load_config(self):
        """从文件加载配置"""
        try:
            if os.path.exists('潜在股监控器_配置.json'):
                with open('潜在股监控器_配置.json', 'r', encoding='utf-8') as f:
                    config_data = json.load(f)

                # 加载配置参数
                if 'config' in config_data:
                    self.config.update(config_data['config'])

                # 加载监控股票列表
                if 'monitored_stocks' in config_data:
                    for code, info in config_data['monitored_stocks'].items():
                        # 重新获取股票名称（如果配置中的名称是"未知"或以"股票"开头）
                        saved_name = info.get('name', '未知')
                        if saved_name == '未知' or saved_name.startswith('股票'):
                            print(f"   🔄 重新获取股票名称: {code}")
                            try:
                                new_stock_info = enhanced_stock_fetcher.get_stock_basic_info(code)
                                stock_name = new_stock_info.get('name', saved_name)
                            except:
                                stock_name = saved_name
                        else:
                            stock_name = saved_name

                        self.monitored_stocks[code] = {
                            'name': stock_name,
                            'data': {},
                            'signals': {},
                            'last_update': None,
                            'alert_history': []
                        }

                        # 添加到表格
                        self.tree.insert('', 'end', iid=code, values=(
                            code, stock_name, '-', '-', '-', '-', '-', '等待数据', '-'
                        ))

                        print(f"   📊 加载股票: {code} {stock_name}")

                print(f"✅ 配置加载成功，监控股票: {len(self.monitored_stocks)} 只")

                if self.monitored_stocks:
                    self.info_label.config(text=f"💡 已加载 {len(self.monitored_stocks)} 只监控股票")

        except Exception as e:
            print(f"⚠️ 加载配置失败: {e}")

    def on_closing(self):
        """程序关闭时的处理"""
        try:
            # 停止监控
            if self.monitoring:
                self.stop_monitoring()

            # 保存配置
            self.save_config()

            # 关闭程序
            self.root.destroy()

        except Exception as e:
            print(f"⚠️ 程序关闭处理失败: {e}")
            self.root.destroy()

    def run(self):
        """运行应用"""
        # 设置关闭事件处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)

        # 显示启动信息
        print("🚀 潜在股监控器启动成功")
        print("📊 6大黄金启动信号实时监控系统")
        print("=" * 50)

        if not enhanced_stock_fetcher:
            messagebox.showwarning("警告", "数据获取器不可用，部分功能可能受限")

        if not NOTIFICATION_AVAILABLE:
            messagebox.showwarning("警告", "通知功能不可用，将无法发送报警通知")

        # 启动GUI主循环
        self.root.mainloop()


def main():
    """主函数"""
    try:
        print("🚀 启动潜在股监控器...")

        # 检查依赖
        missing_deps = []

        if enhanced_stock_fetcher is None:
            missing_deps.append("enhanced_data_fetcher")

        if not NOTIFICATION_AVAILABLE:
            print("⚠️ 通知功能不可用，建议安装: pip install win10toast 或 pip install plyer")

        if missing_deps:
            print(f"⚠️ 缺少依赖模块: {', '.join(missing_deps)}")
            print("⚠️ 部分功能可能受限")

        # 创建并运行应用
        app = PotentialStockMonitor()
        app.run()

    except Exception as e:
        print(f"❌ 程序启动失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
