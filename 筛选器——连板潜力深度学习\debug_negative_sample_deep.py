#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
深度调试负样本创建问题
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os
import sqlite3

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from training.training_pipeline import TrainingPipeline
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_negative_sample_creation_deep():
    """深度调试负样本创建"""
    print("🔍 深度调试负样本创建...")
    
    # 创建训练流程
    pipeline = TrainingPipeline()
    storage = HistoricalDataStorage()
    
    # 从训练日志中选择一个失败的股票
    test_stock = "603880"
    test_date = "2025-01-29"
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 测试日期: {test_date}")
    
    try:
        # 1. 检查数据库状态
        print("\n1️⃣ 检查数据库状态...")
        conn = sqlite3.connect('historical_data.db')
        
        # 检查K线数据
        kline_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
        print(f"   {test_stock}的K线数据: {kline_count}条")
        
        # 检查特征数据
        feature_count = conn.execute('SELECT COUNT(*) FROM stock_features WHERE stock_code = ?', (test_stock,)).fetchone()[0]
        print(f"   {test_stock}的特征数据: {feature_count}条")
        
        if feature_count > 0:
            label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features WHERE stock_code = ? GROUP BY label', conn, params=[test_stock])
            print(f"   标签分布:\n{label_dist}")
        
        conn.close()
        
        # 2. 模拟训练流程中的K线数据查询
        print("\n2️⃣ 模拟训练流程中的K线数据查询...")
        limit_up_datetime = datetime.strptime(test_date, '%Y-%m-%d')
        start_date_for_query = (limit_up_datetime - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date_for_query = test_date
        
        print(f"   查询时间范围: {start_date_for_query} 到 {end_date_for_query}")
        
        kline_data = storage.get_kline_data(
            test_stock,
            start_date=start_date_for_query,
            end_date=end_date_for_query
        )
        
        if not kline_data.empty:
            print(f"✅ 查询K线数据成功: {len(kline_data)}条")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   数据类型:\n{kline_data.dtypes}")
            print(f"   样本数据:\n{kline_data.head(3)}")
            
            # 3. 测试负样本创建的每个步骤
            print("\n3️⃣ 测试负样本创建的每个步骤...")
            
            # 模拟基础特征
            base_features = {
                'ma5': 10.5,
                'ma10': 10.3,
                'ma20': 10.1,
                'rsi': 65.2,
                'volume_ratio': 1.5,
                'test_feature': 1.0
            }
            
            limit_dates = [test_date]
            
            print(f"   基础特征: {len(base_features)}个")
            print(f"   涨停日期: {limit_dates}")
            
            # 手动执行负样本创建的每个步骤
            print("\n   🔍 手动执行负样本创建步骤...")
            
            # 步骤1: 检查数据列名
            date_column = None
            if 'date' in kline_data.columns:
                date_column = 'date'
                print(f"   ✅ 找到date列")
            elif 'trade_date' in kline_data.columns:
                date_column = 'trade_date'
                print(f"   ✅ 找到trade_date列")
            else:
                print(f"   ❌ 没有找到日期列，可用列: {list(kline_data.columns)}")
                return False
            
            # 步骤2: 获取所有日期
            print(f"   🔍 处理{date_column}列...")
            if pd.api.types.is_datetime64_any_dtype(kline_data[date_column]):
                print(f"   ✅ {date_column}是datetime类型")
                all_dates = kline_data[date_column].dt.strftime('%Y-%m-%d').tolist()
            else:
                print(f"   ✅ {date_column}是字符串类型")
                all_dates = kline_data[date_column].astype(str).tolist()
                all_dates = [date[:10] if len(date) > 10 else date for date in all_dates]
            
            print(f"   ✅ 获取所有日期: {len(all_dates)}个")
            print(f"   样本日期: {all_dates[:5]}")
            
            # 步骤3: 筛选负样本日期
            limit_dates_set = set(limit_dates)
            negative_dates = [date for date in all_dates if date not in limit_dates_set]
            
            print(f"   ✅ 筛选负样本日期: {len(negative_dates)}个")
            print(f"   负样本日期: {negative_dates[:5]}")
            
            if not negative_dates:
                print("   ❌ 没有可用的负样本日期")
                return False
            
            # 步骤4: 选择负样本
            max_negative_samples = min(len(negative_dates), len(limit_dates) * 3)
            print(f"   ✅ 计算最大负样本数: {max_negative_samples}")
            
            if max_negative_samples == 0:
                print("   ❌ 最大负样本数为0")
                return False
            
            selected_negative_dates = np.random.choice(negative_dates, max_negative_samples, replace=False)
            print(f"   ✅ 选择负样本日期: {len(selected_negative_dates)}个")
            print(f"   选择的日期: {list(selected_negative_dates)}")
            
            # 步骤5: 存储负样本
            print(f"   🔍 存储负样本...")
            stored_count = 0
            for neg_date in selected_negative_dates:
                try:
                    success = storage.store_stock_features(test_stock, neg_date, base_features, label=0)
                    if success:
                        stored_count += 1
                        print(f"   ✅ 存储负样本 {neg_date} 成功")
                    else:
                        print(f"   ❌ 存储负样本 {neg_date} 失败")
                except Exception as e:
                    print(f"   ❌ 存储负样本 {neg_date} 异常: {e}")
            
            print(f"   ✅ 成功存储负样本: {stored_count}/{len(selected_negative_dates)}")
            
            # 4. 验证存储结果
            print("\n4️⃣ 验证存储结果...")
            conn = sqlite3.connect('historical_data.db')
            
            new_feature_count = conn.execute('SELECT COUNT(*) FROM stock_features WHERE stock_code = ?', (test_stock,)).fetchone()[0]
            print(f"   {test_stock}现在的特征数据: {new_feature_count}条")
            print(f"   新增特征数据: {new_feature_count - feature_count}条")
            
            if new_feature_count > feature_count:
                new_label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features WHERE stock_code = ? GROUP BY label', conn, params=[test_stock])
                print(f"   新的标签分布:\n{new_label_dist}")
                
                print("🎉 负样本创建成功！")
                return True
            else:
                print("❌ 负样本创建失败，没有新增数据")
                return False
            
            conn.close()
        else:
            print("❌ 查询K线数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_training_pipeline_negative_samples():
    """测试训练流程中的负样本创建"""
    print("\n🧪 测试训练流程中的负样本创建...")
    
    pipeline = TrainingPipeline()
    
    # 模拟训练流程中的负样本创建调用
    test_stock = "603880"
    test_date = "2025-01-29"
    
    try:
        # 获取K线数据
        limit_up_datetime = datetime.strptime(test_date, '%Y-%m-%d')
        start_date_for_query = (limit_up_datetime - timedelta(days=30)).strftime('%Y-%m-%d')
        end_date_for_query = test_date
        
        kline_data = pipeline.data_storage.get_kline_data(
            test_stock,
            start_date=start_date_for_query,
            end_date=end_date_for_query
        )
        
        if not kline_data.empty:
            # 模拟基础特征
            base_features = {
                'ma5': 10.5,
                'ma10': 10.3,
                'ma20': 10.1,
                'rsi': 65.2,
                'volume_ratio': 1.5
            }
            
            # 直接调用训练流程中的负样本创建方法
            print(f"   🔍 调用训练流程的负样本创建方法...")
            pipeline._create_negative_samples(
                test_stock,
                kline_data,
                [test_date],
                base_features
            )
            
            print(f"   ✅ 训练流程负样本创建调用完成")
            return True
        else:
            print(f"   ❌ 获取K线数据失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 训练流程负样本创建失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 深度调试负样本创建问题...")
    print("="*60)
    
    # 深度调试负样本创建
    deep_result = debug_negative_sample_creation_deep()
    
    # 测试训练流程中的负样本创建
    pipeline_result = test_training_pipeline_negative_samples()
    
    print("\n" + "="*60)
    print("📋 调试结论:")
    
    if deep_result:
        print("✅ 手动负样本创建成功")
    else:
        print("❌ 手动负样本创建失败")
    
    if pipeline_result:
        print("✅ 训练流程负样本创建成功")
    else:
        print("❌ 训练流程负样本创建失败")
    
    if deep_result and pipeline_result:
        print("🎉 负样本创建问题已解决！")
        print("💡 建议: 重新运行完整训练")
    else:
        print("❌ 负样本创建仍有问题，需要进一步调试")
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
