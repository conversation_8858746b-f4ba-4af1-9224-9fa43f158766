#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户界面与集成
将连板潜力股预测功能集成到现有的股票监控器主应用中，提供友好的用户交互界面
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import os
import sys
import threading
import json
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from algorithms.multi_layer_filter import MultiLayerFilter
from monitoring.real_time_monitor import RealTimeMonitor
from training.training_pipeline import TrainingPipeline
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class LimitUpPredictorGUI:
    """连板潜力股预测器图形界面"""
    
    def __init__(self, root):
        self.root = root
        self.root.title("连板潜力股深度学习器 v1.0")
        self.root.geometry("1200x800")
        
        # 初始化组件
        self.filter_engine = MultiLayerFilter()
        self.monitor = RealTimeMonitor()
        self.training_pipeline = TrainingPipeline()
        self.data_storage = HistoricalDataStorage()
        
        # 界面状态
        self.monitoring_active = False
        self.last_scan_results = None
        
        # 创建界面
        self.create_widgets()
        
        logger.info("🖥️ 连板潜力股预测器GUI初始化完成")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="连板潜力股深度学习器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # 左侧控制面板
        self.create_control_panel(main_frame)
        
        # 右侧结果显示区域
        self.create_results_panel(main_frame)
        
        # 底部状态栏
        self.create_status_bar(main_frame)
    
    def create_control_panel(self, parent):
        """创建控制面板"""
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # 实时监控控制
        monitor_frame = ttk.LabelFrame(control_frame, text="实时监控", padding="5")
        monitor_frame.pack(fill=tk.X, pady=(0, 10))
        
        self.monitor_button = ttk.Button(monitor_frame, text="开始监控", 
                                        command=self.toggle_monitoring)
        self.monitor_button.pack(side=tk.LEFT, padx=(0, 5))
        
        self.monitor_status_label = ttk.Label(monitor_frame, text="状态: 未运行")
        self.monitor_status_label.pack(side=tk.LEFT)
        
        # 手动扫描
        scan_frame = ttk.LabelFrame(control_frame, text="手动扫描", padding="5")
        scan_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(scan_frame, text="执行扫描", 
                  command=self.perform_manual_scan).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(scan_frame, text="查看历史", 
                  command=self.view_scan_history).pack(side=tk.LEFT)
        
        # 模型训练
        training_frame = ttk.LabelFrame(control_frame, text="模型训练", padding="5")
        training_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(training_frame, text="快速训练", 
                  command=self.quick_training).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(training_frame, text="完整训练", 
                  command=self.full_training).pack(side=tk.LEFT)
        
        # 数据管理
        data_frame = ttk.LabelFrame(control_frame, text="数据管理", padding="5")
        data_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Button(data_frame, text="数据统计", 
                  command=self.show_data_statistics).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(data_frame, text="导出报告", 
                  command=self.export_report).pack(side=tk.LEFT)
        
        # 系统设置
        settings_frame = ttk.LabelFrame(control_frame, text="系统设置", padding="5")
        settings_frame.pack(fill=tk.X)
        
        ttk.Button(settings_frame, text="监控设置", 
                  command=self.open_settings).pack(side=tk.LEFT, padx=(0, 5))
        
        ttk.Button(settings_frame, text="关于系统", 
                  command=self.show_about).pack(side=tk.LEFT)
    
    def create_results_panel(self, parent):
        """创建结果显示面板"""
        results_frame = ttk.LabelFrame(parent, text="扫描结果", padding="10")
        results_frame.grid(row=1, column=1, columnspan=2, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 创建Notebook用于多标签页
        self.notebook = ttk.Notebook(results_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # 预警股票标签页
        self.create_alerts_tab()
        
        # 扫描统计标签页
        self.create_statistics_tab()
        
        # 系统日志标签页
        self.create_logs_tab()
    
    def create_alerts_tab(self):
        """创建预警股票标签页"""
        alerts_frame = ttk.Frame(self.notebook)
        self.notebook.add(alerts_frame, text="预警股票")
        
        # 创建表格
        columns = ("股票代码", "股票名称", "预警时间", "综合评分", "ML概率", "置信度", "推荐操作")
        self.alerts_tree = ttk.Treeview(alerts_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题
        for col in columns:
            self.alerts_tree.heading(col, text=col)
            self.alerts_tree.column(col, width=100)
        
        # 添加滚动条
        alerts_scrollbar = ttk.Scrollbar(alerts_frame, orient=tk.VERTICAL, command=self.alerts_tree.yview)
        self.alerts_tree.configure(yscrollcommand=alerts_scrollbar.set)
        
        # 布局
        self.alerts_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        alerts_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 绑定双击事件
        self.alerts_tree.bind("<Double-1>", self.on_alert_double_click)
    
    def create_statistics_tab(self):
        """创建扫描统计标签页"""
        stats_frame = ttk.Frame(self.notebook)
        self.notebook.add(stats_frame, text="扫描统计")
        
        # 统计信息文本框
        self.stats_text = tk.Text(stats_frame, wrap=tk.WORD, height=20)
        stats_scrollbar = ttk.Scrollbar(stats_frame, orient=tk.VERTICAL, command=self.stats_text.yview)
        self.stats_text.configure(yscrollcommand=stats_scrollbar.set)
        
        self.stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def create_logs_tab(self):
        """创建系统日志标签页"""
        logs_frame = ttk.Frame(self.notebook)
        self.notebook.add(logs_frame, text="系统日志")
        
        # 日志文本框
        self.logs_text = tk.Text(logs_frame, wrap=tk.WORD, height=20)
        logs_scrollbar = ttk.Scrollbar(logs_frame, orient=tk.VERTICAL, command=self.logs_text.yview)
        self.logs_text.configure(yscrollcommand=logs_scrollbar.set)
        
        self.logs_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        logs_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 添加初始日志
        self.add_log("系统启动完成")
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.pack(side=tk.LEFT)
        
        # 进度条
        self.progress = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress.pack(side=tk.RIGHT, padx=(10, 0))
    
    def toggle_monitoring(self):
        """切换监控状态"""
        try:
            if not self.monitoring_active:
                # 开始监控
                success = self.monitor.start_monitoring()
                if success:
                    self.monitoring_active = True
                    self.monitor_button.config(text="停止监控")
                    self.monitor_status_label.config(text="状态: 运行中")
                    self.add_log("实时监控已启动")
                    
                    # 启动监控结果更新线程
                    self.start_monitoring_update_thread()
                else:
                    messagebox.showerror("错误", "启动监控失败")
            else:
                # 停止监控
                success = self.monitor.stop_monitoring()
                if success:
                    self.monitoring_active = False
                    self.monitor_button.config(text="开始监控")
                    self.monitor_status_label.config(text="状态: 未运行")
                    self.add_log("实时监控已停止")
                else:
                    messagebox.showerror("错误", "停止监控失败")
                    
        except Exception as e:
            messagebox.showerror("错误", f"监控操作失败: {e}")
            self.add_log(f"监控操作失败: {e}")
    
    def start_monitoring_update_thread(self):
        """启动监控结果更新线程"""
        def update_monitoring_results():
            while self.monitoring_active:
                try:
                    # 获取最新预警
                    recent_alerts = self.monitor.get_recent_alerts(1)  # 最近1小时
                    
                    if recent_alerts:
                        # 更新预警表格
                        self.root.after(0, self.update_alerts_display, recent_alerts)
                    
                    # 等待30秒再次检查
                    import time
                    time.sleep(30)
                    
                except Exception as e:
                    self.add_log(f"监控更新错误: {e}")
                    break
        
        thread = threading.Thread(target=update_monitoring_results, daemon=True)
        thread.start()
    
    def perform_manual_scan(self):
        """执行手动扫描"""
        try:
            self.add_log("开始手动扫描...")
            self.progress.start()
            self.status_label.config(text="扫描中...")
            
            def scan_thread():
                try:
                    # 执行扫描
                    results = self.filter_engine.run_multi_layer_filtering()
                    
                    # 更新界面
                    self.root.after(0, self.on_scan_complete, results)
                    
                except Exception as e:
                    self.root.after(0, self.on_scan_error, str(e))
            
            thread = threading.Thread(target=scan_thread, daemon=True)
            thread.start()
            
        except Exception as e:
            messagebox.showerror("错误", f"启动扫描失败: {e}")
            self.add_log(f"启动扫描失败: {e}")
    
    def on_scan_complete(self, results):
        """扫描完成回调"""
        try:
            self.progress.stop()
            self.status_label.config(text="就绪")
            
            if results.get('success', False):
                final_stocks = results.get('final_stocks', [])
                self.add_log(f"扫描完成，发现{len(final_stocks)}只潜力股")
                
                # 更新结果显示
                self.update_scan_results(results)
                
                if final_stocks:
                    messagebox.showinfo("扫描完成", f"发现{len(final_stocks)}只潜力股")
                else:
                    messagebox.showinfo("扫描完成", "未发现符合条件的潜力股")
            else:
                error_msg = results.get('error', '未知错误')
                messagebox.showerror("扫描失败", f"扫描失败: {error_msg}")
                self.add_log(f"扫描失败: {error_msg}")
                
        except Exception as e:
            messagebox.showerror("错误", f"处理扫描结果失败: {e}")
            self.add_log(f"处理扫描结果失败: {e}")
    
    def on_scan_error(self, error_msg):
        """扫描错误回调"""
        self.progress.stop()
        self.status_label.config(text="就绪")
        messagebox.showerror("扫描错误", f"扫描过程中发生错误: {error_msg}")
        self.add_log(f"扫描错误: {error_msg}")
    
    def update_scan_results(self, results):
        """更新扫描结果显示"""
        try:
            # 清空现有结果
            for item in self.alerts_tree.get_children():
                self.alerts_tree.delete(item)
            
            # 添加新结果
            final_stocks = results.get('final_stocks', [])
            for stock in final_stocks:
                values = (
                    stock.get('stock_code', ''),
                    stock.get('stock_name', ''),
                    datetime.now().strftime('%H:%M:%S'),
                    f"{stock.get('final_score', 0):.3f}",
                    f"{stock.get('ml_probability', 0):.3f}",
                    stock.get('confidence_level', 'UNKNOWN'),
                    stock.get('recommended_action', '关注观察')
                )
                self.alerts_tree.insert('', tk.END, values=values)
            
            # 更新统计信息
            self.update_statistics_display(results)
            
        except Exception as e:
            self.add_log(f"更新扫描结果失败: {e}")
    
    def update_alerts_display(self, alerts):
        """更新预警显示"""
        try:
            for alert in alerts:
                values = (
                    alert.get('stock_code', ''),
                    alert.get('stock_name', ''),
                    alert.get('alert_time', ''),
                    f"{alert.get('alert_score', 0):.3f}",
                    f"{alert.get('ml_probability', 0):.3f}",
                    alert.get('confidence_level', ''),
                    alert.get('recommended_action', '')
                )
                self.alerts_tree.insert('', 0, values=values)  # 插入到顶部
                
                # 限制显示数量
                children = self.alerts_tree.get_children()
                if len(children) > 100:
                    self.alerts_tree.delete(children[-1])
                    
        except Exception as e:
            self.add_log(f"更新预警显示失败: {e}")
    
    def update_statistics_display(self, results):
        """更新统计信息显示"""
        try:
            self.stats_text.delete(1.0, tk.END)
            
            stats_text = f"""扫描统计信息
{'='*50}

扫描时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
初始股票数: {results.get('initial_count', 0)}
最终股票数: {results.get('final_count', 0)}

各层筛选结果:
"""
            
            layers = results.get('layers', {})
            for layer_name, layer_data in layers.items():
                if layer_data.get('success', False):
                    input_count = layer_data.get('input_count', 0)
                    output_count = layer_data.get('output_count', 0)
                    filter_rate = layer_data.get('filter_rate', 0)
                    stats_text += f"  {layer_name}: {input_count} → {output_count} (筛选率: {filter_rate:.1%})\n"
            
            # 添加筛选总结
            summary = results.get('filtering_summary', {})
            if summary:
                stats_text += f"\n筛选总结:\n"
                stats_text += f"  总筛选率: {summary.get('total_reduction_rate', 0):.1%}\n"
                
                final_stocks_info = summary.get('final_stocks_info', [])
                if final_stocks_info:
                    stats_text += f"\n最终筛选股票:\n"
                    for i, stock in enumerate(final_stocks_info, 1):
                        stats_text += f"  {i}. {stock.get('stock_code', '')} {stock.get('stock_name', '')}\n"
                        stats_text += f"     最终评分: {stock.get('final_score', 0):.3f}\n"
                        stats_text += f"     ML概率: {stock.get('ml_probability', 0):.3f}\n"
            
            self.stats_text.insert(1.0, stats_text)
            
        except Exception as e:
            self.add_log(f"更新统计信息失败: {e}")
    
    def add_log(self, message):
        """添加日志"""
        try:
            timestamp = datetime.now().strftime('%H:%M:%S')
            log_message = f"[{timestamp}] {message}\n"
            
            self.logs_text.insert(tk.END, log_message)
            self.logs_text.see(tk.END)
            
            # 限制日志行数
            lines = self.logs_text.get(1.0, tk.END).split('\n')
            if len(lines) > 1000:
                self.logs_text.delete(1.0, f"{len(lines)-500}.0")
                
        except Exception as e:
            print(f"添加日志失败: {e}")

    def on_alert_double_click(self, event):
        """预警双击事件"""
        try:
            selection = self.alerts_tree.selection()
            if selection:
                item = self.alerts_tree.item(selection[0])
                values = item['values']

                if values:
                    stock_code = values[0]
                    stock_name = values[1]

                    # 显示股票详细信息
                    self.show_stock_details(stock_code, stock_name)

        except Exception as e:
            self.add_log(f"处理预警双击事件失败: {e}")

    def show_stock_details(self, stock_code, stock_name):
        """显示股票详细信息"""
        try:
            # 创建详细信息窗口
            detail_window = tk.Toplevel(self.root)
            detail_window.title(f"股票详情 - {stock_code} {stock_name}")
            detail_window.geometry("600x400")

            # 详细信息文本框
            detail_text = tk.Text(detail_window, wrap=tk.WORD)
            detail_scrollbar = ttk.Scrollbar(detail_window, orient=tk.VERTICAL, command=detail_text.yview)
            detail_text.configure(yscrollcommand=detail_scrollbar.set)

            detail_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            detail_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 获取股票详细信息
            detail_info = f"""股票详细信息
{'='*50}

股票代码: {stock_code}
股票名称: {stock_name}
查询时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

基础信息:
  市值: 待获取
  价格: 待获取
  换手率: 待获取

技术指标:
  MA5>MA10: 待分析
  MACD金叉: 待分析
  RSI区间: 待分析

资金流向:
  主力净流入: 待分析
  大单净流入: 待分析

预警原因:
  综合评分达到预警阈值

推荐操作:
  密切关注，等待确认信号

注意事项:
  本预警仅供参考，投资有风险，决策需谨慎
"""

            detail_text.insert(1.0, detail_info)
            detail_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"显示股票详情失败: {e}")

    def view_scan_history(self):
        """查看扫描历史"""
        try:
            # 创建历史窗口
            history_window = tk.Toplevel(self.root)
            history_window.title("扫描历史")
            history_window.geometry("800x600")

            # 历史列表
            columns = ("扫描时间", "扫描股票数", "预警数量", "状态")
            history_tree = ttk.Treeview(history_window, columns=columns, show="headings")

            for col in columns:
                history_tree.heading(col, text=col)
                history_tree.column(col, width=150)

            history_scrollbar = ttk.Scrollbar(history_window, orient=tk.VERTICAL, command=history_tree.yview)
            history_tree.configure(yscrollcommand=history_scrollbar.set)

            history_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 模拟历史数据
            for i in range(10):
                scan_time = (datetime.now() - timedelta(hours=i)).strftime('%Y-%m-%d %H:%M:%S')
                values = (scan_time, "100", str(np.random.randint(0, 5)), "完成")
                history_tree.insert('', tk.END, values=values)

        except Exception as e:
            messagebox.showerror("错误", f"查看扫描历史失败: {e}")

    def quick_training(self):
        """快速训练"""
        try:
            self.add_log("开始快速训练...")
            self.progress.start()
            self.status_label.config(text="训练中...")

            def training_thread():
                try:
                    # 执行快速训练
                    results = self.training_pipeline.run_quick_training_demo()

                    # 更新界面
                    self.root.after(0, self.on_training_complete, results, "快速训练")

                except Exception as e:
                    self.root.after(0, self.on_training_error, str(e))

            thread = threading.Thread(target=training_thread, daemon=True)
            thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动快速训练失败: {e}")

    def full_training(self):
        """完整训练"""
        try:
            response = messagebox.askyesno("确认", "完整训练可能需要较长时间，是否继续？")
            if not response:
                return

            self.add_log("开始完整训练...")
            self.progress.start()
            self.status_label.config(text="完整训练中...")

            def training_thread():
                try:
                    # 执行完整训练
                    results = self.training_pipeline.run_full_training_pipeline()

                    # 更新界面
                    self.root.after(0, self.on_training_complete, results, "完整训练")

                except Exception as e:
                    self.root.after(0, self.on_training_error, str(e))

            thread = threading.Thread(target=training_thread, daemon=True)
            thread.start()

        except Exception as e:
            messagebox.showerror("错误", f"启动完整训练失败: {e}")

    def on_training_complete(self, results, training_type):
        """训练完成回调"""
        try:
            self.progress.stop()
            self.status_label.config(text="就绪")

            if results.get('demo_success', False) or results.get('overall_success', False):
                self.add_log(f"{training_type}完成")

                # 显示训练结果
                result_msg = f"{training_type}完成！\n\n"

                if 'created_samples' in results:
                    result_msg += f"创建样本数: {results['created_samples']}\n"
                if 'training_samples' in results:
                    result_msg += f"训练样本数: {results['training_samples']}\n"
                if 'feature_count' in results:
                    result_msg += f"特征数量: {results['feature_count']}\n"

                messagebox.showinfo("训练完成", result_msg)
            else:
                error_msg = results.get('error', '未知错误')
                messagebox.showerror("训练失败", f"{training_type}失败: {error_msg}")
                self.add_log(f"{training_type}失败: {error_msg}")

        except Exception as e:
            messagebox.showerror("错误", f"处理训练结果失败: {e}")

    def on_training_error(self, error_msg):
        """训练错误回调"""
        self.progress.stop()
        self.status_label.config(text="就绪")
        messagebox.showerror("训练错误", f"训练过程中发生错误: {error_msg}")
        self.add_log(f"训练错误: {error_msg}")

    def show_data_statistics(self):
        """显示数据统计"""
        try:
            # 获取数据库统计
            stats = self.data_storage.get_database_statistics()

            # 创建统计窗口
            stats_window = tk.Toplevel(self.root)
            stats_window.title("数据统计")
            stats_window.geometry("500x400")

            # 统计信息文本框
            stats_text = tk.Text(stats_window, wrap=tk.WORD)
            stats_scrollbar = ttk.Scrollbar(stats_window, orient=tk.VERTICAL, command=stats_text.yview)
            stats_text.configure(yscrollcommand=stats_scrollbar.set)

            stats_text.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            stats_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

            # 格式化统计信息
            stats_info = f"""数据库统计信息
{'='*50}

涨停股票数据:
  总记录数: {stats.get('limit_up_stocks_count', 0)}
  唯一股票数: {stats.get('unique_limit_up_stocks', 0)}
  日期范围: {stats.get('limit_up_date_range', 'N/A')}

K线数据:
  总记录数: {stats.get('kline_records_count', 0)}
  股票数量: {stats.get('kline_stocks_count', 0)}

分时数据:
  总记录数: {stats.get('minute_records_count', 0)}

资金流向数据:
  总记录数: {stats.get('capital_flow_records_count', 0)}

特征数据:
  总记录数: {stats.get('features_records_count', 0)}
  已标注数: {stats.get('labeled_features_count', 0)}

统计时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

            stats_text.insert(1.0, stats_info)
            stats_text.config(state=tk.DISABLED)

        except Exception as e:
            messagebox.showerror("错误", f"获取数据统计失败: {e}")

    def export_report(self):
        """导出报告"""
        try:
            # 选择保存路径
            filename = filedialog.asksaveasfilename(
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")],
                title="保存监控报告"
            )

            if filename:
                # 导出监控报告
                report_file = self.monitor.export_monitoring_report(filename)

                if report_file:
                    messagebox.showinfo("导出成功", f"报告已保存到: {report_file}")
                    self.add_log(f"报告已导出: {report_file}")
                else:
                    messagebox.showerror("导出失败", "导出报告失败")

        except Exception as e:
            messagebox.showerror("错误", f"导出报告失败: {e}")

    def open_settings(self):
        """打开设置窗口"""
        try:
            # 创建设置窗口
            settings_window = tk.Toplevel(self.root)
            settings_window.title("监控设置")
            settings_window.geometry("400x300")

            # 设置框架
            settings_frame = ttk.LabelFrame(settings_window, text="监控参数", padding="10")
            settings_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 扫描间隔设置
            ttk.Label(settings_frame, text="扫描间隔(秒):").grid(row=0, column=0, sticky=tk.W, pady=5)
            scan_interval_var = tk.StringVar(value="300")
            ttk.Entry(settings_frame, textvariable=scan_interval_var, width=10).grid(row=0, column=1, sticky=tk.W, padx=10)

            # 预警阈值设置
            ttk.Label(settings_frame, text="预警阈值:").grid(row=1, column=0, sticky=tk.W, pady=5)
            alert_threshold_var = tk.StringVar(value="0.7")
            ttk.Entry(settings_frame, textvariable=alert_threshold_var, width=10).grid(row=1, column=1, sticky=tk.W, padx=10)

            # 监控股票数设置
            ttk.Label(settings_frame, text="监控股票数:").grid(row=2, column=0, sticky=tk.W, pady=5)
            stock_count_var = tk.StringVar(value="100")
            ttk.Entry(settings_frame, textvariable=stock_count_var, width=10).grid(row=2, column=1, sticky=tk.W, padx=10)

            # 声音预警
            sound_alert_var = tk.BooleanVar(value=False)
            ttk.Checkbutton(settings_frame, text="启用声音预警", variable=sound_alert_var).grid(row=3, column=0, columnspan=2, sticky=tk.W, pady=5)

            # 邮件预警
            email_alert_var = tk.BooleanVar(value=False)
            ttk.Checkbutton(settings_frame, text="启用邮件预警", variable=email_alert_var).grid(row=4, column=0, columnspan=2, sticky=tk.W, pady=5)

            # 按钮框架
            button_frame = ttk.Frame(settings_window)
            button_frame.pack(pady=10)

            def save_settings():
                try:
                    # 这里可以保存设置到配置文件
                    messagebox.showinfo("保存成功", "设置已保存")
                    settings_window.destroy()
                except Exception as e:
                    messagebox.showerror("错误", f"保存设置失败: {e}")

            ttk.Button(button_frame, text="保存", command=save_settings).pack(side=tk.LEFT, padx=5)
            ttk.Button(button_frame, text="取消", command=settings_window.destroy).pack(side=tk.LEFT, padx=5)

        except Exception as e:
            messagebox.showerror("错误", f"打开设置失败: {e}")

    def show_about(self):
        """显示关于信息"""
        about_text = """连板潜力股深度学习器 v1.0

基于280+个特征的机器学习连板预测系统

核心功能:
• 多层筛选算法 (5500→2-3只股票)
• 实时监控和预警
• 机器学习模型训练
• 历史数据分析

技术特色:
• 6个维度特征工程
• 多模型集成预测
• 智能预警系统
• 完整数据存储

开发团队: Augment Agent
版本: 1.0
发布日期: 2025-07-13

免责声明:
本系统仅供学习和研究使用，
投资有风险，决策需谨慎！
"""
        messagebox.showinfo("关于系统", about_text)

    def on_closing(self):
        """关闭程序时的清理工作"""
        try:
            if self.monitoring_active:
                self.monitor.stop_monitoring()

            self.monitor.close()
            self.data_storage.close()

            self.root.destroy()

        except Exception as e:
            print(f"关闭程序时发生错误: {e}")
            self.root.destroy()

def main():
    """主函数"""
    try:
        # 创建主窗口
        root = tk.Tk()

        # 创建应用
        app = LimitUpPredictorGUI(root)

        # 设置关闭事件
        root.protocol("WM_DELETE_WINDOW", app.on_closing)

        # 启动主循环
        root.mainloop()

    except Exception as e:
        print(f"启动应用失败: {e}")
        messagebox.showerror("启动错误", f"启动应用失败: {e}")

if __name__ == "__main__":
    main()
