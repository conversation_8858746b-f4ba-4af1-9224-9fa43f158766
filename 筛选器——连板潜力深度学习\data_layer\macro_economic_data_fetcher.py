#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宏观经济数据获取器
集成GDP、CPI、利率、汇率等宏观经济数据，分析对股市整体情绪的影响
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional
import akshare as ak
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MacroEconomicDataFetcher:
    """宏观经济数据获取器"""
    
    def __init__(self):
        self.akshare_available = self._check_akshare_availability()
        self.data_cache = {}  # 数据缓存
        self.cache_timeout = 3600  # 缓存1小时
        
        # 宏观经济指标配置
        self.macro_indicators = {
            'gdp': {
                'name': 'GDP增长率',
                'frequency': 'quarterly',  # 季度
                'impact_weight': 0.3,
                'description': '国内生产总值增长率，反映经济整体增长状况'
            },
            'cpi': {
                'name': 'CPI消费者价格指数',
                'frequency': 'monthly',  # 月度
                'impact_weight': 0.25,
                'description': '消费者价格指数，反映通胀水平'
            },
            'ppi': {
                'name': 'PPI生产者价格指数',
                'frequency': 'monthly',
                'impact_weight': 0.2,
                'description': '生产者价格指数，反映上游价格压力'
            },
            'interest_rate': {
                'name': '基准利率',
                'frequency': 'irregular',  # 不定期
                'impact_weight': 0.4,
                'description': '央行基准利率，影响资金成本'
            },
            'money_supply': {
                'name': '货币供应量',
                'frequency': 'monthly',
                'impact_weight': 0.35,
                'description': 'M1、M2货币供应量，反映流动性状况'
            },
            'exchange_rate': {
                'name': '人民币汇率',
                'frequency': 'daily',
                'impact_weight': 0.15,
                'description': '人民币对美元汇率，影响外资流入'
            },
            'pmi': {
                'name': 'PMI采购经理指数',
                'frequency': 'monthly',
                'impact_weight': 0.3,
                'description': '制造业采购经理指数，反映制造业景气度'
            }
        }
        
        logger.info("🏛️ 宏观经济数据获取器初始化完成")
    
    def _check_akshare_availability(self) -> bool:
        """检查AKShare可用性"""
        try:
            import akshare as ak
            # 简单测试
            ak.stock_info_a_code_name()
            return True
        except Exception as e:
            logger.warning(f"AKShare不可用: {e}")
            return False
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (datetime.now().timestamp() - cache_time) < self.cache_timeout
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now().timestamp()
        }
    
    def _get_cache(self, cache_key: str) -> Any:
        """获取缓存数据"""
        return self.data_cache[cache_key]['data']
    
    def get_gdp_data(self) -> Dict[str, Any]:
        """获取GDP数据"""
        cache_key = 'gdp_data'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_indicator_data('gdp')
            
            # 获取GDP数据
            gdp_data = ak.macro_china_gdp()
            
            if gdp_data.empty:
                return self._get_empty_indicator_data('gdp')
            
            # 数据处理
            gdp_data = gdp_data.sort_values('季度', ascending=False)
            latest_data = gdp_data.iloc[0]
            
            result = {
                'indicator': 'gdp',
                'name': self.macro_indicators['gdp']['name'],
                'latest_value': float(latest_data.get('国内生产总值-绝对值', 0)),
                'latest_growth_rate': float(latest_data.get('国内生产总值-同比增长', 0)),
                'latest_period': str(latest_data.get('季度', '')),
                'trend_analysis': self._analyze_gdp_trend(gdp_data),
                'market_impact': self._calculate_gdp_market_impact(latest_data),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ GDP数据获取成功: {result['latest_growth_rate']:.2f}%")
            return result
            
        except Exception as e:
            logger.error(f"获取GDP数据失败: {e}")
            return self._get_empty_indicator_data('gdp')
    
    def get_cpi_data(self) -> Dict[str, Any]:
        """获取CPI数据"""
        cache_key = 'cpi_data'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_indicator_data('cpi')
            
            # 获取CPI数据
            cpi_data = ak.macro_china_cpi()
            
            if cpi_data.empty:
                return self._get_empty_indicator_data('cpi')
            
            # 数据处理
            cpi_data = cpi_data.sort_values('月份', ascending=False)
            latest_data = cpi_data.iloc[0]
            
            result = {
                'indicator': 'cpi',
                'name': self.macro_indicators['cpi']['name'],
                'latest_value': float(latest_data.get('全国-当月', 0)),
                'latest_growth_rate': float(latest_data.get('全国-同比增长', 0)),
                'latest_period': str(latest_data.get('月份', '')),
                'trend_analysis': self._analyze_cpi_trend(cpi_data),
                'market_impact': self._calculate_cpi_market_impact(latest_data),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ CPI数据获取成功: {result['latest_growth_rate']:.2f}%")
            return result
            
        except Exception as e:
            logger.error(f"获取CPI数据失败: {e}")
            return self._get_empty_indicator_data('cpi')
    
    def get_pmi_data(self) -> Dict[str, Any]:
        """获取PMI数据"""
        cache_key = 'pmi_data'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_indicator_data('pmi')
            
            # 获取PMI数据
            pmi_data = ak.macro_china_pmi()
            
            if pmi_data.empty:
                return self._get_empty_indicator_data('pmi')
            
            # 数据处理
            pmi_data = pmi_data.sort_values('月份', ascending=False)
            latest_data = pmi_data.iloc[0]
            
            result = {
                'indicator': 'pmi',
                'name': self.macro_indicators['pmi']['name'],
                'latest_value': float(latest_data.get('制造业-PMI', 0)),
                'latest_period': str(latest_data.get('月份', '')),
                'trend_analysis': self._analyze_pmi_trend(pmi_data),
                'market_impact': self._calculate_pmi_market_impact(latest_data),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ PMI数据获取成功: {result['latest_value']:.1f}")
            return result
            
        except Exception as e:
            logger.error(f"获取PMI数据失败: {e}")
            return self._get_empty_indicator_data('pmi')
    
    def get_exchange_rate_data(self) -> Dict[str, Any]:
        """获取汇率数据"""
        cache_key = 'exchange_rate_data'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return self._get_empty_indicator_data('exchange_rate')
            
            # 获取人民币汇率数据
            exchange_data = ak.currency_boc_safe()
            
            if exchange_data.empty:
                return self._get_empty_indicator_data('exchange_rate')
            
            # 查找美元汇率
            usd_data = exchange_data[exchange_data['货币名称'].str.contains('美元', na=False)]
            
            if usd_data.empty:
                return self._get_empty_indicator_data('exchange_rate')
            
            latest_data = usd_data.iloc[0]
            
            result = {
                'indicator': 'exchange_rate',
                'name': self.macro_indicators['exchange_rate']['name'],
                'latest_value': float(latest_data.get('现汇卖出价', 0)),
                'currency_pair': 'USD/CNY',
                'latest_period': datetime.now().strftime('%Y-%m-%d'),
                'trend_analysis': self._analyze_exchange_rate_trend(usd_data),
                'market_impact': self._calculate_exchange_rate_market_impact(latest_data),
                'data_available': True,
                'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 汇率数据获取成功: {result['latest_value']:.4f}")
            return result
            
        except Exception as e:
            logger.error(f"获取汇率数据失败: {e}")
            return self._get_empty_indicator_data('exchange_rate')
    
    def _get_empty_indicator_data(self, indicator: str) -> Dict[str, Any]:
        """获取空的指标数据结构"""
        return {
            'indicator': indicator,
            'name': self.macro_indicators.get(indicator, {}).get('name', '未知指标'),
            'latest_value': 0.0,
            'latest_growth_rate': 0.0,
            'latest_period': '',
            'trend_analysis': {
                'trend': 'unknown',
                'strength': 0.0,
                'description': '数据不可用'
            },
            'market_impact': {
                'impact_score': 0.0,
                'impact_direction': 'neutral',
                'description': '无法评估影响'
            },
            'data_available': False,
            'update_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }

    def _analyze_gdp_trend(self, gdp_data: pd.DataFrame) -> Dict[str, Any]:
        """分析GDP趋势"""
        try:
            if len(gdp_data) < 2:
                return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

            # 获取最近几个季度的增长率
            recent_growth = gdp_data.head(4)['国内生产总值-同比增长'].astype(float)

            # 计算趋势
            if len(recent_growth) >= 2:
                trend_slope = recent_growth.iloc[0] - recent_growth.iloc[1]

                if trend_slope > 0.5:
                    trend = 'accelerating'
                    description = 'GDP增长加速，经济向好'
                elif trend_slope < -0.5:
                    trend = 'decelerating'
                    description = 'GDP增长放缓，经济承压'
                else:
                    trend = 'stable'
                    description = 'GDP增长稳定'

                strength = min(abs(trend_slope) / 2.0, 1.0)  # 标准化强度

                return {
                    'trend': trend,
                    'strength': strength,
                    'description': description,
                    'recent_values': recent_growth.tolist()
                }

            return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

        except Exception as e:
            logger.error(f"GDP趋势分析失败: {e}")
            return {'trend': 'unknown', 'strength': 0.0, 'description': '分析失败'}

    def _calculate_gdp_market_impact(self, latest_data: pd.Series) -> Dict[str, Any]:
        """计算GDP对市场的影响"""
        try:
            growth_rate = float(latest_data.get('国内生产总值-同比增长', 0))

            # GDP增长率对股市的影响评估
            if growth_rate > 7.0:
                impact_score = 0.8
                impact_direction = 'positive'
                description = 'GDP高增长，利好股市'
            elif growth_rate > 5.0:
                impact_score = 0.4
                impact_direction = 'positive'
                description = 'GDP稳健增长，温和利好'
            elif growth_rate > 3.0:
                impact_score = 0.1
                impact_direction = 'neutral'
                description = 'GDP增长平稳，影响中性'
            else:
                impact_score = -0.6
                impact_direction = 'negative'
                description = 'GDP增长疲弱，利空股市'

            return {
                'impact_score': impact_score,
                'impact_direction': impact_direction,
                'description': description,
                'weight': self.macro_indicators['gdp']['impact_weight']
            }

        except Exception as e:
            logger.error(f"GDP市场影响计算失败: {e}")
            return {'impact_score': 0.0, 'impact_direction': 'neutral', 'description': '计算失败'}

    def _analyze_cpi_trend(self, cpi_data: pd.DataFrame) -> Dict[str, Any]:
        """分析CPI趋势"""
        try:
            if len(cpi_data) < 2:
                return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

            # 获取最近几个月的CPI同比增长
            recent_cpi = cpi_data.head(6)['全国-同比增长'].astype(float)

            if len(recent_cpi) >= 2:
                trend_slope = recent_cpi.iloc[0] - recent_cpi.iloc[1]

                if trend_slope > 0.3:
                    trend = 'rising'
                    description = 'CPI上升，通胀压力增加'
                elif trend_slope < -0.3:
                    trend = 'falling'
                    description = 'CPI下降，通胀压力缓解'
                else:
                    trend = 'stable'
                    description = 'CPI稳定'

                strength = min(abs(trend_slope) / 1.0, 1.0)

                return {
                    'trend': trend,
                    'strength': strength,
                    'description': description,
                    'recent_values': recent_cpi.tolist()
                }

            return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

        except Exception as e:
            logger.error(f"CPI趋势分析失败: {e}")
            return {'trend': 'unknown', 'strength': 0.0, 'description': '分析失败'}

    def _calculate_cpi_market_impact(self, latest_data: pd.Series) -> Dict[str, Any]:
        """计算CPI对市场的影响"""
        try:
            cpi_growth = float(latest_data.get('全国-同比增长', 0))

            # CPI对股市的影响评估
            if cpi_growth > 4.0:
                impact_score = -0.7
                impact_direction = 'negative'
                description = 'CPI过高，通胀压力大，利空股市'
            elif cpi_growth > 2.5:
                impact_score = -0.3
                impact_direction = 'negative'
                description = 'CPI偏高，温和通胀压力'
            elif cpi_growth > 1.0:
                impact_score = 0.2
                impact_direction = 'positive'
                description = 'CPI适中，经济健康增长'
            elif cpi_growth > -0.5:
                impact_score = 0.1
                impact_direction = 'neutral'
                description = 'CPI低位，影响中性'
            else:
                impact_score = -0.4
                impact_direction = 'negative'
                description = 'CPI过低，通缩风险'

            return {
                'impact_score': impact_score,
                'impact_direction': impact_direction,
                'description': description,
                'weight': self.macro_indicators['cpi']['impact_weight']
            }

        except Exception as e:
            logger.error(f"CPI市场影响计算失败: {e}")
            return {'impact_score': 0.0, 'impact_direction': 'neutral', 'description': '计算失败'}

    def _analyze_pmi_trend(self, pmi_data: pd.DataFrame) -> Dict[str, Any]:
        """分析PMI趋势"""
        try:
            if len(pmi_data) < 2:
                return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

            # 获取最近几个月的PMI数据
            recent_pmi = pmi_data.head(6)['制造业-PMI'].astype(float)

            if len(recent_pmi) >= 2:
                trend_slope = recent_pmi.iloc[0] - recent_pmi.iloc[1]

                if trend_slope > 1.0:
                    trend = 'improving'
                    description = 'PMI上升，制造业景气度改善'
                elif trend_slope < -1.0:
                    trend = 'deteriorating'
                    description = 'PMI下降，制造业景气度恶化'
                else:
                    trend = 'stable'
                    description = 'PMI稳定'

                strength = min(abs(trend_slope) / 3.0, 1.0)

                return {
                    'trend': trend,
                    'strength': strength,
                    'description': description,
                    'recent_values': recent_pmi.tolist()
                }

            return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

        except Exception as e:
            logger.error(f"PMI趋势分析失败: {e}")
            return {'trend': 'unknown', 'strength': 0.0, 'description': '分析失败'}

    def _calculate_pmi_market_impact(self, latest_data: pd.Series) -> Dict[str, Any]:
        """计算PMI对市场的影响"""
        try:
            pmi_value = float(latest_data.get('制造业-PMI', 0))

            # PMI对股市的影响评估（50为荣枯线）
            if pmi_value > 52.0:
                impact_score = 0.6
                impact_direction = 'positive'
                description = 'PMI高于荣枯线，制造业扩张，利好股市'
            elif pmi_value > 50.0:
                impact_score = 0.3
                impact_direction = 'positive'
                description = 'PMI略高于荣枯线，制造业温和扩张'
            elif pmi_value > 48.0:
                impact_score = -0.2
                impact_direction = 'negative'
                description = 'PMI接近荣枯线，制造业承压'
            else:
                impact_score = -0.5
                impact_direction = 'negative'
                description = 'PMI低于荣枯线，制造业收缩，利空股市'

            return {
                'impact_score': impact_score,
                'impact_direction': impact_direction,
                'description': description,
                'weight': self.macro_indicators['pmi']['impact_weight']
            }

        except Exception as e:
            logger.error(f"PMI市场影响计算失败: {e}")
            return {'impact_score': 0.0, 'impact_direction': 'neutral', 'description': '计算失败'}

    def _analyze_exchange_rate_trend(self, exchange_data: pd.DataFrame) -> Dict[str, Any]:
        """分析汇率趋势"""
        try:
            if len(exchange_data) < 2:
                return {'trend': 'unknown', 'strength': 0.0, 'description': '数据不足'}

            # 简单趋势分析（实际应用中需要更多历史数据）
            current_rate = float(exchange_data.iloc[0].get('现汇卖出价', 0))

            if current_rate > 7.2:
                trend = 'weakening'
                description = '人民币偏弱，汇率压力较大'
                strength = 0.6
            elif current_rate > 6.8:
                trend = 'stable'
                description = '人民币汇率相对稳定'
                strength = 0.3
            else:
                trend = 'strengthening'
                description = '人民币相对强势'
                strength = 0.5

            return {
                'trend': trend,
                'strength': strength,
                'description': description,
                'current_rate': current_rate
            }

        except Exception as e:
            logger.error(f"汇率趋势分析失败: {e}")
            return {'trend': 'unknown', 'strength': 0.0, 'description': '分析失败'}

    def _calculate_exchange_rate_market_impact(self, latest_data: pd.Series) -> Dict[str, Any]:
        """计算汇率对市场的影响"""
        try:
            exchange_rate = float(latest_data.get('现汇卖出价', 0))

            # 汇率对股市的影响评估
            if exchange_rate > 7.3:
                impact_score = -0.4
                impact_direction = 'negative'
                description = '人民币大幅贬值，资本外流压力，利空股市'
            elif exchange_rate > 7.0:
                impact_score = -0.2
                impact_direction = 'negative'
                description = '人民币偏弱，轻微利空'
            elif exchange_rate > 6.5:
                impact_score = 0.1
                impact_direction = 'neutral'
                description = '汇率相对稳定，影响中性'
            else:
                impact_score = 0.3
                impact_direction = 'positive'
                description = '人民币强势，外资流入，利好股市'

            return {
                'impact_score': impact_score,
                'impact_direction': impact_direction,
                'description': description,
                'weight': self.macro_indicators['exchange_rate']['impact_weight']
            }

        except Exception as e:
            logger.error(f"汇率市场影响计算失败: {e}")
            return {'impact_score': 0.0, 'impact_direction': 'neutral', 'description': '计算失败'}

    def get_comprehensive_macro_analysis(self) -> Dict[str, Any]:
        """获取综合宏观经济分析"""
        try:
            logger.info("🔍 开始获取综合宏观经济分析...")

            # 获取各项宏观指标
            indicators_data = {
                'gdp': self.get_gdp_data(),
                'cpi': self.get_cpi_data(),
                'pmi': self.get_pmi_data(),
                'exchange_rate': self.get_exchange_rate_data()
            }

            # 计算综合市场影响评分
            total_weighted_score = 0.0
            total_weight = 0.0
            available_indicators = []

            for indicator_name, data in indicators_data.items():
                if data.get('data_available', False):
                    impact = data.get('market_impact', {})
                    weight = impact.get('weight', 0)
                    score = impact.get('impact_score', 0)

                    total_weighted_score += score * weight
                    total_weight += weight
                    available_indicators.append(indicator_name)

            # 计算综合评分
            if total_weight > 0:
                comprehensive_score = total_weighted_score / total_weight
            else:
                comprehensive_score = 0.0

            # 确定综合影响方向
            if comprehensive_score > 0.3:
                overall_direction = 'positive'
                overall_description = '宏观经济环境整体利好股市'
            elif comprehensive_score > -0.3:
                overall_direction = 'neutral'
                overall_description = '宏观经济环境对股市影响中性'
            else:
                overall_direction = 'negative'
                overall_description = '宏观经济环境整体利空股市'

            result = {
                'comprehensive_score': comprehensive_score,
                'overall_direction': overall_direction,
                'overall_description': overall_description,
                'available_indicators': available_indicators,
                'indicators_count': len(available_indicators),
                'total_indicators': len(indicators_data),
                'indicators_data': indicators_data,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_quality': 'good' if len(available_indicators) >= 3 else 'limited'
            }

            logger.info(f"✅ 综合宏观分析完成，评分: {comprehensive_score:.3f}, 方向: {overall_direction}")
            return result

        except Exception as e:
            logger.error(f"综合宏观分析失败: {e}")
            return {
                'comprehensive_score': 0.0,
                'overall_direction': 'neutral',
                'overall_description': '宏观分析失败',
                'available_indicators': [],
                'indicators_count': 0,
                'total_indicators': 0,
                'indicators_data': {},
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_quality': 'error'
            }

def main():
    """测试宏观经济数据获取器"""
    print("🏛️ 测试宏观经济数据获取器...")

    fetcher = MacroEconomicDataFetcher()

    # 测试各项指标
    print("\n📊 测试GDP数据...")
    gdp_data = fetcher.get_gdp_data()
    print(f"GDP增长率: {gdp_data.get('latest_growth_rate', 0):.2f}%")

    print("\n📊 测试CPI数据...")
    cpi_data = fetcher.get_cpi_data()
    print(f"CPI同比: {cpi_data.get('latest_growth_rate', 0):.2f}%")

    print("\n📊 测试PMI数据...")
    pmi_data = fetcher.get_pmi_data()
    print(f"PMI指数: {pmi_data.get('latest_value', 0):.1f}")

    print("\n📊 测试汇率数据...")
    exchange_data = fetcher.get_exchange_rate_data()
    print(f"USD/CNY: {exchange_data.get('latest_value', 0):.4f}")

    print("\n🔍 综合宏观分析...")
    comprehensive = fetcher.get_comprehensive_macro_analysis()
    print(f"综合评分: {comprehensive['comprehensive_score']:.3f}")
    print(f"整体方向: {comprehensive['overall_direction']}")
    print(f"描述: {comprehensive['overall_description']}")

if __name__ == "__main__":
    main()

    def get_comprehensive_macro_analysis(self) -> Dict[str, Any]:
        """获取综合宏观经济分析"""
        try:
            logger.info("🔍 开始获取综合宏观经济分析...")

            # 获取各项宏观指标
            indicators_data = {
                'gdp': self.get_gdp_data(),
                'cpi': self.get_cpi_data(),
                'pmi': self.get_pmi_data(),
                'exchange_rate': self.get_exchange_rate_data()
            }

            # 计算综合市场影响评分
            total_weighted_score = 0.0
            total_weight = 0.0
            available_indicators = []

            for indicator_name, data in indicators_data.items():
                if data.get('data_available', False):
                    impact = data.get('market_impact', {})
                    weight = impact.get('weight', 0)
                    score = impact.get('impact_score', 0)

                    total_weighted_score += score * weight
                    total_weight += weight
                    available_indicators.append(indicator_name)

            # 计算综合评分
            if total_weight > 0:
                comprehensive_score = total_weighted_score / total_weight
            else:
                comprehensive_score = 0.0

            # 确定综合影响方向
            if comprehensive_score > 0.3:
                overall_direction = 'positive'
                overall_description = '宏观经济环境整体利好股市'
            elif comprehensive_score > -0.3:
                overall_direction = 'neutral'
                overall_description = '宏观经济环境对股市影响中性'
            else:
                overall_direction = 'negative'
                overall_description = '宏观经济环境整体利空股市'

            result = {
                'comprehensive_score': comprehensive_score,
                'overall_direction': overall_direction,
                'overall_description': overall_description,
                'available_indicators': available_indicators,
                'indicators_count': len(available_indicators),
                'total_indicators': len(indicators_data),
                'indicators_data': indicators_data,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_quality': 'good' if len(available_indicators) >= 3 else 'limited'
            }

            logger.info(f"✅ 综合宏观分析完成，评分: {comprehensive_score:.3f}, 方向: {overall_direction}")
            return result

        except Exception as e:
            logger.error(f"综合宏观分析失败: {e}")
            return {
                'comprehensive_score': 0.0,
                'overall_direction': 'neutral',
                'overall_description': '宏观分析失败',
                'available_indicators': [],
                'indicators_count': 0,
                'total_indicators': 0,
                'indicators_data': {},
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_quality': 'error'
            }

def main():
    """测试宏观经济数据获取器"""
    print("🏛️ 测试宏观经济数据获取器...")

    fetcher = MacroEconomicDataFetcher()

    # 测试各项指标
    print("\n📊 测试GDP数据...")
    gdp_data = fetcher.get_gdp_data()
    print(f"GDP增长率: {gdp_data.get('latest_growth_rate', 0):.2f}%")

    print("\n📊 测试CPI数据...")
    cpi_data = fetcher.get_cpi_data()
    print(f"CPI同比: {cpi_data.get('latest_growth_rate', 0):.2f}%")

    print("\n📊 测试PMI数据...")
    pmi_data = fetcher.get_pmi_data()
    print(f"PMI指数: {pmi_data.get('latest_value', 0):.1f}")

    print("\n📊 测试汇率数据...")
    exchange_data = fetcher.get_exchange_rate_data()
    print(f"USD/CNY: {exchange_data.get('latest_value', 0):.4f}")

    print("\n🔍 综合宏观分析...")
    comprehensive = fetcher.get_comprehensive_macro_analysis()
    print(f"综合评分: {comprehensive['comprehensive_score']:.3f}")
    print(f"整体方向: {comprehensive['overall_direction']}")
    print(f"描述: {comprehensive['overall_description']}")

if __name__ == "__main__":
    main()
