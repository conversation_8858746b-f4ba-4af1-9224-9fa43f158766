#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板潜力股实时卖点分析器
基于分时数据的涨停股持有决策系统
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Optional, Tuple

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入现有的数据获取模块
try:
    from 连板潜力股实时评分 import SimpleStockScorer
    from enhanced_data_fetcher import EnhancedStockDataFetcher
    from chip_distribution_analyzer import ChipDistributionAnalyzer
    from real_data_interface import RealDataInterface  # 使用修复后的数据接口

    # 检查数据接口可用性
    REAL_DATA_AVAILABLE = True
    try:
        # 使用修复后的RealDataInterface，包含同花顺备用数据源
        real_data_interface = RealDataInterface()
        print("✅ 真实数据接口连接成功")
    except Exception as e:
        print(f"⚠️ 真实数据接口初始化失败: {e}")
        # 备用：尝试使用原有接口
        try:
            real_data_interface = EnhancedStockDataFetcher()
            print("⚠️ 使用备用数据接口")
        except Exception as e2:
            print(f"⚠️ 备用数据接口也失败: {e2}")
            REAL_DATA_AVAILABLE = False
            real_data_interface = None

    # 检查筹码分析器可用性
    CHIP_ANALYZER_AVAILABLE = True
    try:
        chip_analyzer_interface = ChipDistributionAnalyzer()
    except Exception as e:
        print(f"⚠️ 筹码分析器初始化失败: {e}")
        CHIP_ANALYZER_AVAILABLE = False
        chip_analyzer_interface = None

except ImportError as e:
    print(f"⚠️ 导入模块失败: {e}")
    REAL_DATA_AVAILABLE = False
    CHIP_ANALYZER_AVAILABLE = False
    real_data_interface = None
    chip_analyzer_interface = None

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('sell_point_analysis.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SellPointAnalyzer(SimpleStockScorer):
    """
    连板潜力股实时卖点分析器
    继承买点分析器的数据获取能力，专注卖点决策逻辑
    """
    
    def __init__(self):
        """初始化卖点分析器"""
        super().__init__()  # 继承所有数据获取方法
        self.profit_line_rate = 0.03  # 动态止盈线：成本价+3%

        # 初始化筹码分析器
        if CHIP_ANALYZER_AVAILABLE:
            self.chip_analyzer = chip_analyzer_interface
            logger.info("✅ 筹码分析器初始化成功")
        else:
            self.chip_analyzer = None
            logger.warning("⚠️ 筹码分析器不可用")

        logger.info("🔴 连板潜力股实时卖点分析器初始化完成")
    
    def analyze_stocks(self, stock_codes: List[str]) -> List[Dict]:
        """
        分析股票列表的卖点信号
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            分析结果列表
        """
        results = []
        
        print(f"\n🔴 开始分析 {len(stock_codes)} 只股票的卖点信号...")
        print("=" * 80)
        
        for i, code in enumerate(stock_codes, 1):
            try:
                print(f"\n[{i}/{len(stock_codes)}] 分析 {code}...")
                
                # 获取分时数据（复用父类方法）
                # 智能数据获取：优先当日数据，备用前一交易日数据
                current_time = datetime.now()
                if self._is_trading_time(current_time):
                    target_date = current_time.strftime('%Y%m%d')
                    print(f"📊 交易时间，获取当日数据: {target_date}")
                else:
                    # 非交易时间，优先尝试获取当日数据（可能是涨停板等重要数据）
                    today_date = current_time.strftime('%Y%m%d')
                    print(f"📊 非交易时间，优先尝试获取当日数据: {today_date}")

                    # 先尝试获取当日数据
                    test_minute_data = self._get_minute_data(code, today_date)
                    if test_minute_data is not None and len(test_minute_data) >= 5:
                        target_date = today_date
                        print(f"✅ 成功获取当日数据，使用: {target_date}")
                    else:
                        # 如果当日数据不可用，获取前一个交易日
                        prev_trading_day = self._get_previous_trading_day(current_time)
                        target_date = prev_trading_day.strftime('%Y%m%d')
                        print(f"📊 当日数据不可用，获取前一交易日数据: {target_date}")

                minute_data = self._get_minute_data(code, target_date)
                if minute_data is None or len(minute_data) < 5:
                    print(f"❌ {code} 数据不足，跳过")
                    continue
                
                # 执行卖点分析
                result = self._analyze_sell_point(code, minute_data)
                if result:
                    results.append(result)
                    self._print_sell_analysis_result(result)
                else:
                    print(f"❌ {code} 分析失败")
                    
            except Exception as e:
                logger.error(f"分析{code}失败: {e}")
                print(f"❌ {code} 分析异常: {e}")
                continue
        
        return results
    
    def _analyze_sell_point(self, code: str, minute_data: pd.DataFrame) -> Optional[Dict]:
        """
        核心卖点分析方法
        
        Args:
            code: 股票代码
            minute_data: 分时数据
            
        Returns:
            分析结果字典
        """
        try:
            # 1. 开盘类型检测（复用父类方法）
            open_info = self._check_open_type(minute_data, code)
            
            # 2. 获取历史数据用于对比（复用父类方法）
            history_data = self._get_history_data(code)
            
            # 3. 检查是否为涨停股（优先级最高）
            current_change = open_info.get('current_change_pct', 0)
            if current_change >= 0.099:  # 涨幅≥9.9%，视为涨停股
                return self._analyze_limit_up_sell_point(code, minute_data, history_data, open_info)

            # 4. 根据开盘类型执行相应的卖点分析
            if open_info.get('type') == 'high':  # 高开 >3%
                return self._analyze_high_open_sell_point(code, minute_data, history_data, open_info)
            elif open_info.get('type') == 'flat':  # 平开 -1%~+1%
                return self._analyze_flat_open_sell_point(code, minute_data, history_data, open_info)
            elif open_info.get('type') == 'low':  # 低开 <-1%
                return self._analyze_low_open_sell_point(code, minute_data, history_data, open_info)
            else:
                return self._analyze_unknown_sell_point(code, minute_data, history_data, open_info)
                
        except Exception as e:
            logger.error(f"卖点分析失败 {code}: {e}")
            return None
    
    # ==================== 核心算法实现 ====================

    def _calculate_volume_ratio_enhanced(self, minute_data: pd.DataFrame, history_data: pd.DataFrame) -> Dict:
        """增强版量比估算：前5分钟总量 / (5日均量 × 0.25)"""
        try:
            if len(minute_data) < 5 or history_data is None or len(history_data) == 0:
                return {'volume_ratio': 1.0, 'health_score': 0, 'signals': ['数据不足']}

            # 调试：检查数据结构
            logger.info(f"分时数据列名: {list(minute_data.columns)}")
            logger.info(f"历史数据列名: {list(history_data.columns)}")

            # 智能列名检测 - 分时数据
            minute_volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    minute_volume_col = col
                    break

            # 智能列名检测 - 历史数据
            history_volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in history_data.columns:
                    history_volume_col = col
                    break

            if minute_volume_col is None or history_volume_col is None:
                logger.error(f"未找到成交量列，分时数据列: {list(minute_data.columns)}, 历史数据列: {list(history_data.columns)}")
                return {'volume_ratio': 1.0, 'health_score': 0, 'signals': ['无成交量数据']}

            # 前5分钟总成交量
            vol_5min = minute_data[minute_volume_col].iloc[:5].sum()

            # 5日分时均量计算
            avg_vol_5d = history_data[history_volume_col].mean()
            expected_5min_vol = avg_vol_5d * 0.25  # 25%日交易量

            # 量比计算
            volume_ratio = vol_5min / expected_5min_vol if expected_5min_vol > 0 else 1.0

            logger.info(f"量比计算: 前5分钟量={vol_5min}, 5日均量={avg_vol_5d}, 预期量={expected_5min_vol}, 量比={volume_ratio:.2f}")

            # 健康度评分
            health_score = 0
            signals = []

            if volume_ratio > 2.8:
                health_score += 40
                signals.append(f'✅ 量比优秀({volume_ratio:.1f})')
            elif volume_ratio > 2.0:
                health_score += 20
                signals.append(f'⚠️ 量比一般({volume_ratio:.1f})')
            else:
                signals.append(f'❌ 量比不足({volume_ratio:.1f})')

            return {
                'volume_ratio': volume_ratio,
                'health_score': health_score,
                'signals': signals,
                'vol_5min': vol_5min,
                'expected_vol': expected_5min_vol,
                'minute_volume_col': minute_volume_col,
                'history_volume_col': history_volume_col
            }

        except Exception as e:
            logger.error(f"增强版量比计算失败: {e}")
            logger.error(f"分时数据形状: {minute_data.shape if minute_data is not None else 'None'}")
            logger.error(f"历史数据形状: {history_data.shape if history_data is not None else 'None'}")
            return {'volume_ratio': 1.0, 'health_score': 0, 'signals': [f'计算失败: {str(e)}']}

    def _detect_volume_price_divergence(self, minute_data: pd.DataFrame, volume_ratio: float) -> Dict:
        """量价背离检测"""
        try:
            if len(minute_data) < 5:
                return {'divergence_detected': False, 'up_ratio': 0, 'score': 0}

            # 计算上涨分钟数占比
            up_minutes = 0
            total_minutes = len(minute_data[:5])  # 前5分钟

            for i in range(min(5, len(minute_data))):
                row = minute_data.iloc[i]
                if row['close'] > row['open']:
                    up_minutes += 1

            up_ratio = up_minutes / total_minutes

            # 量价配合评分
            score = 0
            divergence_detected = False

            if up_ratio > 0.7 and volume_ratio > 2.5:
                score = 30
                divergence_detected = False  # 量价配合良好
            elif up_ratio < 0.3 and volume_ratio > 2.0:
                divergence_detected = True  # 量价背离

            return {
                'divergence_detected': divergence_detected,
                'up_ratio': up_ratio,
                'score': score,
                'description': f'上涨占比{up_ratio:.1%}，量比{volume_ratio:.1f}'
            }

        except Exception as e:
            logger.error(f"量价背离检测失败: {e}")
            return {'divergence_detected': False, 'up_ratio': 0, 'score': 0}

    def _locate_volume_pressure_peak(self, minute_data: pd.DataFrame) -> Dict:
        """抛压峰值定位：最大量出现在最高价分钟"""
        try:
            if len(minute_data) < 5:
                return {'peak_at_high': False, 'score': 0}

            # 智能列名检测
            volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            high_col = None
            for col in ['high', '最高', 'High']:
                if col in minute_data.columns:
                    high_col = col
                    break

            if volume_col is None or high_col is None:
                logger.warning(f"缺少必要列: volume_col={volume_col}, high_col={high_col}")
                return {'peak_at_high': False, 'score': 0}

            # 找到最大成交量的分钟
            max_vol_idx = minute_data[volume_col].iloc[:5].idxmax()
            max_vol_minute = minute_data.loc[max_vol_idx]

            # 找到最高价的分钟
            max_price_idx = minute_data[high_col].iloc[:5].idxmax()
            max_price_minute = minute_data.loc[max_price_idx]

            # 判断是否在同一分钟或相邻分钟
            peak_at_high = abs(max_vol_idx - max_price_idx) <= 1

            score = 30 if peak_at_high else 0

            return {
                'peak_at_high': peak_at_high,
                'score': score,
                'max_vol_time': max_vol_minute.get('time', max_vol_minute.get('时间', '未知')),
                'max_price_time': max_price_minute.get('time', max_price_minute.get('时间', '未知')),
                'description': '量峰与价峰同步' if peak_at_high else '量峰与价峰分离'
            }

        except Exception as e:
            logger.error(f"抛压峰值定位失败: {e}")
            return {'peak_at_high': False, 'score': 0}

    def _analyze_price_resilience(self, minute_data: pd.DataFrame, open_info: Dict, scenario: str) -> Dict:
        """价格韧性评分表分析"""
        try:
            if len(minute_data) < 5:
                return {'resilience_score': 0, 'signals': ['数据不足']}

            open_change = open_info.get('open_change_pct', 0)
            current_change = open_info.get('current_change_pct', 0)

            # 智能列名检测
            high_col = None
            for col in ['high', '最高', 'High']:
                if col in minute_data.columns:
                    high_col = col
                    break

            low_col = None
            for col in ['low', '最低', 'Low']:
                if col in minute_data.columns:
                    low_col = col
                    break

            close_col = None
            for col in ['close', '收盘', 'Close']:
                if col in minute_data.columns:
                    close_col = col
                    break

            if high_col is None or low_col is None or close_col is None:
                logger.warning(f"缺少价格列: high={high_col}, low={low_col}, close={close_col}")
                return {'resilience_score': 0, 'signals': ['价格数据不足']}

            # 计算最大回撤
            highs = minute_data[high_col].values
            lows = minute_data[low_col].values
            max_high = np.max(highs)
            min_low = np.min(lows)
            max_drawdown = (max_high - min_low) / max_high if max_high > 0 else 0

            # 计算均价线关系
            prices = minute_data[close_col].values
            avg_price = np.mean(prices)
            current_price = prices[-1]
            avg_line_relation = (current_price - avg_price) / avg_price if avg_price > 0 else 0

            # 场景化评分标准
            resilience_score = 0
            signals = []

            if scenario == 'high':  # 高开场景
                # 最大回撤评分
                if max_drawdown <= 0.012:  # ≤1.2%
                    resilience_score += 40
                    signals.append('✅ 回撤控制优秀')
                elif max_drawdown <= 0.020:
                    resilience_score += 20
                    signals.append('⚠️ 回撤控制一般')
                else:
                    signals.append('❌ 回撤过大')

                # 均价线关系评分
                if avg_line_relation > 0.015:  # 始终高于+1.5%
                    resilience_score += 30
                    signals.append('✅ 强势运行')
                elif avg_line_relation > 0:
                    resilience_score += 15
                    signals.append('⚠️ 均价线上方')
                else:
                    signals.append('❌ 跌破均价线')

            elif scenario == 'flat':  # 平开场景
                # 最大回撤评分
                if max_drawdown <= 0.018:  # ≤1.8%
                    resilience_score += 35
                    signals.append('✅ 回撤可接受')
                else:
                    signals.append('❌ 回撤过大')

                # 站上均价线即达标
                if avg_line_relation > 0:
                    resilience_score += 25
                    signals.append('✅ 站稳均价线')
                else:
                    signals.append('❌ 未站稳均价线')

            elif scenario == 'low':  # 低开场景
                # 最大回撤评分（极限容忍）
                if max_drawdown <= 0.030:  # ≤3.0%
                    resilience_score += 30
                    signals.append('✅ 回撤在极限范围')
                else:
                    signals.append('❌ 回撤超出极限')

                # 突破均价线即观望信号
                if avg_line_relation > 0:
                    resilience_score += 20
                    signals.append('✅ 突破均价线')
                else:
                    signals.append('❌ 未突破均价线')

            # 反弹力度检测
            rebound_strength = self._calculate_rebound_strength(minute_data)
            if rebound_strength > 0.01:  # 反弹>1%
                resilience_score += 10
                signals.append('✅ 反弹有力')

            return {
                'resilience_score': resilience_score,
                'max_drawdown': max_drawdown,
                'avg_line_relation': avg_line_relation,
                'rebound_strength': rebound_strength,
                'signals': signals,
                'description': f'回撤{max_drawdown:.1%}，均价线{avg_line_relation:+.1%}'
            }

        except Exception as e:
            logger.error(f"价格韧性分析失败: {e}")
            return {'resilience_score': 0, 'signals': ['分析失败']}

    def _calculate_rebound_strength(self, minute_data: pd.DataFrame) -> float:
        """计算反弹力度"""
        try:
            if len(minute_data) < 3:
                return 0

            # 智能列名检测
            low_col = None
            for col in ['low', '最低', 'Low']:
                if col in minute_data.columns:
                    low_col = col
                    break

            high_col = None
            for col in ['high', '最高', 'High']:
                if col in minute_data.columns:
                    high_col = col
                    break

            if low_col is None or high_col is None:
                logger.warning(f"反弹力度计算缺少价格列: low={low_col}, high={high_col}")
                return 0

            lows = minute_data[low_col].values
            highs = minute_data[high_col].values

            # 找到最低点
            min_low_idx = np.argmin(lows)
            min_low = lows[min_low_idx]

            # 找到最低点之后的最高反弹
            if min_low_idx < len(highs) - 1:
                max_rebound = np.max(highs[min_low_idx+1:])
                rebound_strength = (max_rebound - min_low) / min_low if min_low > 0 else 0
            else:
                rebound_strength = 0

            return rebound_strength

        except Exception as e:
            logger.error(f"反弹力度计算失败: {e}")
            return 0

    def _detect_dangerous_patterns(self, minute_data: pd.DataFrame) -> Dict:
        """危险形态识别：钓鱼线、量能塌方、阶梯跌"""
        try:
            if len(minute_data) < 5:
                return {'patterns': [], 'danger_score': 0}

            patterns = []
            danger_score = 0

            # 1. 钓鱼线检测：急拉2%+缓慢跌破黄线
            fishing_line = self._detect_fishing_line(minute_data)
            if fishing_line['detected']:
                patterns.append('🚫 钓鱼线形态')
                danger_score += 50

            # 2. 量能塌方检测：9:35量柱为9:30的40%以下
            volume_collapse = self._detect_volume_collapse(minute_data)
            if volume_collapse['detected']:
                patterns.append('🚫 量能塌方')
                danger_score += 40

            # 3. 阶梯跌检测：连续低点下移
            ladder_down = self._detect_ladder_down(minute_data)
            if ladder_down['detected']:
                patterns.append('🚫 阶梯跌形态')
                danger_score += 30

            return {
                'patterns': patterns,
                'danger_score': danger_score,
                'fishing_line': fishing_line,
                'volume_collapse': volume_collapse,
                'ladder_down': ladder_down,
                'high_danger': danger_score >= 50
            }

        except Exception as e:
            logger.error(f"危险形态识别失败: {e}")
            return {'patterns': [], 'danger_score': 0}

    def _detect_fishing_line(self, minute_data: pd.DataFrame) -> Dict:
        """钓鱼线检测：急拉后缓慢跌破均价线"""
        try:
            if len(minute_data) < 5:
                return {'detected': False, 'description': '数据不足'}

            prices = minute_data['close'].values
            avg_price = np.mean(prices)

            # 检测急拉：前2分钟涨幅>2%
            early_surge = False
            if len(prices) >= 2:
                early_gain = (prices[1] - prices[0]) / prices[0] if prices[0] > 0 else 0
                early_surge = early_gain > 0.02

            # 检测缓慢跌破均价线：后续价格持续在均价线下方
            below_avg_count = 0
            for i in range(2, len(prices)):
                if prices[i] < avg_price:
                    below_avg_count += 1

            below_avg_ratio = below_avg_count / max(1, len(prices) - 2)

            detected = early_surge and below_avg_ratio > 0.6

            return {
                'detected': detected,
                'early_surge': early_surge,
                'below_avg_ratio': below_avg_ratio,
                'description': f'急拉{early_surge}，跌破均价{below_avg_ratio:.1%}'
            }

        except Exception as e:
            logger.error(f"钓鱼线检测失败: {e}")
            return {'detected': False, 'description': '检测失败'}

    def _detect_volume_collapse(self, minute_data: pd.DataFrame) -> Dict:
        """量能塌方检测：9:35量柱为9:30的40%以下"""
        try:
            if len(minute_data) < 6:  # 需要至少6分钟数据
                return {'detected': False, 'ratio': 1.0}

            # 智能列名检测
            volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            if volume_col is None:
                logger.warning("量能塌方检测缺少成交量列")
                return {'detected': False, 'ratio': 1.0}

            # 9:30的量（第一分钟）
            vol_930 = minute_data[volume_col].iloc[0]
            # 9:35的量（第六分钟）
            vol_935 = minute_data[volume_col].iloc[5]

            ratio = vol_935 / vol_930 if vol_930 > 0 else 1.0
            detected = ratio < 0.4

            return {
                'detected': detected,
                'ratio': ratio,
                'vol_930': vol_930,
                'vol_935': vol_935,
                'description': f'9:35量能为9:30的{ratio:.1%}'
            }

        except Exception as e:
            logger.error(f"量能塌方检测失败: {e}")
            return {'detected': False, 'ratio': 1.0}

    def _detect_ladder_down(self, minute_data: pd.DataFrame) -> Dict:
        """阶梯跌检测：连续低点下移"""
        try:
            if len(minute_data) < 4:
                return {'detected': False, 'consecutive_count': 0}

            # 智能列名检测
            low_col = None
            for col in ['low', '最低', 'Low']:
                if col in minute_data.columns:
                    low_col = col
                    break

            if low_col is None:
                logger.warning("阶梯跌检测缺少最低价列")
                return {'detected': False, 'consecutive_count': 0}

            lows = minute_data[low_col].values
            consecutive_count = 0
            max_consecutive = 0

            for i in range(1, len(lows)):
                if lows[i] < lows[i-1]:
                    consecutive_count += 1
                    max_consecutive = max(max_consecutive, consecutive_count)
                else:
                    consecutive_count = 0

            detected = max_consecutive >= 3  # 连续3次低点下移

            return {
                'detected': detected,
                'consecutive_count': max_consecutive,
                'description': f'连续{max_consecutive}次低点下移'
            }

        except Exception as e:
            logger.error(f"阶梯跌检测失败: {e}")
            return {'detected': False, 'consecutive_count': 0}

    def _analyze_sector_anchoring(self, code: str, open_info: Dict, scenario: str) -> Dict:
        """板块锚定效应分析"""
        try:
            # 获取股票板块信息（复用父类方法）
            sector_info = self._get_stock_sector(code)
            if not sector_info.get('has_sector', False):
                return {'anchoring_score': 0, 'signals': ['无板块信息']}

            sector_name = sector_info.get('sector_name', '')

            # 获取板块表现（复用父类方法）
            sector_performance = self._get_sector_performance(sector_name)
            sector_gain = sector_performance.get('morning_gain', 0)  # 已经是小数格式，无需再除以100

            individual_gain = open_info.get('current_change_pct', 0)

            anchoring_score = 0
            signals = []

            if scenario == 'high':  # 高开场景
                # 持有条件：板块涨幅>个股涨幅*0.8
                threshold = individual_gain * 0.8
                if sector_gain > threshold:
                    anchoring_score = 30
                    signals.append(f'✅ 板块支撑({sector_gain:.1%}>{threshold:.1%})')
                else:
                    signals.append(f'❌ 板块支撑不足({sector_gain:.1%}<{threshold:.1%})')

                # 止盈条件：板块转跌且个股滞涨
                if sector_gain < 0 and individual_gain < sector_gain * 1.5:
                    signals.append('🚫 板块转跌个股滞涨')
                    anchoring_score = -20

            elif scenario == 'flat':  # 平开场景
                # 持有条件：板块中涨幅前30%（简化为板块涨幅>0.5%）
                if sector_gain > 0.005:
                    anchoring_score = 25
                    signals.append(f'✅ 板块表现良好({sector_gain:.1%})')
                else:
                    signals.append(f'❌ 板块表现一般({sector_gain:.1%})')

                # 止盈条件：板块跌幅>1%
                if sector_gain < -0.01:
                    signals.append('🚫 板块跌幅过大')
                    anchoring_score = -30

            elif scenario == 'low':  # 低开场景
                # 持有条件：板块率先翻红
                if sector_gain > 0:
                    anchoring_score = 20
                    signals.append(f'✅ 板块率先翻红({sector_gain:.1%})')
                else:
                    signals.append(f'❌ 板块未翻红({sector_gain:.1%})')

                # 止盈条件：板块龙头跌停（简化为板块跌幅>2%）
                if sector_gain < -0.02:
                    signals.append('🚫 板块大幅下跌')
                    anchoring_score = -40

            return {
                'anchoring_score': anchoring_score,
                'sector_gain': sector_gain,
                'individual_gain': individual_gain,
                'sector_name': sector_name,
                'signals': signals,
                'description': f'板块{sector_gain:.1%} vs 个股{individual_gain:.1%}'
            }

        except Exception as e:
            logger.error(f"板块锚定分析失败: {e}")
            return {'anchoring_score': 0, 'signals': ['分析失败']}

    def _check_time_nodes(self, minute_data: pd.DataFrame, open_info: Dict) -> Dict:
        """时间节点控制检查"""
        try:
            current_time = datetime.now()
            current_change = open_info.get('current_change_pct', 0)

            time_signals = []
            time_score = 0

            # 判断是否在交易时间
            is_trading_time = self._is_trading_time(current_time)

            if not is_trading_time:
                # 非交易时间，使用模拟时间进行分析
                time_signals.append('⏰ 非交易时间，使用模拟分析')
                # 假设当前是9:40进行分析
                simulated_time = current_time.replace(hour=9, minute=40)
                time_signals.append('✅ 模拟9:40终极决策点')
                time_score += 20

                return {
                    'time_score': time_score,
                    'signals': time_signals,
                    'is_after_10': False,
                    'is_red': current_change > 0,
                    'operation_window': True,  # 模拟操作窗口
                    'current_time': f'{current_time.strftime("%H:%M")}(模拟9:40)',
                    'description': f'非交易时间{current_time.strftime("%H:%M")}，模拟9:40分析',
                    'is_trading_time': False
                }

            # 交易时间内的正常判断
            # 9:35首次判断
            if current_time.hour == 9 and current_time.minute >= 35:
                time_signals.append('✅ 已过9:35首次判断点')
                time_score += 10

            # 9:40终极决策
            if current_time.hour == 9 and current_time.minute >= 40:
                time_signals.append('✅ 已到9:40终极决策点')
                time_score += 20

            # 10:00翻红检测（平开股关键）
            is_after_10 = current_time.hour >= 10 or (current_time.hour == 10 and current_time.minute >= 0)
            is_red = current_change > 0

            if is_after_10:
                if is_red:
                    time_signals.append('✅ 10:00后已翻红')
                    time_score += 15
                else:
                    time_signals.append('🚫 10:00后未翻红')
                    time_score -= 30

            # 9:45前操作窗口
            operation_window = (current_time.hour == 9 and current_time.minute <= 45) or \
                             (current_time.hour < 9)
            if operation_window:
                time_signals.append('⏰ 9:45前操作窗口')
            else:
                time_signals.append('⏰ 已过9:45操作窗口')

            return {
                'time_score': time_score,
                'signals': time_signals,
                'is_after_10': is_after_10,
                'is_red': is_red,
                'operation_window': operation_window,
                'current_time': current_time.strftime('%H:%M'),
                'description': f'{current_time.strftime("%H:%M")} 时间节点检查',
                'is_trading_time': True
            }

        except Exception as e:
            logger.error(f"时间节点检查失败: {e}")
            return {'time_score': 0, 'signals': ['检查失败']}

    def _is_trading_time(self, current_time: datetime) -> bool:
        """判断是否在交易时间内"""
        try:
            # 获取当前时间的小时和分钟
            hour = current_time.hour
            minute = current_time.minute
            weekday = current_time.weekday()  # 0=周一, 6=周日

            # 周末不交易
            if weekday >= 5:  # 周六、周日
                return False

            # 交易时间：9:30-11:30, 13:00-15:00
            morning_start = (hour == 9 and minute >= 30) or (hour == 10) or (hour == 11 and minute <= 30)
            afternoon_start = (hour >= 13 and hour < 15) or (hour == 15 and minute == 0)

            return morning_start or afternoon_start

        except Exception as e:
            logger.error(f"交易时间判断失败: {e}")
            return False

    def _get_previous_trading_day(self, current_time: datetime) -> datetime:
        """获取前一个交易日"""
        try:
            # 从当前时间开始往前找
            check_date = current_time - timedelta(days=1)

            # 最多往前找7天，避免长假期问题
            for _ in range(7):
                # 检查是否为工作日（周一到周五）
                if check_date.weekday() < 5:  # 0=周一, 4=周五
                    return check_date
                check_date -= timedelta(days=1)

            # 如果找不到，返回昨天
            return current_time - timedelta(days=1)

        except Exception as e:
            logger.error(f"获取前一交易日失败: {e}")
            return current_time - timedelta(days=1)

    def _analyze_limit_up_sell_point(self, code: str, minute_data: pd.DataFrame,
                                   history_data: pd.DataFrame, open_info: Dict) -> Dict:
        """
        涨停股专用卖点分析（涨幅≥9.9%）
        基于报告的四大核心指标：量能结构(50%) + 板块锚定(30%) + 时间节点(20%)
        """
        try:
            logger.info(f"🔴 涨停股专用卖点分析: {code}")

            current_change = open_info.get('current_change_pct', 0)
            open_change = open_info.get('open_change_pct', 0)

            # 涨停股动态止盈线：昨收+3%
            profit_line = self.profit_line_rate  # 3%

            # 1. 量能结构分析（权重50%）- 涨停股核心指标
            volume_analysis = self._analyze_limit_up_volume_structure(minute_data, history_data)

            # 2. 板块锚定效应（权重25%）- 涨停股验证指标
            sector_analysis = self._analyze_limit_up_sector_anchoring(code, open_info)

            # 3. 筹码分布分析（权重20%）- 涨停股专业指标
            prev_close = open_info.get('prev_close', 0)
            limit_up_price = prev_close * 1.1 if prev_close > 0 else 0
            current_price = prev_close * (1 + current_change) if prev_close > 0 else 0
            chip_analysis = self._analyze_limit_up_chip_distribution(code, current_price, limit_up_price)

            # 4. 一字板识别（特殊处理）
            is_limit_board = self._is_limit_board_stock(minute_data, limit_up_price)

            if is_limit_board:
                # 一字板股票使用专用决策逻辑
                logger.info(f"识别为一字板股票: {code}")
                decision = self._make_limit_board_decision(chip_analysis, sector_analysis, volume_analysis)

                return {
                    'code': code,
                    'analysis_type': 'limit_board_enhanced',
                    'open_info': open_info,
                    'decision': decision,
                    'total_score': 85,  # 一字板固定高分
                    'key_indicators': {
                        '开盘涨幅': f'{open_change:.1%}',
                        '当前涨幅': f'{current_change:.1%}',
                        '涨停状态': '一字板涨停',
                        '动态止盈线': f'{profit_line:.1%}',
                        '5分钟量比': f'{volume_analysis.get("volume_ratio", 0):.1f}',
                        '板块涨幅': f'{sector_analysis.get("sector_gain", 0):.1%}',
                        '筹码评分': f'{chip_analysis.get("chip_score", 0):.0f}分',
                        '股票类型': '一字板',
                        '综合评分': '85分(一字板)'
                    },
                    'detailed_analysis': {
                        'volume_analysis': volume_analysis,
                        'sector_analysis': sector_analysis,
                        'chip_analysis': chip_analysis,
                        'limit_board_detected': True
                    },
                    'analysis_time': datetime.now().strftime('%H:%M:%S')
                }

            # 5. 涨停板出货识别（权重10%）- 普通涨停股风控
            limit_break_analysis = self._detect_limit_break_patterns(minute_data, limit_up_price)

            # 6. 时间节点控制（权重10%）- 涨停股时机判断
            time_analysis = self._check_time_nodes(minute_data, open_info)

            # 7. 涨停股特有风险检测（增强版，包含出货识别）
            limit_up_risks = self._detect_limit_up_risks_enhanced(minute_data, volume_analysis, sector_analysis, chip_analysis, limit_break_analysis)

            # 综合评分计算（涨停股权重调整，加入出货识别）
            limit_break_score = -limit_break_analysis.get('break_count', 0) * 10  # 开板扣分
            total_score = (
                volume_analysis['volume_score'] * 0.35 +
                sector_analysis['anchoring_score'] * 0.25 +
                chip_analysis['chip_score'] * 0.2 +
                time_analysis['time_score'] * 0.1 +
                limit_break_score * 0.1  # 出货识别权重10%
            )

            # 涨停股专用决策逻辑（增强版，包含出货识别）
            decision = self._make_limit_up_decision_enhanced(
                current_change, profit_line, volume_analysis, sector_analysis,
                chip_analysis, time_analysis, limit_up_risks, limit_break_analysis, total_score
            )

            return {
                'code': code,
                'analysis_type': 'limit_up_sell_enhanced',
                'open_info': open_info,
                'decision': decision,
                'total_score': total_score,
                'key_indicators': {
                    '开盘涨幅': f'{open_change:.1%}',
                    '当前涨幅': f'{current_change:.1%}',
                    '涨停状态': '涨停' if current_change >= 0.099 else '接近涨停',
                    '动态止盈线': f'{profit_line:.1%}',
                    '5分钟量比': f'{volume_analysis.get("volume_ratio", 0):.1f}',
                    '多空力道比': f'{volume_analysis.get("power_ratio", 0):.1f}',
                    '开板次数': f'{limit_break_analysis.get("break_count", 0)}次',
                    '封单变化': f'{limit_break_analysis.get("seal_change_ratio", 0):+.1%}',
                    '板块涨幅': f'{sector_analysis.get("sector_gain", 0):.1%}',
                    '筹码评分': f'{chip_analysis.get("chip_score", 0):.0f}分',
                    '获利盘比例': f'{chip_analysis.get("profit_ratio", 0):.1%}',
                    '综合评分': f'{total_score:.0f}分'
                },
                'detailed_analysis': {
                    'volume_analysis': volume_analysis,
                    'sector_analysis': sector_analysis,
                    'chip_analysis': chip_analysis,
                    'limit_break_analysis': limit_break_analysis,
                    'time_analysis': time_analysis,
                    'limit_up_risks': limit_up_risks
                },
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            logger.error(f"涨停股卖点分析失败 {code}: {e}")
            return self._create_error_result(code, 'limit_up_sell', str(e))

    def _analyze_limit_up_volume_structure(self, minute_data: pd.DataFrame, history_data: pd.DataFrame) -> Dict:
        """涨停股量能结构分析（权重50%）"""
        try:
            if len(minute_data) < 5:
                return {'volume_score': 0, 'signals': ['数据不足'], 'volume_ratio': 0}

            # 智能列名检测
            minute_volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    minute_volume_col = col
                    break

            history_volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in history_data.columns:
                    history_volume_col = col
                    break

            if minute_volume_col is None or history_volume_col is None:
                return {'volume_score': 0, 'signals': ['无成交量数据'], 'volume_ratio': 0}

            # 1. 5分钟量比计算（涨停股标准：>2.5安全）
            vol_5min = minute_data[minute_volume_col].iloc[:5].sum()
            avg_vol_5d = history_data[history_volume_col].mean()
            expected_5min_vol = avg_vol_5d * 0.25
            volume_ratio = vol_5min / expected_5min_vol if expected_5min_vol > 0 else 1.0

            volume_score = 0
            signals = []

            # 量比评分（涨停股标准）
            if volume_ratio > 2.5:
                volume_score += 40
                signals.append(f'✅ 量比安全({volume_ratio:.1f}>2.5)')
            elif volume_ratio > 1.8:
                volume_score += 20
                signals.append(f'⚠️ 量比一般({volume_ratio:.1f})')
            else:
                signals.append(f'❌ 量比不足({volume_ratio:.1f}<2.5)')

            # 2. 量价配合检测（涨停股关键）
            volume_price_match = self._check_limit_up_volume_price_match(minute_data, minute_volume_col)
            if volume_price_match['matched']:
                volume_score += 30
                signals.append('✅ 量价配合良好')
            else:
                signals.append('❌ 量价背离')

            # 3. 首分钟抛压分析
            first_min_pressure = self._analyze_first_minute_pressure(minute_data, minute_volume_col)
            if first_min_pressure['safe']:
                volume_score += 20
                signals.append('✅ 首分钟抛压可控')
            else:
                signals.append('❌ 首分钟抛压过重')

            # 4. 量能持续性检测
            volume_sustainability = self._check_volume_sustainability(minute_data, minute_volume_col)
            if volume_sustainability['sustainable']:
                volume_score += 10
                signals.append('✅ 量能持续')
            else:
                signals.append('❌ 量能衰减')

            # 5. 多空力道比分析（新增）
            power_ratio_analysis = self._calculate_power_ratio_alternative(minute_data)
            power_ratio = power_ratio_analysis['power_ratio']

            if power_ratio > 1.8:  # 报告标准：>1.8持有
                volume_score += 15
                signals.append(f'✅ 多空力道强({power_ratio:.1f}>1.8)')
            elif power_ratio > 1.2:
                volume_score += 8
                signals.append(f'⚠️ 多空力道一般({power_ratio:.1f})')
            else:
                signals.append(f'❌ 多空力道弱({power_ratio:.1f}<1.2)')

            return {
                'volume_score': volume_score,
                'volume_ratio': volume_ratio,
                'power_ratio': power_ratio,
                'signals': signals,
                'volume_price_match': volume_price_match,
                'first_min_pressure': first_min_pressure,
                'volume_sustainability': volume_sustainability,
                'power_ratio_analysis': power_ratio_analysis,
                'description': f'量比{volume_ratio:.1f}，多空比{power_ratio:.1f}，评分{volume_score}分'
            }

        except Exception as e:
            logger.error(f"涨停股量能分析失败: {e}")
            return {'volume_score': 0, 'signals': ['分析失败'], 'volume_ratio': 0}

    def _check_limit_up_volume_price_match(self, minute_data: pd.DataFrame, volume_col: str) -> Dict:
        """检查涨停股量价配合情况"""
        try:
            if len(minute_data) < 5:
                return {'matched': False, 'description': '数据不足'}

            # 找到价格最高点和成交量最大点
            high_col = None
            for col in ['high', '最高', 'High']:
                if col in minute_data.columns:
                    high_col = col
                    break

            if high_col is None:
                return {'matched': False, 'description': '无价格数据'}

            max_price_idx = minute_data[high_col].iloc[:5].idxmax()
            max_vol_idx = minute_data[volume_col].iloc[:5].idxmax()

            # 量价配合：最高价和最大量在相邻时间点
            matched = abs(max_price_idx - max_vol_idx) <= 1

            return {
                'matched': matched,
                'max_price_time': max_price_idx,
                'max_vol_time': max_vol_idx,
                'description': '量价同步' if matched else '量价分离'
            }

        except Exception as e:
            logger.error(f"量价配合检测失败: {e}")
            return {'matched': False, 'description': '检测失败'}

    def _analyze_first_minute_pressure(self, minute_data: pd.DataFrame, volume_col: str) -> Dict:
        """分析首分钟抛压强度"""
        try:
            if len(minute_data) < 1:
                return {'safe': False, 'pressure_ratio': 0}

            first_min_vol = minute_data[volume_col].iloc[0]
            total_vol_5min = minute_data[volume_col].iloc[:5].sum()

            # 首分钟成交量占前5分钟比例
            pressure_ratio = first_min_vol / total_vol_5min if total_vol_5min > 0 else 0

            # 安全标准：首分钟占比<50%
            safe = pressure_ratio < 0.5

            return {
                'safe': safe,
                'pressure_ratio': pressure_ratio,
                'first_min_vol': first_min_vol,
                'description': f'首分钟占比{pressure_ratio:.1%}'
            }

        except Exception as e:
            logger.error(f"首分钟抛压分析失败: {e}")
            return {'safe': False, 'pressure_ratio': 0}

    def _check_volume_sustainability(self, minute_data: pd.DataFrame, volume_col: str) -> Dict:
        """检查量能持续性"""
        try:
            if len(minute_data) < 5:
                return {'sustainable': False, 'sustainability_ratio': 0}

            first_min_vol = minute_data[volume_col].iloc[0]
            avg_vol_2_5 = minute_data[volume_col].iloc[1:5].mean()

            # 后4分钟平均量能相对首分钟的比例
            sustainability_ratio = avg_vol_2_5 / first_min_vol if first_min_vol > 0 else 0

            # 持续性标准：后4分钟平均≥首分钟的60%
            sustainable = sustainability_ratio >= 0.6

            return {
                'sustainable': sustainable,
                'sustainability_ratio': sustainability_ratio,
                'description': f'量能持续性{sustainability_ratio:.1%}'
            }

        except Exception as e:
            logger.error(f"量能持续性检测失败: {e}")
            return {'sustainable': False, 'sustainability_ratio': 0}

    def _calculate_power_ratio_alternative(self, minute_data: pd.DataFrame) -> Dict:
        """多空力道比替代计算（基于上涨/下跌分钟成交量比）"""
        try:
            if len(minute_data) < 5:
                return {'power_ratio': 1.0, 'up_volume': 0, 'down_volume': 0}

            # 智能列名检测
            volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            open_col = None
            for col in ['open', '开盘', 'Open']:
                if col in minute_data.columns:
                    open_col = col
                    break

            close_col = None
            for col in ['close', '收盘', 'Close']:
                if col in minute_data.columns:
                    close_col = col
                    break

            if not all([volume_col, open_col, close_col]):
                return {'power_ratio': 1.0, 'up_volume': 0, 'down_volume': 0}

            up_volume = 0  # 上涨分钟的成交量
            down_volume = 0  # 下跌分钟的成交量
            flat_volume = 0  # 平盘分钟的成交量

            for i in range(min(5, len(minute_data))):  # 前5分钟
                row = minute_data.iloc[i]
                volume = row[volume_col]
                open_price = row[open_col]
                close_price = row[close_col]

                if close_price > open_price:
                    up_volume += volume
                elif close_price < open_price:
                    down_volume += volume
                else:
                    flat_volume += volume

            # 计算多空力道比
            total_down = down_volume + flat_volume * 0.5  # 平盘按一半算空方
            power_ratio = up_volume / total_down if total_down > 0 else 999

            return {
                'power_ratio': power_ratio,
                'up_volume': up_volume,
                'down_volume': down_volume,
                'flat_volume': flat_volume,
                'description': f'多空比{power_ratio:.1f}(多{up_volume}/空{total_down})'
            }

        except Exception as e:
            logger.error(f"多空力道比计算失败: {e}")
            return {'power_ratio': 1.0, 'up_volume': 0, 'down_volume': 0}

    def _detect_limit_break_patterns(self, minute_data: pd.DataFrame, limit_price: float) -> Dict:
        """涨停板出货识别：开板次数、回封速度等"""
        try:
            if len(minute_data) < 5 or limit_price <= 0:
                return {
                    'break_count': 0,
                    'reseal_speed': None,
                    'seal_strength_935': 0,
                    'seal_strength_940': 0,
                    'seal_change_ratio': 0,
                    'danger_signals': []
                }

            # 智能列名检测
            low_col = None
            for col in ['low', '最低', 'Low']:
                if col in minute_data.columns:
                    low_col = col
                    break

            close_col = None
            for col in ['close', '收盘', 'Close']:
                if col in minute_data.columns:
                    close_col = col
                    break

            volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            time_col = None
            for col in ['time', '时间', 'Time']:
                if col in minute_data.columns:
                    time_col = col
                    break

            if not all([low_col, close_col, volume_col, time_col]):
                return {'break_count': 0, 'danger_signals': ['数据列不完整']}

            # 1. 开板次数统计
            break_count = self._count_limit_breaks(minute_data, limit_price, low_col)

            # 2. 回封速度计算
            reseal_speed = self._calculate_reseal_speed(minute_data, limit_price, low_col, close_col, time_col)

            # 3. 封单强度变化
            seal_changes = self._track_seal_strength_changes(minute_data, limit_price, close_col, volume_col, time_col)

            # 4. 危险信号识别
            danger_signals = []

            # 开板次数风险
            if break_count >= 2:
                danger_signals.append(f'🚫 开板{break_count}次(≥2次危险)')

            # 回封速度风险
            if reseal_speed and reseal_speed > 5:
                danger_signals.append(f'🚫 回封速度慢({reseal_speed:.1f}分钟>5分钟)')

            # 封单变化风险
            if seal_changes['change_ratio'] < -0.4:
                danger_signals.append(f'🚫 封单减少{abs(seal_changes["change_ratio"]):.1%}(>40%)')

            return {
                'break_count': break_count,
                'reseal_speed': reseal_speed,
                'seal_strength_935': seal_changes['seal_935'],
                'seal_strength_940': seal_changes['seal_940'],
                'seal_change_ratio': seal_changes['change_ratio'],
                'danger_signals': danger_signals,
                'high_risk': len(danger_signals) >= 2,
                'description': f'开板{break_count}次，封单变化{seal_changes["change_ratio"]:+.1%}'
            }

        except Exception as e:
            logger.error(f"涨停板出货识别失败: {e}")
            return {'break_count': 0, 'danger_signals': ['识别失败']}

    def _count_limit_breaks(self, minute_data: pd.DataFrame, limit_price: float, low_col: str) -> int:
        """统计开板次数"""
        try:
            break_count = 0
            limit_threshold = limit_price * 0.999  # 考虑精度误差

            for i in range(len(minute_data)):
                # 如果最低价低于涨停价，说明开板了
                if minute_data[low_col].iloc[i] < limit_threshold:
                    break_count += 1

            return break_count

        except Exception as e:
            logger.error(f"开板次数统计失败: {e}")
            return 0

    def _calculate_reseal_speed(self, minute_data: pd.DataFrame, limit_price: float,
                              low_col: str, close_col: str, time_col: str) -> float:
        """计算回封速度（分钟）"""
        try:
            limit_threshold = limit_price * 0.999
            first_break_time = None
            reseal_time = None

            for i in range(len(minute_data)):
                row = minute_data.iloc[i]

                # 找到第一次开板时间
                if first_break_time is None and row[low_col] < limit_threshold:
                    first_break_time = row[time_col]

                # 找到重新封板时间（收盘价回到涨停价）
                if first_break_time and row[close_col] >= limit_threshold:
                    reseal_time = row[time_col]
                    break

            if first_break_time and reseal_time:
                # 计算时间差（分钟）
                if hasattr(first_break_time, 'hour'):  # datetime对象
                    time_diff = (reseal_time - first_break_time).total_seconds() / 60
                else:  # 字符串格式
                    # 简化处理：假设都在同一小时内
                    time_diff = 1  # 默认1分钟
                return max(time_diff, 0)

            return None

        except Exception as e:
            logger.error(f"回封速度计算失败: {e}")
            return None

    def _track_seal_strength_changes(self, minute_data: pd.DataFrame, limit_price: float,
                                   close_col: str, volume_col: str, time_col: str) -> Dict:
        """追踪封单强度变化"""
        try:
            limit_threshold = limit_price * 0.999
            seal_935 = 0
            seal_940 = 0

            for i in range(len(minute_data)):
                row = minute_data.iloc[i]

                # 检查是否在涨停状态
                if row[close_col] >= limit_threshold:
                    # 提取时间信息
                    if hasattr(row[time_col], 'hour'):  # datetime对象
                        hour = row[time_col].hour
                        minute = row[time_col].minute
                    else:  # 字符串格式
                        try:
                            time_str = str(row[time_col])
                            if ':' in time_str:
                                hour, minute = map(int, time_str.split(':')[:2])
                            else:
                                continue
                        except:
                            continue

                    # 记录9:35和9:40的封单强度（用成交量表示）
                    if hour == 9 and minute == 35:
                        seal_935 = row[volume_col]
                    elif hour == 9 and minute == 40:
                        seal_940 = row[volume_col]

            # 计算变化比例
            change_ratio = 0
            if seal_935 > 0 and seal_940 > 0:
                change_ratio = (seal_940 - seal_935) / seal_935

            return {
                'seal_935': seal_935,
                'seal_940': seal_940,
                'change_ratio': change_ratio
            }

        except Exception as e:
            logger.error(f"封单强度变化追踪失败: {e}")
            return {'seal_935': 0, 'seal_940': 0, 'change_ratio': 0}

    def _is_limit_board_stock(self, minute_data: pd.DataFrame, limit_price: float) -> bool:
        """识别一字板股票"""
        try:
            if len(minute_data) < 10 or limit_price <= 0:
                return False

            # 智能列名检测
            close_col = None
            for col in ['close', '收盘', 'Close']:
                if col in minute_data.columns:
                    close_col = col
                    break

            high_col = None
            for col in ['high', '最高', 'High']:
                if col in minute_data.columns:
                    high_col = col
                    break

            low_col = None
            for col in ['low', '最低', 'Low']:
                if col in minute_data.columns:
                    low_col = col
                    break

            if not all([close_col, high_col, low_col]):
                return False

            limit_threshold = limit_price * 0.999  # 考虑精度误差
            limit_minutes = 0
            total_minutes = len(minute_data)

            # 检查每分钟是否在涨停价附近
            for i in range(total_minutes):
                row = minute_data.iloc[i]
                close_price = row[close_col]
                high_price = row[high_col]
                low_price = row[low_col]

                # 如果收盘价、最高价、最低价都接近涨停价，认为是一字板分钟
                if (close_price >= limit_threshold and
                    high_price >= limit_threshold and
                    low_price >= limit_threshold * 0.995):  # 最低价允许稍微低一点
                    limit_minutes += 1

            # 如果85%以上的时间都是一字板，认为是一字板股票
            limit_ratio = limit_minutes / total_minutes
            is_limit_board = limit_ratio > 0.85

            logger.info(f"一字板检测: {limit_minutes}/{total_minutes}分钟在涨停价，比例{limit_ratio:.1%}，判定{'是' if is_limit_board else '否'}一字板")

            return is_limit_board

        except Exception as e:
            logger.error(f"一字板识别失败: {e}")
            return False

    def _make_limit_board_decision(self, chip_analysis: Dict, sector_analysis: Dict, volume_analysis: Dict) -> Dict:
        """一字板股票专用决策逻辑"""
        try:
            logger.info("执行一字板股票专用决策")

            # 一字板股票的决策逻辑：
            # 1. 主要依靠筹码分布和板块支撑
            # 2. 不考虑多空力道比（因为数据失真）
            # 3. 不考虑封单变化（因为一字板特性）
            # 4. 量比仍然有参考价值

            hold_score = 0
            reasons = []

            # 1. 筹码分布分析（权重最高）
            if chip_analysis.get('available', False):
                chip_score = chip_analysis.get('chip_score', 0)
                if chip_score >= 60:
                    hold_score += 40
                    reasons.append('筹码分布健康')
                elif chip_score >= 40:
                    hold_score += 25
                    reasons.append('筹码分布一般')
                else:
                    reasons.append('筹码分布较差')
            else:
                hold_score += 20  # 无筹码数据时给予中性分数
                reasons.append('筹码数据不可用')

            # 2. 板块支撑分析（权重较高）
            sector_score = sector_analysis.get('anchoring_score', 0)
            sector_gain = sector_analysis.get('sector_gain', 0)

            if sector_gain > 0.02:  # 板块涨幅>2%
                hold_score += 30
                reasons.append('板块强势支撑')
            elif sector_gain > 0:
                hold_score += 15
                reasons.append('板块温和支撑')
            elif sector_gain > -0.01:  # 板块跌幅<1%
                hold_score += 5
                reasons.append('板块相对稳定')
            else:
                reasons.append('板块走弱')

            # 3. 量比分析（权重较低，但仍有意义）
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            if volume_ratio > 2.0:  # 一字板的量比标准可以适当降低
                hold_score += 20
                reasons.append('量比充足')
            elif volume_ratio > 1.0:
                hold_score += 10
                reasons.append('量比一般')
            else:
                reasons.append('量比不足')

            # 决策逻辑
            if hold_score >= 70:
                return {
                    'action': '持有',
                    'reason': f'一字板涨停+{"+".join(reasons[:2])}，评分{hold_score}',
                    'confidence': min(85 + (hold_score - 70), 95),
                    'trigger': '一字板持有'
                }
            elif hold_score >= 50:
                return {
                    'action': '持有',
                    'reason': f'一字板涨停谨慎持有，评分{hold_score}',
                    'confidence': 75,
                    'trigger': '一字板谨慎'
                }
            else:
                return {
                    'action': '观望',
                    'reason': f'一字板但基本面较弱，评分{hold_score}',
                    'confidence': 60,
                    'trigger': '一字板观望'
                }

        except Exception as e:
            logger.error(f"一字板决策失败: {e}")
            return {
                'action': '持有',
                'reason': '一字板涨停，默认持有',
                'confidence': 75,
                'trigger': '一字板默认'
            }

    def _analyze_limit_up_sector_anchoring(self, code: str, open_info: Dict) -> Dict:
        """涨停股板块锚定分析（权重30%）"""
        try:
            # 获取板块信息
            sector_info = self._get_stock_sector(code)
            if not sector_info.get('has_sector', False):
                return {'anchoring_score': 0, 'signals': ['无板块信息'], 'sector_gain': 0}

            sector_name = sector_info.get('sector_name', '')
            sector_performance = self._get_sector_performance(sector_name)
            sector_gain = sector_performance.get('morning_gain', 0)  # 已经是小数格式，无需再除以100

            individual_gain = open_info.get('current_change_pct', 0)

            anchoring_score = 0
            signals = []

            # 涨停股板块验证标准
            # 1. 板块强势检查（板块涨幅>1%）
            if sector_gain > 0.01:
                anchoring_score += 40
                signals.append(f'✅ 板块强势({sector_gain:.1%}>1%)')
            elif sector_gain > 0:
                anchoring_score += 20
                signals.append(f'⚠️ 板块一般({sector_gain:.1%})')
            else:
                signals.append(f'❌ 板块转跌({sector_gain:.1%})')

            # 2. 个股相对表现（涨停股应该领涨板块）
            if individual_gain > sector_gain * 1.5:  # 个股涨幅>板块1.5倍
                anchoring_score += 30
                signals.append('✅ 个股领涨板块')
            elif individual_gain > sector_gain:
                anchoring_score += 15
                signals.append('⚠️ 个股跑赢板块')
            else:
                signals.append('❌ 个股落后板块')

            # 3. 板块跌幅风险检查
            if sector_gain < -0.008:  # 板块跌幅>0.8%
                anchoring_score -= 30
                signals.append('🚫 板块大幅下跌')

            return {
                'anchoring_score': anchoring_score,
                'sector_gain': sector_gain,
                'individual_gain': individual_gain,
                'sector_name': sector_name,
                'signals': signals,
                'description': f'板块{sector_gain:.1%} vs 个股{individual_gain:.1%}'
            }

        except Exception as e:
            logger.error(f"涨停股板块分析失败: {e}")
            return {'anchoring_score': 0, 'signals': ['分析失败'], 'sector_gain': 0}

    def _detect_limit_up_risks(self, minute_data: pd.DataFrame, volume_analysis: Dict, sector_analysis: Dict) -> Dict:
        """涨停股特有风险检测"""
        try:
            risks = []
            risk_score = 0

            # 1. 量能风险
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            if volume_ratio < 1.5:
                risks.append('🚫 量能不足风险')
                risk_score += 30

            # 2. 板块风险
            sector_gain = sector_analysis.get('sector_gain', 0)
            if sector_gain < -0.008:
                risks.append('🚫 板块下跌风险')
                risk_score += 40

            # 3. 量价背离风险
            volume_price_match = volume_analysis.get('volume_price_match', {})
            if not volume_price_match.get('matched', False):
                risks.append('🚫 量价背离风险')
                risk_score += 20

            # 4. 首分钟抛压风险
            first_min_pressure = volume_analysis.get('first_min_pressure', {})
            if not first_min_pressure.get('safe', False):
                risks.append('🚫 首分钟抛压过重')
                risk_score += 25

            return {
                'risks': risks,
                'risk_score': risk_score,
                'high_risk': risk_score >= 50,
                'description': f'风险评分{risk_score}分'
            }

        except Exception as e:
            logger.error(f"涨停股风险检测失败: {e}")
            return {'risks': [], 'risk_score': 0, 'high_risk': False}

    def _detect_limit_up_risks_enhanced(self, minute_data: pd.DataFrame, volume_analysis: Dict,
                                       sector_analysis: Dict, chip_analysis: Dict, limit_break_analysis: Dict) -> Dict:
        """涨停股特有风险检测（增强版，包含筹码分析和出货识别）"""
        try:
            risks = []
            risk_score = 0

            # 1. 量能风险
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            if volume_ratio < 1.5:
                risks.append('🚫 量能不足风险')
                risk_score += 30

            # 2. 板块风险
            sector_gain = sector_analysis.get('sector_gain', 0)
            if sector_gain < -0.008:
                risks.append('🚫 板块下跌风险')
                risk_score += 40

            # 3. 量价背离风险
            volume_price_match = volume_analysis.get('volume_price_match', {})
            if not volume_price_match.get('matched', False):
                risks.append('🚫 量价背离风险')
                risk_score += 20

            # 4. 首分钟抛压风险
            first_min_pressure = volume_analysis.get('first_min_pressure', {})
            if not first_min_pressure.get('safe', False):
                risks.append('🚫 首分钟抛压过重')
                risk_score += 25

            # 5. 筹码分布风险（新增）
            if chip_analysis.get('available', False):
                # 底部筹码迁移风险
                if chip_analysis.get('bottom_chip_migration', {}).get('migration_detected', False):
                    risks.append('🚫 底部筹码快速上移')
                    risk_score += 35

                # 上方筹码压力风险
                if not chip_analysis.get('upper_chip_gap', {}).get('has_gap', False):
                    risks.append('🚫 上方筹码压力')
                    risk_score += 15

                # 涨停价无筹码峰风险
                if not chip_analysis.get('limit_up_chip_peak', {}).get('has_peak', False):
                    risks.append('🚫 涨停价无筹码峰')
                    risk_score += 20

                # 主力成本风险
                main_cost_relation = chip_analysis.get('main_cost_relation', {}).get('relation', 'unknown')
                if main_cost_relation == 'below_cost':
                    risks.append('🚫 低于主力成本区')
                    risk_score += 25

            # 6. 涨停板出货风险（新增）
            if limit_break_analysis.get('high_risk', False):
                danger_signals = limit_break_analysis.get('danger_signals', [])
                risks.extend(danger_signals[:2])  # 添加前2个出货信号
                risk_score += len(danger_signals) * 15  # 每个出货信号15分

            # 7. 多空力道比风险
            power_ratio = volume_analysis.get('power_ratio', 1.0)
            if power_ratio < 0.8:  # 报告标准：<0.8卖出
                risks.append('🚫 多空力道严重失衡')
                risk_score += 30

            return {
                'risks': risks,
                'risk_score': risk_score,
                'high_risk': risk_score >= 50,
                'chip_risks_included': chip_analysis.get('available', False),
                'limit_break_risks_included': True,
                'description': f'增强版风险评分{risk_score}分(含出货识别)'
            }

        except Exception as e:
            logger.error(f"涨停股增强版风险检测失败: {e}")
            return {'risks': [], 'risk_score': 0, 'high_risk': False}

    def _make_limit_up_decision(self, current_change: float, profit_line: float,
                              volume_analysis: Dict, sector_analysis: Dict,
                              time_analysis: Dict, limit_up_risks: Dict, total_score: float) -> Dict:
        """涨停股专用决策逻辑"""
        try:
            # 1. 硬性风控：动态止盈线（对涨停股意义不大，但保持一致性）
            if current_change <= profit_line:
                return {
                    'action': '止盈',
                    'reason': f'跌破动态止盈线({profit_line:.1%})，立即止盈',
                    'confidence': 95,
                    'trigger': '止盈线'
                }

            # 2. 高风险一票否决
            if limit_up_risks.get('high_risk', False):
                risks = limit_up_risks.get('risks', [])
                return {
                    'action': '卖出',
                    'reason': f'涨停股高风险: {"; ".join(risks)}',
                    'confidence': 90,
                    'trigger': '高风险'
                }

            # 3. 涨停股持有条件检查（基于报告标准）
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            sector_score = sector_analysis.get('anchoring_score', 0)
            volume_score = volume_analysis.get('volume_score', 0)

            # 涨停股持有条件（更严格）
            hold_conditions = {
                'volume_ratio_ok': volume_ratio >= 2.5,  # 量比>2.5
                'volume_structure_ok': volume_score >= 60,  # 量能结构良好
                'sector_support': sector_score >= 30,  # 板块支撑
                'time_window': time_analysis.get('operation_window', True)  # 操作窗口
            }

            # 涨停股卖出条件
            sell_conditions = {
                'volume_weak': volume_ratio < 1.5,  # 量比不足
                'sector_weak': sector_score < 0,  # 板块转跌
                'volume_structure_poor': volume_score < 30,  # 量能结构差
                'risk_signals': len(limit_up_risks.get('risks', [])) >= 2  # 多重风险
            }

            # 决策逻辑
            hold_score = sum(hold_conditions.values())
            sell_score = sum(sell_conditions.values())

            if hold_score >= 3:  # 涨停股要求更严格，至少3个条件满足
                confidence = min(70 + total_score * 0.3, 90)
                return {
                    'action': '持有',
                    'reason': f'涨停股持有条件充分({hold_score}/4)，综合评分{total_score:.0f}',
                    'confidence': int(confidence),
                    'trigger': '持有条件'
                }
            elif sell_score >= 2:  # 任意2个卖出条件即建议卖出
                confidence = min(70 + sell_score * 10, 90)
                return {
                    'action': '卖出',
                    'reason': f'涨停股卖出信号({sell_score}/4)，建议卖出',
                    'confidence': int(confidence),
                    'trigger': '卖出条件'
                }
            else:
                return {
                    'action': '观望',
                    'reason': f'涨停股信号复杂，持有{hold_score}/4，卖出{sell_score}/4',
                    'confidence': 50,
                    'trigger': '信号复杂'
                }

        except Exception as e:
            logger.error(f"涨停股决策失败: {e}")
            return {
                'action': '观望',
                'reason': '决策异常',
                'confidence': 30,
                'trigger': '异常'
            }

    def _make_limit_up_decision_enhanced(self, current_change: float, profit_line: float,
                                       volume_analysis: Dict, sector_analysis: Dict, chip_analysis: Dict,
                                       time_analysis: Dict, limit_up_risks: Dict, limit_break_analysis: Dict, total_score: float) -> Dict:
        """涨停股专用决策逻辑（增强版，包含筹码分析和出货识别）"""
        try:
            # 1. 硬性风控：动态止盈线（对涨停股意义不大，但保持一致性）
            if current_change <= profit_line:
                return {
                    'action': '止盈',
                    'reason': f'跌破动态止盈线({profit_line:.1%})，立即止盈',
                    'confidence': 95,
                    'trigger': '止盈线'
                }

            # 2. 高风险一票否决（增强版）
            if limit_up_risks.get('high_risk', False):
                risks = limit_up_risks.get('risks', [])
                return {
                    'action': '卖出',
                    'reason': f'涨停股高风险: {"; ".join(risks[:2])}',  # 显示前2个风险
                    'confidence': 90,
                    'trigger': '高风险'
                }

            # 2.5. 涨停板出货识别一票否决（新增）
            if limit_break_analysis.get('high_risk', False):
                danger_signals = limit_break_analysis.get('danger_signals', [])
                return {
                    'action': '卖出',
                    'reason': f'涨停板出货信号: {"; ".join(danger_signals[:2])}',
                    'confidence': 95,
                    'trigger': '出货识别'
                }

            # 3. 涨停股持有条件检查（增强版，包含筹码和出货识别）
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            sector_score = sector_analysis.get('anchoring_score', 0)
            volume_score = volume_analysis.get('volume_score', 0)
            chip_score = chip_analysis.get('chip_score', 0)
            power_ratio = volume_analysis.get('power_ratio', 1.0)
            break_count = limit_break_analysis.get('break_count', 0)

            # 涨停股持有条件（更严格，包含筹码和出货识别）
            hold_conditions = {
                'volume_ratio_ok': volume_ratio >= 2.5,  # 量比>2.5
                'volume_structure_ok': volume_score >= 60,  # 量能结构良好
                'power_ratio_ok': power_ratio >= 1.2,  # 多空力道比>1.2
                'sector_support': sector_score >= 30,  # 板块支撑
                'chip_support': chip_score >= 40 if chip_analysis.get('available', False) else True,  # 筹码支撑
                'no_break_risk': break_count <= 1,  # 开板次数≤1
                'time_window': time_analysis.get('operation_window', True)  # 操作窗口
            }

            # 涨停股卖出条件（增强版，包含出货识别）
            sell_conditions = {
                'volume_weak': volume_ratio < 1.5,  # 量比不足
                'power_ratio_weak': power_ratio < 0.8,  # 多空力道比<0.8（报告标准）
                'sector_weak': sector_score < 0,  # 板块转跌
                'volume_structure_poor': volume_score < 30,  # 量能结构差
                'chip_risk': chip_score < 20 if chip_analysis.get('available', False) else False,  # 筹码风险
                'break_risk': break_count >= 2,  # 开板次数≥2（报告标准）
                'risk_signals': len(limit_up_risks.get('risks', [])) >= 2  # 多重风险
            }

            # 决策逻辑
            hold_score = sum(hold_conditions.values())
            sell_score = sum(sell_conditions.values())

            # 筹码分析可用时提高标准
            required_hold_score = 4 if chip_analysis.get('available', False) else 3

            if hold_score >= required_hold_score:  # 筹码可用时要求更严格
                confidence = min(70 + total_score * 0.3, 95)
                features = []
                if chip_analysis.get('available', False):
                    features.append("筹码分析")
                if limit_break_analysis.get('break_count', 0) <= 1:
                    features.append("出货识别")
                feature_desc = f"(含{'+'.join(features)})" if features else ""

                return {
                    'action': '持有',
                    'reason': f'涨停股持有条件充分({hold_score}/{len(hold_conditions)}){feature_desc}，综合评分{total_score:.0f}',
                    'confidence': int(confidence),
                    'trigger': '持有条件'
                }
            elif sell_score >= 2:  # 任意2个卖出条件即建议卖出
                confidence = min(70 + sell_score * 10, 90)
                return {
                    'action': '卖出',
                    'reason': f'涨停股卖出信号({sell_score}/{len(sell_conditions)})，建议卖出',
                    'confidence': int(confidence),
                    'trigger': '卖出条件'
                }
            else:
                return {
                    'action': '观望',
                    'reason': f'涨停股信号复杂，持有{hold_score}/{len(hold_conditions)}，卖出{sell_score}/{len(sell_conditions)}',
                    'confidence': 50,
                    'trigger': '信号复杂'
                }

        except Exception as e:
            logger.error(f"涨停股增强版决策失败: {e}")
            return {
                'action': '观望',
                'reason': '决策异常',
                'confidence': 30,
                'trigger': '异常'
            }

    def _analyze_high_open_sell_point(self, code: str, minute_data: pd.DataFrame,
                                     history_data: pd.DataFrame, open_info: Dict) -> Dict:
        """
        高开股卖点分析（开盘涨幅>3%）- 增强版五维决策模型
        持有条件：量比>2.5 + 未破分时均线 + 买一档金额>500万
        止盈条件：量比<1.8 + 跌破均线>3分钟 + 卖盘持续压盘
        """
        try:
            logger.info(f"🔴 高开股增强版卖点分析: {code}")

            # 计算动态止盈线：成本价+5%
            open_change = open_info.get('open_change_pct', 0)
            current_change = open_info.get('current_change_pct', 0)
            profit_line = open_change + self.profit_line_rate

            # 1. 量能结构分析（权重30%）
            volume_analysis = self._calculate_volume_ratio_enhanced(minute_data, history_data)
            volume_divergence = self._detect_volume_price_divergence(minute_data, volume_analysis['volume_ratio'])
            volume_peak = self._locate_volume_pressure_peak(minute_data)

            volume_score = volume_analysis['health_score'] + volume_divergence['score'] + volume_peak['score']

            # 2. 价格韧性评分（权重25%）
            resilience_analysis = self._analyze_price_resilience(minute_data, open_info, 'high')

            # 3. 危险形态识别（一票否决）
            danger_patterns = self._detect_dangerous_patterns(minute_data)

            # 4. 板块锚定效应（权重20%）
            sector_analysis = self._analyze_sector_anchoring(code, open_info, 'high')

            # 5. 时间节点控制（权重25%）
            time_analysis = self._check_time_nodes(minute_data, open_info)

            # 综合评分计算
            total_score = (
                volume_score * 0.3 +
                resilience_analysis['resilience_score'] * 0.25 +
                sector_analysis['anchoring_score'] * 0.2 +
                time_analysis['time_score'] * 0.25
            )

            # 决策逻辑（报告标准）
            decision = self._make_enhanced_high_open_decision(
                current_change, profit_line, volume_analysis, resilience_analysis,
                danger_patterns, sector_analysis, time_analysis, total_score
            )
            
            return {
                'code': code,
                'analysis_type': 'high_open_sell_enhanced',
                'open_info': open_info,
                'decision': decision,
                'total_score': total_score,
                'key_indicators': {
                    '开盘涨幅': f'{open_change:.1%}',
                    '当前涨幅': f'{current_change:.1%}',
                    '动态止盈线': f'{profit_line:.1%}',
                    '距离止盈线': f'{(current_change - profit_line):.1%}',
                    '量比': f'{volume_analysis["volume_ratio"]:.1f}',
                    '最大回撤': f'{resilience_analysis["max_drawdown"]:.1%}',
                    '板块涨幅': f'{sector_analysis["sector_gain"]:.1%}',
                    '综合评分': f'{total_score:.0f}分'
                },
                'detailed_analysis': {
                    'volume_analysis': volume_analysis,
                    'resilience_analysis': resilience_analysis,
                    'danger_patterns': danger_patterns,
                    'sector_analysis': sector_analysis,
                    'time_analysis': time_analysis
                },
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"高开股卖点分析失败 {code}: {e}")
            return self._create_error_result(code, 'high_open_sell', str(e))

    def _make_enhanced_high_open_decision(self, current_change: float, profit_line: float,
                                        volume_analysis: Dict, resilience_analysis: Dict,
                                        danger_patterns: Dict, sector_analysis: Dict,
                                        time_analysis: Dict, total_score: float) -> Dict:
        """增强版高开股决策逻辑（基于报告标准）"""
        try:
            # 1. 硬性风控：动态止盈线
            if current_change <= profit_line:
                return {
                    'action': '止盈',
                    'reason': f'跌破动态止盈线({profit_line:.1%})，立即止盈',
                    'confidence': 95,
                    'trigger': '止盈线'
                }

            # 2. 一票否决：危险形态
            if danger_patterns.get('high_danger', False):
                patterns = danger_patterns.get('patterns', [])
                return {
                    'action': '止盈',
                    'reason': f'检测到危险形态: {"; ".join(patterns)}',
                    'confidence': 90,
                    'trigger': '危险形态'
                }

            # 3. 报告标准持有条件检查
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            avg_line_relation = resilience_analysis.get('avg_line_relation', 0)
            sector_score = sector_analysis.get('anchoring_score', 0)

            # 持有条件：量比>2.5 + 未破分时均线 + 板块支撑
            hold_conditions = {
                'volume_ratio_ok': volume_ratio >= 2.5,
                'above_avg_line': avg_line_relation > 0,
                'sector_support': sector_score > 0,
                'time_window': time_analysis.get('operation_window', False)
            }

            # 止盈条件：量比<1.8 + 跌破均线>3分钟 + 板块转跌
            sell_conditions = {
                'volume_ratio_weak': volume_ratio < 1.8,
                'below_avg_line': avg_line_relation < -0.01,  # 跌破均线1%
                'sector_weak': sector_score < -10,
                'time_risk': time_analysis.get('is_after_10', False) and not time_analysis.get('is_red', False)
            }

            # 决策逻辑
            hold_score = sum(hold_conditions.values())
            sell_score = sum(sell_conditions.values())

            if hold_score >= 3:  # 至少3个持有条件满足
                confidence = min(70 + total_score * 0.2, 90)
                return {
                    'action': '持有',
                    'reason': f'持有条件满足({hold_score}/4)，综合评分{total_score:.0f}',
                    'confidence': int(confidence),
                    'trigger': '持有条件'
                }
            elif sell_score >= 2:  # 至少2个止盈条件满足
                confidence = min(60 + sell_score * 15, 85)
                return {
                    'action': '止盈',
                    'reason': f'止盈条件触发({sell_score}/4)，建议止盈',
                    'confidence': int(confidence),
                    'trigger': '止盈条件'
                }
            else:
                return {
                    'action': '观望',
                    'reason': f'信号不明确，持有{hold_score}/4，止盈{sell_score}/4',
                    'confidence': 40,
                    'trigger': '信号不明'
                }

        except Exception as e:
            logger.error(f"增强版高开股决策失败: {e}")
            return {
                'action': '观望',
                'reason': '决策异常',
                'confidence': 20,
                'trigger': '异常'
            }

    def _make_enhanced_flat_open_decision(self, current_change: float, profit_line: float,
                                        volume_analysis: Dict, resilience_analysis: Dict,
                                        danger_patterns: Dict, sector_analysis: Dict,
                                        time_analysis: Dict, total_score: float) -> Dict:
        """增强版平开股决策逻辑（基于报告标准）"""
        try:
            # 1. 硬性风控：动态止盈线
            if current_change <= profit_line:
                return {
                    'action': '止盈',
                    'reason': f'跌破动态止盈线({profit_line:.1%})，立即止盈',
                    'confidence': 95,
                    'trigger': '止盈线'
                }

            # 2. 关键条件：10:00未翻红
            if time_analysis.get('is_after_10', False) and not time_analysis.get('is_red', False):
                return {
                    'action': '止盈',
                    'reason': '10:00后未翻红，平开股风险加大',
                    'confidence': 80,
                    'trigger': '10点未翻红'
                }

            # 3. 危险形态一票否决
            if danger_patterns.get('high_danger', False):
                patterns = danger_patterns.get('patterns', [])
                return {
                    'action': '止盈',
                    'reason': f'检测到危险形态: {"; ".join(patterns)}',
                    'confidence': 85,
                    'trigger': '危险形态'
                }

            # 4. 报告标准条件检查
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            avg_line_relation = resilience_analysis.get('avg_line_relation', 0)
            sector_score = sector_analysis.get('anchoring_score', 0)

            # 持有条件：量比>1.8 + 站稳昨日涨停价 + 内外盘比<1.2（简化为均价线上方）
            hold_conditions = {
                'volume_ratio_ok': volume_ratio >= 1.8,
                'above_avg_line': avg_line_relation > 0,  # 站稳均价线
                'sector_support': sector_score > 0,
                'resilience_ok': resilience_analysis.get('resilience_score', 0) > 30
            }

            # 止盈条件：卖五总量>买五50% + 筹码峰下移（简化为板块弱势+量能衰减）
            sell_conditions = {
                'volume_weak': volume_ratio < 1.5,
                'sector_weak': sector_score < -15,
                'resilience_poor': resilience_analysis.get('resilience_score', 0) < 20,
                'time_risk': time_analysis.get('time_score', 0) < 0
            }

            # 决策逻辑
            hold_score = sum(hold_conditions.values())
            sell_score = sum(sell_conditions.values())

            if hold_score >= 3:  # 平开股要求更严格
                confidence = min(65 + total_score * 0.15, 80)
                return {
                    'action': '持有',
                    'reason': f'持有条件满足({hold_score}/4)，综合评分{total_score:.0f}',
                    'confidence': int(confidence),
                    'trigger': '持有条件'
                }
            elif sell_score >= 1:  # 平开股更谨慎，任一止盈条件即考虑
                confidence = min(55 + sell_score * 20, 80)
                return {
                    'action': '止盈',
                    'reason': f'止盈信号出现({sell_score}/4)，建议止盈',
                    'confidence': int(confidence),
                    'trigger': '止盈条件'
                }
            else:
                return {
                    'action': '观望',
                    'reason': f'信号不明确，持有{hold_score}/4，止盈{sell_score}/4',
                    'confidence': 35,
                    'trigger': '信号不明'
                }

        except Exception as e:
            logger.error(f"增强版平开股决策失败: {e}")
            return {
                'action': '观望',
                'reason': '决策异常',
                'confidence': 20,
                'trigger': '异常'
            }
    
    def _analyze_flat_open_sell_point(self, code: str, minute_data: pd.DataFrame,
                                     history_data: pd.DataFrame, open_info: Dict) -> Dict:
        """平开股卖点分析（开盘涨跌幅-1%~+1%）- 增强版"""
        try:
            logger.info(f"🔴 平开股增强版卖点分析: {code}")

            open_change = open_info.get('open_change_pct', 0)
            current_change = open_info.get('current_change_pct', 0)
            profit_line = open_change + self.profit_line_rate

            # 1. 量能结构分析
            volume_analysis = self._calculate_volume_ratio_enhanced(minute_data, history_data)

            # 2. 价格韧性评分
            resilience_analysis = self._analyze_price_resilience(minute_data, open_info, 'flat')

            # 3. 危险形态识别
            danger_patterns = self._detect_dangerous_patterns(minute_data)

            # 4. 板块锚定效应
            sector_analysis = self._analyze_sector_anchoring(code, open_info, 'flat')

            # 5. 时间节点控制
            time_analysis = self._check_time_nodes(minute_data, open_info)

            # 综合评分
            total_score = (
                volume_analysis['health_score'] * 0.3 +
                resilience_analysis['resilience_score'] * 0.25 +
                sector_analysis['anchoring_score'] * 0.2 +
                time_analysis['time_score'] * 0.25
            )

            # 增强版决策逻辑
            decision = self._make_enhanced_flat_open_decision(
                current_change, profit_line, volume_analysis, resilience_analysis,
                danger_patterns, sector_analysis, time_analysis, total_score
            )
            
            return {
                'code': code,
                'analysis_type': 'flat_open_sell_enhanced',
                'open_info': open_info,
                'decision': decision,
                'total_score': total_score,
                'key_indicators': {
                    '开盘涨幅': f'{open_change:.1%}',
                    '当前涨幅': f'{current_change:.1%}',
                    '动态止盈线': f'{profit_line:.1%}',
                    '量比': f'{volume_analysis["volume_ratio"]:.1f}',
                    '最大回撤': f'{resilience_analysis["max_drawdown"]:.1%}',
                    '板块涨幅': f'{sector_analysis["sector_gain"]:.1%}',
                    '是否翻红': '是' if time_analysis.get('is_red', False) else '否',
                    '当前时间': time_analysis.get('current_time', '未知'),
                    '综合评分': f'{total_score:.0f}分'
                },
                'detailed_analysis': {
                    'volume_analysis': volume_analysis,
                    'resilience_analysis': resilience_analysis,
                    'danger_patterns': danger_patterns,
                    'sector_analysis': sector_analysis,
                    'time_analysis': time_analysis
                },
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"平开股卖点分析失败 {code}: {e}")
            return self._create_error_result(code, 'flat_open_sell', str(e))
    
    def _analyze_low_open_sell_point(self, code: str, minute_data: pd.DataFrame,
                                    history_data: pd.DataFrame, open_info: Dict) -> Dict:
        """低开股卖点分析（开盘跌幅<-1%）- 增强版"""
        try:
            logger.info(f"🔴 低开股增强版卖点分析: {code}")

            open_change = open_info.get('open_change_pct', 0)
            current_change = open_info.get('current_change_pct', 0)
            profit_line = open_change + self.profit_line_rate

            # 1. 量能结构分析
            volume_analysis = self._calculate_volume_ratio_enhanced(minute_data, history_data)

            # 2. 价格韧性评分
            resilience_analysis = self._analyze_price_resilience(minute_data, open_info, 'low')

            # 3. 危险形态识别
            danger_patterns = self._detect_dangerous_patterns(minute_data)

            # 4. 板块锚定效应
            sector_analysis = self._analyze_sector_anchoring(code, open_info, 'low')

            # 5. 时间节点控制
            time_analysis = self._check_time_nodes(minute_data, open_info)

            # 综合评分
            total_score = (
                volume_analysis['health_score'] * 0.3 +
                resilience_analysis['resilience_score'] * 0.25 +
                sector_analysis['anchoring_score'] * 0.2 +
                time_analysis['time_score'] * 0.25
            )

            # 增强版决策逻辑
            decision = self._make_enhanced_low_open_decision(
                current_change, profit_line, volume_analysis, resilience_analysis,
                danger_patterns, sector_analysis, time_analysis, total_score
            )
            
            return {
                'code': code,
                'analysis_type': 'low_open_sell_enhanced',
                'open_info': open_info,
                'decision': decision,
                'total_score': total_score,
                'key_indicators': {
                    '开盘涨幅': f'{open_change:.1%}',
                    '当前涨幅': f'{current_change:.1%}',
                    '动态止盈线': f'{profit_line:.1%}',
                    '量比': f'{volume_analysis["volume_ratio"]:.1f}',
                    '最大回撤': f'{resilience_analysis["max_drawdown"]:.1%}',
                    '板块涨幅': f'{sector_analysis["sector_gain"]:.1%}',
                    '反抽强度': f'{resilience_analysis["rebound_strength"]:.1%}',
                    '综合评分': f'{total_score:.0f}分'
                },
                'detailed_analysis': {
                    'volume_analysis': volume_analysis,
                    'resilience_analysis': resilience_analysis,
                    'danger_patterns': danger_patterns,
                    'sector_analysis': sector_analysis,
                    'time_analysis': time_analysis
                },
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }

        except Exception as e:
            logger.error(f"低开股卖点分析失败 {code}: {e}")
            return self._create_error_result(code, 'low_open_sell', str(e))

    def _make_enhanced_low_open_decision(self, current_change: float, profit_line: float,
                                       volume_analysis: Dict, resilience_analysis: Dict,
                                       danger_patterns: Dict, sector_analysis: Dict,
                                       time_analysis: Dict, total_score: float) -> Dict:
        """增强版低开股决策逻辑（基于报告标准）"""
        try:
            # 1. 硬性风控：动态止盈线
            if current_change <= profit_line:
                return {
                    'action': '止盈',
                    'reason': f'跌破动态止盈线({profit_line:.1%})，立即止盈',
                    'confidence': 95,
                    'trigger': '止盈线'
                }

            # 2. 反抽力度检测：反抽未触及-0.5%
            if current_change < -0.005:
                return {
                    'action': '止盈',
                    'reason': '反抽未触及-0.5%，低开股风险加大',
                    'confidence': 85,
                    'trigger': '反抽不足'
                }

            # 3. 危险形态一票否决
            if danger_patterns.get('high_danger', False):
                patterns = danger_patterns.get('patterns', [])
                return {
                    'action': '止盈',
                    'reason': f'检测到危险形态: {"; ".join(patterns)}',
                    'confidence': 90,
                    'trigger': '危险形态'
                }

            # 4. 报告标准条件检查
            volume_ratio = volume_analysis.get('volume_ratio', 0)
            rebound_strength = resilience_analysis.get('rebound_strength', 0)
            sector_score = sector_analysis.get('anchoring_score', 0)

            # 持有条件：量比>1.5 + 低点抬高 + 买五档突增>2000手（简化为板块率先翻红）
            hold_conditions = {
                'volume_ratio_ok': volume_ratio >= 1.5,
                'rebound_strong': rebound_strength > 0.01,  # 反弹>1%
                'sector_lead': sector_score > 0,  # 板块率先翻红
                'resilience_ok': resilience_analysis.get('resilience_score', 0) > 25
            }

            # 止盈条件：放量跌破前低 + 板块跌幅>1%
            sell_conditions = {
                'volume_breakdown': danger_patterns.get('ladder_down', {}).get('detected', False),
                'sector_weak': sector_score < -20,  # 板块大幅下跌
                'resilience_poor': resilience_analysis.get('resilience_score', 0) < 15,
                'time_risk': time_analysis.get('time_score', 0) < -20
            }

            # 决策逻辑（低开股更严格）
            hold_score = sum(hold_conditions.values())
            sell_score = sum(sell_conditions.values())

            if hold_score >= 3 and sell_score == 0:  # 必须满足大部分持有条件且无止盈信号
                confidence = min(60 + total_score * 0.1, 75)
                return {
                    'action': '持有',
                    'reason': f'低开股持有条件充分({hold_score}/4)，谨慎持有',
                    'confidence': int(confidence),
                    'trigger': '持有条件'
                }
            elif sell_score >= 1 or hold_score < 2:  # 任何止盈信号或持有条件不足
                confidence = min(70 + sell_score * 10, 90)
                return {
                    'action': '止盈',
                    'reason': f'低开股风险较高，建议止盈',
                    'confidence': int(confidence),
                    'trigger': '风险控制'
                }
            else:
                return {
                    'action': '观望',
                    'reason': '低开股信号复杂，建议观望',
                    'confidence': 30,
                    'trigger': '信号复杂'
                }

        except Exception as e:
            logger.error(f"增强版低开股决策失败: {e}")
            return {
                'action': '观望',
                'reason': '决策异常',
                'confidence': 20,
                'trigger': '异常'
            }
            
        except Exception as e:
            logger.error(f"低开股卖点分析失败 {code}: {e}")
            return self._create_error_result(code, 'low_open_sell', str(e))
    
    def _analyze_unknown_sell_point(self, code: str, minute_data: pd.DataFrame,
                                   history_data: pd.DataFrame, open_info: Dict) -> Dict:
        """未知类型股票的基础卖点分析"""
        try:
            current_change = open_info.get('current_change_pct', 0)
            profit_line = current_change + self.profit_line_rate
            
            decision = {
                'action': '观望',
                'reason': f'开盘类型未知({open_info.get("type_desc", "未知")})，建议观望',
                'confidence': 30
            }
            
            return {
                'code': code,
                'analysis_type': 'unknown_sell',
                'open_info': open_info,
                'decision': decision,
                'key_indicators': {
                    '开盘类型': open_info.get('type_desc', '未知'),
                    '当前涨幅': f'{current_change:.1%}',
                    '动态止盈线': f'{profit_line:.1%}'
                },
                'analysis_time': datetime.now().strftime('%H:%M:%S')
            }
            
        except Exception as e:
            logger.error(f"未知类型卖点分析失败 {code}: {e}")
            return self._create_error_result(code, 'unknown_sell', str(e))
    
    def _create_error_result(self, code: str, analysis_type: str, error_msg: str) -> Dict:
        """创建错误结果"""
        return {
            'code': code,
            'analysis_type': analysis_type,
            'error': error_msg,
            'decision': {
                'action': '观望',
                'reason': f'分析异常: {error_msg}',
                'confidence': 0
            },
            'key_indicators': {
                '状态': '分析失败'
            },
            'analysis_time': datetime.now().strftime('%H:%M:%S')
        }

    def _print_sell_analysis_result(self, result: Dict):
        """打印卖点分析结果"""
        try:
            code = result.get('code', 'Unknown')
            decision = result.get('decision', {})
            action = decision.get('action', '观望')
            confidence = decision.get('confidence', 0)

            # 基础信息
            open_info = result.get('open_info', {})
            open_type = open_info.get('type_desc', '未知')
            open_change = open_info.get('open_change_pct', 0)
            current_change = open_info.get('current_change_pct', 0)

            # 检查是否为涨停股分析
            analysis_type = result.get('analysis_type', '')
            if analysis_type == 'limit_up_sell_enhanced':
                limit_up_status = result.get('key_indicators', {}).get('涨停状态', '涨停')
                print(f"📊 {code} | 开盘: {open_change:.1%}({open_type}) | 当前: {current_change:.1%} | 🔥 {limit_up_status}")
            elif analysis_type == 'limit_board_enhanced':
                stock_type = result.get('key_indicators', {}).get('股票类型', '一字板')
                print(f"📊 {code} | 开盘: {open_change:.1%}({open_type}) | 当前: {current_change:.1%} | 📋 {stock_type}")
            else:
                print(f"📊 {code} | 开盘: {open_change:.1%}({open_type}) | 当前: {current_change:.1%}")

            # 决策结果
            action_emoji = {'持有': '✅', '止盈': '🔴', '观望': '⏸️', '卖出': '🔴'}.get(action, '❓')
            trigger = decision.get('trigger', '未知')
            print(f"{action_emoji} 决策: {action} | 置信度: {confidence}% | 触发: {trigger}")
            print(f"💡 理由: {decision.get('reason', '无')}")

            # 关键指标
            if 'key_indicators' in result:
                indicators = result['key_indicators']
                print(f"📈 关键指标:")
                for key, value in indicators.items():
                    print(f"    {key}: {value}")

            # 增强版分析详情
            if 'detailed_analysis' in result:
                detailed = result['detailed_analysis']
                print(f"🔍 详细分析:")

                # 涨停股专用分析显示
                if analysis_type == 'limit_up_sell_enhanced':
                    # 普通涨停股分析显示
                    # 涨停股量能分析（增强版）
                    if 'volume_analysis' in detailed:
                        vol_signals = detailed['volume_analysis'].get('signals', [])
                        power_ratio = detailed['volume_analysis'].get('power_ratio', 0)
                        print(f"    量能: {'; '.join(vol_signals[:3])} | 多空比{power_ratio:.1f}")

                    # 涨停股筹码分析
                    if 'chip_analysis' in detailed:
                        chip_signals = detailed['chip_analysis'].get('signals', [])
                        chip_available = detailed['chip_analysis'].get('available', False)
                        if chip_available and chip_signals:
                            print(f"    筹码: {'; '.join(chip_signals[:3])}")
                        elif chip_available:
                            print(f"    筹码: 分析完成，评分{detailed['chip_analysis'].get('chip_score', 0)}分")
                        else:
                            print(f"    筹码: 数据不可用")

                    # 涨停板出货识别（新增）
                    if 'limit_break_analysis' in detailed:
                        break_count = detailed['limit_break_analysis'].get('break_count', 0)
                        danger_signals = detailed['limit_break_analysis'].get('danger_signals', [])
                        seal_change = detailed['limit_break_analysis'].get('seal_change_ratio', 0)
                        if danger_signals:
                            print(f"    🚫 出货识别: {'; '.join(danger_signals[:2])}")
                        else:
                            print(f"    ✅ 出货识别: 开板{break_count}次，封单{seal_change:+.1%}")

                    # 涨停股风险检测（增强版）
                    if 'limit_up_risks' in detailed:
                        risks = detailed['limit_up_risks'].get('risks', [])
                        limit_break_included = detailed['limit_up_risks'].get('limit_break_risks_included', False)
                        chip_risks_included = detailed['limit_up_risks'].get('chip_risks_included', False)
                        risk_desc = "完整版" if (chip_risks_included and limit_break_included) else "增强版"
                        if risks:
                            print(f"    ⚠️ 涨停股风险({risk_desc}): {'; '.join(risks[:2])}")
                        else:
                            print(f"    ✅ 涨停股风险({risk_desc}): 无明显风险")

                    # 板块分析
                    if 'sector_analysis' in detailed:
                        sector_desc = detailed['sector_analysis'].get('description', '')
                        if sector_desc:
                            print(f"    板块: {sector_desc}")

                    # 时间节点
                    if 'time_analysis' in detailed:
                        time_desc = detailed['time_analysis'].get('description', '')
                        if time_desc:
                            print(f"    时间: {time_desc}")

                else:
                    # 检查是否为一字板股票
                    if analysis_type == 'limit_board_enhanced':
                        # 一字板股票专用分析显示
                        print(f"    📋 股票类型: 一字板涨停（数据特殊处理）")

                        # 一字板量能分析
                        if 'volume_analysis' in detailed:
                            volume_ratio = detailed['volume_analysis'].get('volume_ratio', 0)
                            print(f"    量能: 量比{volume_ratio:.1f}（一字板参考价值有限）")

                        # 一字板筹码分析
                        if 'chip_analysis' in detailed:
                            chip_available = detailed['chip_analysis'].get('available', False)
                            if chip_available:
                                chip_score = detailed['chip_analysis'].get('chip_score', 0)
                                print(f"    筹码: 评分{chip_score}分（一字板主要参考指标）")
                            else:
                                print(f"    筹码: 数据不可用")

                        # 一字板板块分析
                        if 'sector_analysis' in detailed:
                            sector_desc = detailed['sector_analysis'].get('description', '')
                            if sector_desc:
                                print(f"    板块: {sector_desc}（一字板重要支撑）")

                        # 一字板特殊说明
                        if detailed.get('limit_board_detected', False):
                            print(f"    ℹ️ 说明: 一字板股票不适用多空力道比和封单变化分析")

                    else:
                        # 普通股票分析显示
                        # 量能分析
                        if 'volume_analysis' in detailed:
                            vol_signals = detailed['volume_analysis'].get('signals', [])
                            print(f"    量能: {'; '.join(vol_signals[:2])}")  # 显示前2个信号

                    # 价格韧性
                    if 'resilience_analysis' in detailed:
                        res_signals = detailed['resilience_analysis'].get('signals', [])
                        print(f"    韧性: {'; '.join(res_signals[:2])}")  # 显示前2个信号

                    # 危险形态
                    if 'danger_patterns' in detailed:
                        patterns = detailed['danger_patterns'].get('patterns', [])
                        if patterns:
                            print(f"    ⚠️ 危险形态: {'; '.join(patterns)}")

                    # 板块分析
                    if 'sector_analysis' in detailed:
                        sector_desc = detailed['sector_analysis'].get('description', '')
                        if sector_desc:
                            print(f"    板块: {sector_desc}")

                    # 时间节点
                    if 'time_analysis' in detailed:
                        time_desc = detailed['time_analysis'].get('description', '')
                        if time_desc:
                            print(f"    时间: {time_desc}")



            print("-" * 60)

        except Exception as e:
            logger.error(f"打印结果失败: {e}")
            print(f"❌ 结果显示异常: {e}")

    def _generate_sell_summary_report(self, results: List[Dict]):
        """生成卖点分析汇总报告"""
        try:
            print("\n" + "🔴" * 80)
            print("🔴 连板潜力股实时卖点分析汇总报告")
            print("🔴" + "=" * 78)

            if not results:
                print("❌ 没有有效的分析结果")
                return

            # 统计决策分布
            hold_stocks = [r for r in results if r.get('decision', {}).get('action') == '持有']
            sell_stocks = [r for r in results if r.get('decision', {}).get('action') == '止盈']
            watch_stocks = [r for r in results if r.get('decision', {}).get('action') == '观望']

            print(f"\n📊 决策分布:")
            print(f"✅ 持有建议: {len(hold_stocks)} 只")
            print(f"🔴 止盈建议: {len(sell_stocks)} 只")
            print(f"⏸️ 观望建议: {len(watch_stocks)} 只")

            # 按决策类型分组显示
            if hold_stocks:
                print(f"\n✅ 建议持有的股票 ({len(hold_stocks)}只):")
                for i, stock in enumerate(hold_stocks, 1):
                    decision = stock.get('decision', {})
                    key_indicators = stock.get('key_indicators', {})
                    print(f"{i:2d}. {stock['code']} | 置信度: {decision.get('confidence', 0)}%")
                    print(f"    理由: {decision.get('reason', '无')}")

                    # 显示关键指标
                    current_change = key_indicators.get('当前涨幅', '未知')
                    profit_line = key_indicators.get('动态止盈线', '未知')
                    print(f"    当前涨幅: {current_change} | 止盈线: {profit_line}")

            if sell_stocks:
                print(f"\n🔴 建议止盈的股票 ({len(sell_stocks)}只):")
                for i, stock in enumerate(sell_stocks, 1):
                    decision = stock.get('decision', {})
                    key_indicators = stock.get('key_indicators', {})
                    print(f"{i:2d}. {stock['code']} | 置信度: {decision.get('confidence', 0)}%")
                    print(f"    理由: {decision.get('reason', '无')}")

                    # 显示关键指标
                    current_change = key_indicators.get('当前涨幅', '未知')
                    profit_line = key_indicators.get('动态止盈线', '未知')
                    print(f"    当前涨幅: {current_change} | 止盈线: {profit_line}")

            if watch_stocks:
                print(f"\n⏸️ 建议观望的股票 ({len(watch_stocks)}只):")
                for i, stock in enumerate(watch_stocks, 1):
                    decision = stock.get('decision', {})
                    print(f"{i:2d}. {stock['code']} | {decision.get('reason', '无明确信号')}")

            # 风险提示
            print(f"\n⚠️ 风险提示:")
            print(f"1. 动态止盈线为成本价+5%，跌破此线建议立即止盈")
            print(f"2. 决策基于开盘后实时数据，市场变化快速，请及时关注")
            print(f"3. 本分析仅供参考，投资决策请结合个人风险承受能力")

            print("🔴" + "=" * 78)

        except Exception as e:
            logger.error(f"生成汇总报告失败: {e}")
            print(f"❌ 汇总报告生成失败: {e}")

    def _generate_sell_report_file(self, results: List[Dict], stock_codes: List[str]) -> str:
        """生成卖点分析TXT报告文件"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            target_date = datetime.now().strftime('%Y%m%d')
            report_file = f"连板潜力股实时卖点报告_{target_date}_{timestamp}.txt"

            with open(report_file, 'w', encoding='utf-8') as f:
                # 报告头部
                f.write("=" * 80 + "\n")
                f.write("🔴 连板潜力股实时卖点分析报告 (增强版)\n")
                f.write("=" * 80 + "\n")
                f.write(f"📅 分析日期: {target_date}\n")
                f.write(f"⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"📊 分析股票数: {len(stock_codes)}只\n")
                f.write(f"✅ 成功分析: {len(results)}只\n")
                f.write(f"❌ 分析失败: {len(stock_codes) - len(results)}只\n")
                f.write("\n")

                if not results:
                    f.write("⚠️ 未获得有效分析结果\n")
                    return report_file

                # 决策分布统计
                hold_stocks = [r for r in results if r.get('decision', {}).get('action') == '持有']
                sell_stocks = [r for r in results if r.get('decision', {}).get('action') == '止盈']
                watch_stocks = [r for r in results if r.get('decision', {}).get('action') == '观望']

                f.write("📊 决策分布统计\n")
                f.write("-" * 80 + "\n")
                f.write(f"✅ 持有建议: {len(hold_stocks)} 只 ({len(hold_stocks)/len(results)*100:.1f}%)\n")
                f.write(f"🔴 止盈建议: {len(sell_stocks)} 只 ({len(sell_stocks)/len(results)*100:.1f}%)\n")
                f.write(f"⏸️ 观望建议: {len(watch_stocks)} 只 ({len(watch_stocks)/len(results)*100:.1f}%)\n")
                f.write("\n")

                # 详细分析结果
                f.write("📈 详细分析结果\n")
                f.write("-" * 80 + "\n")

                for i, result in enumerate(results, 1):
                    self._write_stock_analysis_section(f, result, i)

                # 风险提示
                f.write("\n")
                f.write("⚠️ 风险提示\n")
                f.write("-" * 80 + "\n")
                f.write("1. 动态止盈线为成本价+5%，跌破此线建议立即止盈\n")
                f.write("2. 决策基于开盘后实时数据，市场变化快速，请及时关注\n")
                f.write("3. 本分析仅供参考，投资决策请结合个人风险承受能力\n")
                f.write("4. 增强版五维决策模型：量能结构+价格韧性+危险形态+板块锚定+时间节点\n")
                f.write("5. 高开股量比>2.5，平开股量比>1.8，低开股量比>1.5为基础持有条件\n")

                f.write("\n")
                f.write("=" * 80 + "\n")
                f.write("🔴 报告生成完成 - 连板潜力股实时卖点分析器\n")
                f.write("=" * 80 + "\n")

            return report_file

        except Exception as e:
            logger.error(f"生成卖点分析报告失败: {e}")
            return f"报告生成失败: {e}"

    def _write_stock_analysis_section(self, f, result: Dict, index: int):
        """写入单个股票的分析结果到报告文件"""
        try:
            code = result.get('code', 'Unknown')
            decision = result.get('decision', {})
            action = decision.get('action', '观望')
            confidence = decision.get('confidence', 0)
            trigger = decision.get('trigger', '未知')

            # 基础信息
            open_info = result.get('open_info', {})
            analysis_type = result.get('analysis_type', 'unknown')
            total_score = result.get('total_score', 0)

            # 算法类型描述
            type_desc = {
                'high_open_sell_enhanced': '高开股增强版',
                'flat_open_sell_enhanced': '平开股增强版',
                'low_open_sell_enhanced': '低开股增强版',
                'unknown_sell': '未知类型'
            }.get(analysis_type, '基础版')

            f.write(f"{index:2d}. {code} | 决策: {action} | 置信度: {confidence}% | 算法: {type_desc}\n")
            f.write(f"    触发原因: {trigger} | 综合评分: {total_score:.0f}分\n")
            f.write(f"    理由: {decision.get('reason', '无')}\n")

            # 关键指标
            if 'key_indicators' in result:
                indicators = result['key_indicators']
                f.write(f"    关键指标:\n")
                for key, value in indicators.items():
                    f.write(f"      {key}: {value}\n")

            # 详细分析（如果有）
            if 'detailed_analysis' in result:
                detailed = result['detailed_analysis']
                f.write(f"    详细分析:\n")

                # 量能分析
                if 'volume_analysis' in detailed:
                    vol_signals = detailed['volume_analysis'].get('signals', [])
                    if vol_signals:
                        f.write(f"      量能: {'; '.join(vol_signals[:2])}\n")

                # 价格韧性
                if 'resilience_analysis' in detailed:
                    res_signals = detailed['resilience_analysis'].get('signals', [])
                    if res_signals:
                        f.write(f"      韧性: {'; '.join(res_signals[:2])}\n")

                # 危险形态
                if 'danger_patterns' in detailed:
                    patterns = detailed['danger_patterns'].get('patterns', [])
                    if patterns:
                        f.write(f"      危险形态: {'; '.join(patterns)}\n")

                # 板块分析
                if 'sector_analysis' in detailed:
                    sector_desc = detailed['sector_analysis'].get('description', '')
                    if sector_desc:
                        f.write(f"      板块: {sector_desc}\n")

                # 时间节点
                if 'time_analysis' in detailed:
                    time_desc = detailed['time_analysis'].get('description', '')
                    if time_desc:
                        f.write(f"      时间: {time_desc}\n")

            f.write("\n")

        except Exception as e:
            logger.error(f"写入股票分析结果失败: {e}")
            f.write(f"    ❌ 结果写入异常: {e}\n\n")


    def _get_chip_distribution_analysis(self, code: str) -> Dict:
        """获取筹码分布分析数据"""
        try:
            if not self.chip_analyzer:
                return {
                    'available': False,
                    'error': '筹码分析器不可用',
                    'chip_distribution': {},
                    'profit_loss_analysis': {},
                    'main_cost_analysis': {},
                    'concentration_analysis': {}
                }

            logger.info(f"获取{code}的筹码分布分析")
            chip_result = self.chip_analyzer.get_chip_distribution_analysis(code)

            if chip_result.get('data_available', False):
                return {
                    'available': True,
                    'chip_distribution': chip_result.get('chip_distribution', {}),
                    'profit_loss_analysis': chip_result.get('profit_loss_analysis', {}),
                    'main_cost_analysis': chip_result.get('main_cost_analysis', {}),
                    'concentration_analysis': chip_result.get('concentration_analysis', {}),
                    'trend_analysis': chip_result.get('trend_analysis', {}),
                    'current_price': chip_result.get('current_price', 0),
                    'analysis_date': chip_result.get('analysis_date', '')
                }
            else:
                return {
                    'available': False,
                    'error': chip_result.get('error', '筹码分析失败'),
                    'chip_distribution': {},
                    'profit_loss_analysis': {},
                    'main_cost_analysis': {},
                    'concentration_analysis': {}
                }

        except Exception as e:
            logger.error(f"获取筹码分布分析失败: {e}")
            return {
                'available': False,
                'error': str(e),
                'chip_distribution': {},
                'profit_loss_analysis': {},
                'main_cost_analysis': {},
                'concentration_analysis': {}
            }

    def _analyze_limit_up_chip_distribution(self, code: str, current_price: float, limit_up_price: float) -> Dict:
        """涨停股筹码分布专用分析（权重20%）"""
        try:
            # 获取筹码分布数据
            chip_data = self._get_chip_distribution_analysis(code)

            if not chip_data.get('available', False):
                return {
                    'chip_score': 0,
                    'signals': [f'筹码数据不可用: {chip_data.get("error", "未知错误")}'],
                    'limit_up_chip_peak': False,
                    'upper_chip_gap': False,
                    'bottom_chip_migration': False,
                    'main_cost_relation': 'unknown'
                }

            chip_distribution = chip_data.get('chip_distribution', {})
            main_cost_analysis = chip_data.get('main_cost_analysis', {})
            profit_loss_analysis = chip_data.get('profit_loss_analysis', {})

            chip_score = 0
            signals = []

            # 1. 涨停价筹码峰检测
            limit_up_chip_peak = self._check_limit_up_chip_peak(chip_distribution, limit_up_price)
            if limit_up_chip_peak['has_peak']:
                chip_score += 30
                signals.append('✅ 涨停价筹码峰锁定')
            else:
                signals.append('❌ 涨停价无筹码峰')

            # 2. 上方筹码缺口检测
            upper_chip_gap = self._check_upper_chip_gap(chip_distribution, current_price)
            if upper_chip_gap['has_gap']:
                chip_score += 25
                signals.append('✅ 上方筹码缺口(看涨)')
            else:
                signals.append('❌ 上方存在筹码压力')

            # 3. 底部筹码迁移检测
            bottom_chip_migration = self._check_bottom_chip_migration(chip_data)
            if bottom_chip_migration['migration_detected']:
                signals.append('🚫 底部筹码快速上移(出货)')
                chip_score -= 20
            else:
                chip_score += 15
                signals.append('✅ 底部筹码稳定')

            # 4. 主力成本关系分析
            main_cost_relation = self._analyze_main_cost_relation(main_cost_analysis, current_price)
            if main_cost_relation['relation'] == 'above_cost':
                chip_score += 10
                signals.append('✅ 高于主力成本区')
            elif main_cost_relation['relation'] == 'in_cost':
                signals.append('⚠️ 处于主力成本区')
            else:
                signals.append('❌ 低于主力成本区')

            return {
                'chip_score': chip_score,
                'signals': signals,
                'limit_up_chip_peak': limit_up_chip_peak,
                'upper_chip_gap': upper_chip_gap,
                'bottom_chip_migration': bottom_chip_migration,
                'main_cost_relation': main_cost_relation,
                'profit_ratio': profit_loss_analysis.get('profit_chips_ratio', 0),
                'description': f'筹码评分{chip_score}分'
            }

        except Exception as e:
            logger.error(f"涨停股筹码分析失败: {e}")
            return {
                'chip_score': 0,
                'signals': ['筹码分析异常'],
                'limit_up_chip_peak': False,
                'upper_chip_gap': False,
                'bottom_chip_migration': False,
                'main_cost_relation': 'unknown'
            }


    def _check_limit_up_chip_peak(self, chip_distribution: Dict, limit_up_price: float) -> Dict:
        """检查涨停价筹码峰"""
        try:
            if not chip_distribution:
                return {'has_peak': False, 'peak_ratio': 0}

            # 涨停价附近±2%范围内的筹码比例
            price_range = limit_up_price * 0.02
            peak_chips = 0

            for price_str, chip_ratio in chip_distribution.items():
                try:
                    price = float(price_str)
                    if abs(price - limit_up_price) <= price_range:
                        peak_chips += chip_ratio
                except (ValueError, TypeError):
                    continue

            # 筹码峰标准：涨停价附近筹码比例>15%
            has_peak = peak_chips > 0.15

            return {
                'has_peak': has_peak,
                'peak_ratio': peak_chips,
                'description': f'涨停价附近筹码{peak_chips:.1%}'
            }

        except Exception as e:
            logger.error(f"涨停价筹码峰检测失败: {e}")
            return {'has_peak': False, 'peak_ratio': 0}

    def _check_upper_chip_gap(self, chip_distribution: Dict, current_price: float) -> Dict:
        """检查上方筹码缺口"""
        try:
            if not chip_distribution:
                return {'has_gap': False, 'gap_size': 0}

            # 检查当前价格上方5%范围内的筹码分布
            upper_range = current_price * 1.05
            upper_chips = 0

            for price_str, chip_ratio in chip_distribution.items():
                try:
                    price = float(price_str)
                    if current_price < price <= upper_range:
                        upper_chips += chip_ratio
                except (ValueError, TypeError):
                    continue

            # 缺口标准：上方5%范围内筹码<5%
            has_gap = upper_chips < 0.05

            return {
                'has_gap': has_gap,
                'upper_chips': upper_chips,
                'gap_size': 0.05 - upper_chips if has_gap else 0,
                'description': f'上方5%筹码{upper_chips:.1%}'
            }

        except Exception as e:
            logger.error(f"上方筹码缺口检测失败: {e}")
            return {'has_gap': False, 'gap_size': 0}

    def _check_bottom_chip_migration(self, chip_data: Dict) -> Dict:
        """检查底部筹码迁移"""
        try:
            trend_analysis = chip_data.get('trend_analysis', {})
            concentration_analysis = chip_data.get('concentration_analysis', {})

            # 简化版：基于筹码集中度变化判断
            concentration_change = concentration_analysis.get('concentration_change', 0)

            # 迁移检测：筹码集中度快速下降>20%
            migration_detected = concentration_change < -0.2

            return {
                'migration_detected': migration_detected,
                'concentration_change': concentration_change,
                'description': f'筹码集中度变化{concentration_change:.1%}'
            }

        except Exception as e:
            logger.error(f"底部筹码迁移检测失败: {e}")
            return {'migration_detected': False, 'concentration_change': 0}

    def _analyze_main_cost_relation(self, main_cost_analysis: Dict, current_price: float) -> Dict:
        """分析主力成本关系"""
        try:
            main_cost_range = main_cost_analysis.get('main_cost_range', {})
            avg_cost = main_cost_range.get('avg_cost', 0)
            min_cost = main_cost_range.get('min_cost', 0)
            max_cost = main_cost_range.get('max_cost', 0)

            if avg_cost == 0:
                return {'relation': 'unknown', 'cost_distance': 0}

            # 判断当前价格与主力成本的关系
            if current_price > max_cost:
                relation = 'above_cost'
            elif current_price < min_cost:
                relation = 'below_cost'
            else:
                relation = 'in_cost'

            cost_distance = (current_price - avg_cost) / avg_cost if avg_cost > 0 else 0

            return {
                'relation': relation,
                'avg_cost': avg_cost,
                'cost_distance': cost_distance,
                'description': f'距主力成本{cost_distance:+.1%}'
            }

        except Exception as e:
            logger.error(f"主力成本关系分析失败: {e}")
            return {'relation': 'unknown', 'cost_distance': 0}


def main():
    """主程序入口"""
    try:
        print("🔴" + "=" * 50)
        print("🔴 连板潜力股实时卖点分析器")
        print("🔴 基于分时数据的涨停股持有决策系统")
        print("🔴" + "=" * 50)

        if not REAL_DATA_AVAILABLE:
            print("❌ 数据接口不可用，请检查数据源配置")
            return

        # 创建分析器实例
        analyzer = SellPointAnalyzer()

        # 获取用户输入的股票代码
        print("\n📝 请输入股票代码（一行一个，输入完成后按回车）:")
        print("💡 示例:")
        print("002082")
        print("603045")
        print("603109")
        print("\n请输入股票代码:")

        stock_codes = []
        while True:
            try:
                line = input().strip()
                if not line:  # 空行表示输入结束
                    break
                if len(line) == 6 and line.isdigit():
                    stock_codes.append(line)
                    print(f"✅ 已添加: {line}")
                else:
                    print(f"⚠️ 无效代码: {line}，请输入6位数字")
            except KeyboardInterrupt:
                print("\n\n👋 用户取消操作")
                return
            except EOFError:
                break

        if not stock_codes:
            print("❌ 未输入有效的股票代码")
            return

        print(f"\n🎯 准备分析 {len(stock_codes)} 只股票的卖点信号")

        # 执行卖点分析
        results = analyzer.analyze_stocks(stock_codes)

        # 生成汇总报告
        if results:
            analyzer._generate_sell_summary_report(results)
            # 生成并保存TXT报告文件
            report_file = analyzer._generate_sell_report_file(results, stock_codes)
            print(f"\n📄 分析报告已保存至: {report_file}")
        else:
            print("\n❌ 没有获得有效的分析结果")

    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        logger.error(f"主程序异常: {e}")
        print(f"❌ 程序异常: {e}")


if __name__ == "__main__":
    main()
