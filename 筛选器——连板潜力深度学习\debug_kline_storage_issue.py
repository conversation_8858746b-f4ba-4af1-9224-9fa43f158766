#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试K线数据存储问题
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from training.training_pipeline import TrainingPipeline
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def debug_kline_data_format():
    """调试K线数据格式"""
    print("🔍 调试K线数据格式...")
    
    pipeline = TrainingPipeline()
    
    # 测试股票
    test_stock = "603880"
    test_date = datetime(2025, 1, 29)
    
    try:
        # 1. 获取K线数据
        print(f"\n1️⃣ 获取{test_stock}的K线数据...")
        start_date = test_date - timedelta(days=60)
        end_date = test_date - timedelta(days=1)
        
        kline_data = pipeline._get_kline_data_by_date_range(test_stock, start_date, end_date)
        
        if not kline_data.empty:
            print(f"✅ 获取K线数据成功: {len(kline_data)}条")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   数据类型:\n{kline_data.dtypes}")
            print(f"   样本数据:\n{kline_data.head(3)}")
            
            # 2. 测试存储逻辑
            print(f"\n2️⃣ 测试存储逻辑...")
            
            # 检查关键列是否存在
            required_columns = ['date', 'open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in kline_data.columns]
            
            if missing_columns:
                print(f"❌ 缺少必要列: {missing_columns}")
                print(f"   可用列: {list(kline_data.columns)}")
                
                # 尝试映射列名
                column_mapping = {
                    '开盘': 'open',
                    '收盘': 'close', 
                    '最高': 'high',
                    '最低': 'low',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '日期': 'date'
                }
                
                print(f"   尝试列名映射...")
                for old_name, new_name in column_mapping.items():
                    if old_name in kline_data.columns:
                        kline_data[new_name] = kline_data[old_name]
                        print(f"   ✅ 映射 {old_name} -> {new_name}")
                
                print(f"   映射后的列: {list(kline_data.columns)}")
            
            # 3. 手动测试存储
            print(f"\n3️⃣ 手动测试存储...")
            storage = HistoricalDataStorage()
            
            # 只取前几条数据测试
            test_data = kline_data.head(5).copy()
            
            print(f"   测试数据:")
            print(test_data)
            
            stored_count = storage.store_kline_data(test_stock, test_data)
            print(f"   存储结果: {stored_count}条")
            
            # 4. 验证存储结果
            print(f"\n4️⃣ 验证存储结果...")
            import sqlite3
            conn = sqlite3.connect('historical_data.db')
            
            count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
            print(f"   数据库中{test_stock}的K线数据: {count}条")
            
            if count > 0:
                sample_data = pd.read_sql('SELECT * FROM stock_kline_data WHERE stock_code = ? LIMIT 3', conn, params=[test_stock])
                print(f"   样本数据:\n{sample_data}")
                print("✅ K线数据存储成功！")
                return True
            else:
                print("❌ K线数据存储失败")
                return False
            
            conn.close()
        else:
            print("❌ 获取K线数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_training_pipeline_storage():
    """调试训练流程中的存储"""
    print("\n🔍 调试训练流程中的存储...")
    
    pipeline = TrainingPipeline()
    
    # 测试股票
    test_stock = "603880"
    test_date = datetime(2025, 1, 29)
    
    try:
        # 调用训练流程中的K线数据获取方法
        print(f"   调用训练流程的K线数据获取方法...")
        kline_data = pipeline._get_historical_kline_before_date(test_stock, test_date, 30)
        
        if not kline_data.empty:
            print(f"✅ 训练流程获取K线数据成功: {len(kline_data)}条")
            
            # 验证存储结果
            import sqlite3
            conn = sqlite3.connect('historical_data.db')
            
            count = conn.execute('SELECT COUNT(*) FROM stock_kline_data WHERE stock_code = ?', (test_stock,)).fetchone()[0]
            print(f"   数据库中{test_stock}的K线数据: {count}条")
            
            conn.close()
            
            if count > 0:
                print("✅ 训练流程K线数据存储成功！")
                return True
            else:
                print("❌ 训练流程K线数据存储失败")
                return False
        else:
            print("❌ 训练流程获取K线数据失败")
            return False
            
    except Exception as e:
        print(f"❌ 训练流程调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 K线数据存储问题调试...")
    print("="*60)
    
    # 调试K线数据格式
    format_result = debug_kline_data_format()
    
    # 调试训练流程中的存储
    pipeline_result = debug_training_pipeline_storage()
    
    print("\n" + "="*60)
    print("📋 调试结论:")
    
    if format_result:
        print("✅ K线数据格式和存储逻辑正常")
    else:
        print("❌ K线数据格式或存储逻辑异常")
    
    if pipeline_result:
        print("✅ 训练流程K线数据存储正常")
    else:
        print("❌ 训练流程K线数据存储异常")
    
    if format_result and pipeline_result:
        print("🎉 K线数据存储问题已解决！")
        print("💡 建议: 重新运行完整训练")
    else:
        print("❌ K线数据存储仍有问题，需要进一步修复")
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
