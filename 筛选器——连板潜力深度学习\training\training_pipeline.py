#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型训练流程
整合特征工程和机器学习模型，建立完整的训练pipeline
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import time
from typing import Dict, List, Any, Optional, Tuple
import os
import sys
import warnings
warnings.filterwarnings('ignore')

# 导入efinance
try:
    import efinance as ef
    efinance_available = True
except ImportError:
    efinance_available = False
    ef = None

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from data_layer.historical_data_fetcher import HistoricalDataFetcher
from data_layer.efinance_data_fetcher import EfinanceDataFetcher
from data_layer.historical_data_storage import HistoricalDataStorage
from feature_engineering.technical_indicators import TechnicalIndicatorsFeatureEngine
from feature_engineering.capital_flow_features import CapitalFlowFeatureEngine
from feature_engineering.youzi_behavior_features import YouzieBehaviorFeatureEngine
from feature_engineering.chip_distribution_features import ChipDistributionFeatureEngine
from feature_engineering.intraday_features_optimizer import IntradayFeaturesOptimizer
from feature_engineering.sector_linkage_features import SectorLinkageFeatureEngine
from ml_models.limit_up_predictor import LimitUpPredictor

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TrainingPipeline:
    """模型训练流程"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        
        # 初始化组件
        self.data_fetcher = HistoricalDataFetcher()
        self.efinance_fetcher = EfinanceDataFetcher()  # 新增efinance数据获取器
        self.data_storage = HistoricalDataStorage()
        
        # 特征工程器
        self.technical_engine = TechnicalIndicatorsFeatureEngine()
        self.capital_engine = CapitalFlowFeatureEngine()
        self.youzi_engine = YouzieBehaviorFeatureEngine()
        self.chip_engine = ChipDistributionFeatureEngine()
        self.intraday_engine = IntradayFeaturesOptimizer()
        self.sector_engine = SectorLinkageFeatureEngine()
        
        # 机器学习模型
        self.predictor = LimitUpPredictor()
        
        # 训练状态
        self.training_stats = {}
        
        logger.info("🚀 模型训练流程初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'training_period_days': 180,      # 训练数据时间范围
            'lookback_days': 30,              # 特征提取回看天数
            'min_samples_per_stock': 5,       # 每只股票最少样本数
            'test_size': 0.2,                 # 测试集比例
            'validation_size': 0.1,           # 验证集比例
            'feature_selection_threshold': 0.01,  # 特征选择阈值
            'use_grid_search': False,         # 是否使用网格搜索
            'save_intermediate_results': True, # 是否保存中间结果
            'batch_size': 50,                 # 批处理大小
            'max_stocks_per_batch': 100       # 每批最大股票数
        }
    
    def run_full_training_pipeline(self) -> Dict[str, Any]:
        """运行完整的训练流程"""
        try:
            logger.info("🚀 开始完整训练流程...")
            
            pipeline_results = {}
            
            # 1. 数据收集阶段
            logger.info("📊 阶段1: 数据收集")
            data_collection_results = self._collect_training_data()
            pipeline_results['data_collection'] = data_collection_results
            
            if not data_collection_results.get('success', False):
                logger.error("❌ 数据收集失败，终止训练流程")
                return pipeline_results
            
            # 2. 特征工程阶段
            logger.info("🔧 阶段2: 特征工程")
            feature_engineering_results = self._run_feature_engineering()
            pipeline_results['feature_engineering'] = feature_engineering_results
            
            if not feature_engineering_results.get('success', False):
                logger.error("❌ 特征工程失败，终止训练流程")
                return pipeline_results
            
            # 3. 数据准备阶段
            logger.info("📋 阶段3: 数据准备")
            data_preparation_results = self._prepare_training_data()
            pipeline_results['data_preparation'] = data_preparation_results
            
            if not data_preparation_results.get('success', False):
                logger.error("❌ 数据准备失败，终止训练流程")
                return pipeline_results
            
            # 4. 模型训练阶段
            logger.info("🤖 阶段4: 模型训练")
            model_training_results = self._train_models()
            pipeline_results['model_training'] = model_training_results
            
            # 5. 模型评估阶段
            logger.info("📈 阶段5: 模型评估")
            evaluation_results = self._evaluate_models()
            pipeline_results['evaluation'] = evaluation_results
            
            # 6. 结果保存阶段
            logger.info("💾 阶段6: 结果保存")
            save_results = self._save_training_results(pipeline_results)
            pipeline_results['save_results'] = save_results
            
            pipeline_results['overall_success'] = True
            pipeline_results['completion_time'] = datetime.now().isoformat()
            
            logger.info("🎉 完整训练流程完成！")
            return pipeline_results
            
        except Exception as e:
            logger.error(f"训练流程失败: {e}")
            return {'overall_success': False, 'error': str(e)}
    
    def _collect_training_data(self) -> Dict[str, Any]:
        """收集训练数据"""
        try:
            logger.info("📊 开始收集训练数据...")
            
            # 计算数据收集时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=self.config['training_period_days'])
            
            # 获取历史涨停股票
            logger.info(f"🔍 获取{start_date.strftime('%Y-%m-%d')}到{end_date.strftime('%Y-%m-%d')}的涨停股票...")
            limit_up_stocks = self.data_fetcher.get_limit_up_stocks_before_date(
                end_date, self.config['training_period_days']
            )
            
            if not limit_up_stocks:
                logger.warning("⚠️ 未找到涨停股票数据")
                return {'success': False, 'error': '未找到涨停股票数据'}
            
            # 存储涨停股票数据
            stored_count = self.data_storage.store_limit_up_stocks(limit_up_stocks)
            
            # 筛选优质股票进行训练
            logger.info("🎯 筛选优质股票进行训练...")
            qualified_stocks = self._filter_qualified_stocks(limit_up_stocks)

            if not qualified_stocks:
                logger.warning("⚠️ 未找到符合条件的优质股票")
                return {'success': False, 'error': '未找到符合条件的优质股票'}

            logger.info(f"📊 筛选出{len(qualified_stocks)}只优质股票进行训练")

            # 使用efinance获取真实历史数据
            logger.info("📥 使用efinance获取真实历史数据...")
            batch_results = self._get_real_historical_data(qualified_stocks[:50], end_date)

            # 保存batch_results供后续使用
            self.batch_results = batch_results
            
            # 存储历史数据
            stored_kline_count = 0
            stored_minute_count = 0
            stored_capital_count = 0
            
            for stock_code, data in batch_results.items():
                if data['kline_data'] is not None:
                    count = self.data_storage.store_kline_data(stock_code, data['kline_data'])
                    stored_kline_count += count
                
                if data['minute_data'] is not None:
                    # 假设是最近一个交易日的分时数据
                    trade_date = (end_date - timedelta(days=1)).strftime('%Y-%m-%d')
                    count = self.data_storage.store_minute_data(stock_code, trade_date, data['minute_data'])
                    stored_minute_count += count
                
                if data['capital_data'] is not None:
                    count = self.data_storage.store_capital_flow_data(stock_code, data['capital_data'])
                    stored_capital_count += count
            
            return {
                'success': True,
                'limit_up_stocks_found': len(limit_up_stocks),
                'qualified_stocks': len(qualified_stocks),
                'stored_limit_up_count': stored_count,
                'stored_kline_count': stored_kline_count,
                'stored_minute_count': stored_minute_count,
                'stored_capital_count': stored_capital_count,
                'data_quality_distribution': self._analyze_data_quality(batch_results)
            }
            
        except Exception as e:
            logger.error(f"收集训练数据失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _analyze_data_quality(self, batch_results: Dict[str, Dict[str, Any]]) -> Dict[str, int]:
        """分析数据质量分布"""
        try:
            quality_distribution = {'excellent': 0, 'good': 0, 'fair': 0, 'poor': 0, 'error': 0}
            
            for stock_code, data in batch_results.items():
                quality = data.get('data_quality', 'error')
                if quality in quality_distribution:
                    quality_distribution[quality] += 1
                else:
                    quality_distribution['error'] += 1
            
            return quality_distribution
            
        except Exception as e:
            logger.error(f"分析数据质量失败: {e}")
            return {}

    def _filter_qualified_stocks(self, limit_up_stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """筛选优质股票：一年内涨停≥3次，5个交易日内出现过2次涨停，市值30-130亿"""
        try:
            logger.info("🔍 开始筛选优质股票...")

            # 第一步：统计每只股票的涨停次数
            stock_limit_count = {}
            stock_limit_dates = {}

            for stock in limit_up_stocks:
                stock_code = stock.get('stock_code', '')
                limit_date = stock.get('limit_up_date', '')

                if stock_code and limit_date:
                    stock_limit_count[stock_code] = stock_limit_count.get(stock_code, 0) + 1

                    if stock_code not in stock_limit_dates:
                        stock_limit_dates[stock_code] = []
                    stock_limit_dates[stock_code].append(limit_date)

            # 第二步：筛选涨停次数≥3次的股票
            frequent_limit_stocks = {code: count for code, count in stock_limit_count.items() if count >= 3}
            logger.info(f"📊 一年内涨停次数≥3次的股票: {len(frequent_limit_stocks)}只")

            # 第三步：筛选5个交易日内出现过2次涨停的股票
            consecutive_limit_stocks = self._filter_consecutive_limit_stocks(stock_limit_dates, frequent_limit_stocks)
            logger.info(f"📊 5个交易日内出现过2次涨停的股票: {len(consecutive_limit_stocks)}只")

            # 第四步：市值和板块筛选
            qualified_stocks = []
            for stock in limit_up_stocks:
                stock_code = stock.get('stock_code', '')
                stock_name = stock.get('stock_name', '')

                # 检查是否在连续涨停股票中
                if stock_code not in consecutive_limit_stocks:
                    continue

                # 板块筛选：排除ST、创业板、科创板
                if not self._check_stock_eligibility(stock_code, stock_name):
                    continue

                # 市值筛选（使用真实数据或估算）
                market_cap = self._get_real_market_cap(stock_code, stock.get('close_price', 10))

                # 市值筛选：30-130亿
                if not (30e8 <= market_cap <= 130e8):
                    continue

                # 添加额外信息
                stock_info = stock.copy()
                stock_info['limit_up_count'] = frequent_limit_stocks[stock_code]
                stock_info['consecutive_limit_count'] = consecutive_limit_stocks[stock_code]
                stock_info['estimated_market_cap'] = market_cap

                # 避免重复
                if not any(s['stock_code'] == stock_code for s in qualified_stocks):
                    qualified_stocks.append(stock_info)

            logger.info(f"✅ 筛选出{len(qualified_stocks)}只优质股票")

            # 按连续涨停次数和总涨停次数排序
            qualified_stocks.sort(key=lambda x: (x.get('consecutive_limit_count', 0), x.get('limit_up_count', 0)), reverse=True)

            return qualified_stocks

        except Exception as e:
            logger.error(f"筛选优质股票失败: {e}")
            return []

    def _filter_consecutive_limit_stocks(self, stock_limit_dates: Dict[str, List[str]],
                                       frequent_stocks: Dict[str, int]) -> Dict[str, int]:
        """筛选5个交易日内出现过2次涨停的股票"""
        try:
            consecutive_stocks = {}

            for stock_code in frequent_stocks.keys():
                limit_dates = stock_limit_dates.get(stock_code, [])

                if len(limit_dates) < 2:
                    continue

                # 转换日期格式并排序
                try:
                    parsed_dates = []
                    for date_str in limit_dates:
                        if isinstance(date_str, str):
                            # 尝试不同的日期格式
                            for fmt in ['%Y-%m-%d', '%Y%m%d', '%m/%d/%Y']:
                                try:
                                    parsed_date = datetime.strptime(date_str, fmt)
                                    parsed_dates.append(parsed_date)
                                    break
                                except ValueError:
                                    continue

                    if len(parsed_dates) < 2:
                        continue

                    parsed_dates.sort()

                    # 检查是否有5个交易日内的连续涨停
                    consecutive_count = 0
                    max_consecutive = 0

                    for i in range(len(parsed_dates) - 1):
                        date1 = parsed_dates[i]
                        date2 = parsed_dates[i + 1]

                        # 计算交易日差距（简化计算，假设5个自然日约等于5个交易日）
                        days_diff = (date2 - date1).days

                        if days_diff <= 5:  # 5个交易日内
                            consecutive_count += 1
                            max_consecutive = max(max_consecutive, consecutive_count + 1)
                        else:
                            consecutive_count = 0

                    if max_consecutive >= 2:
                        consecutive_stocks[stock_code] = max_consecutive

                except Exception as e:
                    logger.warning(f"处理{stock_code}日期失败: {e}")
                    continue

            return consecutive_stocks

        except Exception as e:
            logger.error(f"筛选连续涨停股票失败: {e}")
            return {}

    def _check_stock_eligibility(self, stock_code: str, stock_name: str) -> bool:
        """检查股票是否符合板块要求"""
        try:
            # 排除ST股票
            if 'ST' in stock_name or '*ST' in stock_name:
                return False

            # 排除科创板（688开头）
            if stock_code.startswith('688'):
                return False

            # 排除创业板（300开头）
            if stock_code.startswith('300'):
                return False

            # 排除北交所（8开头）
            if stock_code.startswith('8'):
                return False

            # 只保留主板和中小板
            if not stock_code.startswith(('000', '001', '002', '600', '601', '603')):
                return False

            return True

        except Exception as e:
            logger.warning(f"检查股票{stock_code}资格失败: {e}")
            return False

    def _get_real_market_cap(self, stock_code: str, price: float) -> float:
        """获取真实市值（尝试从efinance获取，失败则估算）"""
        try:
            # 尝试从efinance获取真实市值
            try:
                import efinance as ef
                stock_info = ef.stock.get_realtime_quotes(stock_code)

                if stock_info is not None and not stock_info.empty:
                    market_cap = stock_info.get('总市值', 0)
                    if market_cap and market_cap > 0:
                        # 转换为亿元
                        return float(market_cap) / 10000 if market_cap > 1000 else float(market_cap)
            except Exception as e:
                logger.debug(f"获取{stock_code}真实市值失败: {e}")

            # 备用方案：估算市值
            return self._estimate_market_cap(stock_code, price)

        except Exception as e:
            logger.warning(f"获取{stock_code}市值失败: {e}")
            return 50e8  # 默认50亿

    def _get_stock_limit_up_dates(self, stock_code: str) -> List[str]:
        """获取股票的所有涨停日期"""
        try:
            # 从数据库中查询该股票的所有涨停日期
            conn = self.data_storage.get_connection()
            if not conn:
                return []

            cursor = conn.execute('''
                SELECT DISTINCT limit_up_date
                FROM limit_up_stocks
                WHERE stock_code = ?
                ORDER BY limit_up_date
            ''', (stock_code,))

            dates = [row[0] for row in cursor.fetchall()]
            return dates

        except Exception as e:
            logger.error(f"获取{stock_code}涨停日期失败: {e}")
            return []

    def _get_historical_kline_before_date(self, stock_code: str, before_date: datetime, days: int) -> pd.DataFrame:
        """获取指定日期前的K线数据"""
        try:
            logger.info(f"📊 获取{stock_code}在{before_date.strftime('%Y-%m-%d')}前{days}天的K线数据")

            # 计算需要获取的数据时间范围
            start_date = before_date - timedelta(days=days + 30)  # 额外获取30天以确保有足够的交易日数据
            end_date = before_date - timedelta(days=1)  # 涨停前一天

            logger.info(f"📅 数据时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")

            # 使用efinance获取指定时间范围的K线数据
            kline_data = self._get_kline_data_by_date_range(stock_code, start_date, end_date)

            if not kline_data.empty:
                # 按日期排序并取最近的days个交易日
                kline_data = kline_data.sort_values('date')
                result_data = kline_data.tail(days)

                # 存储K线数据到数据库
                try:
                    stored_count = self.data_storage.store_kline_data(stock_code, result_data)
                    logger.info(f"✅ 存储{stock_code}的K线数据完成: {stored_count}条")
                except Exception as e:
                    logger.warning(f"存储{stock_code}的K线数据失败: {e}")

                logger.info(f"✅ 成功获取{stock_code}在{before_date.strftime('%Y-%m-%d')}前{len(result_data)}天的K线数据")
                return result_data
            else:
                logger.warning(f"⚠️ {stock_code}在{before_date.strftime('%Y-%m-%d')}前没有K线数据")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取{stock_code}历史K线数据失败: {e}")
            return pd.DataFrame()

    def _get_kline_data_by_date_range(self, stock_code: str, start_date: datetime, end_date: datetime) -> pd.DataFrame:
        """获取指定日期范围的K线数据"""
        try:
            if not self.efinance_fetcher.efinance_available:
                logger.error("efinance不可用")
                return pd.DataFrame()

            # 使用efinance获取历史K线数据（获取所有历史数据）
            df = ef.stock.get_quote_history(stock_code)

            if df is None or df.empty:
                logger.warning(f"⚠️ {stock_code}没有K线数据")
                return pd.DataFrame()

            # 数据格式转换
            df_processed = self.efinance_fetcher._process_kline_data(df)

            if not df_processed.empty and 'date' in df_processed.columns:
                # 确保日期格式正确
                df_processed['date'] = pd.to_datetime(df_processed['date'])

                # 过滤指定日期范围的数据
                filtered_data = df_processed[
                    (df_processed['date'] >= start_date) &
                    (df_processed['date'] <= end_date)
                ]

                logger.info(f"✅ 获取{stock_code}在{start_date.strftime('%Y-%m-%d')}到{end_date.strftime('%Y-%m-%d')}的{len(filtered_data)}条K线数据")
                return filtered_data
            else:
                logger.warning(f"⚠️ {stock_code}数据格式处理失败")
                return pd.DataFrame()

        except Exception as e:
            logger.error(f"获取{stock_code}指定日期范围K线数据失败: {e}")
            return pd.DataFrame()

    def _get_historical_capital_before_date(self, stock_code: str, before_date: datetime, days: int) -> pd.DataFrame:
        """获取指定日期前的资金流向数据"""
        try:
            logger.info(f"💰 获取{stock_code}在{before_date.strftime('%Y-%m-%d')}前{days}天的资金流向数据")

            # 临时方案：使用当前的资金流向数据获取方法
            # 实际应用中需要根据before_date获取历史数据
            capital_data = self.efinance_fetcher.get_capital_flow_data(stock_code, days)

            if not capital_data.empty and 'date' in capital_data.columns:
                # 过滤出before_date之前的数据
                capital_data['date'] = pd.to_datetime(capital_data['date'])
                filtered_data = capital_data[capital_data['date'] < before_date]

                if not filtered_data.empty:
                    return filtered_data.tail(days)  # 取最近的days天数据

            return capital_data

        except Exception as e:
            logger.error(f"获取{stock_code}历史资金流向数据失败: {e}")
            return pd.DataFrame()

    def _estimate_market_cap(self, stock_code: str, price: float) -> float:
        """估算市值（基于股票代码和价格的简单估算）"""
        try:
            # 基于股票代码估算流通股本（这是简化估算，实际需要真实数据）
            if stock_code.startswith('00'):  # 深市
                if stock_code.startswith('002'):  # 中小板
                    estimated_shares = np.random.uniform(3e8, 8e8)  # 3-8亿股
                else:  # 主板
                    estimated_shares = np.random.uniform(5e8, 15e8)  # 5-15亿股
            elif stock_code.startswith('60'):  # 沪市主板
                estimated_shares = np.random.uniform(8e8, 20e8)  # 8-20亿股
            else:
                estimated_shares = 5e8  # 默认5亿股

            # 市值 = 股价 × 流通股本
            market_cap = price * estimated_shares

            return market_cap

        except Exception as e:
            logger.error(f"估算{stock_code}市值失败: {e}")
            return 50e8  # 默认50亿

    def _get_real_historical_data(self, qualified_stocks: List[Dict[str, Any]], end_date: datetime) -> Dict[str, Dict[str, Any]]:
        """获取真实历史数据（为每个涨停事件获取对应的历史数据）"""
        try:
            logger.info("📊 获取真实历史数据...")
            logger.info("🎯 为每个涨停事件获取对应的涨停前历史数据")

            # 重新组织数据：按涨停事件而不是按股票
            training_samples = []

            # 从qualified_stocks中提取所有涨停事件
            for stock_info in qualified_stocks:
                stock_code = stock_info['stock_code']
                stock_name = stock_info['stock_name']

                # 获取该股票的所有涨停日期
                limit_up_dates = self._get_stock_limit_up_dates(stock_code)

                for limit_up_date in limit_up_dates:
                    training_samples.append({
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'limit_up_date': limit_up_date,
                        'sample_id': f"{stock_code}_{limit_up_date}"
                    })

            logger.info(f"📊 总共需要获取{len(training_samples)}个训练样本的数据")
            logger.info(f"🎯 不限制样本数量，使用全部{len(training_samples)}个样本进行训练")

            results = {}

            for i, sample in enumerate(training_samples, 1):
                try:
                    stock_code = sample['stock_code']
                    limit_up_date = sample['limit_up_date']
                    sample_id = sample['sample_id']

                    logger.info(f"处理第{i}/{len(training_samples)}个样本: {stock_code} 涨停日期{limit_up_date}")

                    # 解析涨停日期
                    if isinstance(limit_up_date, str):
                        try:
                            limit_date_obj = datetime.strptime(limit_up_date, '%Y-%m-%d')
                        except:
                            try:
                                limit_date_obj = datetime.strptime(limit_up_date, '%Y%m%d')
                            except:
                                logger.warning(f"无法解析日期格式: {limit_up_date}")
                                continue
                    else:
                        limit_date_obj = limit_up_date

                    # 获取涨停前一天的日期
                    feature_date = limit_date_obj - timedelta(days=1)

                    # 获取涨停前30天的K线数据
                    kline_data = self._get_historical_kline_before_date(stock_code, feature_date, 30)

                    # 获取涨停前一天的分时数据
                    minute_data = self.efinance_fetcher.get_stock_minute_data(stock_code, feature_date)

                    # 获取涨停前10天的资金流向数据
                    capital_data = self._get_historical_capital_before_date(stock_code, feature_date, 10)

                    # 评估数据质量
                    data_quality = self.efinance_fetcher._evaluate_data_quality(kline_data, minute_data, capital_data)

                    results[sample_id] = {
                        'stock_code': stock_code,
                        'stock_name': sample['stock_name'],
                        'limit_up_date': limit_up_date,
                        'feature_date': feature_date.strftime('%Y-%m-%d'),
                        'kline_data': kline_data,
                        'minute_data': minute_data,
                        'capital_data': capital_data,
                        'data_quality': data_quality,
                        'label': 1  # 涨停标签
                    }

                    # API限流
                    time.sleep(0.5)

                except Exception as e:
                    logger.warning(f"获取样本{sample.get('sample_id', '')}数据失败: {e}")
                    continue

            logger.info(f"✅ 完成{len(results)}个训练样本的真实数据获取")
            return results

        except Exception as e:
            logger.error(f"获取真实历史数据失败: {e}")
            return {}

    def _analyze_stock_pattern(self, stock_info: Dict[str, Any]) -> Dict[str, Any]:
        """分析单只股票的涨停模式"""
        try:
            stock_code = stock_info['stock_code']
            limit_up_count = stock_info.get('limit_up_count', 0)
            market_cap = stock_info.get('estimated_market_cap', 50e8)

            # 基于涨停次数和市值分析模式特征
            pattern = {
                'stock_code': stock_code,
                'limit_up_frequency': limit_up_count,
                'market_cap_level': self._categorize_market_cap(market_cap),
                'activity_score': min(limit_up_count / 10.0, 1.0),  # 活跃度评分
                'size_score': self._calculate_size_score(market_cap),  # 规模评分
                'quality_score': self._calculate_quality_score(stock_info)  # 质量评分
            }

            return pattern

        except Exception as e:
            logger.error(f"分析股票模式失败: {e}")
            return {}

    def _categorize_market_cap(self, market_cap: float) -> str:
        """市值分类"""
        if market_cap < 50e8:
            return 'small'
        elif market_cap < 100e8:
            return 'medium'
        else:
            return 'large'

    def _calculate_size_score(self, market_cap: float) -> float:
        """计算规模评分"""
        # 30-130亿市值区间，中等规模得分最高
        if 50e8 <= market_cap <= 80e8:
            return 1.0
        elif 30e8 <= market_cap <= 130e8:
            return 0.8
        else:
            return 0.5

    def _calculate_quality_score(self, stock_info: Dict[str, Any]) -> float:
        """计算质量评分"""
        try:
            score = 0.0

            # 涨停次数评分
            limit_count = stock_info.get('limit_up_count', 0)
            if limit_count >= 10:
                score += 0.4
            elif limit_count >= 5:
                score += 0.3
            elif limit_count >= 3:
                score += 0.2

            # 价格合理性评分
            price = stock_info.get('close_price', 0)
            if 8 <= price <= 30:
                score += 0.3
            elif 5 <= price <= 50:
                score += 0.2
            else:
                score += 0.1

            # 连续涨停评分
            consecutive_days = stock_info.get('consecutive_days', 1)
            if consecutive_days >= 3:
                score += 0.3
            elif consecutive_days >= 2:
                score += 0.2
            else:
                score += 0.1

            return min(score, 1.0)

        except Exception as e:
            logger.error(f"计算质量评分失败: {e}")
            return 0.5
    
    def _run_feature_engineering(self) -> Dict[str, Any]:
        """运行特征工程"""
        try:
            logger.info("🔧 开始特征工程...")
            
            # 获取需要进行特征工程的股票
            limit_up_df = self.data_storage.get_limit_up_stocks()
            
            if limit_up_df.empty:
                return {'success': False, 'error': '没有涨停股票数据'}
            
            feature_extraction_results = []
            successful_extractions = 0
            
            # 处理每个训练样本
            for sample_id, sample_data in self.batch_results.items():
                try:
                    stock_code = sample_data.get('stock_code', '')
                    stock_name = sample_data.get('stock_name', '')
                    limit_up_date = sample_data.get('limit_up_date', '')

                    logger.info(f"🔧 处理训练样本: {stock_code} 涨停日期{limit_up_date}")

                    if sample_data.get('data_quality') == 'error':
                        logger.warning(f"⚠️ 样本{sample_id}没有有效数据，跳过")
                        continue

                    # 基于真实历史数据提取特征
                    all_features = self._extract_all_features(stock_code, stock_name, sample_data)
                    
                    if all_features:
                        # 直接为当前训练样本创建标签
                        feature_date = sample_data.get('feature_date', '')
                        label = sample_data.get('label', 1)  # 涨停标签

                        # 存储特征数据
                        success = self.data_storage.store_stock_features(
                            stock_code, feature_date, all_features, label=label
                        )

                        if success:
                            successful_extractions += 1

                            # 为每个正样本创建对应的负样本
                            try:
                                # 计算查询K线数据的时间范围（涨停前30天到涨停日）
                                limit_up_datetime = datetime.strptime(limit_up_date, '%Y-%m-%d')
                                start_date_for_query = (limit_up_datetime - timedelta(days=30)).strftime('%Y-%m-%d')
                                end_date_for_query = limit_up_date

                                # 获取该股票指定时间范围的K线数据来创建负样本
                                kline_data = self.data_storage.get_kline_data(
                                    stock_code,
                                    start_date=start_date_for_query,
                                    end_date=end_date_for_query
                                )

                                if not kline_data.empty:
                                    # 创建负样本（非涨停日的特征）
                                    self._create_negative_samples(
                                        stock_code,
                                        kline_data,
                                        [limit_up_date],
                                        all_features
                                    )
                                    logger.info(f"✅ {stock_code}负样本创建完成，K线数据{len(kline_data)}条")
                                else:
                                    logger.warning(f"⚠️ {stock_code}在{start_date_for_query}到{end_date_for_query}期间没有K线数据，无法创建负样本")
                            except Exception as e:
                                logger.warning(f"创建{stock_code}负样本失败: {e}")
                                import traceback
                                logger.debug(f"负样本创建详细错误: {traceback.format_exc()}")

                        feature_extraction_results.append({
                            'sample_id': sample_id,
                            'stock_code': stock_code,
                            'feature_date': feature_date,
                            'limit_up_date': limit_up_date,
                            'feature_count': len(all_features),
                            'label': label
                        })
                    
                except Exception as e:
                    logger.warning(f"处理股票{stock_code}失败: {e}")
                    continue
            
            return {
                'success': True,
                'processed_samples': len(self.batch_results),
                'successful_extractions': successful_extractions,
                'feature_extraction_results': feature_extraction_results
            }
            
        except Exception as e:
            logger.error(f"特征工程失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _extract_all_features(self, stock_code: str, stock_name: str, stock_data: Dict[str, Any]) -> Dict[str, Any]:
        """基于真实历史数据提取特征"""
        try:
            all_features = {}

            # 获取各种数据
            kline_data = stock_data.get('kline_data', pd.DataFrame())
            minute_data = stock_data.get('minute_data', pd.DataFrame())
            capital_data = stock_data.get('capital_data', pd.DataFrame())
            pattern_analysis = stock_data.get('pattern_analysis', {})

            # 1. 技术指标特征（基于K线数据）
            if not kline_data.empty:
                technical_features = self._extract_technical_features(kline_data)
                all_features.update(technical_features)
                logger.info(f"提取技术指标特征: {len(technical_features)}个")

            # 2. 资金流向特征（基于资金数据）
            if not capital_data.empty:
                capital_features = self._extract_capital_features(capital_data)
                all_features.update(capital_features)
                logger.info(f"提取资金流向特征: {len(capital_features)}个")

            # 3. 分时特征（基于分时数据）
            if not minute_data.empty:
                minute_features = self._extract_minute_features(minute_data)
                all_features.update(minute_features)
                logger.info(f"提取分时特征: {len(minute_features)}个")

            # 4. 基于历史涨停模式的特征
            if pattern_analysis:
                pattern_features = self._extract_pattern_features(pattern_analysis)
                all_features.update(pattern_features)
                logger.info(f"提取模式特征: {len(pattern_features)}个")

            # 5. 游资行为特征（基于本地数据）
            youzi_features = self.youzi_engine.extract_youzi_behavior_features(stock_code, stock_name)
            all_features.update(youzi_features)
            logger.info(f"提取游资特征: {len(youzi_features)}个")

            # 6. 基础特征
            basic_features = self._extract_basic_features(stock_code, pattern_analysis)
            all_features.update(basic_features)
            logger.info(f"提取基础特征: {len(basic_features)}个")

            # 清理特征（移除非数值特征）
            cleaned_features = self._clean_features(all_features)

            logger.info(f"✅ {stock_code}特征提取完成: {len(cleaned_features)}个特征")
            return cleaned_features

        except Exception as e:
            logger.error(f"提取{stock_code}特征失败: {e}")
            return {}

    def _extract_pattern_features(self, pattern_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """基于历史模式提取特征"""
        try:
            features = {}

            # 涨停频率特征
            features['limit_up_frequency'] = pattern_analysis.get('limit_up_frequency', 0)
            features['activity_score'] = pattern_analysis.get('activity_score', 0)
            features['size_score'] = pattern_analysis.get('size_score', 0)
            features['quality_score'] = pattern_analysis.get('quality_score', 0)

            # 市值等级特征
            market_cap_level = pattern_analysis.get('market_cap_level', 'medium')
            features['is_small_cap'] = 1 if market_cap_level == 'small' else 0
            features['is_medium_cap'] = 1 if market_cap_level == 'medium' else 0
            features['is_large_cap'] = 1 if market_cap_level == 'large' else 0

            # 综合评分特征
            features['pattern_comprehensive_score'] = (
                features['activity_score'] * 0.4 +
                features['size_score'] * 0.3 +
                features['quality_score'] * 0.3
            )

            return features

        except Exception as e:
            logger.error(f"提取模式特征失败: {e}")
            return {}

    def _extract_basic_features(self, stock_code: str, pattern_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """提取基础特征"""
        try:
            features = {}

            # 股票代码特征
            features['is_shenzhen'] = 1 if stock_code.startswith('00') else 0
            features['is_shanghai'] = 1 if stock_code.startswith('60') else 0
            features['is_sme_board'] = 1 if stock_code.startswith('002') else 0

            # 活跃度特征
            limit_count = pattern_analysis.get('limit_up_frequency', 0)
            features['high_activity'] = 1 if limit_count >= 10 else 0
            features['medium_activity'] = 1 if 5 <= limit_count < 10 else 0
            features['low_activity'] = 1 if 3 <= limit_count < 5 else 0

            # 质量等级特征
            quality_score = pattern_analysis.get('quality_score', 0)
            features['high_quality'] = 1 if quality_score >= 0.8 else 0
            features['medium_quality'] = 1 if 0.5 <= quality_score < 0.8 else 0
            features['low_quality'] = 1 if quality_score < 0.5 else 0

            return features

        except Exception as e:
            logger.error(f"提取基础特征失败: {e}")
            return {}

    def _extract_technical_features(self, kline_data: pd.DataFrame) -> Dict[str, Any]:
        """提取技术指标特征"""
        try:
            features = {}

            if len(kline_data) < 5:
                return features

            # 价格特征
            features['close_price'] = kline_data['close'].iloc[-1]
            features['price_change_5d'] = (kline_data['close'].iloc[-1] / kline_data['close'].iloc[-6] - 1) if len(kline_data) >= 6 else 0
            features['price_change_10d'] = (kline_data['close'].iloc[-1] / kline_data['close'].iloc[-11] - 1) if len(kline_data) >= 11 else 0

            # 成交量特征
            features['volume_avg_5d'] = kline_data['volume'].tail(5).mean()
            features['volume_ratio'] = kline_data['volume'].iloc[-1] / features['volume_avg_5d'] if features['volume_avg_5d'] > 0 else 1

            # 换手率特征
            if 'turnover_rate' in kline_data.columns:
                features['turnover_rate'] = kline_data['turnover_rate'].iloc[-1]
                features['turnover_avg_5d'] = kline_data['turnover_rate'].tail(5).mean()

            # 振幅特征
            features['amplitude'] = (kline_data['high'].iloc[-1] - kline_data['low'].iloc[-1]) / kline_data['close'].iloc[-2] if len(kline_data) >= 2 else 0

            # 移动平均线特征
            if len(kline_data) >= 5:
                ma5 = kline_data['close'].tail(5).mean()
                features['price_vs_ma5'] = kline_data['close'].iloc[-1] / ma5 - 1

            if len(kline_data) >= 10:
                ma10 = kline_data['close'].tail(10).mean()
                features['price_vs_ma10'] = kline_data['close'].iloc[-1] / ma10 - 1

            return features

        except Exception as e:
            logger.error(f"提取技术指标特征失败: {e}")
            return {}

    def _extract_capital_features(self, capital_data: pd.DataFrame) -> Dict[str, Any]:
        """提取资金流向特征"""
        try:
            features = {}

            if capital_data.empty:
                return features

            # 最新资金流向
            latest = capital_data.iloc[-1]
            features['main_net_inflow'] = latest.get('main_net_inflow', 0)
            features['large_net_inflow'] = latest.get('large_net_inflow', 0)
            features['super_large_net_inflow'] = latest.get('super_large_net_inflow', 0)
            features['main_net_inflow_ratio'] = latest.get('main_net_inflow_ratio', 0)

            # 资金流向趋势（最近3天）
            if len(capital_data) >= 3:
                recent_3d = capital_data.tail(3)
                features['main_inflow_3d_sum'] = recent_3d['main_net_inflow'].sum()
                features['main_inflow_positive_days'] = (recent_3d['main_net_inflow'] > 0).sum()

            # 资金流向趋势（最近5天）
            if len(capital_data) >= 5:
                recent_5d = capital_data.tail(5)
                features['main_inflow_5d_avg'] = recent_5d['main_net_inflow'].mean()
                features['large_inflow_5d_avg'] = recent_5d['large_net_inflow'].mean()

            return features

        except Exception as e:
            logger.error(f"提取资金流向特征失败: {e}")
            return {}

    def _extract_minute_features(self, minute_data: pd.DataFrame) -> Dict[str, Any]:
        """提取分时特征"""
        try:
            features = {}

            if minute_data.empty:
                return features

            # 早盘活跃度（9:30-10:30）
            morning_data = minute_data[minute_data['datetime'].dt.hour == 9]
            if not morning_data.empty:
                features['morning_volume_ratio'] = morning_data['volume'].sum() / minute_data['volume'].sum()
                features['morning_amplitude'] = (morning_data['high'].max() - morning_data['low'].min()) / morning_data['open'].iloc[0]

            # 尾盘活跃度（14:30-15:00）
            afternoon_data = minute_data[minute_data['datetime'].dt.hour == 14]
            if not afternoon_data.empty:
                features['afternoon_volume_ratio'] = afternoon_data['volume'].sum() / minute_data['volume'].sum()
                features['afternoon_price_change'] = (afternoon_data['close'].iloc[-1] - afternoon_data['open'].iloc[0]) / afternoon_data['open'].iloc[0]

            # 全天波动特征
            features['intraday_high'] = minute_data['high'].max()
            features['intraday_low'] = minute_data['low'].min()
            features['intraday_amplitude'] = (features['intraday_high'] - features['intraday_low']) / minute_data['open'].iloc[0]

            return features

        except Exception as e:
            logger.error(f"提取分时特征失败: {e}")
            return {}

    def _get_default_features(self, stock_code: str) -> Dict[str, Any]:
        """获取默认特征"""
        return {
            'limit_up_frequency': 0,
            'activity_score': 0,
            'size_score': 0.5,
            'quality_score': 0.5,
            'is_shenzhen': 1 if stock_code.startswith('00') else 0,
            'is_shanghai': 1 if stock_code.startswith('60') else 0,
            'pattern_comprehensive_score': 0.5
        }
    
    def _clean_features(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """清理特征数据"""
        try:
            cleaned_features = {}
            
            for key, value in features.items():
                # 跳过非数值特征
                if key in ['identified_sectors', 'error', 'operation_style']:
                    continue
                
                # 跳过成功标志
                if key.endswith('_success') or key.endswith('_analysis_success'):
                    continue
                
                # 跳过计数特征
                if key.startswith('total_') and key.endswith('_features'):
                    continue
                
                # 转换为数值
                try:
                    if isinstance(value, (int, float, bool)):
                        cleaned_features[key] = float(value)
                    elif isinstance(value, str):
                        # 尝试转换字符串为数值
                        try:
                            cleaned_features[key] = float(value)
                        except ValueError:
                            continue
                except (ValueError, TypeError):
                    continue
            
            return cleaned_features
            
        except Exception as e:
            logger.error(f"清理特征失败: {e}")
            return features

    def _create_negative_samples(self, stock_code: str, kline_data: pd.DataFrame,
                               limit_dates: List[str], base_features: Dict[str, Any]):
        """创建负样本（非涨停日的特征）"""
        try:
            # 检查数据列名，兼容不同的数据源
            date_column = None
            if 'date' in kline_data.columns:
                date_column = 'date'
            elif 'trade_date' in kline_data.columns:
                date_column = 'trade_date'
            else:
                logger.warning(f"K线数据中没有找到日期列，可用列: {list(kline_data.columns)}")
                return

            # 获取非涨停日期
            if pd.api.types.is_datetime64_any_dtype(kline_data[date_column]):
                # 如果是datetime类型，转换为字符串
                all_dates = kline_data[date_column].dt.strftime('%Y-%m-%d').tolist()
            else:
                # 如果已经是字符串类型，直接使用
                all_dates = kline_data[date_column].astype(str).tolist()
                # 确保日期格式统一
                all_dates = [date[:10] if len(date) > 10 else date for date in all_dates]

            limit_dates_set = set(limit_dates)

            # 选择一些非涨停日作为负样本
            negative_dates = [date for date in all_dates if date not in limit_dates_set]

            if not negative_dates:
                logger.warning(f"{stock_code}没有可用的非涨停日期创建负样本")
                return

            # 限制负样本数量，避免数据不平衡
            max_negative_samples = min(len(negative_dates), len(limit_dates) * 3)
            if max_negative_samples == 0:
                logger.warning(f"{stock_code}负样本数量为0，跳过创建")
                return

            selected_negative_dates = np.random.choice(negative_dates, max_negative_samples, replace=False)

            for neg_date in selected_negative_dates:
                # 存储负样本特征（标签为0，表示不涨停）
                self.data_storage.store_stock_features(stock_code, neg_date, base_features, label=0)

            logger.info(f"✅ {stock_code}创建负样本: {len(selected_negative_dates)}个")

        except Exception as e:
            logger.warning(f"创建{stock_code}负样本失败: {e}")
            import traceback
            logger.debug(f"负样本创建详细错误: {traceback.format_exc()}")

    def _prepare_training_data(self) -> Dict[str, Any]:
        """准备训练数据"""
        try:
            logger.info("📋 开始准备训练数据...")

            # 从数据库获取特征数据
            features_list, labels_list = self.data_storage.get_training_dataset(
                min_features=self.config['feature_selection_threshold']
            )

            if not features_list:
                return {'success': False, 'error': '没有可用的训练数据'}

            # 转换为DataFrame
            features_df, labels_array = self.predictor.prepare_training_data(features_list, labels_list)

            # 数据统计
            total_samples = len(features_df)
            positive_samples = sum(labels_array)
            negative_samples = total_samples - positive_samples
            positive_ratio = positive_samples / total_samples if total_samples > 0 else 0

            logger.info(f"📊 训练数据统计:")
            logger.info(f"  总样本数: {total_samples}")
            logger.info(f"  正样本数: {positive_samples}")
            logger.info(f"  负样本数: {negative_samples}")
            logger.info(f"  正样本比例: {positive_ratio:.2%}")
            logger.info(f"  特征数量: {len(features_df.columns)}")

            # 保存准备好的数据
            self.training_features = features_df
            self.training_labels = labels_array

            return {
                'success': True,
                'total_samples': total_samples,
                'positive_samples': positive_samples,
                'negative_samples': negative_samples,
                'positive_ratio': positive_ratio,
                'feature_count': len(features_df.columns),
                'feature_names': features_df.columns.tolist()
            }

        except Exception as e:
            logger.error(f"准备训练数据失败: {e}")
            return {'success': False, 'error': str(e)}

    def _train_models(self) -> Dict[str, Any]:
        """训练模型"""
        try:
            logger.info("🤖 开始训练模型...")

            if not hasattr(self, 'training_features') or not hasattr(self, 'training_labels'):
                return {'success': False, 'error': '训练数据未准备'}

            # 训练模型
            training_results = self.predictor.train_models(
                self.training_features,
                self.training_labels,
                test_size=self.config['test_size'],
                use_grid_search=self.config['use_grid_search']
            )

            if not training_results:
                return {'success': False, 'error': '模型训练失败'}

            # 统计训练结果
            successful_models = [name for name, result in training_results.items() if 'error' not in result]

            logger.info(f"✅ 模型训练完成，成功训练{len(successful_models)}个模型")

            return {
                'success': True,
                'trained_models': successful_models,
                'training_results': training_results,
                'model_count': len(successful_models)
            }

        except Exception as e:
            logger.error(f"训练模型失败: {e}")
            return {'success': False, 'error': str(e)}

    def _evaluate_models(self) -> Dict[str, Any]:
        """评估模型"""
        try:
            logger.info("📈 开始评估模型...")

            # 获取特征重要性分析
            feature_importance = self.predictor.get_feature_importance_analysis(top_n=20)

            # 模拟预测测试
            if hasattr(self, 'training_features') and len(self.training_features) > 0:
                # 使用第一个样本进行预测测试
                test_features = self.training_features.iloc[0].to_dict()
                prediction_result = self.predictor.predict_limit_up_probability(test_features)

                return {
                    'success': True,
                    'feature_importance': feature_importance,
                    'prediction_test': prediction_result,
                    'evaluation_completed': True
                }
            else:
                return {
                    'success': True,
                    'feature_importance': feature_importance,
                    'evaluation_completed': False,
                    'note': '没有测试数据进行预测验证'
                }

        except Exception as e:
            logger.error(f"评估模型失败: {e}")
            return {'success': False, 'error': str(e)}

    def _save_training_results(self, pipeline_results: Dict[str, Any]) -> Dict[str, Any]:
        """保存训练结果"""
        try:
            logger.info("💾 开始保存训练结果...")

            if not self.config['save_intermediate_results']:
                return {'success': True, 'note': '配置为不保存中间结果'}

            # 创建结果目录
            results_dir = "training_results"
            os.makedirs(results_dir, exist_ok=True)

            # 保存训练结果
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            results_file = os.path.join(results_dir, f"training_results_{timestamp}.json")

            import json
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(pipeline_results, f, ensure_ascii=False, indent=2, default=str)

            # 获取数据库统计
            db_stats = self.data_storage.get_database_statistics()

            logger.info(f"✅ 训练结果已保存: {results_file}")

            return {
                'success': True,
                'results_file': results_file,
                'database_stats': db_stats,
                'save_timestamp': timestamp
            }

        except Exception as e:
            logger.error(f"保存训练结果失败: {e}")
            return {'success': False, 'error': str(e)}

    def run_quick_training_demo(self) -> Dict[str, Any]:
        """运行快速训练演示"""
        try:
            logger.info("🚀 开始快速训练演示...")

            # 创建模拟数据进行演示
            demo_results = {}

            # 1. 模拟数据收集
            logger.info("📊 模拟数据收集...")
            demo_limit_up_data = [
                {
                    'stock_code': '000001',
                    'stock_name': '平安银行',
                    'limit_up_date': '2024-07-10',
                    'close_price': 12.50,
                    'change_pct': 10.0,
                    'turnover_rate': 3.5,
                    'volume': 50000000,
                    'amount': 625000000,
                    'consecutive_days': 1,
                    'reason': '金融板块上涨'
                }
            ]

            stored_count = self.data_storage.store_limit_up_stocks(demo_limit_up_data)
            demo_results['stored_limit_up'] = stored_count

            # 2. 模拟特征数据
            logger.info("🔧 模拟特征工程...")
            demo_features = {
                'morning_volume_active': 1,
                'active_volume_pattern': 1,
                'near_recent_high': 1,
                'stable_price_pattern': 1,
                'volume_price_positive_correlation': 1,
                'price_breakthrough': 0,
                'ma5_above_ma10': 1,
                'macd_golden_cross': 0,
                'rsi_bullish_zone': 1,
                'main_inflow_positive': 1,
                'famous_seats_count': 2,
                'technical_signal_score': 0.75,
                'capital_flow_score': 0.68,
                'chip_concentration': 0.45
            }

            # 创建多个样本
            for i in range(20):
                # 正样本
                features_pos = demo_features.copy()
                # 添加一些随机变化
                for key in features_pos:
                    if isinstance(features_pos[key], (int, float)):
                        noise = np.random.normal(0, 0.1)
                        features_pos[key] = max(0, features_pos[key] + noise)

                success = self.data_storage.store_stock_features(
                    '000001', f'2024-07-{9+i:02d}', features_pos, label=1
                )

                # 负样本
                features_neg = demo_features.copy()
                for key in features_neg:
                    if isinstance(features_neg[key], (int, float)):
                        features_neg[key] = max(0, features_neg[key] * np.random.uniform(0.3, 0.7))

                success = self.data_storage.store_stock_features(
                    '000001', f'2024-06-{10+i:02d}', features_neg, label=0
                )

            demo_results['created_samples'] = 40

            # 3. 准备训练数据
            logger.info("📋 准备训练数据...")
            features_list, labels_list = self.data_storage.get_training_dataset(min_features=5)

            if features_list:
                features_df, labels_array = self.predictor.prepare_training_data(features_list, labels_list)
                demo_results['training_samples'] = len(features_df)
                demo_results['feature_count'] = len(features_df.columns)

                # 4. 训练模型
                logger.info("🤖 训练模型...")
                training_results = self.predictor.train_models(features_df, labels_array, test_size=0.3)
                demo_results['training_results'] = training_results

                # 5. 测试预测
                logger.info("🔮 测试预测...")
                test_features = features_df.iloc[0].to_dict()
                prediction = self.predictor.predict_limit_up_probability(test_features)
                demo_results['prediction_test'] = prediction

            demo_results['demo_success'] = True
            logger.info("🎉 快速训练演示完成！")
            return demo_results

        except Exception as e:
            logger.error(f"快速训练演示失败: {e}")
            return {'demo_success': False, 'error': str(e)}

    def close(self):
        """关闭资源"""
        try:
            self.data_storage.close()
            logger.info("✅ 训练流程资源已关闭")
        except Exception as e:
            logger.error(f"关闭资源失败: {e}")

def main():
    """测试训练流程"""
    print("🚀 测试模型训练流程...")

    pipeline = TrainingPipeline()

    # 运行快速训练演示
    print("\n🔄 运行快速训练演示...")
    demo_results = pipeline.run_quick_training_demo()

    if demo_results.get('demo_success', False):
        print("✅ 快速训练演示成功！")
        print(f"  创建样本数: {demo_results.get('created_samples', 0)}")
        print(f"  训练样本数: {demo_results.get('training_samples', 0)}")
        print(f"  特征数量: {demo_results.get('feature_count', 0)}")

        if 'prediction_test' in demo_results:
            pred = demo_results['prediction_test']
            print(f"  预测测试: {pred.get('ensemble_prediction', 'N/A')}")
            print(f"  预测概率: {pred.get('ensemble_probability', 0):.3f}")
    else:
        print("❌ 快速训练演示失败")
        print(f"  错误: {demo_results.get('error', 'Unknown error')}")

    # 关闭资源
    pipeline.close()

if __name__ == "__main__":
    main()
