#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新闻情绪处理器
集成新闻数据和社交媒体数据，进行情绪分析和热点识别
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import re
import jieba
from collections import Counter
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NewsSentimentProcessor:
    """新闻情绪处理器"""
    
    def __init__(self):
        self.data_cache = {}  # 数据缓存
        self.cache_timeout = 1800  # 缓存30分钟
        
        # 情绪词典配置
        self.sentiment_dict = {
            'positive_words': [
                '上涨', '大涨', '暴涨', '飙升', '突破', '创新高', '利好', '看好', '乐观',
                '增长', '盈利', '业绩', '超预期', '强势', '活跃', '热点', '机会',
                '买入', '推荐', '目标价', '上调', '看涨', '牛市', '反弹', '回升'
            ],
            'negative_words': [
                '下跌', '大跌', '暴跌', '跳水', '破位', '创新低', '利空', '看空', '悲观',
                '下滑', '亏损', '业绩', '不及预期', '弱势', '低迷', '风险', '危机',
                '卖出', '减持', '下调', '看跌', '熊市', '调整', '回调', '下行'
            ],
            'neutral_words': [
                '平稳', '震荡', '整理', '观望', '等待', '维持', '持平', '稳定'
            ]
        }
        
        # 热点关键词配置
        self.hot_keywords = {
            'technology': ['人工智能', 'AI', '芯片', '半导体', '5G', '物联网', '云计算', '大数据'],
            'new_energy': ['新能源', '电动车', '锂电池', '光伏', '风电', '储能', '充电桩'],
            'healthcare': ['医药', '生物', '疫苗', '医疗', '器械', '创新药', 'CRO'],
            'finance': ['银行', '保险', '券商', '金融', '支付', '数字货币', '区块链'],
            'consumption': ['消费', '白酒', '食品', '零售', '电商', '直播', '品牌'],
            'infrastructure': ['基建', '房地产', '建筑', '水泥', '钢铁', '工程', '城建'],
            'military': ['军工', '航空', '航天', '国防', '武器', '雷达', '导弹'],
            'environment': ['环保', '碳中和', '节能', '减排', '绿色', '清洁', '循环']
        }
        
        # 新闻源权重配置
        self.news_source_weights = {
            '新华社': 1.0,
            '人民日报': 1.0,
            '央视新闻': 0.9,
            '证券时报': 0.8,
            '上海证券报': 0.8,
            '中国证券报': 0.8,
            '财经': 0.7,
            '21世纪经济报道': 0.7,
            '第一财经': 0.7,
            '东方财富': 0.6,
            '同花顺': 0.6,
            '雪球': 0.5,
            '其他': 0.4
        }
        
        logger.info("📰 新闻情绪处理器初始化完成")
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (datetime.now().timestamp() - cache_time) < self.cache_timeout
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now().timestamp()
        }
    
    def _get_cache(self, cache_key: str) -> Any:
        """获取缓存数据"""
        return self.data_cache[cache_key]['data']
    
    def analyze_text_sentiment(self, text: str) -> Dict[str, Any]:
        """分析文本情绪"""
        try:
            if not text or not text.strip():
                return self._get_empty_sentiment_result()
            
            # 文本预处理
            cleaned_text = self._preprocess_text(text)
            
            # 分词
            words = list(jieba.cut(cleaned_text))
            
            # 情绪词统计
            positive_count = sum(1 for word in words if word in self.sentiment_dict['positive_words'])
            negative_count = sum(1 for word in words if word in self.sentiment_dict['negative_words'])
            neutral_count = sum(1 for word in words if word in self.sentiment_dict['neutral_words'])
            
            total_sentiment_words = positive_count + negative_count + neutral_count
            
            # 计算情绪评分
            if total_sentiment_words == 0:
                sentiment_score = 0.5  # 中性
                sentiment_label = 'neutral'
            else:
                # 加权计算情绪评分
                positive_weight = positive_count / total_sentiment_words
                negative_weight = negative_count / total_sentiment_words
                
                sentiment_score = positive_weight - negative_weight * 0.5 + 0.5
                sentiment_score = max(0, min(sentiment_score, 1))
                
                if sentiment_score > 0.6:
                    sentiment_label = 'positive'
                elif sentiment_score < 0.4:
                    sentiment_label = 'negative'
                else:
                    sentiment_label = 'neutral'
            
            # 提取关键词
            keywords = self._extract_keywords(words)
            
            # 识别热点话题
            hot_topics = self._identify_hot_topics(text)
            
            return {
                'sentiment_score': sentiment_score,
                'sentiment_label': sentiment_label,
                'positive_words_count': positive_count,
                'negative_words_count': negative_count,
                'neutral_words_count': neutral_count,
                'keywords': keywords,
                'hot_topics': hot_topics,
                'text_length': len(text),
                'analysis_success': True
            }
            
        except Exception as e:
            logger.error(f"文本情绪分析失败: {e}")
            return self._get_empty_sentiment_result()
    
    def _preprocess_text(self, text: str) -> str:
        """文本预处理"""
        try:
            # 去除HTML标签
            text = re.sub(r'<[^>]+>', '', text)
            
            # 去除特殊字符，保留中文、英文、数字
            text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9\s]', '', text)
            
            # 去除多余空格
            text = re.sub(r'\s+', ' ', text).strip()
            
            return text
            
        except Exception as e:
            logger.error(f"文本预处理失败: {e}")
            return text
    
    def _extract_keywords(self, words: List[str], top_k: int = 10) -> List[Dict[str, Any]]:
        """提取关键词"""
        try:
            # 过滤停用词和短词
            filtered_words = [
                word for word in words 
                if len(word) >= 2 and word not in ['的', '了', '在', '是', '有', '和', '与', '或']
            ]
            
            # 统计词频
            word_counts = Counter(filtered_words)
            
            # 返回前top_k个关键词
            keywords = []
            for word, count in word_counts.most_common(top_k):
                keywords.append({
                    'word': word,
                    'count': count,
                    'importance': count / len(filtered_words) if filtered_words else 0
                })
            
            return keywords
            
        except Exception as e:
            logger.error(f"关键词提取失败: {e}")
            return []
    
    def _identify_hot_topics(self, text: str) -> List[Dict[str, Any]]:
        """识别热点话题"""
        try:
            hot_topics = []
            
            for topic_category, keywords in self.hot_keywords.items():
                topic_score = 0
                matched_keywords = []
                
                for keyword in keywords:
                    if keyword in text:
                        topic_score += 1
                        matched_keywords.append(keyword)
                
                if topic_score > 0:
                    hot_topics.append({
                        'category': topic_category,
                        'score': topic_score,
                        'matched_keywords': matched_keywords,
                        'relevance': topic_score / len(keywords)
                    })
            
            # 按相关性排序
            hot_topics.sort(key=lambda x: x['relevance'], reverse=True)
            
            return hot_topics
            
        except Exception as e:
            logger.error(f"热点话题识别失败: {e}")
            return []
    
    def analyze_news_batch(self, news_list: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量分析新闻情绪"""
        try:
            if not news_list:
                return self._get_empty_batch_result()
            
            logger.info(f"🔍 开始批量分析{len(news_list)}条新闻...")
            
            all_sentiments = []
            all_hot_topics = []
            source_sentiments = {}
            
            for news in news_list:
                title = news.get('title', '')
                content = news.get('content', '')
                source = news.get('source', '其他')
                publish_time = news.get('publish_time', '')
                
                # 分析标题和内容
                full_text = f"{title} {content}"
                sentiment_result = self.analyze_text_sentiment(full_text)
                
                if sentiment_result['analysis_success']:
                    # 应用新闻源权重
                    source_weight = self.news_source_weights.get(source, 0.4)
                    weighted_score = sentiment_result['sentiment_score'] * source_weight
                    
                    sentiment_result['source'] = source
                    sentiment_result['source_weight'] = source_weight
                    sentiment_result['weighted_score'] = weighted_score
                    sentiment_result['publish_time'] = publish_time
                    
                    all_sentiments.append(sentiment_result)
                    all_hot_topics.extend(sentiment_result['hot_topics'])
                    
                    # 按新闻源分类统计
                    if source not in source_sentiments:
                        source_sentiments[source] = []
                    source_sentiments[source].append(weighted_score)
            
            # 计算综合情绪
            if all_sentiments:
                avg_sentiment = np.mean([s['weighted_score'] for s in all_sentiments])
                sentiment_distribution = self._calculate_sentiment_distribution(all_sentiments)
            else:
                avg_sentiment = 0.5
                sentiment_distribution = {'positive': 0, 'negative': 0, 'neutral': 0}
            
            # 统计热点话题
            topic_summary = self._summarize_hot_topics(all_hot_topics)
            
            # 计算新闻源影响力
            source_influence = self._calculate_source_influence(source_sentiments)
            
            result = {
                'total_news': len(news_list),
                'analyzed_news': len(all_sentiments),
                'average_sentiment': avg_sentiment,
                'sentiment_distribution': sentiment_distribution,
                'hot_topics_summary': topic_summary,
                'source_influence': source_influence,
                'detailed_sentiments': all_sentiments,
                'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'analysis_success': True
            }
            
            logger.info(f"✅ 新闻批量分析完成，平均情绪: {avg_sentiment:.3f}")
            return result
            
        except Exception as e:
            logger.error(f"新闻批量分析失败: {e}")
            return self._get_empty_batch_result()
    
    def _calculate_sentiment_distribution(self, sentiments: List[Dict]) -> Dict[str, float]:
        """计算情绪分布"""
        try:
            total = len(sentiments)
            if total == 0:
                return {'positive': 0, 'negative': 0, 'neutral': 0}
            
            positive_count = sum(1 for s in sentiments if s['sentiment_label'] == 'positive')
            negative_count = sum(1 for s in sentiments if s['sentiment_label'] == 'negative')
            neutral_count = total - positive_count - negative_count
            
            return {
                'positive': positive_count / total,
                'negative': negative_count / total,
                'neutral': neutral_count / total
            }
            
        except Exception as e:
            logger.error(f"计算情绪分布失败: {e}")
            return {'positive': 0, 'negative': 0, 'neutral': 0}
    
    def _summarize_hot_topics(self, all_topics: List[Dict]) -> Dict[str, Any]:
        """汇总热点话题"""
        try:
            if not all_topics:
                return {}
            
            topic_stats = {}
            for topic in all_topics:
                category = topic['category']
                if category not in topic_stats:
                    topic_stats[category] = {
                        'count': 0,
                        'total_score': 0,
                        'keywords': set()
                    }
                
                topic_stats[category]['count'] += 1
                topic_stats[category]['total_score'] += topic['score']
                topic_stats[category].update(topic['matched_keywords'])
            
            # 计算热度排名
            topic_ranking = []
            for category, stats in topic_stats.items():
                avg_score = stats['total_score'] / stats['count']
                topic_ranking.append({
                    'category': category,
                    'mention_count': stats['count'],
                    'average_score': avg_score,
                    'heat_index': stats['count'] * avg_score,
                    'keywords': list(stats['keywords'])[:5]  # 取前5个关键词
                })
            
            # 按热度指数排序
            topic_ranking.sort(key=lambda x: x['heat_index'], reverse=True)
            
            return {
                'total_topics': len(all_topics),
                'unique_categories': len(topic_stats),
                'top_topics': topic_ranking[:10]
            }
            
        except Exception as e:
            logger.error(f"汇总热点话题失败: {e}")
            return {}
    
    def _calculate_source_influence(self, source_sentiments: Dict[str, List[float]]) -> Dict[str, Any]:
        """计算新闻源影响力"""
        try:
            source_influence = {}
            
            for source, scores in source_sentiments.items():
                if scores:
                    avg_score = np.mean(scores)
                    score_std = np.std(scores)
                    news_count = len(scores)
                    
                    # 影响力 = 新闻数量 * 平均情绪 * 权重
                    weight = self.news_source_weights.get(source, 0.4)
                    influence = news_count * abs(avg_score - 0.5) * weight
                    
                    source_influence[source] = {
                        'news_count': news_count,
                        'average_sentiment': avg_score,
                        'sentiment_volatility': score_std,
                        'source_weight': weight,
                        'influence_score': influence
                    }
            
            # 按影响力排序
            sorted_sources = sorted(
                source_influence.items(),
                key=lambda x: x[1]['influence_score'],
                reverse=True
            )
            
            return dict(sorted_sources)
            
        except Exception as e:
            logger.error(f"计算新闻源影响力失败: {e}")
            return {}
    
    def _get_empty_sentiment_result(self) -> Dict[str, Any]:
        """获取空的情绪分析结果"""
        return {
            'sentiment_score': 0.5,
            'sentiment_label': 'neutral',
            'positive_words_count': 0,
            'negative_words_count': 0,
            'neutral_words_count': 0,
            'keywords': [],
            'hot_topics': [],
            'text_length': 0,
            'analysis_success': False
        }
    
    def _get_empty_batch_result(self) -> Dict[str, Any]:
        """获取空的批量分析结果"""
        return {
            'total_news': 0,
            'analyzed_news': 0,
            'average_sentiment': 0.5,
            'sentiment_distribution': {'positive': 0, 'negative': 0, 'neutral': 0},
            'hot_topics_summary': {},
            'source_influence': {},
            'detailed_sentiments': [],
            'analysis_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'analysis_success': False
        }

def main():
    """测试新闻情绪处理器"""
    print("📰 测试新闻情绪处理器...")
    
    processor = NewsSentimentProcessor()
    
    # 测试单条文本分析
    test_text = "A股市场今日大涨，科技股表现强势，人工智能概念股涨停潮，投资者情绪乐观"
    print(f"\n🔍 测试文本情绪分析...")
    print(f"测试文本: {test_text}")
    
    sentiment_result = processor.analyze_text_sentiment(test_text)
    print(f"情绪评分: {sentiment_result['sentiment_score']:.3f}")
    print(f"情绪标签: {sentiment_result['sentiment_label']}")
    print(f"关键词: {[kw['word'] for kw in sentiment_result['keywords'][:5]]}")
    print(f"热点话题: {[topic['category'] for topic in sentiment_result['hot_topics']]}")
    
    # 测试批量新闻分析
    test_news = [
        {
            'title': '科技股大涨，AI概念持续火热',
            'content': '人工智能板块今日表现强势，多只个股涨停',
            'source': '证券时报',
            'publish_time': '2024-07-13 10:00:00'
        },
        {
            'title': '新能源汽车销量创新高',
            'content': '电动车市场持续增长，锂电池需求旺盛',
            'source': '第一财经',
            'publish_time': '2024-07-13 11:00:00'
        }
    ]
    
    print(f"\n🔍 测试批量新闻分析...")
    batch_result = processor.analyze_news_batch(test_news)
    print(f"分析新闻数: {batch_result['analyzed_news']}/{batch_result['total_news']}")
    print(f"平均情绪: {batch_result['average_sentiment']:.3f}")
    print(f"情绪分布: {batch_result['sentiment_distribution']}")

if __name__ == "__main__":
    main()
