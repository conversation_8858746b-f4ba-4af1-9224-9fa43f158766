#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查K线数据问题
"""

import sqlite3
import pandas as pd

def check_kline_data():
    """检查K线数据"""
    print("🔍 检查K线数据问题...")
    
    # 连接数据库
    conn = sqlite3.connect('historical_data.db')
    
    try:
        # 检查K线数据总数
        count = conn.execute('SELECT COUNT(*) FROM stock_kline_data').fetchone()[0]
        print(f"📊 K线数据总记录数: {count}")
        
        if count > 0:
            # 按股票分组统计
            stock_counts = pd.read_sql(
                'SELECT stock_code, COUNT(*) as count, MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_kline_data GROUP BY stock_code ORDER BY count DESC', 
                conn
            )
            print(f"📊 股票分布:")
            print(stock_counts)
            
            # 检查603880的数据（训练日志中查询失败的股票）
            print(f"\n📊 603880的K线数据:")
            stock_603880 = pd.read_sql('SELECT * FROM stock_kline_data WHERE stock_code = "603880"', conn)
            print(f"   记录数: {len(stock_603880)}")
            if not stock_603880.empty:
                print(stock_603880)
            else:
                print("   ❌ 603880没有K线数据")
            
            # 检查日期范围查询
            print(f"\n📊 测试日期范围查询:")
            test_query = '''
            SELECT * FROM stock_kline_data 
            WHERE stock_code = "603880" 
            AND trade_date >= "2024-12-30" 
            AND trade_date <= "2025-01-29"
            '''
            test_result = pd.read_sql(test_query, conn)
            print(f"   查询结果: {len(test_result)}条记录")
            
            # 检查所有股票的日期范围
            print(f"\n📊 所有K线数据的日期范围:")
            date_range = pd.read_sql(
                'SELECT MIN(trade_date) as min_date, MAX(trade_date) as max_date FROM stock_kline_data', 
                conn
            )
            print(date_range)
            
            # 检查2025年1月的数据
            print(f"\n📊 2025年1月的K线数据:")
            jan_2025 = pd.read_sql(
                'SELECT stock_code, COUNT(*) as count FROM stock_kline_data WHERE trade_date LIKE "2025-01%" GROUP BY stock_code', 
                conn
            )
            print(f"   2025年1月有数据的股票数: {len(jan_2025)}")
            if not jan_2025.empty:
                print(jan_2025.head(10))
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_kline_data()
