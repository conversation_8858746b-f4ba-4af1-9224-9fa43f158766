#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复特征不匹配问题
"""

import joblib
import json
import os

def fix_feature_names():
    """修复特征名称文件"""
    print("🔧 修复特征名称文件...")
    
    try:
        # 从训练结果中获取正确的特征名称
        results_dir = "ui_integration/training_results"
        result_files = [f for f in os.listdir(results_dir) if f.endswith('.json')]
        latest_file = sorted(result_files)[-1]
        result_path = os.path.join(results_dir, latest_file)
        
        with open(result_path, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        # 获取正确的特征名称
        data_prep = results.get('data_preparation', {})
        correct_feature_names = data_prep.get('feature_names', [])
        
        print(f"📊 从训练结果获取特征名称:")
        print(f"  特征数量: {len(correct_feature_names)}")
        print(f"  前10个特征: {correct_feature_names[:10]}")
        
        # 备份原文件
        original_file = 'trained_models/feature_names.joblib'
        backup_file = 'trained_models/feature_names_backup.joblib'
        
        if os.path.exists(original_file):
            import shutil
            shutil.copy2(original_file, backup_file)
            print(f"✅ 备份原文件: {backup_file}")
        
        # 保存正确的特征名称
        joblib.dump(correct_feature_names, original_file)
        print(f"✅ 保存正确的特征名称: {original_file}")
        
        return True
        
    except Exception as e:
        print(f"❌ 修复特征名称失败: {e}")
        return False

def verify_fix():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    
    try:
        # 重新加载文件
        feature_names = joblib.load('trained_models/feature_names.joblib')
        scaler = joblib.load('trained_models/scaler.joblib')
        
        print(f"📊 修复后状态:")
        print(f"  特征名称数量: {len(feature_names)}")
        print(f"  标准化器期望: {scaler.n_features_in_}")
        
        if len(feature_names) == scaler.n_features_in_:
            print("✅ 特征数量匹配！")
            return True
        else:
            print("❌ 特征数量仍不匹配")
            
            # 如果还是不匹配，可能需要重新训练标准化器
            print("💡 可能需要重新训练标准化器或使用不同的模型文件")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_prediction_after_fix():
    """修复后测试预测"""
    print("\n🧪 测试修复后的预测...")
    
    try:
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        
        from training.model_predictor import ModelPredictor
        
        # 重新创建预测器
        predictor = ModelPredictor()
        
        print(f"📊 预测器状态:")
        print(f"  模型数量: {len(predictor.models)}")
        print(f"  特征数量: {len(predictor.feature_columns)}")
        print(f"  标准化器: {'已加载' if predictor.scaler else '未加载'}")
        
        if len(predictor.models) > 0 and len(predictor.feature_columns) > 0:
            # 创建测试特征（使用训练时的特征）
            test_features = {
                'close_price': 15.5,
                'price_change_5d': 0.05,
                'price_change_10d': 0.08,
                'volume_avg_5d': 1000000,
                'volume_ratio': 1.2,
                'turnover_rate': 3.5,
                'turnover_avg_5d': 3.0,
                'amplitude': 5.2,
                'price_vs_ma5': 1.02,
                'price_vs_ma10': 1.05,
                'ma5_slope': 0.02,
                'ma10_slope': 0.01,
                'rsi': 65.0,
                'macd_signal': 1,
                'bollinger_position': 0.7,
                'volume_ma_ratio': 1.1,
                'price_volatility': 0.03,
                'main_net_inflow': 1000000,
                'main_net_inflow_ratio': 0.05,
                'retail_net_inflow': -500000,
                'large_order_ratio': 0.3,
                'is_shenzhen': 1,
                'is_shanghai': 0,
                'is_sme_board': 1,
                'high_quality': 1,
                'medium_quality': 0,
                'low_quality': 0,
                'technical_signal_count': 3
            }
            
            # 进行预测
            result = predictor.predict_single_stock(test_features)
            
            print(f"📈 预测测试结果:")
            print(f"  成功: {result.get('success', False)}")
            
            if result.get('success', False):
                print(f"  预测分数: {result.get('prediction_score', 0):.3f}")
                print("✅ 预测功能修复成功！")
                return True
            else:
                print(f"  错误: {result.get('error', 'Unknown')}")
                print("❌ 预测功能仍有问题")
                return False
        else:
            print("❌ 预测器初始化失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试预测失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 修复特征不匹配问题...")
    print("="*60)
    
    # 修复特征名称
    fix_success = fix_feature_names()
    
    if not fix_success:
        print("❌ 修复失败")
        return
    
    # 验证修复
    verify_success = verify_fix()
    
    if verify_success:
        # 测试预测
        test_success = test_prediction_after_fix()
        
        print("\n" + "="*60)
        print("📋 修复结论:")
        
        if test_success:
            print("✅ 特征不匹配问题已修复")
            print("🎉 模型预测功能恢复正常")
            print("💡 现在可以重新运行潜在股调查")
        else:
            print("⚠️ 特征匹配但预测仍有问题")
    else:
        print("\n❌ 修复不完整，可能需要重新训练模型")
    
    print("\n🎉 修复完成！")

if __name__ == "__main__":
    main()
