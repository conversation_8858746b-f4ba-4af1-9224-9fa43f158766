#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断筛选问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_market_cap_logic():
    """测试市值逻辑"""
    print("🔍 诊断市值筛选逻辑...")
    
    # 模拟不同单位的市值数据
    test_cases = [
        {'name': '万元单位小盘股', 'market_cap': 300000, 'expected_converted': 30},      # 30万万元 = 30亿
        {'name': '万元单位中盘股', 'market_cap': 500000, 'expected_converted': 50},      # 50万万元 = 50亿  
        {'name': '万元单位大盘股', 'market_cap': 1000000, 'expected_converted': 100},    # 100万万元 = 100亿
        {'name': '亿元单位小盘股', 'market_cap': 30, 'expected_converted': 30},          # 30亿
        {'name': '亿元单位中盘股', 'market_cap': 50, 'expected_converted': 50},          # 50亿
        {'name': '亿元单位大盘股', 'market_cap': 100, 'expected_converted': 100},        # 100亿
    ]
    
    def convert_market_cap(market_cap):
        """模拟市值转换逻辑"""
        market_cap = float(market_cap) if market_cap else 0
        # 如果是万元单位，转换为亿元
        if market_cap > 1000:
            market_cap = market_cap / 10000
        return market_cap
    
    print("📊 市值转换测试:")
    for case in test_cases:
        original = case['market_cap']
        converted = convert_market_cap(original)
        expected = case['expected_converted']
        
        status = "✅" if abs(converted - expected) < 0.01 else "❌"
        print(f"  {case['name']}: {original} → {converted}亿 (期望:{expected}亿) {status}")
    
    # 测试筛选逻辑
    print(f"\n🔍 筛选逻辑测试 (30-130亿):")
    
    # 原始错误配置
    old_min, old_max = 30e8, 130e8  # 3000亿, 13000亿
    # 修复后配置  
    new_min, new_max = 30, 130      # 30亿, 130亿
    
    for case in test_cases:
        converted = convert_market_cap(case['market_cap'])
        
        old_pass = old_min <= converted <= old_max
        new_pass = new_min <= converted <= new_max
        
        print(f"  {case['name']}({converted}亿):")
        print(f"    原配置({old_min/1e8:.0f}-{old_max/1e8:.0f}亿): {'✅通过' if old_pass else '❌排除'}")
        print(f"    新配置({new_min}-{new_max}亿): {'✅通过' if new_pass else '❌排除'}")
    
    return True

def test_actual_data_source():
    """测试实际数据源"""
    print("\n🔍 测试实际数据源...")
    
    try:
        from data_fetchers.efinance_data_fetcher import EfinanceDataFetcher
        
        fetcher = EfinanceDataFetcher()
        print("📊 获取少量市场数据进行分析...")
        
        # 获取市场数据
        market_data = fetcher.get_all_stocks_realtime()
        
        if not market_data:
            print("❌ 无法获取市场数据")
            return False
        
        print(f"✅ 获取到 {len(market_data)} 只股票")
        
        # 分析前10只股票的市值数据
        print("\n📋 前10只股票市值数据分析:")
        for i, stock in enumerate(market_data[:10]):
            market_cap_raw = (stock.get('总市值', 0) or
                             stock.get('market_cap', 0) or  
                             stock.get('市值', 0) or
                             stock.get('流通市值', 0))
            
            # 应用转换逻辑
            market_cap_converted = float(market_cap_raw) if market_cap_raw else 0
            if market_cap_converted > 1000:
                market_cap_converted = market_cap_converted / 10000
            
            code = stock.get('代码', '') or stock.get('code', '')
            name = stock.get('名称', '') or stock.get('name', '')
            
            print(f"  {i+1}. {code} {name}")
            print(f"     原始市值: {market_cap_raw}")
            print(f"     转换后: {market_cap_converted}亿")
        
        # 统计市值分布
        market_caps = []
        for stock in market_data[:100]:  # 分析前100只
            market_cap_raw = (stock.get('总市值', 0) or
                             stock.get('market_cap', 0) or
                             stock.get('市值', 0) or
                             stock.get('流通市值', 0))
            
            market_cap_converted = float(market_cap_raw) if market_cap_raw else 0
            if market_cap_converted > 1000:
                market_cap_converted = market_cap_converted / 10000
            
            if market_cap_converted > 0:
                market_caps.append(market_cap_converted)
        
        if market_caps:
            print(f"\n📈 市值分布统计 (前100只股票):")
            print(f"  有效数据: {len(market_caps)}/100")
            print(f"  最小市值: {min(market_caps):.2f}亿")
            print(f"  最大市值: {max(market_caps):.2f}亿")
            print(f"  平均市值: {sum(market_caps)/len(market_caps):.2f}亿")
            
            # 统计在30-130亿范围内的股票数量
            in_range = [cap for cap in market_caps if 30 <= cap <= 130]
            print(f"  30-130亿范围内: {len(in_range)}/{len(market_caps)} ({len(in_range)/len(market_caps)*100:.1f}%)")
            
            if len(in_range) == 0:
                print("❌ 没有股票在30-130亿范围内，这可能是问题所在！")
                return False
            else:
                print("✅ 有股票在目标范围内")
                return True
        else:
            print("❌ 没有有效的市值数据")
            return False
            
    except Exception as e:
        print(f"❌ 测试实际数据源失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 筛选问题完整诊断...")
    print("="*60)
    
    # 测试市值逻辑
    logic_ok = test_market_cap_logic()
    
    # 测试实际数据源
    data_ok = test_actual_data_source()
    
    print("\n" + "="*60)
    print("📋 诊断结论:")
    
    if logic_ok and data_ok:
        print("✅ 市值筛选逻辑正常，数据源正常")
        print("💡 问题可能在其他筛选条件上")
    elif logic_ok and not data_ok:
        print("✅ 市值筛选逻辑正常")
        print("❌ 数据源问题：没有股票在30-130亿范围内")
        print("💡 建议调整市值范围或检查数据源单位")
    else:
        print("❌ 市值筛选逻辑或数据源有问题")
    
    print("\n🎉 诊断完成！")

if __name__ == "__main__":
    main()
