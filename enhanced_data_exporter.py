#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的数据导出模块
支持导出全面详细的股票分析数据，为AI分析提供完整数据基础
包含：基础信息、技术面、资金面、盘口、概念题材等全方位数据
"""

import pandas as pd
import os
import numpy as np
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import logging

# 导入新开发的分析模块
try:
    from chip_distribution_analyzer import ChipDistributionAnalyzer
    from sector_linkage_analyzer import SectorLinkageAnalyzer
    from market_sentiment_data_collector import MarketSentimentDataCollector
    from opening_features_analyzer import OpeningFeaturesAnalyzer
    ADVANCED_MODULES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 高级分析模块导入失败: {e}")
    ADVANCED_MODULES_AVAILABLE = False

logger = logging.getLogger(__name__)

class EnhancedDataExporter:
    """增强的数据导出器 - 为AI分析提供全面数据支持"""

    def __init__(self):
        """初始化"""
        self.export_time = None
        self.data_quality_report = {}

        # 初始化高级分析器
        if ADVANCED_MODULES_AVAILABLE:
            try:
                self.chip_analyzer = ChipDistributionAnalyzer()
                self.sector_analyzer = SectorLinkageAnalyzer()
                self.sentiment_collector = MarketSentimentDataCollector()
                self.opening_analyzer = OpeningFeaturesAnalyzer()
                self.advanced_analyzers_ready = True
                logger.info("高级分析器初始化成功")
            except Exception as e:
                logger.warning(f"高级分析器初始化失败: {e}")
                self.advanced_analyzers_ready = False
        else:
            self.advanced_analyzers_ready = False

        # 字段名中英文对照表
        self.field_translations = {
            # 实时行情字段
            'stock_code': '股票代码',
            'short_name': '股票名称',
            'price': '当前价格',
            'change': '涨跌额',
            'change_pct': '涨跌幅(%)',
            'volume': '成交量(股)',
            'amount': '成交额(元)',
            'update_time': '更新时间',

            # 五档行情字段
            's5': '卖五价', 'sv5': '卖五量',
            's4': '卖四价', 'sv4': '卖四量',
            's3': '卖三价', 'sv3': '卖三量',
            's2': '卖二价', 'sv2': '卖二量',
            's1': '卖一价', 'sv1': '卖一量',
            'b1': '买一价', 'bv1': '买一量',
            'b2': '买二价', 'bv2': '买二量',
            'b3': '买三价', 'bv3': '买三量',
            'b4': '买四价', 'bv4': '买四量',
            'b5': '买五价', 'bv5': '买五量',

            # K线数据字段
            'date': '日期',
            'trade_time': '交易时间',
            'open': '开盘价',
            'high': '最高价',
            'low': '最低价',
            'close': '收盘价',
            'pre_close': '昨收价',
            'turnover_rate': '换手率(%)',
            'turnover_ratio': '换手率(%)',
            'pe': '市盈率',
            'pb': '市净率',

            # 资金流向字段
            'trade_date': '交易日期',
            'main_net_inflow': '主力净流入(万元)',
            'super_large_net_inflow': '超大单净流入(万元)',
            'large_net_inflow': '大单净流入(万元)',
            'medium_net_inflow': '中单净流入(万元)',
            'small_net_inflow': '小单净流入(万元)',
            'sm_net_inflow': '小单净流入(万元)',
            'mid_net_inflow': '中单净流入(万元)',
            'lg_net_inflow': '大单净流入(万元)',
            'max_net_inflow': '超大单净流入(万元)',

            # 龙虎榜字段
            'change_cpt': '涨跌幅(%)',
            'a_net_amount': '机构净买入(元)',
            'a_buy_amount': '机构买入(元)',
            'a_sell_amount': '机构卖出(元)',
            'a_amount': '机构成交额(元)',
            'net_amount_rate': '净买入占比(%)',
            'a_amount_rate': '机构成交占比(%)',
            'reason': '上榜原因',

            # 概念板块字段
            'name': '概念名称',
            'concept_name': '概念名称',
            'concept_code': '概念代码',
            'stocks_count': '成分股数量',
            'source': '数据来源',

            # 基本信息字段
            'code': '股票代码',
            'exchange': '交易所',
            'industry': '所属行业',
            'list_date': '上市日期',
            'total_market_value': '总市值(元)',
            'circulation_market_value': '流通市值(元)',
            'total_shares': '总股本(股)',
            'circulation_shares': '流通股(股)',
            'bvps': '每股净资产',
            'roe': '净资产收益率(%)',
        }

    def _safe_get_float_from_quote(self, quote_df: pd.DataFrame, keys: list, default=0):
        """安全获取实时行情中的浮点数，支持多个可能的字段名"""
        if quote_df.empty:
            return default

        quote = quote_df.iloc[0]
        for key in keys:
            value = quote.get(key, default)
            if value is not None and value != '':
                try:
                    return float(value)
                except (ValueError, TypeError):
                    continue
        return default

    def _validate_realtime_data(self, quote_df: pd.DataFrame) -> pd.DataFrame:
        """验证和修复实时行情数据"""
        if quote_df.empty:
            return quote_df

        quote_df = quote_df.copy()

        for idx, row in quote_df.iterrows():
            # 验证涨跌幅是否合理（绝对值不应超过20%，除非是特殊情况）
            change_pct = self._safe_get_float_from_quote(pd.DataFrame([row]), ['change_pct', '涨跌幅'])
            if abs(change_pct) > 20:
                print(f"⚠️ 检测到异常涨跌幅: {change_pct:.2f}%, 股票: {row.get('stock_code', 'Unknown')}")
                # 重新计算涨跌幅
                current_price = self._safe_get_float_from_quote(pd.DataFrame([row]), ['current_price', 'price', '最新价'])
                prev_close = self._safe_get_float_from_quote(pd.DataFrame([row]), ['prev_close', '昨收盘'])
                if prev_close > 0:
                    corrected_change = current_price - prev_close
                    corrected_change_pct = (corrected_change / prev_close) * 100
                    quote_df.at[idx, 'change'] = corrected_change
                    quote_df.at[idx, 'change_pct'] = corrected_change_pct
                    print(f"✅ 修复涨跌幅: {corrected_change_pct:.2f}%")

        return quote_df

    def _translate_dataframe_columns(self, df: pd.DataFrame) -> pd.DataFrame:
        """将DataFrame的英文列名翻译为中文，并处理数字格式"""
        if df.empty:
            return df

        df_copy = df.copy()

        # 处理股票代码格式，确保保持前导零
        stock_code_columns = ['stock_code', '股票代码']
        for col in stock_code_columns:
            if col in df_copy.columns:
                df_copy[col] = df_copy[col].astype(str).apply(lambda x: x.zfill(6) if x.isdigit() and len(x) <= 6 else x)

        # 处理概念代码格式
        concept_code_columns = ['concept_code', '概念代码']
        for col in concept_code_columns:
            if col in df_copy.columns:
                df_copy[col] = df_copy[col].astype(str)

        # 处理大数字，防止科学计数法
        numeric_columns = ['amount', '成交额(元)', 'volume', '成交量(股)', 'total_market_value',
                          'circulation_market_value', 'total_shares', 'circulation_shares',
                          'a_net_amount', 'a_buy_amount', 'a_sell_amount', 'a_amount']
        for col in numeric_columns:
            if col in df_copy.columns:
                try:
                    # 转换为数值类型，然后格式化为字符串避免科学计数法
                    df_copy[col] = pd.to_numeric(df_copy[col], errors='coerce')
                    df_copy[col] = df_copy[col].apply(lambda x: f"{x:.0f}" if pd.notna(x) and abs(x) >= 1000000 else x)
                except:
                    pass

        # 创建新的列名映射
        new_columns = {}
        for col in df_copy.columns:
            if col in self.field_translations:
                new_columns[col] = self.field_translations[col]
            else:
                new_columns[col] = col  # 保持原名

        df_copy.rename(columns=new_columns, inplace=True)
        return df_copy

    def export_comprehensive_analysis(self, stock_code: str, analysis_data: Dict,
                                    filename: str) -> bool:
        """
        导出全面的综合分析报告
        为AI分析提供完整的数据基础

        Args:
            stock_code: 股票代码
            analysis_data: 综合分析数据
            filename: 文件名

        Returns:
            bool: 导出是否成功
        """
        try:
            self.export_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            self.data_quality_report = {}

            with pd.ExcelWriter(filename, engine='openpyxl') as writer:

                # 1. 创建数据概览页
                self._create_data_overview_sheet(writer, stock_code, analysis_data)

                # 2. 导出基础信息详细页
                self._export_detailed_basic_info(writer, analysis_data.get('basic_info', {}), stock_code)

                # 3. 导出实时行情详细页
                self._export_detailed_realtime_quote(writer, analysis_data.get('realtime_quote', pd.DataFrame()))

                # 4. 导出K线数据及技术分析
                self._export_detailed_kline_data(writer, analysis_data.get('kline_data', pd.DataFrame()), stock_code)

                # 5. 导出五档行情及盘口分析
                self._export_detailed_market_depth(writer, analysis_data.get('market_depth', pd.DataFrame()))

                # 6. 导出龙虎榜数据及分析
                self._export_detailed_lhb_data(writer, analysis_data.get('lhb_data', {}), stock_code)

                # 7. 导出资金流向详细分析
                self._export_detailed_capital_flow(writer, analysis_data.get('capital_flow', pd.DataFrame()))

                # 8. 导出概念板块详细分析
                self._export_detailed_concept_info(writer, analysis_data.get('concept_info', pd.DataFrame()))

                # 9. 技术指标详细分析已删除（根据用户要求去除分析结论）

                # 10. 导出筹码分布分析（使用高级分析器）
                self._export_advanced_chip_distribution_analysis(writer, stock_code)

                # 11. 导出板块联动分析
                self._export_advanced_sector_linkage_analysis(writer, stock_code)

                # 12. 导出市场情绪分析
                self._export_advanced_market_sentiment_analysis(writer, stock_code)

                # 13. 导出开盘特征分析
                self._export_advanced_opening_features_analysis(writer, stock_code)

                # 14. 导出时间周期分析
                self._export_time_cycle_analysis(writer, analysis_data)

                # 15. 导出分时数据详细分析
                self._export_detailed_minute_data(writer, analysis_data.get('minute_data', {}))

                # 16. 导出数据质量报告
                self._export_data_quality_report(writer)

            logger.info(f"全面综合分析报告导出成功: {filename}")
            return True

        except Exception as e:
            logger.error(f"导出综合分析报告失败: {str(e)}")
            return False
    
    def _create_data_overview_sheet(self, writer, stock_code: str, analysis_data: Dict):
        """创建数据概览页"""
        try:
            basic_info = analysis_data.get('basic_info', {})
            realtime_quote = analysis_data.get('realtime_quote', pd.DataFrame())
            kline_data = analysis_data.get('kline_data', pd.DataFrame())
            capital_flow = analysis_data.get('capital_flow', pd.DataFrame())
            concept_info = analysis_data.get('concept_info', pd.DataFrame())
            lhb_data = analysis_data.get('lhb_data', {})

            # 计算数据完整性
            data_completeness = self._calculate_data_completeness(analysis_data)

            overview_data = {
                '数据项目': [
                    '股票代码', '股票名称', '交易所', '行业', '上市日期',
                    '总市值(亿元)', '流通市值(亿元)', '总股本(万股)', '流通股(万股)',
                    '当前价格', '涨跌幅(%)', '成交量', '换手率(%)',
                    '导出时间', '数据来源', '数据完整性',
                    'K线数据条数', '资金流向数据条数', '概念数量', '龙虎榜状态'
                ],
                '数据值': [
                    basic_info.get('code', stock_code),
                    basic_info.get('name', '未知'),
                    basic_info.get('exchange', '未知'),
                    basic_info.get('industry', '未知'),
                    basic_info.get('list_date', '未知'),
                    f"{basic_info.get('total_market_value', 0)/100000000:.2f}",
                    f"{basic_info.get('circulation_market_value', 0)/100000000:.2f}",
                    f"{basic_info.get('total_shares', 0)/10000:.2f}",
                    f"{basic_info.get('circulation_shares', 0)/10000:.2f}",
                    f"{self._safe_get_float_from_quote(realtime_quote, ['current_price', 'price', '最新价']):.2f}" if not realtime_quote.empty else '未知',
                    f"{self._safe_get_float_from_quote(realtime_quote, ['change_pct', '涨跌幅']):.2f}" if not realtime_quote.empty else '未知',
                    f"{self._safe_get_float_from_quote(realtime_quote, ['volume', '成交量']):,.0f}" if not realtime_quote.empty else '未知',
                    "未知",  # 换手率数据暂时不可用
                    self.export_time,
                    'AData + AKShare双数据源',
                    f"{data_completeness:.1f}%",
                    len(kline_data),
                    len(capital_flow),
                    len(concept_info),
                    '上榜' if lhb_data.get('stock_in_lhb', False) else '未上榜'
                ]
            }

            overview_df = pd.DataFrame(overview_data)
            overview_df.to_excel(writer, sheet_name='数据概览', index=False)

            # 记录数据质量
            self.data_quality_report['overview'] = {
                'completeness': data_completeness,
                'kline_count': len(kline_data),
                'capital_flow_count': len(capital_flow),
                'concept_count': len(concept_info)
            }

        except Exception as e:
            logger.error(f"创建数据概览页失败: {e}")

    def _calculate_data_completeness(self, analysis_data: Dict) -> float:
        """计算数据完整性百分比"""
        try:
            total_items = 8  # 总共8个主要数据项
            available_items = 0

            # 检查各项数据是否可用
            if analysis_data.get('basic_info', {}):
                available_items += 1
            if not analysis_data.get('realtime_quote', pd.DataFrame()).empty:
                available_items += 1
            if not analysis_data.get('kline_data', pd.DataFrame()).empty:
                available_items += 1
            if not analysis_data.get('market_depth', pd.DataFrame()).empty:
                available_items += 1
            if analysis_data.get('lhb_data', {}):
                available_items += 1
            if not analysis_data.get('capital_flow', pd.DataFrame()).empty:
                available_items += 1
            if not analysis_data.get('concept_info', pd.DataFrame()).empty:
                available_items += 1
            if analysis_data.get('technical_indicators', {}):
                available_items += 1

            return (available_items / total_items) * 100
        except:
            return 0.0

    def _export_detailed_basic_info(self, writer, basic_info: Dict, stock_code: str):
        """导出详细基本信息"""
        try:
            if not basic_info:
                # 创建空数据提示
                empty_data = pd.DataFrame({
                    '提示': ['基本信息数据为空'],
                    '说明': ['无法获取股票基本信息，请检查股票代码是否正确']
                })
                empty_data.to_excel(writer, sheet_name='基本信息', index=False)
                return

            # 基础信息
            basic_data = {
                '基础信息项': [
                    '股票代码', '股票名称', '交易所', '行业分类', '上市日期',
                    '总市值(元)', '流通市值(元)', '总股本(股)', '流通股(股)',
                    '每股净资产', '净资产收益率', '市盈率', '市净率'
                ],
                '数值': [
                    basic_info.get('code', stock_code),
                    basic_info.get('name', '未知'),
                    basic_info.get('exchange', '未知'),
                    basic_info.get('industry', '未知'),
                    basic_info.get('list_date', '未知'),
                    f"{basic_info.get('total_market_value', 0):,.0f}",
                    f"{basic_info.get('circulation_market_value', 0):,.0f}",
                    f"{basic_info.get('total_shares', 0):,.0f}",
                    f"{basic_info.get('circulation_shares', 0):,.0f}",
                    f"{basic_info.get('bvps', 0):.2f}",
                    f"{basic_info.get('roe', 0):.2f}%",
                    f"{basic_info.get('pe', 0):.2f}",
                    f"{basic_info.get('pb', 0):.2f}"
                ],
                '说明': [
                    '6位数字股票代码',
                    '股票简称',
                    '上交所/深交所/北交所',
                    '证监会行业分类',
                    '首次公开发行日期',
                    '总股本×股价',
                    '流通股本×股价',
                    '公司发行的股票总数',
                    '可自由交易的股票数量',
                    '每股对应的净资产',
                    '净利润/净资产',
                    '股价/每股收益',
                    '股价/每股净资产'
                ]
            }

            basic_df = pd.DataFrame(basic_data)
            basic_df.to_excel(writer, sheet_name='基本信息', index=False)

            # 市值分析
            market_value = basic_info.get('circulation_market_value', 0)
            market_analysis = {
                '市值分析项': ['流通市值等级', '游资偏好度', '机构偏好度', '流动性评估'],
                '分析结果': [
                    self._get_market_value_level(market_value),
                    self._get_trader_preference(market_value),
                    self._get_institution_preference(market_value),
                    self._get_liquidity_assessment(market_value)
                ]
            }

            # 将基本信息和市值分析合并到一个DataFrame
            # 添加空行分隔
            empty_row = pd.DataFrame({'基础信息项': [''], '数值': [''], '说明': ['']})
            market_df_renamed = pd.DataFrame({
                '基础信息项': market_analysis['市值分析项'],
                '数值': market_analysis['分析结果'],
                '说明': ['市值等级分类', '游资操作偏好程度', '机构投资偏好程度', '股票流动性评估']
            })

            # 合并所有数据
            combined_df = pd.concat([basic_df, empty_row, market_df_renamed], ignore_index=True)
            combined_df.to_excel(writer, sheet_name='基本信息', index=False)

        except Exception as e:
            logger.error(f"导出详细基本信息失败: {e}")

    def _get_market_value_level(self, market_value: float) -> str:
        """获取市值等级"""
        if market_value >= 100000000000:  # 1000亿以上
            return "超大盘股"
        elif market_value >= 50000000000:  # 500-1000亿
            return "大盘股"
        elif market_value >= 20000000000:  # 200-500亿
            return "中大盘股"
        elif market_value >= 5000000000:   # 50-200亿
            return "中盘股"
        elif market_value >= 1000000000:   # 10-50亿
            return "小盘股"
        else:
            return "微盘股"

    def _get_trader_preference(self, market_value: float) -> str:
        """获取游资偏好度"""
        if 5000000000 <= market_value <= 20000000000:  # 50-200亿
            return "高偏好(黄金区间)"
        elif 2000000000 <= market_value <= 50000000000:  # 20-500亿
            return "中等偏好"
        else:
            return "低偏好"

    def _get_institution_preference(self, market_value: float) -> str:
        """获取机构偏好度"""
        if market_value >= 50000000000:  # 500亿以上
            return "高偏好"
        elif market_value >= 20000000000:  # 200-500亿
            return "中等偏好"
        else:
            return "低偏好"

    def _get_liquidity_assessment(self, market_value: float) -> str:
        """获取流动性评估"""
        if market_value >= 20000000000:  # 200亿以上
            return "流动性充足"
        elif market_value >= 5000000000:   # 50-200亿
            return "流动性良好"
        elif market_value >= 1000000000:   # 10-50亿
            return "流动性一般"
        else:
            return "流动性不足"



    def _export_detailed_realtime_quote(self, writer, realtime_quote: pd.DataFrame):
        """导出详细实时行情"""
        try:
            if realtime_quote.empty:
                empty_data = pd.DataFrame({
                    '提示': ['实时行情数据为空'],
                    '说明': ['无法获取实时行情数据，可能是非交易时间或网络问题']
                })
                empty_data.to_excel(writer, sheet_name='实时行情', index=False)
                return

            # 🔧 修复：验证和修复实时行情数据
            realtime_quote = self._validate_realtime_data(realtime_quote)

            # 原始实时行情数据（翻译列名）
            realtime_quote_cn = self._translate_dataframe_columns(realtime_quote)

            # 实时行情分析
            if not realtime_quote.empty:
                quote = realtime_quote.iloc[0]

                # 🔧 修复：兼容多种字段名格式
                def safe_get_float(data, keys, default=0):
                    """安全获取浮点数，支持多个可能的字段名"""
                    if isinstance(keys, str):
                        keys = [keys]
                    for key in keys:
                        value = data.get(key, default)
                        if value is not None and value != '':
                            try:
                                return float(value)
                            except (ValueError, TypeError):
                                continue
                    return default

                # 兼容不同的字段名
                current_price = safe_get_float(quote, ['current_price', 'price', '最新价'])
                change = safe_get_float(quote, ['change', '涨跌额'])
                change_pct = safe_get_float(quote, ['change_pct', '涨跌幅'])
                volume = safe_get_float(quote, ['volume', '成交量'])
                amount = safe_get_float(quote, ['amount', '成交额'])

                analysis_data = {
                    '行情分析项': [
                        '当前价格', '涨跌额', '涨跌幅', '成交量', '成交额',
                        '价格位置', '波动强度', '成交活跃度', '资金关注度'
                    ],
                    '数值': [
                        f"{current_price:.2f}",
                        f"{change:+.2f}",
                        f"{change_pct:+.2f}%",
                        f"{volume:,.0f}",
                        f"{amount:,.0f}",
                        self._get_price_position_simple(quote),
                        self._get_volatility_strength_simple(quote),
                        self._get_trading_activity_simple(quote),
                        self._get_capital_attention_simple(quote)
                    ],
                    '分析': [
                        '当前交易价格',
                        '相对昨收盘的价格变化',
                        '涨跌幅百分比',
                        '当日累计成交股数',
                        '当日累计成交金额（万元）',  # 🔧 修复：标注单位
                        '基于价格变化的位置评估',
                        '价格波动的强度评估',
                        '成交量的活跃程度',
                        '资金对该股的关注度'
                    ]
                }

                analysis_df = pd.DataFrame(analysis_data)

                # 在实时行情数据下方添加分析数据
                # 创建空行分隔
                empty_row = pd.DataFrame({col: [''] for col in realtime_quote_cn.columns})

                # 创建分析标题行
                title_row = pd.DataFrame()
                title_row[realtime_quote_cn.columns[0]] = ['=== 行情分析 ===']
                for col in realtime_quote_cn.columns[1:]:
                    title_row[col] = ['']

                # 创建分析数据行
                analysis_rows = []
                for _, row in analysis_df.iterrows():
                    analysis_row = pd.DataFrame()
                    analysis_row[realtime_quote_cn.columns[0]] = [f"{row['行情分析项']}: {row['数值']}"]
                    analysis_row[realtime_quote_cn.columns[1]] = [row['分析']]
                    for col in realtime_quote_cn.columns[2:]:
                        analysis_row[col] = ['']
                    analysis_rows.append(analysis_row)

                # 合并所有数据
                all_analysis = pd.concat(analysis_rows, ignore_index=True) if analysis_rows else pd.DataFrame()
                if not all_analysis.empty:
                    combined_realtime = pd.concat([realtime_quote_cn, empty_row, title_row, all_analysis], ignore_index=True)
                else:
                    combined_realtime = realtime_quote_cn

                combined_realtime.to_excel(writer, sheet_name='实时行情', index=False)

        except Exception as e:
            logger.error(f"导出详细实时行情失败: {e}")

    def _get_price_position_simple(self, quote: pd.Series) -> str:
        """获取价格位置(简化版)"""
        try:
            # 🔧 修复：兼容多种字段名
            change_pct = 0
            for key in ['change_pct', '涨跌幅']:
                if key in quote and quote[key] is not None:
                    try:
                        change_pct = float(quote[key])
                        break
                    except (ValueError, TypeError):
                        continue

            if change_pct >= 5:
                return "强势上涨"
            elif change_pct >= 2:
                return "上涨"
            elif change_pct >= -2:
                return "横盘整理"
            elif change_pct >= -5:
                return "下跌"
            else:
                return "大幅下跌"
        except:
            return "未知"

    def _get_volatility_strength_simple(self, quote: pd.Series) -> str:
        """获取波动强度(简化版)"""
        try:
            # 🔧 修复：兼容多种字段名
            change_pct = 0
            for key in ['change_pct', '涨跌幅']:
                if key in quote and quote[key] is not None:
                    try:
                        change_pct = abs(float(quote[key]))
                        break
                    except (ValueError, TypeError):
                        continue

            if change_pct >= 7:
                return "极强波动"
            elif change_pct >= 5:
                return "强波动"
            elif change_pct >= 3:
                return "中等波动"
            elif change_pct >= 1:
                return "弱波动"
            else:
                return "微弱波动"
        except:
            return "未知"

    def _get_trading_activity_simple(self, quote: pd.Series) -> str:
        """获取成交活跃度(简化版)"""
        try:
            # 🔧 修复：兼容多种字段名
            volume = 0
            for key in ['volume', '成交量']:
                if key in quote and quote[key] is not None:
                    try:
                        volume = float(quote[key])
                        break
                    except (ValueError, TypeError):
                        continue

            if volume >= 10000000:  # 1000万股以上
                return "极度活跃"
            elif volume >= 5000000:  # 500万股以上
                return "高度活跃"
            elif volume >= 1000000:  # 100万股以上
                return "中等活跃"
            elif volume >= 100000:   # 10万股以上
                return "低度活跃"
            else:
                return "不活跃"
        except:
            return "未知"

    def _get_capital_attention_simple(self, quote: pd.Series) -> str:
        """获取资金关注度(简化版)"""
        try:
            # 🔧 修复：兼容多种字段名，注意成交额可能是万元单位
            amount = 0
            for key in ['amount', '成交额']:
                if key in quote and quote[key] is not None:
                    try:
                        amount = float(quote[key])
                        break
                    except (ValueError, TypeError):
                        continue

            # 如果成交额是万元单位，需要转换为元
            if amount < 1000000 and amount > 0:  # 可能是万元单位
                amount = amount * 10000

            if amount >= 1000000000:  # 10亿以上
                return "极高关注"
            elif amount >= 500000000:  # 5亿以上
                return "高度关注"
            elif amount >= 100000000:  # 1亿以上
                return "中等关注"
            else:
                return "低度关注"
        except:
            return "未知"

    def _get_price_position(self, quote: pd.Series) -> str:
        """获取价格位置"""
        try:
            current = quote.get('current', 0)
            high = quote.get('high', 0)
            low = quote.get('low', 0)

            if high == low:
                return "无波动"

            position = (current - low) / (high - low)
            if position >= 0.8:
                return "高位(80%以上)"
            elif position >= 0.6:
                return "中高位(60-80%)"
            elif position >= 0.4:
                return "中位(40-60%)"
            elif position >= 0.2:
                return "中低位(20-40%)"
            else:
                return "低位(20%以下)"
        except:
            return "未知"

    def _get_volatility_strength(self, quote: pd.Series) -> str:
        """获取波动强度"""
        try:
            amplitude = abs(quote.get('pct_change', 0))
            if amplitude >= 7:
                return "极强波动"
            elif amplitude >= 5:
                return "强波动"
            elif amplitude >= 3:
                return "中等波动"
            elif amplitude >= 1:
                return "弱波动"
            else:
                return "微弱波动"
        except:
            return "未知"

    def _get_trading_activity(self, quote: pd.Series) -> str:
        """获取成交活跃度"""
        try:
            turnover = quote.get('turnover_rate', 0)
            if turnover >= 10:
                return "极度活跃"
            elif turnover >= 5:
                return "高度活跃"
            elif turnover >= 2:
                return "中等活跃"
            elif turnover >= 1:
                return "低度活跃"
            else:
                return "不活跃"
        except:
            return "未知"

    def _get_capital_attention(self, quote: pd.Series) -> str:
        """获取资金关注度"""
        try:
            amount = quote.get('amount', 0)
            turnover = quote.get('turnover_rate', 0)

            if amount >= 1000000000 and turnover >= 5:  # 10亿以上且换手率5%以上
                return "极高关注"
            elif amount >= 500000000 and turnover >= 3:  # 5亿以上且换手率3%以上
                return "高度关注"
            elif amount >= 100000000 and turnover >= 1:  # 1亿以上且换手率1%以上
                return "中等关注"
            else:
                return "低度关注"
        except:
            return "未知"

    def _export_detailed_kline_data(self, writer, kline_data: pd.DataFrame, stock_code: str):
        """导出详细K线数据及技术分析"""
        try:
            if kline_data.empty:
                empty_data = pd.DataFrame({
                    '提示': ['K线数据为空'],
                    '说明': ['无法获取K线数据，请检查股票代码或网络连接']
                })
                empty_data.to_excel(writer, sheet_name='K线数据', index=False)
                return

            # 原始K线数据（翻译列名）
            kline_data_cn = self._translate_dataframe_columns(kline_data)
            kline_data_cn.to_excel(writer, sheet_name='K线数据', index=False)

            # K线技术分析
            tech_analysis = self._calculate_technical_analysis(kline_data)
            tech_df = pd.DataFrame(tech_analysis)
            tech_df.to_excel(writer, sheet_name='技术分析', index=False)

            # 价格统计分析
            price_stats = self._calculate_price_statistics(kline_data)
            stats_df = pd.DataFrame(price_stats)
            stats_df.to_excel(writer, sheet_name='价格统计', index=False)

        except Exception as e:
            logger.error(f"导出详细K线数据失败: {e}")

    def _calculate_technical_analysis(self, kline_data: pd.DataFrame) -> Dict:
        """计算技术分析指标"""
        try:
            if len(kline_data) < 5:
                return {
                    '技术指标': ['数据不足'],
                    '数值': ['K线数据少于5条'],
                    '说明': ['无法进行技术分析']
                }

            # 计算移动平均线
            kline_data = kline_data.copy()
            kline_data['ma5'] = kline_data['close'].rolling(window=5).mean()
            kline_data['ma10'] = kline_data['close'].rolling(window=10).mean()
            kline_data['ma20'] = kline_data['close'].rolling(window=20).mean()

            # 计算技术指标
            latest = kline_data.iloc[-1]
            current_price = latest['close']

            # 均线分析
            ma5 = latest.get('ma5', 0)
            ma10 = latest.get('ma10', 0)
            ma20 = latest.get('ma20', 0)

            # 趋势分析
            trend_analysis = self._analyze_trend(kline_data)

            # 支撑阻力分析
            support_resistance = self._analyze_support_resistance(kline_data)

            return {
                '技术指标': [
                    '当前价格', '5日均线', '10日均线', '20日均线',
                    '价格相对MA5', '价格相对MA10', '价格相对MA20',
                    '短期趋势', '中期趋势', '长期趋势',
                    '近期最高价', '近期最低价', '价格振幅',
                    '支撑位1', '支撑位2', '阻力位1', '阻力位2',
                    '连续上涨天数', '连续下跌天数', '创新高状态'
                ],
                '数值': [
                    f"{current_price:.2f}",
                    f"{ma5:.2f}" if not pd.isna(ma5) else "计算中",
                    f"{ma10:.2f}" if not pd.isna(ma10) else "计算中",
                    f"{ma20:.2f}" if not pd.isna(ma20) else "计算中",
                    f"{((current_price/ma5-1)*100):+.2f}%" if not pd.isna(ma5) else "计算中",
                    f"{((current_price/ma10-1)*100):+.2f}%" if not pd.isna(ma10) else "计算中",
                    f"{((current_price/ma20-1)*100):+.2f}%" if not pd.isna(ma20) else "计算中",
                    trend_analysis['short_trend'],
                    trend_analysis['medium_trend'],
                    trend_analysis['long_trend'],
                    f"{kline_data['high'].max():.2f}",
                    f"{kline_data['low'].min():.2f}",
                    f"{((kline_data['high'].max()/kline_data['low'].min()-1)*100):.2f}%",
                    f"{support_resistance['support1']:.2f}",
                    f"{support_resistance['support2']:.2f}",
                    f"{support_resistance['resistance1']:.2f}",
                    f"{support_resistance['resistance2']:.2f}",
                    str(trend_analysis['up_days']),
                    str(trend_analysis['down_days']),
                    trend_analysis['new_high_status']
                ],
                '说明': [
                    '最新收盘价',
                    '5日收盘价平均值',
                    '10日收盘价平均值',
                    '20日收盘价平均值',
                    '当前价格偏离5日均线程度',
                    '当前价格偏离10日均线程度',
                    '当前价格偏离20日均线程度',
                    '近5日价格趋势方向',
                    '近10日价格趋势方向',
                    '近20日价格趋势方向',
                    '统计期间最高价格',
                    '统计期间最低价格',
                    '最高价相对最低价的涨幅',
                    '第一支撑位价格',
                    '第二支撑位价格',
                    '第一阻力位价格',
                    '第二阻力位价格',
                    '最近连续上涨交易日数',
                    '最近连续下跌交易日数',
                    '是否创近期新高'
                ]
            }

        except Exception as e:
            logger.error(f"计算技术分析失败: {e}")
            return {
                '技术指标': ['计算失败'],
                '数值': [str(e)],
                '说明': ['技术分析计算出现错误']
            }

    def _analyze_trend(self, kline_data: pd.DataFrame) -> Dict:
        """分析价格趋势"""
        try:
            # 短期趋势(5日)
            recent_5 = kline_data.tail(5)
            short_trend = "横盘"
            if len(recent_5) >= 2:
                if recent_5.iloc[-1]['close'] > recent_5.iloc[0]['close'] * 1.02:
                    short_trend = "上涨"
                elif recent_5.iloc[-1]['close'] < recent_5.iloc[0]['close'] * 0.98:
                    short_trend = "下跌"

            # 中期趋势(10日)
            recent_10 = kline_data.tail(10)
            medium_trend = "横盘"
            if len(recent_10) >= 2:
                if recent_10.iloc[-1]['close'] > recent_10.iloc[0]['close'] * 1.05:
                    medium_trend = "上涨"
                elif recent_10.iloc[-1]['close'] < recent_10.iloc[0]['close'] * 0.95:
                    medium_trend = "下跌"

            # 长期趋势(20日)
            recent_20 = kline_data.tail(20)
            long_trend = "横盘"
            if len(recent_20) >= 2:
                if recent_20.iloc[-1]['close'] > recent_20.iloc[0]['close'] * 1.1:
                    long_trend = "上涨"
                elif recent_20.iloc[-1]['close'] < recent_20.iloc[0]['close'] * 0.9:
                    long_trend = "下跌"

            # 连续上涨/下跌天数
            up_days = 0
            down_days = 0
            for i in range(len(kline_data)-1, 0, -1):
                if kline_data.iloc[i]['close'] > kline_data.iloc[i-1]['close']:
                    if down_days == 0:
                        up_days += 1
                    else:
                        break
                elif kline_data.iloc[i]['close'] < kline_data.iloc[i-1]['close']:
                    if up_days == 0:
                        down_days += 1
                    else:
                        break
                else:
                    break

            # 创新高状态
            current_price = kline_data.iloc[-1]['close']
            max_price = kline_data['high'].max()
            new_high_status = "是" if current_price >= max_price * 0.99 else "否"

            return {
                'short_trend': short_trend,
                'medium_trend': medium_trend,
                'long_trend': long_trend,
                'up_days': up_days,
                'down_days': down_days,
                'new_high_status': new_high_status
            }
        except:
            return {
                'short_trend': '未知',
                'medium_trend': '未知',
                'long_trend': '未知',
                'up_days': 0,
                'down_days': 0,
                'new_high_status': '未知'
            }

    def _analyze_support_resistance(self, kline_data: pd.DataFrame) -> Dict:
        """分析支撑阻力位"""
        try:
            # 简单的支撑阻力计算
            highs = kline_data['high'].values
            lows = kline_data['low'].values
            closes = kline_data['close'].values

            # 支撑位：近期低点
            recent_lows = sorted(lows[-10:])  # 最近10个交易日的低点
            support1 = recent_lows[0] if len(recent_lows) > 0 else 0
            support2 = recent_lows[1] if len(recent_lows) > 1 else support1

            # 阻力位：近期高点
            recent_highs = sorted(highs[-10:], reverse=True)  # 最近10个交易日的高点
            resistance1 = recent_highs[0] if len(recent_highs) > 0 else 0
            resistance2 = recent_highs[1] if len(recent_highs) > 1 else resistance1

            return {
                'support1': support1,
                'support2': support2,
                'resistance1': resistance1,
                'resistance2': resistance2
            }
        except:
            return {
                'support1': 0,
                'support2': 0,
                'resistance1': 0,
                'resistance2': 0
            }

    def _calculate_price_statistics(self, kline_data: pd.DataFrame) -> Dict:
        """计算价格统计信息"""
        try:
            if kline_data.empty:
                return {'统计项': ['无数据'], '数值': [''], '说明': ['']}

            # 基础统计
            closes = kline_data['close']
            volumes = kline_data['volume']

            return {
                '统计项': [
                    '数据天数', '平均收盘价', '价格标准差', '价格变异系数',
                    '最高收盘价', '最低收盘价', '价格区间',
                    '平均成交量', '最大成交量', '最小成交量',
                    '总成交量', '平均换手率', '价格上涨天数', '价格下跌天数'
                ],
                '数值': [
                    str(len(kline_data)),
                    f"{closes.mean():.2f}",
                    f"{closes.std():.2f}",
                    f"{closes.std()/closes.mean()*100:.2f}%",
                    f"{closes.max():.2f}",
                    f"{closes.min():.2f}",
                    f"{((closes.max()/closes.min()-1)*100):.2f}%",
                    f"{volumes.mean():,.0f}",
                    f"{volumes.max():,.0f}",
                    f"{volumes.min():,.0f}",
                    f"{volumes.sum():,.0f}",
                    f"{kline_data.get('turnover_rate', pd.Series([0])).mean():.2f}%",
                    str(len(kline_data[kline_data['close'] > kline_data['close'].shift(1)])),
                    str(len(kline_data[kline_data['close'] < kline_data['close'].shift(1)]))
                ],
                '说明': [
                    'K线数据的总天数',
                    '统计期间收盘价平均值',
                    '收盘价的标准差',
                    '价格波动的相对程度',
                    '统计期间最高收盘价',
                    '统计期间最低收盘价',
                    '最高价相对最低价涨幅',
                    '统计期间平均成交量',
                    '统计期间最大成交量',
                    '统计期间最小成交量',
                    '统计期间累计成交量',
                    '统计期间平均换手率',
                    '收盘价上涨的天数',
                    '收盘价下跌的天数'
                ]
            }
        except Exception as e:
            logger.error(f"计算价格统计失败: {e}")
            return {'统计项': ['计算失败'], '数值': [str(e)], '说明': ['统计计算出现错误']}

    def _export_detailed_market_depth(self, writer, market_depth: pd.DataFrame):
        """导出详细五档行情"""
        try:
            if market_depth.empty:
                empty_data = pd.DataFrame({
                    '提示': ['五档行情数据为空'],
                    '说明': ['无法获取五档行情数据，可能是非交易时间']
                })
                empty_data.to_excel(writer, sheet_name='五档行情', index=False)
            else:
                market_depth_cn = self._translate_dataframe_columns(market_depth)
                market_depth_cn.to_excel(writer, sheet_name='五档行情', index=False)
        except Exception as e:
            logger.error(f"导出详细五档行情失败: {e}")

    def _export_detailed_lhb_data(self, writer, lhb_data: Dict, stock_code: str):
        """导出详细龙虎榜数据 - 增强版，包含6个月历史数据"""
        try:
            # 1. 导出今日龙虎榜
            today_lhb = lhb_data.get('today_lhb', pd.DataFrame())
            if not today_lhb.empty:
                today_lhb_cn = self._translate_dataframe_columns(today_lhb)
                today_lhb_cn.to_excel(writer, sheet_name='龙虎榜-当日', index=False)
            else:
                empty_data = pd.DataFrame({
                    '提示': ['今日无龙虎榜数据'],
                    '说明': ['今日该股票未上龙虎榜'],
                    '备注': ['请查看历史龙虎榜数据了解该股票上榜情况']
                })
                empty_data.to_excel(writer, sheet_name='龙虎榜-当日', index=False)

            # 2. 导出6个月历史龙虎榜数据
            history_lhb = lhb_data.get('history_lhb', pd.DataFrame())
            if not history_lhb.empty:
                # 准备历史数据导出
                history_export = history_lhb.copy()

                # 选择关键列进行导出
                export_columns = ['上榜日', '收盘价', '涨跌幅', '龙虎榜净买额', '龙虎榜买入额',
                                '龙虎榜卖出额', '龙虎榜成交额', '市场总成交额', '净买额占总成交比',
                                '成交额占总成交比', '换手率', '上榜原因']

                available_columns = [col for col in export_columns if col in history_export.columns]
                history_export_final = history_export[available_columns]

                # 格式化数值列
                if '涨跌幅' in history_export_final.columns:
                    history_export_final['涨跌幅'] = history_export_final['涨跌幅'].round(2)
                if '龙虎榜净买额' in history_export_final.columns:
                    history_export_final['龙虎榜净买额(万元)'] = (history_export_final['龙虎榜净买额'] / 10000).round(2)
                    history_export_final = history_export_final.drop('龙虎榜净买额', axis=1)

                history_export_final.to_excel(writer, sheet_name='龙虎榜-6个月历史', index=False)
            else:
                empty_history = pd.DataFrame({
                    '提示': ['6个月内无龙虎榜历史数据'],
                    '说明': ['该股票在最近6个月内未上过龙虎榜'],
                    '建议': ['可能是大盘股或交易不够活跃的股票']
                })
                empty_history.to_excel(writer, sheet_name='龙虎榜-6个月历史', index=False)

            # 3. 导出龙虎榜综合分析
            lhb_analysis_data = lhb_data.get('lhb_analysis', {})

            comprehensive_analysis = {
                '分析维度': [
                    '当日上榜状态',
                    '上榜原因',
                    '6个月历史记录数',
                    '数据可用性',
                    '最近活跃性',
                    '上榜频率',
                    '资金流向趋势',
                    '最近趋势',
                    '综合分析摘要'
                ],
                '分析结果': [
                    '是' if lhb_data.get('stock_in_lhb', False) else '否',
                    lhb_data.get('stock_lhb_reason', '未上榜'),
                    lhb_analysis_data.get('total_records', 0),
                    '有数据' if lhb_analysis_data.get('data_available', False) else '无数据',
                    '是' if lhb_analysis_data.get('recent_activity', False) else '否',
                    lhb_analysis_data.get('frequency', '无'),
                    lhb_analysis_data.get('net_buy_trend', '无'),
                    lhb_analysis_data.get('recent_trend', '无'),
                    lhb_analysis_data.get('analysis_summary', '无可用分析')
                ]
            }

            # 添加主要上榜原因
            main_reasons = lhb_analysis_data.get('main_reasons', [])
            if main_reasons:
                for i, reason in enumerate(main_reasons[:3], 1):
                    comprehensive_analysis['分析维度'].append(f'主要上榜原因{i}')
                    comprehensive_analysis['分析结果'].append(reason)

            comprehensive_df = pd.DataFrame(comprehensive_analysis)
            comprehensive_df.to_excel(writer, sheet_name='龙虎榜-综合分析', index=False)

            # 4. 游资分析建议已删除（根据用户要求去除分析结论）

        except Exception as e:
            logger.error(f"导出详细龙虎榜数据失败: {e}")
            # 导出错误信息
            error_data = pd.DataFrame({
                '错误': ['龙虎榜数据导出失败'],
                '原因': [str(e)],
                '建议': ['请检查网络连接或稍后重试']
            })
            error_data.to_excel(writer, sheet_name='龙虎榜-错误信息', index=False)

    def _generate_lhb_suggestions(self, history_lhb: pd.DataFrame, analysis_data: Dict) -> Dict:
        """基于龙虎榜历史数据生成游资分析建议"""
        try:
            suggestions = {
                '分析项目': [],
                '分析结果': [],
                '投资建议': [],
                '风险提示': []
            }

            total_records = len(history_lhb)
            frequency = analysis_data.get('frequency', '无')
            net_buy_trend = analysis_data.get('net_buy_trend', '无')
            recent_trend = analysis_data.get('recent_trend', '无')

            # 1. 上榜频率分析
            suggestions['分析项目'].append('上榜频率分析')
            if frequency == '高频':
                suggestions['分析结果'].append(f'6个月内上榜{total_records}次，属于高频上榜股票')
                suggestions['投资建议'].append('高度关注，可能存在游资炒作机会')
                suggestions['风险提示'].append('高频上榜风险较高，注意及时止盈止损')
            elif frequency == '中频':
                suggestions['分析结果'].append(f'6个月内上榜{total_records}次，属于中频上榜股票')
                suggestions['投资建议'].append('适度关注，可考虑波段操作')
                suggestions['风险提示'].append('注意市场情绪变化，控制仓位')
            else:
                suggestions['分析结果'].append(f'6个月内上榜{total_records}次，上榜频率较低')
                suggestions['投资建议'].append('谨慎关注，可能不是游资重点关注标的')
                suggestions['风险提示'].append('流动性可能不足，注意交易风险')

            # 2. 资金流向分析
            suggestions['分析项目'].append('资金流向分析')
            if net_buy_trend == '净买入为主':
                suggestions['分析结果'].append('历史上榜以净买入为主，资金流入明显')
                suggestions['投资建议'].append('积极信号，可考虑跟随主力资金')
                suggestions['风险提示'].append('注意主力资金是否有出货迹象')
            elif net_buy_trend == '净卖出为主':
                suggestions['分析结果'].append('历史上榜以净卖出为主，资金流出明显')
                suggestions['投资建议'].append('谨慎信号，建议观望或减仓')
                suggestions['风险提示'].append('可能存在主力出货，高度警惕')
            else:
                suggestions['分析结果'].append('买卖资金相对均衡')
                suggestions['投资建议'].append('中性信号，需结合其他指标判断')
                suggestions['风险提示'].append('方向不明确，控制风险')

            # 3. 时间趋势分析
            suggestions['分析项目'].append('时间趋势分析')
            if recent_trend == '密集上榜':
                suggestions['分析结果'].append('最近密集上榜，市场关注度高')
                suggestions['投资建议'].append('高度关注，可能处于炒作周期')
                suggestions['风险提示'].append('密集炒作后可能面临回调风险')
            elif recent_trend == '活跃期':
                suggestions['分析结果'].append('最近处于活跃期，有一定关注度')
                suggestions['投资建议'].append('适度关注，寻找合适介入时机')
                suggestions['风险提示'].append('注意炒作周期的阶段性风险')
            else:
                suggestions['分析结果'].append('上榜时间相对分散')
                suggestions['投资建议'].append('可能不在炒作周期内，谨慎操作')
                suggestions['风险提示'].append('缺乏持续性炒作动力')

            # 4. 综合建议
            suggestions['分析项目'].append('综合投资建议')

            # 计算综合评分
            score = 0
            if frequency == '高频':
                score += 3
            elif frequency == '中频':
                score += 2
            else:
                score += 1

            if net_buy_trend == '净买入为主':
                score += 3
            elif net_buy_trend == '买卖均衡':
                score += 2
            else:
                score += 1

            if recent_trend == '密集上榜':
                score += 3
            elif recent_trend == '活跃期':
                score += 2
            else:
                score += 1

            if score >= 8:
                suggestions['分析结果'].append('综合评分：高(8-9分) - 游资关注度高')
                suggestions['投资建议'].append('积极关注，可考虑适量参与')
                suggestions['风险提示'].append('高收益伴随高风险，严格止损')
            elif score >= 6:
                suggestions['分析结果'].append('综合评分：中(6-7分) - 有一定游资关注')
                suggestions['投资建议'].append('谨慎关注，小仓位试探')
                suggestions['风险提示'].append('控制仓位，注意风险管理')
            else:
                suggestions['分析结果'].append('综合评分：低(3-5分) - 游资关注度低')
                suggestions['投资建议'].append('建议观望，寻找更好标的')
                suggestions['风险提示'].append('投资价值有限，避免盲目跟风')

            return suggestions

        except Exception as e:
            logger.error(f"生成龙虎榜建议失败: {e}")
            return {
                '分析项目': ['分析失败'],
                '分析结果': [f'生成建议时发生错误: {str(e)}'],
                '投资建议': ['请手动分析龙虎榜数据'],
                '风险提示': ['系统分析不可用，请谨慎投资']
            }

    def _export_detailed_capital_flow(self, writer, capital_flow_data):
        """导出详细资金流向 - 增强版，支持新的数据结构"""
        try:
            # 处理新的数据结构（Dict格式）
            if isinstance(capital_flow_data, dict):
                self._export_enhanced_capital_flow(writer, capital_flow_data)
            # 兼容旧的数据结构（DataFrame格式）
            elif isinstance(capital_flow_data, pd.DataFrame):
                self._export_legacy_capital_flow(writer, capital_flow_data)
            else:
                # 数据格式不支持
                empty_data = pd.DataFrame({
                    '提示': ['资金流向数据格式不支持'],
                    '说明': ['数据类型错误，请检查数据源']
                })
                empty_data.to_excel(writer, sheet_name='资金流向-错误', index=False)

        except Exception as e:
            logger.error(f"导出详细资金流向失败: {e}")
            # 导出错误信息
            error_data = pd.DataFrame({
                '错误': ['资金流向数据导出失败'],
                '原因': [str(e)],
                '建议': ['请检查网络连接或稍后重试']
            })
            error_data.to_excel(writer, sheet_name='资金流向-错误信息', index=False)

    def _export_enhanced_capital_flow(self, writer, capital_flow_data: Dict):
        """导出增强版资金流向数据"""
        try:
            # 1. 导出历史资金流向数据
            historical_flow = capital_flow_data.get('historical_flow', pd.DataFrame())

            if not historical_flow.empty:
                # 准备历史数据导出
                flow_export = historical_flow.copy()

                # 选择关键列进行导出
                export_columns = ['日期', '收盘价', '涨跌幅', '主力净流入-净额', '主力净流入-净占比',
                                '超大单净流入-净额', '超大单净流入-净占比', '大单净流入-净额', '大单净流入-净占比',
                                '中单净流入-净额', '中单净流入-净占比', '小单净流入-净额', '小单净流入-净占比']

                available_columns = [col for col in export_columns if col in flow_export.columns]
                flow_export_final = flow_export[available_columns]

                # 格式化数值列（转换为万元）
                money_columns = ['主力净流入-净额', '超大单净流入-净额', '大单净流入-净额', '中单净流入-净额', '小单净流入-净额']
                for col in money_columns:
                    if col in flow_export_final.columns:
                        new_col_name = col.replace('-净额', '-净额(万元)')
                        flow_export_final[new_col_name] = (flow_export_final[col] / 10000).round(2)
                        flow_export_final = flow_export_final.drop(col, axis=1)

                # 格式化百分比列
                percent_columns = [col for col in flow_export_final.columns if '净占比' in col]
                for col in percent_columns:
                    flow_export_final[col] = flow_export_final[col].round(2)

                # 按照用户要求，只保留最近30天的数据
                if '日期' in flow_export_final.columns and len(flow_export_final) > 30:
                    # 确保日期列是datetime类型
                    flow_export_final['日期'] = pd.to_datetime(flow_export_final['日期'], errors='coerce')
                    # 按日期排序，最新的在前
                    flow_export_final = flow_export_final.sort_values('日期', ascending=False)
                    # 只保留最近30条记录
                    flow_export_final = flow_export_final.head(30)
                    # 重新按日期排序，最新的在下面（符合用户习惯）
                    flow_export_final = flow_export_final.sort_values('日期', ascending=True)

                flow_export_final.to_excel(writer, sheet_name='资金流向-历史数据', index=False)
            else:
                empty_history = pd.DataFrame({
                    '提示': ['无历史资金流向数据'],
                    '说明': ['无法获取该股票的资金流向历史数据'],
                    '建议': ['可能是数据源问题或股票代码错误']
                })
                empty_history.to_excel(writer, sheet_name='资金流向-历史数据', index=False)

            # 2. 导出最新资金流向分析
            latest_analysis = capital_flow_data.get('latest_analysis', {})

            if latest_analysis:
                analysis_data = {
                    '分析维度': [
                        '数据可用性',
                        '最新日期',
                        '主力净流入(万元)',
                        '超大单净流入(万元)',
                        '大单净流入(万元)',
                        '中单净流入(万元)',
                        '小单净流入(万元)',
                        '趋势分析',
                        '数据质量',
                        '综合评估'
                    ],
                    '分析结果': [
                        '有数据' if capital_flow_data.get('data_available', False) else '无数据',
                        latest_analysis.get('latest_date', '未知'),
                        f"{latest_analysis.get('main_flow_wan', 0):.2f}",
                        f"{latest_analysis.get('super_large_net_inflow', 0)/10000:.2f}",
                        f"{latest_analysis.get('large_net_inflow', 0)/10000:.2f}",
                        f"{latest_analysis.get('medium_net_inflow', 0)/10000:.2f}",
                        f"{latest_analysis.get('small_net_inflow', 0)/10000:.2f}",
                        latest_analysis.get('trend_analysis', '未知'),
                        latest_analysis.get('data_quality', '未知'),
                        capital_flow_data.get('flow_summary', '无可用分析')
                    ]
                }

                analysis_df = pd.DataFrame(analysis_data)
                analysis_df.to_excel(writer, sheet_name='资金流向-最新分析', index=False)

            # 3. 资金流向投资建议已删除（根据用户要求去除分析结论）

        except Exception as e:
            logger.error(f"导出增强版资金流向数据失败: {e}")
            error_data = pd.DataFrame({
                '错误': ['增强版资金流向导出失败'],
                '原因': [str(e)],
                '建议': ['请检查数据格式或联系技术支持']
            })
            error_data.to_excel(writer, sheet_name='资金流向-导出错误', index=False)

    def _export_legacy_capital_flow(self, writer, capital_flow: pd.DataFrame):
        """导出传统格式资金流向数据（兼容性）"""
        try:
            if capital_flow.empty:
                empty_data = pd.DataFrame({
                    '提示': ['资金流向数据为空'],
                    '说明': ['无法获取资金流向数据']
                })
                empty_data.to_excel(writer, sheet_name='资金流向', index=False)
            else:
                # 创建副本以避免修改原数据
                capital_flow_copy = capital_flow.copy()

                # 将资金流向数据从"元"转换为"万元"
                money_columns = ['main_net_inflow', 'sm_net_inflow', 'mid_net_inflow',
                               'lg_net_inflow', 'max_net_inflow', 'super_large_net_inflow',
                               'large_net_inflow', 'medium_net_inflow', 'small_net_inflow']

                for col in money_columns:
                    if col in capital_flow_copy.columns:
                        # 将元转换为万元
                        capital_flow_copy[col] = capital_flow_copy[col] / 10000

                # 翻译列名
                capital_flow_cn = self._translate_dataframe_columns(capital_flow_copy)
                capital_flow_cn.to_excel(writer, sheet_name='资金流向', index=False)

        except Exception as e:
            logger.error(f"导出传统资金流向数据失败: {e}")

    def _generate_capital_flow_suggestions(self, analysis_data: Dict, historical_flow: pd.DataFrame) -> Dict:
        """基于资金流向数据生成投资建议"""
        try:
            suggestions = {
                '分析项目': [],
                '分析结果': [],
                '投资建议': [],
                '风险提示': []
            }

            main_flow_wan = analysis_data.get('main_flow_wan', 0)
            trend_analysis = analysis_data.get('trend_analysis', '无')
            data_quality = analysis_data.get('data_quality', 'poor')

            # 1. 主力资金流向分析
            suggestions['分析项目'].append('主力资金流向分析')
            if main_flow_wan > 5000:
                suggestions['分析结果'].append(f'主力大额净流入{main_flow_wan:.0f}万元，资金关注度高')
                suggestions['投资建议'].append('积极信号，可考虑适量跟进')
                suggestions['风险提示'].append('大额流入后需防范获利回吐')
            elif main_flow_wan > 1000:
                suggestions['分析结果'].append(f'主力中等净流入{main_flow_wan:.0f}万元，有一定关注')
                suggestions['投资建议'].append('谨慎乐观，可小仓位关注')
                suggestions['风险提示'].append('注意资金流向的持续性')
            elif main_flow_wan > -1000:
                suggestions['分析结果'].append(f'主力资金小幅波动{main_flow_wan:.0f}万元')
                suggestions['投资建议'].append('中性信号，观望为主')
                suggestions['风险提示'].append('缺乏明确方向，控制仓位')
            elif main_flow_wan > -5000:
                suggestions['分析结果'].append(f'主力中等净流出{abs(main_flow_wan):.0f}万元，存在抛压')
                suggestions['投资建议'].append('谨慎信号，建议减仓观望')
                suggestions['风险提示'].append('主力资金流出，注意下跌风险')
            else:
                suggestions['分析结果'].append(f'主力大额净流出{abs(main_flow_wan):.0f}万元，抛压严重')
                suggestions['投资建议'].append('避险信号，建议回避或止损')
                suggestions['风险提示'].append('大额资金流出，高度警惕下跌风险')

            # 2. 资金流向趋势分析
            suggestions['分析项目'].append('资金流向趋势分析')
            if trend_analysis == '强势流入':
                suggestions['分析结果'].append('近期资金持续强势流入，趋势明确')
                suggestions['投资建议'].append('趋势向好，可考虑逢低布局')
                suggestions['风险提示'].append('强势流入后注意高位风险')
            elif trend_analysis == '偏向流入':
                suggestions['分析结果'].append('近期资金偏向流入，有一定支撑')
                suggestions['投资建议'].append('适度关注，等待更明确信号')
                suggestions['风险提示'].append('流入趋势需要确认，控制风险')
            elif trend_analysis == '震荡整理':
                suggestions['分析结果'].append('资金流向震荡，方向不明确')
                suggestions['投资建议'].append('观望为主，等待方向选择')
                suggestions['风险提示'].append('方向不明，避免盲目操作')
            elif trend_analysis == '持续流出':
                suggestions['分析结果'].append('近期资金持续流出，趋势偏弱')
                suggestions['投资建议'].append('谨慎操作，建议减仓')
                suggestions['风险提示'].append('持续流出趋势，注意下跌风险')
            else:
                suggestions['分析结果'].append('资金流向趋势不明确')
                suggestions['投资建议'].append('缺乏明确信号，谨慎操作')
                suggestions['风险提示'].append('趋势不明，控制仓位风险')

            # 3. 资金结构分析
            suggestions['分析项目'].append('资金结构分析')
            super_large_flow = analysis_data.get('super_large_net_inflow', 0) / 10000
            large_flow = analysis_data.get('large_net_inflow', 0) / 10000

            if super_large_flow > 0 and large_flow > 0:
                suggestions['分析结果'].append('超大单和大单均为净流入，机构资金积极')
                suggestions['投资建议'].append('机构看好，可考虑跟随')
                suggestions['风险提示'].append('机构资金变化快，注意及时止盈')
            elif super_large_flow > 0 and large_flow < 0:
                suggestions['分析结果'].append('超大单流入但大单流出，资金分歧明显')
                suggestions['投资建议'].append('存在分歧，谨慎操作')
                suggestions['风险提示'].append('资金分歧可能导致波动加大')
            elif super_large_flow < 0 and large_flow < 0:
                suggestions['分析结果'].append('超大单和大单均为净流出，机构资金谨慎')
                suggestions['投资建议'].append('机构资金流出，建议观望')
                suggestions['风险提示'].append('机构资金流出，警惕下跌风险')
            else:
                suggestions['分析结果'].append('资金结构复杂，需要综合判断')
                suggestions['投资建议'].append('结构复杂，建议专业分析')
                suggestions['风险提示'].append('复杂结构增加判断难度')

            # 4. 数据质量评估
            suggestions['分析项目'].append('数据质量评估')
            if data_quality == 'good':
                suggestions['分析结果'].append('数据质量良好，分析结果可信度高')
                suggestions['投资建议'].append('可基于分析结果制定投资策略')
                suggestions['风险提示'].append('虽然数据质量好，仍需结合其他指标')
            elif data_quality == 'fair':
                suggestions['分析结果'].append('数据质量一般，分析结果仅供参考')
                suggestions['投资建议'].append('谨慎参考分析结果，多方验证')
                suggestions['风险提示'].append('数据质量限制，增加判断风险')
            else:
                suggestions['分析结果'].append('数据质量较差，分析结果可信度低')
                suggestions['投资建议'].append('不建议依据当前分析做决策')
                suggestions['风险提示'].append('数据质量差，避免盲目决策')

            return suggestions

        except Exception as e:
            logger.error(f"生成资金流向建议失败: {e}")
            return {
                '分析项目': ['分析失败'],
                '分析结果': [f'生成建议时发生错误: {str(e)}'],
                '投资建议': ['请手动分析资金流向数据'],
                '风险提示': ['系统分析不可用，请谨慎投资']
            }

    def _export_detailed_concept_info(self, writer, concept_info: pd.DataFrame):
        """导出详细概念信息"""
        try:
            if concept_info.empty:
                empty_data = pd.DataFrame({
                    '提示': ['概念板块数据为空'],
                    '说明': ['无法获取概念板块数据']
                })
                empty_data.to_excel(writer, sheet_name='概念板块', index=False)
            else:
                concept_info_cn = self._translate_dataframe_columns(concept_info)
                concept_info_cn.to_excel(writer, sheet_name='概念板块', index=False)
        except Exception as e:
            logger.error(f"导出详细概念信息失败: {e}")

    def _export_detailed_technical_indicators(self, writer, tech_indicators: Dict):
        """导出详细技术指标"""
        try:
            if not tech_indicators:
                empty_data = pd.DataFrame({
                    '提示': ['技术指标数据为空'],
                    '说明': ['无法计算技术指标']
                })
                empty_data.to_excel(writer, sheet_name='技术指标详情', index=False)
                return

            tech_data = {
                '技术指标': ['是否创月新高', '是否连续上涨', '连涨天数'],
                '结果': [
                    '是' if tech_indicators.get('is_new_high', False) else '否',
                    '是' if tech_indicators.get('is_continuous_up', False) else '否',
                    f"{tech_indicators.get('continuous_days', 0)}天"
                ]
            }

            tech_df = pd.DataFrame(tech_data)
            tech_df.to_excel(writer, sheet_name='技术指标详情', index=False)

        except Exception as e:
            logger.error(f"导出详细技术指标失败: {e}")

    def _export_chip_distribution_analysis(self, writer, kline_data: pd.DataFrame):
        """导出筹码分布分析"""
        try:
            if kline_data.empty:
                empty_data = pd.DataFrame({
                    '提示': ['筹码分布分析需要K线数据'],
                    '说明': ['无K线数据，无法进行筹码分布分析']
                })
                empty_data.to_excel(writer, sheet_name='筹码分布', index=False)
                return

            # 简单的筹码分布分析
            chip_analysis = {
                '分析项': ['数据天数', '平均价格', '价格标准差', '集中度评估'],
                '结果': [
                    str(len(kline_data)),
                    f"{kline_data['close'].mean():.2f}",
                    f"{kline_data['close'].std():.2f}",
                    '中等集中' if kline_data['close'].std() / kline_data['close'].mean() < 0.1 else '分散'
                ]
            }

            chip_df = pd.DataFrame(chip_analysis)
            chip_df.to_excel(writer, sheet_name='筹码分布', index=False)

        except Exception as e:
            logger.error(f"导出筹码分布分析失败: {e}")

    def _export_time_cycle_analysis(self, writer, analysis_data: Dict):
        """导出时间周期分析"""
        try:
            kline_data = analysis_data.get('kline_data', pd.DataFrame())

            if kline_data.empty:
                empty_data = pd.DataFrame({
                    '提示': ['时间周期分析需要K线数据'],
                    '说明': ['无K线数据，无法进行时间周期分析']
                })
                empty_data.to_excel(writer, sheet_name='时间周期', index=False)
                return

            # 简单的时间周期分析
            cycle_analysis = {
                '周期项': ['数据起始日期', '数据结束日期', '数据天数', '交易周期'],
                '结果': [
                    str(kline_data.iloc[0].get('date', '未知')),
                    str(kline_data.iloc[-1].get('date', '未知')),
                    str(len(kline_data)),
                    f"{len(kline_data)}个交易日"
                ]
            }

            cycle_df = pd.DataFrame(cycle_analysis)
            cycle_df.to_excel(writer, sheet_name='时间周期', index=False)

        except Exception as e:
            logger.error(f"导出时间周期分析失败: {e}")

    def _export_data_quality_report(self, writer):
        """导出数据质量报告"""
        try:
            quality_data = {
                '质量项': ['数据完整性', '导出时间', '数据来源', '质量评估'],
                '结果': [
                    f"{self.data_quality_report.get('overview', {}).get('completeness', 0):.1f}%",
                    self.export_time,
                    'AData + AKShare',
                    '良好' if self.data_quality_report.get('overview', {}).get('completeness', 0) >= 70 else '一般'
                ]
            }

            quality_df = pd.DataFrame(quality_data)
            quality_df.to_excel(writer, sheet_name='数据质量', index=False)

        except Exception as e:
            logger.error(f"导出数据质量报告失败: {e}")

    def export_kline_data_only(self, stock_code: str, kline_data: pd.DataFrame,
                              filename: str, kline_type: str = "日K") -> bool:
        """
        单独导出K线数据
        
        Args:
            stock_code: 股票代码
            kline_data: K线数据
            kline_type: K线类型
            filename: 文件名
        
        Returns:
            bool: 导出是否成功
        """
        try:
            if kline_data.empty:
                logger.warning("K线数据为空，无法导出")
                return False
            
            export_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            
            with pd.ExcelWriter(filename, engine='openpyxl') as writer:
                # 导出K线数据
                kline_data.to_excel(writer, sheet_name=f'{kline_type}数据', index=False)
                
                # 创建信息页
                info_data = {
                    '项目': ['股票代码', 'K线类型', '数据条数', '导出时间', 
                           '数据起始时间', '数据结束时间'],
                    '值': [
                        stock_code,
                        kline_type,
                        len(kline_data),
                        export_time,
                        str(kline_data.iloc[0]['trade_date'] if 'trade_date' in kline_data.columns 
                            else kline_data.iloc[0]['trade_time']),
                        str(kline_data.iloc[-1]['trade_date'] if 'trade_date' in kline_data.columns 
                            else kline_data.iloc[-1]['trade_time'])
                    ]
                }
                
                info_df = pd.DataFrame(info_data)
                info_df.to_excel(writer, sheet_name='导出信息', index=False)
            
            logger.info(f"K线数据导出成功: {filename}")
            return True
            
        except Exception as e:
            logger.error(f"导出K线数据失败: {str(e)}")
            return False
    
    def get_default_filename(self, stock_code: str, data_type: str = "综合分析", 
                           file_format: str = "xlsx") -> str:
        """
        生成默认文件名
        
        Args:
            stock_code: 股票代码
            data_type: 数据类型
            file_format: 文件格式
        
        Returns:
            str: 默认文件名
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        return f"{stock_code}_{data_type}_{timestamp}.{file_format}"

    def _export_detailed_minute_data(self, writer, minute_data_result: Dict):
        """导出详细分时数据分析 - 增强版，支持7天数据和游资手法分析"""
        try:
            # 检查是否有7天数据
            if minute_data_result and minute_data_result.get('trading_days_data'):
                # 导出7天分时数据
                self._export_7days_minute_data(writer, minute_data_result)
            elif minute_data_result and minute_data_result.get('data_available', False):
                # 导出单日分时数据
                self._export_single_day_minute_data(writer, minute_data_result)
            else:
                # 导出空数据提示
                empty_data = pd.DataFrame({
                    '提示': ['分时数据不可用'],
                    '说明': ['无法获取分时数据，可能是非交易日或数据源问题'],
                    '建议': ['请在交易日重试或检查股票代码']
                })
                empty_data.to_excel(writer, sheet_name='分时数据', index=False)

        except Exception as e:
            logger.error(f"导出详细分时数据失败: {e}")
            # 导出错误信息
            error_data = pd.DataFrame({
                '错误': ['分时数据导出失败'],
                '原因': [str(e)],
                '建议': ['请检查数据源或联系技术支持']
            })
            error_data.to_excel(writer, sheet_name='分时数据-错误信息', index=False)

    def _export_7days_minute_data(self, writer, result_7days: Dict):
        """导出7天分时数据和游资分析"""
        try:
            trading_days_data = result_7days.get('trading_days_data', [])
            combined_analysis = result_7days.get('combined_analysis', {})

            # 1. 导出7天数据概览
            overview_data = {
                '分析项目': [
                    '分析周期',
                    '交易日总数',
                    '日期范围',
                    '炒作阶段',
                    '成交量趋势',
                    '价格趋势',
                    '游资信号数量',
                    '分析摘要'
                ],
                '分析结果': [
                    '最近7天',
                    f"{result_7days.get('total_trading_days', 0)}个交易日",
                    result_7days.get('date_range', '未知'),
                    combined_analysis.get('cycle_stage', '无法判断'),
                    combined_analysis.get('volume_trend', '未知'),
                    combined_analysis.get('price_trend', '未知'),
                    f"{len(combined_analysis.get('gaming_capital_signals', []))}个",
                    result_7days.get('summary', '无')
                ]
            }

            overview_df = pd.DataFrame(overview_data)
            overview_df.to_excel(writer, sheet_name='分时数据-7天概览', index=False)

            # 2. 导出每日分时数据汇总
            daily_summary = []
            for day_data in trading_days_data:
                minute_data = day_data['minute_data']
                analysis = day_data['analysis']

                if not minute_data.empty:
                    daily_summary.append({
                        '日期': day_data['date_formatted'],
                        '星期': day_data['weekday'],
                        '记录数': len(minute_data),
                        '开盘价(元)': minute_data['收盘'].iloc[0] if '收盘' in minute_data.columns else 0,
                        '收盘价(元)': minute_data['收盘'].iloc[-1] if '收盘' in minute_data.columns else 0,
                        '最高价(元)': minute_data['最高'].max() if '最高' in minute_data.columns else 0,
                        '最低价(元)': minute_data['最低'].min() if '最低' in minute_data.columns else 0,
                        '成交量(万股)': round(minute_data['成交量'].sum() / 10000, 2) if '成交量' in minute_data.columns else 0,
                        '成交额(万元)': round(minute_data['成交额'].sum() / 10000, 2) if '成交额' in minute_data.columns else 0,
                        '振幅(%)': analysis.get('price_change_pct', 0),
                        '关键特征': ', '.join(analysis.get('key_features', [])[:3])
                    })

            if daily_summary:
                daily_df = pd.DataFrame(daily_summary)
                daily_df.to_excel(writer, sheet_name='分时数据-每日汇总', index=False)

            # 3. 导出游资信号详情
            gaming_signals = combined_analysis.get('gaming_capital_signals', [])
            if gaming_signals:
                signals_data = {
                    '序号': list(range(1, len(gaming_signals) + 1)),
                    '游资信号': gaming_signals,
                    '信号类型': ['成交量异常' if '放量' in signal else '价格异常' if '波动' in signal else '趋势信号' for signal in gaming_signals],
                    '重要程度': ['高' if any(word in signal for word in ['连续', '大幅', '持续']) else '中' for signal in gaming_signals]
                }

                signals_df = pd.DataFrame(signals_data)
                signals_df.to_excel(writer, sheet_name='分时数据-游资信号', index=False)

            # 4. 导出详细逐分钟分时数据（关键修复）
            self._export_detailed_minute_records(writer, trading_days_data)

            # 5. 导出关键时段分析
            self._export_key_timeperiod_analysis(writer, trading_days_data)

            # 6. 导出最新交易日详细分析
            if trading_days_data:
                latest_day = trading_days_data[-1]  # 最新的交易日
                self._export_single_day_detailed_analysis(writer, latest_day, '分时数据-最新详情')

            logger.info(f"7天分时数据导出成功")

        except Exception as e:
            logger.error(f"导出7天分时数据失败: {e}")

    def _export_single_day_minute_data(self, writer, minute_data_result: Dict):
        """导出单日分时数据"""
        try:
            minute_data = minute_data_result.get('minute_data', pd.DataFrame())
            analysis = minute_data_result.get('analysis', {})

            if minute_data.empty:
                empty_data = pd.DataFrame({
                    '提示': ['分时数据为空'],
                    '说明': ['获取到的分时数据为空'],
                    '目标日期': [minute_data_result.get('target_date', '未知')],
                    '数据周期': [f"{minute_data_result.get('period', '1')}分钟"]
                })
                empty_data.to_excel(writer, sheet_name='分时数据', index=False)
                return

            # 导出完整分时数据
            minute_export = self._format_minute_data_for_export(minute_data)
            minute_export.to_excel(writer, sheet_name='分时数据-完整记录', index=False)

            # 导出分析摘要
            analysis_data = {
                '分析项目': [
                    '数据日期',
                    '数据周期',
                    '记录总数',
                    '时间范围',
                    '价格范围',
                    '总成交量(股)',
                    '价格波动幅度',
                    '关键特征'
                ],
                '分析结果': [
                    minute_data_result.get('target_date', '未知'),
                    f"{minute_data_result.get('period', '1')}分钟",
                    f"{analysis.get('total_records', 0)}条",
                    analysis.get('time_range', '未知'),
                    analysis.get('price_range', '未知'),
                    f"{analysis.get('volume_total', 0):,}",
                    f"{analysis.get('price_change_pct', 0):.2f}%",
                    ', '.join(analysis.get('key_features', [])[:5])
                ]
            }

            analysis_df = pd.DataFrame(analysis_data)
            analysis_df.to_excel(writer, sheet_name='分时数据-分析摘要', index=False)

            logger.info(f"单日分时数据导出成功")

        except Exception as e:
            logger.error(f"导出单日分时数据失败: {e}")

    def _format_minute_data_for_export(self, minute_data: pd.DataFrame, date_str: str = None) -> pd.DataFrame:
        """格式化分时数据用于导出 - 修复版本，保留完整日期时间"""
        try:
            minute_export = minute_data.copy()

            # 关键修复1：保留完整的日期时间格式
            if '时间' in minute_export.columns:
                # 确保时间列包含日期信息
                if minute_export['时间'].dtype == 'object':
                    # 如果时间列是字符串，尝试解析
                    try:
                        minute_export['时间'] = pd.to_datetime(minute_export['时间'])
                    except:
                        # 如果解析失败且提供了日期，手动添加日期
                        if date_str:
                            minute_export['时间'] = pd.to_datetime(f"{date_str} " + minute_export['时间'].astype(str))

                # 如果时间列没有日期信息且提供了日期，添加日期
                if date_str and minute_export['时间'].dt.date.nunique() == 1:
                    # 检查是否需要添加日期
                    first_time = minute_export['时间'].iloc[0]
                    if first_time.date() == datetime(1900, 1, 1).date():
                        # 需要添加正确的日期
                        minute_export['时间'] = minute_export['时间'].apply(
                            lambda x: datetime.strptime(f"{date_str} {x.strftime('%H:%M:%S')}", '%Y%m%d %H:%M:%S')
                        )

                # 格式化为完整的日期时间字符串
                minute_export['完整时间'] = minute_export['时间'].dt.strftime('%Y-%m-%d %H:%M:%S')
                minute_export['交易日期'] = minute_export['时间'].dt.strftime('%Y-%m-%d')
                minute_export['交易时间'] = minute_export['时间'].dt.strftime('%H:%M:%S')

            # 格式化数值列，确保单位正确
            price_columns = ['开盘', '收盘', '最高', '最低', '均价']
            for col in price_columns:
                if col in minute_export.columns:
                    minute_export[f'{col}(元)'] = minute_export[col].round(2)
                    minute_export = minute_export.drop(col, axis=1)

            # 成交量保持原单位（股）
            if '成交量' in minute_export.columns:
                minute_export['成交量(股)'] = minute_export['成交量'].astype(int)
                minute_export = minute_export.drop('成交量', axis=1)

            # 成交额转换为万元
            if '成交额' in minute_export.columns:
                minute_export['成交额(万元)'] = (minute_export['成交额'] / 10000).round(2)
                minute_export = minute_export.drop('成交额', axis=1)

            # 重新排列列顺序
            column_order = ['完整时间', '交易日期', '交易时间']
            price_cols = [col for col in minute_export.columns if '(元)' in col]
            volume_cols = [col for col in minute_export.columns if '成交量' in col]
            amount_cols = [col for col in minute_export.columns if '成交额' in col]
            other_cols = [col for col in minute_export.columns if col not in column_order + price_cols + volume_cols + amount_cols]

            final_columns = column_order + price_cols + volume_cols + amount_cols + other_cols
            available_columns = [col for col in final_columns if col in minute_export.columns]
            minute_export = minute_export[available_columns]

            return minute_export

        except Exception as e:
            logger.error(f"格式化分时数据失败: {e}")
            return minute_data

    def _export_single_day_detailed_analysis(self, writer, day_data: Dict, sheet_name: str):
        """导出单日详细分析，包括游资手法识别"""
        try:
            minute_data = day_data['minute_data']

            if minute_data.empty:
                return

            # 进行游资手法分析
            from enhanced_data_fetcher import enhanced_stock_fetcher
            analysis_result = enhanced_stock_fetcher.analyze_minute_data_patterns(minute_data, day_data['date'])

            if not analysis_result.get('analysis_available', False):
                return

            # 1. 导出游资手法识别结果
            patterns = analysis_result.get('patterns', {})
            gaming_signals = analysis_result.get('gaming_signals', [])
            recommendations = analysis_result.get('recommendations', [])

            # 创建游资分析摘要
            try:
                # 确保patterns是字典类型
                if not isinstance(patterns, dict):
                    patterns = {}

                # 安全获取各项数据
                pullup_patterns = patterns.get('pullup', {}) if isinstance(patterns.get('pullup'), dict) else {}
                gaming_techniques = patterns.get('gaming_techniques', {}) if isinstance(patterns.get('gaming_techniques'), dict) else {}
                volume_anomalies = patterns.get('volume_anomalies', {}) if isinstance(patterns.get('volume_anomalies'), dict) else {}
                key_timepoints = analysis_result.get('key_timepoints', {}) if isinstance(analysis_result.get('key_timepoints'), dict) else {}

                gaming_analysis = {
                    '分析项目': [
                        '分析日期',
                        '游资信号总数',
                        '拉升形态',
                        '游资手法',
                        '成交量异常',
                        '关键时点信号',
                        '分析摘要',
                        '操作建议'
                    ],
                    '分析结果': [
                        str(day_data.get('date_formatted', '未知日期')),
                        f"{len(gaming_signals)}个",
                        ', '.join(pullup_patterns.get('patterns_found', ['无'])) if pullup_patterns.get('patterns_found') else '无',
                        ', '.join(gaming_techniques.get('techniques_found', ['无'])) if gaming_techniques.get('techniques_found') else '无',
                        f"{len(volume_anomalies.get('volume_spikes', []))}次异常",
                        f"{len(key_timepoints.get('signals', []))}个",
                        str(analysis_result.get('summary', '无')),
                        f"{len(recommendations)}条建议"
                    ]
                }
            except Exception as e:
                logger.error(f"模式摘要生成失败: {e}")
                # 创建简化的摘要
                gaming_analysis = {
                    '分析项目': ['分析日期', '游资信号总数', '错误信息'],
                    '分析结果': [
                        str(day_data.get('date_formatted', '未知日期')),
                        f"{len(gaming_signals)}个",
                        f"摘要生成失败: {str(e)}"
                    ]
                }

            gaming_df = pd.DataFrame(gaming_analysis)
            gaming_df.to_excel(writer, sheet_name=f'{sheet_name}-游资分析', index=False)

            # 2. 导出拉升形态详情
            pullup_patterns = patterns.get('pullup', {})
            if pullup_patterns.get('details'):
                pullup_details = []

                # 90度拉板详情
                rapid_pullups = pullup_patterns.get('details', {}).get('rapid_pullups', [])
                for pullup in rapid_pullups:
                    pullup_details.append({
                        '形态类型': '90度拉板',
                        '时间': pullup['time'],
                        '涨幅(%)': pullup['change_pct'],
                        '成交量': pullup['volume'],
                        '特征': '快速拉升'
                    })

                # 阶梯拉升详情
                step_pullups = pullup_patterns.get('details', {}).get('step_pullups', [])
                for step in step_pullups:
                    pullup_details.append({
                        '形态类型': '阶梯拉升',
                        '时间': f"{step['start_time']}-{step.get('end_time', '未知')}",
                        '涨幅(%)': round(step['total_change'], 2),
                        '成交量': '多笔',
                        '特征': f"{step['steps']}步拉升"
                    })

                if pullup_details:
                    pullup_df = pd.DataFrame(pullup_details)
                    pullup_df.to_excel(writer, sheet_name=f'{sheet_name}-拉升形态', index=False)

            # 3. 导出成交量异常详情
            volume_anomalies = patterns.get('volume_anomalies', {})
            volume_spikes = volume_anomalies.get('volume_spikes', [])

            if volume_spikes:
                volume_details = []
                for spike in volume_spikes:
                    volume_details.append({
                        '时间': spike['time'],
                        '成交量(股)': spike['volume'],
                        '倍数': f"{spike['multiple']}倍",
                        '异常程度': '高' if spike['multiple'] > 5 else '中' if spike['multiple'] > 3 else '低',
                        '可能原因': '大单买入' if spike['multiple'] > 5 else '资金关注'
                    })

                volume_df = pd.DataFrame(volume_details)
                volume_df.to_excel(writer, sheet_name=f'{sheet_name}-成交量异常', index=False)

            # 4. 导出操作建议
            if recommendations:
                rec_details = []
                for rec in recommendations:
                    rec_details.append({
                        '建议类型': rec['type'],
                        '分析原因': rec['reason'],
                        '操作建议': rec['action'],
                        '风险提示': rec['risk']
                    })

                rec_df = pd.DataFrame(rec_details)
                rec_df.to_excel(writer, sheet_name=f'{sheet_name}-操作建议', index=False)

            logger.info(f"单日详细分析导出成功: {sheet_name}")

        except Exception as e:
            logger.error(f"导出单日详细分析失败: {e}")

    def _export_detailed_minute_records(self, writer, trading_days_data: List[Dict]):
        """导出详细逐分钟分时数据 - 游资炒作全链路核心需求"""
        try:
            if not trading_days_data:
                return

            # 1. 导出所有交易日的详细分时数据
            all_minute_records = []

            for day_data in trading_days_data:
                minute_data = day_data['minute_data']
                if not minute_data.empty:
                    # 关键修复2：正确处理每天的数据
                    minute_export = minute_data.copy()

                    # 使用原始日期格式传递给格式化函数
                    date_str = day_data['date']  # 使用原始日期格式 YYYYMMDD

                    # 使用修复后的格式化函数，传递日期参数
                    minute_export = self._format_minute_data_for_export(minute_export, date_str)

                    # 添加星期信息
                    minute_export['星期'] = day_data['weekday']

                    # 添加涨跌幅计算
                    if '收盘(元)' in minute_export.columns:
                        minute_export['涨跌幅(%)'] = minute_export['收盘(元)'].pct_change() * 100
                        minute_export['涨跌幅(%)'] = minute_export['涨跌幅(%)'].round(3)

                    all_minute_records.append(minute_export)

            if all_minute_records:
                # 关键修复3：正确合并数据，避免重复
                combined_minute_data = pd.concat(all_minute_records, ignore_index=True)

                # 关键修复4：按完整时间排序，确保时间顺序正确
                if '完整时间' in combined_minute_data.columns:
                    combined_minute_data = combined_minute_data.sort_values('完整时间')

                # 验证数据质量并去重
                total_records = len(combined_minute_data)
                if '完整时间' in combined_minute_data.columns:
                    unique_times = combined_minute_data['完整时间'].nunique()
                    if total_records != unique_times:
                        logger.warning(f"发现{total_records - unique_times}条重复数据，正在去重...")
                        combined_minute_data = combined_minute_data.drop_duplicates(subset=['完整时间'])
                        logger.info(f"去重完成，剩余{len(combined_minute_data)}条记录")

                # 重新排列列顺序，确保关键信息在前
                column_order = ['完整时间', '交易日期', '交易时间', '星期']
                price_cols = [col for col in combined_minute_data.columns if '(元)' in col]
                volume_cols = [col for col in combined_minute_data.columns if '成交量' in col]
                amount_cols = [col for col in combined_minute_data.columns if '成交额' in col]
                other_cols = [col for col in combined_minute_data.columns if col not in column_order + price_cols + volume_cols + amount_cols]

                final_columns = column_order + price_cols + volume_cols + amount_cols + other_cols
                available_columns = [col for col in final_columns if col in combined_minute_data.columns]
                combined_minute_data = combined_minute_data[available_columns]

                # 导出详细分时数据
                combined_minute_data.to_excel(writer, sheet_name='分时数据-详细记录(7天)', index=False)

                # 记录修复后的数据质量
                unique_dates = combined_minute_data['交易日期'].nunique() if '交易日期' in combined_minute_data.columns else 0
                logger.info(f"7天分时数据导出成功: {len(combined_minute_data)}条记录, {unique_dates}个交易日")

                # 2. 按日期分别导出（便于单日分析）
                for day_data in trading_days_data:
                    day_minute_data = combined_minute_data[combined_minute_data['交易日期'] == day_data['date_formatted']]
                    if not day_minute_data.empty:
                        sheet_name = f"分时数据-{day_data['date_formatted']}"
                        day_minute_data.to_excel(writer, sheet_name=sheet_name, index=False)

                logger.info(f"详细逐分钟分时数据导出成功: {len(combined_minute_data)}条记录")

        except Exception as e:
            logger.error(f"导出详细逐分钟分时数据失败: {e}")

    def _export_key_timeperiod_analysis(self, writer, trading_days_data: List[Dict]):
        """导出关键时段分析 - 基于游资炒作全链路的关键时点"""
        try:
            if not trading_days_data:
                return

            key_periods = {
                '开盘30分钟': ('09:30', '10:00'),
                '最佳建仓时段': ('10:15', '11:15'),
                '午后建仓时段': ('13:15', '14:15'),
                '尾盘30分钟': ('14:30', '15:00')
            }

            # 分析每个关键时段
            timeperiod_analysis = []

            for day_data in trading_days_data:
                minute_data = day_data['minute_data']
                if minute_data.empty:
                    continue

                # 确保时间列是datetime类型
                if minute_data['时间'].dtype == 'object':
                    minute_data['时间'] = pd.to_datetime(minute_data['时间'])

                for period_name, (start_time, end_time) in key_periods.items():
                    # 筛选时段数据
                    start_hour, start_min = map(int, start_time.split(':'))
                    end_hour, end_min = map(int, end_time.split(':'))

                    period_data = minute_data[
                        (minute_data['时间'].dt.hour > start_hour) |
                        ((minute_data['时间'].dt.hour == start_hour) & (minute_data['时间'].dt.minute >= start_min))
                    ]
                    period_data = period_data[
                        (period_data['时间'].dt.hour < end_hour) |
                        ((period_data['时间'].dt.hour == end_hour) & (period_data['时间'].dt.minute <= end_min))
                    ]

                    if not period_data.empty:
                        # 计算时段统计
                        period_volume = period_data['成交量'].sum() if '成交量' in period_data.columns else 0
                        period_amount = period_data['成交额'].sum() if '成交额' in period_data.columns else 0

                        if '收盘' in period_data.columns:
                            period_start_price = period_data['收盘'].iloc[0]
                            period_end_price = period_data['收盘'].iloc[-1]
                            period_high = period_data['最高'].max() if '最高' in period_data.columns else period_end_price
                            period_low = period_data['最低'].min() if '最低' in period_data.columns else period_end_price
                            period_change = (period_end_price - period_start_price) / period_start_price * 100
                            period_amplitude = (period_high - period_low) / period_low * 100
                        else:
                            period_change = 0
                            period_amplitude = 0

                        # 识别异常特征
                        features = []
                        if period_volume > minute_data['成交量'].mean() * 2:
                            features.append('成交量异常放大')
                        if abs(period_change) > 2:
                            features.append(f'价格大幅{"上涨" if period_change > 0 else "下跌"}')
                        if period_amplitude > 3:
                            features.append('振幅较大')

                        timeperiod_analysis.append({
                            '交易日期': day_data['date_formatted'],
                            '时段名称': period_name,
                            '时段范围': f"{start_time}-{end_time}",
                            '成交量(万股)': round(period_volume / 10000, 2),
                            '成交额(万元)': round(period_amount / 10000, 2),
                            '涨跌幅(%)': round(period_change, 2),
                            '振幅(%)': round(period_amplitude, 2),
                            '记录数': len(period_data),
                            '异常特征': ', '.join(features) if features else '正常'
                        })

            if timeperiod_analysis:
                timeperiod_df = pd.DataFrame(timeperiod_analysis)
                timeperiod_df.to_excel(writer, sheet_name='分时数据-关键时段分析', index=False)
                logger.info(f"关键时段分析导出成功: {len(timeperiod_analysis)}条记录")

        except Exception as e:
            logger.error(f"导出关键时段分析失败: {e}")

    def _export_advanced_chip_distribution_analysis(self, writer, stock_code: str):
        """导出高级筹码分布分析"""
        try:
            if not self.advanced_analyzers_ready:
                # 使用简单版本
                self._export_chip_distribution_analysis(writer, pd.DataFrame())
                return

            logger.info(f"开始导出{stock_code}的高级筹码分布分析")

            # 使用高级筹码分布分析器
            chip_result = self.chip_analyzer.get_chip_distribution_analysis(stock_code)

            if chip_result.get('data_available', False):
                # 导出筹码分布概览
                try:
                    overview_data = []
                    analysis = chip_result.get('analysis', {})

                    # 确保所有值都是字符串，避免DataFrame创建错误
                    overview_data.append(['分析项目', '分析结果', '详细说明'])
                    overview_data.append(['数据可用性', '是', '成功获取筹码分布数据'])
                    overview_data.append(['分析时间', str(chip_result.get('analysis_time', '')), '筹码分布分析完成时间'])
                    overview_data.append(['当前价格', f"{analysis.get('current_price', 0):.2f}元", '分析基准价格'])
                    overview_data.append(['套牢盘比例', f"{analysis.get('trapped_ratio', 0):.1f}%", '高于当前价格的筹码比例'])
                    overview_data.append(['获利盘比例', f"{analysis.get('profit_ratio', 0):.1f}%", '低于当前价格的筹码比例'])
                    overview_data.append(['主力成本区间', str(analysis.get('main_cost_range', 'N/A')), '主力资金集中成本区域'])
                    overview_data.append(['筹码集中度', str(analysis.get('concentration_level', 'N/A')), '筹码分布的集中程度'])
                    overview_data.append(['风险等级', str(analysis.get('risk_level', 'N/A')), '基于筹码分布的风险评估'])
                    overview_data.append(['操作建议', str(analysis.get('operation_advice', 'N/A')), '基于筹码分析的操作建议'])

                    overview_df = pd.DataFrame(overview_data[1:], columns=overview_data[0])
                    overview_df.to_excel(writer, sheet_name='筹码分布-概览', index=False)
                except Exception as e:
                    logger.error(f"筹码分布概览导出失败: {e}")
                    # 创建简单的错误信息表
                    error_df = pd.DataFrame({
                        '错误信息': ['筹码分布概览创建失败'],
                        '详细说明': [str(e)]
                    })
                    error_df.to_excel(writer, sheet_name='筹码分布-概览', index=False)

                # 导出详细筹码分布数据
                if 'chip_distribution' in chip_result:
                    chip_dist_data = chip_result['chip_distribution']
                    if isinstance(chip_dist_data, dict) and chip_dist_data:
                        # 将字典转换为DataFrame
                        chip_dist_df = pd.DataFrame([
                            {'价格': price, '筹码比例': ratio}
                            for price, ratio in chip_dist_data.items()
                        ])
                        chip_dist_df = chip_dist_df.sort_values('价格')
                        chip_dist_df.to_excel(writer, sheet_name='筹码分布-详细数据', index=False)
                    elif isinstance(chip_dist_data, pd.DataFrame) and not chip_dist_data.empty:
                        chip_dist_data.to_excel(writer, sheet_name='筹码分布-详细数据', index=False)

                # 导出筹码变化趋势
                if 'trend_analysis' in chip_result:
                    trend_data = chip_result['trend_analysis']
                    if isinstance(trend_data, dict) and trend_data:
                        # 将趋势分析字典转换为DataFrame
                        trend_items = []
                        for key, value in trend_data.items():
                            trend_items.append({'分析项目': key, '分析结果': str(value)})
                        trend_df = pd.DataFrame(trend_items)
                        trend_df.to_excel(writer, sheet_name='筹码分布-变化趋势', index=False)
                    elif isinstance(trend_data, pd.DataFrame) and not trend_data.empty:
                        trend_data.to_excel(writer, sheet_name='筹码分布-变化趋势', index=False)

                logger.info(f"高级筹码分布分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [chip_result.get('error', '筹码分布分析失败')],
                    '说明': ['无法获取筹码分布数据，请检查股票代码或网络连接']
                })
                error_data.to_excel(writer, sheet_name='筹码分布-错误', index=False)

        except Exception as e:
            logger.error(f"导出高级筹码分布分析失败: {e}")
            # 回退到简单版本
            self._export_chip_distribution_analysis(writer, pd.DataFrame())

    def _export_advanced_sector_linkage_analysis(self, writer, stock_code: str):
        """导出高级板块联动分析"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的高级板块联动分析")

            # 使用高级板块联动分析器
            sector_result = self.sector_analyzer.get_sector_linkage_analysis(stock_code)

            if sector_result.get('data_available', False):
                # 导出板块联动概览
                overview_data = []
                analysis = sector_result.get('analysis', {})

                overview_data.append(['分析项目', '分析结果', '详细说明'])
                overview_data.append(['数据可用性', '是', '成功获取板块联动数据'])
                overview_data.append(['分析时间', sector_result.get('analysis_time', ''), '板块联动分析完成时间'])
                overview_data.append(['股票名称', analysis.get('stock_name', 'N/A'), '目标股票名称'])
                overview_data.append(['所属行业', analysis.get('industry', 'N/A'), '股票所属行业分类'])
                overview_data.append(['概念板块', analysis.get('concepts', 'N/A'), '股票相关概念板块'])
                overview_data.append(['同板块股票数', str(analysis.get('sector_stock_count', 0)), '同板块股票总数'])
                overview_data.append(['板块联动强度', analysis.get('linkage_strength', 'N/A'), '与板块的联动程度'])
                overview_data.append(['板块热度', analysis.get('sector_heat', 'N/A'), '板块当前热度水平'])
                overview_data.append(['资金流向', analysis.get('fund_flow', 'N/A'), '板块资金流向情况'])
                overview_data.append(['综合评分', f"{analysis.get('overall_score', 0)}/100", '板块联动综合评分'])

                overview_df = pd.DataFrame(overview_data[1:], columns=overview_data[0])
                overview_df.to_excel(writer, sheet_name='板块联动-概览', index=False)

                # 导出同板块股票数据
                if 'sector_stocks' in sector_result:
                    sector_stocks_df = pd.DataFrame(sector_result['sector_stocks'])
                    if not sector_stocks_df.empty:
                        sector_stocks_df.to_excel(writer, sheet_name='板块联动-同板块股票', index=False)

                # 导出板块表现数据
                if 'sector_performance' in sector_result:
                    performance_df = pd.DataFrame(sector_result['sector_performance'])
                    if not performance_df.empty:
                        performance_df.to_excel(writer, sheet_name='板块联动-板块表现', index=False)

                logger.info(f"高级板块联动分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [sector_result.get('error', '板块联动分析失败')],
                    '说明': ['无法获取板块联动数据，请检查股票代码或网络连接']
                })
                error_data.to_excel(writer, sheet_name='板块联动-错误', index=False)

        except Exception as e:
            logger.error(f"导出高级板块联动分析失败: {e}")

    def _export_advanced_market_sentiment_analysis(self, writer, stock_code: str):
        """导出高级市场情绪分析"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的高级市场情绪分析")

            # 使用高级市场情绪数据收集器
            sentiment_result = self.sentiment_collector.get_market_sentiment_data(stock_code)

            if sentiment_result.get('data_available', False):
                # 导出市场情绪概览
                summary = sentiment_result.get('sentiment_summary', {})
                overview_data = []

                overview_data.append(['分析项目', '分析结果', '详细说明'])
                overview_data.append(['数据可用性', '是', '成功收集市场情绪数据'])
                overview_data.append(['收集时间', sentiment_result.get('collection_time', ''), '数据收集完成时间'])
                overview_data.append(['目标股票', sentiment_result.get('target_stock', ''), '分析的目标股票'])
                overview_data.append(['数据完整性', summary.get('overall_completeness', 'N/A'), '数据收集完整性'])
                overview_data.append(['完整性百分比', f"{summary.get('completeness_percentage', 0)}%", '数据完整性百分比'])
                overview_data.append(['总数据记录', str(sum(summary.get('data_counts', {}).values())), '收集的总数据条数'])

                overview_df = pd.DataFrame(overview_data[1:], columns=overview_data[0])
                overview_df.to_excel(writer, sheet_name='市场情绪-概览', index=False)

                # 导出各类市场情绪数据
                data_types = [
                    ('market_indices_data', '市场情绪-指数数据'),
                    ('market_breadth_data', '市场情绪-市场广度'),
                    ('volatility_data', '市场情绪-波动率'),
                    ('volume_sentiment_data', '市场情绪-成交量'),
                    ('sector_sentiment_data', '市场情绪-板块情绪'),
                    ('fear_greed_indicators', '市场情绪-恐慌贪婪'),
                    ('market_momentum_data', '市场情绪-市场动量')
                ]

                for data_key, sheet_name in data_types:
                    if data_key in sentiment_result:
                        data_df = sentiment_result[data_key]
                        if isinstance(data_df, pd.DataFrame) and not data_df.empty:
                            # 限制数据量，避免Excel文件过大
                            if len(data_df) > 2000:
                                data_df = data_df.head(2000)
                            data_df.to_excel(writer, sheet_name=sheet_name, index=False)

                logger.info(f"高级市场情绪分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [sentiment_result.get('error', '市场情绪分析失败')],
                    '说明': ['无法收集市场情绪数据，请检查网络连接']
                })
                error_data.to_excel(writer, sheet_name='市场情绪-错误', index=False)

        except Exception as e:
            logger.error(f"导出高级市场情绪分析失败: {e}")

    def _export_advanced_opening_features_analysis(self, writer, stock_code: str):
        """导出高级开盘特征分析"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的高级开盘特征分析")

            # 使用高级开盘特征分析器
            opening_result = self.opening_analyzer.get_opening_features_analysis(stock_code)

            if opening_result.get('data_available', False):
                # 导出开盘特征概览
                overview_data = []
                analysis = opening_result.get('analysis', {})

                overview_data.append(['分析项目', '分析结果', '详细说明'])
                overview_data.append(['数据可用性', '是', '成功获取开盘特征数据'])
                overview_data.append(['分析时间', opening_result.get('analysis_time', ''), '开盘特征分析完成时间'])
                overview_data.append(['综合评分', f"{analysis.get('overall_score', 0)}/100", '开盘特征综合评分'])
                overview_data.append(['集合竞价表现', analysis.get('auction_performance', 'N/A'), '集合竞价阶段表现'])
                overview_data.append(['开盘异动情况', analysis.get('opening_anomaly', 'N/A'), '开盘价格异动分析'])
                overview_data.append(['试盘行为', analysis.get('probing_behavior', 'N/A'), '早盘试盘行为识别'])
                overview_data.append(['大单情况', analysis.get('large_orders', 'N/A'), '开盘大单追踪'])
                overview_data.append(['机会信号', analysis.get('opportunity_signals', 'N/A'), '识别的机会信号'])
                overview_data.append(['操作建议', analysis.get('operation_advice', 'N/A'), '基于开盘特征的操作建议'])

                overview_df = pd.DataFrame(overview_data[1:], columns=overview_data[0])
                overview_df.to_excel(writer, sheet_name='开盘特征-概览', index=False)

                # 导出集合竞价详细数据
                if 'auction_details' in opening_result:
                    auction_df = pd.DataFrame(opening_result['auction_details'])
                    if not auction_df.empty:
                        auction_df.to_excel(writer, sheet_name='开盘特征-集合竞价', index=False)

                # 导出开盘异动数据
                if 'anomaly_details' in opening_result:
                    anomaly_df = pd.DataFrame(opening_result['anomaly_details'])
                    if not anomaly_df.empty:
                        anomaly_df.to_excel(writer, sheet_name='开盘特征-异动分析', index=False)

                # 导出试盘行为数据
                if 'probing_details' in opening_result:
                    probing_df = pd.DataFrame(opening_result['probing_details'])
                    if not probing_df.empty:
                        probing_df.to_excel(writer, sheet_name='开盘特征-试盘行为', index=False)

                # 导出大单追踪数据
                if 'large_order_details' in opening_result:
                    large_order_df = pd.DataFrame(opening_result['large_order_details'])
                    if not large_order_df.empty:
                        large_order_df.to_excel(writer, sheet_name='开盘特征-大单追踪', index=False)

                logger.info(f"高级开盘特征分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [opening_result.get('error', '开盘特征分析失败')],
                    '说明': ['无法获取开盘特征数据，请检查股票代码或网络连接']
                })
                error_data.to_excel(writer, sheet_name='开盘特征-错误', index=False)

        except Exception as e:
            logger.error(f"导出高级开盘特征分析失败: {e}")

    # ========== 简化版导出方法 ==========

    def _export_simplified_chip_distribution_analysis(self, writer, stock_code: str):
        """导出简化的筹码分布分析（去除分析结论，只保留原始数据）"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的简化筹码分布分析")

            # 使用高级筹码分布分析器
            chip_result = self.chip_analyzer.get_chip_distribution_analysis(stock_code)

            if chip_result.get('data_available', False):
                # 导出筹码分布数据（只保留原始数据，不包含分析结论）
                if 'chip_distribution' in chip_result:
                    chip_dist_data = chip_result['chip_distribution']
                    if isinstance(chip_dist_data, dict) and chip_dist_data:
                        # 将字典转换为DataFrame
                        chip_dist_df = pd.DataFrame([
                            {'价格区间': f"{price:.2f}元", '筹码比例': f"{ratio:.2f}%"}
                            for price, ratio in chip_dist_data.items()
                        ])
                        chip_dist_df = chip_dist_df.sort_values('价格区间')
                        chip_dist_df.to_excel(writer, sheet_name='筹码分布-价格分布', index=False)

                logger.info(f"简化筹码分布分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [chip_result.get('error', '筹码分布分析失败')],
                    '说明': ['无法获取筹码分布数据']
                })
                error_data.to_excel(writer, sheet_name='筹码分布-错误', index=False)

        except Exception as e:
            logger.error(f"导出简化筹码分布分析失败: {e}")

    def _export_simplified_sector_linkage_analysis(self, writer, stock_code: str):
        """导出简化的板块联动分析（去除分析结论，只保留原始数据）"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的简化板块联动分析")

            # 使用高级板块联动分析器
            sector_result = self.sector_analyzer.get_sector_linkage_analysis(stock_code)

            if sector_result.get('data_available', False):
                # 导出同板块股票数据（只保留原始数据）
                if 'sector_stocks' in sector_result:
                    sector_stocks_df = pd.DataFrame(sector_result['sector_stocks'])
                    if not sector_stocks_df.empty:
                        # 只保留关键列，去除评分和建议
                        keep_columns = ['股票代码', '股票名称', '最新价', '涨跌幅', '成交量', '成交额', '市值']
                        available_columns = [col for col in keep_columns if col in sector_stocks_df.columns]
                        if available_columns:
                            simplified_df = sector_stocks_df[available_columns]
                            simplified_df.to_excel(writer, sheet_name='板块联动-同板块股票', index=False)

                # 导出板块表现数据（只保留原始数据）
                if 'sector_performance' in sector_result:
                    performance_df = pd.DataFrame(sector_result['sector_performance'])
                    if not performance_df.empty:
                        # 只保留关键列，去除评分和建议
                        keep_columns = ['日期', '板块涨跌幅', '个股涨跌幅', '相对强度', '成交量比率']
                        available_columns = [col for col in keep_columns if col in performance_df.columns]
                        if available_columns:
                            simplified_df = performance_df[available_columns]
                            simplified_df.to_excel(writer, sheet_name='板块联动-表现数据', index=False)

                logger.info(f"简化板块联动分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [sector_result.get('error', '板块联动分析失败')],
                    '说明': ['无法获取板块联动数据']
                })
                error_data.to_excel(writer, sheet_name='板块联动-错误', index=False)

        except Exception as e:
            logger.error(f"导出简化板块联动分析失败: {e}")

    def _export_simplified_market_sentiment_analysis(self, writer, stock_code: str):
        """导出简化的市场情绪分析（去除分析结论，修复英文列名）"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的简化市场情绪分析")

            # 使用高级市场情绪数据收集器
            sentiment_result = self.sentiment_collector.get_market_sentiment_data(stock_code)

            if sentiment_result.get('data_available', False):
                # 导出市场指数数据（只保留上证指数，修复列名）
                if 'market_indices_data' in sentiment_result:
                    indices_df = sentiment_result['market_indices_data']
                    if isinstance(indices_df, pd.DataFrame) and not indices_df.empty:
                        # 只保留上证指数数据
                        if 'index_code' in indices_df.columns:
                            shanghai_data = indices_df[indices_df['index_code'] == '000001'].copy()
                            if not shanghai_data.empty:
                                # 修复列名为中文
                                column_mapping = {
                                    'date': '日期',
                                    'open': '开盘价',
                                    'high': '最高价',
                                    'low': '最低价',
                                    'close': '收盘价',
                                    'volume': '成交量',
                                    'index_name': '指数名称'
                                }
                                shanghai_data = shanghai_data.rename(columns=column_mapping)
                                # 只保留最近30天
                                if len(shanghai_data) > 30:
                                    shanghai_data = shanghai_data.tail(30)
                                shanghai_data.to_excel(writer, sheet_name='市场情绪-上证指数', index=False)

                # 导出市场广度数据（简化版，只保留关键统计）
                if 'market_breadth_data' in sentiment_result:
                    breadth_df = sentiment_result['market_breadth_data']
                    if isinstance(breadth_df, pd.DataFrame) and not breadth_df.empty:
                        # 只保留关键列
                        keep_columns = ['上涨家数', '下跌家数', '平盘家数', '涨停家数', '跌停家数']
                        available_columns = [col for col in keep_columns if col in breadth_df.columns]
                        if available_columns:
                            simplified_breadth = breadth_df[available_columns].head(1)  # 只保留最新一条
                            simplified_breadth.to_excel(writer, sheet_name='市场情绪-市场广度', index=False)

                # 导出板块情绪数据（简化版）
                if 'sector_sentiment_data' in sentiment_result:
                    sector_df = sentiment_result['sector_sentiment_data']
                    if isinstance(sector_df, pd.DataFrame) and not sector_df.empty:
                        # 只保留前20个板块
                        simplified_sector = sector_df.head(20)
                        # 修复列名
                        column_mapping = {
                            '板块名称': '板块名称',
                            '涨跌幅': '涨跌幅(%)',
                            '总市值': '总市值(亿)',
                            '换手率': '换手率(%)'
                        }
                        simplified_sector = simplified_sector.rename(columns=column_mapping)
                        simplified_sector.to_excel(writer, sheet_name='市场情绪-板块表现', index=False)

                logger.info(f"简化市场情绪分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [sentiment_result.get('error', '市场情绪分析失败')],
                    '说明': ['无法收集市场情绪数据']
                })
                error_data.to_excel(writer, sheet_name='市场情绪-错误', index=False)

        except Exception as e:
            logger.error(f"导出简化市场情绪分析失败: {e}")

    def _export_simplified_opening_features_analysis(self, writer, stock_code: str):
        """导出简化的开盘特征分析（去除分析结论，只保留原始数据）"""
        try:
            if not self.advanced_analyzers_ready:
                return

            logger.info(f"开始导出{stock_code}的简化开盘特征分析")

            # 使用高级开盘特征分析器
            opening_result = self.opening_analyzer.get_opening_features_analysis(stock_code)

            if opening_result.get('data_available', False):
                # 导出集合竞价详细数据（只保留原始数据）
                if 'auction_details' in opening_result:
                    auction_df = pd.DataFrame(opening_result['auction_details'])
                    if not auction_df.empty:
                        # 只保留关键列，去除评分和建议
                        keep_columns = ['时间', '价格', '成交量', '涨跌幅', '成交额']
                        available_columns = [col for col in keep_columns if col in auction_df.columns]
                        if available_columns:
                            simplified_df = auction_df[available_columns]
                            simplified_df.to_excel(writer, sheet_name='开盘特征-集合竞价', index=False)

                # 导出开盘异动数据（只保留原始数据）
                if 'anomaly_details' in opening_result:
                    anomaly_df = pd.DataFrame(opening_result['anomaly_details'])
                    if not anomaly_df.empty:
                        # 只保留关键列
                        keep_columns = ['时间', '异动类型', '价格变化', '成交量变化', '异动幅度']
                        available_columns = [col for col in keep_columns if col in anomaly_df.columns]
                        if available_columns:
                            simplified_df = anomaly_df[available_columns]
                            simplified_df.to_excel(writer, sheet_name='开盘特征-异动记录', index=False)

                # 导出大单追踪数据（只保留原始数据）
                if 'large_order_details' in opening_result:
                    large_order_df = pd.DataFrame(opening_result['large_order_details'])
                    if not large_order_df.empty:
                        # 只保留关键列
                        keep_columns = ['时间', '方向', '成交价', '成交量', '成交额']
                        available_columns = [col for col in keep_columns if col in large_order_df.columns]
                        if available_columns:
                            simplified_df = large_order_df[available_columns]
                            simplified_df.to_excel(writer, sheet_name='开盘特征-大单记录', index=False)

                logger.info(f"简化开盘特征分析导出成功")
            else:
                # 导出错误信息
                error_data = pd.DataFrame({
                    '错误信息': [opening_result.get('error', '开盘特征分析失败')],
                    '说明': ['无法获取开盘特征数据']
                })
                error_data.to_excel(writer, sheet_name='开盘特征-错误', index=False)

        except Exception as e:
            logger.error(f"导出简化开盘特征分析失败: {e}")

    def _export_simplified_minute_data(self, writer, minute_data: Dict):
        """导出简化的分时数据（去除分析结论，只保留原始数据）"""
        try:
            logger.info("开始导出简化分时数据")

            # 只导出最近3天的分时数据，限制数据量
            trading_days_data = minute_data.get('trading_days_data', {})

            if trading_days_data:
                # 获取最近3天的数据
                sorted_days = sorted(trading_days_data.keys(), reverse=True)[:3]

                combined_minute_data = []
                for day in sorted_days:
                    day_data = trading_days_data[day]
                    minute_records = day_data.get('minute_data', [])

                    for record in minute_records:
                        record_copy = record.copy()
                        record_copy['交易日期'] = day
                        combined_minute_data.append(record_copy)

                if combined_minute_data:
                    minute_df = pd.DataFrame(combined_minute_data)

                    # 只保留关键列，去除分析结论
                    keep_columns = ['交易日期', '时间', '价格', '成交量', '成交额', '涨跌幅']
                    available_columns = [col for col in keep_columns if col in minute_df.columns]

                    if available_columns:
                        simplified_df = minute_df[available_columns]
                        # 限制记录数为1000条
                        if len(simplified_df) > 1000:
                            simplified_df = simplified_df.tail(1000)
                        simplified_df.to_excel(writer, sheet_name='分时数据-详细记录', index=False)
                        logger.info(f"简化分时数据导出成功: {len(simplified_df)}条记录")

        except Exception as e:
            logger.error(f"导出简化分时数据失败: {e}")

# 全局实例
enhanced_data_exporter = EnhancedDataExporter()
