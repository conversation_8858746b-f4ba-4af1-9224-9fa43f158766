#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
板块数据管理器 - 处理板块数据的获取、缓存和映射
"""

import pandas as pd
import json
import os
from datetime import datetime, timedelta
import time

class SectorDataManager:
    """板块数据管理器"""
    
    def __init__(self):
        self.cache_file = "sector_cache.json"
        self.stock_sector_file = "stock_sector_mapping.json"
        self.cache_duration = 3600  # 缓存1小时
        
    def get_sector_performance(self, use_cache=True):
        """获取板块涨幅数据，支持缓存"""
        try:
            # 检查缓存
            if use_cache and self._is_cache_valid():
                print("使用缓存的板块数据...")
                return self._load_cache()
            
            # 获取实时数据
            print("获取实时板块数据...")
            sector_data = self._fetch_real_sector_data()
            
            if sector_data:
                # 保存缓存
                self._save_cache(sector_data)
                return sector_data
            else:
                # 如果实时获取失败，尝试使用缓存
                if os.path.exists(self.cache_file):
                    print("实时数据获取失败，使用缓存数据...")
                    return self._load_cache()
                else:
                    raise Exception("无法获取板块数据且无可用缓存")
                    
        except Exception as e:
            print(f"获取板块数据失败: {e}")
            return {}
    
    def _fetch_real_sector_data(self):
        """获取真实的板块数据"""
        try:
            import akshare as ak
            
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    sector_df = ak.stock_board_industry_name_em()
                    
                    if not sector_df.empty:
                        sector_performance = {}
                        for _, row in sector_df.iterrows():
                            try:
                                sector_name = row['板块名称']
                                gain_str = str(row['涨跌幅']).replace('%', '')
                                gain_pct = float(gain_str)
                                sector_performance[sector_name] = gain_pct
                            except (ValueError, KeyError):
                                continue
                        
                        print(f"成功获取 {len(sector_performance)} 个板块数据")
                        return sector_performance
                    
                except Exception as e:
                    print(f"第 {attempt + 1} 次尝试失败: {e}")
                    if attempt < max_retries - 1:
                        time.sleep(3)
                    else:
                        raise e
            
            return {}
            
        except ImportError:
            raise Exception("AKShare不可用，无法获取真实板块数据")
        except Exception as e:
            print(f"获取实时板块数据失败: {e}")
            return {}
    
    def get_stock_sectors(self, code):
        """获取股票所属板块"""
        try:
            # 方法1: 从本地映射文件获取
            local_sectors = self._get_local_stock_sectors(code)
            if local_sectors:
                return local_sectors
            
            # 方法2: 从API获取
            api_sectors = self._fetch_stock_sectors_from_api(code)
            if api_sectors:
                # 保存到本地映射
                self._save_stock_sector_mapping(code, api_sectors)
                return api_sectors
            
            # 方法3: 从导出的概念板块文件获取
            file_sectors = self._get_sectors_from_export_file(code)
            if file_sectors:
                self._save_stock_sector_mapping(code, file_sectors)
                return file_sectors
            
            print(f"无法获取股票 {code} 的板块信息")
            return []
            
        except Exception as e:
            print(f"获取股票 {code} 板块信息失败: {e}")
            return []
    
    def _get_local_stock_sectors(self, code):
        """从本地映射文件获取股票板块"""
        try:
            if os.path.exists(self.stock_sector_file):
                with open(self.stock_sector_file, 'r', encoding='utf-8') as f:
                    mapping = json.load(f)
                    return mapping.get(code, [])
            return []
        except Exception as e:
            print(f"读取本地股票板块映射失败: {e}")
            return []
    
    def _fetch_stock_sectors_from_api(self, code):
        """从API获取股票板块信息"""
        try:
            import akshare as ak
            
            concept_df = ak.stock_individual_info_em(symbol=code)
            if not concept_df.empty:
                sectors = []
                for _, row in concept_df.iterrows():
                    item = row.get('item', '')
                    value = row.get('value', '')
                    if '概念' in item or '板块' in item or '行业' in item:
                        if value and value != '-':
                            concepts = value.replace('；', ';').split(';')
                            sectors.extend([c.strip() for c in concepts if c.strip()])
                
                return list(set(sectors))  # 去重
            return []
            
        except Exception as e:
            print(f"从API获取股票 {code} 板块信息失败: {e}")
            return []
    
    def _get_sectors_from_export_file(self, code):
        """从导出的概念板块文件获取"""
        try:
            concept_file = f"${code}/概念板块.csv"
            if os.path.exists(concept_file):
                concept_df = pd.read_csv(concept_file)
                if not concept_df.empty and '概念名称' in concept_df.columns:
                    sectors = concept_df['概念名称'].dropna().tolist()
                    print(f"从导出文件获取股票 {code} 板块信息: {sectors}")
                    return sectors
            return []
        except Exception as e:
            print(f"从导出文件获取板块信息失败: {e}")
            return []
    
    def _save_stock_sector_mapping(self, code, sectors):
        """保存股票板块映射"""
        try:
            mapping = {}
            if os.path.exists(self.stock_sector_file):
                with open(self.stock_sector_file, 'r', encoding='utf-8') as f:
                    mapping = json.load(f)
            
            mapping[code] = sectors
            
            with open(self.stock_sector_file, 'w', encoding='utf-8') as f:
                json.dump(mapping, f, ensure_ascii=False, indent=2)
                
            print(f"已保存股票 {code} 的板块映射")
            
        except Exception as e:
            print(f"保存股票板块映射失败: {e}")
    
    def _is_cache_valid(self):
        """检查缓存是否有效"""
        try:
            if not os.path.exists(self.cache_file):
                return False
            
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            
            cache_time = datetime.fromisoformat(cache_data.get('timestamp', ''))
            return datetime.now() - cache_time < timedelta(seconds=self.cache_duration)
            
        except Exception:
            return False
    
    def _load_cache(self):
        """加载缓存数据"""
        try:
            with open(self.cache_file, 'r', encoding='utf-8') as f:
                cache_data = json.load(f)
            return cache_data.get('data', {})
        except Exception as e:
            print(f"加载缓存失败: {e}")
            return {}
    
    def _save_cache(self, data):
        """保存缓存数据"""
        try:
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'data': data
            }
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存缓存失败: {e}")
    
    def update_stock_sector_mapping_from_exports(self, stock_codes):
        """从导出文件批量更新股票板块映射"""
        try:
            updated_count = 0
            
            for code in stock_codes:
                sectors = self._get_sectors_from_export_file(code)
                if sectors:
                    self._save_stock_sector_mapping(code, sectors)
                    updated_count += 1
            
            print(f"已更新 {updated_count} 只股票的板块映射")
            return updated_count
            
        except Exception as e:
            print(f"批量更新股票板块映射失败: {e}")
            return 0

# 全局实例
sector_manager = SectorDataManager()
