#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
模型预测器
使用训练好的模型对新股票进行预测
"""

import os
import sys
import pandas as pd
import numpy as np
import pickle
import joblib
import logging
from typing import Dict, Any, Optional, List
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ModelPredictor:
    """模型预测器"""
    
    def __init__(self, model_dir: str = "trained_models"):
        self.model_dir = model_dir
        self.models = {}
        self.feature_columns = []
        self.scaler = None
        
        # 加载训练好的模型
        self._load_models()
        
        logger.info("🤖 模型预测器初始化完成")
    
    def _load_models(self):
        """加载训练好的模型"""
        try:
            # 尝试多个可能的模型路径
            possible_paths = [
                os.path.join(os.path.dirname(__file__), self.model_dir),  # training/trained_models
                os.path.join(os.path.dirname(os.path.dirname(__file__)), self.model_dir),  # 项目根目录/trained_models
                self.model_dir  # 相对路径
            ]

            model_path = None
            for path in possible_paths:
                if os.path.exists(path):
                    model_path = path
                    break

            if not model_path:
                logger.warning(f"模型目录不存在，尝试的路径: {possible_paths}")
                return

            logger.info(f"✅ 找到模型目录: {model_path}")
            
            # 加载模型文件 - 支持 .joblib 和 .pkl 格式
            model_files = {
                'random_forest': ['random_forest_model.joblib', 'random_forest_model.pkl'],
                'gradient_boosting': ['gradient_boosting_model.joblib', 'gradient_boosting_model.pkl'],
                'logistic_regression': ['logistic_regression_model.joblib', 'logistic_regression_model.pkl']
            }

            loaded_count = 0
            for model_name, filenames in model_files.items():
                model_loaded = False
                for filename in filenames:
                    filepath = os.path.join(model_path, filename)
                    if os.path.exists(filepath):
                        try:
                            if filename.endswith('.joblib'):
                                self.models[model_name] = joblib.load(filepath)
                            else:
                                with open(filepath, 'rb') as f:
                                    self.models[model_name] = pickle.load(f)
                            loaded_count += 1
                            logger.info(f"✅ 加载模型: {model_name} (from {filename})")
                            model_loaded = True
                            break
                        except Exception as e:
                            logger.warning(f"加载模型{model_name}从{filename}失败: {e}")
                            continue

                if not model_loaded:
                    logger.warning(f"未找到模型文件: {model_name}")

            # 加载特征列信息 - 支持 feature_names.joblib 和 feature_columns.pkl
            feature_files = ['feature_names.joblib', 'feature_columns.pkl']
            for feature_filename in feature_files:
                feature_file = os.path.join(model_path, feature_filename)
                if os.path.exists(feature_file):
                    try:
                        if feature_filename.endswith('.joblib'):
                            self.feature_columns = joblib.load(feature_file)
                        else:
                            with open(feature_file, 'rb') as f:
                                self.feature_columns = pickle.load(f)
                        logger.info(f"✅ 加载特征列: {len(self.feature_columns)}个 (from {feature_filename})")
                        break
                    except Exception as e:
                        logger.warning(f"加载特征列从{feature_filename}失败: {e}")
                        continue

            # 加载标准化器 - 支持 scaler.joblib 和 feature_scaler.pkl
            scaler_files = ['scaler.joblib', 'feature_scaler.pkl']
            for scaler_filename in scaler_files:
                scaler_file = os.path.join(model_path, scaler_filename)
                if os.path.exists(scaler_file):
                    try:
                        if scaler_filename.endswith('.joblib'):
                            self.scaler = joblib.load(scaler_file)
                        else:
                            with open(scaler_file, 'rb') as f:
                                self.scaler = pickle.load(f)
                        logger.info(f"✅ 加载特征标准化器 (from {scaler_filename})")
                        break
                    except Exception as e:
                        logger.warning(f"加载标准化器从{scaler_filename}失败: {e}")
                        continue
            
            if loaded_count > 0:
                logger.info(f"✅ 成功加载{loaded_count}个模型")
            else:
                logger.warning("⚠️ 未找到可用的训练模型")
                
        except Exception as e:
            logger.error(f"加载模型失败: {e}")
    
    def predict_single_stock(self, features: Dict[str, Any]) -> Dict[str, Any]:
        """预测单只股票"""
        try:
            if not self.models:
                return {'success': False, 'error': '没有可用的训练模型'}
            
            if not self.feature_columns:
                return {'success': False, 'error': '没有特征列信息'}
            
            # 准备特征数据
            feature_vector = self._prepare_feature_vector(features)
            
            if feature_vector is None:
                return {'success': False, 'error': '特征准备失败'}
            
            # 使用所有模型进行预测
            predictions = {}
            prediction_scores = {}
            
            for model_name, model in self.models.items():
                try:
                    # 预测概率
                    if hasattr(model, 'predict_proba'):
                        proba = model.predict_proba(feature_vector.reshape(1, -1))[0]
                        prediction_score = proba[1] if len(proba) > 1 else proba[0]
                    else:
                        prediction_score = model.predict(feature_vector.reshape(1, -1))[0]
                    
                    # 预测标签
                    prediction_label = 1 if prediction_score > 0.5 else 0
                    
                    predictions[model_name] = prediction_label
                    prediction_scores[model_name] = float(prediction_score)
                    
                except Exception as e:
                    logger.warning(f"模型{model_name}预测失败: {e}")
                    continue
            
            if not predictions:
                return {'success': False, 'error': '所有模型预测失败'}
            
            # 集成预测结果
            ensemble_result = self._ensemble_predictions(predictions, prediction_scores)
            
            return {
                'success': True,
                'prediction_label': ensemble_result['label'],
                'prediction_score': ensemble_result['score'],
                'individual_predictions': predictions,
                'individual_scores': prediction_scores
            }
            
        except Exception as e:
            logger.error(f"预测失败: {e}")
            return {'success': False, 'error': str(e)}
    
    def _prepare_feature_vector(self, features: Dict[str, Any]) -> Optional[np.ndarray]:
        """准备特征向量"""
        try:
            # 创建特征向量
            feature_vector = np.zeros(len(self.feature_columns))
            
            for i, feature_name in enumerate(self.feature_columns):
                if feature_name in features:
                    value = features[feature_name]
                    if isinstance(value, (int, float)) and not (np.isnan(value) or np.isinf(value)):
                        feature_vector[i] = float(value)
                    else:
                        feature_vector[i] = 0.0
                else:
                    feature_vector[i] = 0.0
            
            # 标准化特征 - 检查维度匹配
            if self.scaler is not None:
                if len(feature_vector) == self.scaler.n_features_in_:
                    feature_vector = self.scaler.transform(feature_vector.reshape(1, -1))[0]
                else:
                    logger.warning(f"特征维度不匹配，跳过标准化。特征数量: {len(feature_vector)}, 标准化器期望: {self.scaler.n_features_in_}")
                    # 不使用标准化器，直接使用原始特征
            
            return feature_vector
            
        except Exception as e:
            logger.error(f"准备特征向量失败: {e}")
            return None
    
    def _ensemble_predictions(self, predictions: Dict[str, int], 
                            prediction_scores: Dict[str, float]) -> Dict[str, Any]:
        """集成多个模型的预测结果"""
        try:
            # 投票法确定最终标签
            positive_votes = sum(1 for pred in predictions.values() if pred == 1)
            total_votes = len(predictions)
            
            ensemble_label = 1 if positive_votes > total_votes / 2 else 0
            
            # 平均分数
            ensemble_score = np.mean(list(prediction_scores.values()))
            
            # 如果有随机森林模型，给予更高权重
            if 'random_forest' in prediction_scores:
                rf_score = prediction_scores['random_forest']
                ensemble_score = 0.5 * ensemble_score + 0.5 * rf_score
            
            return {
                'label': ensemble_label,
                'score': float(ensemble_score)
            }
            
        except Exception as e:
            logger.error(f"集成预测失败: {e}")
            return {'label': 0, 'score': 0.0}
    
    def predict_batch_stocks(self, stocks_features: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """批量预测股票"""
        try:
            results = []
            
            for i, features in enumerate(stocks_features):
                try:
                    stock_code = features.get('stock_code', f'stock_{i}')
                    
                    # 预测单只股票
                    prediction_result = self.predict_single_stock(features)
                    
                    if prediction_result.get('success', False):
                        result = {
                            'stock_code': stock_code,
                            'prediction_label': prediction_result['prediction_label'],
                            'prediction_score': prediction_result['prediction_score'],
                            'success': True
                        }
                    else:
                        result = {
                            'stock_code': stock_code,
                            'prediction_label': 0,
                            'prediction_score': 0.0,
                            'success': False,
                            'error': prediction_result.get('error', '预测失败')
                        }
                    
                    results.append(result)
                    
                except Exception as e:
                    logger.warning(f"预测第{i}只股票失败: {e}")
                    results.append({
                        'stock_code': f'stock_{i}',
                        'prediction_label': 0,
                        'prediction_score': 0.0,
                        'success': False,
                        'error': str(e)
                    })
            
            return results
            
        except Exception as e:
            logger.error(f"批量预测失败: {e}")
            return []
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        try:
            info = {
                'loaded_models': list(self.models.keys()),
                'feature_count': len(self.feature_columns),
                'has_scaler': self.scaler is not None,
                'feature_columns': self.feature_columns[:10] if self.feature_columns else []  # 只显示前10个特征
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取模型信息失败: {e}")
            return {}
    
    def is_ready(self) -> bool:
        """检查模型是否准备就绪"""
        return len(self.models) > 0 and len(self.feature_columns) > 0

def main():
    """测试模型预测器"""
    print("🧪 测试模型预测器...")
    
    predictor = ModelPredictor()
    
    # 检查模型状态
    print(f"模型准备状态: {predictor.is_ready()}")
    print(f"模型信息: {predictor.get_model_info()}")
    
    # 测试预测
    if predictor.is_ready():
        test_features = {
            'close_price': 15.5,
            'volume_ratio': 1.2,
            'price_change_5d': 0.05,
            'main_net_inflow': 1000000,
            'is_shenzhen': 1,
            'is_shanghai': 0
        }
        
        result = predictor.predict_single_stock(test_features)
        print(f"预测结果: {result}")
    else:
        print("⚠️ 模型未准备就绪，请先进行完整训练")

if __name__ == "__main__":
    main()
