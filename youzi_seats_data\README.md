# 游资席位数据文件夹

## 📁 文件夹说明

这个文件夹用于存储过去一年的游资席位数据，类似于 `limit_up_data` 文件夹的功能。

## 🗂️ 文件结构

```
youzi_seats_data/
├── README.md                    # 说明文档（本文件）
├── 20240710.json               # 2024年7月10日的游资席位数据
├── 20240711.json               # 2024年7月11日的游资席位数据
├── ...                         # 其他交易日的数据文件
├── download_status.json        # 下载状态记录（自动生成）
└── data_index.json            # 数据索引文件（自动生成）
```

## 📊 数据文件格式

每个 JSON 文件包含当日的完整游资席位数据：

```json
{
  "date": "20250710",
  "download_time": "2025-07-10T20:32:31.714967",
  "data_source": "akshare_lhb_seats",
  "summary": {
    "total_seats": 304,
    "famous_seats_count": 5,
    "market_sentiment": "散户主导"
  },
  "processed_data": {
    "total_seats": 304,
    "famous_seats_count": 5,
    "seats_detail": [...],
    "famous_seats": [...],
    "analysis_summary": {...}
  },
  "raw_data_count": 304
}
```

## 🎯 知名游资席位类型

系统自动识别以下类型的知名游资席位：

- **华鑫系**：华鑫证券上海分公司、宁波分公司等
- **中信系**：中信证券上海溧阳路、北京远大路等
- **华泰系**：华泰证券上海武定路、深圳益田路等
- **银河系**：银河证券绍兴等
- **光大系**：光大证券佛山绿景路等
- **国泰系**：国泰君安深圳益田路等
- **申万系**：申万宏源上海东川路等
- **海通系**：海通证券大连黄河路等
- **散户营业部**：东方财富拉萨等（用于风险识别）

## 🔄 自动管理功能

### 数据下载
- 每次启动主应用时自动检查数据完整性
- 自动下载缺失的交易日数据
- 支持后台下载，不影响主程序使用

### 数据维护
- 自动清理超过一年的过期数据
- 保持数据文件夹大小合理
- 错误数据自动重新下载

### 数据统计
- 游资活跃度统计
- 知名席位出现频次分析
- 市场情绪评估
- 个股游资操作历史查询

## 📈 使用方式

### 在主应用中使用
```python
# 主应用会自动初始化游资席位管理器
# 无需手动操作，数据会自动下载和更新

# 在调查潜在操作股功能中会自动使用这些数据
# 提供游资活跃度评分和风险评估
```

### 手动查询数据
```python
from youzi_seats_manager import YouziSeatsManager

manager = YouziSeatsManager()

# 获取游资活跃度统计
stats = manager.get_youzi_activity_statistics()

# 查询特定股票的游资操作历史
history = manager.get_stock_youzi_history('000001')

# 获取最近30天的游资活跃情况
recent = manager.get_recent_youzi_activity(days=30)
```

## 🚀 性能优化

- 使用 JSON 格式存储，读取速度快
- 按日期分文件存储，便于管理和查询
- 支持增量下载，避免重复获取数据
- 内存占用小，适合长期运行

## ⚠️ 注意事项

1. **数据来源**：主要使用 AKShare 的龙虎榜营业部数据
2. **更新频率**：每个交易日更新一次
3. **存储空间**：一年数据约占用 50-100MB 空间
4. **网络依赖**：需要网络连接下载数据
5. **API限制**：遵循 AKShare 的访问频率限制

## 🔧 故障排除

### 数据下载失败
- 检查网络连接
- 确认 AKShare 可用性
- 查看错误日志

### 数据不完整
- 运行测试程序检查状态
- 手动触发数据下载
- 检查磁盘空间

### 性能问题
- 清理过期数据文件
- 检查文件权限
- 重启主应用

## 📝 更新日志

- **2025-07-10**：创建游资席位数据管理系统
- **2025-07-10**：实现自动下载和数据分析功能
- **2025-07-10**：集成到主应用的调查潜在操作股功能

---

**注意**：此文件夹由系统自动管理，请勿手动修改数据文件。如需查询或分析数据，请使用提供的 API 接口。
