#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板潜力股深度学习器 - 简化版主界面
只包含两个核心功能：完整训练 和 调查潜在股
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import logging
import sys
import os
from datetime import datetime
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入核心模块
from training.training_pipeline import TrainingPipeline
from investigation.potential_stock_investigator import PotentialStockInvestigator

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimplifiedStockAnalyzer:
    """简化版连板潜力股分析器"""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("连板潜力股深度学习器 v2.0")
        self.root.geometry("1000x700")
        
        # 初始化核心组件
        self.training_pipeline = TrainingPipeline()
        self.investigator = PotentialStockInvestigator()
        
        # 界面状态
        self.training_in_progress = False
        self.investigation_in_progress = False
        
        # 创建界面
        self.create_widgets()
        
        logger.info("🖥️ 简化版连板潜力股分析器初始化完成")
    
    def create_widgets(self):
        """创建界面组件"""
        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="20")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(0, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="连板潜力股深度学习器 v2.0", 
                               font=("Arial", 18, "bold"))
        title_label.grid(row=0, column=0, pady=(0, 30))
        
        # 功能按钮区域
        self.create_function_buttons(main_frame)
        
        # 结果显示区域
        self.create_results_area(main_frame)
        
        # 状态栏
        self.create_status_bar(main_frame)
    
    def create_function_buttons(self, parent):
        """创建功能按钮"""
        button_frame = ttk.LabelFrame(parent, text="核心功能", padding="20")
        button_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), pady=(0, 20))
        button_frame.columnconfigure(0, weight=1)
        button_frame.columnconfigure(1, weight=1)
        
        # 完整训练按钮
        self.train_button = ttk.Button(
            button_frame, 
            text="🤖 完整训练", 
            command=self.start_training,
            style="Accent.TButton"
        )
        self.train_button.grid(row=0, column=0, padx=(0, 10), pady=10, sticky=(tk.W, tk.E))
        
        # 调查潜在股按钮
        self.investigate_button = ttk.Button(
            button_frame, 
            text="🔍 调查潜在股", 
            command=self.start_investigation,
            style="Accent.TButton"
        )
        self.investigate_button.grid(row=0, column=1, padx=(10, 0), pady=10, sticky=(tk.W, tk.E))
        
        # 功能说明
        desc_frame = ttk.Frame(button_frame)
        desc_frame.grid(row=1, column=0, columnspan=2, pady=(10, 0), sticky=(tk.W, tk.E))
        
        train_desc = ttk.Label(desc_frame, text="基于历史涨停股票训练预测模型", 
                              font=("Arial", 9), foreground="gray")
        train_desc.grid(row=0, column=0, sticky=tk.W)
        
        investigate_desc = ttk.Label(desc_frame, text="使用训练好的模型筛选潜在股票", 
                                   font=("Arial", 9), foreground="gray")
        investigate_desc.grid(row=0, column=1, sticky=tk.E)
    
    def create_results_area(self, parent):
        """创建结果显示区域"""
        results_frame = ttk.LabelFrame(parent, text="执行结果", padding="10")
        results_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(0, 10))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # 创建文本显示区域
        self.results_text = scrolledtext.ScrolledText(
            results_frame, 
            wrap=tk.WORD, 
            width=80, 
            height=25,
            font=("Consolas", 10)
        )
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加清空按钮
        clear_button = ttk.Button(results_frame, text="清空日志", command=self.clear_results)
        clear_button.grid(row=1, column=0, pady=(5, 0), sticky=tk.E)
    
    def create_status_bar(self, parent):
        """创建状态栏"""
        self.status_var = tk.StringVar()
        self.status_var.set("就绪")
        
        status_bar = ttk.Label(parent, textvariable=self.status_var, 
                              relief=tk.SUNKEN, anchor=tk.W)
        status_bar.grid(row=3, column=0, sticky=(tk.W, tk.E))
    
    def start_training(self):
        """开始完整训练"""
        if self.training_in_progress:
            messagebox.showwarning("警告", "训练正在进行中，请等待完成")
            return
        
        if self.investigation_in_progress:
            messagebox.showwarning("警告", "调查正在进行中，请等待完成")
            return
        
        # 确认对话框
        result = messagebox.askyesno(
            "确认训练", 
            "完整训练将：\n"
            "1. 从本地涨停文件筛选优质股票\n"
            "2. 下载历史数据进行特征工程\n"
            "3. 训练机器学习模型\n\n"
            "此过程可能需要10-30分钟，确认开始？"
        )
        
        if result:
            self.training_in_progress = True
            self.train_button.config(state="disabled")
            self.investigate_button.config(state="disabled")
            
            # 在新线程中执行训练
            training_thread = threading.Thread(target=self._run_training)
            training_thread.daemon = True
            training_thread.start()
    
    def start_investigation(self):
        """开始调查潜在股"""
        if self.investigation_in_progress:
            messagebox.showwarning("警告", "调查正在进行中，请等待完成")
            return
        
        if self.training_in_progress:
            messagebox.showwarning("警告", "训练正在进行中，请等待完成")
            return
        
        # 确认对话框
        result = messagebox.askyesno(
            "确认调查", 
            "调查潜在股将：\n"
            "1. 获取全市场股票数据\n"
            "2. 执行基础筛选（市值、板块等）\n"
            "3. 使用训练好的模型预测\n"
            "4. 导出结果到Excel文件\n\n"
            "此过程可能需要5-15分钟，确认开始？"
        )
        
        if result:
            self.investigation_in_progress = True
            self.train_button.config(state="disabled")
            self.investigate_button.config(state="disabled")
            
            # 在新线程中执行调查
            investigation_thread = threading.Thread(target=self._run_investigation)
            investigation_thread.daemon = True
            investigation_thread.start()
    
    def _run_training(self):
        """执行训练（在后台线程中）"""
        try:
            self.update_status("🤖 开始完整训练...")
            self.log_message("🚀 开始完整训练流程...")
            
            # 执行完整训练
            result = self.training_pipeline.run_full_training_pipeline()
            
            # 检查训练是否成功
            all_stages_success = all([
                result.get('data_collection', {}).get('success', False),
                result.get('feature_engineering', {}).get('success', False),
                result.get('data_preparation', {}).get('success', False),
                result.get('model_training', {}).get('success', False)
            ])

            if all_stages_success or result.get('overall_success', False) or result.get('success', False):
                self.log_message("✅ 完整训练成功完成！")
                self.log_message(f"📊 训练统计：")

                # 从各个阶段提取统计信息
                data_collection = result.get('data_collection', {})
                feature_engineering = result.get('feature_engineering', {})
                data_preparation = result.get('data_preparation', {})
                model_training = result.get('model_training', {})

                limit_up_stocks = data_collection.get('limit_up_stocks_found', 0)
                qualified_stocks = data_collection.get('qualified_stocks', 0)
                training_samples = data_preparation.get('total_samples', 0)
                feature_count = data_preparation.get('feature_count', 0)
                trained_models = len(model_training.get('trained_models', []))

                self.log_message(f"  - 涨停股票：{limit_up_stocks}只")
                self.log_message(f"  - 优质股票：{qualified_stocks}只")
                self.log_message(f"  - 训练样本：{training_samples}个")
                self.log_message(f"  - 特征数量：{feature_count}个")
                self.log_message(f"  - 训练模型：{trained_models}个")
                
                self.update_status("✅ 训练完成")
                messagebox.showinfo("训练完成", "模型训练成功完成！现在可以进行潜在股调查。")
            else:
                error_msg = result.get('error', '未知错误')
                self.log_message(f"❌ 训练失败：{error_msg}")
                self.update_status("❌ 训练失败")
                messagebox.showerror("训练失败", f"训练过程中出现错误：{error_msg}")
        
        except Exception as e:
            self.log_message(f"❌ 训练异常：{str(e)}")
            self.update_status("❌ 训练异常")
            messagebox.showerror("训练异常", f"训练过程中出现异常：{str(e)}")
        
        finally:
            self.training_in_progress = False
            self.train_button.config(state="normal")
            self.investigate_button.config(state="normal")
    
    def _run_investigation(self):
        """执行调查（在后台线程中）"""
        try:
            self.update_status("🔍 开始调查潜在股...")
            self.log_message("🚀 开始调查潜在股流程...")
            
            # 执行潜在股调查
            result = self.investigator.investigate_potential_stocks()
            
            if result.get('success', False):
                self.log_message("✅ 潜在股调查成功完成！")
                self.log_message(f"📊 调查统计：")
                self.log_message(f"  - 全市场股票：{result.get('total_stocks', 0)}只")
                self.log_message(f"  - 基础筛选通过：{result.get('basic_filtered', 0)}只")
                self.log_message(f"  - 模型预测通过：{result.get('model_filtered', 0)}只")
                self.log_message(f"  - 最终推荐：{result.get('final_recommendations', 0)}只")
                
                export_file = result.get('export_file', '')
                if export_file:
                    self.log_message(f"📄 结果已导出：{export_file}")
                
                self.update_status("✅ 调查完成")
                messagebox.showinfo("调查完成", f"潜在股调查完成！\n推荐股票：{result.get('final_recommendations', 0)}只\n结果已导出到Excel文件。")
            else:
                error_msg = result.get('error', '未知错误')
                self.log_message(f"❌ 调查失败：{error_msg}")
                self.update_status("❌ 调查失败")
                messagebox.showerror("调查失败", f"调查过程中出现错误：{error_msg}")
        
        except Exception as e:
            self.log_message(f"❌ 调查异常：{str(e)}")
            self.update_status("❌ 调查异常")
            messagebox.showerror("调查异常", f"调查过程中出现异常：{str(e)}")
        
        finally:
            self.investigation_in_progress = False
            self.train_button.config(state="normal")
            self.investigate_button.config(state="normal")
    
    def log_message(self, message):
        """添加日志消息"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        formatted_message = f"[{timestamp}] {message}\n"
        
        # 在主线程中更新UI
        self.root.after(0, lambda: self._append_to_results(formatted_message))
    
    def _append_to_results(self, message):
        """在结果区域添加消息"""
        self.results_text.insert(tk.END, message)
        self.results_text.see(tk.END)
    
    def update_status(self, status):
        """更新状态栏"""
        self.root.after(0, lambda: self.status_var.set(status))
    
    def clear_results(self):
        """清空结果显示"""
        self.results_text.delete(1.0, tk.END)
    
    def run(self):
        """运行应用"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            logger.info("用户中断程序")
        finally:
            # 清理资源
            if hasattr(self.training_pipeline, 'close'):
                self.training_pipeline.close()

def main():
    """主函数"""
    try:
        app = SimplifiedStockAnalyzer()
        app.run()
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        messagebox.showerror("启动失败", f"应用启动失败：{str(e)}")

if __name__ == "__main__":
    main()
