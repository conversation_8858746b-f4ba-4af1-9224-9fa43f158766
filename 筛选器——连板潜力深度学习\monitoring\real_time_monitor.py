#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时监控系统
建立实时监控系统，能够在交易时间实时扫描全市场股票，识别潜在连板机会
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta, time
import logging
from typing import Dict, List, Any, Optional, Tuple
import os
import sys
import time as time_module
import threading
import json
import warnings
warnings.filterwarnings('ignore')

# 添加项目路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 导入项目模块
from algorithms.multi_layer_filter import MultiLayerFilter
from data_layer.historical_data_fetcher import HistoricalDataFetcher
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RealTimeMonitor:
    """实时监控系统"""
    
    def __init__(self, config: Dict[str, Any] = None):
        self.config = config or self._get_default_config()
        
        # 初始化组件
        self.filter_engine = MultiLayerFilter()
        self.data_fetcher = HistoricalDataFetcher()
        self.data_storage = HistoricalDataStorage()
        
        # 监控状态
        self.is_monitoring = False
        self.monitoring_thread = None
        self.last_scan_time = None
        self.scan_results = []
        self.alert_history = []
        
        # 交易时间配置
        self.trading_hours = {
            'morning_start': time(9, 30),
            'morning_end': time(11, 30),
            'afternoon_start': time(13, 0),
            'afternoon_end': time(15, 0)
        }
        
        logger.info("📡 实时监控系统初始化完成")
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'scan_interval_seconds': 300,      # 扫描间隔5分钟
            'max_alerts_per_scan': 5,          # 每次扫描最大预警数量
            'alert_threshold_score': 0.7,     # 预警阈值评分
            'alert_threshold_probability': 0.6, # 预警阈值概率
            'enable_trading_hours_only': True, # 仅在交易时间监控
            'enable_sound_alert': False,       # 声音预警
            'enable_email_alert': False,       # 邮件预警
            'save_scan_results': True,         # 保存扫描结果
            'max_history_days': 7,             # 最大历史记录天数
            'stock_universe_size': 100,        # 监控股票池大小
            'priority_stocks': [],             # 优先监控股票列表
            'blacklist_stocks': []             # 黑名单股票列表
        }
    
    def start_monitoring(self) -> bool:
        """开始实时监控"""
        try:
            if self.is_monitoring:
                logger.warning("⚠️ 监控系统已在运行中")
                return False
            
            logger.info("🚀 启动实时监控系统...")
            
            # 检查交易时间
            if self.config['enable_trading_hours_only'] and not self._is_trading_time():
                logger.info("📅 当前非交易时间，监控系统将在下个交易时段自动启动")
            
            self.is_monitoring = True
            
            # 启动监控线程
            self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
            self.monitoring_thread.start()
            
            logger.info("✅ 实时监控系统启动成功")
            return True
            
        except Exception as e:
            logger.error(f"启动监控系统失败: {e}")
            return False
    
    def stop_monitoring(self) -> bool:
        """停止实时监控"""
        try:
            if not self.is_monitoring:
                logger.warning("⚠️ 监控系统未在运行")
                return False
            
            logger.info("🛑 停止实时监控系统...")
            
            self.is_monitoring = False
            
            # 等待监控线程结束
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=10)
            
            logger.info("✅ 实时监控系统已停止")
            return True
            
        except Exception as e:
            logger.error(f"停止监控系统失败: {e}")
            return False
    
    def _monitoring_loop(self):
        """监控主循环"""
        try:
            logger.info("🔄 监控主循环开始...")
            
            while self.is_monitoring:
                try:
                    # 检查是否为交易时间
                    if self.config['enable_trading_hours_only'] and not self._is_trading_time():
                        logger.info("📅 非交易时间，等待下个扫描周期...")
                        time_module.sleep(60)  # 非交易时间每分钟检查一次
                        continue
                    
                    # 执行扫描
                    scan_start_time = datetime.now()
                    logger.info(f"🔍 开始扫描 - {scan_start_time.strftime('%H:%M:%S')}")
                    
                    scan_results = self._perform_scan()
                    
                    scan_end_time = datetime.now()
                    scan_duration = (scan_end_time - scan_start_time).total_seconds()
                    
                    logger.info(f"✅ 扫描完成 - 耗时{scan_duration:.1f}秒，发现{len(scan_results.get('alerts', []))}个预警")
                    
                    # 处理扫描结果
                    self._process_scan_results(scan_results)
                    
                    # 更新最后扫描时间
                    self.last_scan_time = scan_end_time
                    
                    # 等待下次扫描
                    time_module.sleep(self.config['scan_interval_seconds'])
                    
                except Exception as e:
                    logger.error(f"监控循环错误: {e}")
                    time_module.sleep(30)  # 出错后等待30秒再继续
                    continue
            
            logger.info("🔄 监控主循环结束")
            
        except Exception as e:
            logger.error(f"监控主循环失败: {e}")
    
    def _is_trading_time(self) -> bool:
        """判断是否为交易时间"""
        try:
            now = datetime.now()
            current_time = now.time()
            current_weekday = now.weekday()
            
            # 检查是否为工作日（周一到周五）
            if current_weekday >= 5:  # 周六、周日
                return False
            
            # 检查是否在交易时间段内
            morning_trading = (self.trading_hours['morning_start'] <= current_time <= self.trading_hours['morning_end'])
            afternoon_trading = (self.trading_hours['afternoon_start'] <= current_time <= self.trading_hours['afternoon_end'])
            
            return morning_trading or afternoon_trading
            
        except Exception as e:
            logger.error(f"判断交易时间失败: {e}")
            return False
    
    def _perform_scan(self) -> Dict[str, Any]:
        """执行扫描"""
        try:
            scan_results = {
                'scan_time': datetime.now().isoformat(),
                'alerts': [],
                'scan_stats': {},
                'error': None
            }
            
            # 获取监控股票池
            stock_universe = self._get_monitoring_universe()
            
            if not stock_universe:
                scan_results['error'] = "无法获取监控股票池"
                return scan_results
            
            logger.info(f"📊 监控股票池: {len(stock_universe)}只股票")
            
            # 执行多层筛选
            filtering_results = self.filter_engine.run_multi_layer_filtering(stock_universe)
            
            if not filtering_results.get('success', False):
                scan_results['error'] = f"多层筛选失败: {filtering_results.get('error', 'Unknown error')}"
                return scan_results
            
            # 提取潜在预警股票
            potential_alerts = filtering_results.get('final_stocks', [])
            
            # 生成预警
            alerts = self._generate_alerts(potential_alerts)
            
            scan_results.update({
                'alerts': alerts,
                'scan_stats': {
                    'total_scanned': len(stock_universe),
                    'alerts_generated': len(alerts),
                    'filtering_summary': filtering_results.get('filtering_summary', {})
                }
            })
            
            return scan_results
            
        except Exception as e:
            logger.error(f"执行扫描失败: {e}")
            return {
                'scan_time': datetime.now().isoformat(),
                'alerts': [],
                'scan_stats': {},
                'error': str(e)
            }
    
    def _get_monitoring_universe(self) -> List[str]:
        """获取监控股票池"""
        try:
            # 优先监控股票
            priority_stocks = self.config.get('priority_stocks', [])
            
            # 生成基础股票池
            base_universe = []
            
            # 模拟股票池（实际应用中应从数据源获取）
            for i in range(1, self.config['stock_universe_size'] + 1):
                if i <= 50:
                    stock_code = f"00{i:04d}"
                else:
                    stock_code = f"60{i:04d}"
                
                # 排除黑名单股票
                if stock_code not in self.config.get('blacklist_stocks', []):
                    base_universe.append(stock_code)
            
            # 合并优先股票和基础股票池
            monitoring_universe = list(set(priority_stocks + base_universe))
            
            return monitoring_universe
            
        except Exception as e:
            logger.error(f"获取监控股票池失败: {e}")
            return []
    
    def _generate_alerts(self, potential_stocks: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """生成预警"""
        try:
            alerts = []
            
            for stock in potential_stocks:
                try:
                    # 检查预警条件
                    final_score = stock.get('final_score', 0)
                    ml_probability = stock.get('ml_probability', 0)
                    
                    # 预警阈值检查
                    score_threshold = self.config['alert_threshold_score']
                    prob_threshold = self.config['alert_threshold_probability']
                    
                    if final_score >= score_threshold or ml_probability >= prob_threshold:
                        alert = self._create_alert(stock)
                        alerts.append(alert)
                
                except Exception as e:
                    logger.warning(f"生成股票{stock.get('stock_code', 'unknown')}预警失败: {e}")
                    continue
            
            # 按评分排序，取前N个
            alerts.sort(key=lambda x: x['alert_score'], reverse=True)
            max_alerts = self.config['max_alerts_per_scan']
            
            return alerts[:max_alerts]
            
        except Exception as e:
            logger.error(f"生成预警失败: {e}")
            return []
    
    def _create_alert(self, stock: Dict[str, Any]) -> Dict[str, Any]:
        """创建预警"""
        try:
            alert = {
                'alert_id': f"ALERT_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{stock.get('stock_code', '')}",
                'stock_code': stock.get('stock_code', ''),
                'stock_name': stock.get('stock_name', ''),
                'alert_time': datetime.now().isoformat(),
                'alert_type': 'LIMIT_UP_POTENTIAL',
                'alert_score': stock.get('final_score', 0),
                'ml_probability': stock.get('ml_probability', 0),
                'timing_score': stock.get('timing_score', 0),
                'technical_score': stock.get('technical_score', 0),
                'capital_score': stock.get('capital_score', 0),
                'basic_score': stock.get('basic_score', 0),
                'market_cap': stock.get('market_cap', 0),
                'price': stock.get('price', 0),
                'turnover_rate': stock.get('turnover_rate', 0),
                'alert_reason': self._generate_alert_reason(stock),
                'confidence_level': self._calculate_confidence_level(stock),
                'recommended_action': self._get_recommended_action(stock)
            }
            
            return alert
            
        except Exception as e:
            logger.error(f"创建预警失败: {e}")
            return {}
    
    def _generate_alert_reason(self, stock: Dict[str, Any]) -> str:
        """生成预警原因"""
        try:
            reasons = []
            
            # 检查各项评分
            if stock.get('ml_probability', 0) >= 0.7:
                reasons.append("机器学习预测概率高")
            
            if stock.get('timing_score', 0) >= 0.7:
                reasons.append("时机信号强烈")
            
            if stock.get('technical_score', 0) >= 0.6:
                reasons.append("技术指标良好")
            
            if stock.get('capital_score', 0) >= 0.5:
                reasons.append("资金流向积极")
            
            # 检查核心特征
            comprehensive_features = stock.get('comprehensive_features', {})
            if comprehensive_features.get('morning_volume_active', 0) > 0:
                reasons.append("早盘成交活跃")
            
            if comprehensive_features.get('near_recent_high', 0) > 0:
                reasons.append("接近近期高点")
            
            return "; ".join(reasons) if reasons else "综合评分达到预警阈值"
            
        except Exception as e:
            logger.error(f"生成预警原因失败: {e}")
            return "系统预警"
    
    def _calculate_confidence_level(self, stock: Dict[str, Any]) -> str:
        """计算置信度等级"""
        try:
            final_score = stock.get('final_score', 0)
            ml_probability = stock.get('ml_probability', 0)
            
            # 综合评分
            combined_score = (final_score + ml_probability) / 2
            
            if combined_score >= 0.8:
                return "HIGH"
            elif combined_score >= 0.6:
                return "MEDIUM"
            else:
                return "LOW"
                
        except Exception as e:
            logger.error(f"计算置信度等级失败: {e}")
            return "UNKNOWN"
    
    def _get_recommended_action(self, stock: Dict[str, Any]) -> str:
        """获取推荐操作"""
        try:
            confidence = self._calculate_confidence_level(stock)
            ml_probability = stock.get('ml_probability', 0)
            
            if confidence == "HIGH" and ml_probability >= 0.8:
                return "强烈关注，考虑建仓"
            elif confidence == "MEDIUM" and ml_probability >= 0.6:
                return "密切关注，等待确认信号"
            else:
                return "一般关注，观察后续走势"
                
        except Exception as e:
            logger.error(f"获取推荐操作失败: {e}")
            return "关注观察"

    def _process_scan_results(self, scan_results: Dict[str, Any]):
        """处理扫描结果"""
        try:
            # 保存扫描结果
            if self.config['save_scan_results']:
                self.scan_results.append(scan_results)

                # 限制历史记录数量
                max_records = self.config['max_history_days'] * 24 * 60 // (self.config['scan_interval_seconds'] // 60)
                if len(self.scan_results) > max_records:
                    self.scan_results = self.scan_results[-max_records:]

            # 处理预警
            alerts = scan_results.get('alerts', [])
            if alerts:
                for alert in alerts:
                    self._process_alert(alert)

            # 保存到文件
            self._save_scan_results_to_file(scan_results)

        except Exception as e:
            logger.error(f"处理扫描结果失败: {e}")

    def _process_alert(self, alert: Dict[str, Any]):
        """处理单个预警"""
        try:
            # 添加到预警历史
            self.alert_history.append(alert)

            # 限制预警历史数量
            max_alert_history = 1000
            if len(self.alert_history) > max_alert_history:
                self.alert_history = self.alert_history[-max_alert_history:]

            # 输出预警信息
            self._output_alert(alert)

            # 发送预警通知
            if self.config['enable_sound_alert']:
                self._send_sound_alert(alert)

            if self.config['enable_email_alert']:
                self._send_email_alert(alert)

        except Exception as e:
            logger.error(f"处理预警失败: {e}")

    def _output_alert(self, alert: Dict[str, Any]):
        """输出预警信息"""
        try:
            stock_code = alert.get('stock_code', '')
            stock_name = alert.get('stock_name', '')
            alert_score = alert.get('alert_score', 0)
            ml_probability = alert.get('ml_probability', 0)
            confidence = alert.get('confidence_level', '')
            reason = alert.get('alert_reason', '')
            action = alert.get('recommended_action', '')

            logger.info("🚨 " + "="*60)
            logger.info(f"🚨 连板潜力股预警")
            logger.info(f"🚨 股票代码: {stock_code}")
            logger.info(f"🚨 股票名称: {stock_name}")
            logger.info(f"🚨 综合评分: {alert_score:.3f}")
            logger.info(f"🚨 ML概率: {ml_probability:.3f}")
            logger.info(f"🚨 置信度: {confidence}")
            logger.info(f"🚨 预警原因: {reason}")
            logger.info(f"🚨 推荐操作: {action}")
            logger.info("🚨 " + "="*60)

        except Exception as e:
            logger.error(f"输出预警信息失败: {e}")

    def _send_sound_alert(self, alert: Dict[str, Any]):
        """发送声音预警"""
        try:
            # 这里可以集成声音播放功能
            # 例如使用 winsound (Windows) 或 playsound 库
            logger.info(f"🔊 声音预警: {alert.get('stock_code', '')} {alert.get('stock_name', '')}")

        except Exception as e:
            logger.error(f"发送声音预警失败: {e}")

    def _send_email_alert(self, alert: Dict[str, Any]):
        """发送邮件预警"""
        try:
            # 这里可以集成邮件发送功能
            # 例如使用 smtplib 发送邮件
            logger.info(f"📧 邮件预警: {alert.get('stock_code', '')} {alert.get('stock_name', '')}")

        except Exception as e:
            logger.error(f"发送邮件预警失败: {e}")

    def _save_scan_results_to_file(self, scan_results: Dict[str, Any]):
        """保存扫描结果到文件"""
        try:
            # 创建结果目录
            results_dir = "monitoring_results"
            os.makedirs(results_dir, exist_ok=True)

            # 按日期保存
            date_str = datetime.now().strftime('%Y%m%d')
            results_file = os.path.join(results_dir, f"scan_results_{date_str}.json")

            # 读取现有结果
            existing_results = []
            if os.path.exists(results_file):
                try:
                    with open(results_file, 'r', encoding='utf-8') as f:
                        existing_results = json.load(f)
                except:
                    existing_results = []

            # 添加新结果
            existing_results.append(scan_results)

            # 保存到文件
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(existing_results, f, ensure_ascii=False, indent=2, default=str)

        except Exception as e:
            logger.error(f"保存扫描结果到文件失败: {e}")

    def get_monitoring_status(self) -> Dict[str, Any]:
        """获取监控状态"""
        try:
            status = {
                'is_monitoring': self.is_monitoring,
                'last_scan_time': self.last_scan_time.isoformat() if self.last_scan_time else None,
                'total_scans': len(self.scan_results),
                'total_alerts': len(self.alert_history),
                'is_trading_time': self._is_trading_time(),
                'config': self.config,
                'recent_alerts': self.alert_history[-10:] if self.alert_history else []
            }

            return status

        except Exception as e:
            logger.error(f"获取监控状态失败: {e}")
            return {'error': str(e)}

    def get_recent_alerts(self, hours: int = 24) -> List[Dict[str, Any]]:
        """获取最近的预警"""
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours)

            recent_alerts = []
            for alert in self.alert_history:
                try:
                    alert_time = datetime.fromisoformat(alert.get('alert_time', ''))
                    if alert_time >= cutoff_time:
                        recent_alerts.append(alert)
                except:
                    continue

            # 按时间倒序排列
            recent_alerts.sort(key=lambda x: x.get('alert_time', ''), reverse=True)

            return recent_alerts

        except Exception as e:
            logger.error(f"获取最近预警失败: {e}")
            return []

    def get_scan_statistics(self) -> Dict[str, Any]:
        """获取扫描统计"""
        try:
            if not self.scan_results:
                return {'error': '没有扫描结果'}

            # 统计信息
            total_scans = len(self.scan_results)
            total_alerts = sum(len(scan.get('alerts', [])) for scan in self.scan_results)

            # 最近24小时统计
            cutoff_time = datetime.now() - timedelta(hours=24)
            recent_scans = []
            recent_alerts = 0

            for scan in self.scan_results:
                try:
                    scan_time = datetime.fromisoformat(scan.get('scan_time', ''))
                    if scan_time >= cutoff_time:
                        recent_scans.append(scan)
                        recent_alerts += len(scan.get('alerts', []))
                except:
                    continue

            # 平均扫描时间
            avg_scan_interval = self.config['scan_interval_seconds']

            statistics = {
                'total_scans': total_scans,
                'total_alerts': total_alerts,
                'recent_24h_scans': len(recent_scans),
                'recent_24h_alerts': recent_alerts,
                'avg_alerts_per_scan': total_alerts / total_scans if total_scans > 0 else 0,
                'avg_scan_interval_seconds': avg_scan_interval,
                'monitoring_uptime_hours': len(self.scan_results) * avg_scan_interval / 3600
            }

            return statistics

        except Exception as e:
            logger.error(f"获取扫描统计失败: {e}")
            return {'error': str(e)}

    def export_monitoring_report(self, output_file: str = None) -> str:
        """导出监控报告"""
        try:
            if output_file is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                output_file = f"monitoring_report_{timestamp}.json"

            # 生成报告
            report = {
                'report_time': datetime.now().isoformat(),
                'monitoring_status': self.get_monitoring_status(),
                'scan_statistics': self.get_scan_statistics(),
                'recent_alerts': self.get_recent_alerts(24),
                'scan_results': self.scan_results[-50:] if self.scan_results else [],  # 最近50次扫描
                'alert_history': self.alert_history[-100:] if self.alert_history else []  # 最近100个预警
            }

            # 保存报告
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(report, f, ensure_ascii=False, indent=2, default=str)

            logger.info(f"✅ 监控报告已导出: {output_file}")
            return output_file

        except Exception as e:
            logger.error(f"导出监控报告失败: {e}")
            return ""

    def close(self):
        """关闭监控系统"""
        try:
            self.stop_monitoring()
            self.data_storage.close()
            logger.info("✅ 监控系统资源已关闭")
        except Exception as e:
            logger.error(f"关闭监控系统失败: {e}")

def main():
    """测试实时监控系统"""
    print("📡 测试实时监控系统...")

    # 创建监控系统
    monitor = RealTimeMonitor()

    # 获取监控状态
    print("\n📊 监控状态:")
    status = monitor.get_monitoring_status()
    print(f"  监控状态: {'运行中' if status.get('is_monitoring', False) else '未运行'}")
    print(f"  交易时间: {'是' if status.get('is_trading_time', False) else '否'}")
    print(f"  扫描间隔: {status.get('config', {}).get('scan_interval_seconds', 0)}秒")

    # 执行一次扫描测试
    print("\n🔍 执行测试扫描...")
    scan_results = monitor._perform_scan()

    if scan_results.get('error'):
        print(f"❌ 扫描失败: {scan_results['error']}")
    else:
        alerts = scan_results.get('alerts', [])
        scan_stats = scan_results.get('scan_stats', {})

        print(f"✅ 扫描成功:")
        print(f"  扫描股票数: {scan_stats.get('total_scanned', 0)}")
        print(f"  生成预警数: {scan_stats.get('alerts_generated', 0)}")

        if alerts:
            print(f"\n🚨 预警股票:")
            for i, alert in enumerate(alerts, 1):
                print(f"  {i}. {alert.get('stock_code', '')} {alert.get('stock_name', '')}")
                print(f"     评分: {alert.get('alert_score', 0):.3f}")
                print(f"     概率: {alert.get('ml_probability', 0):.3f}")
                print(f"     置信度: {alert.get('confidence_level', '')}")

    # 导出测试报告
    print("\n📄 导出测试报告...")
    report_file = monitor.export_monitoring_report()
    if report_file:
        print(f"✅ 报告已保存: {report_file}")

    # 关闭系统
    monitor.close()

if __name__ == "__main__":
    main()
