#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试K线数据存储是否正常工作
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_layer.efinance_data_fetcher import EfinanceDataFetcher
from data_layer.historical_data_storage import HistoricalDataStorage

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_real_kline_data_storage():
    """测试真实K线数据的获取和存储"""
    print("🧪 测试真实K线数据的获取和存储...")
    
    # 创建数据获取器和存储器
    fetcher = EfinanceDataFetcher()
    storage = HistoricalDataStorage()
    
    # 测试股票
    test_stock = "000001"  # 平安银行
    
    # 获取最近30天的K线数据
    end_date = datetime.now()
    start_date = end_date - timedelta(days=30)
    
    print(f"📊 测试股票: {test_stock}")
    print(f"📅 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
    
    try:
        # 1. 获取K线数据
        print("\n1️⃣ 获取K线数据...")
        kline_data = fetcher.get_stock_kline_data(
            stock_code=test_stock,
            days=30
        )
        
        if not kline_data.empty:
            print(f"✅ K线数据获取成功: {len(kline_data)}条记录")
            print(f"   数据列: {list(kline_data.columns)}")
            print(f"   数据类型:\n{kline_data.dtypes}")
            print(f"   样本数据:\n{kline_data.head(2)}")
            
            # 2. 存储K线数据
            print("\n2️⃣ 存储K线数据...")
            stored_count = storage.store_kline_data(test_stock, kline_data)
            print(f"✅ K线数据存储成功: {stored_count}条记录")
            
            if stored_count > 0:
                print("🎉 K线数据获取和存储都正常工作！")
                
                # 3. 验证存储的数据
                print("\n3️⃣ 验证存储的数据...")
                stored_data = storage.get_kline_data(test_stock)
                print(f"✅ 从数据库读取: {len(stored_data)}条记录")
                
                if not stored_data.empty:
                    print(f"   存储的数据样本:\n{stored_data.head(2)}")
                    return True
                else:
                    print("❌ 数据库中没有找到存储的数据")
                    return False
            else:
                print("❌ K线数据存储失败")
                return False
        else:
            print("❌ K线数据获取失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_negative_sample_creation():
    """测试负样本创建逻辑"""
    print("\n🧪 测试负样本创建逻辑...")
    
    storage = HistoricalDataStorage()
    
    try:
        # 查看当前特征数据的标签分布
        print("📊 当前标签分布:")
        import sqlite3
        conn = sqlite3.connect('historical_data.db')
        
        label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features GROUP BY label', conn)
        print(f"   标签分布:\n{label_dist}")
        
        total_samples = label_dist['count'].sum()
        positive_samples = label_dist[label_dist['label'] == 1]['count'].sum() if not label_dist[label_dist['label'] == 1].empty else 0
        negative_samples = label_dist[label_dist['label'] == 0]['count'].sum() if not label_dist[label_dist['label'] == 0].empty else 0
        
        print(f"   总样本数: {total_samples}")
        print(f"   正样本数: {positive_samples}")
        print(f"   负样本数: {negative_samples}")
        print(f"   正样本比例: {positive_samples/total_samples*100:.2f}%")
        
        conn.close()
        
        if negative_samples > 0:
            print("✅ 已有负样本，负样本创建逻辑可能正常工作")
            return True
        else:
            print("⚠️ 没有负样本，需要重新运行训练来创建负样本")
            return False
            
    except Exception as e:
        print(f"❌ 检查负样本失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 K线数据存储测试开始...")
    print("="*60)
    
    # 测试真实K线数据存储
    kline_test_result = test_real_kline_data_storage()
    
    # 测试负样本创建
    negative_test_result = test_negative_sample_creation()
    
    print("\n" + "="*60)
    print("📋 测试结果总结:")
    print(f"   K线数据存储: {'✅ 正常' if kline_test_result else '❌ 异常'}")
    print(f"   负样本创建: {'✅ 正常' if negative_test_result else '⚠️ 需要重新训练'}")
    
    if kline_test_result:
        print("\n💡 建议:")
        print("   1. K线数据存储已修复，可以重新运行完整训练")
        print("   2. 重新训练后应该会生成负样本")
        print("   3. 训练完成后检查是否有正负样本平衡")
    else:
        print("\n⚠️ 警告:")
        print("   K线数据存储仍有问题，需要进一步调试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
