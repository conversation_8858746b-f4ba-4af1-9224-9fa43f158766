#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游资洗盘行为量化捕手
基于三维共振模型的全A股游资洗盘痕迹识别系统
不依赖龙虎榜数据，仅使用基础K线数据进行游资行为识别
"""

import os
import json
import pandas as pd
import numpy as np
import requests
import time
import re
from datetime import datetime, timedelta
from collections import defaultdict
import warnings
warnings.filterwarnings('ignore')

class GameCapitalWashingDetector:
    """游资洗盘行为量化捕手"""
    
    def __init__(self):
        # 修改为指向上级目录的路径
        self.limit_up_data_dir = os.path.join("..", "limit_up_data")
        self.current_date = datetime.now()
        self.akshare_available = True
        
        # 三维共振模型参数
        self.config = {
            'min_limit_up_count': 3,        # 最小涨停次数（活跃股基因）
            'exclude_recent_days': 20,      # 排除近N日内有涨停的股票
            'kline_days': 60,              # K线数据天数
            'volume_shrink_threshold': 0.4, # 量能萎缩阈值
            'anti_fall_ratio': 0.6,        # 抗跌比例阈值
            'doji_amplitude_threshold': 3.0, # 十字星振幅阈值
            'comprehensive_score_threshold': 60, # 综合评分阈值
            'batch_size': 50               # 批量处理大小
        }
        
        print("✅ 游资洗盘行为量化捕手初始化完成")
        print(f"📊 配置参数: 涨停次数≥{self.config['min_limit_up_count']}, 排除近{self.config['exclude_recent_days']}日涨停")
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("🚀 开始游资洗盘行为量化分析...")
        print("=" * 80)
        
        try:
            # 第一步：获取全市场数据并基础筛选
            print("\n📊 第一步：获取全市场数据并基础筛选...")
            market_candidates = self._get_market_candidates()
            
            if not market_candidates:
                print("❌ 基础筛选后无候选股票")
                return []
            
            print(f"✅ 基础筛选完成，获得 {len(market_candidates)} 只候选股票")
            
            # 第二步：活跃股基因筛选（涨停次数）
            print("\n🧬 第二步：活跃股基因筛选...")
            active_candidates = self._filter_active_stocks(market_candidates)
            
            if not active_candidates:
                print("❌ 活跃股筛选后无候选股票")
                return []
            
            print(f"✅ 活跃股筛选完成，剩余 {len(active_candidates)} 只股票")
            
            # 第三步：三维共振检测
            print("\n🎯 第三步：三维共振检测...")
            final_candidates = self._three_dimension_resonance_detection(active_candidates)
            
            print(f"✅ 三维共振检测完成，发现 {len(final_candidates)} 只洗盘股票")
            
            # 第四步：结果展示和导出
            self._display_results(final_candidates)
            self._export_results(final_candidates)
            
            return final_candidates
            
        except Exception as e:
            print(f"❌ 分析流程失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def _get_market_candidates(self):
        """获取全市场数据并进行基础筛选"""
        try:
            print("📡 获取全市场股票数据...")
            
            # 使用新浪财经API获取全市场数据
            market_data = self._get_sina_market_data()
            
            if not market_data or len(market_data) == 0:
                print("❌ 无法获取市场数据")
                return []
            
            print(f"✅ 获取全市场数据: {len(market_data)} 只股票")
            
            # 基础筛选：排除ST、创业板、科创板、北交所
            filtered_candidates = []
            
            for stock in market_data:
                try:
                    code = stock.get('代码', '') or stock.get('symbol', '')
                    name = stock.get('名称', '') or stock.get('name', '')
                    
                    # 标准化股票代码
                    code = self._normalize_stock_code(code)
                    
                    # 基础筛选
                    if self._check_market_scope(code, name):
                        # 获取市值（亿元）
                        market_cap = self._get_market_cap(stock)
                        
                        candidate = {
                            'code': code,
                            'name': name,
                            'market_cap': market_cap,
                            'latest_price': stock.get('最新价', 0) or stock.get('trade', 0),
                            'change_pct': stock.get('涨跌幅', 0) or stock.get('changepercent', 0),
                            'volume': stock.get('成交量', 0) or stock.get('volume', 0),
                            'turnover_rate': stock.get('换手率', 0) or stock.get('turnoverratio', 0),
                            'original_data': stock
                        }
                        filtered_candidates.append(candidate)
                        
                except Exception as e:
                    continue
            
            print(f"✅ 基础筛选完成: {len(market_data)} → {len(filtered_candidates)} 只股票")
            print(f"   排除: ST股票、创业板(300)、科创板(688)、北交所(8/4)")
            
            return filtered_candidates
            
        except Exception as e:
            print(f"❌ 获取市场候选股票失败: {e}")
            return []
    
    def _get_sina_market_data(self):
        """从新浪财经获取全市场数据（带重试机制）"""
        max_retries = 2  # 最多重试2次

        for attempt in range(max_retries + 1):
            try:
                print(f"📡 从新浪财经获取全市场数据... (尝试 {attempt + 1}/{max_retries + 1})")

                all_data = []
                page = 1
                max_pages = 60  # 最多获取60页

                while page <= max_pages:
                    page_data = self._get_sina_page_data_with_retry(page)

                    if page_data is None:
                        print(f"⚠️ 第 {page} 页获取失败，停止获取")
                        break
                    elif len(page_data) == 0:
                        print(f"📄 已获取到最后一页，共 {page} 页")
                        break
                    else:
                        all_data.extend(page_data)
                        print(f"✅ 第 {page} 页获取成功: {len(page_data)} 只股票")

                        if len(page_data) < 100:
                            print(f"📄 已获取到最后一页，共 {page} 页")
                            break

                        page += 1
                        time.sleep(1.0)  # 页面间隔1秒

                if not all_data:
                    if attempt < max_retries:
                        wait_time = 10 if attempt == 0 else 60
                        print(f"❌ 第{attempt + 1}次获取数据为空，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print("❌ 重试后仍未获取到任何数据")
                        return []

                print(f"✅ 新浪财经数据获取完成: 共 {len(all_data)} 只股票")

                # 标准化数据格式
                standardized_data = []
                for item in all_data:
                    try:
                        standardized_item = {
                            '代码': item.get('symbol', ''),
                            '名称': item.get('name', ''),
                            '最新价': float(item.get('trade', 0)) if item.get('trade') else 0,
                            '涨跌幅': float(item.get('changepercent', 0)) if item.get('changepercent') else 0,
                            '成交量': int(item.get('volume', 0)) if item.get('volume') else 0,
                            '总市值': float(item.get('mktcap', 0)) if item.get('mktcap') else 0,
                            '流通市值': float(item.get('nmc', 0)) if item.get('nmc') else 0,
                            '换手率': float(item.get('turnoverratio', 0)) if item.get('turnoverratio') else 0
                        }
                        standardized_data.append(standardized_item)
                    except Exception as e:
                        continue

                return standardized_data

            except Exception as e:
                if attempt < max_retries:
                    wait_time = 10 if attempt == 0 else 60
                    print(f"❌ 第{attempt + 1}次获取新浪财经数据失败: {e}")
                    print(f"⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ 重试{max_retries}次后仍失败: {e}")
                    return []

        return []

    def _get_sina_page_data_with_retry(self, page, max_retries=2):
        """获取新浪财经单页数据（带重试机制）"""
        for attempt in range(max_retries + 1):
            try:
                url = "http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
                params = {
                    'page': page,
                    'num': 100,
                    'sort': 'symbol',
                    'asc': 1,
                    'node': 'hs_a'
                }

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'http://vip.stock.finance.sina.com.cn/',
                    'Accept': 'application/json, text/javascript, */*; q=0.01'
                }

                response = requests.get(url, params=params, headers=headers, timeout=15)

                if response.status_code == 200:
                    data_text = response.text
                    data_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', data_text)

                    try:
                        page_data = json.loads(data_text)

                        if page_data and isinstance(page_data, list):
                            return page_data
                        else:
                            return []  # 空数据，表示到达最后一页

                    except json.JSONDecodeError as e:
                        if attempt < max_retries:
                            wait_time = 10 if attempt == 0 else 60
                            print(f"⚠️ 第 {page} 页JSON解析失败(尝试{attempt + 1}): {e}")
                            print(f"⏱️ {wait_time}秒后重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            print(f"❌ 第 {page} 页JSON解析重试失败: {e}")
                            return None
                else:
                    if attempt < max_retries:
                        wait_time = 10 if attempt == 0 else 60
                        print(f"⚠️ 第 {page} 页请求失败(尝试{attempt + 1}): {response.status_code}")
                        print(f"⏱️ {wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print(f"❌ 第 {page} 页请求重试失败: {response.status_code}")
                        return None

            except Exception as e:
                if attempt < max_retries:
                    wait_time = 10 if attempt == 0 else 60
                    print(f"⚠️ 第 {page} 页获取异常(尝试{attempt + 1}): {e}")
                    print(f"⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"❌ 第 {page} 页获取重试失败: {e}")
                    return None

        return None

    def _normalize_stock_code(self, code):
        """标准化股票代码"""
        if not code:
            return ''

        code = str(code).lower()

        # 移除市场前缀
        prefixes = ['sh', 'sz', 'bj', 'hk']
        for prefix in prefixes:
            if code.startswith(prefix):
                code = code[len(prefix):]
                break

        # 确保是6位数字代码
        if code.isdigit() and len(code) == 6:
            return code

        return code

    def _check_market_scope(self, code, name):
        """检查市场范围筛选"""
        try:
            # 排除ST股票
            if 'ST' in name or '*ST' in name or 'SST' in name:
                return False

            # 排除科创板
            if code.startswith('688'):
                return False

            # 排除创业板
            if code.startswith('300'):
                return False

            # 排除北交所
            if code.startswith('8') or code.startswith('4'):
                return False

            # 确保是6位股票代码
            if len(code) != 6:
                return False

            # 只保留主板和中小板
            if not (code.startswith(('000', '001', '002', '600', '601', '603'))):
                return False

            return True

        except Exception as e:
            return False

    def _get_market_cap(self, stock):
        """获取市值（亿元）"""
        try:
            # 尝试多个可能的市值字段
            market_cap = (stock.get('总市值', 0) or
                         stock.get('流通市值', 0) or
                         stock.get('mktcap', 0) or
                         stock.get('nmc', 0))

            # 转换为数值
            market_cap = float(market_cap) if market_cap else 0

            # 如果是万元单位，转换为亿元
            if market_cap > 1000:
                market_cap = market_cap / 10000

            return market_cap

        except Exception as e:
            return 0

    def _filter_active_stocks(self, market_candidates):
        """活跃股基因筛选：基于涨停次数"""
        try:
            print("🧬 开始活跃股基因筛选...")

            # 获取涨停统计数据
            limit_up_stats = self._get_limit_up_statistics()

            if not limit_up_stats:
                print("❌ 无法获取涨停统计数据")
                return []

            print(f"✅ 涨停统计数据获取成功，包含 {len(limit_up_stats)} 只股票")

            active_candidates = []

            for candidate in market_candidates:
                try:
                    code = candidate['code']
                    name = candidate['name']

                    # 获取涨停统计
                    limit_up_info = limit_up_stats.get(code, {})
                    limit_up_count = limit_up_info.get('count', 0)
                    last_limit_up_date = limit_up_info.get('last_date', None)

                    # 条件1：涨停次数≥3次
                    if limit_up_count < self.config['min_limit_up_count']:
                        continue

                    # 条件2：排除近20日内有涨停的股票
                    if last_limit_up_date:
                        try:
                            last_date = datetime.strptime(last_limit_up_date, '%Y%m%d')
                            days_since = (self.current_date - last_date).days

                            if days_since < self.config['exclude_recent_days']:
                                continue
                        except:
                            pass

                    # 通过筛选
                    candidate['limit_up_count'] = limit_up_count
                    candidate['last_limit_up_date'] = last_limit_up_date
                    candidate['days_since_limit_up'] = days_since if last_limit_up_date else 999

                    active_candidates.append(candidate)

                    if len(active_candidates) <= 10:  # 只打印前10个
                        print(f"✅ {code} {name}: {limit_up_count}次涨停, 距今{days_since if last_limit_up_date else '999+'}天")

                except Exception as e:
                    continue

            if len(active_candidates) > 10:
                print(f"... 还有 {len(active_candidates) - 10} 只股票符合条件")

            print(f"✅ 活跃股筛选完成: {len(market_candidates)} → {len(active_candidates)} 只股票")
            return active_candidates

        except Exception as e:
            print(f"❌ 活跃股筛选失败: {e}")
            return []

    def _get_limit_up_statistics(self):
        """获取涨停统计数据"""
        try:
            print("📊 统计涨停数据...")

            # 获取最近一年的交易日
            date_list = self._get_recent_trading_days(250)  # 约一年

            limit_up_stats = defaultdict(lambda: {'count': 0, 'dates': [], 'last_date': None})

            for date_str in date_list:
                file_path = os.path.join(self.limit_up_data_dir, f"{date_str}.json")

                if not os.path.exists(file_path):
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 获取涨停股票列表
                    stocks = data.get('stocks', [])

                    if not stocks:
                        continue

                    for stock in stocks:
                        stock_code = stock.get('code', '')
                        stock_name = stock.get('name', '')

                        if stock_code:
                            # 使用股票代码作为key
                            limit_up_stats[stock_code]['count'] += 1
                            limit_up_stats[stock_code]['dates'].append(date_str)
                            limit_up_stats[stock_code]['name'] = stock_name  # 保存股票名称

                            # 更新最后涨停日期
                            if not limit_up_stats[stock_code]['last_date'] or date_str > limit_up_stats[stock_code]['last_date']:
                                limit_up_stats[stock_code]['last_date'] = date_str

                except Exception as e:
                    continue

            # 直接返回基于股票代码的统计结果
            print(f"✅ 涨停统计完成: 处理 {len(date_list)} 个交易日, 发现 {len(limit_up_stats)} 只活跃股票")

            return dict(limit_up_stats)

        except Exception as e:
            print(f"❌ 获取涨停统计失败: {e}")
            return {}

    def _get_recent_trading_days(self, days):
        """获取最近N个交易日"""
        date_list = []
        current = self.current_date

        search_range = days * 2  # 扩大搜索范围

        for i in range(search_range):
            check_date = current - timedelta(days=i)
            # 跳过周末
            if check_date.weekday() < 5:  # 0-4是周一到周五
                date_str = check_date.strftime('%Y%m%d')
                date_list.append(date_str)
                if len(date_list) >= days:
                    break

        return sorted(date_list)

    def _get_stock_name_to_code_mapping(self):
        """获取股票名称到代码的映射（带重试机制）

        按照要求的重试策略：
        - 第一次失败后等待10秒重试
        - 第二次失败后等待60秒重试
        """
        max_retries = 2

        # 尝试使用AKShare获取股票列表
        if self.akshare_available:
            for attempt in range(max_retries + 1):
                try:
                    import akshare as ak
                    stock_list = ak.stock_info_a_code_name()

                    name_to_code = {}
                    for _, row in stock_list.iterrows():
                        code = row['code']
                        name = row['name']
                        name_to_code[name] = code

                    print(f"✅ 股票名称映射获取成功: {len(name_to_code)} 只股票")
                    return name_to_code

                except Exception as e:
                    if attempt < max_retries:
                        # 根据尝试次数设置不同的等待时间
                        wait_time = 10 if attempt == 0 else 60
                        print(f"⚠️ 第{attempt + 1}次获取股票映射失败: {e}")
                        print(f"⏱️ {wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        print(f"❌ 重试{max_retries}次后仍无法获取股票映射: {e}")

        # 备用映射（常见股票）
        print("📋 使用备用股票名称映射")
        backup_mapping = {
            '海航科技': '600751',
            '新通联': '603022',
            '元隆雅图': '002878',
            '四创电子': '600990',
            '万里马': '300591',
            '大位科技': '600589',
            '天际股份': '002759',
            '建业股份': '603948',
            '中远海科': '002401',
            '华鑫股份': '600621'
        }
        return backup_mapping

    def _three_dimension_resonance_detection(self, active_candidates):
        """三维共振检测：量能密码 + 价格韧性 + 形态识别"""
        try:
            print("🎯 开始三维共振检测...")
            print(f"📊 待检测股票数量: {len(active_candidates)} 只")

            # 获取大盘指数数据
            index_data = self._get_index_data()

            final_candidates = []
            batch_size = self.config['batch_size']

            for i in range(0, len(active_candidates), batch_size):
                batch = active_candidates[i:i+batch_size]
                print(f"\n🔍 处理第 {i//batch_size + 1} 批次: {len(batch)} 只股票")

                for candidate in batch:
                    try:
                        code = candidate['code']
                        name = candidate['name']

                        print(f"\n🔍 分析 {code} {name}...")

                        # 获取K线数据（带重试机制）
                        kline_data = self._get_kline_data_with_retry(code, self.config['kline_days'])

                        if kline_data.empty:
                            print(f"  ❌ 无法获取K线数据")
                            continue

                        # 三维检测
                        dimension_results = self._detect_three_dimensions(kline_data, index_data)

                        # 计算综合评分
                        comprehensive_score = self._calculate_comprehensive_score(dimension_results)

                        print(f"  📊 量能密码: {dimension_results['volume_score']:.1f}分")
                        print(f"  📊 价格韧性: {dimension_results['resilience_score']:.1f}分")
                        print(f"  📊 形态识别: {dimension_results['pattern_score']:.1f}分")
                        print(f"  📊 综合评分: {comprehensive_score:.1f}分")

                        # 筛选阈值
                        if comprehensive_score >= self.config['comprehensive_score_threshold']:
                            candidate['kline_data'] = kline_data
                            candidate['dimension_results'] = dimension_results
                            candidate['comprehensive_score'] = comprehensive_score

                            final_candidates.append(candidate)
                            print(f"  ✅ 通过三维共振检测")
                        else:
                            print(f"  ❌ 综合评分不足 (需≥{self.config['comprehensive_score_threshold']})")

                        # 分析完一只股票后等待2秒
                        print(f"  ⏱️ 等待2秒后继续...")
                        time.sleep(2)

                    except Exception as e:
                        print(f"  ⚠️ 分析失败: {e}")
                        time.sleep(2)  # 出错也等待2秒
                        continue

            print(f"\n✅ 三维共振检测完成: {len(active_candidates)} → {len(final_candidates)} 只股票")
            return final_candidates

        except Exception as e:
            print(f"❌ 三维共振检测失败: {e}")
            return []

    def _get_index_data(self):
        """获取大盘指数数据（带重试机制）

        按照要求的重试策略：
        - 第一次失败后等待10秒重试
        - 第二次失败后等待60秒重试
        """
        max_retries = 2

        for attempt in range(max_retries + 1):
            try:
                if self.akshare_available:
                    import akshare as ak

                    # 获取上证指数数据
                    index_df = ak.stock_zh_index_daily(symbol="sh000001")

                    if not index_df.empty:
                        # 处理日期列
                        date_column = None
                        for col in ['date', '日期', 'Date', 'DATE']:
                            if col in index_df.columns:
                                date_column = col
                                break

                        if date_column:
                            index_df['date'] = pd.to_datetime(index_df[date_column])

                            # 重命名收盘价列
                            if '收盘' in index_df.columns:
                                index_df['close'] = index_df['收盘']
                            elif 'close' not in index_df.columns and '收盘价' in index_df.columns:
                                index_df['close'] = index_df['收盘价']

                            # 计算涨跌幅
                            if 'pct_chg' not in index_df.columns:
                                index_df['pct_chg'] = index_df['close'].pct_change() * 100

                            print(f"✅ 指数数据获取成功: {len(index_df)}条数据")
                            return index_df.tail(60)  # 返回最近60天数据

            except Exception as e:
                if attempt < max_retries:
                    # 根据尝试次数设置不同的等待时间
                    wait_time = 10 if attempt == 0 else 60
                    print(f"⚠️ 第{attempt + 1}次获取指数数据失败: {e}")
                    print(f"⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ 重试{max_retries}次后仍无法获取指数数据: {e}")

        # 返回空DataFrame
        return pd.DataFrame()

    def _get_kline_data(self, code, days=60):
        """获取K线数据"""
        try:
            if self.akshare_available:
                import akshare as ak

                # 计算日期范围
                end_date = self.current_date.strftime('%Y%m%d')
                start_date = (self.current_date - timedelta(days=days*2)).strftime('%Y%m%d')

                # 获取K线数据
                kline_df = ak.stock_zh_a_hist(
                    symbol=code,
                    start_date=start_date,
                    end_date=end_date,
                    adjust="qfq"  # 前复权
                )

                if not kline_df.empty:
                    # 标准化列名
                    kline_df = kline_df.rename(columns={
                        '日期': '日期',
                        '开盘': '开盘',
                        '最高': '最高',
                        '最低': '最低',
                        '收盘': '收盘',
                        '成交量': '成交量',
                        '成交额': '成交额',
                        '涨跌幅': '涨跌幅',
                        '涨跌额': '涨跌额',
                        '换手率': '换手率'
                    })

                    # 计算振幅
                    if '振幅' not in kline_df.columns:
                        kline_df['振幅'] = ((kline_df['最高'] - kline_df['最低']) / kline_df['开盘'] * 100).round(2)

                    # 确保数据按日期排序
                    kline_df['日期'] = pd.to_datetime(kline_df['日期'])
                    kline_df = kline_df.sort_values('日期').reset_index(drop=True)

                    return kline_df.tail(days)  # 返回最近N天数据

        except Exception as e:
            print(f"  ⚠️ 获取K线数据失败: {e}")

        # 返回空DataFrame
        return pd.DataFrame()

    def _get_kline_data_with_retry(self, code, days=60, max_retries=2):
        """获取K线数据（带重试机制）

        按照要求的重试策略：
        - 第一次失败后等待10秒重试
        - 第二次失败后等待60秒重试
        """
        for attempt in range(max_retries + 1):
            try:
                kline_data = self._get_kline_data(code, days)

                if not kline_data.empty:
                    return kline_data
                else:
                    if attempt < max_retries:
                        # 根据尝试次数设置不同的等待时间
                        wait_time = 10 if attempt == 0 else 60
                        print(f"  ⚠️ 第{attempt + 1}次获取数据为空，等待{wait_time}秒后重试...")
                        time.sleep(wait_time)
                    else:
                        print(f"  ❌ 重试{max_retries}次后仍无法获取数据")
                        return pd.DataFrame()

            except Exception as e:
                if attempt < max_retries:
                    # 根据尝试次数设置不同的等待时间
                    wait_time = 10 if attempt == 0 else 60
                    print(f"  ⚠️ 第{attempt + 1}次获取数据失败: {e}")
                    print(f"  ⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"  ❌ 重试{max_retries}次后仍失败: {e}")
                    return pd.DataFrame()

        return pd.DataFrame()

    def _detect_three_dimensions(self, kline_data, index_data):
        """检测三维特征"""
        try:
            results = {
                'volume_score': 0,
                'resilience_score': 0,
                'pattern_score': 0,
                'volume_details': {},
                'resilience_details': {},
                'pattern_details': {}
            }

            # 维度1：量能密码检测
            volume_result = self._detect_volume_pattern(kline_data)
            results['volume_score'] = volume_result['score']
            results['volume_details'] = volume_result

            # 维度2：价格韧性检测
            resilience_result = self._detect_price_resilience(kline_data, index_data)
            results['resilience_score'] = resilience_result['score']
            results['resilience_details'] = resilience_result

            # 维度3：形态识别检测
            pattern_result = self._detect_washing_patterns(kline_data)
            results['pattern_score'] = pattern_result['score']
            results['pattern_details'] = pattern_result

            return results

        except Exception as e:
            print(f"    ❌ 三维检测失败: {e}")
            return {
                'volume_score': 0,
                'resilience_score': 0,
                'pattern_score': 0,
                'volume_details': {},
                'resilience_details': {},
                'pattern_details': {}
            }

    def _detect_volume_pattern(self, kline_data):
        """维度1：量能密码检测"""
        try:
            if kline_data.empty or len(kline_data) < 20:
                return {'score': 0, 'reason': '数据不足'}

            # 寻找历史爆量日（涨停或大阳线）
            explosion_days = kline_data[
                (kline_data['涨跌幅'] >= 9.5) |  # 涨停
                (kline_data['涨跌幅'] >= 5.0)    # 大阳线
            ]

            if explosion_days.empty:
                return {'score': 0, 'reason': '无爆量日'}

            # 获取最大成交量日
            max_volume_day = kline_data.loc[kline_data['成交量'].idxmax()]
            max_volume = max_volume_day['成交量']

            # 获取最近5日平均成交量
            recent_5d_volume = kline_data.tail(5)['成交量'].mean()

            # 计算量能萎缩比例
            volume_shrink_ratio = recent_5d_volume / max_volume if max_volume > 0 else 1.0

            # 检查价格是否守住爆量日最低价
            max_volume_low = max_volume_day['最低']
            current_price = kline_data['收盘'].iloc[-1]
            price_held = current_price > max_volume_low

            # 评分逻辑
            score = 0

            # 量能萎缩评分（0-40分）
            if volume_shrink_ratio < 0.3:
                score += 40
            elif volume_shrink_ratio < 0.4:
                score += 30
            elif volume_shrink_ratio < 0.5:
                score += 20
            elif volume_shrink_ratio < 0.6:
                score += 10

            # 价格守住评分（0-20分）
            if price_held:
                score += 20

            # 连续缩量评分（0-20分）
            recent_volumes = kline_data.tail(5)['成交量'].tolist()
            is_continuous_shrink = all(recent_volumes[i] <= recent_volumes[i-1] * 1.1 for i in range(1, len(recent_volumes)))
            if is_continuous_shrink:
                score += 20

            return {
                'score': min(score, 100),
                'volume_shrink_ratio': volume_shrink_ratio,
                'price_held': price_held,
                'continuous_shrink': is_continuous_shrink,
                'max_volume': max_volume,
                'recent_avg_volume': recent_5d_volume,
                'reason': f'量缩{volume_shrink_ratio:.2f}, 价守{price_held}, 连缩{is_continuous_shrink}'
            }

        except Exception as e:
            return {'score': 0, 'reason': f'检测失败: {e}'}

    def _detect_price_resilience(self, kline_data, index_data):
        """维度2：价格韧性检测（抗跌性）"""
        try:
            if kline_data.empty or index_data.empty or len(kline_data) < 10:
                return {'score': 0, 'reason': '数据不足'}

            # 获取最近20日数据
            recent_data = kline_data.tail(20)

            # 找到大盘下跌日（跌幅>0.5%）
            fall_days = []
            red_days_in_fall = 0

            for _, row in recent_data.iterrows():
                date = row['日期'].date()

                # 在指数数据中找到对应日期
                index_row = index_data[index_data['date'].dt.date == date]

                if not index_row.empty:
                    index_change = index_row['pct_chg'].iloc[0] if 'pct_chg' in index_row.columns else 0

                    if index_change < -0.5:  # 大盘下跌日
                        fall_days.append(date)
                        if row['涨跌幅'] > 0:  # 个股收阳
                            red_days_in_fall += 1

            # 计算抗跌比例
            anti_fall_ratio = red_days_in_fall / len(fall_days) if fall_days else 0

            # 计算相对强度（个股vs大盘）
            stock_change = (recent_data['收盘'].iloc[-1] / recent_data['收盘'].iloc[0] - 1) * 100

            # 获取同期大盘变化
            if not index_data.empty:
                index_recent = index_data.tail(20)
                if len(index_recent) >= 2:
                    index_change = (index_recent['close'].iloc[-1] / index_recent['close'].iloc[0] - 1) * 100
                    relative_strength = stock_change - index_change
                else:
                    relative_strength = stock_change
            else:
                relative_strength = stock_change

            # 评分逻辑
            score = 0

            # 抗跌比例评分（0-50分）
            if anti_fall_ratio >= 0.7:
                score += 50
            elif anti_fall_ratio >= 0.6:
                score += 40
            elif anti_fall_ratio >= 0.5:
                score += 30
            elif anti_fall_ratio >= 0.4:
                score += 20
            elif anti_fall_ratio >= 0.3:
                score += 10

            # 相对强度评分（0-30分）
            if relative_strength > 5:
                score += 30
            elif relative_strength > 2:
                score += 20
            elif relative_strength > 0:
                score += 10

            # 价格稳定性评分（0-20分）
            price_volatility = recent_data['涨跌幅'].std()
            if price_volatility < 2:
                score += 20
            elif price_volatility < 3:
                score += 15
            elif price_volatility < 4:
                score += 10
            elif price_volatility < 5:
                score += 5

            return {
                'score': min(score, 100),
                'anti_fall_ratio': anti_fall_ratio,
                'relative_strength': relative_strength,
                'price_volatility': price_volatility,
                'fall_days_count': len(fall_days),
                'red_days_in_fall': red_days_in_fall,
                'reason': f'抗跌{anti_fall_ratio:.2f}, 相强{relative_strength:.1f}%, 波动{price_volatility:.1f}'
            }

        except Exception as e:
            return {'score': 0, 'reason': f'检测失败: {e}'}

    def _detect_washing_patterns(self, kline_data):
        """维度3：洗盘形态识别"""
        try:
            if kline_data.empty or len(kline_data) < 10:
                return {'score': 0, 'reason': '数据不足'}

            recent_data = kline_data.tail(10)  # 最近10日数据
            detected_patterns = []
            score = 0

            # 形态1：缩量十字星（振幅<3%且量比<0.5）
            doji_count = 0
            for _, row in recent_data.iterrows():
                amplitude = row.get('振幅', 0)
                volume = row.get('成交量', 0)

                # 计算量比（当日量/5日均量）
                idx = kline_data.index[kline_data['日期'] == row['日期']].tolist()
                if idx:
                    pos = idx[0]
                    if pos >= 4:  # 确保有足够的历史数据
                        avg_volume = kline_data.iloc[pos-4:pos+1]['成交量'].mean()
                        volume_ratio = volume / avg_volume if avg_volume > 0 else 1

                        if amplitude < self.config['doji_amplitude_threshold'] and volume_ratio < 0.5:
                            doji_count += 1

            if doji_count >= 2:
                detected_patterns.append('缩量十字星')
                score += 30

            # 形态2：长下影探针（下影线长度是实体的2倍+）
            probe_count = 0
            for _, row in recent_data.iterrows():
                open_price = row['开盘']
                close_price = row['收盘']
                high_price = row['最高']
                low_price = row['最低']

                if open_price > 0 and close_price > 0:
                    # 计算实体长度
                    body_length = abs(close_price - open_price)
                    # 计算下影线长度
                    lower_shadow = min(open_price, close_price) - low_price

                    if body_length > 0 and lower_shadow > body_length * 2:
                        probe_count += 1

            if probe_count >= 1:
                detected_patterns.append('长下影探针')
                score += 25

            # 形态3：假阴真阳（高开低走但收盘价>前日收盘）
            fake_negative_count = 0
            for i in range(1, len(recent_data)):
                current = recent_data.iloc[i]
                previous = recent_data.iloc[i-1]

                open_price = current['开盘']
                close_price = current['收盘']
                prev_close = previous['收盘']

                # 高开低走但收盘价>前日收盘
                if (open_price > prev_close and  # 高开
                    close_price < open_price and  # 低走
                    close_price > prev_close):    # 收盘价>前日收盘
                    fake_negative_count += 1

            if fake_negative_count >= 1:
                detected_patterns.append('假阴真阳')
                score += 20

            # 形态4：地量见地价（量创新低但价格未破前低）
            recent_volume = recent_data['成交量'].min()
            recent_low = recent_data['最低'].min()

            # 检查是否是近期地量
            historical_volume = kline_data.tail(30)['成交量']
            is_ground_volume = recent_volume == historical_volume.min()

            # 检查价格是否未破前低
            historical_low = kline_data.tail(30)['最低'].min()
            price_not_break = recent_low > historical_low * 0.95  # 允许5%的误差

            if is_ground_volume and price_not_break:
                detected_patterns.append('地量见地价')
                score += 25

            return {
                'score': min(score, 100),
                'detected_patterns': detected_patterns,
                'doji_count': doji_count,
                'probe_count': probe_count,
                'fake_negative_count': fake_negative_count,
                'ground_volume_price': is_ground_volume and price_not_break,
                'reason': f'形态: {", ".join(detected_patterns) if detected_patterns else "无"}'
            }

        except Exception as e:
            return {'score': 0, 'reason': f'检测失败: {e}'}

    def _calculate_comprehensive_score(self, dimension_results):
        """计算综合评分"""
        try:
            # 权重分配：量能密码40% + 价格韧性30% + 形态识别30%
            volume_score = dimension_results.get('volume_score', 0)
            resilience_score = dimension_results.get('resilience_score', 0)
            pattern_score = dimension_results.get('pattern_score', 0)

            comprehensive_score = (
                volume_score * 0.4 +
                resilience_score * 0.3 +
                pattern_score * 0.3
            )

            return comprehensive_score

        except Exception as e:
            return 0

    def _display_results(self, candidates):
        """展示分析结果"""
        try:
            print("\n" + "=" * 80)
            print("🎉 游资洗盘行为量化捕手分析结果")
            print("=" * 80)

            if not candidates:
                print("❌ 未发现符合条件的股票")
                return

            print(f"📊 共发现 {len(candidates)} 只游资洗盘股票：\n")

            # 按综合评分排序
            sorted_candidates = sorted(candidates, key=lambda x: x.get('comprehensive_score', 0), reverse=True)

            for i, candidate in enumerate(sorted_candidates, 1):
                code = candidate['code']
                name = candidate['name']
                score = candidate.get('comprehensive_score', 0)
                limit_up_count = candidate.get('limit_up_count', 0)
                days_since = candidate.get('days_since_limit_up', 0)

                print(f"🏆 {i}. {code} {name}")
                print(f"   📊 综合评分: {score:.1f}分")
                print(f"   🧬 活跃基因: {limit_up_count}次涨停, 距今{days_since}天")
                print(f"   💰 市值: {candidate.get('market_cap', 0):.1f}亿元")
                print(f"   💹 最新价: {candidate.get('latest_price', 0):.2f}元")

                # 显示三维分析结果
                dimension_results = candidate.get('dimension_results', {})
                print(f"   🔍 量能密码: {dimension_results.get('volume_score', 0):.1f}分")
                print(f"   🔍 价格韧性: {dimension_results.get('resilience_score', 0):.1f}分")
                print(f"   🔍 形态识别: {dimension_results.get('pattern_score', 0):.1f}分")

                # 显示检测到的形态
                pattern_details = dimension_results.get('pattern_details', {})
                detected_patterns = pattern_details.get('detected_patterns', [])
                if detected_patterns:
                    print(f"   📈 洗盘形态: {', '.join(detected_patterns)}")

                print()

            # 显示投资建议
            self._display_investment_advice(sorted_candidates)

        except Exception as e:
            print(f"❌ 展示结果失败: {e}")

    def _display_investment_advice(self, candidates):
        """显示投资建议"""
        print("💡 投资建议：")
        print("-" * 40)

        if not candidates:
            return

        top_candidate = candidates[0]
        top_score = top_candidate.get('comprehensive_score', 0)
        print(f"🥇 重点关注: {top_candidate['code']} {top_candidate['name']}")
        print(f"   理由: 综合评分最高({top_score:.1f}分)")

        # 寻找量能密码评分最高的
        volume_top = max(candidates, key=lambda x: x.get('dimension_results', {}).get('volume_score', 0))
        volume_score = volume_top.get('dimension_results', {}).get('volume_score', 0)
        if volume_score >= 60:
            print(f"🔥 量能最佳: {volume_top['code']} {volume_top['name']}")
            print(f"   理由: 量能萎缩最明显({volume_score:.1f}分)")

        print("\n⚠️ 操作建议：")
        print("1. 重点监控量比突破1.8的信号（启动前兆）")
        print("2. 突破20日最高价时考虑介入（确认启动）")
        print("3. 设置-3%止损，盈利>5%启动移动止盈")
        print("4. 单股仓位不超过总资金的10%")
        print("5. 仅在大盘量能>8000亿时重点关注")

    def _export_results(self, candidates):
        """导出结果到Excel"""
        try:
            if not candidates:
                return

            print("\n📁 导出分析结果...")

            # 准备导出数据
            export_data = []

            for candidate in candidates:
                try:
                    dimension_results = candidate.get('dimension_results', {})
                    volume_details = dimension_results.get('volume_details', {})
                    resilience_details = dimension_results.get('resilience_details', {})
                    pattern_details = dimension_results.get('pattern_details', {})

                    row = {
                        # 基础信息
                        '股票代码': candidate.get('code', 'N/A'),
                        '股票名称': candidate.get('name', 'N/A'),
                        '最新价格': candidate.get('latest_price', 0),
                        '涨跌幅(%)': candidate.get('change_pct', 0),
                        '市值(亿元)': candidate.get('market_cap', 0),
                        '换手率(%)': candidate.get('turnover_rate', 0),

                        # 活跃股基因
                        '年内涨停次数': candidate.get('limit_up_count', 0),
                        '距最近涨停天数': candidate.get('days_since_limit_up', 0),
                        '最近涨停日期': candidate.get('last_limit_up_date', 'N/A'),

                        # 三维评分
                        '综合评分': candidate.get('comprehensive_score', 0),
                        '量能密码评分': dimension_results.get('volume_score', 0),
                        '价格韧性评分': dimension_results.get('resilience_score', 0),
                        '形态识别评分': dimension_results.get('pattern_score', 0),

                        # 量能密码详情
                        '量能萎缩比例': volume_details.get('volume_shrink_ratio', 0),
                        '价格是否守住': '是' if volume_details.get('price_held', False) else '否',
                        '是否连续缩量': '是' if volume_details.get('continuous_shrink', False) else '否',
                        '最大成交量': volume_details.get('max_volume', 0),
                        '近5日平均量': volume_details.get('recent_avg_volume', 0),

                        # 价格韧性详情
                        '抗跌比例': resilience_details.get('anti_fall_ratio', 0),
                        '相对强度(%)': resilience_details.get('relative_strength', 0),
                        '价格波动率': resilience_details.get('price_volatility', 0),
                        '大盘下跌天数': resilience_details.get('fall_days_count', 0),
                        '逆势收阳天数': resilience_details.get('red_days_in_fall', 0),

                        # 形态识别详情
                        '检测到的洗盘形态': ', '.join(pattern_details.get('detected_patterns', [])),
                        '缩量十字星次数': pattern_details.get('doji_count', 0),
                        '长下影探针次数': pattern_details.get('probe_count', 0),
                        '假阴真阳次数': pattern_details.get('fake_negative_count', 0),
                        '地量见地价': '是' if pattern_details.get('ground_volume_price', False) else '否'
                    }

                    export_data.append(row)

                except Exception as e:
                    print(f"  ⚠️ 处理股票数据失败: {e}")
                    continue

            if not export_data:
                print("❌ 无有效数据可导出")
                return

            # 创建DataFrame并导出
            df = pd.DataFrame(export_data)

            # 按综合评分排序
            df = df.sort_values('综合评分', ascending=False)

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"游资洗盘行为量化分析_{timestamp}.xlsx"

            # 导出到根目录
            root_path = os.getcwd()
            full_path = os.path.join(root_path, filename)

            df.to_excel(full_path, index=False, engine='openpyxl')
            print(f"📁 结果已导出到: {full_path}")
            print(f"📊 共导出 {len(df)} 只股票的详细分析数据")

            # 显示导出统计
            high_score_count = len(df[df['综合评分'] >= 80])
            medium_score_count = len(df[(df['综合评分'] >= 60) & (df['综合评分'] < 80)])

            print(f"📈 评分分布: 高分(≥80分) {high_score_count}只, 中分(60-80分) {medium_score_count}只")

        except Exception as e:
            print(f"❌ 导出结果失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    try:
        print("🚀 游资洗盘行为量化捕手系统")
        print("基于三维共振模型的全A股游资洗盘痕迹识别")
        print("=" * 60)
        
        # 检查涨停数据目录
        limit_up_dir = os.path.join("..", "limit_up_data")
        if not os.path.exists(limit_up_dir):
            print(f"❌ 涨停数据目录不存在: {limit_up_dir}")
            print(f"💡 请确保 limit_up_data 文件夹在上级目录中")
            return
        
        files = os.listdir(limit_up_dir)
        json_files = [f for f in files if f.endswith('.json') and not f.startswith('annual_')]
        print(f"✅ 涨停数据目录存在，包含 {len(json_files)} 个数据文件")
        
        # 创建检测器
        print("🔧 创建游资洗盘检测器...")
        detector = GameCapitalWashingDetector()
        
        # 运行分析
        print("🚀 开始运行分析...")
        results = detector.run_analysis()
        
        if results:
            print(f"\n✅ 分析完成，发现 {len(results)} 只游资洗盘股票")
        else:
            print("\n❌ 未发现符合条件的股票")
            
    except Exception as e:
        print(f"❌ 程序运行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
