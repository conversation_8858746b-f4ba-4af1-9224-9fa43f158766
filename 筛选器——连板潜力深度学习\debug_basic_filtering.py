#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试基础筛选问题
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from data_fetchers.efinance_data_fetcher import EfinanceDataFetcher
from investigation.potential_stock_investigator import PotentialStockInvestigator

def debug_market_data():
    """调试市场数据获取"""
    print("🔍 调试市场数据获取...")
    
    try:
        investigator = PotentialStockInvestigator()
        
        # 获取市场数据
        print("📊 获取全市场股票数据...")
        market_stocks = investigator._get_market_stocks()
        
        if not market_stocks:
            print("❌ 无法获取市场数据")
            return
        
        print(f"✅ 获取到 {len(market_stocks)} 只股票")
        
        # 分析前10只股票的数据结构
        print("\n📋 前10只股票数据结构:")
        for i, stock in enumerate(market_stocks[:10]):
            print(f"  {i+1}. {stock}")
        
        # 分析市值数据
        print("\n💰 市值数据分析:")
        market_caps = []
        for stock in market_stocks[:100]:  # 分析前100只
            market_cap = investigator._get_market_cap(stock)
            if market_cap > 0:
                market_caps.append(market_cap)
        
        if market_caps:
            print(f"  有效市值数据: {len(market_caps)}/{len(market_stocks[:100])}")
            print(f"  市值范围: {min(market_caps):.2f} - {max(market_caps):.2f} 亿元")
            print(f"  平均市值: {sum(market_caps)/len(market_caps):.2f} 亿元")
        else:
            print("  ❌ 没有有效的市值数据")
        
        return market_stocks
        
    except Exception as e:
        print(f"❌ 调试市场数据失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def debug_basic_filtering(market_stocks):
    """调试基础筛选"""
    print("\n🔍 调试基础筛选...")
    
    try:
        investigator = PotentialStockInvestigator()
        
        print(f"📊 筛选配置:")
        print(f"  市值范围: {investigator.config['market_cap_min']/1e8:.0f} - {investigator.config['market_cap_max']/1e8:.0f} 亿元")
        
        # 手动执行筛选逻辑
        filtered_stocks = []
        filter_stats = {
            'total': len(market_stocks),
            'market_scope_failed': 0,
            'market_cap_failed': 0,
            'market_cap_too_low': 0,
            'market_cap_too_high': 0,
            'market_cap_zero': 0,
            'passed': 0
        }
        
        for stock in market_stocks:
            try:
                code = stock.get('代码', '') or stock.get('code', '')
                name = stock.get('名称', '') or stock.get('name', '')
                
                # 标准化股票代码
                code = investigator._normalize_stock_code(code)
                
                # 市场范围筛选
                if not investigator._check_market_scope(code, name):
                    filter_stats['market_scope_failed'] += 1
                    continue
                
                # 市值筛选
                market_cap = investigator._get_market_cap(stock)
                
                if market_cap == 0:
                    filter_stats['market_cap_zero'] += 1
                    continue
                
                if market_cap < investigator.config['market_cap_min']:
                    filter_stats['market_cap_too_low'] += 1
                    continue
                
                if market_cap > investigator.config['market_cap_max']:
                    filter_stats['market_cap_too_high'] += 1
                    continue
                
                # 通过筛选
                filter_stats['passed'] += 1
                filtered_stocks.append({
                    'code': code,
                    'name': name,
                    'market_cap': market_cap
                })
                
            except Exception as e:
                continue
        
        print(f"\n📊 筛选统计:")
        print(f"  总股票数: {filter_stats['total']}")
        print(f"  市场范围不符: {filter_stats['market_scope_failed']}")
        print(f"  市值为0: {filter_stats['market_cap_zero']}")
        print(f"  市值过低(<{investigator.config['market_cap_min']/1e8:.0f}亿): {filter_stats['market_cap_too_low']}")
        print(f"  市值过高(>{investigator.config['market_cap_max']/1e8:.0f}亿): {filter_stats['market_cap_too_high']}")
        print(f"  通过筛选: {filter_stats['passed']}")
        
        if filtered_stocks:
            print(f"\n✅ 通过筛选的前10只股票:")
            for i, stock in enumerate(filtered_stocks[:10]):
                print(f"  {i+1}. {stock['code']} {stock['name']} 市值:{stock['market_cap']:.2f}亿")
        else:
            print(f"\n❌ 没有股票通过基础筛选")
        
        return filtered_stocks
        
    except Exception as e:
        print(f"❌ 调试基础筛选失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def debug_market_cap_config():
    """调试市值配置问题"""
    print("\n🔍 调试市值配置...")
    
    investigator = PotentialStockInvestigator()
    
    print(f"📊 当前市值配置:")
    print(f"  market_cap_min: {investigator.config['market_cap_min']}")
    print(f"  market_cap_max: {investigator.config['market_cap_max']}")
    print(f"  转换为亿元: {investigator.config['market_cap_min']/1e8:.0f} - {investigator.config['market_cap_max']/1e8:.0f} 亿")
    
    # 检查配置是否合理
    if investigator.config['market_cap_min'] == 30e8:  # 30亿 * 1亿 = 3000亿
        print("❌ 发现问题：市值配置错误！")
        print("   当前配置：30e8 = 3000亿，应该是 30 * 1e8 = 30亿")
        print("   建议修复：market_cap_min: 30 * 1e8, market_cap_max: 130 * 1e8")
        return False
    else:
        print("✅ 市值配置正常")
        return True

def suggest_fix():
    """建议修复方案"""
    print("\n💡 修复建议:")
    print("1. 修复市值配置错误:")
    print("   market_cap_min: 30 * 1e8  # 30亿")
    print("   market_cap_max: 130 * 1e8  # 130亿")
    print()
    print("2. 或者调整市值单位处理逻辑")
    print("3. 检查数据源的市值单位是否正确")

def main():
    """主函数"""
    print("🔧 基础筛选问题调试...")
    print("="*60)
    
    # 调试市值配置
    config_ok = debug_market_cap_config()
    
    # 调试市场数据获取
    market_stocks = debug_market_data()
    
    if market_stocks:
        # 调试基础筛选
        filtered_stocks = debug_basic_filtering(market_stocks)
        
        if not filtered_stocks and not config_ok:
            print("\n🎯 问题确认：市值配置错误导致基础筛选失败")
        elif not filtered_stocks:
            print("\n🎯 问题确认：其他原因导致基础筛选失败")
        else:
            print("\n✅ 基础筛选正常工作")
    
    # 建议修复方案
    if not config_ok:
        suggest_fix()
    
    print("\n🎉 调试完成！")

if __name__ == "__main__":
    main()
