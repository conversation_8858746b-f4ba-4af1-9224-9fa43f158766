#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
连板潜力股实时评分系统 - 简化版
专注核心功能，避免复杂的DataFrame操作
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入真实数据接口
try:
    from real_data_interface import real_data_interface
    REAL_DATA_AVAILABLE = True
    print("✅ 真实数据接口连接成功")
except ImportError as e:
    REAL_DATA_AVAILABLE = False
    print(f"❌ 真实数据接口连接失败: {e}")

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SimpleStockScorer:
    """简化版连板潜力股实时评分器"""
    
    def __init__(self):
        """初始化评分器"""
        logger.info("🔍 简化版连板潜力股实时评分器初始化完成")
        if REAL_DATA_AVAILABLE:
            logger.info("✅ 真实数据接口可用")
        else:
            logger.warning("⚠️ 真实数据接口不可用，部分功能将受限")

        # 初始化10点后趋势逆转分析器
        self.trend_reversal_analyzer = TrendReversalAnalyzer()

    def _get_volume_column(self, data):
        """获取成交量列名（智能检测）"""
        if data is None or data.empty:
            return None

        # 按优先级检查可能的列名
        volume_columns = ['volume', '成交量', 'vol', 'Volume', 'VOLUME']
        for col in volume_columns:
            if col in data.columns:
                return col
        return None

    def _get_safe_volume_data(self, data, column_name=None):
        """安全获取成交量数据"""
        if data is None or data.empty:
            return pd.Series(dtype=float)

        if column_name is None:
            column_name = self._get_volume_column(data)

        if column_name is None:
            # 如果找不到成交量列，返回空Series
            return pd.Series(dtype=float)

        return data[column_name]
    
    def get_stock_codes_from_user(self):
        """从用户输入获取股票代码"""
        print("📝 请输入要分析的股票代码，一行一个，输入完成后按回车键结束：")
        print("💡 示例：002082")
        print("\n请输入股票代码：")
        
        stock_codes = []
        while True:
            try:
                line = input().strip()
                if not line:
                    break

                if len(line) == 6 and line.isdigit():
                    stock_codes.append(line)
                    print(f"✅ 已添加: {line}")
                else:
                    print(f"❌ 无效代码: {line}")

            except KeyboardInterrupt:
                print("\n⚠️ 用户中断输入")
                break
            except EOFError:
                print("\n✅ 输入完成")
                break
        
        return stock_codes
    
    def analyze_stocks(self, stock_codes):
        """分析股票列表"""
        if not stock_codes:
            return {'success': False, 'error': '股票代码列表为空'}
        
        logger.info(f"🚀 开始分析{len(stock_codes)}只股票...")
        
        results = []
        target_date = datetime.now().strftime('%Y%m%d')
        
        for i, code in enumerate(stock_codes, 1):
            logger.info(f"📊 分析第{i}/{len(stock_codes)}只股票: {code}")
            
            try:
                # 获取分时数据
                minute_data = self._get_minute_data(code, target_date)
                if minute_data is None:
                    logger.warning(f"⚠️ {code}: 无法获取分时数据")
                    continue
                
                # 执行简化评分
                score_result = self._calculate_simple_score(code, minute_data)
                
                if score_result:
                    results.append(score_result)
                    logger.info(f"✅ {code}: 评分完成 ({score_result['total_score']:.3f})")
                
            except Exception as e:
                logger.error(f"❌ 分析{code}失败: {e}")
                continue
        
        # 按评分排序
        results.sort(key=lambda x: x['total_score'], reverse=True)

        # 生成分析报告
        report_file = self._generate_report(results, target_date)

        return {
            'success': True,
            'analyzed_count': len(results),
            'results': results,
            'target_date': target_date,
            'report_file': report_file
        }
    
    def _get_minute_data(self, code, target_date):
        """获取分时数据（使用真实数据接口）"""
        try:
            if not REAL_DATA_AVAILABLE:
                logger.error("真实数据接口不可用")
                return None

            minute_result = real_data_interface.get_historical_minute_data(code, target_date)

            if minute_result and minute_result.get('data_available', False):
                minute_df = minute_result.get('minute_data', pd.DataFrame())
                if len(minute_df) > 0:
                    # 标准化列名
                    column_mapping = {
                        '时间': 'time', '开盘': 'open', '收盘': 'close',
                        '最高': 'high', '最低': 'low', '成交量': 'volume',
                        '成交额': 'amount', '均价': 'avg_price'
                    }
                    minute_df = minute_df.rename(columns=column_mapping)
                    logger.info(f"✅ 获取{code}分时数据: {len(minute_df)}条记录")
                    return minute_df
            else:
                error_msg = minute_result.get('error', '数据获取失败')
                logger.warning(f"⚠️ {code}分时数据获取失败: {error_msg}")

            return None

        except Exception as e:
            logger.error(f"获取{code}分时数据失败: {e}")
            return None

    def _get_history_data(self, code):
        """获取历史数据用于对比（使用真实数据接口）"""
        try:
            if not REAL_DATA_AVAILABLE:
                logger.error("真实数据接口不可用")
                return None

            # 获取最近7天的分时数据作为历史参考
            history_result = real_data_interface.get_recent_7days_minute_data(code)

            if history_result and history_result.get('data_available', False):
                trading_days_data = history_result.get('trading_days_data', [])
                if trading_days_data:
                    # 合并历史数据（排除最后一天）
                    all_history = []
                    for day_data in trading_days_data[:-1]:  # 排除最后一天（当日）
                        day_minute_data = day_data.get('minute_data', pd.DataFrame())
                        if not day_minute_data.empty:
                            all_history.append(day_minute_data)

                    if all_history:
                        history_df = pd.concat(all_history, ignore_index=True)
                        logger.info(f"✅ 获取{code}历史数据: {len(history_df)}条记录")
                        return history_df

            return None

        except Exception as e:
            logger.error(f"获取{code}历史数据失败: {e}")
            return None
    
    def _calculate_simple_score(self, code, minute_data):
        """计算简化评分（支持平开/高开算法切换）"""
        try:
            if len(minute_data) < 5:
                return None

            # 获取历史数据用于量比计算
            history_data = self._get_history_data(code)

            # 检测开盘类型
            open_info = self._check_open_type(minute_data, code)

            # 根据开盘类型选择算法
            if open_info.get('is_high', False):  # 高开股（涨幅>=3%）
                logger.info(f"🚀 {code} 检测为高开股，启用高开算法")
                return self._analyze_high_open_stock(code, minute_data, history_data, open_info)

            elif open_info.get('is_flat', False):  # 平开股（涨跌幅<3%）
                logger.info(f"📊 {code} 检测为平开股，启用平开算法")
                return self._analyze_flat_open_stock(code, minute_data, history_data, open_info)

            else:  # 其他情况（低开等）
                logger.info(f"⚠️ {code} 开盘类型: {open_info.get('type_desc', '未知')}，使用基础算法")
                return self._analyze_basic_stock(code, minute_data, history_data, open_info)

        except Exception as e:
            logger.error(f"股票评分计算失败: {e}")
            return None

    def _analyze_flat_open_stock(self, code, minute_data, history_data, open_info):
        """平开股分析（原有算法）"""
        try:
            # 1. 量能分析 (40%) - 使用增强版
            volume_analysis = self._analyze_volume_enhanced(code, minute_data, history_data)
            volume_score = volume_analysis['score']

            # 2. 价格分析 (40%) - 使用增强版
            price_analysis = self._analyze_price_enhanced(minute_data)
            price_score = price_analysis['score']

            # 3. 时间分析 (20%) - 使用增强版
            time_analysis = self._analyze_time_enhanced(minute_data)
            time_score = time_analysis['score']

            # 计算基础加权总分 (量能45% + 时间35% + 价格20%) - 按报告重视程度调整
            base_score = volume_score * 0.45 + time_score * 0.35 + price_score * 0.2

            # 10点后趋势逆转分析
            current_time = datetime.now()
            reversal_analysis = self.trend_reversal_analyzer.analyze_trend_reversal(
                minute_data, history_data, {
                    'base_score': base_score,
                    'volume_score': volume_score,
                    'time_score': time_score,
                    'price_score': price_score
                }, current_time
            )

            # 融合评分计算
            if reversal_analysis.get('enabled', False):
                reversal_score = reversal_analysis.get('reversal_score', 0.0)
                # 动态权重：时间越晚，逆转补偿权重越高
                if current_time.hour >= 11:
                    base_weight, reversal_weight = 0.5, 0.5
                elif current_time.hour == 10 and current_time.minute >= 30:
                    base_weight, reversal_weight = 0.6, 0.4
                else:
                    base_weight, reversal_weight = 0.7, 0.3

                total_score = base_score * base_weight + reversal_score * reversal_weight
                logger.info(f"🔄 10点后融合评分: 基础{base_score:.3f}×{base_weight} + 逆转{reversal_score:.3f}×{reversal_weight} = {total_score:.3f}")
            else:
                total_score = base_score
            
            # 确定评级
            if total_score >= 0.8:
                rating = 'A+'
            elif total_score >= 0.7:
                rating = 'A'
            elif total_score >= 0.6:
                rating = 'B+'
            elif total_score >= 0.5:
                rating = 'B'
            else:
                rating = 'C'
            
            # 实战决策树分析
            decision_tree_result = self._execute_complete_decision_tree(
                code, minute_data, volume_analysis, price_analysis, time_analysis
            )

            # 生成具体操作建议
            operation_advice = self._generate_operation_advice(
                code, minute_data, decision_tree_result, total_score, volume_analysis, price_analysis
            )

            return {
                'algorithm_type': 'flat_open',
                'code': code,
                'open_info': open_info,
                'total_score': total_score,
                'base_score': base_score,  # 添加基础评分
                'rating': rating,
                'grade': self._get_score_grade(total_score),
                'volume_score': volume_score,
                'price_score': price_score,
                'time_score': time_score,
                'volume_analysis': volume_analysis,  # 详细量能分析结果
                'price_analysis': price_analysis,    # 详细价格分析结果
                'time_analysis': time_analysis,      # 详细时间分析结果
                'reversal_analysis': reversal_analysis,  # 10点后逆转分析结果
                'decision_tree': decision_tree_result,  # 完整决策树结果
                'operation_advice': operation_advice,  # 具体操作建议
                'analysis_time': datetime.now().strftime('%H:%M:%S'),
                'summary': f"平开股评分: {total_score:.1%} ({rating}级) | 决策: {operation_advice.get('action', '观望')}"
            }

        except Exception as e:
            logger.error(f"平开股分析失败: {e}")
            return {
                'algorithm_type': 'flat_open',
                'code': code,
                'error': str(e),
                'total_score': 0.0,
                'decision': {'action': '观望', 'reason': '分析失败', 'confidence': 0}
            }

    def _analyze_basic_stock(self, code, minute_data, history_data, open_info):
        """基础股票分析（低开等其他情况）"""
        try:
            logger.info(f"⚠️ {code} 使用基础算法分析")

            # 简化的基础分析
            basic_score = 0.3  # 基础分数

            return {
                'algorithm_type': 'basic',
                'code': code,
                'open_info': open_info,
                'total_score': basic_score,
                'grade': self._get_score_grade(basic_score),
                'decision': {
                    'action': '观望',
                    'reason': f'开盘类型({open_info.get("type_desc", "未知")})不适用连板算法',
                    'confidence': 50
                },
                'summary': f"基础分析: {basic_score:.1%} | 开盘类型: {open_info.get('type_desc', '未知')}"
            }

        except Exception as e:
            logger.error(f"基础股票分析失败: {e}")
            return {
                'algorithm_type': 'basic',
                'code': code,
                'error': str(e),
                'total_score': 0.0,
                'decision': {'action': '观望', 'reason': '分析失败', 'confidence': 0}
            }

    def _get_score_grade(self, score):
        """获取评分等级"""
        if score >= 0.8:
            return 'A+'
        elif score >= 0.7:
            return 'A'
        elif score >= 0.6:
            return 'B+'
        elif score >= 0.5:
            return 'B'
        elif score >= 0.4:
            return 'C+'
        else:
            return 'C'
    
    def _analyze_volume_enhanced(self, code, minute_data, history_data=None):
        """增强版量能分析（包含量比计算）"""
        try:
            if len(minute_data) < 5:
                return {'score': 0.0, 'signals': ['数据不足'], 'volume_ratio_current': 1.0}

            # 安全获取成交量数据
            volume_col = self._get_volume_column(minute_data)
            if volume_col is None:
                return {'score': 0.0, 'signals': ['未找到成交量列'], 'volume_ratio_current': 1.0}

            volumes = minute_data[volume_col].values
            signals = []
            score = 0.0

            # 1. 计算量比（当前量/历史同期平均量）
            volume_ratio_current = self._calculate_volume_ratio(minute_data, history_data)

            # 2. 开盘量比检测（9:30量比>1.8）- 按报告要求优化权重
            early_volume_ratio = self._calculate_early_volume_ratio(minute_data, history_data)
            if early_volume_ratio > 2.0:  # 超强量比
                score += 0.5  # 提升加分
                signals.append(f'超强开盘量比{early_volume_ratio:.1f}倍')
            elif early_volume_ratio > 1.8:
                score += 0.4
                signals.append(f'开盘量比{early_volume_ratio:.1f}倍')
            elif early_volume_ratio < 0.8:  # 按报告要求，量比<0.8是重要风险信号
                score -= 0.3  # 提升扣分权重
                signals.append(f'开盘量比偏低{early_volume_ratio:.1f}倍')

            # 3. 量能衰竭检测（增强版）
            volume_exhaustion = self._detect_volume_exhaustion_enhanced(minute_data, history_data)
            if volume_exhaustion['detected']:
                score -= volume_exhaustion['penalty']
                signals.extend(volume_exhaustion['signals'])

            # 4. 攻击量峰检测（按报告要求：单分钟量能>前日同期300%）
            attack_wave_result = self._detect_attack_volume_waves(minute_data, history_data)
            if attack_wave_result['has_attack_wave']:
                if attack_wave_result['max_ratio'] > 5.0:  # 超强攻击量峰
                    score += 0.6
                    signals.append(f'超强攻击量峰{attack_wave_result["max_ratio"]:.1f}倍')
                elif attack_wave_result['max_ratio'] > 3.0:  # 标准攻击量峰
                    score += 0.5
                    signals.append(f'攻击量峰{attack_wave_result["max_ratio"]:.1f}倍')
                elif attack_wave_result['max_ratio'] > 2.0:
                    score += 0.3
                    signals.append(f'量能放大{attack_wave_result["max_ratio"]:.1f}倍')

                # 添加时间信息
                if attack_wave_result['peak_times']:
                    signals.append(f'量峰时间: {",".join(attack_wave_result["peak_times"][:3])}')  # 最多显示3个

            # 5. 量能结构分析（上涨时段成交量>下跌时段150%）
            volume_structure = self._analyze_volume_structure(minute_data)
            if volume_structure['good_structure']:
                score += 0.3
                signals.append(volume_structure['description'])

            # 6. 历史风险对比分析
            historical_risk = self._analyze_historical_risk_similarity(minute_data, history_data)
            if historical_risk['high_risk']:
                score -= historical_risk['penalty']
                signals.append(historical_risk['description'])

            # 7. 炸板后遗症验证
            zhaban_syndrome = self._analyze_zhaban_syndrome(code, minute_data, history_data)
            if zhaban_syndrome.get('high_risk', False):
                score -= zhaban_syndrome.get('penalty', 0)
                signals.append(zhaban_syndrome.get('description', '炸板后遗症'))

            # 8. 板块联动验证 - 按报告要求提升权重
            sector_linkage = self._analyze_sector_linkage(code, minute_data)
            if sector_linkage.get('positive_linkage', False):
                bonus = sector_linkage.get('bonus', 0) * 1.2  # 提升板块联动权重
                score += bonus
                signals.append(sector_linkage.get('description', '板块联动'))

            description = '; '.join(signals) if signals else '量能表现一般'

            return {
                'score': max(0.0, min(score, 1.0)),  # 确保分数在0-1之间
                'signals': signals,
                'description': description,
                'volume_ratio_current': volume_ratio_current,
                'early_volume_ratio': early_volume_ratio,
                'peak_ratio': attack_wave_result.get('max_ratio', 1.0),  # 使用攻击量峰的最大比例
                'attack_wave_result': attack_wave_result,  # 新增攻击量峰结果
                'volume_exhaustion': volume_exhaustion,
                'historical_risk': historical_risk,
                'zhaban_syndrome': zhaban_syndrome,
                'sector_linkage': sector_linkage
            }

        except Exception as e:
            logger.error(f"增强量能分析失败: {e}")
            return {'score': 0.0, 'signals': ['分析失败'], 'volume_ratio_current': 1.0}

    def _calculate_volume_ratio(self, minute_data, history_data):
        """计算当前量比（当前量/历史同期平均量）"""
        try:
            if history_data is None:
                return 1.0

            # 安全检查DataFrame是否为空
            try:
                if len(history_data) == 0:
                    return 1.0
            except:
                return 1.0

            # 计算当前总成交量（支持多种列名）
            volume_col = None
            for col in ['volume', '成交量', 'vol']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            if volume_col is None:
                logger.warning("未找到成交量列，使用默认量比1.0")
                return 1.0

            current_total_volume = minute_data[volume_col].sum()

            # 计算历史平均成交量
            history_volume_col = None
            for col in ['volume', '成交量', 'vol']:
                if col in history_data.columns:
                    history_volume_col = col
                    break

            if history_volume_col is None:
                logger.warning("历史数据未找到成交量列，使用默认量比1.0")
                return 1.0

            history_avg_volume = history_data[history_volume_col].mean()

            # 计算量比
            volume_ratio = current_total_volume / history_avg_volume if history_avg_volume > 0 else 1.0

            return volume_ratio

        except Exception as e:
            logger.error(f"计算量比失败: {e}")
            return 1.0

    def _calculate_early_volume_ratio(self, minute_data, history_data):
        """计算开盘量比（前5分钟量比）"""
        try:
            if len(minute_data) < 5:
                return 1.0

            # 查找成交量列
            volume_col = None
            for col in ['volume', '成交量', 'vol']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            if volume_col is None:
                logger.warning("未找到成交量列，使用默认开盘量比1.0")
                return 1.0

            # 前5分钟成交量
            early_volume = minute_data[volume_col].iloc[:5].sum()

            # 检查历史数据
            if history_data is None:
                # 没有历史数据，用当前数据的平均量估算
                avg_volume = minute_data[volume_col].mean()
                expected_5min_volume = avg_volume * 5
                return early_volume / expected_5min_volume if expected_5min_volume > 0 else 1.0

            try:
                if len(history_data) == 0:
                    # 历史数据为空，用当前数据估算
                    avg_volume = minute_data[volume_col].mean()
                    expected_5min_volume = avg_volume * 5
                    return early_volume / expected_5min_volume if expected_5min_volume > 0 else 1.0
            except:
                # DataFrame比较出错，用当前数据估算
                avg_volume = minute_data[volume_col].mean()
                expected_5min_volume = avg_volume * 5
                return early_volume / expected_5min_volume if expected_5min_volume > 0 else 1.0

            # 历史同期5分钟平均量
            # 查找历史数据的成交量列
            history_volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in history_data.columns:
                    history_volume_col = col
                    break

            if history_volume_col is None:
                # 历史数据没有成交量列，用当前数据估算
                logger.warning("历史数据未找到成交量列，使用当前数据估算")
                avg_volume = minute_data[volume_col].mean()
                expected_5min_volume = avg_volume * 5
                return early_volume / expected_5min_volume if expected_5min_volume > 0 else 1.0

            history_avg_volume = history_data[history_volume_col].mean()
            expected_5min_volume = history_avg_volume * 5

            early_volume_ratio = early_volume / expected_5min_volume if expected_5min_volume > 0 else 1.0

            return early_volume_ratio

        except Exception as e:
            logger.error(f"计算开盘量比失败: {e}")
            return 1.0

    def _detect_volume_collapse(self, minute_data, history_data):
        """检测量比崩塔（9:35量比<0.7 → 9:45<0.5）"""
        try:
            if len(minute_data) < 15:  # 至少需要15分钟数据
                return {'detected': False, 'description': '数据不足'}

            # 检查是否有时间列
            if 'time' not in minute_data.columns:
                return {'detected': False, 'description': '无时间数据'}

            # 计算9:35和9:45的量比
            time_strs = minute_data['time'].astype(str)

            # 9:35前的数据（前5分钟）
            early_mask = time_strs.str.contains('09:3[0-5]', na=False)
            early_data = minute_data[early_mask]

            # 9:45前的数据（前15分钟）
            mid_mask = time_strs.str.contains('09:[3-4][0-9]', na=False)
            mid_data = minute_data[mid_mask]

            if len(early_data) < 3 or len(mid_data) < 10:
                return {'detected': False, 'description': '时间数据不足'}

            # 查找成交量列
            volume_col = None
            for col in ['volume', '成交量', 'vol', 'Volume']:
                if col in minute_data.columns:
                    volume_col = col
                    break

            if volume_col is None:
                return {'detected': False, 'description': '未找到成交量列'}

            # 计算量比
            early_volume = early_data[volume_col].sum()
            mid_volume = mid_data[volume_col].sum()

            # 安全检查历史数据并计算量比
            try:
                if history_data is not None and len(history_data) > 0:
                    # 查找历史数据的成交量列
                    history_volume_col = None
                    for col in ['volume', '成交量', 'vol', 'Volume']:
                        if col in history_data.columns:
                            history_volume_col = col
                            break

                    if history_volume_col:
                        history_avg = history_data[history_volume_col].mean()
                        early_ratio = early_volume / (history_avg * len(early_data)) if history_avg > 0 else 1.0
                        mid_ratio = mid_volume / (history_avg * len(mid_data)) if history_avg > 0 else 1.0
                    else:
                        # 历史数据没有成交量列，用当前数据估算
                        current_avg = minute_data[volume_col].mean()
                        early_ratio = early_volume / (current_avg * len(early_data)) if current_avg > 0 else 1.0
                        mid_ratio = mid_volume / (current_avg * len(mid_data)) if current_avg > 0 else 1.0
                else:
                    # 用当前数据估算
                    current_avg = minute_data[volume_col].mean()
                    early_ratio = early_volume / (current_avg * len(early_data)) if current_avg > 0 else 1.0
                    mid_ratio = mid_volume / (current_avg * len(mid_data)) if current_avg > 0 else 1.0
            except:
                # DataFrame比较出错，用当前数据估算
                current_avg = minute_data[volume_col].mean()
                early_ratio = early_volume / (current_avg * len(early_data)) if current_avg > 0 else 1.0
                mid_ratio = mid_volume / (current_avg * len(mid_data)) if current_avg > 0 else 1.0

            # 检测量比崩塔
            if early_ratio < 0.7 and mid_ratio < 0.5:
                return {
                    'detected': True,
                    'description': f'量比崩塔(9:35:{early_ratio:.1f} → 9:45:{mid_ratio:.1f})',
                    'early_ratio': early_ratio,
                    'mid_ratio': mid_ratio
                }
            elif early_ratio < 0.8:
                return {
                    'detected': True,
                    'description': f'量比偏低(9:35:{early_ratio:.1f})',
                    'early_ratio': early_ratio,
                    'mid_ratio': mid_ratio
                }

            return {'detected': False, 'description': '量比正常'}

        except Exception as e:
            logger.error(f"检测量比崩塔失败: {e}")
            return {'detected': False, 'description': '检测失败'}

    def _analyze_volume_structure(self, minute_data):
        """分析量能结构（上涨时段成交量>下跌时段150%）"""
        try:
            up_mask = minute_data['close'] > minute_data['open']
            down_mask = minute_data['close'] < minute_data['open']

            up_volume = minute_data[up_mask]['volume'].sum()
            down_volume = minute_data[down_mask]['volume'].sum()

            if down_volume > 0:
                structure_ratio = up_volume / down_volume
                if structure_ratio > 1.5:
                    return {
                        'good_structure': True,
                        'description': f'上涨放量结构良好({structure_ratio:.1f}倍)',
                        'ratio': structure_ratio
                    }

            return {'good_structure': False, 'description': '量能结构一般', 'ratio': 1.0}

        except Exception as e:
            logger.error(f"分析量能结构失败: {e}")
            return {'good_structure': False, 'description': '结构分析失败', 'ratio': 1.0}

    def _detect_volume_exhaustion_enhanced(self, minute_data, history_data):
        """简化的量能衰竭检测"""
        try:
            exhaustion_signals = []
            total_penalty = 0.0
            detected = False

            # 1. 量比崩塔检测（简化版）
            volume_collapse = self._detect_volume_collapse_simple(minute_data)
            if volume_collapse['detected']:
                detected = True
                total_penalty += volume_collapse['penalty']
                exhaustion_signals.append(volume_collapse['description'])

            # 2. 无量反抽检测（保留核心功能）
            volume_rebound = self._detect_volume_rebound_weakness(minute_data)
            if volume_rebound['detected']:
                detected = True
                total_penalty += volume_rebound['penalty']
                exhaustion_signals.append(volume_rebound['description'])

            return {
                'detected': detected,
                'penalty': min(total_penalty, 0.4),  # 简化最大扣分0.4
                'signals': exhaustion_signals
            }

        except Exception as e:
            logger.error(f"量能衰竭检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'signals': ['检测失败']}

    def _detect_volume_collapse_simple(self, minute_data):
        """简化的量比崩塔检测（按报告要求：9:35量比<0.7 → 9:45<0.5）"""
        try:
            if len(minute_data) < 15:
                return {'detected': False, 'penalty': 0.0, 'description': '数据不足'}

            volumes = minute_data['volume'].values

            # 计算9:35和9:45的量比
            vol_935 = volumes[5] if len(volumes) > 5 else 0  # 9:35分钟
            vol_945 = volumes[15] if len(volumes) > 15 else 0  # 9:45分钟

            # 计算基准量（用前5分钟平均）
            avg_vol = np.mean(volumes[:5]) if len(volumes) >= 5 else 1

            ratio_935 = vol_935 / avg_vol if avg_vol > 0 else 1
            ratio_945 = vol_945 / avg_vol if avg_vol > 0 else 1

            # 按报告要求的量比崩塔判断
            if ratio_935 < 0.7 and ratio_945 < 0.5:
                return {
                    'detected': True,
                    'penalty': 0.3,
                    'description': f'量比崩塔(9:35:{ratio_935:.1f} → 9:45:{ratio_945:.1f})'
                }

            return {'detected': False, 'penalty': 0.0, 'description': '量比正常'}

        except Exception as e:
            logger.error(f"量比崩塔检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'description': '检测失败'}





    def _detect_volume_rebound_weakness(self, minute_data):
        """检测无量反抽（简化版）"""
        try:
            if len(minute_data) < 10:
                return {'detected': False, 'penalty': 0.0, 'description': '数据不足'}

            opens = minute_data['open'].values
            closes = minute_data['close'].values
            volumes = minute_data['volume'].values

            # 识别上涨和下跌分钟
            up_mask = closes > opens
            down_mask = closes < opens

            up_volumes = volumes[up_mask]
            down_volumes = volumes[down_mask]

            if len(up_volumes) == 0 or len(down_volumes) == 0:
                return {'detected': False, 'penalty': 0.0, 'description': '无反抽数据'}

            # 计算上涨和下跌时的平均成交量
            avg_up_volume = np.mean(up_volumes)
            avg_down_volume = np.mean(down_volumes)

            # 简化的无量反抽判断：反弹时成交量<下跌时80%
            volume_ratio = avg_up_volume / avg_down_volume if avg_down_volume > 0 else 1.0

            if volume_ratio < 0.8:  # 按报告要求的80%标准
                return {
                    'detected': True,
                    'penalty': 0.2,
                    'description': f'无量反抽(上涨量/下跌量={volume_ratio:.1f})',
                    'volume_ratio': volume_ratio
                }

            return {'detected': False, 'penalty': 0.0, 'description': '反抽量能正常'}

        except Exception as e:
            logger.error(f"无量反抽检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'description': '检测失败'}



    def _analyze_historical_risk_similarity(self, minute_data, history_data):
        """简化的历史风险分析（基于基础指标）"""
        try:
            if len(minute_data) < 10:
                return {'high_risk': False, 'penalty': 0.0, 'description': '数据不足'}

            # 简化的风险特征提取
            volumes = minute_data['volume'].values
            closes = minute_data['close'].values

            # 基础风险指标
            early_volume = np.mean(volumes[:5]) if len(volumes) >= 5 else 0
            late_volume = np.mean(volumes[-5:]) if len(volumes) >= 5 else 0
            volume_decline = (early_volume - late_volume) / early_volume if early_volume > 0 else 0

            # 价格波动率
            price_changes = np.diff(closes) / closes[:-1]
            volatility = np.std(price_changes) if len(price_changes) > 0 else 0

            # 简化的风险判断
            risk_score = 0
            risk_reasons = []

            # 量能衰竭风险
            if volume_decline > 0.6:
                risk_score += 0.3
                risk_reasons.append('量能严重衰竭')
            elif volume_decline > 0.4:
                risk_score += 0.2
                risk_reasons.append('量能明显衰竭')

            # 波动率异常
            if volatility > 0.08:
                risk_score += 0.2
                risk_reasons.append('波动率异常')

            # 判断风险等级
            if risk_score >= 0.4:
                return {
                    'high_risk': True,
                    'penalty': min(risk_score, 0.3),  # 最大扣分0.3
                    'description': f'历史风险模式匹配: {"; ".join(risk_reasons)}',
                    'risk_level': 'high'
                }
            elif risk_score >= 0.2:
                return {
                    'high_risk': True,
                    'penalty': min(risk_score, 0.2),
                    'description': f'轻度历史风险: {"; ".join(risk_reasons)}',
                    'risk_level': 'medium'
                }

            return {
                'high_risk': False,
                'penalty': 0.0,
                'description': '历史风险较低',
                'risk_level': 'safe'
            }

        except Exception as e:
            logger.error(f"历史风险分析失败: {e}")
            return {'high_risk': False, 'penalty': 0.0, 'description': '分析失败'}

    def _analyze_zhaban_syndrome(self, code, minute_data, history_data):
        """分析炸板后遗症（前日炸板评分<60分+今日平开）"""
        try:
            # 检查今日是否平开
            is_flat_open = self._check_flat_open(minute_data, code)
            if not is_flat_open['is_flat']:
                return {
                    'high_risk': False,
                    'penalty': 0.0,
                    'description': '非平开股票',
                    'is_flat_open': False
                }

            # 获取前日数据并计算炸板评分
            prev_day_score = self._get_previous_day_zhaban_score(code)

            # 炸板后遗症判断：前日炸板评分<60分 + 今日平开
            if prev_day_score is not None and prev_day_score < 60:
                penalty = self._calculate_zhaban_syndrome_penalty(prev_day_score, is_flat_open)
                return {
                    'high_risk': True,
                    'penalty': penalty,
                    'description': f'炸板后遗症(前日评分{prev_day_score:.0f}分+今日平开)',
                    'prev_day_score': prev_day_score,
                    'is_flat_open': True,
                    'flat_open_details': is_flat_open
                }

            return {
                'high_risk': False,
                'penalty': 0.0,
                'description': f'前日评分正常({prev_day_score:.0f}分)' if prev_day_score else '无前日数据',
                'prev_day_score': prev_day_score,
                'is_flat_open': True,
                'flat_open_details': is_flat_open
            }

        except Exception as e:
            logger.error(f"炸板后遗症分析失败: {e}")
            return {'high_risk': False, 'penalty': 0.0, 'description': '分析失败'}

    def _check_open_type(self, minute_data, code=None):
        """检查开盘类型（平开/高开）并返回相应信息"""
        try:
            if len(minute_data) < 1:
                return {'type': 'unknown', 'reason': '数据不足'}

            # 获取开盘价（9:30的开盘价）
            open_price = minute_data['open'].iloc[0]
            current_price = minute_data['close'].iloc[-1]  # 用于显示当前状态

            # 尝试从分时数据中获取前收盘价
            prev_close = None
            if 'prev_close' in minute_data.columns:
                prev_close = minute_data['prev_close'].iloc[0]
            elif '昨收盘' in minute_data.columns:
                prev_close = minute_data['昨收盘'].iloc[0]

            # 如果分时数据中没有前收盘价，尝试通过数据接口获取
            if (not prev_close or prev_close <= 0) and REAL_DATA_AVAILABLE and code:
                try:
                    prev_close = real_data_interface.get_prev_close_price(code)
                    if prev_close > 0:
                        logger.info(f"✅ 通过数据接口获取{code}前收盘价: {prev_close:.2f}")
                except Exception as e:
                    logger.warning(f"通过数据接口获取前收盘价失败: {e}")

            if prev_close and prev_close > 0:
                # 计算开盘涨跌幅
                open_change_pct = (open_price - prev_close) / prev_close
                current_change_pct = (current_price - prev_close) / prev_close

                # 判断开盘类型
                if abs(open_change_pct) < 0.03:  # 开盘涨跌幅 < 3%
                    open_type = 'flat'
                    type_desc = '平开'
                elif open_change_pct >= 0.03:  # 开盘涨幅 >= 3%
                    open_type = 'high'
                    type_desc = '高开'
                else:  # 开盘跌幅 >= 3%
                    open_type = 'low'
                    type_desc = '低开'

                return {
                    'type': open_type,
                    'type_desc': type_desc,
                    'open_change_pct': open_change_pct,  # 开盘涨跌幅
                    'current_change_pct': current_change_pct,  # 当前涨跌幅
                    'open_price': open_price,
                    'current_price': current_price,
                    'prev_close': prev_close,
                    'reason': f'开盘涨跌幅{open_change_pct:.1%}({type_desc})，当前涨跌幅{current_change_pct:.1%}',
                    # 兼容性字段
                    'is_flat': open_type == 'flat',
                    'is_high': open_type == 'high'
                }
            else:
                # 如果没有前收盘价，无法准确判断开盘类型
                logger.warning("缺少前收盘价，无法准确判断开盘类型")
                return {
                    'type': 'unknown',
                    'type_desc': '未知',
                    'open_price': open_price,
                    'current_price': current_price,
                    'prev_close': None,
                    'reason': '缺少前收盘价，无法判断开盘类型',
                    'is_flat': False,
                    'is_high': False
                }

        except Exception as e:
            logger.error(f"开盘类型检查失败: {e}")
            return {'type': 'unknown', 'reason': '检查失败', 'is_flat': False, 'is_high': False}

    def _check_flat_open(self, minute_data, code=None):
        """检查是否为平开股票（兼容性方法）"""
        result = self._check_open_type(minute_data, code)
        return {
            'is_flat': result.get('is_flat', False),
            'open_change_pct': result.get('open_change_pct', 0),
            'current_change_pct': result.get('current_change_pct', 0),
            'open_price': result.get('open_price', 0),
            'current_price': result.get('current_price', 0),
            'prev_close': result.get('prev_close', 0),
            'reason': result.get('reason', '检查失败')
        }

    def _get_previous_day_zhaban_score(self, code):
        """获取前日炸板评分（使用真实数据接口）"""
        try:
            if not REAL_DATA_AVAILABLE:
                logger.warning(f"真实数据接口不可用，无法获取{code}的前日炸板评分")
                return None

            # 获取前一交易日数据
            prev_day_result = real_data_interface.get_previous_day_data(code)

            if prev_day_result.get('data_available', False):
                zhaban_score = prev_day_result.get('zhaban_score', 0)
                target_date = prev_day_result.get('target_date', '未知')
                logger.info(f"✅ 获取{code}前日({target_date})炸板评分: {zhaban_score:.1f}")
                return zhaban_score
            else:
                error_msg = prev_day_result.get('error', '前日数据获取失败')
                logger.warning(f"⚠️ 无法获取{code}前日炸板评分: {error_msg}")
                return None

        except Exception as e:
            logger.error(f"获取前日炸板评分失败: {e}")
            return None

    def _calculate_zhaban_syndrome_penalty(self, prev_day_score, flat_open_info):
        """计算炸板后遗症扣分"""
        try:
            # 基础扣分：根据前日评分确定
            if prev_day_score < 40:
                base_penalty = 0.4  # 严重炸板
            elif prev_day_score < 50:
                base_penalty = 0.3  # 中等炸板
            elif prev_day_score < 60:
                base_penalty = 0.2  # 轻度炸板
            else:
                base_penalty = 0.0  # 不应该到这里

            # 平开程度调整（使用开盘涨跌幅）
            open_change_pct = abs(flat_open_info.get('open_change_pct', 0))
            if open_change_pct < 0.01:  # 真正的平开
                adjustment = 1.2
            elif open_change_pct < 0.02:  # 接近平开
                adjustment = 1.0
            else:  # 勉强平开
                adjustment = 0.8

            final_penalty = base_penalty * adjustment
            return min(final_penalty, 0.5)  # 最大扣分0.5

        except Exception as e:
            logger.error(f"计算炸板后遗症扣分失败: {e}")
            return 0.2  # 默认扣分

    def _analyze_sector_linkage(self, code, minute_data):
        """分析板块联动验证（所属板块指数10:00前涨幅>1.5%）"""
        try:
            # 获取股票所属板块
            sector_info = self._get_stock_sector(code)
            if not sector_info['has_sector']:
                return {
                    'positive_linkage': False,
                    'bonus': 0.0,
                    'description': '无板块信息',
                    'sector_name': None,
                    'sector_performance': None
                }

            # 获取板块表现
            sector_performance = self._get_sector_performance(sector_info['sector_name'])

            # 板块联动判断
            if sector_performance['morning_gain'] > 0.015:  # 10:00前涨幅>1.5%
                bonus = self._calculate_sector_linkage_bonus(sector_performance)
                return {
                    'positive_linkage': True,
                    'bonus': bonus,
                    'description': f'板块联动({sector_info["sector_name"]}涨{sector_performance["morning_gain"]:.1%})',
                    'sector_name': sector_info['sector_name'],
                    'sector_performance': sector_performance
                }
            elif sector_performance['morning_gain'] > 0.01:  # 10:00前涨幅>1%
                bonus = 0.1  # 轻度板块联动
                return {
                    'positive_linkage': True,
                    'bonus': bonus,
                    'description': f'轻度板块联动({sector_info["sector_name"]}涨{sector_performance["morning_gain"]:.1%})',
                    'sector_name': sector_info['sector_name'],
                    'sector_performance': sector_performance
                }
            else:
                return {
                    'positive_linkage': False,
                    'bonus': 0.0,
                    'description': f'板块表现一般({sector_info["sector_name"]}涨{sector_performance["morning_gain"]:.1%})',
                    'sector_name': sector_info['sector_name'],
                    'sector_performance': sector_performance
                }

        except Exception as e:
            logger.error(f"板块联动分析失败: {e}")
            return {'positive_linkage': False, 'bonus': 0.0, 'description': '分析失败'}

    def _get_stock_sector(self, code):
        """获取股票所属板块（使用真实数据接口）"""
        try:
            if not REAL_DATA_AVAILABLE:
                logger.warning(f"真实数据接口不可用，使用代码推断{code}的板块")
                return self._infer_sector_from_code(code)

            # 获取真实的板块信息
            sector_info = real_data_interface.get_sector_info(code)

            if sector_info.get('has_sector', False):
                sector_name = sector_info.get('sector_name', '未知板块')
                logger.info(f"✅ 获取{code}板块信息: {sector_name}")
                return sector_info
            else:
                logger.warning(f"⚠️ 无法获取{code}板块信息，使用代码推断")
                return self._infer_sector_from_code(code)

        except Exception as e:
            logger.error(f"获取股票板块失败: {e}")
            return {'has_sector': False, 'sector_name': None}

    def _infer_sector_from_code(self, code):
        """根据股票代码推断板块"""
        try:
            if code.startswith('000'):
                return {'has_sector': True, 'sector_name': '深市主板'}
            elif code.startswith('002'):
                return {'has_sector': True, 'sector_name': '中小板'}
            elif code.startswith('300'):
                return {'has_sector': True, 'sector_name': '创业板'}
            elif code.startswith('600') or code.startswith('601') or code.startswith('603'):
                return {'has_sector': True, 'sector_name': '沪市主板'}
            elif code.startswith('688'):
                return {'has_sector': True, 'sector_name': '科创板'}
            else:
                return {'has_sector': False, 'sector_name': None}
        except:
            return {'has_sector': False, 'sector_name': None}

    def _get_sector_performance(self, sector_name):
        """获取板块表现（使用真实数据接口）"""
        try:
            if not REAL_DATA_AVAILABLE:
                logger.warning("真实数据接口不可用，使用默认板块表现")
                return {
                    'morning_gain': 0.0,
                    'limit_up_count': 0,
                    'total_stocks': 0,
                    'limit_up_ratio': 0.0,
                    'sector_strength': 'unknown'
                }

            logger.info(f"获取板块{sector_name}表现数据")

            # 使用真实数据接口获取板块表现
            sector_performance = real_data_interface.get_sector_performance(sector_name)

            if sector_performance.get('morning_gain', 0) != 0 or sector_performance.get('total_stocks', 0) > 0:
                logger.info(f"✅ 获取板块{sector_name}表现成功")
                return sector_performance
            else:
                logger.warning(f"⚠️ 板块{sector_name}表现数据为空，使用默认值")
                return {
                    'morning_gain': 0.0,
                    'limit_up_count': 0,
                    'total_stocks': 0,
                    'limit_up_ratio': 0.0,
                    'sector_strength': 'unknown'
                }

        except Exception as e:
            logger.error(f"获取板块表现失败: {e}")
            return {
                'morning_gain': 0.0,
                'limit_up_count': 0,
                'total_stocks': 0,
                'limit_up_ratio': 0.0,
                'sector_strength': 'unknown'
            }

    def _calculate_sector_linkage_bonus(self, sector_performance):
        """计算板块联动加分"""
        try:
            morning_gain = sector_performance['morning_gain']
            limit_up_count = sector_performance['limit_up_count']
            limit_up_ratio = sector_performance['limit_up_ratio']

            # 基础加分：根据板块涨幅
            if morning_gain > 0.03:  # 涨幅>3%
                base_bonus = 0.3
            elif morning_gain > 0.02:  # 涨幅>2%
                base_bonus = 0.2
            elif morning_gain > 0.015:  # 涨幅>1.5%
                base_bonus = 0.15
            else:
                base_bonus = 0.1

            # 涨停股加分
            if limit_up_count >= 3:
                limit_up_bonus = 0.1
            elif limit_up_count >= 1:
                limit_up_bonus = 0.05
            else:
                limit_up_bonus = 0.0

            # 涨停比例加分
            if limit_up_ratio > 0.1:  # 涨停比例>10%
                ratio_bonus = 0.1
            elif limit_up_ratio > 0.05:  # 涨停比例>5%
                ratio_bonus = 0.05
            else:
                ratio_bonus = 0.0

            total_bonus = base_bonus + limit_up_bonus + ratio_bonus
            return min(total_bonus, 0.4)  # 最大加分0.4

        except Exception as e:
            logger.error(f"计算板块联动加分失败: {e}")
            return 0.1  # 默认加分







    def _execute_complete_decision_tree(self, code, minute_data, volume_analysis, price_analysis, time_analysis):
        """执行完整的实战决策树"""
        try:
            decision_path = []
            current_signal = "观望"
            confidence = 0.0
            risk_level = "中等"

            # 第一步：平开股监控（基础条件检查）
            step1_result = self._decision_step1_flat_open_check(minute_data, code)
            decision_path.append(step1_result)

            if not step1_result['passed']:
                return {
                    'final_signal': '不符合平开条件',
                    'confidence': 0.0,
                    'risk_level': '高',
                    'decision_path': decision_path,
                    'reasoning': step1_result['reason']
                }

            # 第二步：9:35量比判断
            step2_result = self._decision_step2_volume_ratio_check(volume_analysis)
            decision_path.append(step2_result)

            if step2_result['signal'] == '准备止损':
                return {
                    'final_signal': '立即止损',
                    'confidence': 0.9,
                    'risk_level': '极高',
                    'decision_path': decision_path,
                    'reasoning': step2_result['reason']
                }
            elif step2_result['signal'] == '重点监控':
                # 第三步：9:40均价线检查
                step3_result = self._decision_step3_avg_price_check(price_analysis, time_analysis)
                decision_path.append(step3_result)

                if step3_result['signal'] == '观望':
                    return {
                        'final_signal': '观望等待',
                        'confidence': 0.5,
                        'risk_level': '中等',
                        'decision_path': decision_path,
                        'reasoning': step3_result['reason']
                    }
                elif step3_result['signal'] == '继续监控':
                    # 第四步：9:50攻击量峰检查
                    step4_result = self._decision_step4_attack_volume_check(volume_analysis, time_analysis)
                    decision_path.append(step4_result)

                    if step4_result['signal'] == '买入信号':
                        return {
                            'final_signal': '买入：现价+2%挂单',
                            'confidence': 0.8,
                            'risk_level': '低',
                            'decision_path': decision_path,
                            'reasoning': step4_result['reason']
                        }
                    else:
                        return {
                            'final_signal': '等待10:00窗口',
                            'confidence': 0.3,
                            'risk_level': '中等',
                            'decision_path': decision_path,
                            'reasoning': step4_result['reason']
                        }

            # 默认观望
            return {
                'final_signal': '观望等待',
                'confidence': 0.3,
                'risk_level': '中等',
                'decision_path': decision_path,
                'reasoning': '未满足明确买入或止损条件'
            }

        except Exception as e:
            logger.error(f"执行完整决策树失败: {e}")
            return {
                'final_signal': '分析失败',
                'confidence': 0.0,
                'risk_level': '未知',
                'decision_path': [],
                'reasoning': '决策树执行出错'
            }

    def _decision_step1_flat_open_check(self, minute_data, code=None):
        """决策步骤1：平开股监控（使用正确的平开检测）"""
        try:
            if len(minute_data) < 1:
                return {
                    'step': '平开股监控',
                    'passed': False,
                    'reason': '数据不足，无法判断开盘情况',
                    'details': {}
                }

            # 使用正确的平开检测方法
            flat_open_result = self._check_flat_open(minute_data, code)

            open_price = minute_data['open'].iloc[0]
            close_price = minute_data['close'].iloc[-1]
            high_price = minute_data['high'].max()
            low_price = minute_data['low'].min()

            # 基本条件检查
            has_volume = minute_data['volume'].sum() > 0  # 有成交量
            price_range = (high_price - low_price) / open_price if open_price > 0 else 0
            has_movement = price_range > 0.005  # 有一定波动（0.5%以上）

            if flat_open_result['is_flat'] and has_volume and has_movement:
                return {
                    'step': '平开股监控',
                    'passed': True,
                    'reason': f'符合平开股条件：{flat_open_result["reason"]}，波动{price_range:.1%}',
                    'details': {
                        'flat_open_result': flat_open_result,
                        'price_range': price_range,
                        'total_volume': minute_data['volume'].sum()
                    }
                }
            else:
                reasons = []
                if not flat_open_result['is_flat']:
                    reasons.append(flat_open_result['reason'])
                if not has_volume:
                    reasons.append('无成交量')
                if not has_movement:
                    reasons.append(f'波动不足({price_range:.1%})')

                return {
                    'step': '平开股监控',
                    'passed': False,
                    'reason': f'不符合平开股条件：{"; ".join(reasons)}',
                    'details': {
                        'flat_open_result': flat_open_result,
                        'price_range': price_range,
                        'total_volume': minute_data['volume'].sum()
                    }
                }

        except Exception as e:
            logger.error(f"平开股监控检查失败: {e}")
            return {
                'step': '平开股监控',
                'passed': False,
                'reason': '检查过程出错',
                'details': {}
            }

    def _decision_step2_volume_ratio_check(self, volume_analysis):
        """决策步骤2：9:35量比判断"""
        try:
            early_volume_ratio = volume_analysis.get('early_volume_ratio', 1.0)
            volume_exhaustion = volume_analysis.get('volume_exhaustion', {})

            # 按报告要求的量比判断逻辑
            if early_volume_ratio > 1.8:
                return {
                    'step': '9:35量比判断',
                    'signal': '重点监控',
                    'reason': f'量比{early_volume_ratio:.1f}倍>1.8，进入重点监控连板池',
                    'details': {
                        'early_volume_ratio': early_volume_ratio,
                        'threshold': 1.8,
                        'next_step': '检查均价线'
                    }
                }
            elif early_volume_ratio < 0.8:
                return {
                    'step': '9:35量比判断',
                    'signal': '准备止损',
                    'reason': f'量比{early_volume_ratio:.1f}倍<0.8，移入风险观察池',
                    'details': {
                        'early_volume_ratio': early_volume_ratio,
                        'threshold': 0.8,
                        'next_step': '准备止损'
                    }
                }
            else:
                # 检查是否有量能衰竭风险
                if volume_exhaustion.get('detected', False):
                    penalty = volume_exhaustion.get('penalty', 0)
                    if penalty >= 0.3:
                        return {
                            'step': '9:35量比判断',
                            'signal': '准备止损',
                            'reason': f'量比{early_volume_ratio:.1f}倍一般，但检测到严重量能衰竭',
                            'details': {
                                'early_volume_ratio': early_volume_ratio,
                                'volume_exhaustion_penalty': penalty,
                                'next_step': '准备止损'
                            }
                        }

                return {
                    'step': '9:35量比判断',
                    'signal': '重点监控',
                    'reason': f'量比{early_volume_ratio:.1f}倍处于观察区间，继续监控',
                    'details': {
                        'early_volume_ratio': early_volume_ratio,
                        'next_step': '检查均价线'
                    }
                }

        except Exception as e:
            logger.error(f"量比判断失败: {e}")
            return {
                'step': '9:35量比判断',
                'signal': '观望',
                'reason': '量比判断过程出错',
                'details': {}
            }

    def _decision_step3_avg_price_check(self, price_analysis, time_analysis):
        """决策步骤3：9:40均价线检查"""
        try:
            avg_price_conquest = price_analysis.get('avg_price_conquest', {})
            dangerous_patterns = price_analysis.get('dangerous_patterns', {})
            decision_tree = time_analysis.get('decision_tree', {})

            # 检查9:40节点表现
            timeline_analysis = decision_tree.get('timeline_analysis', {})
            node_940 = timeline_analysis.get('9:40', {})

            # 均价线征服判断
            conquered = avg_price_conquest.get('conquered', False)
            consecutive_count = avg_price_conquest.get('max_consecutive', 0)

            # 危险形态检查
            has_dangerous_patterns = dangerous_patterns.get('detected', False)
            pattern_penalty = dangerous_patterns.get('penalty', 0)

            # 按报告要求的均价线判断逻辑
            if conquered and consecutive_count >= 3 and not has_dangerous_patterns:
                if node_940.get('signal') == 'positive':
                    return {
                        'step': '9:40均价线检查',
                        'signal': '继续监控',
                        'reason': f'站稳均价线(连续{consecutive_count}根)且9:40节点表现良好',
                        'details': {
                            'conquered': conquered,
                            'consecutive_count': consecutive_count,
                            'node_940_signal': node_940.get('signal'),
                            'next_step': '检查攻击量峰'
                        }
                    }
                else:
                    return {
                        'step': '9:40均价线检查',
                        'signal': '观望',
                        'reason': f'站稳均价线但9:40节点表现一般',
                        'details': {
                            'conquered': conquered,
                            'consecutive_count': consecutive_count,
                            'node_940_signal': node_940.get('signal'),
                            'next_step': '观望等待'
                        }
                    }
            elif has_dangerous_patterns and pattern_penalty >= 0.3:
                return {
                    'step': '9:40均价线检查',
                    'signal': '观望',
                    'reason': f'检测到严重危险价格形态，建议观望',
                    'details': {
                        'conquered': conquered,
                        'dangerous_patterns': has_dangerous_patterns,
                        'pattern_penalty': pattern_penalty,
                        'next_step': '观望等待'
                    }
                }
            else:
                return {
                    'step': '9:40均价线检查',
                    'signal': '观望',
                    'reason': f'未站稳均价线(连续{consecutive_count}根<3)或存在风险',
                    'details': {
                        'conquered': conquered,
                        'consecutive_count': consecutive_count,
                        'next_step': '观望等待'
                    }
                }

        except Exception as e:
            logger.error(f"均价线检查失败: {e}")
            return {
                'step': '9:40均价线检查',
                'signal': '观望',
                'reason': '均价线检查过程出错',
                'details': {}
            }

    def _decision_step4_attack_volume_check(self, volume_analysis, time_analysis):
        """决策步骤4：攻击量峰检查（使用新算法）"""
        try:
            # 使用新的攻击量峰检测结果
            attack_wave_result = volume_analysis.get('attack_wave_result', {})
            has_attack_wave = attack_wave_result.get('has_attack_wave', False)
            max_ratio = attack_wave_result.get('max_ratio', 1.0)
            peak_count = attack_wave_result.get('peak_count', 0)

            golden_window = time_analysis.get('golden_window', {})
            decision_tree = time_analysis.get('decision_tree', {})

            # 检查9:50节点表现
            timeline_analysis = decision_tree.get('timeline_analysis', {})
            node_950 = timeline_analysis.get('9:50', {})

            # 黄金窗口组合信号
            combo_detected = golden_window.get('combo_detected', False)
            combo_score = golden_window.get('combo_score', 0)

            # 按报告要求的攻击量峰判断逻辑（使用新算法）
            if has_attack_wave and max_ratio >= 3.0 and combo_detected and node_950.get('signal') == 'positive':
                return {
                    'step': '攻击量峰检查',
                    'signal': '买入信号',
                    'reason': f'强攻击量峰{max_ratio:.1f}倍({peak_count}个)+黄金窗口组合+9:50节点良好',
                    'details': {
                        'max_ratio': max_ratio,
                        'peak_count': peak_count,
                        'combo_detected': combo_detected,
                        'combo_score': combo_score,
                        'node_950_signal': node_950.get('signal'),
                        'action': '买入：现价+2%挂单'
                    }
                }
            elif has_attack_wave and max_ratio >= 2.0 and combo_detected:
                return {
                    'step': '攻击量峰检查',
                    'signal': '买入信号',
                    'reason': f'中等攻击量峰{max_ratio:.1f}倍({peak_count}个)+黄金窗口组合',
                    'details': {
                        'max_ratio': max_ratio,
                        'peak_count': peak_count,
                        'combo_detected': combo_detected,
                        'combo_score': combo_score,
                        'action': '买入：现价+1%挂单'
                    }
                }
            elif not has_attack_wave or max_ratio < 1.5:
                return {
                    'step': '攻击量峰检查',
                    'signal': '观望',
                    'reason': f'量能不足{max_ratio:.1f}倍，未检测到攻击量峰',
                    'details': {
                        'max_ratio': max_ratio,
                        'has_attack_wave': has_attack_wave,
                        'combo_detected': combo_detected,
                        'action': '等待10:00窗口'
                    }
                }
            else:
                return {
                    'step': '9:50攻击量峰检查',
                    'signal': '观望',
                    'reason': f'攻击量峰{peak_ratio:.1f}倍一般，黄金窗口组合不足',
                    'details': {
                        'peak_ratio': peak_ratio,
                        'combo_detected': combo_detected,
                        'combo_score': combo_score,
                        'action': '等待10:00窗口'
                    }
                }

        except Exception as e:
            logger.error(f"攻击量峰检查失败: {e}")
            return {
                'step': '9:50攻击量峰检查',
                'signal': '观望',
                'reason': '攻击量峰检查过程出错',
                'details': {}
            }











    def _generate_operation_advice(self, code, minute_data, decision_tree_result, total_score, volume_analysis, price_analysis):
        """生成具体的操作建议"""
        try:
            final_signal = decision_tree_result.get('final_signal', '观望')
            confidence = decision_tree_result.get('confidence', 0)
            risk_level = decision_tree_result.get('risk_level', '中等')

            # 获取当前价格信息
            current_price = minute_data['close'].iloc[-1] if len(minute_data) > 0 else 0

            operation_advice = {
                'signal_type': self._classify_signal_type(final_signal),
                'confidence': confidence,
                'risk_level': risk_level,
                'current_price': current_price,
                'recommendations': []
            }

            # 根据信号类型生成具体建议
            if '买入' in final_signal:
                operation_advice.update(self._generate_buy_advice(code, current_price, confidence, total_score, volume_analysis, price_analysis))
            elif '止损' in final_signal:
                operation_advice.update(self._generate_sell_advice(code, current_price, confidence, volume_analysis, price_analysis))
            else:
                operation_advice.update(self._generate_watch_advice(code, current_price, total_score, volume_analysis, price_analysis))

            return operation_advice

        except Exception as e:
            logger.error(f"生成操作建议失败: {e}")
            return {
                'signal_type': '未知',
                'confidence': 0,
                'risk_level': '未知',
                'current_price': 0,
                'recommendations': ['操作建议生成失败']
            }

    def _classify_signal_type(self, final_signal):
        """分类信号类型"""
        if '买入' in final_signal:
            return '买入信号'
        elif '止损' in final_signal:
            return '止损信号'
        elif '观望' in final_signal or '等待' in final_signal:
            return '观望信号'
        else:
            return '其他信号'

    def _generate_buy_advice(self, code, current_price, confidence, total_score, volume_analysis, price_analysis):
        """生成买入操作建议"""
        try:
            advice = {
                'action': '买入',
                'entry_strategy': [],
                'position_management': [],
                'stop_loss': [],
                'risk_control': []
            }

            # 根据置信度确定挂单策略
            if confidence > 0.8:
                premium = 0.02  # 高置信度：现价+2%
                advice['entry_strategy'].append(f"🎯 强势买入：{current_price:.2f} + 2% = {current_price * 1.02:.2f}元挂单")
                advice['entry_strategy'].append("💪 高置信度信号，可适当激进")
            elif confidence > 0.6:
                premium = 0.01  # 中等置信度：现价+1%
                advice['entry_strategy'].append(f"📈 稳健买入：{current_price:.2f} + 1% = {current_price * 1.01:.2f}元挂单")
                advice['entry_strategy'].append("⚖️ 中等置信度，建议稳健操作")
            else:
                premium = 0.005  # 低置信度：现价+0.5%
                advice['entry_strategy'].append(f"🔍 试探买入：{current_price:.2f} + 0.5% = {current_price * 1.005:.2f}元挂单")
                advice['entry_strategy'].append("⚠️ 低置信度，建议小仓位试探")

            # 仓位管理建议
            if total_score > 0.7:
                position_ratio = 0.6  # 高评分：60%仓位
                advice['position_management'].append("🚀 高评分股票，建议仓位：60%")
            elif total_score > 0.5:
                position_ratio = 0.4  # 中等评分：40%仓位
                advice['position_management'].append("📊 中等评分股票，建议仓位：40%")
            else:
                position_ratio = 0.2  # 低评分：20%仓位
                advice['position_management'].append("🔍 低评分股票，建议仓位：20%")

            advice['position_management'].append("📋 分批建仓：首次50%，突破后加仓50%")
            advice['position_management'].append("⏰ 建仓时间：9:50-10:00黄金窗口期")

            # 止损设置
            stop_loss_price = current_price * 0.95  # 5%止损
            advice['stop_loss'].append(f"🛑 止损价位：{stop_loss_price:.2f}元 (跌破5%)")
            advice['stop_loss'].append("📉 止损条件：跌破均价线且量能萎缩")
            advice['stop_loss'].append("⚡ 紧急止损：出现墓碑线或阶梯跌形态")

            # 风险控制
            volume_exhaustion = volume_analysis.get('volume_exhaustion', {})
            dangerous_patterns = price_analysis.get('dangerous_patterns', {})

            if volume_exhaustion.get('detected', False):
                advice['risk_control'].append("⚠️ 注意：检测到量能衰竭风险，密切关注量能变化")

            if dangerous_patterns.get('detected', False):
                advice['risk_control'].append("⚠️ 注意：检测到危险价格形态，加强风险监控")

            advice['risk_control'].append("📊 持续监控：量比、均价线、攻击量峰")
            advice['risk_control'].append("🕐 关键时点：9:35、9:40、9:50决策节点")

            return advice

        except Exception as e:
            logger.error(f"生成买入建议失败: {e}")
            return {
                'action': '买入',
                'entry_strategy': ['买入建议生成失败'],
                'position_management': [],
                'stop_loss': [],
                'risk_control': []
            }

    def _generate_sell_advice(self, code, current_price, confidence, volume_analysis, price_analysis):
        """生成止损操作建议"""
        try:
            advice = {
                'action': '止损',
                'exit_strategy': [],
                'urgency_level': [],
                'risk_analysis': [],
                'follow_up': []
            }

            # 根据置信度确定止损策略
            if confidence > 0.8:
                advice['exit_strategy'].append(f"🚨 立即止损：{current_price:.2f}元市价卖出")
                advice['exit_strategy'].append("⚡ 高置信度预警，建议立即执行")
                advice['urgency_level'].append("🔴 紧急程度：极高")
            elif confidence > 0.6:
                advice['exit_strategy'].append(f"⚠️ 快速止损：{current_price * 0.99:.2f}元限价卖出")
                advice['exit_strategy'].append("🕐 中等置信度预警，尽快执行")
                advice['urgency_level'].append("🚨 紧急程度：高")
            else:
                advice['exit_strategy'].append(f"📉 谨慎止损：{current_price * 0.98:.2f}元限价卖出")
                advice['exit_strategy'].append("🔍 低置信度预警，谨慎观察")
                advice['urgency_level'].append("⚠️ 紧急程度：中等")

            # 分批止损策略
            advice['exit_strategy'].append("📋 分批止损：首次70%，确认趋势后清仓")
            advice['exit_strategy'].append("⏰ 止损时间：立即执行，不要犹豫")

            # 风险分析
            volume_exhaustion = volume_analysis.get('volume_exhaustion', {})
            dangerous_patterns = price_analysis.get('dangerous_patterns', {})
            historical_risk = volume_analysis.get('historical_risk', {})

            if volume_exhaustion.get('detected', False):
                penalty = volume_exhaustion.get('penalty', 0)
                advice['risk_analysis'].append(f"📉 量能衰竭风险：扣分{penalty:.1f}，量能严重不足")

            if dangerous_patterns.get('detected', False):
                penalty = dangerous_patterns.get('penalty', 0)
                patterns = dangerous_patterns.get('signals', [])
                advice['risk_analysis'].append(f"⚠️ 危险价格形态：扣分{penalty:.1f}，{'; '.join(patterns[:2])}")

            if historical_risk.get('high_risk', False):
                penalty = historical_risk.get('penalty', 0)
                description = historical_risk.get('description', '历史风险')
                advice['risk_analysis'].append(f"📊 历史风险分析：扣分{penalty:.1f}，{description}")

            # 炸板后遗症风险
            zhaban_syndrome = volume_analysis.get('zhaban_syndrome', {})
            if zhaban_syndrome.get('high_risk', False):
                penalty = zhaban_syndrome.get('penalty', 0)
                description = zhaban_syndrome.get('description', '炸板后遗症')
                advice['risk_analysis'].append(f"💥 炸板后遗症：扣分{penalty:.1f}，{description}")

            # 板块联动优势
            sector_linkage = volume_analysis.get('sector_linkage', {})
            if sector_linkage.get('positive_linkage', False):
                bonus = sector_linkage.get('bonus', 0)
                description = sector_linkage.get('description', '板块联动')
                advice['entry_strategy'].append(f"🚀 板块联动优势：加分{bonus:.1f}，{description}")

            # 后续跟踪
            advice['follow_up'].append("👀 持续观察：是否出现反弹信号")
            advice['follow_up'].append("📈 重新评估：风险解除后可考虑重新介入")
            advice['follow_up'].append("🔍 学习总结：分析止损原因，优化策略")

            return advice

        except Exception as e:
            logger.error(f"生成止损建议失败: {e}")
            return {
                'action': '止损',
                'exit_strategy': ['止损建议生成失败'],
                'urgency_level': [],
                'risk_analysis': [],
                'follow_up': []
            }

    def _generate_watch_advice(self, code, current_price, total_score, volume_analysis, price_analysis):
        """生成观望操作建议"""
        try:
            advice = {
                'action': '观望',
                'watch_points': [],
                'entry_conditions': [],
                'risk_monitoring': [],
                'timing_strategy': []
            }

            # 观察要点
            advice['watch_points'].append(f"👀 当前价格：{current_price:.2f}元，评分：{total_score:.3f}")
            advice['watch_points'].append("📊 重点关注：量比变化、均价线表现、攻击量峰")

            # 入场条件
            if total_score > 0.4:
                advice['entry_conditions'].append("✅ 量比突破1.5倍且持续放量")
                advice['entry_conditions'].append("✅ 连续3根K线站稳均价线")
                advice['entry_conditions'].append("✅ 出现攻击量峰(>2倍)且价格突破")
            else:
                advice['entry_conditions'].append("⚠️ 当前评分较低，建议等待更明确信号")
                advice['entry_conditions'].append("🔍 关注基本面变化或技术面改善")

            # 风险监控
            volume_exhaustion = volume_analysis.get('volume_exhaustion', {})
            dangerous_patterns = price_analysis.get('dangerous_patterns', {})

            if volume_exhaustion.get('detected', False):
                advice['risk_monitoring'].append("⚠️ 量能衰竭风险：密切关注量能恢复情况")

            if dangerous_patterns.get('detected', False):
                advice['risk_monitoring'].append("⚠️ 价格形态风险：关注是否出现反转信号")

            advice['risk_monitoring'].append("📉 止损信号：跌破关键支撑或出现恶化形态")
            advice['risk_monitoring'].append("🚨 风险加剧：多重风险信号叠加时立即回避")

            # 时机策略
            advice['timing_strategy'].append("⏰ 最佳观察时间：9:35、9:40、9:50关键节点")
            advice['timing_strategy'].append("📅 持续跟踪：每日开盘30分钟内重新评估")
            advice['timing_strategy'].append("🔄 动态调整：根据市场环境和个股表现调整策略")

            return advice

        except Exception as e:
            logger.error(f"生成观望建议失败: {e}")
            return {
                'action': '观望',
                'watch_points': ['观望建议生成失败'],
                'entry_conditions': [],
                'risk_monitoring': [],
                'timing_strategy': []
            }
    
    def _analyze_price_enhanced(self, minute_data):
        """增强版价格分析（包含均价线征服分析）"""
        try:
            if len(minute_data) < 5:
                return {'score': 0.0, 'signals': ['数据不足'], 'avg_price_conquest': False}

            opens = minute_data['open'].values
            closes = minute_data['close'].values
            highs = minute_data['high'].values
            lows = minute_data['low'].values

            signals = []
            score = 0.0

            # 1. 均价线征服分析（连续3根K线站稳分时均价线）- 降低基础要求权重
            avg_price_conquest = self._analyze_avg_price_conquest(minute_data)
            if avg_price_conquest['conquered']:
                score += 0.3  # 降低权重
                signals.append(avg_price_conquest['description'])

            # 2. 低点抬高检测 - 降低基础要求权重
            low_points_rising = self._analyze_low_points_rising(minute_data)
            if low_points_rising['rising']:
                score += 0.2  # 降低权重
                signals.append(low_points_rising['description'])

            # 3. 突破模式检测（9:45前突破平开价+1.5%）- 降低基础要求权重
            breakthrough_analysis = self._analyze_breakthrough_pattern(minute_data)
            if breakthrough_analysis['breakthrough']:
                score += 0.2  # 降低权重
                signals.append(breakthrough_analysis['description'])

            # 4. 危险价格形态识别 - 按报告要求提升风险扣分权重
            dangerous_patterns = self._detect_dangerous_price_patterns(minute_data)
            if dangerous_patterns['detected']:
                penalty = dangerous_patterns['penalty'] * 1.3  # 提升危险形态扣分权重
                score -= penalty
                signals.extend(dangerous_patterns['signals'])

            # 5. 价格崩溃预警形态检测（新增：墓碑线、阶梯跌、均价压制）
            collapse_patterns = self._detect_price_collapse_patterns(minute_data)
            if collapse_patterns['has_collapse_pattern']:
                collapse_penalty = collapse_patterns['severity'] * 0.4  # 崩溃形态严重扣分
                score -= collapse_penalty
                signals.append(f"⚠️ {collapse_patterns['pattern_type']}: {collapse_patterns['description']}")

            # 5. 上涨分钟占比（保留原有逻辑）
            up_minutes = np.sum(closes > opens)
            up_ratio = up_minutes / len(closes) if len(closes) > 0 else 0

            if up_ratio > 0.6:
                score += 0.2
                signals.append(f'上涨占比{up_ratio:.1%}')
            elif up_ratio > 0.5:
                score += 0.1
                signals.append(f'上涨占比{up_ratio:.1%}')

            description = '; '.join(signals) if signals else '价格表现一般'

            return {
                'score': max(0.0, min(score, 1.0)),
                'signals': signals,
                'description': description,
                'avg_price_conquest': avg_price_conquest,
                'low_points_rising': low_points_rising,
                'breakthrough_analysis': breakthrough_analysis,
                'dangerous_patterns': dangerous_patterns,
                'price_collapse': collapse_patterns,  # 新增价格崩溃信息
                'up_ratio': up_ratio
            }

        except Exception as e:
            logger.error(f"增强价格分析失败: {e}")
            return {'score': 0.0, 'signals': ['分析失败'], 'avg_price_conquest': False}

    def _detect_dangerous_price_patterns(self, minute_data):
        """检测危险价格形态"""
        try:
            dangerous_signals = []
            total_penalty = 0.0
            detected = False

            # 1. 墓碑线检测
            tombstone = self._detect_tombstone_pattern(minute_data)
            if tombstone['detected']:
                detected = True
                total_penalty += tombstone['penalty']
                dangerous_signals.append(tombstone['description'])

            # 2. 阶梯跌检测
            stair_decline = self._detect_stair_decline_pattern(minute_data)
            if stair_decline['detected']:
                detected = True
                total_penalty += stair_decline['penalty']
                dangerous_signals.append(stair_decline['description'])

            # 3. 均价压制检测
            avg_price_suppression = self._detect_avg_price_suppression(minute_data)
            if avg_price_suppression['detected']:
                detected = True
                total_penalty += avg_price_suppression['penalty']
                dangerous_signals.append(avg_price_suppression['description'])

            # 4. 高位震荡检测
            high_oscillation = self._detect_high_oscillation_pattern(minute_data)
            if high_oscillation['detected']:
                detected = True
                total_penalty += high_oscillation['penalty']
                dangerous_signals.append(high_oscillation['description'])

            return {
                'detected': detected,
                'penalty': min(total_penalty, 0.8),  # 最大扣分0.8
                'signals': dangerous_signals,
                'tombstone': tombstone,
                'stair_decline': stair_decline,
                'avg_price_suppression': avg_price_suppression,
                'high_oscillation': high_oscillation
            }

        except Exception as e:
            logger.error(f"危险价格形态检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'signals': ['检测失败']}

    def _detect_tombstone_pattern(self, minute_data):
        """检测墓碑线形态（首分钟冲高2%后回落破开盘价）"""
        try:
            if len(minute_data) < 3:
                return {'detected': False, 'penalty': 0.0, 'description': '数据不足'}

            opens = minute_data['open'].values
            closes = minute_data['close'].values
            highs = minute_data['high'].values
            lows = minute_data['low'].values

            open_price = opens[0]
            first_high = highs[0]
            first_close = closes[0]

            # 检查首分钟冲高幅度
            first_surge = (first_high - open_price) / open_price if open_price > 0 else 0

            # 检查首分钟回落幅度
            first_decline = (first_close - first_high) / first_high if first_high > 0 else 0

            # 检查后续是否破开盘价
            subsequent_lows = lows[1:5] if len(lows) > 5 else lows[1:]
            broke_open = np.any(subsequent_lows < open_price) if len(subsequent_lows) > 0 else False

            # 墓碑线判断
            if first_surge > 0.015 and first_decline < -0.01 and broke_open:  # 冲高1.5%，回落1%，破开盘价
                return {
                    'detected': True,
                    'penalty': 0.4,
                    'description': f'墓碑线形态(冲高{first_surge:.1%}后破开盘价)',
                    'first_surge': first_surge,
                    'first_decline': first_decline,
                    'broke_open': broke_open
                }
            elif first_surge > 0.01 and first_decline < -0.005 and broke_open:  # 较轻的墓碑线
                return {
                    'detected': True,
                    'penalty': 0.2,
                    'description': f'轻度墓碑线(冲高{first_surge:.1%}后破开盘价)',
                    'first_surge': first_surge,
                    'first_decline': first_decline,
                    'broke_open': broke_open
                }

            return {'detected': False, 'penalty': 0.0, 'description': '无墓碑线形态'}

        except Exception as e:
            logger.error(f"墓碑线检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'description': '检测失败'}

    def _detect_stair_decline_pattern(self, minute_data):
        """检测阶梯跌形态（连续3根K线低点下移）"""
        try:
            if len(minute_data) < 5:
                return {'detected': False, 'penalty': 0.0, 'description': '数据不足'}

            lows = minute_data['low'].values
            highs = minute_data['high'].values

            # 检查连续低点下移
            consecutive_decline = 0
            max_consecutive = 0

            for i in range(1, len(lows)):
                if lows[i] < lows[i-1]:
                    consecutive_decline += 1
                    max_consecutive = max(max_consecutive, consecutive_decline)
                else:
                    consecutive_decline = 0

            # 检查高点也在下移（更严重的阶梯跌）
            high_decline_count = 0
            for i in range(1, min(5, len(highs))):
                if highs[i] < highs[i-1]:
                    high_decline_count += 1

            # 阶梯跌判断
            if max_consecutive >= 4 and high_decline_count >= 3:
                return {
                    'detected': True,
                    'penalty': 0.4,
                    'description': f'严重阶梯跌(连续{max_consecutive}根低点下移+高点下移)',
                    'max_consecutive': max_consecutive,
                    'high_decline_count': high_decline_count
                }
            elif max_consecutive >= 3:
                return {
                    'detected': True,
                    'penalty': 0.3,
                    'description': f'阶梯跌形态(连续{max_consecutive}根低点下移)',
                    'max_consecutive': max_consecutive,
                    'high_decline_count': high_decline_count
                }
            elif max_consecutive >= 2 and high_decline_count >= 2:
                return {
                    'detected': True,
                    'penalty': 0.2,
                    'description': f'轻度阶梯跌(连续{max_consecutive}根低点下移)',
                    'max_consecutive': max_consecutive,
                    'high_decline_count': high_decline_count
                }

            return {'detected': False, 'penalty': 0.0, 'description': '无阶梯跌形态'}

        except Exception as e:
            logger.error(f"阶梯跌检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'description': '检测失败'}

    def _detect_avg_price_suppression(self, minute_data):
        """检测均价压制形态（三次上攻均价线失败）"""
        try:
            if len(minute_data) < 8:
                return {'detected': False, 'penalty': 0.0, 'description': '数据不足'}

            # 计算分时均价线
            total_amount = (minute_data['close'] * minute_data['volume']).sum()
            total_volume = minute_data['volume'].sum()
            avg_price = total_amount / total_volume if total_volume > 0 else minute_data['close'].mean()

            closes = minute_data['close'].values
            highs = minute_data['high'].values

            # 寻找上攻均价线的尝试
            attack_attempts = []
            for i in range(1, len(closes)):
                # 检查是否从均价线下方向上攻击
                if closes[i-1] < avg_price and highs[i] > avg_price:
                    # 检查攻击是否失败（收盘价回到均价线下方）
                    failed = closes[i] < avg_price
                    attack_attempts.append({
                        'index': i,
                        'high': highs[i],
                        'close': closes[i],
                        'failed': failed
                    })

            # 统计失败的攻击次数
            failed_attacks = [att for att in attack_attempts if att['failed']]

            # 检查连续失败的攻击
            consecutive_failures = 0
            max_consecutive_failures = 0

            for i in range(len(attack_attempts)):
                if attack_attempts[i]['failed']:
                    consecutive_failures += 1
                    max_consecutive_failures = max(max_consecutive_failures, consecutive_failures)
                else:
                    consecutive_failures = 0

            # 均价压制判断
            if len(failed_attacks) >= 4 and max_consecutive_failures >= 3:
                return {
                    'detected': True,
                    'penalty': 0.4,
                    'description': f'严重均价压制({len(failed_attacks)}次攻击失败)',
                    'failed_attacks': len(failed_attacks),
                    'max_consecutive_failures': max_consecutive_failures,
                    'avg_price': avg_price
                }
            elif len(failed_attacks) >= 3:
                return {
                    'detected': True,
                    'penalty': 0.3,
                    'description': f'均价压制({len(failed_attacks)}次攻击失败)',
                    'failed_attacks': len(failed_attacks),
                    'max_consecutive_failures': max_consecutive_failures,
                    'avg_price': avg_price
                }
            elif max_consecutive_failures >= 3:
                return {
                    'detected': True,
                    'penalty': 0.2,
                    'description': f'均价压制(连续{max_consecutive_failures}次失败)',
                    'failed_attacks': len(failed_attacks),
                    'max_consecutive_failures': max_consecutive_failures,
                    'avg_price': avg_price
                }

            return {'detected': False, 'penalty': 0.0, 'description': '无均价压制'}

        except Exception as e:
            logger.error(f"均价压制检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'description': '检测失败'}

    def _detect_high_oscillation_pattern(self, minute_data):
        """检测高位震荡形态（高位反复震荡无突破）"""
        try:
            if len(minute_data) < 10:
                return {'detected': False, 'penalty': 0.0, 'description': '数据不足'}

            highs = minute_data['high'].values
            lows = minute_data['low'].values
            closes = minute_data['close'].values
            opens = minute_data['open'].values

            # 计算价格区间
            max_high = np.max(highs)
            min_low = np.min(lows)
            price_range = max_high - min_low

            # 定义高位区间（最高价的90%以上）
            high_threshold = max_high - price_range * 0.1

            # 统计在高位区间的时间
            high_time_count = 0
            oscillation_count = 0

            for i in range(len(closes)):
                if closes[i] > high_threshold:
                    high_time_count += 1

                    # 检查是否有震荡（上下波动）
                    if i > 0 and i < len(closes) - 1:
                        prev_close = closes[i-1]
                        next_close = closes[i+1] if i+1 < len(closes) else closes[i]

                        # 震荡判断：前后价格变化方向相反
                        if (closes[i] > prev_close and next_close < closes[i]) or \
                           (closes[i] < prev_close and next_close > closes[i]):
                            oscillation_count += 1

            high_time_ratio = high_time_count / len(closes)
            oscillation_ratio = oscillation_count / high_time_count if high_time_count > 0 else 0

            # 检查是否有有效突破
            recent_highs = highs[-5:] if len(highs) >= 5 else highs
            breakthrough = np.any(recent_highs > max_high * 1.01)  # 突破前高1%

            # 高位震荡判断
            if high_time_ratio > 0.6 and oscillation_ratio > 0.5 and not breakthrough:
                return {
                    'detected': True,
                    'penalty': 0.3,
                    'description': f'高位震荡({high_time_ratio:.1%}时间在高位+{oscillation_ratio:.1%}震荡)',
                    'high_time_ratio': high_time_ratio,
                    'oscillation_ratio': oscillation_ratio,
                    'breakthrough': breakthrough
                }
            elif high_time_ratio > 0.5 and not breakthrough:
                return {
                    'detected': True,
                    'penalty': 0.2,
                    'description': f'高位滞涨({high_time_ratio:.1%}时间在高位无突破)',
                    'high_time_ratio': high_time_ratio,
                    'oscillation_ratio': oscillation_ratio,
                    'breakthrough': breakthrough
                }

            return {'detected': False, 'penalty': 0.0, 'description': '无高位震荡'}

        except Exception as e:
            logger.error(f"高位震荡检测失败: {e}")
            return {'detected': False, 'penalty': 0.0, 'description': '检测失败'}

    def _analyze_avg_price_conquest(self, minute_data):
        """分析均价线征服（连续3根K线站稳分时均价线）"""
        try:
            if len(minute_data) < 3:
                return {'conquered': False, 'description': '数据不足'}

            # 计算分时均价线（成交额加权平均价）
            total_amount = (minute_data['close'] * minute_data['volume']).sum()
            total_volume = minute_data['volume'].sum()
            avg_price = total_amount / total_volume if total_volume > 0 else minute_data['close'].mean()

            closes = minute_data['close'].values

            # 检查连续3根K线站稳均价线
            consecutive_above = 0
            max_consecutive = 0

            for i in range(len(closes)):
                if closes[i] > avg_price:
                    consecutive_above += 1
                    max_consecutive = max(max_consecutive, consecutive_above)
                else:
                    consecutive_above = 0

            # 判断是否征服均价线
            if max_consecutive >= 3:
                return {
                    'conquered': True,
                    'description': f'均价线征服(连续{max_consecutive}根)',
                    'avg_price': avg_price,
                    'max_consecutive': max_consecutive
                }

            return {
                'conquered': False,
                'description': f'未征服均价线(最多{max_consecutive}根)',
                'avg_price': avg_price,
                'max_consecutive': max_consecutive
            }

        except Exception as e:
            logger.error(f"均价线征服分析失败: {e}")
            return {'conquered': False, 'description': '分析失败'}

    def _analyze_low_points_rising(self, minute_data):
        """分析低点抬高（每波回调低点>前低）"""
        try:
            if len(minute_data) < 6:
                return {'rising': False, 'description': '数据不足'}

            lows = minute_data['low'].values

            # 寻找局部低点（前后都比它高的点）
            local_lows = []
            for i in range(1, len(lows) - 1):
                if lows[i] <= lows[i-1] and lows[i] <= lows[i+1]:
                    local_lows.append((i, lows[i]))

            if len(local_lows) < 2:
                return {'rising': False, 'description': '低点不足'}

            # 检查低点是否抬高
            rising_count = 0
            for i in range(1, len(local_lows)):
                if local_lows[i][1] > local_lows[i-1][1]:
                    rising_count += 1

            # 至少2次验证
            if rising_count >= 2:
                return {
                    'rising': True,
                    'description': f'低点抬高({rising_count}次验证)',
                    'low_points': local_lows,
                    'rising_count': rising_count
                }

            return {
                'rising': False,
                'description': f'低点未抬高({rising_count}次)',
                'low_points': local_lows,
                'rising_count': rising_count
            }

        except Exception as e:
            logger.error(f"低点抬高分析失败: {e}")
            return {'rising': False, 'description': '分析失败'}

    def _analyze_breakthrough_pattern(self, minute_data):
        """分析突破模式（9:45前突破平开价+1.5%）"""
        try:
            if len(minute_data) < 5:
                return {'breakthrough': False, 'description': '数据不足'}

            # 检查是否有时间列
            if 'time' not in minute_data.columns:
                # 没有时间列，用前15分钟数据估算
                early_data = minute_data.iloc[:15] if len(minute_data) >= 15 else minute_data
            else:
                # 筛选9:45前的数据
                time_strs = minute_data['time'].astype(str)
                early_mask = time_strs.str.contains('09:[3-4][0-5]', na=False)
                early_data = minute_data[early_mask]

                if len(early_data) == 0:
                    early_data = minute_data.iloc[:15] if len(minute_data) >= 15 else minute_data

            if len(early_data) == 0:
                return {'breakthrough': False, 'description': '无早盘数据'}

            # 计算突破情况
            open_price = minute_data['open'].iloc[0]
            max_price = early_data['high'].max()
            breakthrough_pct = (max_price - open_price) / open_price if open_price > 0 else 0

            # 判断是否突破1.5%
            if breakthrough_pct > 0.015:  # 1.5%
                return {
                    'breakthrough': True,
                    'description': f'早盘突破{breakthrough_pct:.1%}',
                    'open_price': open_price,
                    'max_price': max_price,
                    'breakthrough_pct': breakthrough_pct
                }

            return {
                'breakthrough': False,
                'description': f'突破不足{breakthrough_pct:.1%}',
                'open_price': open_price,
                'max_price': max_price,
                'breakthrough_pct': breakthrough_pct
            }

        except Exception as e:
            logger.error(f"突破模式分析失败: {e}")
            return {'breakthrough': False, 'description': '分析失败'}
    
    def _analyze_time_enhanced(self, minute_data):
        """增强版时间分析（包含决策树逻辑）"""
        try:
            if len(minute_data) < 10:
                return {'score': 0.0, 'signals': ['数据不足'], 'decision_tree': {}}

            signals = []
            score = 0.0

            # 1. 实现9:35→9:40→9:50决策树逻辑 - 按报告要求提升权重
            decision_tree = self._analyze_decision_tree_timeline(minute_data)
            if decision_tree['positive_signals'] > 0:
                score += 0.5 * (decision_tree['positive_signals'] / 3)  # 提升决策树权重
                signals.extend(decision_tree['descriptions'])

            # 2. 黄金窗口组合信号检测（9:40-9:50）- 按报告要求提升权重
            golden_window = self._analyze_golden_window_signals(minute_data)
            if golden_window['combo_detected']:
                score += 0.5  # 提升黄金窗口权重
                signals.append(golden_window['description'])

            # 3. 早盘表现分析（前10分钟）- 按报告要求提升权重
            early_performance = self._analyze_early_performance(minute_data)
            if early_performance['strong']:
                score += 0.4  # 提升早盘表现权重
                signals.append(early_performance['description'])

            # 4. 关键时段综合评估
            key_periods = self._analyze_key_periods(minute_data)
            if key_periods['favorable']:
                score += 0.2
                signals.append(key_periods['description'])

            description = '; '.join(signals) if signals else '时间表现一般'

            return {
                'score': max(0.0, min(score, 1.0)),
                'signals': signals,
                'description': description,
                'decision_tree': decision_tree,
                'golden_window': golden_window,
                'early_performance': early_performance,
                'key_periods': key_periods
            }

        except Exception as e:
            logger.error(f"增强时间分析失败: {e}")
            return {'score': 0.0, 'signals': ['分析失败'], 'decision_tree': {}}

    def _analyze_decision_tree_timeline(self, minute_data):
        """分析9:35→9:40→9:50决策树逻辑"""
        try:
            positive_signals = 0
            descriptions = []
            timeline_analysis = {}

            # 检查是否有时间列
            if 'time' not in minute_data.columns:
                # 没有时间列，用位置估算
                return self._analyze_decision_tree_by_position(minute_data)

            time_strs = minute_data['time'].astype(str)

            # 9:35节点分析
            node_935 = self._analyze_935_node(minute_data, time_strs)
            timeline_analysis['9:35'] = node_935
            if node_935['signal'] == 'positive':
                positive_signals += 1
                descriptions.append(node_935['description'])

            # 9:40节点分析
            node_940 = self._analyze_940_node(minute_data, time_strs)
            timeline_analysis['9:40'] = node_940
            if node_940['signal'] == 'positive':
                positive_signals += 1
                descriptions.append(node_940['description'])

            # 9:50节点分析
            node_950 = self._analyze_950_node(minute_data, time_strs)
            timeline_analysis['9:50'] = node_950
            if node_950['signal'] == 'positive':
                positive_signals += 1
                descriptions.append(node_950['description'])

            return {
                'positive_signals': positive_signals,
                'descriptions': descriptions,
                'timeline_analysis': timeline_analysis,
                'decision_path': self._generate_decision_path(timeline_analysis)
            }

        except Exception as e:
            logger.error(f"决策树时间线分析失败: {e}")
            return {'positive_signals': 0, 'descriptions': ['决策树分析失败'], 'timeline_analysis': {}}

    def _analyze_935_node(self, minute_data, time_strs):
        """分析9:35节点（量比检查点）"""
        try:
            # 筛选9:30-9:35数据
            early_mask = time_strs.str.contains('09:3[0-5]', na=False)
            early_data = minute_data[early_mask]

            if len(early_data) < 3:
                return {'signal': 'neutral', 'description': '9:35数据不足'}

            # 检查量比表现
            early_volume = early_data['volume'].sum()
            avg_volume = minute_data['volume'].mean()
            volume_ratio = early_volume / (avg_volume * len(early_data)) if avg_volume > 0 else 1.0

            # 检查价格表现
            price_up_count = np.sum(early_data['close'] > early_data['open'])
            price_ratio = price_up_count / len(early_data)

            # 决策逻辑
            if volume_ratio > 1.5 and price_ratio > 0.6:
                return {
                    'signal': 'positive',
                    'description': f'9:35量价齐升(量比{volume_ratio:.1f})',
                    'volume_ratio': volume_ratio,
                    'price_ratio': price_ratio
                }
            elif volume_ratio < 0.8:
                return {
                    'signal': 'negative',
                    'description': f'9:35量能不足(量比{volume_ratio:.1f})',
                    'volume_ratio': volume_ratio,
                    'price_ratio': price_ratio
                }

            return {
                'signal': 'neutral',
                'description': f'9:35表现平平(量比{volume_ratio:.1f})',
                'volume_ratio': volume_ratio,
                'price_ratio': price_ratio
            }

        except Exception as e:
            logger.error(f"9:35节点分析失败: {e}")
            return {'signal': 'neutral', 'description': '9:35分析失败'}

    def _analyze_940_node(self, minute_data, time_strs):
        """分析9:40节点（均价线检查点）"""
        try:
            # 筛选9:30-9:40数据
            mid_mask = time_strs.str.contains('09:3[0-9]|09:40', na=False)
            mid_data = minute_data[mid_mask]

            if len(mid_data) < 5:
                return {'signal': 'neutral', 'description': '9:40数据不足'}

            # 计算均价线
            total_amount = (mid_data['close'] * mid_data['volume']).sum()
            total_volume = mid_data['volume'].sum()
            avg_price = total_amount / total_volume if total_volume > 0 else mid_data['close'].mean()

            # 检查最近3分钟是否站稳均价线
            recent_data = mid_data.tail(3)
            above_avg_count = np.sum(recent_data['close'] > avg_price)

            # 检查价格突破
            open_price = minute_data['open'].iloc[0]
            current_price = mid_data['close'].iloc[-1]
            breakthrough_pct = (current_price - open_price) / open_price if open_price > 0 else 0

            # 决策逻辑
            if above_avg_count >= 2 and breakthrough_pct > 0.01:
                return {
                    'signal': 'positive',
                    'description': f'9:40站稳均价线+突破{breakthrough_pct:.1%}',
                    'above_avg_count': above_avg_count,
                    'breakthrough_pct': breakthrough_pct
                }
            elif above_avg_count == 0:
                return {
                    'signal': 'negative',
                    'description': '9:40未站稳均价线',
                    'above_avg_count': above_avg_count,
                    'breakthrough_pct': breakthrough_pct
                }

            return {
                'signal': 'neutral',
                'description': f'9:40表现一般(均价线{above_avg_count}/3)',
                'above_avg_count': above_avg_count,
                'breakthrough_pct': breakthrough_pct
            }

        except Exception as e:
            logger.error(f"9:40节点分析失败: {e}")
            return {'signal': 'neutral', 'description': '9:40分析失败'}

    def _analyze_950_node(self, minute_data, time_strs):
        """分析9:50节点（攻击量峰检查点）"""
        try:
            # 筛选9:40-9:50数据
            late_mask = time_strs.str.contains('09:4[0-9]|09:50', na=False)
            late_data = minute_data[late_mask]

            if len(late_data) < 3:
                return {'signal': 'neutral', 'description': '9:50数据不足'}

            # 检查攻击量峰
            max_volume = late_data['volume'].max()
            avg_volume = minute_data['volume'].mean()
            peak_ratio = max_volume / avg_volume if avg_volume > 0 else 1.0

            # 检查价格配合
            max_vol_idx = late_data['volume'].idxmax()
            max_vol_price_up = late_data.loc[max_vol_idx, 'close'] > late_data.loc[max_vol_idx, 'open']

            # 检查持续性
            recent_up_count = np.sum(late_data.tail(3)['close'] > late_data.tail(3)['open'])

            # 决策逻辑
            if peak_ratio > 2.0 and max_vol_price_up and recent_up_count >= 2:
                return {
                    'signal': 'positive',
                    'description': f'9:50攻击量峰{peak_ratio:.1f}倍+价格配合',
                    'peak_ratio': peak_ratio,
                    'price_cooperation': max_vol_price_up,
                    'recent_up_count': recent_up_count
                }
            elif peak_ratio < 1.2:
                return {
                    'signal': 'negative',
                    'description': f'9:50量能萎缩{peak_ratio:.1f}倍',
                    'peak_ratio': peak_ratio,
                    'price_cooperation': max_vol_price_up,
                    'recent_up_count': recent_up_count
                }

            return {
                'signal': 'neutral',
                'description': f'9:50量能一般{peak_ratio:.1f}倍',
                'peak_ratio': peak_ratio,
                'price_cooperation': max_vol_price_up,
                'recent_up_count': recent_up_count
            }

        except Exception as e:
            logger.error(f"9:50节点分析失败: {e}")
            return {'signal': 'neutral', 'description': '9:50分析失败'}

    def _analyze_decision_tree_by_position(self, minute_data):
        """基于位置的决策树分析（无时间列时使用）"""
        try:
            positive_signals = 0
            descriptions = []

            # 前5分钟（模拟9:35）
            if len(minute_data) >= 5:
                early_data = minute_data.iloc[:5]
                early_up = np.sum(early_data['close'] > early_data['open'])
                if early_up >= 3:
                    positive_signals += 1
                    descriptions.append(f'早期表现良好({early_up}/5)')

            # 前10分钟（模拟9:40）
            if len(minute_data) >= 10:
                mid_data = minute_data.iloc[:10]
                avg_price = mid_data['close'].mean()
                recent_above = np.sum(mid_data.tail(3)['close'] > avg_price)
                if recent_above >= 2:
                    positive_signals += 1
                    descriptions.append(f'中期站稳均价({recent_above}/3)')

            # 前20分钟（模拟9:50）
            if len(minute_data) >= 20:
                late_data = minute_data.iloc[10:20]
                max_vol = late_data['volume'].max()
                avg_vol = minute_data['volume'].mean()
                if max_vol > avg_vol * 1.5:
                    positive_signals += 1
                    descriptions.append(f'后期量能放大{max_vol/avg_vol:.1f}倍')

            return {
                'positive_signals': positive_signals,
                'descriptions': descriptions,
                'timeline_analysis': {'method': 'position_based'},
                'decision_path': f'{positive_signals}/3个正面信号'
            }

        except Exception as e:
            logger.error(f"位置决策树分析失败: {e}")
            return {'positive_signals': 0, 'descriptions': ['位置分析失败'], 'timeline_analysis': {}}

    def _generate_decision_path(self, timeline_analysis):
        """生成决策路径描述"""
        try:
            path_parts = []
            for time_point in ['9:35', '9:40', '9:50']:
                if time_point in timeline_analysis:
                    signal = timeline_analysis[time_point]['signal']
                    if signal == 'positive':
                        path_parts.append(f"{time_point}✅")
                    elif signal == 'negative':
                        path_parts.append(f"{time_point}❌")
                    else:
                        path_parts.append(f"{time_point}➖")

            return " → ".join(path_parts) if path_parts else "无决策路径"

        except Exception as e:
            logger.error(f"生成决策路径失败: {e}")
            return "路径生成失败"

    def _analyze_golden_window_signals(self, minute_data):
        """分析黄金窗口组合信号（9:40-9:50）"""
        try:
            if 'time' not in minute_data.columns:
                # 无时间列，用位置估算（第10-20分钟）
                if len(minute_data) >= 20:
                    golden_data = minute_data.iloc[10:20]
                else:
                    return {'combo_detected': False, 'description': '黄金窗口数据不足'}
            else:
                # 筛选9:40-9:50数据
                time_strs = minute_data['time'].astype(str)
                golden_mask = time_strs.str.contains('09:4[0-9]|09:50', na=False)
                golden_data = minute_data[golden_mask]

                if len(golden_data) < 5:
                    return {'combo_detected': False, 'description': '黄金窗口数据不足'}

            # 组合信号检测
            combo_score = 0
            signals = []

            # 信号1: 成交量>开盘首分钟
            first_minute_vol = minute_data['volume'].iloc[0]
            max_golden_vol = golden_data['volume'].max()
            if max_golden_vol > first_minute_vol:
                combo_score += 1
                signals.append('量能超首分钟')

            # 信号2: 价格突破早盘高点
            early_high = minute_data['high'].iloc[:10].max() if len(minute_data) >= 10 else minute_data['high'].iloc[0]
            golden_high = golden_data['high'].max()
            if golden_high > early_high:
                combo_score += 1
                signals.append('突破早盘高点')

            # 信号3: 回调不破均价线
            total_amount = (minute_data['close'] * minute_data['volume']).sum()
            total_volume = minute_data['volume'].sum()
            avg_price = total_amount / total_volume if total_volume > 0 else minute_data['close'].mean()

            golden_lows = golden_data['low']
            below_avg_count = np.sum(golden_lows < avg_price)
            if below_avg_count <= 1:  # 最多1次破均价线
                combo_score += 1
                signals.append('守住均价线')

            # 判断组合信号
            if combo_score >= 2:
                return {
                    'combo_detected': True,
                    'description': f'黄金窗口组合({combo_score}/3): {"; ".join(signals)}',
                    'combo_score': combo_score,
                    'signals': signals
                }

            return {
                'combo_detected': False,
                'description': f'黄金窗口不足({combo_score}/3)',
                'combo_score': combo_score,
                'signals': signals
            }

        except Exception as e:
            logger.error(f"黄金窗口分析失败: {e}")
            return {'combo_detected': False, 'description': '黄金窗口分析失败'}

    def _analyze_early_performance(self, minute_data):
        """分析早盘表现（前10分钟）"""
        try:
            early_data = minute_data.iloc[:10] if len(minute_data) >= 10 else minute_data

            if len(early_data) < 5:
                return {'strong': False, 'description': '早盘数据不足'}

            # 上涨分钟占比
            up_count = np.sum(early_data['close'] > early_data['open'])
            up_ratio = up_count / len(early_data)

            # 量能表现
            early_volume = early_data['volume'].sum()
            total_volume = minute_data['volume'].sum()
            volume_ratio = early_volume / total_volume if total_volume > 0 else 0

            # 价格表现
            open_price = early_data['open'].iloc[0]
            end_price = early_data['close'].iloc[-1]
            price_change = (end_price - open_price) / open_price if open_price > 0 else 0

            # 综合判断
            if up_ratio > 0.6 and volume_ratio > 0.3 and price_change > 0.005:  # 0.5%
                return {
                    'strong': True,
                    'description': f'早盘强势(上涨{up_ratio:.1%}+量能{volume_ratio:.1%})',
                    'up_ratio': up_ratio,
                    'volume_ratio': volume_ratio,
                    'price_change': price_change
                }

            return {
                'strong': False,
                'description': f'早盘一般(上涨{up_ratio:.1%})',
                'up_ratio': up_ratio,
                'volume_ratio': volume_ratio,
                'price_change': price_change
            }

        except Exception as e:
            logger.error(f"早盘表现分析失败: {e}")
            return {'strong': False, 'description': '早盘分析失败'}

    def _analyze_key_periods(self, minute_data):
        """分析关键时段综合评估"""
        try:
            favorable_count = 0
            period_signals = []

            # 开盘5分钟
            if len(minute_data) >= 5:
                opening_data = minute_data.iloc[:5]
                opening_up = np.sum(opening_data['close'] > opening_data['open'])
                if opening_up >= 3:
                    favorable_count += 1
                    period_signals.append('开盘强势')

            # 中段10-15分钟
            if len(minute_data) >= 15:
                mid_data = minute_data.iloc[10:15]
                mid_volume = mid_data['volume'].mean()
                total_avg_volume = minute_data['volume'].mean()
                if mid_volume > total_avg_volume * 1.1:
                    favorable_count += 1
                    period_signals.append('中段放量')

            # 后段15-20分钟
            if len(minute_data) >= 20:
                late_data = minute_data.iloc[15:20]
                late_high = late_data['high'].max()
                total_high = minute_data['high'].max()
                if late_high >= total_high * 0.95:  # 接近最高点
                    favorable_count += 1
                    period_signals.append('后段冲高')

            if favorable_count >= 2:
                return {
                    'favorable': True,
                    'description': f'关键时段有利({favorable_count}/3): {"; ".join(period_signals)}',
                    'favorable_count': favorable_count,
                    'period_signals': period_signals
                }

            return {
                'favorable': False,
                'description': f'关键时段一般({favorable_count}/3)',
                'favorable_count': favorable_count,
                'period_signals': period_signals
            }

        except Exception as e:
            logger.error(f"关键时段分析失败: {e}")
            return {'favorable': False, 'description': '关键时段分析失败'}

    def _generate_report(self, results, target_date):
        """生成分析报告"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            report_file = f"连板潜力股实时评分报告_{target_date}_{timestamp}.txt"

            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("=" * 80 + "\n")
                f.write("🔍 连板潜力股实时评分报告 (简化版)\n")
                f.write("=" * 80 + "\n")
                f.write(f"📅 分析日期: {target_date}\n")
                f.write(f"⏰ 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"📊 分析股票数: {len(results)}只\n")
                f.write("\n")

                if not results:
                    f.write("⚠️ 未获得有效分析结果\n")
                    return report_file

                # 评分排序结果
                f.write("📈 实时评分排序\n")
                f.write("-" * 80 + "\n")

                for i, result in enumerate(results, 1):
                    algorithm_type = result.get('algorithm_type', 'flat_open')
                    algorithm_desc = {
                        'flat_open': '平开算法',
                        'high_open': '高开算法',
                        'basic': '基础算法'
                    }.get(algorithm_type, '未知算法')

                    f.write(f"{i:2d}. {result['code']} | 评分: {result['total_score']:.3f} | 评级: {result.get('grade', result.get('rating', 'C'))} | 算法: {algorithm_desc}\n")

                    # 根据算法类型显示不同信息
                    if algorithm_type == 'high_open':
                        self._write_high_open_report_section(f, result)
                    elif algorithm_type == 'flat_open':
                        self._write_flat_open_report_section(f, result)
                    else:
                        f.write(f"    {result.get('summary', '基础分析')}\n")

                    # 添加详细量能分析信息
                    if 'volume_analysis' in result:
                        vol_analysis = result['volume_analysis']
                        f.write(f"    量比: {vol_analysis.get('volume_ratio_current', 1.0):.1f} | ")
                        f.write(f"开盘量比: {vol_analysis.get('early_volume_ratio', 1.0):.1f} | ")
                        f.write(f"攻击量峰: {vol_analysis.get('peak_ratio', 1.0):.1f}\n")

                        # 添加量能衰竭风险信息
                        volume_exhaustion = vol_analysis.get('volume_exhaustion', {})
                        if volume_exhaustion.get('detected', False):
                            penalty = volume_exhaustion.get('penalty', 0)
                            exhaustion_signals = volume_exhaustion.get('signals', [])
                            f.write(f"    ⚠️ 量能衰竭风险 (扣分: {penalty:.1f}): {'; '.join(exhaustion_signals)}\n")

                        # 添加历史风险对比信息
                        historical_risk = vol_analysis.get('historical_risk', {})
                        if historical_risk.get('high_risk', False):
                            penalty = historical_risk.get('penalty', 0)
                            description = historical_risk.get('description', '历史风险')
                            f.write(f"    📊 历史风险分析 (扣分: {penalty:.1f}): {description}\n")

                        # 添加炸板后遗症信息
                        zhaban_syndrome = vol_analysis.get('zhaban_syndrome', {})
                        if zhaban_syndrome.get('high_risk', False):
                            penalty = zhaban_syndrome.get('penalty', 0)
                            description = zhaban_syndrome.get('description', '炸板后遗症')
                            prev_score = zhaban_syndrome.get('prev_day_score', 0)
                            f.write(f"    💥 炸板后遗症 (扣分: {penalty:.1f}): {description}\n")
                        elif zhaban_syndrome.get('is_flat_open', False):
                            description = zhaban_syndrome.get('description', '平开正常')
                            f.write(f"    ✅ 炸板检查: {description}\n")

                        # 添加板块联动信息
                        sector_linkage = vol_analysis.get('sector_linkage', {})
                        if sector_linkage.get('positive_linkage', False):
                            bonus = sector_linkage.get('bonus', 0)
                            description = sector_linkage.get('description', '板块联动')
                            sector_name = sector_linkage.get('sector_name', '未知板块')
                            f.write(f"    🚀 板块联动 (加分: {bonus:.1f}): {description}\n")
                        else:
                            description = sector_linkage.get('description', '板块表现一般')
                            f.write(f"    📊 板块分析: {description}\n")

                        f.write(f"    量能信号: {vol_analysis.get('description', '无')}\n")

                    # 添加详细价格分析信息
                    if 'price_analysis' in result:
                        price_analysis = result['price_analysis']
                        f.write(f"    均价线征服: {'是' if price_analysis.get('avg_price_conquest', {}).get('conquered', False) else '否'} | ")
                        f.write(f"低点抬高: {'是' if price_analysis.get('low_points_rising', {}).get('rising', False) else '否'} | ")
                        f.write(f"早盘突破: {'是' if price_analysis.get('breakthrough_analysis', {}).get('breakthrough', False) else '否'}\n")

                        # 添加危险价格形态风险信息
                        dangerous_patterns = price_analysis.get('dangerous_patterns', {})
                        if dangerous_patterns.get('detected', False):
                            penalty = dangerous_patterns.get('penalty', 0)
                            pattern_signals = dangerous_patterns.get('signals', [])
                            f.write(f"    ⚠️ 危险价格形态 (扣分: {penalty:.1f}): {'; '.join(pattern_signals)}\n")

                        f.write(f"    价格信号: {price_analysis.get('description', '无')}\n")

                    # 添加详细时间分析信息
                    if 'time_analysis' in result:
                        time_analysis = result['time_analysis']
                        decision_tree = time_analysis.get('decision_tree', {})
                        f.write(f"    决策路径: {decision_tree.get('decision_path', '无')} | ")
                        f.write(f"正面信号: {decision_tree.get('positive_signals', 0)}/3 | ")
                        f.write(f"黄金窗口: {'是' if time_analysis.get('golden_window', {}).get('combo_detected', False) else '否'}\n")
                        f.write(f"    时间信号: {time_analysis.get('description', '无')}\n")

                    # 添加实战决策树信息
                    if 'decision_tree' in result:
                        dt_result = result['decision_tree']
                        final_signal = dt_result.get('final_signal', '无')
                        confidence = dt_result.get('confidence', 0)
                        risk_level = dt_result.get('risk_level', '未知')
                        reasoning = dt_result.get('reasoning', '无')

                        f.write(f"    🎯 实战决策: {final_signal} | 置信度: {confidence:.1%} | 风险等级: {risk_level}\n")
                        f.write(f"    决策依据: {reasoning}\n")

                        # 添加决策路径详情
                        decision_path = dt_result.get('decision_path', [])
                        if decision_path:
                            f.write(f"    决策步骤:\n")
                            for i, step in enumerate(decision_path, 1):
                                step_name = step.get('step', f'步骤{i}')
                                step_result = step.get('signal', step.get('passed', '未知'))
                                step_reason = step.get('reason', '无')
                                f.write(f"      {i}. {step_name}: {step_result} - {step_reason}\n")

                    # 添加详细操作建议
                    if 'operation_advice' in result:
                        op_advice = result['operation_advice']
                        action = op_advice.get('action', '无')
                        signal_type = op_advice.get('signal_type', '无')

                        f.write(f"    💡 操作建议: {action} ({signal_type})\n")
                        f.write("-" * 80 + "\n")

                        if action == '买入':
                            # 买入建议详情
                            entry_strategy = op_advice.get('entry_strategy', [])
                            position_management = op_advice.get('position_management', [])
                            stop_loss = op_advice.get('stop_loss', [])
                            risk_control = op_advice.get('risk_control', [])

                            if entry_strategy:
                                f.write("    📈 买入策略:\n")
                                for strategy in entry_strategy:
                                    f.write(f"      {strategy}\n")

                            if position_management:
                                f.write("    📊 仓位管理:\n")
                                for management in position_management:
                                    f.write(f"      {management}\n")

                            if stop_loss:
                                f.write("    🛑 止损设置:\n")
                                for sl in stop_loss:
                                    f.write(f"      {sl}\n")

                            if risk_control:
                                f.write("    ⚠️ 风险控制:\n")
                                for rc in risk_control:
                                    f.write(f"      {rc}\n")

                        elif action == '止损':
                            # 止损建议详情
                            exit_strategy = op_advice.get('exit_strategy', [])
                            urgency_level = op_advice.get('urgency_level', [])
                            risk_analysis = op_advice.get('risk_analysis', [])
                            follow_up = op_advice.get('follow_up', [])

                            if exit_strategy:
                                f.write("    🚨 止损策略:\n")
                                for strategy in exit_strategy:
                                    f.write(f"      {strategy}\n")

                            if urgency_level:
                                f.write("    ⚡ 紧急程度:\n")
                                for level in urgency_level:
                                    f.write(f"      {level}\n")

                            if risk_analysis:
                                f.write("    📉 风险分析:\n")
                                for analysis in risk_analysis:
                                    f.write(f"      {analysis}\n")

                            if follow_up:
                                f.write("    🔍 后续跟踪:\n")
                                for fu in follow_up:
                                    f.write(f"      {fu}\n")

                        else:
                            # 观望建议详情
                            watch_points = op_advice.get('watch_points', [])
                            entry_conditions = op_advice.get('entry_conditions', [])
                            risk_monitoring = op_advice.get('risk_monitoring', [])
                            timing_strategy = op_advice.get('timing_strategy', [])

                            if watch_points:
                                f.write("    👀 观察要点:\n")
                                for point in watch_points:
                                    f.write(f"      {point}\n")

                            if entry_conditions:
                                f.write("    ✅ 入场条件:\n")
                                for condition in entry_conditions:
                                    f.write(f"      {condition}\n")

                            if risk_monitoring:
                                f.write("    ⚠️ 风险监控:\n")
                                for monitor in risk_monitoring:
                                    f.write(f"      {monitor}\n")

                            if timing_strategy:
                                f.write("    ⏰ 时机策略:\n")
                                for timing in timing_strategy:
                                    f.write(f"      {timing}\n")

                f.write("\n")

                # 操作建议
                f.write("💡 操作建议\n")
                f.write("=" * 80 + "\n")

                a_plus_stocks = [r for r in results if r.get('grade', r.get('rating', 'C')) == 'A+']
                a_stocks = [r for r in results if r.get('grade', r.get('rating', 'C')) == 'A']
                b_plus_stocks = [r for r in results if r.get('grade', r.get('rating', 'C')) == 'B+']

                if a_plus_stocks:
                    f.write("🎯 强烈推荐 (A+级):\n")
                    for stock in a_plus_stocks:
                        f.write(f"   • {stock['code']} - 评分: {stock['total_score']:.3f}\n")
                    f.write("\n")

                if a_stocks:
                    f.write("✅ 推荐关注 (A级):\n")
                    for stock in a_stocks:
                        f.write(f"   • {stock['code']} - 评分: {stock['total_score']:.3f}\n")
                    f.write("\n")

                if b_plus_stocks:
                    f.write("👀 重点观察 (B+级):\n")
                    for stock in b_plus_stocks:
                        f.write(f"   • {stock['code']} - 评分: {stock['total_score']:.3f}\n")
                    f.write("\n")

                # 分析说明
                f.write("📋 评分说明\n")
                f.write("-" * 80 + "\n")
                f.write("量能分析(45%): 基于攻击量峰、开盘放量、量比崩塔等核心指标\n")
                f.write("时间分析(35%): 基于9:35/9:40/9:50决策路径、黄金窗口等关键时点\n")
                f.write("价格分析(20%): 基于均价线征服、低点抬高、危险形态等基础指标\n")
                f.write("\n")
                f.write("评级标准:\n")
                f.write("A+ (≥0.8): 强烈推荐，连板潜力极高\n")
                f.write("A  (≥0.7): 推荐关注，连板潜力较高\n")
                f.write("B+ (≥0.6): 重点观察，有一定潜力\n")
                f.write("B  (≥0.5): 谨慎观望，潜力一般\n")
                f.write("C  (<0.5): 建议回避，潜力较低\n")
                f.write("\n")
                f.write("📝 免责声明: 本报告仅供参考，不构成投资建议。投资有风险，入市需谨慎。\n")

            logger.info(f"✅ 分析报告已生成: {report_file}")
            return report_file

        except Exception as e:
            logger.error(f"生成报告失败: {e}")
            return ""

    def _detect_attack_volume_waves(self, minute_data, history_data):
        """检测攻击量峰（按报告要求：单分钟量能>前日同期300%）"""
        try:
            if len(minute_data) < 5 or history_data is None or len(history_data) == 0:
                return {
                    'has_attack_wave': False,
                    'max_ratio': 1.0,
                    'peak_count': 0,
                    'peak_times': []
                }

            # 确保有时间列
            if 'time' not in minute_data.columns:
                minute_data = minute_data.copy()
                minute_data['time'] = pd.date_range(start='09:30', periods=len(minute_data), freq='1min').strftime('%H:%M')

            attack_peaks = []
            max_ratio = 1.0

            # 遍历每分钟数据
            for i, row in minute_data.iterrows():
                current_time = row.get('time', f'09:{30+i}')
                current_volume = row.get('volume', 0)

                if current_volume <= 0:
                    continue

                # 计算前日同期平均量（取前5日同时段平均）
                try:
                    # 提取时间（格式：HH:MM）
                    time_str = str(current_time)
                    if ':' in time_str:
                        hour_min = time_str.split(':')
                        if len(hour_min) == 2:
                            target_time = f"{hour_min[0].zfill(2)}:{hour_min[1].zfill(2)}"
                        else:
                            target_time = current_time
                    else:
                        target_time = current_time

                    # 从历史数据中找同时段数据
                    if 'time' in history_data.columns:
                        same_time_data = history_data[history_data['time'].astype(str).str.contains(target_time, na=False)]
                    else:
                        # 如果没有时间列，按位置估算
                        if i < len(history_data):
                            same_time_data = history_data.iloc[i:i+1]
                        else:
                            same_time_data = pd.DataFrame()

                    if len(same_time_data) > 0:
                        avg_historical_volume = same_time_data['volume'].mean()
                        if avg_historical_volume > 0:
                            ratio = current_volume / avg_historical_volume
                            max_ratio = max(max_ratio, ratio)

                            # 检测攻击量峰（>300%即3.0倍）
                            if ratio >= 3.0:
                                attack_peaks.append({
                                    'time': target_time,
                                    'ratio': ratio,
                                    'volume': current_volume
                                })

                except Exception as e:
                    logger.debug(f"处理时间{current_time}的量峰检测失败: {e}")
                    continue

            # 按比例排序，取前3个最强的量峰
            attack_peaks.sort(key=lambda x: x['ratio'], reverse=True)
            peak_times = [peak['time'] for peak in attack_peaks[:3]]

            return {
                'has_attack_wave': len(attack_peaks) > 0,
                'max_ratio': max_ratio,
                'peak_count': len(attack_peaks),
                'peak_times': peak_times,
                'attack_peaks': attack_peaks[:3]  # 详细信息
            }

        except Exception as e:
            logger.error(f"攻击量峰检测失败: {e}")
            return {
                'has_attack_wave': False,
                'max_ratio': 1.0,
                'peak_count': 0,
                'peak_times': []
            }

    def _detect_price_collapse_patterns(self, minute_data):
        """检测价格崩溃预警形态（墓碑线、阶梯跌、均价压制）"""
        try:
            if len(minute_data) < 5:
                return {
                    'has_collapse_pattern': False,
                    'pattern_type': None,
                    'severity': 0.0,
                    'description': '数据不足'
                }

            prices = minute_data['close'].values
            opens = minute_data['open'].values
            highs = minute_data['high'].values if 'high' in minute_data.columns else prices
            lows = minute_data['low'].values if 'low' in minute_data.columns else prices

            # 计算均价线
            avg_price = np.mean(prices)

            patterns_detected = []
            max_severity = 0.0

            # 1. 墓碑线检测：首分钟冲高2%后回落破开盘价
            if len(prices) >= 2:
                first_high = highs[0]
                first_open = opens[0]
                current_price = prices[-1]

                if first_open > 0:
                    # 首分钟冲高幅度
                    first_surge = (first_high - first_open) / first_open
                    # 当前相对开盘价的位置
                    current_vs_open = (current_price - first_open) / first_open

                    if first_surge >= 0.02 and current_vs_open < 0:  # 冲高2%后破开盘价
                        severity = min(abs(current_vs_open) * 5, 1.0)  # 跌幅越大严重程度越高
                        patterns_detected.append({
                            'type': '墓碑线',
                            'severity': severity,
                            'description': f'首分钟冲高{first_surge:.1%}后回落破开盘价{current_vs_open:.1%}'
                        })
                        max_severity = max(max_severity, severity)

            # 2. 阶梯跌检测：连续3根1分钟K线低点下移
            if len(lows) >= 3:
                consecutive_lower_lows = 0
                for i in range(1, len(lows)):
                    if lows[i] < lows[i-1]:
                        consecutive_lower_lows += 1
                    else:
                        consecutive_lower_lows = 0

                    if consecutive_lower_lows >= 3:
                        # 计算下跌幅度
                        decline_range = (lows[i-3] - lows[i]) / lows[i-3] if lows[i-3] > 0 else 0
                        severity = min(decline_range * 10, 1.0)  # 下跌幅度转换为严重程度
                        patterns_detected.append({
                            'type': '阶梯跌',
                            'severity': severity,
                            'description': f'连续{consecutive_lower_lows+1}根低点下移'
                        })
                        max_severity = max(max_severity, severity)
                        break

            # 3. 均价压制检测：三次上攻均价线失败
            if len(prices) >= 6:
                attack_failures = 0
                for i in range(1, len(prices)):
                    # 检测是否尝试突破均价线
                    if prices[i-1] < avg_price and prices[i] >= avg_price:
                        # 检查后续是否回落
                        if i < len(prices) - 1 and prices[i+1] < avg_price:
                            attack_failures += 1

                if attack_failures >= 3:
                    # 计算压制强度
                    above_avg_ratio = np.sum(prices > avg_price) / len(prices)
                    severity = min((1 - above_avg_ratio) * 2, 1.0)
                    patterns_detected.append({
                        'type': '均价压制',
                        'severity': severity,
                        'description': f'三次上攻均价线失败，压制强度{severity:.1%}'
                    })
                    max_severity = max(max_severity, severity)

            # 返回最严重的形态
            if patterns_detected:
                most_severe = max(patterns_detected, key=lambda x: x['severity'])
                return {
                    'has_collapse_pattern': True,
                    'pattern_type': most_severe['type'],
                    'severity': most_severe['severity'],
                    'description': most_severe['description'],
                    'all_patterns': patterns_detected
                }
            else:
                return {
                    'has_collapse_pattern': False,
                    'pattern_type': None,
                    'severity': 0.0,
                    'description': '未检测到崩溃形态'
                }

        except Exception as e:
            logger.error(f"价格崩溃形态检测失败: {e}")
            return {
                'has_collapse_pattern': False,
                'pattern_type': None,
                'severity': 0.0,
                'description': '检测失败'
            }

    def _make_trading_decision(self, code, analysis_result):
        """制定交易决策（买入/观望）"""
        try:
            score = analysis_result.get('score', 0)
            volume_analysis = analysis_result.get('volume_analysis', {})
            price_analysis = analysis_result.get('price_analysis', {})
            time_analysis = analysis_result.get('time_analysis', {})

            # 决策因子
            decision_factors = {
                'score': score,
                'has_attack_wave': False,
                'volume_ratio_good': False,
                'price_pattern_good': False,
                'time_window_good': False,
                'has_risk_pattern': False,
                'confidence': 0.0
            }

            # 1. 检查攻击量峰
            if 'attack_wave_result' in volume_analysis:
                attack_wave = volume_analysis['attack_wave_result']
                if attack_wave.get('has_attack_wave', False) and attack_wave.get('max_ratio', 0) >= 3.0:
                    decision_factors['has_attack_wave'] = True
                    decision_factors['confidence'] += 0.3

            # 2. 检查量比条件
            volume_ratio = volume_analysis.get('volume_ratio_current', 1.0)
            if volume_ratio >= 1.8:
                decision_factors['volume_ratio_good'] = True
                decision_factors['confidence'] += 0.25

            # 3. 检查价格形态
            price_signals = price_analysis.get('signals', [])
            positive_price_signals = [s for s in price_signals if any(keyword in s for keyword in ['征服', '抬高', '突破'])]
            if len(positive_price_signals) >= 2:
                decision_factors['price_pattern_good'] = True
                decision_factors['confidence'] += 0.2

            # 4. 检查时间窗口（开盘30分钟内的关键信号）
            time_signals = time_analysis.get('signals', [])
            if any('黄金窗口' in s or '关键时段' in s for s in time_signals):
                decision_factors['time_window_good'] = True
                decision_factors['confidence'] += 0.15

            # 5. 检查风险形态
            if 'price_collapse' in price_analysis:
                collapse_pattern = price_analysis['price_collapse']
                if collapse_pattern.get('has_collapse_pattern', False):
                    decision_factors['has_risk_pattern'] = True
                    decision_factors['confidence'] -= collapse_pattern.get('severity', 0) * 0.5

            # 量能衰竭风险
            if 'volume_exhaustion' in volume_analysis:
                exhaustion = volume_analysis['volume_exhaustion']
                if exhaustion.get('detected', False):
                    decision_factors['has_risk_pattern'] = True
                    decision_factors['confidence'] -= exhaustion.get('penalty', 0) * 0.3

            # 决策逻辑
            decision = self._calculate_final_decision(decision_factors)

            return decision

        except Exception as e:
            logger.error(f"制定交易决策失败: {e}")
            return {
                'action': '观望',
                'confidence': 0.0,
                'reason': '决策计算失败',
                'risk_level': '高'
            }

    def _calculate_final_decision(self, factors):
        """计算最终决策"""
        try:
            confidence = max(0.0, min(1.0, factors['confidence']))

            # 连板潜力股识别条件（按报告要求）
            buy_conditions = [
                factors['has_attack_wave'],  # 攻击量峰
                factors['volume_ratio_good'],  # 量比>1.8
                factors['price_pattern_good'],  # 价格形态良好
                factors['score'] >= 0.6  # 基础评分
            ]

            # 风险排除条件
            risk_conditions = [
                factors['has_risk_pattern'],  # 有风险形态
                factors['score'] < 0.3,  # 评分过低
                confidence < 0.2  # 置信度过低
            ]

            # 决策逻辑
            buy_signal_count = sum(buy_conditions)
            risk_signal_count = sum(risk_conditions)

            if risk_signal_count > 0:
                action = '观望'
                reason = '检测到风险信号'
                risk_level = '高'
            elif buy_signal_count >= 3:
                action = '买入'
                reason = f'连板潜力股信号确认({buy_signal_count}/4)'
                risk_level = '中' if confidence >= 0.6 else '中高'
            elif buy_signal_count >= 2:
                action = '观望'
                reason = f'部分信号确认({buy_signal_count}/4)，等待更多确认'
                risk_level = '中'
            else:
                action = '观望'
                reason = '信号不足，继续观察'
                risk_level = '中高'

            return {
                'action': action,
                'confidence': confidence * 100,  # 转换为百分比
                'reason': reason,
                'risk_level': risk_level,
                'buy_signal_count': buy_signal_count,
                'risk_signal_count': risk_signal_count
            }

        except Exception as e:
            logger.error(f"计算最终决策失败: {e}")
            return {
                'action': '观望',
                'confidence': 0.0,
                'reason': '决策计算失败',
                'risk_level': '高'
            }

    def _analyze_high_open_volume_health(self, minute_data, history_data, current_time_str='9:40'):
        """高开股量能健康度检测（权重40%）"""
        try:
            if len(minute_data) < 5:
                return {
                    'score': 0.0,
                    'signals': ['数据不足'],
                    'health_indicators': {},
                    'passed': False
                }

            # 动态参数调整（根据分析时间）
            if current_time_str == '9:35':
                volume_ratio_threshold = 2.5
            else:  # 9:40及以后
                volume_ratio_threshold = 3.0

            health_indicators = {}
            signals = []
            score = 0.0

            # 1. 量比维持检测
            current_volume_ratio = self._calculate_volume_ratio(minute_data, history_data)
            health_indicators['volume_ratio'] = current_volume_ratio

            if current_volume_ratio >= volume_ratio_threshold:
                score += 0.3  # 30%权重
                signals.append(f'✅ 量比维持{current_volume_ratio:.1f}(≥{volume_ratio_threshold})')
            else:
                signals.append(f'❌ 量比不足{current_volume_ratio:.1f}(<{volume_ratio_threshold})')

            # 2. 量价配合检测
            volume_price_match = self._check_volume_price_coordination(minute_data)
            health_indicators['volume_price_match'] = volume_price_match

            if volume_price_match['up_down_ratio'] >= 2.0:  # 上涨分钟成交量 > 下跌分钟200%
                score += 0.25  # 25%权重
                signals.append(f'✅ 量价配合{volume_price_match["up_down_ratio"]:.1f}倍')
            else:
                signals.append(f'❌ 量价配合不足{volume_price_match["up_down_ratio"]:.1f}倍(<2.0)')

            # 3. 最大量能柱位置检测
            max_volume_position = self._check_max_volume_position(minute_data)
            health_indicators['max_volume_position'] = max_volume_position

            if max_volume_position['at_high_price']:
                score += 0.2  # 20%权重
                signals.append('✅ 最大量能出现在价格创新高时')
            else:
                signals.append('❌ 最大量能未出现在高价位')

            # 4. 量能递减率检测
            volume_decay_rate = self._calculate_volume_decay_rate(minute_data)
            health_indicators['volume_decay_rate'] = volume_decay_rate

            if volume_decay_rate >= 0.65:  # 第二分钟量能/首分钟量能 > 65%
                score += 0.25  # 25%权重
                signals.append(f'✅ 量能递减率{volume_decay_rate:.1%}(≥65%)')
            else:
                signals.append(f'❌ 量能递减过快{volume_decay_rate:.1%}(<65%)')

            # 危险信号检测
            danger_signals = []
            if current_volume_ratio < 1.8:
                danger_signals.append('量比快速跌破1.8')
            if volume_price_match.get('max_volume_on_decline', False):
                danger_signals.append('最大量能出现在下跌波段')

            # 一票否决机制
            passed = len(danger_signals) == 0 and score >= 0.6  # 至少3/4指标达标

            return {
                'score': min(score, 1.0),
                'signals': signals,
                'health_indicators': health_indicators,
                'danger_signals': danger_signals,
                'passed': passed,
                'description': f'量能健康度: {score:.1%}' + (f' | 危险信号: {len(danger_signals)}个' if danger_signals else '')
            }

        except Exception as e:
            logger.error(f"高开股量能健康度检测失败: {e}")
            return {
                'score': 0.0,
                'signals': ['检测失败'],
                'health_indicators': {},
                'passed': False
            }

    def _analyze_high_open_price_strength(self, minute_data, open_gain, current_time_str='9:40'):
        """高开股价格强度验证（权重30%）"""
        try:
            if len(minute_data) < 5:
                return {
                    'score': 0.0,
                    'signals': ['数据不足'],
                    'strength_indicators': {},
                    'passed': False
                }

            # 动态参数调整
            if current_time_str == '9:35':
                max_drawdown_threshold = 0.015  # 1.5%
            else:  # 9:40及以后
                max_drawdown_threshold = 0.010  # 1.0%

            strength_indicators = {}
            signals = []
            score = 0

            prices = minute_data['close'].values
            highs = minute_data['high'].values if 'high' in minute_data.columns else prices
            lows = minute_data['low'].values if 'low' in minute_data.columns else prices
            open_price = minute_data['open'].iloc[0]

            # 计算均价线
            avg_price = np.mean(prices)

            # 1. 回撤控制检测（高开股核心）
            max_high = np.max(highs)
            min_low = np.min(lows)
            max_drawdown = (max_high - min_low) / max_high if max_high > 0 else 0
            strength_indicators['max_drawdown'] = max_drawdown

            if max_drawdown <= max_drawdown_threshold:
                score += 50
                signals.append(f'✅ 回撤控制{max_drawdown:.1%}(≤{max_drawdown_threshold:.1%})')
            else:
                signals.append(f'❌ 回撤过大{max_drawdown:.1%}(>{max_drawdown_threshold:.1%})')

            # 2. 均价线征服检测
            above_avg_ratio = np.sum(prices > avg_price) / len(prices)
            strength_indicators['above_avg_ratio'] = above_avg_ratio

            if above_avg_ratio >= 0.8:  # 80%时间在均价线上方
                score += 30
                signals.append(f'✅ 均价线征服{above_avg_ratio:.1%}(≥80%)')
            else:
                signals.append(f'❌ 均价线征服不足{above_avg_ratio:.1%}(<80%)')

            # 3. 创新高能力检测
            current_max = np.max(prices)
            breakthrough_threshold = open_price * (1 + open_gain/100 * 0.8)  # 开盘涨幅的80%
            strength_indicators['breakthrough_ability'] = current_max >= breakthrough_threshold

            if current_max >= breakthrough_threshold:
                score += 20
                signals.append('✅ 具备创新高能力')
            else:
                signals.append('❌ 创新高能力不足')

            # 关键形态识别
            pattern_analysis = self._detect_high_open_patterns(minute_data)
            strength_indicators['pattern_analysis'] = pattern_analysis

            # 阶梯上涨检测
            if pattern_analysis.get('has_ladder_up', False):
                signals.append('✅ 阶梯上涨形态')

            # 钓鱼线检测（危险信号）
            if pattern_analysis.get('has_fishing_line', False):
                score = max(0, score - 30)  # 严重扣分
                signals.append('🚫 钓鱼线形态（急拉后缓慢跌破均价）')

            # 评分达标判断
            passed = score >= 70 and not pattern_analysis.get('has_fishing_line', False)

            return {
                'score': min(score / 100.0, 1.0),  # 转换为0-1分数
                'signals': signals,
                'strength_indicators': strength_indicators,
                'passed': passed,
                'description': f'价格强度: {score}分' + (' | 钓鱼线风险' if pattern_analysis.get('has_fishing_line') else '')
            }

        except Exception as e:
            logger.error(f"高开股价格强度验证失败: {e}")
            return {
                'score': 0.0,
                'signals': ['检测失败'],
                'strength_indicators': {},
                'passed': False
            }

    def _check_volume_price_coordination(self, minute_data):
        """检测量价配合情况"""
        try:
            up_volume = 0
            down_volume = 0
            max_volume = 0
            max_volume_on_decline = False

            for i, row in minute_data.iterrows():
                volume = row.get('volume', 0)
                open_price = row.get('open', 0)
                close_price = row.get('close', 0)

                if volume > max_volume:
                    max_volume = volume
                    max_volume_on_decline = close_price < open_price

                if close_price > open_price:
                    up_volume += volume
                elif close_price < open_price:
                    down_volume += volume

            up_down_ratio = up_volume / down_volume if down_volume > 0 else float('inf')

            return {
                'up_volume': up_volume,
                'down_volume': down_volume,
                'up_down_ratio': up_down_ratio,
                'max_volume_on_decline': max_volume_on_decline
            }

        except Exception as e:
            logger.error(f"量价配合检测失败: {e}")
            return {
                'up_volume': 0,
                'down_volume': 0,
                'up_down_ratio': 1.0,
                'max_volume_on_decline': False
            }

    def _check_max_volume_position(self, minute_data):
        """检测最大量能柱位置"""
        try:
            max_volume = 0
            max_volume_index = 0

            for i, row in minute_data.iterrows():
                volume = row.get('volume', 0)
                if volume > max_volume:
                    max_volume = volume
                    max_volume_index = i

            # 检查最大量能是否出现在价格创新高时
            if max_volume_index < len(minute_data):
                max_vol_row = minute_data.iloc[max_volume_index]
                max_vol_high = max_vol_row.get('high', max_vol_row.get('close', 0))

                # 检查是否是当时的最高价
                prev_data = minute_data.iloc[:max_volume_index+1]
                prev_max_high = prev_data['high'].max() if 'high' in prev_data.columns else prev_data['close'].max()

                at_high_price = abs(max_vol_high - prev_max_high) / prev_max_high < 0.005 if prev_max_high > 0 else False
            else:
                at_high_price = False

            return {
                'max_volume': max_volume,
                'max_volume_index': max_volume_index,
                'at_high_price': at_high_price
            }

        except Exception as e:
            logger.error(f"最大量能柱位置检测失败: {e}")
            return {
                'max_volume': 0,
                'max_volume_index': 0,
                'at_high_price': False
            }

    def _calculate_volume_decay_rate(self, minute_data):
        """计算量能递减率"""
        try:
            if len(minute_data) < 2:
                return 0.0

            first_minute_vol = minute_data['volume'].iloc[0]
            second_minute_vol = minute_data['volume'].iloc[1]

            if first_minute_vol > 0:
                decay_rate = second_minute_vol / first_minute_vol
            else:
                decay_rate = 0.0

            return decay_rate

        except Exception as e:
            logger.error(f"量能递减率计算失败: {e}")
            return 0.0

    def _detect_high_open_patterns(self, minute_data):
        """检测高开股特殊形态"""
        try:
            if len(minute_data) < 5:
                return {
                    'has_ladder_up': False,
                    'has_fishing_line': False
                }

            prices = minute_data['close'].values
            lows = minute_data['low'].values if 'low' in minute_data.columns else prices
            avg_price = np.mean(prices)

            # 阶梯上涨检测：低点连续抬高（3次验证）
            ladder_up_count = 0
            for i in range(2, len(lows)):
                if lows[i] > lows[i-1] and lows[i-1] > lows[i-2]:
                    ladder_up_count += 1

            has_ladder_up = ladder_up_count >= 1  # 至少一次阶梯上涨

            # 钓鱼线检测：急拉后缓慢跌破均价
            has_fishing_line = False
            if len(prices) >= 10:
                # 检查是否有急拉（前30%时间内大幅上涨）
                early_period = int(len(prices) * 0.3)
                early_max = np.max(prices[:early_period])
                early_min = np.min(prices[:early_period])
                early_surge = (early_max - early_min) / early_min if early_min > 0 else 0

                # 检查后续是否缓慢跌破均价
                if early_surge > 0.02:  # 前期有2%以上急拉
                    late_period = prices[early_period:]
                    below_avg_ratio = np.sum(late_period < avg_price) / len(late_period)
                    if below_avg_ratio > 0.6:  # 后期60%时间在均价下方
                        has_fishing_line = True

            return {
                'has_ladder_up': has_ladder_up,
                'has_fishing_line': has_fishing_line,
                'ladder_up_count': ladder_up_count
            }

        except Exception as e:
            logger.error(f"高开股形态检测失败: {e}")
            return {
                'has_ladder_up': False,
                'has_fishing_line': False
            }

    def _analyze_high_open_stock(self, code, minute_data, history_data, open_info):
        """高开股连板潜力分析（开盘涨幅>=3%）"""
        try:
            logger.info(f"🚀 启动高开股算法分析: {code} 开盘涨幅{open_info['open_change_pct']:.1%}")

            # 获取当前时间（模拟9:40分析）
            current_time = datetime.now()
            if current_time.hour == 9 and current_time.minute <= 40:
                current_time_str = '9:35' if current_time.minute <= 35 else '9:40'
            else:
                current_time_str = '9:40'  # 默认使用9:40参数

            open_gain = open_info['open_change_pct'] * 100  # 转换为百分比

            # 1. 量能健康度检测（权重40%）
            volume_health = self._analyze_high_open_volume_health(minute_data, history_data, current_time_str)

            # 2. 价格强度验证（权重30%）
            price_strength = self._analyze_high_open_price_strength(minute_data, open_gain, current_time_str)

            # 3. 板块效应确认（权重20%）
            sector_effect = self._analyze_high_open_sector_effect(code, current_time_str)

            # 4. 股性回溯筛查（权重10%）
            stock_nature = self._analyze_high_open_stock_nature(code)

            # 综合评分计算
            total_score = (
                volume_health['score'] * 0.4 +
                price_strength['score'] * 0.3 +
                sector_effect['score'] * 0.2 +
                stock_nature['score'] * 0.1
            )

            # 决策矩阵判断
            decision = self._make_high_open_decision(volume_health, price_strength, sector_effect, stock_nature)

            # 生成分析报告
            analysis_result = {
                'algorithm_type': 'high_open',
                'code': code,
                'open_info': open_info,
                'analysis_time': current_time_str,
                'total_score': total_score,
                'grade': self._get_score_grade(total_score),
                'decision': decision,
                'volume_health': volume_health,
                'price_strength': price_strength,
                'sector_effect': sector_effect,
                'stock_nature': stock_nature,
                'summary': self._generate_high_open_summary(decision, total_score, volume_health, price_strength)
            }

            logger.info(f"✅ 高开股分析完成: {code} 评分{total_score:.1%} 决策:{decision['action']}")
            return analysis_result

        except Exception as e:
            logger.error(f"高开股分析失败: {e}")
            return {
                'algorithm_type': 'high_open',
                'code': code,
                'error': str(e),
                'total_score': 0.0,
                'decision': {'action': '观望', 'reason': '分析失败', 'confidence': 0}
            }

    def _analyze_high_open_sector_effect(self, code, current_time_str='9:40'):
        """高开股板块效应确认（权重20%）"""
        try:
            # 动态参数调整
            if current_time_str == '9:35':
                sector_gain_threshold = 0.018  # 1.8%
            else:  # 9:40及以后
                sector_gain_threshold = 0.022  # 2.2%

            # 获取板块信息
            sector_info = self._get_stock_sector(code)
            if not sector_info.get('has_sector', False):
                return {
                    'score': 0.0,
                    'signals': ['无板块信息'],
                    'passed': False
                }

            sector_name = sector_info.get('sector_name', '未知板块')

            # 获取板块表现
            sector_performance = self._get_sector_performance(sector_name)

            signals = []
            score = 0.0

            # 板块涨幅检测
            sector_gain = sector_performance.get('morning_gain', 0) / 100
            if sector_gain >= sector_gain_threshold:
                score += 0.6  # 60%权重
                signals.append(f'✅ 板块涨幅{sector_gain:.1%}(≥{sector_gain_threshold:.1%})')
            else:
                signals.append(f'❌ 板块涨幅{sector_gain:.1%}(<{sector_gain_threshold:.1%})')

            # 板块内涨停股检测
            limit_up_count = sector_performance.get('limit_up_count', 0)
            if limit_up_count >= 2:
                score += 0.4  # 40%权重
                signals.append(f'✅ 板块涨停股{limit_up_count}只(≥2只)')
            else:
                signals.append(f'❌ 板块涨停股{limit_up_count}只(<2只)')

            # 弱势板块特征检测
            weak_signals = []
            if sector_gain < -0.015:  # 板块跌幅>1.5%
                weak_signals.append('板块指数冲高回落>1.5%')

            passed = len(weak_signals) == 0 and score >= 0.5

            return {
                'score': min(score, 1.0),
                'signals': signals,
                'weak_signals': weak_signals,
                'passed': passed,
                'sector_name': sector_name,
                'sector_gain': sector_gain,
                'limit_up_count': limit_up_count
            }

        except Exception as e:
            logger.error(f"高开股板块效应分析失败: {e}")
            return {
                'score': 0.0,
                'signals': ['分析失败'],
                'passed': False
            }

    def _analyze_high_open_stock_nature(self, code):
        """高开股股性回溯筛查（权重10%）"""
        try:
            signals = []
            score = 0.0

            # 获取前日炸板评分（如果有的话）
            prev_zhaban_score = self._get_previous_day_zhaban_score(code)

            if prev_zhaban_score is not None:
                if prev_zhaban_score >= 80:
                    score += 0.5
                    signals.append(f'✅ 前日炸板评分{prev_zhaban_score:.0f}(≥80)')
                elif prev_zhaban_score < 50:
                    score -= 0.3
                    signals.append(f'❌ 前日炸板评分{prev_zhaban_score:.0f}(<50)')
                else:
                    signals.append(f'⚠️ 前日炸板评分{prev_zhaban_score:.0f}(中等)')
            else:
                signals.append('⚠️ 无前日炸板评分数据')

            # 基础股性评估（简化实现）
            stock_info = self._get_stock_sector(code)
            if stock_info.get('has_sector', False):
                score += 0.3
                signals.append('✅ 有明确板块归属')

            # 避雷指标检测（简化实现）
            # 实际应用中需要接入龙虎榜数据
            signals.append('⚠️ 龙虎榜数据待接入')

            return {
                'score': max(0.0, min(score, 1.0)),
                'signals': signals,
                'prev_zhaban_score': prev_zhaban_score,
                'passed': score >= 0.3
            }

        except Exception as e:
            logger.error(f"股性回溯分析失败: {e}")
            return {
                'score': 0.0,
                'signals': ['分析失败'],
                'passed': False
            }

    def _make_high_open_decision(self, volume_health, price_strength, sector_effect, stock_nature):
        """高开股决策矩阵"""
        try:
            # 决策因子
            factors = {
                'volume_health_passed': volume_health.get('passed', False),
                'price_strength_passed': price_strength.get('passed', False),
                'sector_effect_passed': sector_effect.get('passed', False),
                'stock_nature_passed': stock_nature.get('passed', False),
                'has_danger_signals': len(volume_health.get('danger_signals', [])) > 0,
                'has_fishing_line': price_strength.get('strength_indicators', {}).get('pattern_analysis', {}).get('has_fishing_line', False),
                'sector_weak': len(sector_effect.get('weak_signals', [])) > 0
            }

            # 一票否决条件
            veto_conditions = [
                factors['has_danger_signals'],  # 量能危险信号
                factors['has_fishing_line'],    # 钓鱼线形态
                factors['sector_weak']          # 板块弱势
            ]

            # 买入触发条件
            buy_conditions = [
                factors['volume_health_passed'],   # 量能健康
                factors['price_strength_passed'],  # 价格强度
                factors['sector_effect_passed'] or factors['stock_nature_passed']  # 板块效应或股性优良
            ]

            # 决策逻辑
            if any(veto_conditions):
                action = '观望'
                reason = '检测到风险信号'
                confidence = 20
            elif all(buy_conditions):
                action = '买入'
                reason = '量能健康+价格强度+板块效应确认'
                confidence = 85
            elif factors['volume_health_passed'] and factors['price_strength_passed']:
                action = '买入'
                reason = '量能健康+价格强度达标'
                confidence = 70
            else:
                action = '观望'
                reason = '核心条件不满足'
                confidence = 30

            return {
                'action': action,
                'reason': reason,
                'confidence': confidence,
                'factors': factors,
                'buy_conditions_met': sum(buy_conditions),
                'veto_conditions_met': sum(veto_conditions)
            }

        except Exception as e:
            logger.error(f"高开股决策制定失败: {e}")
            return {
                'action': '观望',
                'reason': '决策失败',
                'confidence': 0
            }

    def _generate_high_open_summary(self, decision, total_score, volume_health, price_strength):
        """生成高开股分析摘要"""
        try:
            summary_lines = []

            # 决策结果
            action_emoji = '🚀' if decision['action'] == '买入' else '⏸️'
            summary_lines.append(f"{action_emoji} 决策: {decision['action']} (置信度{decision['confidence']}%)")
            summary_lines.append(f"📊 综合评分: {total_score:.1%}")

            # 关键指标
            if volume_health.get('passed', False):
                summary_lines.append("✅ 量能健康度达标")
            else:
                summary_lines.append("❌ 量能健康度不达标")

            if price_strength.get('passed', False):
                summary_lines.append("✅ 价格强度达标")
            else:
                summary_lines.append("❌ 价格强度不达标")

            # 风险提示
            danger_signals = volume_health.get('danger_signals', [])
            if danger_signals:
                summary_lines.append(f"⚠️ 风险信号: {'; '.join(danger_signals)}")

            return '\n'.join(summary_lines)

        except Exception as e:
            logger.error(f"生成高开股摘要失败: {e}")
            return "摘要生成失败"

    def _write_high_open_report_section(self, f, result):
        """写入高开股报告段落"""
        try:
            open_info = result.get('open_info', {})
            f.write(f"    开盘涨幅: {open_info.get('open_change_pct', 0):.1%} | 当前涨幅: {open_info.get('current_change_pct', 0):.1%}\n")

            # 量能健康度
            volume_health = result.get('volume_health', {})
            if volume_health:
                f.write(f"    量能健康: {'✅通过' if volume_health.get('passed') else '❌未通过'} | ")
                f.write(f"量比: {volume_health.get('health_indicators', {}).get('volume_ratio', 0):.1f}\n")

                danger_signals = volume_health.get('danger_signals', [])
                if danger_signals:
                    f.write(f"    ⚠️ 量能风险: {'; '.join(danger_signals)}\n")

            # 价格强度
            price_strength = result.get('price_strength', {})
            if price_strength:
                f.write(f"    价格强度: {'✅通过' if price_strength.get('passed') else '❌未通过'} | ")
                strength_indicators = price_strength.get('strength_indicators', {})
                f.write(f"回撤: {strength_indicators.get('max_drawdown', 0):.1%}\n")

            # 决策结果
            decision = result.get('decision', {})
            f.write(f"    🎯 决策: {decision.get('action', '观望')} | 置信度: {decision.get('confidence', 0)}% | {decision.get('reason', '')}\n")

        except Exception as e:
            f.write(f"    报告生成错误: {e}\n")

    def _write_reversal_analysis_section(self, f, reversal_analysis):
        """写入10点后逆转分析报告段落"""
        try:
            f.write(f"\n🔄 10点后市场表现分析\n")
            f.write("-" * 80 + "\n")

            # 量能突破分析
            volume_analysis = reversal_analysis.get('volume_analysis', {})
            if volume_analysis:
                f.write(f"📈 量能突破: ")
                breakthrough_score = volume_analysis.get('breakthrough_score', 0)
                if breakthrough_score > 0.5:
                    f.write(f"突破{breakthrough_score:.1f}倍 | ")
                else:
                    f.write(f"未突破({breakthrough_score:.1f}) | ")

                early_avg = volume_analysis.get('early_avg_volume', 0)
                current_5min = volume_analysis.get('current_5min_volume', 0)
                f.write(f"早盘均量{early_avg:.0f} → 当前5分钟{current_5min:.0f}\n")

                # 量能信号
                signals = volume_analysis.get('signals', [])
                if signals:
                    f.write(f"    量能信号: {'; '.join(signals)}\n")

            # 价格反转分析
            price_analysis = reversal_analysis.get('price_analysis', {})
            if price_analysis:
                f.write(f"💰 价格反转: ")
                reversal_score = price_analysis.get('reversal_score', 0)
                if reversal_score > 0.3:
                    f.write(f"反转确认({reversal_score:.1f}) | ")
                else:
                    f.write(f"反转不足({reversal_score:.1f}) | ")

                # 形态解除情况
                resolved_patterns = price_analysis.get('resolved_patterns', [])
                if resolved_patterns:
                    f.write(f"解除形态: {'; '.join(resolved_patterns)}\n")
                else:
                    f.write("无形态解除\n")

                # 买点形态识别
                buy_patterns = price_analysis.get('buy_patterns', [])
                if buy_patterns:
                    f.write(f"    买点形态: {'; '.join(buy_patterns)}\n")

            # 时间窗口分析
            time_analysis = reversal_analysis.get('time_analysis', {})
            if time_analysis:
                f.write(f"⏰ 时间窗口: ")
                window_score = time_analysis.get('window_score', 0)
                if window_score > 0.5:
                    f.write(f"黄金窗口确立({window_score:.1f}) | ")
                else:
                    f.write(f"窗口一般({window_score:.1f}) | ")

                current_window = time_analysis.get('current_window', '未知')
                f.write(f"当前窗口: {current_window}\n")

                # 关键节点
                key_nodes = time_analysis.get('key_nodes', [])
                if key_nodes:
                    f.write(f"    关键节点: {'; '.join(key_nodes)}\n")

            # 综合买点评分
            reversal_score = reversal_analysis.get('reversal_score', 0)
            buy_decision = reversal_analysis.get('buy_decision', {})
            f.write(f"🎯 10点后综合评分: {reversal_score:.3f} | 决策: {buy_decision.get('action', '观望')} | 置信度: {buy_decision.get('confidence', 0)}%\n")

            # 决策依据
            reason = buy_decision.get('reason', '')
            if reason:
                f.write(f"    决策依据: {reason}\n")

            f.write("\n")

        except Exception as e:
            f.write(f"    10点后分析报告生成错误: {e}\n")

    def _write_flat_open_report_section(self, f, result):
        """写入平开股报告段落"""
        try:
            f.write(f"    量能: {result.get('volume_score', 0):.2f}(45%) | 时间: {result.get('time_score', 0):.2f}(35%) | 价格: {result.get('price_score', 0):.2f}(20%)\n")

            # 10点后分析报告
            reversal_analysis = result.get('reversal_analysis', {})
            if reversal_analysis.get('enabled', False):
                self._write_reversal_analysis_section(f, reversal_analysis)

            # 添加详细量能分析信息
            if 'volume_analysis' in result:
                vol_analysis = result['volume_analysis']
                f.write(f"    量比: {vol_analysis.get('volume_ratio_current', 1.0):.1f} | ")
                f.write(f"开盘量比: {vol_analysis.get('early_volume_ratio', 1.0):.1f} | ")
                f.write(f"攻击量峰: {vol_analysis.get('peak_ratio', 1.0):.1f}\n")

                # 添加量能衰竭风险信息
                volume_exhaustion = vol_analysis.get('volume_exhaustion', {})
                if volume_exhaustion.get('detected', False):
                    penalty = volume_exhaustion.get('penalty', 0)
                    exhaustion_signals = volume_exhaustion.get('signals', [])
                    f.write(f"    ⚠️ 量能衰竭风险 (扣分: {penalty:.1f}): {'; '.join(exhaustion_signals)}\n")

                # 添加历史风险对比信息
                historical_risk = vol_analysis.get('historical_risk', {})
                if historical_risk.get('high_risk', False):
                    penalty = historical_risk.get('penalty', 0)
                    description = historical_risk.get('description', '历史风险')
                    f.write(f"    📊 历史风险分析 (扣分: {penalty:.1f}): {description}\n")

                # 添加炸板后遗症信息
                zhaban_syndrome = vol_analysis.get('zhaban_syndrome', {})
                if zhaban_syndrome.get('high_risk', False):
                    penalty = zhaban_syndrome.get('penalty', 0)
                    description = zhaban_syndrome.get('description', '炸板后遗症')
                    f.write(f"    💥 炸板后遗症 (扣分: {penalty:.1f}): {description}\n")

            # 10点后趋势逆转分析
            reversal_analysis = result.get('reversal_analysis', {})
            if reversal_analysis.get('enabled', False):
                reversal_score = reversal_analysis.get('reversal_score', 0.0)
                base_score = result.get('base_score', 0.0)
                f.write(f"    🔄 10点后分析: 基础{base_score:.3f} + 逆转{reversal_score:.3f} = 最终{result.get('total_score', 0):.3f}\n")

                # 逆转分析信号
                reversal_signals = reversal_analysis.get('signals', [])
                if reversal_signals:
                    f.write(f"    📈 逆转信号: {'; '.join(reversal_signals)}\n")

                # 买入决策
                buy_decision = reversal_analysis.get('buy_decision', {})
                if buy_decision:
                    f.write(f"    💰 逆转决策: {buy_decision.get('action', '观望')} | 置信度: {buy_decision.get('confidence', 0)}% | {buy_decision.get('reason', '')}\n")

            # 决策树结果
            operation_advice = result.get('operation_advice', {})
            if operation_advice:
                f.write(f"    🎯 操作建议: {operation_advice.get('action', '观望')} | {operation_advice.get('reason', '')}\n")

        except Exception as e:
            f.write(f"    报告生成错误: {e}\n")

def main():
    """主函数"""
    print("🔍 连板潜力股实时评分系统 (简化版)")
    print("=" * 60)
    
    try:
        scorer = SimpleStockScorer()
        
        # 获取股票代码
        stock_codes = scorer.get_stock_codes_from_user()
        
        if not stock_codes:
            print("⚠️ 未输入任何股票代码")
            return
        
        print(f"\n🚀 开始分析{len(stock_codes)}只股票...")
        
        # 执行分析
        result = scorer.analyze_stocks(stock_codes)
        
        # 显示结果
        if result['success'] and result['results']:
            print(f"\n✅ 分析完成! 成功分析{result['analyzed_count']}只股票")
            print(f"\n📈 实时评分排序:")
            print("-" * 60)
            
            for i, stock in enumerate(result['results'], 1):
                # 综合风险提示（量能+价格形态）
                risk_flag = "✅"
                risk_level = 0
                total_risk_penalty = 0

                # 量能风险
                if 'volume_analysis' in stock:
                    vol_analysis = stock['volume_analysis']
                    volume_exhaustion = vol_analysis.get('volume_exhaustion', {})
                    if volume_exhaustion.get('detected', False):
                        total_risk_penalty += volume_exhaustion.get('penalty', 0)

                # 价格形态风险
                if 'price_analysis' in stock:
                    price_analysis = stock['price_analysis']
                    dangerous_patterns = price_analysis.get('dangerous_patterns', {})
                    if dangerous_patterns.get('detected', False):
                        total_risk_penalty += dangerous_patterns.get('penalty', 0)

                # 综合风险等级
                if total_risk_penalty >= 0.6:
                    risk_flag = "🔴"  # 严重风险
                    risk_level = 3
                elif total_risk_penalty >= 0.3:
                    risk_flag = "🚨"  # 高风险
                    risk_level = 2
                elif total_risk_penalty > 0:
                    risk_flag = "⚠️"  # 一般风险
                    risk_level = 1

                # 根据算法类型显示不同信息
                algorithm_type = stock.get('algorithm_type', 'flat_open')
                grade = stock.get('grade', stock.get('rating', 'C'))

                print(f"{i:2d}. {stock['code']} | 评分: {stock['total_score']:.3f} | 评级: {grade} | {risk_flag}")

                if algorithm_type == 'high_open':
                    # 高开股显示
                    open_info = stock.get('open_info', {})
                    decision = stock.get('decision', {})
                    print(f"    高开算法 | 开盘涨幅: {open_info.get('open_change_pct', 0):.1%} | 决策: {decision.get('action', '观望')}")
                elif algorithm_type == 'flat_open':
                    # 平开股显示
                    print(f"    平开算法 | 量能: {stock.get('volume_score', 0):.2f}(45%) | 时间: {stock.get('time_score', 0):.2f}(35%) | 价格: {stock.get('price_score', 0):.2f}(20%)")
                else:
                    # 基础算法显示
                    print(f"    基础算法 | {stock.get('summary', '基础分析')}")

                # 显示量能详情（增强版）
                if 'volume_analysis' in stock:
                    vol_analysis = stock['volume_analysis']
                    print(f"    量比: {vol_analysis.get('volume_ratio_current', 1.0):.1f} | 开盘量比: {vol_analysis.get('early_volume_ratio', 1.0):.1f}")

                    # 显示量能衰竭详情
                    volume_exhaustion = vol_analysis.get('volume_exhaustion', {})
                    if volume_exhaustion.get('detected', False):
                        exhaustion_signals = volume_exhaustion.get('signals', [])
                        penalty = volume_exhaustion.get('penalty', 0)
                        print(f"    ⚠️ 量能衰竭风险 (扣分: {penalty:.1f}): {'; '.join(exhaustion_signals[:2])}")

                    # 显示历史风险对比
                    historical_risk = vol_analysis.get('historical_risk', {})
                    if historical_risk.get('high_risk', False):
                        penalty = historical_risk.get('penalty', 0)
                        description = historical_risk.get('description', '历史风险')
                        print(f"    📊 历史风险分析 (扣分: {penalty:.1f}): {description}")

                    # 显示炸板后遗症分析
                    zhaban_syndrome = vol_analysis.get('zhaban_syndrome', {})
                    if zhaban_syndrome.get('high_risk', False):
                        penalty = zhaban_syndrome.get('penalty', 0)
                        description = zhaban_syndrome.get('description', '炸板后遗症')
                        print(f"    💥 炸板后遗症 (扣分: {penalty:.1f}): {description}")
                    elif zhaban_syndrome.get('is_flat_open', False):
                        description = zhaban_syndrome.get('description', '平开正常')
                        print(f"    ✅ 炸板检查: {description}")

                    # 显示板块联动分析
                    sector_linkage = vol_analysis.get('sector_linkage', {})
                    if sector_linkage.get('positive_linkage', False):
                        bonus = sector_linkage.get('bonus', 0)
                        description = sector_linkage.get('description', '板块联动')
                        print(f"    🚀 板块联动 (加分: {bonus:.1f}): {description}")
                    else:
                        description = sector_linkage.get('description', '板块表现一般')
                        print(f"    📊 板块分析: {description}")

                    if vol_analysis.get('signals'):
                        print(f"    量能信号: {'; '.join(vol_analysis['signals'][:2])}")  # 只显示前2个信号

                # 显示价格详情（增强版）
                if 'price_analysis' in stock:
                    price_analysis = stock['price_analysis']
                    conquest = "✅" if price_analysis.get('avg_price_conquest', {}).get('conquered', False) else "❌"
                    rising = "✅" if price_analysis.get('low_points_rising', {}).get('rising', False) else "❌"
                    breakthrough = "✅" if price_analysis.get('breakthrough_analysis', {}).get('breakthrough', False) else "❌"
                    print(f"    均价线征服: {conquest} | 低点抬高: {rising} | 早盘突破: {breakthrough}")

                    # 显示危险价格形态
                    dangerous_patterns = price_analysis.get('dangerous_patterns', {})
                    if dangerous_patterns.get('detected', False):
                        pattern_signals = dangerous_patterns.get('signals', [])
                        penalty = dangerous_patterns.get('penalty', 0)
                        print(f"    ⚠️ 危险价格形态 (扣分: {penalty:.1f}): {'; '.join(pattern_signals[:2])}")

                    if price_analysis.get('signals'):
                        print(f"    价格信号: {'; '.join(price_analysis['signals'][:2])}")  # 只显示前2个信号

                # 显示时间详情
                if 'time_analysis' in stock:
                    time_analysis = stock['time_analysis']
                    decision_tree = time_analysis.get('decision_tree', {})
                    golden_window = "✅" if time_analysis.get('golden_window', {}).get('combo_detected', False) else "❌"
                    early_strong = "✅" if time_analysis.get('early_performance', {}).get('strong', False) else "❌"
                    print(f"    决策路径: {decision_tree.get('decision_path', '无')} | 黄金窗口: {golden_window} | 早盘强势: {early_strong}")
                    if time_analysis.get('signals'):
                        print(f"    时间信号: {'; '.join(time_analysis['signals'][:2])}")  # 只显示前2个信号

                # 显示实战决策树结果
                if 'decision_tree' in stock:
                    dt_result = stock['decision_tree']
                    final_signal = dt_result.get('final_signal', '无')
                    confidence = dt_result.get('confidence', 0)
                    risk_level = dt_result.get('risk_level', '未知')

                    # 根据信号类型选择图标
                    if '买入' in final_signal:
                        signal_icon = "🚀"
                    elif '止损' in final_signal:
                        signal_icon = "🛑"
                    elif '观望' in final_signal or '等待' in final_signal:
                        signal_icon = "⏳"
                    else:
                        signal_icon = "❓"

                    print(f"    {signal_icon} 实战决策: {final_signal} | 置信度: {confidence:.1%} | 风险: {risk_level}")
                    print(f"    决策依据: {dt_result.get('reasoning', '无')}")

                # 显示操作建议
                if 'operation_advice' in stock:
                    op_advice = stock['operation_advice']
                    action = op_advice.get('action', '无')
                    signal_type = op_advice.get('signal_type', '无')

                    print(f"    💡 操作建议: {action} ({signal_type})")

                    # 根据操作类型显示关键建议
                    if action == '买入':
                        entry_strategy = op_advice.get('entry_strategy', [])
                        if entry_strategy:
                            print(f"    📈 买入策略: {entry_strategy[0]}")
                    elif action == '止损':
                        exit_strategy = op_advice.get('exit_strategy', [])
                        if exit_strategy:
                            print(f"    🚨 止损策略: {exit_strategy[0]}")
                    else:
                        watch_points = op_advice.get('watch_points', [])
                        if watch_points:
                            print(f"    👀 观望要点: {watch_points[0]}")
            
            if result['results']:
                best = result['results'][0]
                print(f"\n🏆 最佳推荐: {best['code']} (评分: {best['total_score']:.3f})")

                # 显示报告文件
                if result.get('report_file'):
                    print(f"\n📄 详细报告: {result['report_file']}")
        else:
            print("❌ 分析失败或无有效结果")
    
    except Exception as e:
        print(f"❌ 程序异常: {e}")

class TrendReversalAnalyzer:
    """10点后趋势逆转分析器"""

    def __init__(self):
        """初始化趋势逆转分析器"""
        self.time_weights = {
            '10:00': 0.20,  # 早盘压力测试
            '10:30': 0.30,  # 盘中多空分水岭
            '11:00': 0.35,  # 午盘突击信号
            '13:10': 0.10,  # 午盘资金回流
            '14:00': 0.05   # 尾盘封板动能
        }
        logger.info("✅ 10点后趋势逆转分析器初始化完成")

    def analyze_trend_reversal(self, minute_data, history_data,
                              early_analysis, current_time):
        """
        10点后趋势逆转分析主函数

        Args:
            minute_data: 当日分时数据
            history_data: 历史数据
            early_analysis: 早盘分析结果
            current_time: 当前时间

        Returns:
            Dict: 趋势逆转分析结果
        """
        try:
            # 检查是否需要启用10点后分析
            if not self._should_enable_reversal_analysis(current_time):
                return {'enabled': False, 'reversal_score': 0.0, 'signals': []}

            logger.info(f"🔄 启用10点后趋势逆转分析 (当前时间: {current_time.strftime('%H:%M')})")

            # 核心三要素分析
            volume_analysis = self._analyze_volume_breakthrough(minute_data, current_time)
            price_analysis = self._analyze_price_reversal(minute_data, early_analysis, current_time)
            time_analysis = self._analyze_time_window(minute_data, current_time)

            # 计算动态买点评分
            reversal_score = self._calculate_reversal_score(
                volume_analysis, price_analysis, time_analysis, current_time
            )

            # 生成信号列表
            signals = self._generate_reversal_signals(volume_analysis, price_analysis, time_analysis)

            # 买入决策判断
            buy_decision = self._make_buy_decision(reversal_score, minute_data)

            return {
                'enabled': True,
                'reversal_score': reversal_score,
                'volume_analysis': volume_analysis,
                'price_analysis': price_analysis,
                'time_analysis': time_analysis,
                'signals': signals,
                'buy_decision': buy_decision
            }

        except Exception as e:
            logger.error(f"趋势逆转分析失败: {e}")
            return {'enabled': False, 'reversal_score': 0.0, 'signals': []}

    def _should_enable_reversal_analysis(self, current_time: datetime) -> bool:
        """判断是否应该启用趋势逆转分析"""
        current_hour = current_time.hour
        current_minute = current_time.minute

        # 10:00后启用
        if current_hour >= 10:
            return True
        return False

    def _analyze_volume_breakthrough(self, minute_data, current_time):
        """量能二次突破分析"""
        try:
            # 获取成交量列
            volume_col = self._get_volume_column(minute_data)
            if volume_col is None:
                return {'breakthrough_score': 0.0, 'signals': []}

            # 计算早盘30分钟平均量
            early_data = minute_data.head(30)  # 前30分钟
            if len(early_data) < 5:
                return {'breakthrough_score': 0.0, 'signals': []}

            early_avg_volume = early_data[volume_col].mean()

            # 计算当前5分钟量能
            current_5min_volume = minute_data.tail(5)[volume_col].sum()

            # 量比再爆发检测
            breakthrough_score = self._volume_breakthrough_score(early_avg_volume, current_5min_volume)

            # 量能持续性检测
            continuity_score = self._volume_continuity_score(minute_data, volume_col, early_avg_volume)

            signals = []
            if breakthrough_score > 0.5:
                signals.append(f"量能突破{breakthrough_score:.1f}倍")
            if continuity_score > 0.3:
                signals.append("量能持续放大")
            elif continuity_score < -0.2:
                signals.append("量能衰减")

            return {
                'breakthrough_score': breakthrough_score,
                'continuity_score': continuity_score,
                'early_avg_volume': early_avg_volume,
                'current_5min_volume': current_5min_volume,
                'signals': signals
            }

        except Exception as e:
            logger.error(f"量能突破分析失败: {e}")
            return {'breakthrough_score': 0.0, 'signals': []}

    def _volume_breakthrough_score(self, early_avg_volume, current_5min_volume):
        """量比再爆发检测评分"""
        if early_avg_volume <= 0:
            return 0.0

        # 早盘30分钟有6个5分钟周期
        ratio = current_5min_volume / (early_avg_volume * 5 / 6 + 1e-5)

        if ratio > 2.5:
            return 0.7  # 量能翻倍以上
        elif ratio > 2.0:
            return 0.5
        elif ratio > 1.5:
            return 0.3
        else:
            return 0.0

    def _volume_continuity_score(self, minute_data, volume_col, early_avg_volume):
        """量能持续性评分"""
        try:
            # 获取最近15分钟数据，分成3个5分钟段
            recent_data = minute_data.tail(15)
            if len(recent_data) < 15:
                return 0.0

            # 分成3个5分钟段
            seg1 = recent_data.iloc[0:5][volume_col].sum()
            seg2 = recent_data.iloc[5:10][volume_col].sum()
            seg3 = recent_data.iloc[10:15][volume_col].sum()

            # 检查是否连续递增且大于早盘均值
            early_5min_avg = early_avg_volume * 5 / 6

            if seg3 > seg2 > seg1 and seg3 > early_5min_avg:
                return 0.4  # 量能持续递增
            elif seg3 < seg2 < seg1 or seg3 < early_5min_avg * 0.5:
                return -0.3  # 量能衰减
            else:
                return 0.0

        except Exception as e:
            logger.error(f"量能持续性分析失败: {e}")
            return 0.0

    def _get_volume_column(self, data):
        """获取成交量列名"""
        volume_columns = ['volume', '成交量', 'vol', 'Volume', 'VOLUME']
        for col in volume_columns:
            if col in data.columns:
                return col
        return None

    def _analyze_price_reversal(self, minute_data, early_analysis, current_time):
        """价格反转确认分析"""
        try:
            # 获取价格相关列
            price_cols = self._get_price_columns(minute_data)
            if not price_cols:
                return {'reversal_score': 0.0, 'signals': []}

            # 早盘危险形态解除检测
            danger_relief_score = self._analyze_danger_pattern_relief(minute_data, early_analysis, price_cols)

            # 盘中专属买点形态识别
            pattern_score = self._analyze_intraday_patterns(minute_data, price_cols, current_time)

            # 综合评分
            total_score = danger_relief_score + pattern_score

            signals = []
            if danger_relief_score > 0.3:
                signals.append("早盘风险解除")
            if pattern_score > 0.4:
                signals.append("发现买点形态")

            return {
                'reversal_score': total_score,
                'danger_relief_score': danger_relief_score,
                'pattern_score': pattern_score,
                'signals': signals
            }

        except Exception as e:
            logger.error(f"价格反转分析失败: {e}")
            return {'reversal_score': 0.0, 'signals': []}

    def _analyze_danger_pattern_relief(self, minute_data, early_analysis, price_cols):
        """早盘危险形态解除检测"""
        try:
            relief_score = 0.0

            # 获取早盘最高价和当前价
            early_data = minute_data.head(30)
            early_high = early_data[price_cols['high']].max()
            current_price = minute_data[price_cols['close']].iloc[-1]

            # 获取均价线数据
            avg_price_col = self._get_avg_price_column(minute_data)

            # 1. 阶梯跌形态解除：当前价 > 早盘最高价
            if current_price > early_high:
                relief_score += 0.4
                logger.info(f"✅ 阶梯跌形态解除: 当前价{current_price:.2f} > 早盘高{early_high:.2f}")

            # 2. 均价压制解除：连续20分钟站稳分时均价线
            if avg_price_col and self._check_above_avg_price(minute_data, avg_price_col, price_cols['close']):
                relief_score += 0.3
                logger.info("✅ 均价压制解除: 连续站稳均价线")

            # 3. 量能衰竭解除：通过量比判断（在量能分析中处理）

            return min(relief_score, 0.8)  # 最大0.8分

        except Exception as e:
            logger.error(f"危险形态解除分析失败: {e}")
            return 0.0

    def _analyze_intraday_patterns(self, minute_data, price_cols, current_time):
        """盘中专属买点形态识别"""
        try:
            pattern_score = 0.0

            # 登云梯形态检测
            if self._detect_climbing_ladder_pattern(minute_data, price_cols):
                pattern_score += 0.6
                logger.info("✅ 检测到登云梯形态")

            # 过龙门形态检测
            if self._detect_dragon_gate_pattern(minute_data, price_cols, current_time):
                pattern_score += 0.7
                logger.info("✅ 检测到过龙门形态")

            # 冲天炮形态检测
            if self._detect_rocket_pattern(minute_data, price_cols, current_time):
                pattern_score += 0.8
                logger.info("✅ 检测到冲天炮形态")

            return min(pattern_score, 0.8)  # 最大0.8分

        except Exception as e:
            logger.error(f"盘中形态识别失败: {e}")
            return 0.0

    def _get_price_columns(self, data):
        """获取价格相关列名"""
        columns = {}

        # 开盘价
        for col in ['open', '开盘', 'Open', 'OPEN']:
            if col in data.columns:
                columns['open'] = col
                break

        # 收盘价
        for col in ['close', '收盘', 'Close', 'CLOSE']:
            if col in data.columns:
                columns['close'] = col
                break

        # 最高价
        for col in ['high', '最高', 'High', 'HIGH']:
            if col in data.columns:
                columns['high'] = col
                break

        # 最低价
        for col in ['low', '最低', 'Low', 'LOW']:
            if col in data.columns:
                columns['low'] = col
                break

        return columns if len(columns) >= 3 else None

    def _get_avg_price_column(self, data):
        """获取均价列名"""
        avg_columns = ['avg_price', '均价', 'average_price', 'AvgPrice']
        for col in avg_columns:
            if col in data.columns:
                return col
        return None

    def _check_above_avg_price(self, minute_data, avg_price_col, close_col):
        """检查是否连续20分钟站稳均价线"""
        try:
            recent_data = minute_data.tail(20)
            if len(recent_data) < 20:
                return False

            # 检查收盘价是否都在均价线之上
            above_avg = (recent_data[close_col] > recent_data[avg_price_col]).all()
            return above_avg

        except Exception as e:
            logger.error(f"均价线检查失败: {e}")
            return False

    def _detect_climbing_ladder_pattern(self, minute_data, price_cols):
        """检测登云梯形态"""
        try:
            # 早盘下跌 → 10点后低点连续抬高≥3次 + 突破分时均价线不回踩
            early_data = minute_data.head(30)
            later_data = minute_data.tail(60)  # 最近60分钟

            if len(later_data) < 30:
                return False

            # 检查低点抬高
            low_col = price_cols.get('low')
            if not low_col:
                return False

            # 找到最近的低点
            recent_lows = []
            for i in range(10, len(later_data), 10):  # 每10分钟取一个低点
                segment = later_data.iloc[i-10:i]
                if len(segment) > 0:
                    recent_lows.append(segment[low_col].min())

            # 检查是否连续抬高≥3次
            if len(recent_lows) >= 3:
                ascending_count = 0
                for i in range(1, len(recent_lows)):
                    if recent_lows[i] > recent_lows[i-1]:
                        ascending_count += 1

                return ascending_count >= 3

            return False

        except Exception as e:
            logger.error(f"登云梯形态检测失败: {e}")
            return False

    def _detect_dragon_gate_pattern(self, minute_data, price_cols, current_time):
        """检测过龙门形态"""
        try:
            # 10:30前突破早盘高点 → 回踩不破 → 二次放量
            if current_time.hour < 10 or (current_time.hour == 10 and current_time.minute < 30):
                return False

            early_data = minute_data.head(30)
            early_high = early_data[price_cols['high']].max()

            # 检查是否突破早盘高点
            current_price = minute_data[price_cols['close']].iloc[-1]
            if current_price <= early_high:
                return False

            # 简化的二次放量检测（通过量能分析模块处理）
            return True

        except Exception as e:
            logger.error(f"过龙门形态检测失败: {e}")
            return False

    def _detect_rocket_pattern(self, minute_data, price_cols, current_time):
        """检测冲天炮形态"""
        try:
            # 11:00-11:30垂直拉升>5% + 全天最大量柱
            if not (11 <= current_time.hour < 12):
                return False

            # 检查最近30分钟的涨幅
            recent_data = minute_data.tail(30)
            if len(recent_data) < 30:
                return False

            start_price = recent_data[price_cols['close']].iloc[0]
            end_price = recent_data[price_cols['close']].iloc[-1]

            if start_price <= 0:
                return False

            gain_pct = (end_price - start_price) / start_price

            # 垂直拉升>5%
            return gain_pct > 0.05

        except Exception as e:
            logger.error(f"冲天炮形态检测失败: {e}")
            return False

    def _analyze_time_window(self, minute_data, current_time):
        """时间窗口迁移分析"""
        try:
            # 确定当前时间权重
            time_key = self._get_time_key(current_time)
            current_weight = self.time_weights.get(time_key, 0.1)

            # 新黄金窗口判断：10:30-11:30
            is_golden_window = (
                (current_time.hour == 10 and current_time.minute >= 30) or
                (current_time.hour == 11 and current_time.minute <= 30)
            )

            signals = []
            if is_golden_window:
                signals.append("处于新黄金窗口(10:30-11:30)")

            return {
                'time_weight': current_weight,
                'is_golden_window': is_golden_window,
                'time_key': time_key,
                'signals': signals
            }

        except Exception as e:
            logger.error(f"时间窗口分析失败: {e}")
            return {'time_weight': 0.1, 'is_golden_window': False, 'signals': []}

    def _get_time_key(self, current_time):
        """获取时间段键值"""
        hour = current_time.hour
        minute = current_time.minute

        if hour == 10 and minute < 30:
            return '10:00'
        elif hour == 10 and minute >= 30:
            return '10:30'
        elif hour == 11:
            return '11:00'
        elif hour == 13 and minute >= 10:
            return '13:10'
        elif hour == 14:
            return '14:00'
        else:
            return '10:00'  # 默认

    def _calculate_reversal_score(self, volume_analysis, price_analysis,
                                 time_analysis, current_time):
        """计算动态买点评分"""
        try:
            # 根据报告公式：早盘分×0.4 + 量能突破分×0.3 + 形态反转分×0.3
            early_base_score = 0.3  # 假设早盘基础分（在主函数中会被实际值替换）

            volume_score = volume_analysis.get('breakthrough_score', 0.0) + volume_analysis.get('continuity_score', 0.0)
            price_score = price_analysis.get('reversal_score', 0.0)

            # 时间权重调整
            time_weight = time_analysis.get('time_weight', 0.1)
            golden_bonus = 0.1 if time_analysis.get('is_golden_window', False) else 0.0

            # 动态评分公式
            reversal_score = (
                early_base_score * 0.4 +
                volume_score * 0.3 +
                price_score * 0.3 +
                golden_bonus
            ) * time_weight

            return min(reversal_score, 1.0)  # 最大1.0分

        except Exception as e:
            logger.error(f"逆转评分计算失败: {e}")
            return 0.0

    def _generate_reversal_signals(self, volume_analysis, price_analysis, time_analysis):
        """生成逆转信号列表"""
        signals = []

        # 量能信号
        signals.extend(volume_analysis.get('signals', []))

        # 价格信号
        signals.extend(price_analysis.get('signals', []))

        # 时间信号
        signals.extend(time_analysis.get('signals', []))

        return signals

    def _make_buy_decision(self, reversal_score, minute_data):
        """买入决策判断"""
        try:
            # 获取当前涨幅
            if len(minute_data) < 2:
                return {'action': '观望', 'reason': '数据不足', 'confidence': 0}

            first_price = minute_data['close'].iloc[0] if 'close' in minute_data.columns else minute_data['收盘'].iloc[0]
            current_price = minute_data['close'].iloc[-1] if 'close' in minute_data.columns else minute_data['收盘'].iloc[-1]

            if first_price <= 0:
                return {'action': '观望', 'reason': '价格数据异常', 'confidence': 0}

            current_gain = (current_price - first_price) / first_price

            # 决策树逻辑
            if current_gain < 0.03:  # 涨幅<3%
                return {'action': '观望', 'reason': '涨幅不足3%', 'confidence': 20}

            elif 0.03 <= current_gain <= 0.07:  # 涨幅3%-7%
                if reversal_score >= 0.7:
                    return {'action': '买入', 'reason': f'中等涨幅+高逆转评分({reversal_score:.2f})', 'confidence': 75}
                elif reversal_score >= 0.5:
                    return {'action': '轻仓', 'reason': f'中等涨幅+中等逆转评分({reversal_score:.2f})', 'confidence': 60}
                else:
                    return {'action': '观望', 'reason': '逆转信号不足', 'confidence': 30}

            else:  # 涨幅>7%
                if reversal_score >= 0.8:
                    return {'action': '追涨', 'reason': f'高涨幅+强逆转信号({reversal_score:.2f})', 'confidence': 80}
                elif reversal_score >= 0.6:
                    return {'action': '轻仓', 'reason': f'高涨幅+中等逆转信号({reversal_score:.2f})', 'confidence': 65}
                else:
                    return {'action': '观望', 'reason': '高位缺乏逆转确认', 'confidence': 25}

        except Exception as e:
            logger.error(f"买入决策失败: {e}")
            return {'action': '观望', 'reason': '决策异常', 'confidence': 0}

if __name__ == "__main__":
    main()
