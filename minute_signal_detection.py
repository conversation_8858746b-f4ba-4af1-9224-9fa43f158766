#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分时异动检测算法
实现报告中要求的分时盘口特征分析
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from enhanced_data_fetcher import enhanced_stock_fetcher

class MinuteSignalDetector:
    """分时异动检测器"""
    
    def __init__(self):
        self.analysis_days = 3  # 分析最近3天
        self.volume_surge_threshold = 10  # 脉冲放量阈值（10倍）
        self.outperformance_threshold = 0.6  # 抗跌性阈值（60%）
        self.break_attempts_threshold = 2  # 关键价位突破尝试阈值
    
    def detect_minute_signals(self, stock_code, progress_callback=None):
        """检测分时异动信号"""
        try:
            if progress_callback:
                progress_callback(f"检测 {stock_code} 分时异动信号...")
            
            # 1. 获取最近3天的分时数据
            minute_data_list = self._get_last_3_days_minute_data(stock_code)
            
            if not minute_data_list:
                return {
                    'has_signals': False,
                    'reason': '无法获取分时数据',
                    'signal_count': 0,
                    'analysis_details': {}
                }
            
            # 2. 逐日分析信号
            daily_signals = []
            
            for i, day_data in enumerate(minute_data_list):
                if day_data.empty:
                    continue
                
                day_signal = self._analyze_single_day_signals(day_data, i)
                daily_signals.append(day_signal)
            
            # 3. 综合判断触发条件
            trigger_result = self._check_trigger_conditions(daily_signals)
            
            return {
                'has_signals': trigger_result['triggered'],
                'reason': trigger_result['reason'],
                'signal_count': len([s for s in daily_signals if s['has_any_signal']]),
                'analysis_details': {
                    'daily_signals': daily_signals,
                    'trigger_analysis': trigger_result,
                    'total_days_analyzed': len(daily_signals)
                }
            }
            
        except Exception as e:
            print(f"❌ 检测 {stock_code} 分时异动信号失败: {e}")
            return {
                'has_signals': False,
                'reason': f'检测失败: {e}',
                'signal_count': 0,
                'analysis_details': {}
            }
    
    def _get_last_3_days_minute_data(self, stock_code):
        """获取最近3天的分时数据"""
        try:
            minute_data_list = []
            
            # 尝试获取最近3个交易日的分时数据
            for days_back in range(3):
                try:
                    # 使用现有的分时数据获取方法（不支持days_back参数）
                    minute_result = enhanced_stock_fetcher.get_historical_minute_data(stock_code)
                    
                    if minute_result and minute_result.get('data_available', False):
                        minute_data = minute_result['minute_data']
                        if not minute_data.empty:
                            minute_data_list.append(minute_data)
                    else:
                        # 如果无法获取真实数据，创建空DataFrame
                        minute_data_list.append(pd.DataFrame())
                        
                except Exception as e:
                    print(f"⚠️ 获取 {stock_code} 第{days_back+1}天分时数据失败: {e}")
                    minute_data_list.append(pd.DataFrame())
            
            return minute_data_list
            
        except Exception as e:
            print(f"❌ 获取 {stock_code} 分时数据失败: {e}")
            return []
    
    def _analyze_single_day_signals(self, day_data, day_index):
        """分析单日分时信号"""
        try:
            if day_data.empty:
                return {
                    'day_index': day_index,
                    'has_any_signal': False,
                    'vol_peaks': 0,
                    'outperformance': 0,
                    'break_attempts': 0,
                    'reason': '无分时数据'
                }
            
            # 特征1：脉冲式放量检测
            vol_peaks = self._detect_volume_peaks(day_data)
            
            # 特征2：价格抗跌性分析
            outperformance = self._analyze_price_outperformance(day_data)
            
            # 特征3：关键价位争夺检测
            break_attempts = self._detect_key_price_breaks(day_data)
            
            # 判断是否有信号
            has_signal = (
                vol_peaks >= 3 or  # 脉冲放量≥3次
                outperformance > 0.6 or  # 抗跌性>60%
                break_attempts >= 2  # 关键价突破尝试≥2次
            )
            
            return {
                'day_index': day_index,
                'has_any_signal': has_signal,
                'vol_peaks': vol_peaks,
                'outperformance': outperformance,
                'break_attempts': break_attempts,
                'reason': f'脉冲{vol_peaks}次, 抗跌{outperformance:.1%}, 突破{break_attempts}次'
            }
            
        except Exception as e:
            return {
                'day_index': day_index,
                'has_any_signal': False,
                'vol_peaks': 0,
                'outperformance': 0,
                'break_attempts': 0,
                'reason': f'分析失败: {e}'
            }
    
    def _detect_volume_peaks(self, day_data):
        """检测脉冲式放量（10倍量突破）"""
        try:
            if '成交量' not in day_data.columns or len(day_data) < 60:
                return 0
            
            volumes = day_data['成交量'].fillna(0)
            
            # 计算60分钟移动平均成交量
            ma_60 = volumes.rolling(window=60, min_periods=30).mean()
            
            # 检测超过10倍平均量的时段
            volume_peaks = (volumes > ma_60 * self.volume_surge_threshold).sum()
            
            return int(volume_peaks)
            
        except Exception as e:
            return 0
    
    def _analyze_price_outperformance(self, day_data):
        """分析价格抗跌性（大盘跳水时表现）"""
        try:
            if '收盘' not in day_data.columns or len(day_data) < 30:
                return 0
            
            prices = day_data['收盘'].fillna(method='ffill')
            
            # 计算价格变化率
            price_changes = prices.pct_change().fillna(0)
            
            # 模拟大盘下跌时段（简化：价格连续下跌的时段）
            # 在实际应用中，这里应该获取大盘指数数据进行对比
            
            # 寻找价格下跌时段（连续3个时段下跌）
            down_periods = []
            consecutive_down = 0
            
            for i, change in enumerate(price_changes):
                if change < -0.005:  # 下跌超过0.5%
                    consecutive_down += 1
                else:
                    if consecutive_down >= 3:
                        down_periods.extend(range(i-consecutive_down, i))
                    consecutive_down = 0
            
            if not down_periods:
                return 0.5  # 无明显下跌时段，给中性评分
            
            # 计算在下跌时段的表现
            down_period_changes = price_changes.iloc[down_periods]
            positive_performance_ratio = (down_period_changes >= 0).mean()
            
            return float(positive_performance_ratio)
            
        except Exception as e:
            return 0
    
    def _detect_key_price_breaks(self, day_data):
        """检测关键价位争夺（突破前高/平台）"""
        try:
            if '最高' not in day_data.columns or '收盘' not in day_data.columns or len(day_data) < 30:
                return 0
            
            highs = day_data['最高'].fillna(method='ffill')
            closes = day_data['收盘'].fillna(method='ffill')
            
            # 计算近期压力位（最近30分钟的最高价）
            if len(highs) >= 30:
                key_price = highs[-30:].max()
            else:
                key_price = highs.max()
            
            # 检测突破尝试（价格接近或超过关键价位）
            break_threshold = key_price * 0.995  # 99.5%的关键价位
            break_attempts = (highs >= break_threshold).sum()
            
            return int(break_attempts)
            
        except Exception as e:
            return 0
    
    def _check_trigger_conditions(self, daily_signals):
        """检查触发条件：3日内至少2天满足条件"""
        try:
            if len(daily_signals) < 2:
                return {
                    'triggered': False,
                    'reason': '分析天数不足',
                    'qualified_days': 0
                }
            
            # 统计满足条件的天数
            qualified_days = sum(1 for signal in daily_signals if signal['has_any_signal'])
            
            # 触发条件：3日内至少2天满足
            triggered = qualified_days >= 2
            
            if triggered:
                reason = f'3日内{qualified_days}天出现异动信号'
            else:
                reason = f'仅{qualified_days}天出现信号，不足2天'
            
            return {
                'triggered': triggered,
                'reason': reason,
                'qualified_days': qualified_days,
                'total_days': len(daily_signals)
            }
            
        except Exception as e:
            return {
                'triggered': False,
                'reason': f'条件检查失败: {e}',
                'qualified_days': 0
            }
    
    def batch_detect_signals(self, stock_codes, progress_callback=None):
        """批量检测股票的分时异动信号"""
        try:
            results = []
            total_stocks = len(stock_codes)
            
            for i, stock_code in enumerate(stock_codes):
                try:
                    if progress_callback:
                        progress = (i + 1) / total_stocks * 100
                        progress_callback(progress, f"分时信号检测: {stock_code}")
                    
                    signal_result = self.detect_minute_signals(stock_code)
                    
                    result = {
                        'code': stock_code,
                        'minute_signal_analysis': signal_result
                    }
                    
                    results.append(result)
                    
                    status = "有信号" if signal_result['has_signals'] else "无信号"
                    print(f"✅ {stock_code}: 分时信号 {status} ({signal_result['signal_count']}天)")
                    
                except Exception as e:
                    print(f"❌ 检测 {stock_code} 分时信号失败: {e}")
                    continue
            
            # 筛选有信号的股票
            qualified_stocks = [r for r in results if r['minute_signal_analysis']['has_signals']]
            
            print(f"📊 分时异动信号检测完成:")
            print(f"   分析股票: {len(results)} 只")
            print(f"   有信号股票: {len(qualified_stocks)} 只")
            print(f"   信号检出率: {len(qualified_stocks)/len(results)*100:.1f}%" if results else "   信号检出率: 0.0%")
            
            return qualified_stocks
            
        except Exception as e:
            print(f"❌ 批量分时信号检测失败: {e}")
            return []

def test_minute_signal_detector():
    """测试分时异动检测器"""
    print("🧪 测试分时异动检测器")
    
    try:
        detector = MinuteSignalDetector()
        
        # 测试股票
        test_stocks = ['000001', '000002', '600036']
        
        print(f"📊 批量检测 {len(test_stocks)} 只股票的分时异动信号:")
        
        qualified_stocks = detector.batch_detect_signals(test_stocks)
        
        if qualified_stocks:
            print(f"\n🏆 检测到分时异动信号的股票:")
            for stock in qualified_stocks:
                analysis = stock['minute_signal_analysis']
                details = analysis['analysis_details']
                print(f"   {stock['code']}: {analysis['reason']}")
                print(f"     信号天数: {analysis['signal_count']}/{details['total_days_analyzed']}")
        else:
            print("⚠️ 无股票检测到分时异动信号")
        
        return len(qualified_stocks) > 0
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_minute_signal_detector()
