#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查数据库中的数据情况
"""

import sqlite3
import pandas as pd
import json

def check_database():
    """检查数据库中的数据"""
    print("🔍 检查数据库中的数据...")
    
    # 连接数据库
    conn = sqlite3.connect('historical_data.db')
    
    try:
        # 查看K线数据
        print('\n📊 K线数据统计:')
        kline_count = conn.execute('SELECT COUNT(*) FROM stock_kline_data').fetchone()[0]
        print(f'   总记录数: {kline_count}')
        
        if kline_count > 0:
            # 查看股票代码分布
            stock_codes = pd.read_sql('SELECT stock_code, COUNT(*) as count FROM stock_kline_data GROUP BY stock_code ORDER BY count DESC LIMIT 10', conn)
            print(f'   股票代码分布（前10）:\n{stock_codes}')
            
            # 查看样本数据
            sample_kline = pd.read_sql('SELECT * FROM stock_kline_data LIMIT 5', conn)
            print(f'   样本数据:\n{sample_kline}')
        
        # 查看资金流向数据
        print('\n💰 资金流向数据统计:')
        capital_count = conn.execute('SELECT COUNT(*) FROM capital_flow_data').fetchone()[0]
        print(f'   总记录数: {capital_count}')
        
        if capital_count > 0:
            # 查看股票代码分布
            capital_stocks = pd.read_sql('SELECT stock_code, COUNT(*) as count FROM capital_flow_data GROUP BY stock_code ORDER BY count DESC LIMIT 10', conn)
            print(f'   股票代码分布（前10）:\n{capital_stocks}')
            
            # 查看样本数据
            sample_capital = pd.read_sql('SELECT * FROM capital_flow_data LIMIT 5', conn)
            print(f'   样本数据:\n{sample_capital}')
        
        # 查看分时数据
        print('\n⏰ 分时数据统计:')
        minute_count = conn.execute('SELECT COUNT(*) FROM stock_minute_data').fetchone()[0]
        print(f'   总记录数: {minute_count}')
        
        if minute_count > 0:
            minute_stocks = pd.read_sql('SELECT stock_code, COUNT(*) as count FROM stock_minute_data GROUP BY stock_code ORDER BY count DESC LIMIT 10', conn)
            print(f'   股票代码分布（前10）:\n{minute_stocks}')
        
        # 查看特征数据
        print('\n🎯 特征数据统计:')
        feature_count = conn.execute('SELECT COUNT(*) FROM stock_features').fetchone()[0]
        print(f'   总记录数: {feature_count}')
        
        if feature_count > 0:
            # 查看标签分布
            print('\n🏷️ 标签分布:')
            label_dist = pd.read_sql('SELECT label, COUNT(*) as count FROM stock_features GROUP BY label', conn)
            print(f'   标签分布:\n{label_dist}')
            
            # 查看特征数量分布
            feature_count_dist = pd.read_sql('SELECT feature_count, COUNT(*) as count FROM stock_features GROUP BY feature_count', conn)
            print(f'   特征数量分布:\n{feature_count_dist}')
            
            # 查看样本特征数据
            sample_features = pd.read_sql('SELECT stock_code, feature_date, feature_count, label FROM stock_features LIMIT 10', conn)
            print(f'   样本特征数据:\n{sample_features}')
            
            # 查看一个具体的特征内容
            sample_feature_detail = conn.execute('SELECT features_json FROM stock_features LIMIT 1').fetchone()
            if sample_feature_detail:
                features = json.loads(sample_feature_detail[0])
                print(f'\n   样本特征详情（前10个特征）:')
                for i, (key, value) in enumerate(list(features.items())[:10]):
                    print(f'     {key}: {value}')
        
        # 查看涨停股票数据
        print('\n🔥 涨停股票数据统计:')
        limit_up_count = conn.execute('SELECT COUNT(*) FROM limit_up_stocks').fetchone()[0]
        print(f'   总记录数: {limit_up_count}')
        
        if limit_up_count > 0:
            limit_up_stocks = pd.read_sql('SELECT stock_code, COUNT(*) as count FROM limit_up_stocks GROUP BY stock_code ORDER BY count DESC LIMIT 10', conn)
            print(f'   涨停次数分布（前10）:\n{limit_up_stocks}')
    
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
    
    finally:
        conn.close()

if __name__ == "__main__":
    check_database()
