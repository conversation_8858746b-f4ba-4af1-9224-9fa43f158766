#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游资洗盘行为量化捕手系统 - 优化版
基于三维共振模型的全A股游资洗盘痕迹识别系统
优化：使用批量数据获取，大幅提升效率
"""

import os
import json
import pandas as pd
import numpy as np
import requests
import time
import re
from datetime import datetime, timedelta
from collections import defaultdict
import warnings
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
warnings.filterwarnings('ignore')

class OptimizedGameCapitalWashingDetector:
    """游资洗盘行为量化捕手 - 优化版"""
    
    def __init__(self):
        # 修改为指向上级目录的路径
        self.limit_up_data_dir = os.path.join("..", "limit_up_data")
        self.current_date = datetime.now()
        self.akshare_available = True
        
        # 三维共振模型参数
        self.config = {
            'min_limit_up_count': 3,        # 最小涨停次数（活跃股基因）
            'exclude_recent_days': 20,      # 排除近N日内有涨停的股票
            'kline_days': 60,              # K线数据天数
            'volume_shrink_threshold': 0.4, # 量能萎缩阈值
            'anti_fall_ratio': 0.6,        # 抗跌比例阈值
            'doji_amplitude_threshold': 3.0, # 十字星振幅阈值
            'comprehensive_score_threshold': 60, # 综合评分阈值
            'batch_size': 1000,            # 批量处理大小（超大批次优化）
            'max_workers': 10,             # 最大线程数
            'use_batch_optimization': True  # 启用批量优化
        }
        
        # 批量数据缓存
        self.batch_kline_cache = {}
        self.index_data_cache = None
        
        print("✅ 游资洗盘行为量化捕手系统（优化版）初始化完成")
        print(f"📊 配置参数: 涨停次数≥{self.config['min_limit_up_count']}, 排除近{self.config['exclude_recent_days']}日涨停")
        print(f"🚀 批量优化: 启用, 批量大小: {self.config['batch_size']}, 线程数: {self.config['max_workers']}")
    
    def run_analysis(self):
        """运行完整分析流程"""
        print("🚀 开始游资洗盘行为量化分析（优化版）...")
        print("=" * 80)
        
        try:
            # 第一步：获取全市场数据并基础筛选
            print("\n📊 第一步：获取全市场数据并基础筛选...")
            market_candidates = self._get_market_candidates()
            
            if not market_candidates:
                print("❌ 基础筛选后无候选股票")
                return []
            
            print(f"✅ 基础筛选完成，获得 {len(market_candidates)} 只候选股票")
            
            # 第二步：活跃股基因筛选（涨停次数）
            print("\n🧬 第二步：活跃股基因筛选...")
            active_candidates = self._filter_active_stocks(market_candidates)
            
            if not active_candidates:
                print("❌ 活跃股筛选后无候选股票")
                return []
            
            print(f"✅ 活跃股筛选完成，剩余 {len(active_candidates)} 只股票")
            
            # 第三步：批量预加载数据（新增优化步骤）
            print("\n📦 第三步：批量预加载历史数据...")
            self._batch_preload_data(active_candidates)
            
            # 第四步：三维共振检测（优化版）
            print("\n🎯 第四步：三维共振检测（批量优化版）...")
            final_candidates = self._optimized_three_dimension_detection(active_candidates)
            
            print(f"✅ 三维共振检测完成，发现 {len(final_candidates)} 只洗盘股票")
            
            # 第五步：结果展示和导出
            self._display_results(final_candidates)
            self._export_results(final_candidates)
            
            return final_candidates
            
        except Exception as e:
            print(f"❌ 分析流程失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def _get_market_candidates(self):
        """获取全市场数据并进行基础筛选（复用原有逻辑）"""
        try:
            print("📡 获取全市场股票数据...")
            
            # 使用新浪财经API获取全市场数据（带重试机制）
            market_data = self._get_sina_market_data_with_retry()
            
            if not market_data or len(market_data) == 0:
                print("❌ 无法获取市场数据")
                return []
            
            print(f"✅ 获取全市场数据: {len(market_data)} 只股票")
            
            # 基础筛选：排除ST、创业板、科创板、北交所
            filtered_candidates = []
            debug_count = 0
            invalid_code_count = 0
            excluded_count = 0

            for stock in market_data:
                try:
                    original_code = stock.get('代码', '') or stock.get('symbol', '')
                    name = stock.get('名称', '') or stock.get('name', '')

                    # 标准化股票代码
                    code = self._normalize_stock_code(original_code)

                    # 调试信息（前10个股票）
                    if debug_count < 10:
                        print(f"   调试 {debug_count + 1}: 原始代码='{original_code}', 标准化='{code}', 名称='{name}'")
                        debug_count += 1

                    # 检查代码是否有效
                    if not code:
                        invalid_code_count += 1
                        continue

                    # 基础筛选
                    if self._check_market_scope(code, name):
                        # 获取市值（亿元）
                        market_cap = self._get_market_cap(stock)

                        candidate = {
                            'code': code,
                            'name': name,
                            'market_cap': market_cap,
                            'latest_price': stock.get('最新价', 0) or stock.get('trade', 0),
                            'change_pct': stock.get('涨跌幅', 0) or stock.get('changepercent', 0),
                            'volume': stock.get('成交量', 0) or stock.get('volume', 0),
                            'turnover_rate': stock.get('换手率', 0) or stock.get('turnoverratio', 0),
                            'original_data': stock
                        }
                        filtered_candidates.append(candidate)
                    else:
                        excluded_count += 1

                except Exception as e:
                    continue
            
            print(f"✅ 基础筛选完成: {len(market_data)} → {len(filtered_candidates)} 只股票")
            print(f"   排除: ST股票、创业板(300)、科创板(688)、北交所(8/4)")
            print(f"   统计: 无效代码 {invalid_code_count} 只, 被排除 {excluded_count} 只, 通过筛选 {len(filtered_candidates)} 只")
            
            return filtered_candidates
            
        except Exception as e:
            print(f"❌ 获取市场候选股票失败: {e}")
            return []
    
    def _get_sina_market_data_with_retry(self):
        """从新浪财经获取全市场数据（带重试机制）"""
        max_retries = 2
        
        for attempt in range(max_retries + 1):
            try:
                print(f"📡 从新浪财经获取全市场数据... (尝试 {attempt + 1}/{max_retries + 1})")
                
                all_data = []
                page = 1
                max_pages = 60
                
                while page <= max_pages:
                    page_data = self._get_sina_page_data_with_retry(page)
                    
                    if page_data is None:
                        print(f"⚠️ 第 {page} 页获取失败，停止获取")
                        break
                    elif len(page_data) == 0:
                        print(f"📄 已获取到最后一页，共 {page} 页")
                        break
                    else:
                        all_data.extend(page_data)
                        print(f"✅ 第 {page} 页获取成功: {len(page_data)} 只股票")
                        
                        if len(page_data) < 100:
                            print(f"📄 已获取到最后一页，共 {page} 页")
                            break
                        
                        page += 1
                        time.sleep(0.5)  # 减少页面间隔到0.5秒
                
                if not all_data:
                    if attempt < max_retries:
                        wait_time = 10 if attempt == 0 else 60
                        print(f"❌ 第{attempt + 1}次获取数据为空，{wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print("❌ 重试后仍未获取到任何数据")
                        return []
                
                print(f"✅ 新浪财经数据获取完成: 共 {len(all_data)} 只股票")
                
                # 标准化数据格式
                standardized_data = []
                for item in all_data:
                    try:
                        standardized_item = {
                            '代码': item.get('symbol', ''),
                            '名称': item.get('name', ''),
                            '最新价': float(item.get('trade', 0)) if item.get('trade') else 0,
                            '涨跌幅': float(item.get('changepercent', 0)) if item.get('changepercent') else 0,
                            '成交量': int(item.get('volume', 0)) if item.get('volume') else 0,
                            '总市值': float(item.get('mktcap', 0)) if item.get('mktcap') else 0,
                            '流通市值': float(item.get('nmc', 0)) if item.get('nmc') else 0,
                            '换手率': float(item.get('turnoverratio', 0)) if item.get('turnoverratio') else 0
                        }
                        standardized_data.append(standardized_item)
                    except Exception as e:
                        continue
                
                return standardized_data
                
            except Exception as e:
                if attempt < max_retries:
                    wait_time = 10 if attempt == 0 else 60
                    print(f"❌ 第{attempt + 1}次获取新浪财经数据失败: {e}")
                    print(f"⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                else:
                    print(f"❌ 重试{max_retries}次后仍失败: {e}")
                    return []
        
        return []

    def _get_sina_page_data_with_retry(self, page, max_retries=2):
        """获取新浪财经单页数据（带重试机制）"""
        for attempt in range(max_retries + 1):
            try:
                url = "http://vip.stock.finance.sina.com.cn/quotes_service/api/json_v2.php/Market_Center.getHQNodeData"
                params = {
                    'page': page,
                    'num': 100,
                    'sort': 'symbol',
                    'asc': 1,
                    'node': 'hs_a'
                }

                headers = {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                    'Referer': 'http://vip.stock.finance.sina.com.cn/',
                    'Accept': 'application/json, text/javascript, */*; q=0.01'
                }

                response = requests.get(url, params=params, headers=headers, timeout=15)

                if response.status_code == 200:
                    data_text = response.text
                    data_text = re.sub(r'[\x00-\x1f\x7f-\x9f]', '', data_text)

                    try:
                        page_data = json.loads(data_text)

                        if page_data and isinstance(page_data, list):
                            return page_data
                        else:
                            return []  # 空数据，表示到达最后一页

                    except json.JSONDecodeError as e:
                        if attempt < max_retries:
                            wait_time = 10 if attempt == 0 else 60
                            print(f"⚠️ 第 {page} 页JSON解析失败(尝试{attempt + 1}): {e}")
                            print(f"⏱️ {wait_time}秒后重试...")
                            time.sleep(wait_time)
                            continue
                        else:
                            print(f"❌ 第 {page} 页JSON解析重试失败: {e}")
                            return None
                else:
                    if attempt < max_retries:
                        wait_time = 10 if attempt == 0 else 60
                        print(f"⚠️ 第 {page} 页请求失败(尝试{attempt + 1}): {response.status_code}")
                        print(f"⏱️ {wait_time}秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        print(f"❌ 第 {page} 页请求重试失败: {response.status_code}")
                        return None

            except Exception as e:
                if attempt < max_retries:
                    wait_time = 10 if attempt == 0 else 60
                    print(f"⚠️ 第 {page} 页获取异常(尝试{attempt + 1}): {e}")
                    print(f"⏱️ {wait_time}秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    print(f"❌ 第 {page} 页获取重试失败: {e}")
                    return None

        return None

    def _batch_preload_data(self, candidates):
        """批量预加载历史数据（核心优化）"""
        try:
            print(f"📦 开始批量预加载 {len(candidates)} 只股票的历史数据...")

            # 提取股票代码列表
            stock_codes = [candidate['code'] for candidate in candidates]

            # 方法1: 尝试使用AKShare批量获取（如果可用）
            if self.akshare_available:
                try:
                    print("🚀 尝试使用AKShare批量获取历史数据...")
                    self._batch_get_kline_data_akshare(stock_codes)
                except Exception as e:
                    print(f"⚠️ AKShare批量获取失败: {e}")

            # 方法2: 使用多线程并发获取（备用方案）
            if not self.batch_kline_cache:
                print("🔄 使用多线程并发获取历史数据...")
                self._batch_get_kline_data_concurrent(stock_codes)

            # 预加载指数数据
            self._preload_index_data()

            print(f"✅ 批量预加载完成，缓存了 {len(self.batch_kline_cache)} 只股票的数据")

        except Exception as e:
            print(f"❌ 批量预加载失败: {e}")

    def _batch_get_kline_data_akshare(self, stock_codes):
        """使用AKShare批量获取K线数据"""
        try:
            import akshare as ak

            # 计算日期范围
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=self.config['kline_days']*2)).strftime('%Y%m%d')

            print(f"📅 数据范围: {start_date} 到 {end_date}")

            # 分批处理，避免API限制
            batch_size = 1000  # 超大批次处理，最大化效率
            total_batches = (len(stock_codes) + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(stock_codes))
                batch_codes = stock_codes[start_idx:end_idx]

                print(f"📦 处理批次 {batch_idx + 1}/{total_batches}: {len(batch_codes)} 只股票")

                for code in batch_codes:
                    # 使用重试机制获取数据
                    success = self._get_single_stock_data_with_retry(code, ak, start_date, end_date)

                # 批次间隔（大批次处理后适当休息）
                if batch_idx < total_batches - 1:
                    time.sleep(5)  # 5秒间隔，确保API稳定

            print(f"✅ AKShare批量获取完成，成功获取 {len(self.batch_kline_cache)} 只股票数据")

        except Exception as e:
            print(f"❌ AKShare批量获取失败: {e}")
            raise e

    def _get_single_stock_data_with_retry(self, code, ak, start_date, end_date):
        """使用重试机制获取单只股票数据"""
        retry_delays = [10, 60, 360]  # 重试间隔：10秒、60秒、360秒
        max_retries = 4  # 最多重试4次（第1次 + 3次重试）

        for attempt in range(max_retries):
            try:
                # 标准化股票代码
                normalized_code = self._normalize_stock_code_for_akshare(code)

                # 获取K线数据
                df = ak.stock_zh_a_hist(
                    symbol=normalized_code,
                    start_date=start_date,
                    end_date=end_date,
                    adjust="qfq"  # 前复权
                )

                if not df.empty:
                    # 标准化列名
                    df = self._standardize_kline_columns(df)
                    self.batch_kline_cache[code] = df

                    if attempt > 0:  # 如果是重试成功的
                        print(f"✅ {code} 重试第{attempt}次成功")

                    return True

            except Exception as e:
                if attempt < max_retries - 1:  # 还有重试机会
                    delay = retry_delays[min(attempt, len(retry_delays) - 1)]
                    print(f"⚠️ 获取 {code} 数据失败 (第{attempt + 1}次): {e}")
                    print(f"🔄 等待 {delay} 秒后重试...")
                    time.sleep(delay)
                else:  # 最后一次尝试失败
                    print(f"❌ 获取 {code} 数据最终失败 (已重试{max_retries}次): {e}")
                    return False

        return False

    def _batch_get_kline_data_concurrent(self, stock_codes):
        """使用多线程并发获取K线数据（备用方案）"""
        try:
            print(f"🔄 使用多线程并发获取 {len(stock_codes)} 只股票的K线数据...")

            def get_single_kline(code):
                """获取单只股票的K线数据"""
                try:
                    # 这里可以使用其他数据源，如东方财富、腾讯财经等
                    # 为了演示，这里使用简化的逻辑
                    return code, self._get_kline_data_fallback(code)
                except Exception as e:
                    return code, None

            # 使用线程池并发获取
            with ThreadPoolExecutor(max_workers=self.config['max_workers']) as executor:
                # 提交所有任务
                future_to_code = {executor.submit(get_single_kline, code): code for code in stock_codes}

                # 收集结果
                completed = 0
                for future in as_completed(future_to_code):
                    code, kline_data = future.result()
                    completed += 1

                    if kline_data is not None and not kline_data.empty:
                        self.batch_kline_cache[code] = kline_data

                    # 进度显示
                    if completed % 50 == 0 or completed == len(stock_codes):
                        print(f"📊 并发获取进度: {completed}/{len(stock_codes)} ({completed/len(stock_codes)*100:.1f}%)")

            print(f"✅ 多线程并发获取完成，成功获取 {len(self.batch_kline_cache)} 只股票数据")

        except Exception as e:
            print(f"❌ 多线程并发获取失败: {e}")

    def _get_kline_data_fallback(self, code):
        """获取K线数据的备用方法"""
        try:
            # 这里可以实现其他数据源的获取逻辑
            # 例如：东方财富、腾讯财经、网易财经等
            # 为了演示，返回空DataFrame
            return pd.DataFrame()
        except Exception as e:
            return pd.DataFrame()

    def _preload_index_data(self):
        """预加载指数数据"""
        try:
            if self.index_data_cache is not None:
                return  # 已经缓存

            print("📈 预加载指数数据...")

            if self.akshare_available:
                try:
                    import akshare as ak

                    # 获取上证指数数据
                    index_df = ak.stock_zh_index_daily(symbol="sh000001")

                    if not index_df.empty:
                        # 处理日期列
                        date_column = None
                        for col in ['date', '日期', 'Date', 'DATE']:
                            if col in index_df.columns:
                                date_column = col
                                break

                        if date_column:
                            index_df['date'] = pd.to_datetime(index_df[date_column])

                            # 重命名收盘价列
                            if '收盘' in index_df.columns:
                                index_df['close'] = index_df['收盘']
                            elif 'close' not in index_df.columns and '收盘价' in index_df.columns:
                                index_df['close'] = index_df['收盘价']

                            # 计算涨跌幅
                            if 'pct_chg' not in index_df.columns:
                                index_df['pct_chg'] = index_df['close'].pct_change() * 100

                            self.index_data_cache = index_df.tail(60)  # 缓存最近60天数据
                            print(f"✅ 指数数据预加载成功: {len(self.index_data_cache)}条数据")
                            return

                except Exception as e:
                    print(f"⚠️ 指数数据预加载失败: {e}")

            # 如果AKShare失败，创建空的指数数据
            self.index_data_cache = pd.DataFrame()

        except Exception as e:
            print(f"❌ 指数数据预加载异常: {e}")
            self.index_data_cache = pd.DataFrame()

    def _filter_active_stocks(self, market_candidates):
        """活跃股基因筛选：基于涨停次数（完全按照原版算法）"""
        try:
            print("🧬 开始活跃股基因筛选...")

            # 获取涨停统计数据
            limit_up_stats = self._get_limit_up_statistics()

            if not limit_up_stats:
                print("❌ 无法获取涨停统计数据")
                return []

            print(f"✅ 涨停统计数据获取成功，包含 {len(limit_up_stats)} 只股票")

            active_candidates = []

            for candidate in market_candidates:
                try:
                    code = candidate['code']
                    name = candidate['name']

                    # 获取涨停统计
                    limit_up_info = limit_up_stats.get(code, {})
                    limit_up_count = limit_up_info.get('count', 0)
                    last_limit_up_date = limit_up_info.get('last_date', None)

                    # 条件1：涨停次数≥3次
                    if limit_up_count < self.config['min_limit_up_count']:
                        continue

                    # 条件2：排除近20日内有涨停的股票
                    days_since = 999  # 默认值
                    if last_limit_up_date:
                        try:
                            last_date = datetime.strptime(last_limit_up_date, '%Y%m%d')
                            days_since = (self.current_date - last_date).days

                            if days_since < self.config['exclude_recent_days']:
                                continue
                        except:
                            pass

                    # 通过筛选
                    candidate['limit_up_count'] = limit_up_count
                    candidate['last_limit_up_date'] = last_limit_up_date
                    candidate['days_since_limit_up'] = days_since

                    active_candidates.append(candidate)

                    if len(active_candidates) <= 10:  # 只打印前10个
                        print(f"✅ {code} {name}: {limit_up_count}次涨停, 距今{days_since if last_limit_up_date else '999+'}天")

                except Exception as e:
                    continue

            if len(active_candidates) > 10:
                print(f"... 还有 {len(active_candidates) - 10} 只股票符合条件")

            print(f"✅ 活跃股筛选完成: {len(market_candidates)} → {len(active_candidates)} 只股票")
            return active_candidates

        except Exception as e:
            print(f"❌ 活跃股筛选失败: {e}")
            return []

    def _optimized_three_dimension_detection(self, candidates):
        """优化版三维共振检测"""
        try:
            print(f"🎯 开始三维共振检测...")
            print(f"📊 待检测股票数量: {len(candidates)} 只")

            final_candidates = []

            # 分批处理，提高效率
            batch_size = self.config['batch_size']
            total_batches = (len(candidates) + batch_size - 1) // batch_size

            for batch_idx in range(total_batches):
                start_idx = batch_idx * batch_size
                end_idx = min(start_idx + batch_size, len(candidates))
                batch_candidates = candidates[start_idx:end_idx]

                print(f"\n🔍 处理第 {batch_idx + 1} 批次: {len(batch_candidates)} 只股票")

                # 批量处理当前批次
                batch_results = self._process_batch_candidates(batch_candidates)
                final_candidates.extend(batch_results)

                # 显示批次进度
                print(f"✅ 第 {batch_idx + 1} 批次完成，发现 {len(batch_results)} 只符合条件的股票")

                # 批次间隔（避免API限制）
                if batch_idx < total_batches - 1:
                    time.sleep(1)

            print(f"✅ 三维共振检测完成，共发现 {len(final_candidates)} 只洗盘股票")

            return final_candidates

        except Exception as e:
            print(f"❌ 三维共振检测失败: {e}")
            return []

    def _process_batch_candidates(self, batch_candidates):
        """处理批次候选股票"""
        batch_results = []

        for i, candidate in enumerate(batch_candidates, 1):
            try:
                code = candidate['code']
                name = candidate['name']

                print(f"\n🔍 分析 {code} {name}...")

                # 从缓存获取K线数据
                kline_data = self.batch_kline_cache.get(code)

                if kline_data is None or kline_data.empty:
                    print(f"  ⚠️ 无K线数据，跳过")
                    continue

                # 执行三维共振分析
                analysis_result = self._perform_three_dimension_analysis(code, kline_data, candidate)

                if analysis_result and analysis_result['comprehensive_score'] >= self.config['comprehensive_score_threshold']:
                    candidate['dimension_results'] = analysis_result
                    candidate['comprehensive_score'] = analysis_result['comprehensive_score']
                    batch_results.append(candidate)

                    print(f"  ✅ 符合条件，综合评分: {analysis_result['comprehensive_score']:.1f}")
                else:
                    score = analysis_result['comprehensive_score'] if analysis_result else 0
                    print(f"  ❌ 综合评分不足 (需≥{self.config['comprehensive_score_threshold']}): {score:.1f}")

                # 减少等待时间
                if i % 10 == 0:
                    time.sleep(0.5)  # 每10只股票暂停0.5秒

            except Exception as e:
                print(f"  ❌ 分析失败: {e}")
                continue

        return batch_results

    def _perform_three_dimension_analysis(self, code, kline_data, candidate):
        """执行三维共振分析（使用原版算法）"""
        try:
            # 获取指数数据
            index_data = self.index_data_cache if self.index_data_cache is not None else pd.DataFrame()

            # 使用原版的三维检测算法
            detection_result = self._detect_three_dimensions(kline_data, index_data)

            # 计算综合评分（使用原版的权重）
            volume_score = detection_result['volume_score']
            resilience_score = detection_result['resilience_score']
            pattern_score = detection_result['pattern_score']

            # 原版的综合评分计算（权重：量能40% + 韧性30% + 形态30%）
            comprehensive_score = (volume_score * 0.4 + resilience_score * 0.3 + pattern_score * 0.3)

            print(f"  📊 量能密码: {volume_score:.1f}分")
            print(f"  📊 价格韧性: {resilience_score:.1f}分")
            print(f"  📊 形态识别: {pattern_score:.1f}分")
            print(f"  📊 综合评分: {comprehensive_score:.1f}分")

            return {
                'volume_score': volume_score,
                'resilience_score': resilience_score,
                'pattern_score': pattern_score,
                'comprehensive_score': comprehensive_score,
                'volume_details': detection_result['volume_details'],
                'resilience_details': detection_result['resilience_details'],
                'pattern_details': detection_result['pattern_details'],
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

        except Exception as e:
            print(f"  ❌ 三维分析失败: {e}")
            return None



    def _detect_three_dimensions(self, kline_data, index_data):
        """检测三维特征（原版算法）"""
        try:
            results = {
                'volume_score': 0,
                'resilience_score': 0,
                'pattern_score': 0,
                'volume_details': {},
                'resilience_details': {},
                'pattern_details': {}
            }

            # 维度1：量能密码检测
            volume_result = self._detect_volume_pattern(kline_data)
            results['volume_score'] = volume_result['score']
            results['volume_details'] = volume_result

            # 维度2：价格韧性检测
            resilience_result = self._detect_price_resilience(kline_data, index_data)
            results['resilience_score'] = resilience_result['score']
            results['resilience_details'] = resilience_result

            # 维度3：形态识别检测
            pattern_result = self._detect_washing_patterns(kline_data)
            results['pattern_score'] = pattern_result['score']
            results['pattern_details'] = pattern_result

            return results

        except Exception as e:
            print(f"    ❌ 三维检测失败: {e}")
            return {
                'volume_score': 0,
                'resilience_score': 0,
                'pattern_score': 0,
                'volume_details': {},
                'resilience_details': {},
                'pattern_details': {}
            }

    def _detect_volume_pattern(self, kline_data):
        """维度1：量能密码检测（原版算法）"""
        try:
            if kline_data.empty or len(kline_data) < 20:
                return {'score': 0, 'reason': '数据不足'}

            # 寻找历史爆量日（涨跌幅大于等于5%） - 兼容中英文列名
            pct_chg_col = 'pct_chg' if 'pct_chg' in kline_data.columns else '涨跌幅'
            explosion_days = kline_data[
                (kline_data[pct_chg_col] >= 9.5) |  # 涨停
                (kline_data[pct_chg_col] >= 5.0)    # 大阳线
            ]

            if explosion_days.empty:
                return {'score': 0, 'reason': '无爆量日'}

            # 获取最大成交量日 - 兼容中英文列名
            volume_col = 'volume' if 'volume' in kline_data.columns else '成交量'
            max_volume_day = kline_data.loc[kline_data[volume_col].idxmax()]
            max_volume = max_volume_day[volume_col]

            # 获取最近5日平均成交量
            recent_5d_volume = kline_data.tail(5)[volume_col].mean()

            # 计算量能萎缩比例
            volume_shrink_ratio = recent_5d_volume / max_volume if max_volume > 0 else 1.0

            # 检查价格是否守住爆量日最低价 - 兼容中英文列名
            low_col = 'low' if 'low' in kline_data.columns else '最低'
            close_col = 'close' if 'close' in kline_data.columns else '收盘'
            max_volume_low = max_volume_day[low_col]
            current_price = kline_data[close_col].iloc[-1]
            price_held = current_price > max_volume_low

            # 评分逻辑
            score = 0

            # 量能萎缩评分（0-40分）
            if volume_shrink_ratio < 0.3:
                score += 40
            elif volume_shrink_ratio < 0.4:
                score += 30
            elif volume_shrink_ratio < 0.5:
                score += 20
            elif volume_shrink_ratio < 0.6:
                score += 10

            # 价格守住评分（0-20分）
            if price_held:
                score += 20

            # 连续缩量评分（0-20分）
            recent_volumes = kline_data.tail(5)[volume_col].tolist()
            is_continuous_shrink = all(recent_volumes[i] <= recent_volumes[i-1] * 1.1 for i in range(1, len(recent_volumes)))
            if is_continuous_shrink:
                score += 20

            return {
                'score': min(score, 100),
                'volume_shrink_ratio': volume_shrink_ratio,
                'price_held': price_held,
                'continuous_shrink': is_continuous_shrink,
                'max_volume': max_volume,
                'recent_avg_volume': recent_5d_volume,
                'reason': f'量能萎缩{volume_shrink_ratio:.2f}, 价格守住: {price_held}'
            }

        except Exception as e:
            return {'score': 0, 'reason': f'检测失败: {e}'}

    def _detect_price_resilience(self, kline_data, index_data):
        """维度2：价格韧性检测（抗跌性）（原版算法）"""
        try:
            if kline_data.empty or index_data.empty or len(kline_data) < 10:
                return {'score': 0, 'reason': '数据不足'}

            # 获取最近20日数据
            recent_data = kline_data.tail(20)

            # 找到大盘下跌日（跌幅>0.5%）
            fall_days = []
            red_days_in_fall = 0

            # 使用原版的逻辑处理日期和数据
            for _, row in recent_data.iterrows():
                try:
                    # 兼容中英文列名获取日期
                    if '日期' in row:
                        date = row['日期'].date()
                    elif 'date' in row:
                        date = pd.to_datetime(row['date']).date()
                    else:
                        continue

                    # 在指数数据中找到对应日期
                    index_row = index_data[index_data['date'].dt.date == date]

                    if not index_row.empty:
                        index_change = index_row['pct_chg'].iloc[0] if 'pct_chg' in index_row.columns else 0

                        if index_change < -0.5:  # 大盘下跌日
                            fall_days.append(date)
                            # 兼容中英文列名获取涨跌幅
                            if '涨跌幅' in row:
                                stock_change = row['涨跌幅']
                            elif 'pct_chg' in row:
                                stock_change = row['pct_chg']
                            else:
                                stock_change = 0

                            if stock_change > 0:  # 个股收阳
                                red_days_in_fall += 1
                except:
                    continue

            # 计算抗跌比例
            anti_fall_ratio = red_days_in_fall / len(fall_days) if fall_days else 0

            # 计算相对强度（个股vs大盘） - 兼容中英文列名
            close_col = 'close' if 'close' in recent_data.columns else '收盘'
            stock_change = (recent_data[close_col].iloc[-1] / recent_data[close_col].iloc[0] - 1) * 100

            # 获取同期大盘变化
            if not index_data.empty:
                index_recent = index_data.tail(20)
                if len(index_recent) >= 2:
                    index_change = (index_recent['close'].iloc[-1] / index_recent['close'].iloc[0] - 1) * 100
                    relative_strength = stock_change - index_change
                else:
                    relative_strength = stock_change
            else:
                relative_strength = stock_change

            # 评分逻辑
            score = 0

            # 抗跌比例评分（0-50分）
            if anti_fall_ratio >= 0.7:
                score += 50
            elif anti_fall_ratio >= 0.6:
                score += 40
            elif anti_fall_ratio >= 0.5:
                score += 30
            elif anti_fall_ratio >= 0.4:
                score += 20
            elif anti_fall_ratio >= 0.3:
                score += 10

            # 相对强度评分（0-30分）
            if relative_strength > 5:
                score += 30
            elif relative_strength > 2:
                score += 20
            elif relative_strength > 0:
                score += 10

            # 价格稳定性评分（0-20分） - 兼容中英文列名
            if 'pct_chg' in recent_data.columns:
                price_volatility = recent_data['pct_chg'].std()
            elif '涨跌幅' in recent_data.columns:
                price_volatility = recent_data['涨跌幅'].std()
            else:
                close_col = 'close' if 'close' in recent_data.columns else '收盘'
                price_volatility = recent_data[close_col].pct_change().std() * 100

            if price_volatility < 2:
                score += 20
            elif price_volatility < 3:
                score += 15
            elif price_volatility < 4:
                score += 10
            elif price_volatility < 5:
                score += 5

            return {
                'score': min(score, 100),
                'anti_fall_ratio': anti_fall_ratio,
                'relative_strength': relative_strength,
                'price_volatility': price_volatility,
                'fall_days_count': len(fall_days),
                'red_days_in_fall': red_days_in_fall,
                'reason': f'抗跌{anti_fall_ratio:.2f}, 相强{relative_strength:.1f}%, 波动{price_volatility:.1f}'
            }

        except Exception as e:
            return {'score': 0, 'reason': f'检测失败: {e}'}

    def _detect_washing_patterns(self, kline_data):
        """维度3：洗盘形态识别（完全按照原版算法）"""
        try:
            if kline_data.empty or len(kline_data) < 10:
                return {'score': 0, 'reason': '数据不足'}

            recent_data = kline_data.tail(10)  # 最近10日数据
            detected_patterns = []
            score = 0

            # 形态1：缩量十字星（振幅<3%且量比<0.5）
            doji_count = 0
            for _, row in recent_data.iterrows():
                # 使用原版的振幅获取方式（兼容中英文列名）
                amplitude = row.get('振幅', 0)
                if amplitude == 0:  # 如果没有振幅列，手动计算
                    open_price = row.get('开盘', row.get('open', 0))
                    high_price = row.get('最高', row.get('high', 0))
                    low_price = row.get('最低', row.get('low', 0))
                    if open_price > 0:
                        amplitude = ((high_price - low_price) / open_price) * 100

                volume = row.get('成交量', row.get('volume', 0))

                # 计算量比（当日量/5日均量）- 使用原版逻辑
                date_col = '日期' if '日期' in kline_data.columns else 'date'
                volume_col = '成交量' if '成交量' in kline_data.columns else 'volume'

                if date_col in row:
                    idx = kline_data.index[kline_data[date_col] == row[date_col]].tolist()
                    if idx:
                        pos = idx[0]
                        if pos >= 4:  # 确保有足够的历史数据
                            avg_volume = kline_data.iloc[pos-4:pos+1][volume_col].mean()
                            volume_ratio = volume / avg_volume if avg_volume > 0 else 1

                            if amplitude < self.config['doji_amplitude_threshold'] and volume_ratio < 0.5:
                                doji_count += 1

            if doji_count >= 2:
                detected_patterns.append('缩量十字星')
                score += 30

            # 形态2：长下影探针（下影线长度是实体的2倍+）
            probe_count = 0
            for _, row in recent_data.iterrows():
                # 兼容中英文列名
                open_price = row.get('开盘', row.get('open', 0))
                close_price = row.get('收盘', row.get('close', 0))
                high_price = row.get('最高', row.get('high', 0))
                low_price = row.get('最低', row.get('low', 0))

                if open_price > 0 and close_price > 0:
                    # 计算实体长度
                    body_length = abs(close_price - open_price)
                    # 计算下影线长度
                    lower_shadow = min(open_price, close_price) - low_price

                    if body_length > 0 and lower_shadow > body_length * 2:
                        probe_count += 1

            if probe_count >= 1:
                detected_patterns.append('长下影探针')
                score += 25

            # 形态3：假阴真阳（高开低走但收盘价>前日收盘）
            fake_negative_count = 0
            for i in range(1, len(recent_data)):
                current = recent_data.iloc[i]
                previous = recent_data.iloc[i-1]

                # 兼容中英文列名
                open_price = current.get('开盘', current.get('open', 0))
                close_price = current.get('收盘', current.get('close', 0))
                prev_close = previous.get('收盘', previous.get('close', 0))

                # 高开低走但收盘价>前日收盘
                if (open_price > prev_close and  # 高开
                    close_price < open_price and  # 低走
                    close_price > prev_close):    # 收盘价>前日收盘
                    fake_negative_count += 1

            if fake_negative_count >= 1:
                detected_patterns.append('假阴真阳')
                score += 20

            # 形态4：地量见地价（量创新低但价格未破前低）
            volume_col = '成交量' if '成交量' in recent_data.columns else 'volume'
            low_col = '最低' if '最低' in recent_data.columns else 'low'

            recent_volume = recent_data[volume_col].min()
            recent_low = recent_data[low_col].min()

            # 检查是否是近期地量
            historical_volume = kline_data.tail(30)[volume_col]
            is_ground_volume = recent_volume == historical_volume.min()

            # 检查价格是否未破前低
            historical_low = kline_data.tail(30)[low_col].min()
            price_not_break = recent_low > historical_low * 0.95  # 允许5%的误差

            if is_ground_volume and price_not_break:
                detected_patterns.append('地量见地价')
                score += 25

            return {
                'score': min(score, 100),
                'detected_patterns': detected_patterns,
                'doji_count': doji_count,
                'probe_count': probe_count,
                'fake_negative_count': fake_negative_count,
                'ground_volume_price': is_ground_volume and price_not_break,
                'reason': f'形态: {", ".join(detected_patterns) if detected_patterns else "无"}'
            }

        except Exception as e:
            return {'score': 0, 'reason': f'检测失败: {e}'}

    # ==================== 辅助方法 ====================

    def _normalize_stock_code(self, code):
        """标准化股票代码"""
        if not code:
            return ""

        # 转换为字符串并清理
        code = str(code).strip().upper()

        # 移除点号分隔符（先处理点号，再处理前缀）
        if '.' in code:
            parts = code.split('.')
            if len(parts) >= 2:
                # 取点号后面的部分作为股票代码
                code = parts[-1]

        # 移除常见前缀（如sh、sz、SH、SZ等）
        if code.startswith(('SH', 'SZ')):
            code = code[2:]
        elif code.startswith(('sh', 'sz')):
            code = code[2:]

        # 确保是6位数字
        if code.isdigit() and len(code) == 6:
            return code

        # 如果不足6位，尝试补零（针对某些特殊情况）
        if code.isdigit() and len(code) < 6:
            code = code.zfill(6)
            return code

        return ""

    def _normalize_stock_code_for_akshare(self, code):
        """为AKShare标准化股票代码"""
        if not code or len(code) != 6:
            return code

        # 根据代码前缀判断市场
        if code.startswith(('000', '001', '002', '003')):
            return code  # 深圳主板/中小板/创业板
        elif code.startswith('6'):
            return code  # 上海主板
        else:
            return code

    def _check_market_scope(self, code, name):
        """检查股票是否在分析范围内"""
        if not code or len(code) != 6:
            return False

        # 排除ST股票
        if 'ST' in name or '*ST' in name:
            return False

        # 排除创业板（300开头）
        if code.startswith('300'):
            return False

        # 排除科创板（688开头）
        if code.startswith('688'):
            return False

        # 排除北交所（8开头或4开头）
        if code.startswith(('8', '4')):
            return False

        return True

    def _get_market_cap(self, stock_data):
        """获取市值（亿元）"""
        try:
            # 尝试获取总市值
            market_cap = stock_data.get('总市值', 0) or stock_data.get('mktcap', 0)

            if market_cap and market_cap > 0:
                # 转换为亿元（假设原始数据是万元）
                return market_cap / 10000

            # 如果没有市值数据，返回0
            return 0

        except Exception as e:
            return 0

    def _standardize_kline_columns(self, df):
        """标准化K线数据列名"""
        try:
            # 创建列名映射
            column_mapping = {
                '日期': 'date',
                '开盘': 'open',
                '最高': 'high',
                '最低': 'low',
                '收盘': 'close',
                '成交量': 'volume',
                '成交额': 'amount',
                '涨跌幅': 'pct_chg',
                '涨跌额': 'change',
                '换手率': 'turnover'
            }

            # 重命名列
            df = df.rename(columns=column_mapping)

            # 确保日期列存在且格式正确
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])

            # 确保数值列为数值类型
            numeric_columns = ['open', 'high', 'low', 'close', 'volume', 'amount']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            return df

        except Exception as e:
            return df

    def _get_limit_up_statistics(self):
        """获取涨停统计数据（完全按照原版算法）"""
        try:
            print("📊 统计涨停数据...")

            # 获取最近一年的交易日（使用原版方法）
            date_list = self._get_recent_trading_days(250)  # 约一年

            limit_up_stats = defaultdict(lambda: {'count': 0, 'dates': [], 'last_date': None})

            for date_str in date_list:
                file_path = os.path.join(self.limit_up_data_dir, f"{date_str}.json")

                if not os.path.exists(file_path):
                    continue

                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    # 获取涨停股票列表（使用实际的数据结构）
                    stocks = data.get('stocks', [])

                    if not stocks:
                        continue

                    for stock in stocks:
                        stock_code = stock.get('code', '')
                        stock_name = stock.get('name', '')

                        if stock_code:
                            # 标准化股票代码
                            normalized_code = self._normalize_stock_code(stock_code)
                            if not normalized_code:
                                continue

                            # 使用标准化的股票代码作为key
                            limit_up_stats[normalized_code]['count'] += 1
                            limit_up_stats[normalized_code]['dates'].append(date_str)
                            limit_up_stats[normalized_code]['name'] = stock_name  # 保存股票名称

                            # 更新最后涨停日期
                            if not limit_up_stats[normalized_code]['last_date'] or date_str > limit_up_stats[normalized_code]['last_date']:
                                limit_up_stats[normalized_code]['last_date'] = date_str

                except Exception as e:
                    continue

            # 直接返回基于股票代码的统计结果（使用原版格式）
            print(f"✅ 涨停统计完成: 处理 {len(date_list)} 个交易日, 发现 {len(limit_up_stats)} 只活跃股票")

            return dict(limit_up_stats)

        except Exception as e:
            print(f"❌ 获取涨停统计失败: {e}")
            return {}

    def _get_recent_trading_days(self, days):
        """获取最近N个交易日（完全按照原版算法）"""
        date_list = []
        current = self.current_date

        search_range = days * 2  # 扩大搜索范围

        for i in range(search_range):
            check_date = current - timedelta(days=i)
            # 跳过周末
            if check_date.weekday() < 5:  # 0-4是周一到周五
                date_str = check_date.strftime('%Y%m%d')  # 使用原版的日期格式
                date_list.append(date_str)
                if len(date_list) >= days:
                    break

        return sorted(date_list)  # 按日期正序返回（原版格式）

    def _display_results(self, final_candidates):
        """显示分析结果"""
        try:
            if not final_candidates:
                print("\n📊 分析结果：未发现符合条件的洗盘股票")
                return

            print(f"\n📊 分析结果：发现 {len(final_candidates)} 只潜在洗盘股票")
            print("=" * 80)

            # 按综合评分排序
            sorted_candidates = sorted(final_candidates,
                                     key=lambda x: x['comprehensive_score'],
                                     reverse=True)

            for i, candidate in enumerate(sorted_candidates[:10], 1):  # 显示前10只
                code = candidate['code']
                name = candidate['name']
                result = candidate['dimension_results']

                print(f"\n🏆 第{i}名: {code} {name}")
                print(f"   💰 市值: {candidate.get('market_cap', 0):.1f}亿元")
                print(f"   📈 涨停次数: {candidate.get('limit_up_count', 0)}次")
                print(f"   📅 距今天数: {candidate.get('days_since_limit_up', 0)}天")
                print(f"   📊 量能密码: {result['volume_score']:.1f}分")
                print(f"   📊 价格韧性: {result['resilience_score']:.1f}分")
                print(f"   📊 形态识别: {result['pattern_score']:.1f}分")
                print(f"   🎯 综合评分: {candidate['comprehensive_score']:.1f}分")

            if len(sorted_candidates) > 10:
                print(f"\n... 还有 {len(sorted_candidates) - 10} 只股票")

        except Exception as e:
            print(f"❌ 结果显示失败: {e}")

    def _export_results(self, candidates):
        """导出结果到Excel（完全按照原版）"""
        try:
            if not candidates:
                return

            print("\n📁 导出分析结果...")

            # 准备导出数据
            export_data = []

            for candidate in candidates:
                try:
                    dimension_results = candidate.get('dimension_results', {})
                    volume_details = dimension_results.get('volume_details', {})
                    resilience_details = dimension_results.get('resilience_details', {})
                    pattern_details = dimension_results.get('pattern_details', {})

                    row = {
                        # 基础信息
                        '股票代码': candidate.get('code', 'N/A'),
                        '股票名称': candidate.get('name', 'N/A'),
                        '最新价格': candidate.get('latest_price', 0),
                        '涨跌幅(%)': candidate.get('change_pct', 0),
                        '市值(亿元)': candidate.get('market_cap', 0),
                        '换手率(%)': candidate.get('turnover_rate', 0),

                        # 活跃股基因
                        '年内涨停次数': candidate.get('limit_up_count', 0),
                        '距最近涨停天数': candidate.get('days_since_limit_up', 0),
                        '最近涨停日期': candidate.get('last_limit_up_date', 'N/A'),

                        # 三维评分
                        '综合评分': candidate.get('comprehensive_score', 0),
                        '量能密码评分': dimension_results.get('volume_score', 0),
                        '价格韧性评分': dimension_results.get('resilience_score', 0),
                        '形态识别评分': dimension_results.get('pattern_score', 0),

                        # 量能密码详情
                        '量能萎缩比例': volume_details.get('volume_shrink_ratio', 0),
                        '价格是否守住': '是' if volume_details.get('price_held', False) else '否',
                        '是否连续缩量': '是' if volume_details.get('continuous_shrink', False) else '否',
                        '最大成交量': volume_details.get('max_volume', 0),
                        '近5日平均量': volume_details.get('recent_avg_volume', 0),

                        # 价格韧性详情
                        '抗跌比例': resilience_details.get('anti_fall_ratio', 0),
                        '相对强度(%)': resilience_details.get('relative_strength', 0),
                        '价格波动率': resilience_details.get('price_volatility', 0),
                        '大盘下跌天数': resilience_details.get('fall_days_count', 0),
                        '逆势收阳天数': resilience_details.get('red_days_in_fall', 0),

                        # 形态识别详情
                        '检测到的洗盘形态': ', '.join(pattern_details.get('detected_patterns', [])),
                        '缩量十字星次数': pattern_details.get('doji_count', 0),
                        '长下影探针次数': pattern_details.get('probe_count', 0),
                        '假阴真阳次数': pattern_details.get('fake_negative_count', 0),
                        '地量见地价': '是' if pattern_details.get('ground_volume_price', False) else '否'
                    }

                    export_data.append(row)

                except Exception as e:
                    print(f"  ⚠️ 处理股票数据失败: {e}")
                    continue

            if not export_data:
                print("❌ 无有效数据可导出")
                return

            # 创建DataFrame并导出
            df = pd.DataFrame(export_data)

            # 按综合评分排序
            df = df.sort_values('综合评分', ascending=False)

            # 生成文件名
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"游资洗盘行为量化分析_{timestamp}.xlsx"

            # 导出到根目录
            root_path = os.getcwd()
            full_path = os.path.join(root_path, filename)

            df.to_excel(full_path, index=False, engine='openpyxl')
            print(f"📁 结果已导出到: {full_path}")
            print(f"📊 共导出 {len(df)} 只股票的详细分析数据")

            # 显示导出统计
            high_score_count = len(df[df['综合评分'] >= 80])
            medium_score_count = len(df[(df['综合评分'] >= 60) & (df['综合评分'] < 80)])

            print(f"📈 评分分布: 高分(≥80分) {high_score_count}只, 中分(60-80分) {medium_score_count}只")

        except Exception as e:
            print(f"❌ 导出结果失败: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主程序入口"""
    print("🚀 游资洗盘行为量化捕手系统（优化版）")
    print("基于三维共振模型的全A股游资洗盘痕迹识别")
    print("=" * 60)

    try:
        # 检查涨停数据目录
        limit_up_dir = os.path.join("..", "limit_up_data")
        if not os.path.exists(limit_up_dir):
            print(f"❌ 涨停数据目录不存在: {limit_up_dir}")
            print("请确保涨停数据目录存在并包含历史数据")
            return

        data_files = [f for f in os.listdir(limit_up_dir) if f.endswith('.json')]
        print(f"✅ 涨停数据目录存在，包含 {len(data_files)} 个数据文件")

        # 创建检测器
        print("🔧 创建游资洗盘检测器...")
        detector = OptimizedGameCapitalWashingDetector()

        # 运行分析
        print("🚀 开始运行分析...")
        results = detector.run_analysis()

        if results:
            print(f"\n🎉 分析完成！发现 {len(results)} 只潜在洗盘股票")
        else:
            print("\n📊 分析完成，未发现符合条件的股票")

    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
