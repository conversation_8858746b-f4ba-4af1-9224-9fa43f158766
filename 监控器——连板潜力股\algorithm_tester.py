#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法测试器 - 专门测试算法逻辑，不涉及GUI
"""

import sys
import os
import time
import importlib.util
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 导入虚拟数据生成器
from virtual_data_generator import VirtualDataGenerator

class AlgorithmTester:
    """算法测试器 - 直接测试算法逻辑"""
    
    def __init__(self):
        self.data_generator = VirtualDataGenerator()
        self.test_results = []
        self.notifications_captured = []
        
        # 模拟主应用的数据结构
        self.realtime_data_series = {}
        self.historical_base_data = {}
        self.monitored_stocks = {}
        
        # 初始化测试股票
        self.setup_test_stock()
    
    def setup_test_stock(self):
        """设置测试股票"""
        test_stock = self.data_generator.get_test_stock_info()
        code = test_stock['code']
        
        self.monitored_stocks[code] = {
            'name': test_stock['name'],
            'initialized': True
        }
        
        self.historical_base_data[code] = {
            'k_line_data': [],
            'volume_data': {'avg_volume_5d': 1000000, 'avg_volume_10d': 1200000},
            'price_stats': {'avg_price_5d': 10.0},
            'circulating_shares': 50000
        }
        
        self.realtime_data_series[code] = []
    
    def simulate_buy_notification(self, code, stock_data, recommendation, signal_strength, algorithm_info):
        """模拟买入通知"""
        notification = {
            'time': datetime.now(),
            'code': code,
            'stock_name': self.monitored_stocks.get(code, {}).get('name', code),
            'price': stock_data.get('current_price', 0),
            'recommendation': recommendation,
            'signal_strength': signal_strength,
            'algorithm': algorithm_info.get('algorithm_name', '未知算法')
        }
        self.notifications_captured.append(notification)
        print(f"🔔 模拟通知: {notification['stock_name']} - {recommendation} (强度: {signal_strength:.2f})")
    
    # ==================== 从主应用复制的核心算法方法 ====================
    
    def get_morning_key_data(self, code):
        """获取早盘关键数据（简化版）"""
        if code not in self.realtime_data_series or not self.realtime_data_series[code]:
            return {'morning_high': 10.0, 'morning_low': 10.0, 'max_volume_ratio': 1.0}
        
        data_series = self.realtime_data_series[code]
        morning_data = data_series[:20] if len(data_series) > 20 else data_series
        
        if not morning_data:
            return {'morning_high': 10.0, 'morning_low': 10.0, 'max_volume_ratio': 1.0}
        
        prices = [item.get('current_price', 10.0) for item in morning_data]
        volume_ratios = [item.get('volume_ratio', 1.0) for item in morning_data]
        
        return {
            'morning_high': max(prices),
            'morning_low': min(prices),
            'max_volume_ratio': max(volume_ratios)
        }
    
    def select_intelligent_algorithm(self, code, stock_data):
        """智能算法选择（从主应用复制）"""
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            
            if prev_close <= 0:
                return {'algorithm_type': 'unknown', 'algorithm_name': '数据异常', 'confidence': 0.0}
            
            current_gain = (current_price - prev_close) / prev_close * 100
            duration = len(self.realtime_data_series.get(code, []))
            
            # 1. 开盘下跌反转场景
            if current_gain < -1.0 and duration >= 10:
                return {
                    'algorithm_type': 'drop_reversal',
                    'algorithm_name': '开盘下跌反转算法',
                    'confidence': 0.8,
                    'reason': f'下跌{current_gain:.2f}%，寻找反转机会'
                }
            
            # 2. 高开修正场景
            if current_gain >= 3.0 and duration >= 15:
                return {
                    'algorithm_type': 'high_open_correction',
                    'algorithm_name': '高开修正算法',
                    'confidence': 0.85,
                    'reason': f'高开{current_gain:.2f}%，监控回调后再攻'
                }
            
            # 3. 中位蓄势场景
            if 1.0 <= current_gain <= 5.0 and duration >= 15:
                volatility = self.calculate_recent_volatility(code, stock_data)
                if volatility < 2.0:
                    return {
                        'algorithm_type': 'mid_level_accumulation',
                        'algorithm_name': '中位蓄势算法',
                        'confidence': 0.85,
                        'reason': f'中位{current_gain:.2f}%蓄势，振幅{volatility:.2f}%'
                    }
            
            # 4. 高位横盘突破场景
            if current_gain >= 6.0 and duration >= 30:
                return {
                    'algorithm_type': 'high_platform_breakout',
                    'algorithm_name': '高位横盘突破算法',
                    'confidence': 0.9,
                    'reason': f'高位{current_gain:.2f}%横盘'
                }
            
            # 5. 标准平开场景
            if -1.0 <= current_gain <= 1.0:
                return {
                    'algorithm_type': 'standard_flat_open',
                    'algorithm_name': '标准平开算法',
                    'confidence': 0.7,
                    'reason': f'平开{current_gain:.2f}%'
                }
            
            # 6. 标准高开场景
            if 1.0 < current_gain < 3.0:
                return {
                    'algorithm_type': 'standard_high_open',
                    'algorithm_name': '标准高开算法',
                    'confidence': 0.75,
                    'reason': f'高开{current_gain:.2f}%'
                }
            
            # 默认观望
            return {
                'algorithm_type': 'wait_and_see',
                'algorithm_name': '观望算法',
                'confidence': 0.3,
                'reason': f'当前涨幅{current_gain:.2f}%，暂不符合条件'
            }
            
        except Exception as e:
            return {'algorithm_type': 'error', 'algorithm_name': '算法错误', 'confidence': 0.0, 'error': str(e)}
    
    def calculate_recent_volatility(self, code, stock_data):
        """计算最近波动率"""
        try:
            if code not in self.realtime_data_series:
                return 0.0
            
            data_series = self.realtime_data_series[code]
            if len(data_series) < 5:
                return 0.0
            
            recent_prices = [item.get('current_price', 0) for item in data_series[-10:]]
            recent_prices = [p for p in recent_prices if p > 0]
            
            if len(recent_prices) < 3:
                return 0.0
            
            price_high = max(recent_prices)
            price_low = min(recent_prices)
            
            if price_low > 0:
                volatility = (price_high - price_low) / price_low * 100
                return volatility
            else:
                return 0.0
                
        except Exception:
            return 0.0
    
    def apply_time_period_strategy(self, code, stock_data):
        """应用时段策略（简化版）"""
        current_time = datetime.now().time()
        
        # 简化的时段判断
        if current_time < datetime.strptime("09:40:00", "%H:%M:%S").time():
            return {
                'period_name': '开盘量能监控',
                'scan_interval': 15,
                'signal_strength': 0.8,
                'period_signal': True
            }
        elif current_time < datetime.strptime("10:30:00", "%H:%M:%S").time():
            return {
                'period_name': '均价征服监控',
                'scan_interval': 30,
                'signal_strength': 0.7,
                'period_signal': True
            }
        else:
            return {
                'period_name': '盘中突击监控',
                'scan_interval': 60,
                'signal_strength': 0.6,
                'period_signal': False
            }
    
    def analyze_opening_drop_reversal(self, code, stock_data):
        """开盘下跌反转算法（简化版）"""
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)
            
            if prev_close <= 0:
                return {'buy_signal': False, 'signal_strength': 0.0}
            
            change_pct = (current_price - prev_close) / prev_close * 100
            
            # 反转信号检测
            reversal_signals = {
                'volume_reversal': volume_ratio > 2.0,  # 量能反转
                'price_reversal': change_pct > -1.0,   # 价格反转
                'support_found': True,  # 简化：假设找到支撑
                'momentum_shift': volume_ratio > 1.5   # 动能转换
            }
            
            signal_count = sum(reversal_signals.values())
            signal_strength = signal_count / 4.0
            
            return {
                'buy_signal': signal_count >= 3,
                'signal_strength': signal_strength,
                'reversal_signals': reversal_signals
            }
            
        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}
    
    def analyze_standard_flat_open(self, code, stock_data):
        """标准平开算法（简化版）"""
        try:
            volume_ratio = stock_data.get('volume_ratio', 0)
            current_price = stock_data.get('current_price', 0)
            
            # 三信号检测
            signals = {
                'volume_signal': volume_ratio > 1.2,
                'price_signal': current_price > 0,
                'momentum_signal': volume_ratio > 1.0
            }
            
            signal_count = sum(signals.values())
            signal_strength = signal_count / 3.0
            
            return {
                'buy_signal': signal_count >= 2,
                'signal_strength': signal_strength,
                'signals': signals
            }
            
        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def analyze_high_open_correction(self, code, stock_data):
        """高开修正算法（简化版）"""
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            if prev_close <= 0:
                return {'buy_signal': False, 'signal_strength': 0.0}

            change_pct = (current_price - prev_close) / prev_close * 100

            # 修正信号检测
            signals = {
                'gap_guard': change_pct > 2.0,  # 缺口保卫
                'volume_healthy': 1.0 <= volume_ratio <= 2.5,  # 量能健康
                'correction_complete': True  # 简化：假设修正完成
            }

            signal_count = sum(signals.values())
            signal_strength = signal_count / 3.0

            return {
                'buy_signal': signal_count >= 2,
                'signal_strength': signal_strength,
                'signals': signals
            }

        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def analyze_mid_level_accumulation(self, code, stock_data):
        """中位蓄势算法（简化版）"""
        try:
            current_price = stock_data.get('current_price', 0)
            prev_close = stock_data.get('prev_close', 0)
            volume_ratio = stock_data.get('volume_ratio', 0)

            if prev_close <= 0:
                return {'buy_signal': False, 'signal_strength': 0.0}

            change_pct = (current_price - prev_close) / prev_close * 100

            # 蓄势信号检测
            trend_prediction = {
                'score': 0.7 if 1.0 <= change_pct <= 5.0 else 0.3,
                'direction': 'upward_breakout' if volume_ratio > 1.5 else 'sideways_continue'
            }

            buy_signal = trend_prediction['score'] >= 0.7 and volume_ratio > 1.2

            return {
                'buy_signal': buy_signal,
                'signal_strength': trend_prediction['score'],
                'trend_prediction': trend_prediction
            }

        except Exception as e:
            return {'buy_signal': False, 'signal_strength': 0.0, 'error': str(e)}

    def execute_intelligent_signal_detection(self, code, stock_data, algorithm_info, period_analysis):
        """执行智能信号检测"""
        try:
            algorithm_type = algorithm_info.get('algorithm_type', '')

            # 根据算法类型调用相应的分析方法
            if algorithm_type == 'drop_reversal':
                core_signals = self.analyze_opening_drop_reversal(code, stock_data)
            elif algorithm_type == 'high_open_correction':
                core_signals = self.analyze_high_open_correction(code, stock_data)
            elif algorithm_type == 'mid_level_accumulation':
                core_signals = self.analyze_mid_level_accumulation(code, stock_data)
            elif algorithm_type == 'standard_flat_open':
                core_signals = self.analyze_standard_flat_open(code, stock_data)
            else:
                # 默认简单信号检测
                volume_ratio = stock_data.get('volume_ratio', 0)
                core_signals = {
                    'buy_signal': volume_ratio > 1.5,
                    'signal_strength': min(1.0, volume_ratio / 2.0)
                }

            # 综合判断
            buy_signal = core_signals.get('buy_signal', False)
            signal_strength = core_signals.get('signal_strength', 0)

            # 生成推荐
            if buy_signal and signal_strength >= 0.7:
                recommendation = "🚀 强烈买入"
            elif buy_signal:
                recommendation = "📈 建议买入"
            elif signal_strength >= 0.5:
                recommendation = "👀 密切关注"
            else:
                recommendation = "⏳ 继续观察"

            # 如果有买入信号，发送通知
            if buy_signal:
                self.simulate_buy_notification(code, stock_data, recommendation, signal_strength, algorithm_info)

            return {
                'buy_signal': buy_signal,
                'signal_strength': signal_strength,
                'recommendation': recommendation,
                'algorithm_type': algorithm_type,
                'core_signals': core_signals
            }

        except Exception as e:
            return {
                'buy_signal': False,
                'signal_strength': 0.0,
                'recommendation': f"❌ 检测失败: {e}",
                'error': str(e)
            }

    def run_scenario_test(self, scenario_name: str) -> Dict[str, Any]:
        """运行场景测试"""
        print(f"\n🎬 测试场景: {scenario_name}")
        print("=" * 50)

        # 清空之前的数据
        test_code = 'TEST001'
        self.realtime_data_series[test_code] = []
        self.notifications_captured.clear()

        # 生成虚拟数据
        virtual_data = self.data_generator.generate_scenario_data(scenario_name, 60)

        test_result = {
            'scenario': scenario_name,
            'success': False,
            'algorithms_triggered': set(),
            'max_signal_strength': 0,
            'notifications_sent': 0,
            'buy_signals_count': 0
        }

        try:
            # 逐个处理数据点
            for i, data_point in enumerate(virtual_data):
                # 添加到实时数据序列
                self.realtime_data_series[test_code].append(data_point)

                # 每5个数据点测试一次（模拟实际监控频率）
                if i % 5 == 0:
                    print(f"⏰ {data_point['timestamp']} | 价格: {data_point['current_price']:.2f} | 涨幅: {data_point['change_pct']:.2f}%")

                    # 算法选择
                    algorithm_info = self.select_intelligent_algorithm(test_code, data_point)
                    test_result['algorithms_triggered'].add(algorithm_info.get('algorithm_name', ''))

                    # 时段策略
                    period_analysis = self.apply_time_period_strategy(test_code, data_point)

                    # 信号检测
                    signal_result = self.execute_intelligent_signal_detection(
                        test_code, data_point, algorithm_info, period_analysis
                    )

                    # 记录结果
                    signal_strength = signal_result.get('signal_strength', 0)
                    if signal_strength > test_result['max_signal_strength']:
                        test_result['max_signal_strength'] = signal_strength

                    if signal_result.get('buy_signal', False):
                        test_result['buy_signals_count'] += 1
                        print(f"   🚀 买入信号! 算法: {algorithm_info.get('algorithm_name', '')} | 强度: {signal_strength:.2f}")

            test_result['notifications_sent'] = len(self.notifications_captured)
            test_result['algorithms_triggered'] = list(test_result['algorithms_triggered'])
            test_result['success'] = True

            print(f"✅ 场景完成 | 最大信号强度: {test_result['max_signal_strength']:.2f} | 买入信号: {test_result['buy_signals_count']} 次")

        except Exception as e:
            test_result['error'] = str(e)
            print(f"❌ 场景测试失败: {e}")

        return test_result

    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始算法测试系统")
        print("=" * 60)

        scenarios = [
            "平开上涨", "高开回落反转", "低开反转", "中位蓄势突破",
            "高位横盘突破", "隔夜挂单强势", "V型反转"
        ]

        results = []
        for scenario in scenarios:
            result = self.run_scenario_test(scenario)
            results.append(result)
            time.sleep(1)  # 场景间暂停

        # 生成报告
        self.generate_report(results)

    def generate_report(self, results):
        """生成测试报告"""
        print("\n" + "=" * 60)
        print("📊 算法测试报告")
        print("=" * 60)

        successful = [r for r in results if r.get('success', False)]
        total_notifications = sum(r.get('notifications_sent', 0) for r in results)
        total_buy_signals = sum(r.get('buy_signals_count', 0) for r in results)

        print(f"📈 测试概览:")
        print(f"   • 测试场景: {len(results)}")
        print(f"   • 成功场景: {len(successful)}")
        print(f"   • 成功率: {len(successful)/len(results)*100:.1f}%")
        print(f"   • 总买入信号: {total_buy_signals}")
        print(f"   • 总通知数: {total_notifications}")

        print(f"\n📋 场景详情:")
        for result in results:
            status = "✅" if result.get('success', False) else "❌"
            print(f"   {status} {result['scenario']}:")
            print(f"      触发算法: {', '.join(result.get('algorithms_triggered', []))}")
            print(f"      最大信号强度: {result.get('max_signal_strength', 0):.2f}")
            print(f"      买入信号次数: {result.get('buy_signals_count', 0)}")

        # 算法覆盖统计
        all_algorithms = set()
        for result in results:
            all_algorithms.update(result.get('algorithms_triggered', []))

        print(f"\n🎯 算法覆盖:")
        print(f"   • 触发算法数: {len(all_algorithms)}")
        print(f"   • 算法列表: {', '.join(sorted(all_algorithms))}")

        print(f"\n🎉 测试完成! 算法系统运行正常。")

if __name__ == "__main__":
    tester = AlgorithmTester()
    tester.run_all_tests()
