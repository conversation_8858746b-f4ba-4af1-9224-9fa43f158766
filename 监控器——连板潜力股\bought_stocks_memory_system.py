#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
已买入股票记忆系统
专门为已买入股票服务的完整历史表现跟踪系统
"""

import json
import os
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Any, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class BoughtStocksMemorySystem:
    """已买入股票记忆系统"""
    
    def __init__(self, data_dir: str = "bought_stocks_memory"):
        """
        初始化记忆系统
        
        Args:
            data_dir: 数据存储目录
        """
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(exist_ok=True)
        
        # 数据文件路径
        self.daily_performance_file = self.data_dir / "daily_performance.json"
        self.wash_probability_history_file = self.data_dir / "wash_probability_history.json"
        self.decision_history_file = self.data_dir / "decision_history.json"
        
        # 内存数据
        self.daily_performance = {}  # {code: {date: performance_data}}
        self.wash_probability_history = {}  # {code: {date: wash_data}}
        self.decision_history = {}  # {code: {date: decision_data}}
        
        # 加载现有数据
        self.load_all_data()
        
        logger.info(f"✅ 已买入股票记忆系统初始化完成，数据目录: {self.data_dir}")
    
    def record_daily_performance(self, stock_code: str, stock_name: str, 
                               current_price: float, buy_price: float, 
                               change_pct: float, volume_ratio: float,
                               hold_days: int, additional_data: Dict = None) -> bool:
        """
        记录股票的每日表现
        
        Args:
            stock_code: 股票代码
            stock_name: 股票名称
            current_price: 当前价格
            buy_price: 买入价格
            change_pct: 当日涨跌幅（相对昨收）
            volume_ratio: 量比
            hold_days: 持有天数
            additional_data: 额外数据
            
        Returns:
            bool: 记录是否成功
        """
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now()
            
            # 计算相对买入价的总盈亏
            total_profit_pct = (current_price - buy_price) / buy_price * 100
            
            # 计算相对前一天的表现（如果有历史数据）
            day_to_day_change = self._calculate_day_to_day_change(stock_code, current_price)
            
            # 构建每日表现数据
            performance_data = {
                'date': current_date,
                'time': current_time.strftime('%H:%M:%S'),
                'datetime': current_time.isoformat(),
                'stock_name': stock_name,
                'hold_days': hold_days,
                'current_price': current_price,
                'buy_price': buy_price,
                'change_pct': change_pct,  # 相对昨收
                'total_profit_pct': total_profit_pct,  # 相对买入价
                'day_to_day_change': day_to_day_change,  # 相对前一天
                'volume_ratio': volume_ratio,
                'additional_data': additional_data or {}
            }
            
            # 存储到内存
            if stock_code not in self.daily_performance:
                self.daily_performance[stock_code] = {}
            
            self.daily_performance[stock_code][current_date] = performance_data
            
            # 保存到文件
            self._save_daily_performance()
            
            logger.info(f"✅ 记录每日表现: {stock_code} {stock_name} 第{hold_days}天")
            return True
            
        except Exception as e:
            logger.error(f"记录每日表现失败 {stock_code}: {e}")
            return False
    
    def record_wash_probability(self, stock_code: str, wash_probability: float,
                              wash_signals: List[str], confidence: str,
                              volume_score: float, pattern_score: float,
                              capital_score: float, regulation_score: float) -> bool:
        """
        记录洗盘概率历史
        
        Args:
            stock_code: 股票代码
            wash_probability: 洗盘概率
            wash_signals: 洗盘信号列表
            confidence: 置信度
            volume_score: 量能评分
            pattern_score: 形态评分
            capital_score: 资金评分
            regulation_score: 监管评分
            
        Returns:
            bool: 记录是否成功
        """
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now()
            
            # 构建洗盘概率数据
            wash_data = {
                'date': current_date,
                'time': current_time.strftime('%H:%M:%S'),
                'datetime': current_time.isoformat(),
                'wash_probability': wash_probability,
                'wash_signals': wash_signals,
                'confidence': confidence,
                'scores': {
                    'volume_score': volume_score,
                    'pattern_score': pattern_score,
                    'capital_score': capital_score,
                    'regulation_score': regulation_score
                }
            }
            
            # 存储到内存
            if stock_code not in self.wash_probability_history:
                self.wash_probability_history[stock_code] = {}
            
            self.wash_probability_history[stock_code][current_date] = wash_data
            
            # 保存到文件
            self._save_wash_probability_history()
            
            logger.info(f"✅ 记录洗盘概率: {stock_code} {wash_probability:.1%}")
            return True
            
        except Exception as e:
            logger.error(f"记录洗盘概率失败 {stock_code}: {e}")
            return False
    
    def record_decision(self, stock_code: str, decision: str, action: str,
                       confidence: str, reason: str, hold_days: int) -> bool:
        """
        记录决策历史
        
        Args:
            stock_code: 股票代码
            decision: 决策建议
            action: 操作类型
            confidence: 置信度
            reason: 决策原因
            hold_days: 持有天数
            
        Returns:
            bool: 记录是否成功
        """
        try:
            current_date = datetime.now().strftime('%Y-%m-%d')
            current_time = datetime.now()
            
            # 构建决策数据
            decision_data = {
                'date': current_date,
                'time': current_time.strftime('%H:%M:%S'),
                'datetime': current_time.isoformat(),
                'hold_days': hold_days,
                'decision': decision,
                'action': action,
                'confidence': confidence,
                'reason': reason
            }
            
            # 存储到内存
            if stock_code not in self.decision_history:
                self.decision_history[stock_code] = {}
            
            self.decision_history[stock_code][current_date] = decision_data
            
            # 保存到文件
            self._save_decision_history()
            
            logger.info(f"✅ 记录决策: {stock_code} {decision}")
            return True
            
        except Exception as e:
            logger.error(f"记录决策失败 {stock_code}: {e}")
            return False
    
    def get_multi_day_performance(self, stock_code: str) -> Dict[str, Any]:
        """
        获取多日表现分析
        
        Args:
            stock_code: 股票代码
            
        Returns:
            Dict: 多日表现分析结果
        """
        try:
            if stock_code not in self.daily_performance:
                return {'error': '无历史数据'}
            
            daily_data = self.daily_performance[stock_code]
            sorted_dates = sorted(daily_data.keys())
            
            if len(sorted_dates) < 2:
                return {'error': '数据不足，需要至少2天数据'}
            
            # 分析每日表现
            daily_analysis = []
            for i, date in enumerate(sorted_dates):
                day_data = daily_data[date]
                hold_days = day_data['hold_days']
                
                analysis = {
                    'date': date,
                    'hold_days': hold_days,
                    'day_name': f'第{hold_days}天',
                    'current_price': day_data['current_price'],
                    'change_pct': day_data['change_pct'],
                    'total_profit_pct': day_data['total_profit_pct'],
                    'day_to_day_change': day_data.get('day_to_day_change', 0),
                    'volume_ratio': day_data['volume_ratio']
                }
                
                # 判断当日表现
                if day_data['change_pct'] > 5:
                    analysis['performance'] = '强势上涨'
                elif day_data['change_pct'] > 0:
                    analysis['performance'] = '温和上涨'
                elif day_data['change_pct'] > -3:
                    analysis['performance'] = '小幅下跌'
                else:
                    analysis['performance'] = '大幅下跌'
                
                daily_analysis.append(analysis)
            
            # 分析连续表现趋势
            trend_analysis = self._analyze_performance_trend(daily_analysis)
            
            return {
                'stock_code': stock_code,
                'total_days': len(sorted_dates),
                'daily_analysis': daily_analysis,
                'trend_analysis': trend_analysis,
                'latest_performance': daily_analysis[-1] if daily_analysis else None
            }
            
        except Exception as e:
            logger.error(f"获取多日表现失败 {stock_code}: {e}")
            return {'error': str(e)}

    def get_wash_probability_trend(self, stock_code: str) -> Dict[str, Any]:
        """
        获取洗盘概率变化趋势

        Args:
            stock_code: 股票代码

        Returns:
            Dict: 洗盘概率趋势分析
        """
        try:
            if stock_code not in self.wash_probability_history:
                return {'error': '无洗盘概率历史数据'}

            wash_data = self.wash_probability_history[stock_code]
            sorted_dates = sorted(wash_data.keys())

            if len(sorted_dates) < 2:
                return {'error': '数据不足，需要至少2天数据'}

            # 分析洗盘概率趋势
            trend_data = []
            for date in sorted_dates:
                day_wash = wash_data[date]
                trend_data.append({
                    'date': date,
                    'wash_probability': day_wash['wash_probability'],
                    'confidence': day_wash['confidence'],
                    'wash_signals': day_wash['wash_signals'],
                    'scores': day_wash['scores']
                })

            # 计算趋势
            latest = trend_data[-1]
            previous = trend_data[-2] if len(trend_data) >= 2 else None

            trend_direction = 'stable'
            trend_strength = 0

            if previous:
                prob_change = latest['wash_probability'] - previous['wash_probability']
                if prob_change > 0.1:
                    trend_direction = 'increasing'
                    trend_strength = prob_change
                elif prob_change < -0.1:
                    trend_direction = 'decreasing'
                    trend_strength = abs(prob_change)

            return {
                'stock_code': stock_code,
                'trend_data': trend_data,
                'latest_probability': latest['wash_probability'],
                'trend_direction': trend_direction,
                'trend_strength': trend_strength,
                'is_wash_strengthening': trend_direction == 'increasing',
                'is_wash_weakening': trend_direction == 'decreasing'
            }

        except Exception as e:
            logger.error(f"获取洗盘概率趋势失败 {stock_code}: {e}")
            return {'error': str(e)}

    def analyze_specific_day_performance(self, stock_code: str, target_day: int) -> Dict[str, Any]:
        """
        分析特定天数的表现（如第2天、第3天）

        Args:
            stock_code: 股票代码
            target_day: 目标天数（2=第二天，3=第三天）

        Returns:
            Dict: 特定天数表现分析
        """
        try:
            multi_day_data = self.get_multi_day_performance(stock_code)
            if 'error' in multi_day_data:
                return multi_day_data

            daily_analysis = multi_day_data['daily_analysis']

            # 查找目标天数的数据
            target_day_data = None
            for day_data in daily_analysis:
                if day_data['hold_days'] == target_day:
                    target_day_data = day_data
                    break

            if not target_day_data:
                return {'error': f'未找到第{target_day}天的数据'}

            # 分析该天的具体表现
            analysis = {
                'stock_code': stock_code,
                'target_day': target_day,
                'day_name': f'第{target_day}天',
                'date': target_day_data['date'],
                'performance_data': target_day_data,
                'is_profitable': target_day_data['total_profit_pct'] > 0,
                'is_daily_up': target_day_data['change_pct'] > 0,
                'is_strong_performance': target_day_data['change_pct'] > 3,
                'is_weak_performance': target_day_data['change_pct'] < -3,
                'volume_healthy': 0.6 <= target_day_data['volume_ratio'] <= 1.5
            }

            # 特殊分析：第2天和第3天
            if target_day == 2:
                analysis['day2_analysis'] = self._analyze_day2_performance(target_day_data)
            elif target_day == 3:
                analysis['day3_analysis'] = self._analyze_day3_performance(stock_code, target_day_data, daily_analysis)

            return analysis

        except Exception as e:
            logger.error(f"分析第{target_day}天表现失败 {stock_code}: {e}")
            return {'error': str(e)}

    def is_continuous_decline(self, stock_code: str, days: int = 2) -> Dict[str, Any]:
        """
        判断是否连续下跌

        Args:
            stock_code: 股票代码
            days: 检查的天数

        Returns:
            Dict: 连续下跌分析结果
        """
        try:
            multi_day_data = self.get_multi_day_performance(stock_code)
            if 'error' in multi_day_data:
                return multi_day_data

            daily_analysis = multi_day_data['daily_analysis']

            if len(daily_analysis) < days:
                return {'error': f'数据不足，需要至少{days}天数据'}

            # 检查最近几天的表现
            recent_days = daily_analysis[-days:]

            decline_days = []
            for day_data in recent_days:
                if day_data['change_pct'] < 0:
                    decline_days.append({
                        'day': day_data['hold_days'],
                        'date': day_data['date'],
                        'change_pct': day_data['change_pct'],
                        'total_profit_pct': day_data['total_profit_pct']
                    })

            is_continuous = len(decline_days) == days
            decline_severity = sum(day['change_pct'] for day in decline_days) if decline_days else 0

            return {
                'stock_code': stock_code,
                'is_continuous_decline': is_continuous,
                'decline_days_count': len(decline_days),
                'total_decline': decline_severity,
                'decline_days': decline_days,
                'severity_level': self._get_decline_severity(decline_severity),
                'requires_attention': is_continuous and decline_severity < -5
            }

        except Exception as e:
            logger.error(f"判断连续下跌失败 {stock_code}: {e}")
            return {'error': str(e)}

    def get_decision_consistency(self, stock_code: str) -> Dict[str, Any]:
        """
        获取决策一致性分析

        Args:
            stock_code: 股票代码

        Returns:
            Dict: 决策一致性分析
        """
        try:
            if stock_code not in self.decision_history:
                return {'error': '无决策历史数据'}

            decision_data = self.decision_history[stock_code]
            sorted_dates = sorted(decision_data.keys())

            decisions = []
            for date in sorted_dates:
                day_decision = decision_data[date]
                decisions.append({
                    'date': date,
                    'hold_days': day_decision['hold_days'],
                    'decision': day_decision['decision'],
                    'action': day_decision['action'],
                    'confidence': day_decision['confidence']
                })

            # 分析决策一致性
            hold_decisions = [d for d in decisions if d['action'] == 'hold']
            sell_decisions = [d for d in decisions if d['action'] == 'sell']

            consistency_analysis = {
                'stock_code': stock_code,
                'total_decisions': len(decisions),
                'hold_decisions': len(hold_decisions),
                'sell_decisions': len(sell_decisions),
                'decisions_history': decisions,
                'latest_decision': decisions[-1] if decisions else None,
                'decision_trend': self._analyze_decision_trend(decisions)
            }

            return consistency_analysis

        except Exception as e:
            logger.error(f"获取决策一致性失败 {stock_code}: {e}")
            return {'error': str(e)}

    def clear_stock_memory(self, stock_code: str) -> bool:
        """
        清除指定股票的所有记忆数据

        Args:
            stock_code: 股票代码

        Returns:
            bool: 清除是否成功
        """
        try:
            # 从内存中删除
            if stock_code in self.daily_performance:
                del self.daily_performance[stock_code]

            if stock_code in self.wash_probability_history:
                del self.wash_probability_history[stock_code]

            if stock_code in self.decision_history:
                del self.decision_history[stock_code]

            # 保存到文件
            self._save_all_data()

            logger.info(f"✅ 清除股票记忆数据: {stock_code}")
            return True

        except Exception as e:
            logger.error(f"清除股票记忆数据失败 {stock_code}: {e}")
            return False

    def get_memory_summary(self, stock_code: str) -> Dict[str, Any]:
        """
        获取股票记忆数据摘要

        Args:
            stock_code: 股票代码

        Returns:
            Dict: 记忆数据摘要
        """
        try:
            summary = {
                'stock_code': stock_code,
                'has_daily_performance': stock_code in self.daily_performance,
                'has_wash_history': stock_code in self.wash_probability_history,
                'has_decision_history': stock_code in self.decision_history,
                'data_days': 0,
                'latest_data': None
            }

            if stock_code in self.daily_performance:
                daily_data = self.daily_performance[stock_code]
                summary['data_days'] = len(daily_data)

                if daily_data:
                    latest_date = max(daily_data.keys())
                    summary['latest_data'] = daily_data[latest_date]

            return summary

        except Exception as e:
            logger.error(f"获取记忆摘要失败 {stock_code}: {e}")
            return {'error': str(e)}

    # ==================== 辅助方法 ====================

    def _calculate_day_to_day_change(self, stock_code: str, current_price: float) -> float:
        """计算相对前一天的价格变化"""
        try:
            if stock_code not in self.daily_performance:
                return 0.0

            daily_data = self.daily_performance[stock_code]
            if not daily_data:
                return 0.0

            # 获取最近一天的数据
            sorted_dates = sorted(daily_data.keys())
            if not sorted_dates:
                return 0.0

            latest_date = sorted_dates[-1]
            previous_price = daily_data[latest_date]['current_price']

            return (current_price - previous_price) / previous_price * 100

        except Exception as e:
            logger.error(f"计算日间变化失败: {e}")
            return 0.0

    def _analyze_performance_trend(self, daily_analysis: List[Dict]) -> Dict[str, Any]:
        """分析表现趋势"""
        try:
            if len(daily_analysis) < 2:
                return {'trend': 'insufficient_data'}

            # 分析连续表现
            up_days = sum(1 for day in daily_analysis if day['change_pct'] > 0)
            down_days = sum(1 for day in daily_analysis if day['change_pct'] < 0)

            # 分析最近趋势
            recent_days = daily_analysis[-3:] if len(daily_analysis) >= 3 else daily_analysis
            recent_trend = []

            for day in recent_days:
                if day['change_pct'] > 3:
                    recent_trend.append('strong_up')
                elif day['change_pct'] > 0:
                    recent_trend.append('up')
                elif day['change_pct'] > -3:
                    recent_trend.append('down')
                else:
                    recent_trend.append('strong_down')

            return {
                'total_days': len(daily_analysis),
                'up_days': up_days,
                'down_days': down_days,
                'recent_trend': recent_trend,
                'is_continuous_decline': all(t in ['down', 'strong_down'] for t in recent_trend[-2:]),
                'is_continuous_rise': all(t in ['up', 'strong_up'] for t in recent_trend[-2:]),
                'volatility': 'high' if any('strong' in t for t in recent_trend) else 'normal'
            }

        except Exception as e:
            logger.error(f"分析表现趋势失败: {e}")
            return {'trend': 'error'}

    def _analyze_day2_performance(self, day2_data: Dict) -> Dict[str, Any]:
        """分析第2天表现"""
        try:
            analysis = {
                'is_second_day_profitable': day2_data['total_profit_pct'] > 0,
                'is_second_day_up': day2_data['change_pct'] > 0,
                'second_day_strength': 'strong' if day2_data['change_pct'] > 5 else 'weak' if day2_data['change_pct'] < -3 else 'normal',
                'volume_healthy': 0.6 <= day2_data['volume_ratio'] <= 1.5,
                'requires_wash_analysis': day2_data['change_pct'] < 0,  # 第2天下跌需要洗盘分析
                'profit_protection_needed': day2_data['total_profit_pct'] > 10  # 盈利>10%需要保护
            }

            return analysis

        except Exception as e:
            logger.error(f"分析第2天表现失败: {e}")
            return {'error': str(e)}

    def _analyze_day3_performance(self, stock_code: str, day3_data: Dict, all_daily_data: List[Dict]) -> Dict[str, Any]:
        """分析第3天表现"""
        try:
            # 获取第2天数据进行对比
            day2_data = None
            for day_data in all_daily_data:
                if day_data['hold_days'] == 2:
                    day2_data = day_data
                    break

            analysis = {
                'is_third_day_profitable': day3_data['total_profit_pct'] > 0,
                'is_third_day_up': day3_data['change_pct'] > 0,
                'third_day_strength': 'strong' if day3_data['change_pct'] > 5 else 'weak' if day3_data['change_pct'] < -3 else 'normal',
                'is_critical_day': True,  # 第3天是关键决策日
                'requires_stop_loss': day3_data['total_profit_pct'] < -8,  # 总亏损>8%需要止损
            }

            # 如果有第2天数据，进行连续分析
            if day2_data:
                analysis['is_continuous_decline'] = day2_data['change_pct'] < 0 and day3_data['change_pct'] < 0
                analysis['two_day_decline'] = day2_data['change_pct'] + day3_data['change_pct']
                analysis['wash_or_escape'] = 'needs_analysis'  # 需要洗盘vs出逃分析

            return analysis

        except Exception as e:
            logger.error(f"分析第3天表现失败: {e}")
            return {'error': str(e)}

    def _get_decline_severity(self, total_decline: float) -> str:
        """获取下跌严重程度"""
        if total_decline > -2:
            return 'mild'
        elif total_decline > -5:
            return 'moderate'
        elif total_decline > -10:
            return 'severe'
        else:
            return 'critical'

    def _analyze_decision_trend(self, decisions: List[Dict]) -> str:
        """分析决策趋势"""
        try:
            if len(decisions) < 2:
                return 'insufficient_data'

            recent_actions = [d['action'] for d in decisions[-3:]]

            if all(action == 'hold' for action in recent_actions):
                return 'consistent_hold'
            elif all(action == 'sell' for action in recent_actions):
                return 'consistent_sell'
            elif recent_actions[-1] == 'sell' and recent_actions[-2] == 'hold':
                return 'hold_to_sell'
            elif recent_actions[-1] == 'hold' and recent_actions[-2] == 'sell':
                return 'sell_to_hold'
            else:
                return 'mixed'

        except Exception as e:
            logger.error(f"分析决策趋势失败: {e}")
            return 'error'

    # ==================== 数据持久化方法 ====================

    def _save_daily_performance(self) -> bool:
        """保存每日表现数据"""
        try:
            with open(self.daily_performance_file, 'w', encoding='utf-8') as f:
                json.dump(self.daily_performance, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存每日表现数据失败: {e}")
            return False

    def _save_wash_probability_history(self) -> bool:
        """保存洗盘概率历史"""
        try:
            with open(self.wash_probability_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.wash_probability_history, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存洗盘概率历史失败: {e}")
            return False

    def _save_decision_history(self) -> bool:
        """保存决策历史"""
        try:
            with open(self.decision_history_file, 'w', encoding='utf-8') as f:
                json.dump(self.decision_history, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logger.error(f"保存决策历史失败: {e}")
            return False

    def _save_all_data(self) -> bool:
        """保存所有数据"""
        try:
            success = True
            success &= self._save_daily_performance()
            success &= self._save_wash_probability_history()
            success &= self._save_decision_history()
            return success
        except Exception as e:
            logger.error(f"保存所有数据失败: {e}")
            return False

    def load_all_data(self) -> bool:
        """加载所有数据"""
        try:
            success = True
            success &= self._load_daily_performance()
            success &= self._load_wash_probability_history()
            success &= self._load_decision_history()
            return success
        except Exception as e:
            logger.error(f"加载所有数据失败: {e}")
            return False

    def _load_daily_performance(self) -> bool:
        """加载每日表现数据"""
        try:
            if self.daily_performance_file.exists():
                with open(self.daily_performance_file, 'r', encoding='utf-8') as f:
                    self.daily_performance = json.load(f)
                logger.info(f"✅ 加载每日表现数据: {len(self.daily_performance)} 只股票")
            return True
        except Exception as e:
            logger.error(f"加载每日表现数据失败: {e}")
            self.daily_performance = {}
            return False

    def _load_wash_probability_history(self) -> bool:
        """加载洗盘概率历史"""
        try:
            if self.wash_probability_history_file.exists():
                with open(self.wash_probability_history_file, 'r', encoding='utf-8') as f:
                    self.wash_probability_history = json.load(f)
                logger.info(f"✅ 加载洗盘概率历史: {len(self.wash_probability_history)} 只股票")
            return True
        except Exception as e:
            logger.error(f"加载洗盘概率历史失败: {e}")
            self.wash_probability_history = {}
            return False

    def _load_decision_history(self) -> bool:
        """加载决策历史"""
        try:
            if self.decision_history_file.exists():
                with open(self.decision_history_file, 'r', encoding='utf-8') as f:
                    self.decision_history = json.load(f)
                logger.info(f"✅ 加载决策历史: {len(self.decision_history)} 只股票")
            return True
        except Exception as e:
            logger.error(f"加载决策历史失败: {e}")
            self.decision_history = {}
            return False

    def get_all_stocks_with_memory(self) -> List[str]:
        """获取所有有记忆数据的股票代码"""
        try:
            all_codes = set()
            all_codes.update(self.daily_performance.keys())
            all_codes.update(self.wash_probability_history.keys())
            all_codes.update(self.decision_history.keys())
            return list(all_codes)
        except Exception as e:
            logger.error(f"获取所有股票代码失败: {e}")
            return []

    def cleanup_old_data(self, days_to_keep: int = 30) -> bool:
        """清理旧数据"""
        try:
            cutoff_date = (datetime.now() - timedelta(days=days_to_keep)).strftime('%Y-%m-%d')

            # 清理每日表现数据
            for stock_code in list(self.daily_performance.keys()):
                stock_data = self.daily_performance[stock_code]
                filtered_data = {date: data for date, data in stock_data.items() if date >= cutoff_date}
                if filtered_data:
                    self.daily_performance[stock_code] = filtered_data
                else:
                    del self.daily_performance[stock_code]

            # 清理洗盘概率历史
            for stock_code in list(self.wash_probability_history.keys()):
                stock_data = self.wash_probability_history[stock_code]
                filtered_data = {date: data for date, data in stock_data.items() if date >= cutoff_date}
                if filtered_data:
                    self.wash_probability_history[stock_code] = filtered_data
                else:
                    del self.wash_probability_history[stock_code]

            # 清理决策历史
            for stock_code in list(self.decision_history.keys()):
                stock_data = self.decision_history[stock_code]
                filtered_data = {date: data for date, data in stock_data.items() if date >= cutoff_date}
                if filtered_data:
                    self.decision_history[stock_code] = filtered_data
                else:
                    del self.decision_history[stock_code]

            # 保存清理后的数据
            self._save_all_data()

            logger.info(f"✅ 清理旧数据完成，保留最近 {days_to_keep} 天")
            return True

        except Exception as e:
            logger.error(f"清理旧数据失败: {e}")
            return False

# 创建全局实例
bought_stocks_memory = BoughtStocksMemorySystem()
