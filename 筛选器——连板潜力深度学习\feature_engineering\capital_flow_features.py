#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
资金流向特征工程
深化资金流向分析，包括主力资金、散户资金、机构资金的流入流出模式
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Any, Optional, Tuple
import akshare as ak
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class CapitalFlowFeatureEngine:
    """资金流向特征工程器"""
    
    def __init__(self):
        self.akshare_available = self._check_akshare_availability()
        self.data_cache = {}  # 数据缓存
        self.cache_timeout = 1800  # 缓存30分钟
        
        # 资金流向分类配置
        self.capital_categories = {
            'super_large': {
                'name': '超大单',
                'threshold': 1000000,  # 100万以上
                'weight': 0.4,
                'description': '通常代表机构资金'
            },
            'large': {
                'name': '大单',
                'threshold': 200000,   # 20万以上
                'weight': 0.3,
                'description': '通常代表主力资金'
            },
            'medium': {
                'name': '中单',
                'threshold': 40000,    # 4万以上
                'weight': 0.2,
                'description': '通常代表大户资金'
            },
            'small': {
                'name': '小单',
                'threshold': 0,        # 4万以下
                'weight': 0.1,
                'description': '通常代表散户资金'
            }
        }
        
        # 资金流向评分权重
        self.flow_weights = {
            'net_inflow_ratio': 0.3,      # 净流入比例
            'inflow_consistency': 0.25,    # 流入一致性
            'large_order_dominance': 0.2,  # 大单主导度
            'flow_acceleration': 0.15,     # 流入加速度
            'institutional_preference': 0.1 # 机构偏好度
        }
        
        logger.info("💰 资金流向特征工程器初始化完成")
    
    def _check_akshare_availability(self) -> bool:
        """检查AKShare可用性"""
        try:
            import akshare as ak
            return True
        except Exception as e:
            logger.warning(f"AKShare不可用: {e}")
            return False
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """检查缓存是否有效"""
        if cache_key not in self.data_cache:
            return False
        
        cache_time = self.data_cache[cache_key].get('timestamp', 0)
        return (datetime.now().timestamp() - cache_time) < self.cache_timeout
    
    def _set_cache(self, cache_key: str, data: Any):
        """设置缓存"""
        self.data_cache[cache_key] = {
            'data': data,
            'timestamp': datetime.now().timestamp()
        }
    
    def _get_cache(self, cache_key: str) -> Any:
        """获取缓存数据"""
        return self.data_cache[cache_key]['data']
    
    def extract_capital_flow_features(self, stock_code: str, kline_data: pd.DataFrame = None) -> Dict[str, Any]:
        """提取资金流向特征"""
        try:
            logger.info(f"💰 开始提取{stock_code}的资金流向特征...")
            
            features = {}
            
            # 1. 获取资金流向数据
            capital_flow_data = self._get_capital_flow_data(stock_code)
            
            if capital_flow_data.get('data_available', False):
                # 2. 基础资金流向特征
                basic_features = self._extract_basic_flow_features(capital_flow_data['flow_data'])
                features.update(basic_features)
                
                # 3. 资金流向趋势特征
                trend_features = self._extract_flow_trend_features(capital_flow_data['flow_data'])
                features.update(trend_features)
                
                # 4. 大单分析特征
                large_order_features = self._extract_large_order_features(capital_flow_data['flow_data'])
                features.update(large_order_features)
                
                # 5. 机构资金特征
                institutional_features = self._extract_institutional_features(capital_flow_data['flow_data'])
                features.update(institutional_features)
                
                # 6. 资金流向与价格关系特征
                if kline_data is not None and not kline_data.empty:
                    price_flow_features = self._extract_price_flow_correlation_features(
                        capital_flow_data['flow_data'], kline_data
                    )
                    features.update(price_flow_features)
                
                # 7. 综合资金流向评分
                comprehensive_score = self._calculate_comprehensive_capital_score(features)
                features.update(comprehensive_score)
                
                features['capital_flow_analysis_success'] = True
            else:
                # 如果无法获取资金流向数据，使用K线数据估算
                if kline_data is not None and not kline_data.empty:
                    estimated_features = self._estimate_capital_flow_from_kline(kline_data)
                    features.update(estimated_features)
                    features['capital_flow_analysis_success'] = False
                    features['using_estimated_data'] = True
                else:
                    features = self._get_empty_capital_features()
            
            features['total_capital_features'] = len([k for k in features.keys() if not k.startswith('capital_flow_')])
            
            logger.info(f"✅ {stock_code}资金流向特征提取完成，共{features['total_capital_features']}个特征")
            return features
            
        except Exception as e:
            logger.error(f"资金流向特征提取失败: {e}")
            return self._get_empty_capital_features()
    
    def _get_capital_flow_data(self, stock_code: str) -> Dict[str, Any]:
        """获取资金流向数据"""
        cache_key = f'capital_flow_{stock_code}'
        
        if self._is_cache_valid(cache_key):
            return self._get_cache(cache_key)
        
        try:
            if not self.akshare_available:
                return {'data_available': False}
            
            # 获取个股资金流向数据
            flow_data = ak.stock_individual_fund_flow(stock=stock_code, market="sh" if stock_code.startswith('6') else "sz")
            
            if flow_data.empty:
                return {'data_available': False}
            
            # 数据预处理
            processed_data = self._preprocess_flow_data(flow_data)
            
            result = {
                'data_available': True,
                'flow_data': processed_data,
                'data_count': len(processed_data),
                'latest_date': processed_data['date'].max() if not processed_data.empty else None
            }
            
            self._set_cache(cache_key, result)
            logger.info(f"✅ 获取{stock_code}资金流向数据成功: {len(processed_data)}条记录")
            return result
            
        except Exception as e:
            logger.error(f"获取{stock_code}资金流向数据失败: {e}")
            return {'data_available': False}
    
    def _preprocess_flow_data(self, flow_data: pd.DataFrame) -> pd.DataFrame:
        """预处理资金流向数据"""
        try:
            # 标准化列名
            column_mapping = {
                '日期': 'date',
                '收盘价': 'close',
                '涨跌幅': 'change_pct',
                '主力净流入-净额': 'main_net_inflow',
                '主力净流入-净占比': 'main_net_inflow_ratio',
                '超大单净流入-净额': 'super_large_net_inflow',
                '超大单净流入-净占比': 'super_large_net_inflow_ratio',
                '大单净流入-净额': 'large_net_inflow',
                '大单净流入-净占比': 'large_net_inflow_ratio',
                '中单净流入-净额': 'medium_net_inflow',
                '中单净流入-净占比': 'medium_net_inflow_ratio',
                '小单净流入-净额': 'small_net_inflow',
                '小单净流入-净占比': 'small_net_inflow_ratio'
            }
            
            # 重命名列
            for chinese_name, english_name in column_mapping.items():
                if chinese_name in flow_data.columns:
                    flow_data[english_name] = flow_data[chinese_name]
            
            # 确保日期格式正确
            if 'date' in flow_data.columns:
                flow_data['date'] = pd.to_datetime(flow_data['date'])
            
            # 按日期排序
            flow_data = flow_data.sort_values('date', ascending=True).reset_index(drop=True)
            
            # 填充缺失值
            numeric_columns = ['main_net_inflow', 'main_net_inflow_ratio', 'super_large_net_inflow',
                             'super_large_net_inflow_ratio', 'large_net_inflow', 'large_net_inflow_ratio',
                             'medium_net_inflow', 'medium_net_inflow_ratio', 'small_net_inflow', 'small_net_inflow_ratio']
            
            for col in numeric_columns:
                if col in flow_data.columns:
                    flow_data[col] = pd.to_numeric(flow_data[col], errors='coerce').fillna(0)
            
            return flow_data
            
        except Exception as e:
            logger.error(f"资金流向数据预处理失败: {e}")
            return pd.DataFrame()
    
    def _extract_basic_flow_features(self, flow_data: pd.DataFrame) -> Dict[str, float]:
        """提取基础资金流向特征"""
        try:
            features = {}
            
            if flow_data.empty:
                return features
            
            # 最新资金流向数据
            latest = flow_data.iloc[-1]
            
            # 基础净流入特征
            features.update({
                'main_net_inflow': float(latest.get('main_net_inflow', 0)),
                'main_net_inflow_ratio': float(latest.get('main_net_inflow_ratio', 0)),
                'super_large_net_inflow': float(latest.get('super_large_net_inflow', 0)),
                'large_net_inflow': float(latest.get('large_net_inflow', 0)),
                'medium_net_inflow': float(latest.get('medium_net_inflow', 0)),
                'small_net_inflow': float(latest.get('small_net_inflow', 0))
            })
            
            # 净流入状态特征
            features.update({
                'main_inflow_positive': 1.0 if features['main_net_inflow'] > 0 else 0.0,
                'super_large_inflow_positive': 1.0 if features['super_large_net_inflow'] > 0 else 0.0,
                'large_inflow_positive': 1.0 if features['large_net_inflow'] > 0 else 0.0,
                'institutional_inflow_positive': 1.0 if (features['super_large_net_inflow'] + features['large_net_inflow']) > 0 else 0.0
            })
            
            # 资金流向强度特征
            if len(flow_data) >= 5:
                recent_5d = flow_data.tail(5)
                
                features.update({
                    'main_inflow_5d_avg': float(recent_5d['main_net_inflow'].mean()),
                    'main_inflow_5d_sum': float(recent_5d['main_net_inflow'].sum()),
                    'main_inflow_consistency_5d': float(sum(recent_5d['main_net_inflow'] > 0) / 5),
                    'super_large_inflow_consistency_5d': float(sum(recent_5d['super_large_net_inflow'] > 0) / 5)
                })
            
            return features
            
        except Exception as e:
            logger.error(f"基础资金流向特征提取失败: {e}")
            return {}
    
    def _extract_flow_trend_features(self, flow_data: pd.DataFrame) -> Dict[str, float]:
        """提取资金流向趋势特征"""
        try:
            features = {}
            
            if len(flow_data) < 10:
                return features
            
            # 计算趋势斜率
            recent_10d = flow_data.tail(10)
            x = np.arange(len(recent_10d))
            
            # 主力资金趋势
            if 'main_net_inflow' in recent_10d.columns:
                main_slope = np.polyfit(x, recent_10d['main_net_inflow'], 1)[0]
                features['main_inflow_trend_slope'] = float(main_slope)
                features['main_inflow_uptrend'] = 1.0 if main_slope > 0 else 0.0
            
            # 超大单趋势
            if 'super_large_net_inflow' in recent_10d.columns:
                super_large_slope = np.polyfit(x, recent_10d['super_large_net_inflow'], 1)[0]
                features['super_large_inflow_trend_slope'] = float(super_large_slope)
                features['super_large_inflow_uptrend'] = 1.0 if super_large_slope > 0 else 0.0
            
            # 资金流向加速度
            if len(flow_data) >= 20:
                recent_20d = flow_data.tail(20)
                first_10d_avg = recent_20d.head(10)['main_net_inflow'].mean()
                last_10d_avg = recent_20d.tail(10)['main_net_inflow'].mean()
                
                if abs(first_10d_avg) > 0:
                    acceleration = (last_10d_avg - first_10d_avg) / abs(first_10d_avg)
                    features['main_inflow_acceleration'] = float(acceleration)
                    features['main_inflow_accelerating'] = 1.0 if acceleration > 0.2 else 0.0
            
            return features
            
        except Exception as e:
            logger.error(f"资金流向趋势特征提取失败: {e}")
            return {}

    def _extract_large_order_features(self, flow_data: pd.DataFrame) -> Dict[str, float]:
        """提取大单分析特征"""
        try:
            features = {}

            if flow_data.empty:
                return features

            latest = flow_data.iloc[-1]

            # 大单主导度分析
            super_large_inflow = float(latest.get('super_large_net_inflow', 0))
            large_inflow = float(latest.get('large_net_inflow', 0))
            medium_inflow = float(latest.get('medium_net_inflow', 0))
            small_inflow = float(latest.get('small_net_inflow', 0))

            total_inflow = abs(super_large_inflow) + abs(large_inflow) + abs(medium_inflow) + abs(small_inflow)

            if total_inflow > 0:
                features.update({
                    'super_large_dominance': abs(super_large_inflow) / total_inflow,
                    'large_order_dominance': (abs(super_large_inflow) + abs(large_inflow)) / total_inflow,
                    'institutional_dominance': (abs(super_large_inflow) + abs(large_inflow)) / total_inflow,
                    'retail_dominance': (abs(medium_inflow) + abs(small_inflow)) / total_inflow
                })
            else:
                features.update({
                    'super_large_dominance': 0.0,
                    'large_order_dominance': 0.0,
                    'institutional_dominance': 0.0,
                    'retail_dominance': 0.0
                })

            # 大单净流入强度
            features.update({
                'large_order_net_strength': (super_large_inflow + large_inflow) / 1e8,  # 转换为亿元
                'large_order_inflow_strong': 1.0 if (super_large_inflow + large_inflow) > 1e8 else 0.0,  # 大于1亿
                'super_large_inflow_strong': 1.0 if super_large_inflow > 5e7 else 0.0  # 超大单大于5000万
            })

            # 大单与散户资金对比
            institutional_net = super_large_inflow + large_inflow
            retail_net = medium_inflow + small_inflow

            if abs(retail_net) > 0:
                features['institutional_vs_retail_ratio'] = institutional_net / abs(retail_net)
            else:
                features['institutional_vs_retail_ratio'] = 0.0

            features['institutional_retail_opposite'] = 1.0 if institutional_net > 0 and retail_net < 0 else 0.0

            return features

        except Exception as e:
            logger.error(f"大单分析特征提取失败: {e}")
            return {}

    def _extract_institutional_features(self, flow_data: pd.DataFrame) -> Dict[str, float]:
        """提取机构资金特征"""
        try:
            features = {}

            if len(flow_data) < 5:
                return features

            # 机构资金持续性分析
            recent_5d = flow_data.tail(5)
            recent_10d = flow_data.tail(10) if len(flow_data) >= 10 else recent_5d

            # 计算机构资金（超大单+大单）
            recent_5d['institutional_net'] = recent_5d['super_large_net_inflow'] + recent_5d['large_net_inflow']
            recent_10d['institutional_net'] = recent_10d['super_large_net_inflow'] + recent_10d['large_net_inflow']

            # 机构资金一致性
            institutional_positive_days_5d = sum(recent_5d['institutional_net'] > 0)
            institutional_positive_days_10d = sum(recent_10d['institutional_net'] > 0)

            features.update({
                'institutional_consistency_5d': institutional_positive_days_5d / 5,
                'institutional_consistency_10d': institutional_positive_days_10d / len(recent_10d),
                'institutional_strong_consistency': 1.0 if institutional_positive_days_5d >= 4 else 0.0
            })

            # 机构资金规模特征
            institutional_avg_5d = recent_5d['institutional_net'].mean()
            institutional_total_5d = recent_5d['institutional_net'].sum()

            features.update({
                'institutional_avg_inflow_5d': float(institutional_avg_5d / 1e8),  # 转换为亿元
                'institutional_total_inflow_5d': float(institutional_total_5d / 1e8),
                'institutional_large_inflow': 1.0 if institutional_total_5d > 5e8 else 0.0  # 5天超过5亿
            })

            # 机构偏好度（相对于市场平均）
            if 'change_pct' in recent_5d.columns:
                avg_change = recent_5d['change_pct'].mean()
                avg_institutional_inflow = institutional_avg_5d

                # 简化的机构偏好度计算
                if avg_change != 0:
                    preference_ratio = avg_institutional_inflow / (abs(avg_change) * 1e8)
                    features['institutional_preference'] = float(min(abs(preference_ratio), 10))  # 限制在合理范围
                else:
                    features['institutional_preference'] = 0.0

            return features

        except Exception as e:
            logger.error(f"机构资金特征提取失败: {e}")
            return {}

    def _extract_price_flow_correlation_features(self, flow_data: pd.DataFrame, kline_data: pd.DataFrame) -> Dict[str, float]:
        """提取资金流向与价格关系特征"""
        try:
            features = {}

            if flow_data.empty or kline_data.empty:
                return features

            # 合并数据（按日期）
            if 'date' in flow_data.columns and 'date' in kline_data.columns:
                merged_data = pd.merge(flow_data, kline_data[['date', 'close', 'volume']], on='date', how='inner')
            else:
                return features

            if len(merged_data) < 5:
                return features

            # 计算价格变化
            merged_data['price_change'] = merged_data['close'].pct_change()

            # 资金流向与价格变化相关性
            if 'main_net_inflow' in merged_data.columns:
                price_changes = merged_data['price_change'].dropna()
                main_inflows = merged_data['main_net_inflow'][price_changes.index]

                if len(price_changes) >= 3:
                    correlation = price_changes.corr(main_inflows)
                    features['price_main_flow_correlation'] = float(correlation) if not pd.isna(correlation) else 0.0
                    features['price_flow_positive_correlation'] = 1.0 if correlation > 0.3 else 0.0

            # 量价资金三重确认
            if len(merged_data) >= 3:
                recent_3d = merged_data.tail(3)

                price_up_days = sum(recent_3d['price_change'] > 0)
                volume_up_days = sum(recent_3d['volume'] > recent_3d['volume'].shift(1))
                flow_up_days = sum(recent_3d['main_net_inflow'] > 0)

                features.update({
                    'price_volume_flow_alignment': (price_up_days + volume_up_days + flow_up_days) / 9,  # 标准化到0-1
                    'triple_confirmation': 1.0 if (price_up_days >= 2 and volume_up_days >= 2 and flow_up_days >= 2) else 0.0
                })

            # 资金流向领先性分析
            if len(merged_data) >= 5:
                # 计算资金流向是否领先价格变化
                flow_lead_correlation = merged_data['main_net_inflow'].shift(1).corr(merged_data['price_change'])
                features['flow_leads_price'] = 1.0 if flow_lead_correlation > 0.3 else 0.0

            return features

        except Exception as e:
            logger.error(f"价格资金流向相关性特征提取失败: {e}")
            return {}

    def _calculate_comprehensive_capital_score(self, features: Dict[str, float]) -> Dict[str, float]:
        """计算综合资金流向评分"""
        try:
            score_components = {}

            # 1. 净流入比例评分
            main_inflow_ratio = features.get('main_net_inflow_ratio', 0)
            score_components['net_inflow_ratio_score'] = min(max(main_inflow_ratio / 10, -1), 1)  # 标准化到-1到1

            # 2. 流入一致性评分
            consistency_5d = features.get('institutional_consistency_5d', 0)
            score_components['inflow_consistency_score'] = consistency_5d

            # 3. 大单主导度评分
            large_dominance = features.get('large_order_dominance', 0)
            score_components['large_order_dominance_score'] = large_dominance

            # 4. 流入加速度评分
            acceleration = features.get('main_inflow_acceleration', 0)
            score_components['flow_acceleration_score'] = min(max(acceleration, -1), 1)

            # 5. 机构偏好度评分
            preference = features.get('institutional_preference', 0)
            score_components['institutional_preference_score'] = min(preference / 5, 1)  # 标准化

            # 计算加权综合评分
            comprehensive_score = 0.0
            for component, weight in self.flow_weights.items():
                score_key = f"{component}_score"
                if score_key in score_components:
                    comprehensive_score += score_components[score_key] * weight

            return {
                'capital_flow_comprehensive_score': comprehensive_score,
                'capital_flow_bullish': 1.0 if comprehensive_score > 0.3 else 0.0,
                'capital_flow_bearish': 1.0 if comprehensive_score < -0.3 else 0.0,
                **score_components
            }

        except Exception as e:
            logger.error(f"综合资金流向评分计算失败: {e}")
            return {'capital_flow_comprehensive_score': 0.0}

    def _estimate_capital_flow_from_kline(self, kline_data: pd.DataFrame) -> Dict[str, float]:
        """从K线数据估算资金流向特征"""
        try:
            features = {}

            if len(kline_data) < 5:
                return features

            # 基于成交量和价格变化估算资金流向
            recent_5d = kline_data.tail(5)

            # 估算主力资金流向（简化算法）
            estimated_main_inflow = 0
            for _, row in recent_5d.iterrows():
                price_change = row.get('close', 0) - row.get('open', 0)
                volume = row.get('volume', 0)

                # 简化：上涨且放量视为资金流入
                if price_change > 0 and volume > 0:
                    estimated_main_inflow += volume * (price_change / row.get('open', 1))
                elif price_change < 0:
                    estimated_main_inflow += volume * (price_change / row.get('open', 1))

            # 估算特征
            features.update({
                'estimated_main_inflow': estimated_main_inflow / 1e8,  # 转换为亿元
                'estimated_inflow_positive': 1.0 if estimated_main_inflow > 0 else 0.0,
                'estimated_large_inflow': 1.0 if estimated_main_inflow > 1e8 else 0.0,
                'volume_price_momentum': self._calculate_volume_price_momentum(recent_5d)
            })

            # 基于换手率估算机构参与度
            if 'turnover_rate' in recent_5d.columns:
                avg_turnover = recent_5d['turnover_rate'].mean()
                features.update({
                    'estimated_institutional_participation': min(avg_turnover / 10, 1.0),  # 标准化
                    'high_institutional_participation': 1.0 if avg_turnover > 5.0 else 0.0
                })

            return features

        except Exception as e:
            logger.error(f"从K线估算资金流向失败: {e}")
            return {}

    def _calculate_volume_price_momentum(self, kline_data: pd.DataFrame) -> float:
        """计算量价动量"""
        try:
            if len(kline_data) < 3:
                return 0.0

            momentum = 0.0
            for i in range(1, len(kline_data)):
                price_change = (kline_data['close'].iloc[i] - kline_data['close'].iloc[i-1]) / kline_data['close'].iloc[i-1]
                volume_ratio = kline_data['volume'].iloc[i] / kline_data['volume'].iloc[i-1] if kline_data['volume'].iloc[i-1] > 0 else 1

                # 量价配合度
                if price_change > 0 and volume_ratio > 1:
                    momentum += price_change * volume_ratio
                elif price_change < 0 and volume_ratio < 1:
                    momentum += abs(price_change) * (2 - volume_ratio)  # 下跌缩量也是好现象

            return momentum / len(kline_data)

        except Exception as e:
            logger.error(f"量价动量计算失败: {e}")
            return 0.0

    def _get_empty_capital_features(self) -> Dict[str, Any]:
        """获取空的资金流向特征"""
        return {
            'capital_flow_analysis_success': False,
            'total_capital_features': 0,
            'capital_flow_comprehensive_score': 0.0,
            'error': '资金流向特征提取失败'
        }

def main():
    """测试资金流向特征工程器"""
    print("💰 测试资金流向特征工程器...")

    engine = CapitalFlowFeatureEngine()

    # 创建测试K线数据
    dates = pd.date_range('2024-06-01', periods=20, freq='D')
    np.random.seed(42)

    test_kline = pd.DataFrame({
        'date': dates,
        'open': 10 + np.random.randn(20) * 0.3,
        'high': 10.5 + np.random.randn(20) * 0.3,
        'low': 9.5 + np.random.randn(20) * 0.3,
        'close': 10 + np.random.randn(20) * 0.3,
        'volume': 1000000 + np.random.randn(20) * 200000,
        'turnover_rate': 2 + np.random.randn(20) * 1
    })

    # 确保价格逻辑正确
    test_kline['high'] = test_kline[['open', 'close', 'high']].max(axis=1)
    test_kline['low'] = test_kline[['open', 'close', 'low']].min(axis=1)
    test_kline['volume'] = test_kline['volume'].abs()
    test_kline['turnover_rate'] = test_kline['turnover_rate'].abs()

    # 测试特征提取（使用估算方法，因为无法获取真实资金流向数据）
    print(f"\n🔍 测试资金流向特征提取...")
    features = engine.extract_capital_flow_features('000001', test_kline)

    print(f"✅ 特征提取完成")
    print(f"分析成功: {features.get('capital_flow_analysis_success', False)}")
    print(f"使用估算数据: {features.get('using_estimated_data', False)}")
    print(f"总特征数: {features.get('total_capital_features', 0)}")

    # 显示关键特征
    key_features = ['estimated_main_inflow', 'estimated_inflow_positive', 'volume_price_momentum',
                   'estimated_institutional_participation', 'capital_flow_comprehensive_score']

    print(f"\n💰 关键资金流向特征:")
    for feature in key_features:
        if feature in features:
            print(f"  {feature}: {features[feature]:.3f}")

if __name__ == "__main__":
    main()
