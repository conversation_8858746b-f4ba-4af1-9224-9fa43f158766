# 已买入股票功能使用说明

## 🎯 功能概述

已买入股票功能为连板潜力股监控系统添加了完整的交易闭环管理，实现从选股到卖出的全流程智能化监控。

### 核心特性
- ✅ **右键标记买入**：在股票列表中右键即可标记已买入
- ✅ **算法自动切换**：已买入股票自动使用卖出监控算法
- ✅ **洗盘识别**：四维评分系统识别主力洗盘行为
- ✅ **时段策略**：6个关键时段的专业监控策略
- ✅ **智能提醒**：自动发送卖出和减仓提醒
- ✅ **数据持久化**：完整记录交易历史和盈亏统计

## 📋 使用流程

### 1. 标记股票为已买入
1. 在监控列表中找到要标记的股票
2. **右键点击**股票行
3. 选择 **"📈 标记已买入"**
4. 系统自动记录买入时间和当前价格

### 2. 查看已买入股票状态
- 已买入股票在右键菜单中会显示特殊标识
- 可通过 **"📊 查看详情"** 查看持仓信息
- 系统显示持有天数、买入价格、盈亏状态等

### 3. 卖出监控分析
启动监控后，已买入股票将自动使用卖出监控算法：

#### 🌊 洗盘识别分析
- **量能分析**：量比、换手率、成交结构
- **价格形态**：下影线、回撤幅度、收盘位置
- **资金流向**：大单流入、机构参与、潜伏资金
- **监管风险**：政策风险、异常波动、特殊公告

#### ⏰ 时段策略监控
| 时段 | 时间 | 监控重点 | 频率 |
|------|------|----------|------|
| 隔夜挂单分析 | 9:15-9:25 | 买盘结构、监管风险 | 15秒 |
| 开盘博弈阶段 | 9:30-9:45 | 真假拉升、诱多信号 | 15秒 |
| 早盘关键窗口 | 9:45-10:30 | 主力意图、量能健康 | 30秒 |
| 午盘变盘点 | 11:00-13:10 | 突击拉升、资金回流 | 30秒 |
| 尾盘决战点 | 14:00-14:45 | 机构态度、量能萎缩 | 15秒 |
| 最后逃生点 | 14:45-15:00 | 强制操作、利润保护 | 10秒 |

### 4. 决策建议类型
- **🚀 坚定持有**：洗盘概率≥85%，主力未出逃
- **⚠️ 减仓观察**：洗盘概率70%-85%，谨慎持有
- **🚨 立即止损**：洗盘概率<70%，主力出逃迹象

### 5. 标记卖出
1. 右键点击已买入股票
2. 选择 **"💰 标记已卖出"**
3. 输入卖出价格和备注
4. 系统自动计算盈亏并记录

## 🔍 监控界面说明

### 已买入股票显示
```
💰 分析已买入股票: 600230 沧州大化 (持有2天)
   📊 当前价格: 15.20 (+5.34%)
   💰 持仓盈亏: +5.34%
   🎯 决策建议: 减仓观察
   🔍 置信度: 中
   🌊 洗盘概率: 68.3%
   ⏰ 当前时段: 尾盘决战点
```

### 提醒消息类型
- **🚨 卖出提醒**：建议立即卖出时发送
- **⚠️ 减仓提醒**：建议减仓观察时发送
- **📢 Windows通知**：重要提醒会弹出系统通知

## 📊 数据管理

### 自动保存数据
- **bought_stocks.json**：已买入股票信息
- **trading_records.json**：完整交易记录
- 数据实时保存，重启应用自动加载

### 统计信息
- 总持仓股票数量
- 已卖出股票数量
- 总盈亏金额和比例
- 胜率统计
- 交易记录数量

## ⚙️ 高级功能

### 四维评分系统
```
洗盘概率 = 量能分析×40% + 形态分析×30% + 资金分析×20% + 监管分析×10%
```

### 智能决策因子
- **洗盘概率**：基于技术分析的洗盘可能性
- **时段策略**：根据交易时段调整策略
- **持有天数**：考虑持仓时间风险
- **盈亏状况**：结合当前盈亏情况

### 风险控制
- **监管风险检测**：实时监控政策风险
- **异常波动识别**：识别非正常价格波动
- **止损保护**：自动触发止损建议
- **利润保护**：盈利时的风险控制

## 🚀 使用建议

### 最佳实践
1. **及时标记**：买入股票后立即标记，确保算法切换
2. **关注提醒**：重视系统发出的卖出和减仓提醒
3. **结合时段**：在关键时段（如尾盘）加强关注
4. **记录完整**：卖出时填写准确价格，便于统计分析

### 注意事项
- 系统提供决策参考，最终决策需结合个人判断
- 洗盘识别基于技术分析，存在一定误判可能
- 建议结合基本面分析和市场环境综合判断
- 严格执行风险控制，设置合理止损位

### 数据源说明
- 主数据源：AKShare（最准确）
- 备用数据源：efinance、easyquotation、腾讯财经
- 自动切换：数据源故障时无缝切换
- 数据验证：多源数据交叉验证确保准确性

## 📞 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. 数据源是否可用
3. 股票代码是否正确
4. 交易时间是否在监控范围内

系统会自动记录运行日志，便于问题排查和功能优化。
