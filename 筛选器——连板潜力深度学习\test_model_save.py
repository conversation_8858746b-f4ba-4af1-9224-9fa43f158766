#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试模型保存问题
"""

import os
import joblib
import pandas as pd
import numpy as np
from datetime import datetime
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler

def test_model_save():
    """测试模型保存"""
    print("🧪 测试模型保存...")
    
    try:
        # 创建一个简单的测试模型
        print("1️⃣ 创建测试模型...")
        model = RandomForestClassifier(n_estimators=10, random_state=42)
        
        # 创建测试数据
        X = np.random.rand(100, 5)
        y = np.random.randint(0, 2, 100)
        
        # 训练模型
        model.fit(X, y)
        print("✅ 模型训练完成")
        
        # 测试保存到trained_models目录
        print("2️⃣ 测试保存到trained_models目录...")
        model_dir = "trained_models"
        
        if not os.path.exists(model_dir):
            os.makedirs(model_dir)
            print(f"✅ 创建目录: {model_dir}")
        
        # 保存测试模型
        test_model_file = os.path.join(model_dir, "test_model.joblib")
        joblib.dump(model, test_model_file)
        print(f"✅ 保存测试模型: {test_model_file}")
        
        # 检查文件是否存在
        if os.path.exists(test_model_file):
            file_time = os.path.getmtime(test_model_file)
            file_datetime = datetime.fromtimestamp(file_time)
            print(f"✅ 文件存在，修改时间: {file_datetime}")
            
            # 删除测试文件
            os.remove(test_model_file)
            print("✅ 清理测试文件")
            
            return True
        else:
            print("❌ 文件保存失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_model_files():
    """检查现有模型文件"""
    print("\n🔍 检查现有模型文件...")
    
    model_dir = "trained_models"
    
    if not os.path.exists(model_dir):
        print(f"❌ 模型目录不存在: {model_dir}")
        return
    
    files = os.listdir(model_dir)
    print(f"📁 模型目录文件: {len(files)}个")
    
    for file in files:
        file_path = os.path.join(model_dir, file)
        if os.path.isfile(file_path):
            file_time = os.path.getmtime(file_path)
            file_datetime = datetime.fromtimestamp(file_time)
            file_size = os.path.getsize(file_path)
            print(f"   {file}: {file_datetime}, {file_size} bytes")

def test_current_model_save():
    """测试当前训练流程的模型保存"""
    print("\n🧪 测试当前训练流程的模型保存...")
    
    try:
        from ml_models.limit_up_predictor import LimitUpPredictor
        
        # 创建预测器
        predictor = LimitUpPredictor()
        
        # 创建测试数据
        print("1️⃣ 创建测试数据...")
        X = pd.DataFrame(np.random.rand(100, 10), columns=[f'feature_{i}' for i in range(10)])
        y = np.random.randint(0, 2, 100)
        
        # 训练模型
        print("2️⃣ 训练模型...")
        results = predictor.train_models(X, y, test_size=0.2)
        
        if results:
            print(f"✅ 训练完成，结果: {len(results)}个模型")
            
            # 检查模型文件
            print("3️⃣ 检查模型文件...")
            check_model_files()
            
            return True
        else:
            print("❌ 训练失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 模型保存问题测试...")
    print("="*60)
    
    # 检查现有模型文件
    check_model_files()
    
    # 测试基本保存功能
    basic_result = test_model_save()
    
    # 测试当前训练流程的保存
    current_result = test_current_model_save()
    
    print("\n" + "="*60)
    print("📋 测试结论:")
    
    if basic_result:
        print("✅ 基本模型保存功能正常")
    else:
        print("❌ 基本模型保存功能异常")
    
    if current_result:
        print("✅ 当前训练流程模型保存正常")
    else:
        print("❌ 当前训练流程模型保存异常")
    
    if basic_result and current_result:
        print("🎉 模型保存功能正常！")
        print("💡 可能是之前的训练没有真正完成模型保存")
    else:
        print("❌ 模型保存存在问题，需要进一步调试")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    main()
